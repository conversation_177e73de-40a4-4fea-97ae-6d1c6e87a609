package developer

import (
	"fmt"

	devPb "github.com/epifi/gamma/api/fittt/developer"
	"github.com/epifi/gamma/fittt/developer/processor"
)

type DevFactory struct {
	devActions          *processor.DevActions
	devActionExecutions *processor.DevActionExecutions
	devEvents           *processor.DevEvents
	devSchedules        *processor.DevSchedules
	devJobs             *processor.DevJobs
	devAggrEntities     *processor.DevAggregationEntitys
	devAggrExecutions   *processor.DevAggregatedExecutions
}

func NewDevFactory(actions *processor.DevActions, executions *processor.DevActionExecutions,
	events *processor.DevEvents, devSchedules *processor.DevSchedules, devJobs *processor.DevJobs,
	devAggrEntities *processor.DevAggregationEntitys, devAggrExecutions *processor.DevAggregatedExecutions) *DevFactory {
	return &DevFactory{
		devActions:          actions,
		devActionExecutions: executions,
		devEvents:           events,
		devSchedules:        devSchedules,
		devJobs:             devJobs,
		devAggrEntities:     devAggrEntities,
		devAggrExecutions:   devAggrExecutions,
	}
}

func (d *DevFactory) getParameterListImpl(entity devPb.FITTTEntity) (IParameterFetcher, error) {
	switch entity {
	case devPb.FITTTEntity_ACTIONS:
		return d.devActions, nil
	case devPb.FITTTEntity_ACTIONS_EXECUTIONS:
		return d.devActionExecutions, nil
	case devPb.FITTTEntity_EVENTS:
		return d.devEvents, nil
	case devPb.FITTTEntity_SCHEDULES:
		return d.devSchedules, nil
	case devPb.FITTTEntity_JOBS:
		return d.devJobs, nil
	case devPb.FITTTEntity_AGGREGATION_ENTITIES:
		return d.devAggrEntities, nil
	case devPb.FITTTEntity_AGGREGATED_EXECUTIONS:
		return d.devAggrExecutions, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity devPb.FITTTEntity) (IDataFetcher, error) {
	switch entity {
	case devPb.FITTTEntity_ACTIONS:
		return d.devActions, nil
	case devPb.FITTTEntity_ACTIONS_EXECUTIONS:
		return d.devActionExecutions, nil
	case devPb.FITTTEntity_EVENTS:
		return d.devEvents, nil
	case devPb.FITTTEntity_SCHEDULES:
		return d.devSchedules, nil
	case devPb.FITTTEntity_JOBS:
		return d.devJobs, nil
	case devPb.FITTTEntity_AGGREGATION_ENTITIES:
		return d.devAggrEntities, nil
	case devPb.FITTTEntity_AGGREGATED_EXECUTIONS:
		return d.devAggrExecutions, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
