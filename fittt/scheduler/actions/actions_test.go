package actions_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"math"
	"testing"
	"time"

	"github.com/epifi/gamma/fittt/config/genconf"

	fitttMock "github.com/epifi/gamma/api/fittt/mocks"

	"github.com/epifi/gamma/api/fittt"

	"google.golang.org/protobuf/proto"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	aggrpb "github.com/epifi/gamma/api/fittt/action/aggregator"
	aggrconsumerpb "github.com/epifi/gamma/api/fittt/action/aggregator/consumer"

	scopepb "github.com/epifi/gamma/api/rms/orchestrator/event/scope"

	"github.com/epifi/be-common/api/rpc"

	sportspb "github.com/epifi/gamma/api/fittt/sports"

	eventpb "github.com/epifi/gamma/api/fittt/event"
	"github.com/epifi/be-common/pkg/datetime"

	orchestratorpb "github.com/epifi/gamma/api/fittt/orchestrator/consumer"

	vgfittt "github.com/epifi/gamma/api/vendorgateway/fittt"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	vgmock "github.com/epifi/gamma/api/vendorgateway/fittt/mocks"
	"github.com/epifi/gamma/fittt/config"
	"github.com/epifi/gamma/fittt/scheduler/dao/mocks"
	sportsdaomock "github.com/epifi/gamma/fittt/sports/dao/mocks"
	sportsmock "github.com/epifi/gamma/fittt/sports/mocks"
	protomocks "github.com/epifi/be-common/pkg/mock"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	"github.com/epifi/gamma/fittt/scheduler/actions"
	qmocks "github.com/epifi/be-common/pkg/queue/mocks"
)

func TestDailyRuleAction(t *testing.T) {
	now := time.Now()
	yesterdayNow := now.Add(-24 * time.Hour)
	// dayBeforeYesterdayNow := now.Add(-48 * time.Hour)
	start := timestamppb.New(time.Date(yesterdayNow.Year(), yesterdayNow.Month(), yesterdayNow.Day(), 0, 0, 0, 0, datetime.IST))
	end := timestamppb.New(time.Date(yesterdayNow.Year(), yesterdayNow.Month(), yesterdayNow.Day(), 23, 59, 59, 0, datetime.IST))

	tests := []struct {
		name                   string
		job                    *schedulerpb.Job
		want                   *orchestratorpb.ProcessEnrichedEventRequest
		expectedStopRecurrence bool
	}{
		{
			name: "trigger for same day",
			job: &schedulerpb.Job{
				Id: uuid.New().String(),
				Schedule: &schedulerpb.Schedule{
					Id:   uuid.New().String(),
					Type: schedulerpb.ScheduleType_DAILY_RULE_EVENT,
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_DailyRuleEvent{
							DailyRuleEvent: &schedulerpb.DailyRuleEvent{}}},
					RecurrenceLimit:         5,
					StopRecurrenceOnFailure: false,
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_EveryDayV2{
							EveryDayV2: &schedulerpb.EveryDay{
								EveryDayAt: timestamppb.New(now),
							}}},
					Status: schedulerpb.ScheduleStatus_RUNNING,
				},
				ScheduledAt:     timestamppb.New(now),
				Status:          schedulerpb.JobStatus_SCHEDULED,
				RecurrenceCount: 1,
			},
			expectedStopRecurrence: false,
			want: &orchestratorpb.ProcessEnrichedEventRequest{
				StartTime: timestamppb.New(now),
				EndTime:   timestamppb.New(now),
				Event: &orchestratorpb.ProcessEnrichedEventRequest_DailyEvent{
					DailyEvent: &orchestratorpb.DailyEvent{
						Day:       timestamppb.New(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, datetime.IST)),
						EventType: eventpb.EventType_DAILY_EVENT,
					},
				},
			},
		},
		{
			name: "trigger for same day with subscription start",
			job: &schedulerpb.Job{
				Id: uuid.New().String(),
				Schedule: &schedulerpb.Schedule{
					Id:   uuid.New().String(),
					Type: schedulerpb.ScheduleType_DAILY_RULE_EVENT,
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_DailyRuleEvent{
							DailyRuleEvent: &schedulerpb.DailyRuleEvent{
								SubsStartTime: timestamppb.New(yesterdayNow),
							}}},
					RecurrenceLimit:         5,
					StopRecurrenceOnFailure: false,
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_EveryDayV2{
							EveryDayV2: &schedulerpb.EveryDay{
								EveryDayAt: timestamppb.New(now),
							}}},
					Status: schedulerpb.ScheduleStatus_RUNNING,
				},
				ScheduledAt:     timestamppb.New(now),
				Status:          schedulerpb.JobStatus_SCHEDULED,
				RecurrenceCount: 1,
			},
			expectedStopRecurrence: false,
			want: &orchestratorpb.ProcessEnrichedEventRequest{
				StartTime: timestamppb.New(yesterdayNow),
				Event: &orchestratorpb.ProcessEnrichedEventRequest_DailyEvent{
					DailyEvent: &orchestratorpb.DailyEvent{
						Day:       timestamppb.New(time.Date(yesterdayNow.Year(), yesterdayNow.Month(), yesterdayNow.Day(), 0, 0, 0, 0, datetime.IST)),
						EventType: eventpb.EventType_DAILY_EVENT,
					},
				},
			},
		},
		{
			name: "trigger app usage daily event for same day",
			job: &schedulerpb.Job{
				Id: uuid.New().String(),
				Schedule: &schedulerpb.Schedule{
					Id:   uuid.New().String(),
					Type: schedulerpb.ScheduleType_APP_USAGE_DAILY_RULE_EVENT,
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_AppUsageRuleEvent{AppUsageRuleEvent: &schedulerpb.AppUsageRuleEvent{}}},
					RecurrenceLimit:         5,
					StopRecurrenceOnFailure: false,
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_EveryDayV2{
							EveryDayV2: &schedulerpb.EveryDay{
								EveryDayAt: timestamppb.New(now),
							}}},
					Status: schedulerpb.ScheduleStatus_RUNNING,
				},
				ScheduledAt:     timestamppb.New(now),
				Status:          schedulerpb.JobStatus_SCHEDULED,
				RecurrenceCount: 1,
			},
			expectedStopRecurrence: false,
			want: &orchestratorpb.ProcessEnrichedEventRequest{
				StartTime: start,
				EndTime:   end,
				Event: &orchestratorpb.ProcessEnrichedEventRequest_AppUsageDailyEvent{
					AppUsageDailyEvent: &orchestratorpb.AppUsageDailyEvent{
						Day:       start,
						EventType: eventpb.EventType_APP_USAGE_DAILY_EVENT,
					},
				},
			},
		},
		{
			name: "trigger app usage daily event for same day with subscription start",
			job: &schedulerpb.Job{
				Id: uuid.New().String(),
				Schedule: &schedulerpb.Schedule{
					Id:   uuid.New().String(),
					Type: schedulerpb.ScheduleType_APP_USAGE_DAILY_RULE_EVENT,
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_AppUsageRuleEvent{AppUsageRuleEvent: &schedulerpb.AppUsageRuleEvent{
							SubsStartTime: timestamppb.New(yesterdayNow),
						}}},
					RecurrenceLimit:         5,
					StopRecurrenceOnFailure: false,
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_EveryDayV2{
							EveryDayV2: &schedulerpb.EveryDay{
								EveryDayAt: timestamppb.New(now),
							}}},
					Status: schedulerpb.ScheduleStatus_RUNNING,
				},
				ScheduledAt:     timestamppb.New(now),
				Status:          schedulerpb.JobStatus_SCHEDULED,
				RecurrenceCount: 1,
			},
			expectedStopRecurrence: false,
			want: &orchestratorpb.ProcessEnrichedEventRequest{
				StartTime: timestamppb.New(yesterdayNow),
				Event: &orchestratorpb.ProcessEnrichedEventRequest_AppUsageDailyEvent{
					AppUsageDailyEvent: &orchestratorpb.AppUsageDailyEvent{
						Day:       timestamppb.New(time.Date(yesterdayNow.Year(), yesterdayNow.Month(), yesterdayNow.Day(), 0, 0, 0, 0, datetime.IST)),
						EventType: eventpb.EventType_APP_USAGE_DAILY_EVENT,
					},
				},
			},
		},
	}

	for _, test := range tests {
		test := test
		t.Run(test.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			ctx := context.Background()
			orchestratorPublisher := qmocks.NewMockPublisher(ctrl)

			orchestratorPublisher.EXPECT().Publish(gomock.Any(), protomocks.NewProtoMatcher(test.want)).Return("msg-id", nil)
			action := actions.NewTriggerDailyRuleAction(orchestratorPublisher)
			stopRecc, err := action.Execute(ctx, test.job)
			a := require.New(t)
			a.Nil(err)
			a.Equal(test.expectedStopRecurrence, stopRecc)
		})
	}
}

func TestCricketAction(t *testing.T) {
	now := time.Now()
	updateMatchFm, err := fieldmaskpb.New(&sportspb.Match{}, "start_time", "end_time")
	if err != nil {
		t.Fatal(err)
	}
	type args struct {
		job                      *schedulerpb.Job
		vgFetchMatchReq          *vgfittt.FetchCricketMatchDetailsRequest
		vgFetchMatchResp         *vgfittt.FetchCricketMatchDetailsResponse
		enableUpdateMatchDaoCall bool
		sportsMatch              *sportspb.Match
		enableTriggerExecStep    bool
		initiateMatchUpdateReq   *sportspb.InitiateMatchUpdateRequest
		initiateMatchUpdateResp  *sportspb.InitiateMatchUpdateResponse
		pnJobSchedule            *schedulerpb.Schedule
		pnJob                    *schedulerpb.Job
	}
	conf := &config.SchedulerService{MaxJobs: 5, BatchSize: 5,
		TriggerJobsDeadlineInSeconds: 9000000}
	tests := []struct {
		name                   string
		args                   args
		expectedStopRecurrence bool
	}{
		{
			name: "trigger exec action",
			args: args{
				job: &schedulerpb.Job{
					Id: uuid.New().String(),
					Schedule: &schedulerpb.Schedule{
						Id:   uuid.New().String(),
						Type: schedulerpb.ScheduleType_CRICKET_MATCH,
						JobData: &schedulerpb.JobData{
							Data: &schedulerpb.JobData_CricketMatch{
								CricketMatch: &schedulerpb.CricketMatch{
									Innings:   []uint32{1, 2},
									MatchId:   "slind_2021_odi_01",
									EventTags: []string{"tournamentTag1"},
									MatchDetails: &schedulerpb.SportsMatchDetails{
										StartTime: timestamppb.New(now.Add(time.Hour * -2)),
										EndTime:   timestamppb.New(now),
										MatchId:   "slind_2021_odi_01",
										ExecScope: &scopepb.RuleExecutionScope{
											Scope: &scopepb.RuleExecutionScope_Tags{
												Tags: &scopepb.TagIdsList{TagIds: []string{"tournamentTag1"}},
											},
										},
										OfficialStartTime: timestamppb.New(now.Add(time.Hour * -2)),
										TournamentTag:     "tournamentTag1",
										Teams: []*schedulerpb.SportTeam{{Name: "Chennai Super Kings", Code: "CSK"},
											{Name: "Mumbai Indians", Code: "MI"}},
										RetrySuffix: "retry1",
										EventTags:   []string{"tournamentTag1"},
									},
									Actions: []schedulerpb.SportsMatchAction{
										schedulerpb.SportsMatchAction_CRICKET_MATCH_TRIGGER_EXECUTION,
									},
								},
							}},
						RecurrenceLimit:         5,
						StopRecurrenceOnFailure: false,
						Recurrence: &schedulerpb.Recurrence{
							Recurrence: &schedulerpb.Recurrence_Once{
								Once: timestamppb.New(now.Add(-2 * time.Hour)),
							},
						},
						Status: schedulerpb.ScheduleStatus_RUNNING,
					},
					ScheduledAt:     timestamppb.New(now.Add(-2 * time.Hour)),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
				vgFetchMatchReq: &vgfittt.FetchCricketMatchDetailsRequest{
					Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ROANUZ},
					MatchId: "slind_2021_odi_01",
				},
				vgFetchMatchResp: &vgfittt.FetchCricketMatchDetailsResponse{
					MatchStartAt:        timestamppb.New(now.Add(time.Hour * -2)),
					EstimatedMatchEnd:   timestamppb.New(now),
					ApproximateMatchEnd: timestamppb.New(now),
					MatchStatus:         vgfittt.MatchStatus_COMPLETED,
					MatchResult: &vgfittt.CricketMatchResult{
						ResultType:    vgfittt.CricketMatchResultType_CRICKET_MATCH_WON,
						WinningTeamId: "team1",
					},
					Status: rpc.StatusOk(),
				},
				enableTriggerExecStep: true,
				initiateMatchUpdateReq: &sportspb.InitiateMatchUpdateRequest{
					MatchRequest: &sportspb.InitiateMatchUpdateRequest_CricketMatch{
						CricketMatch: &sportspb.CricketMatchRequest{
							MatchId: "slind_2021_odi_01",
							Innings: []uint32{1, 2},
						}},
					ExecScope: &scopepb.RuleExecutionScope{
						Scope: &scopepb.RuleExecutionScope_Tags{
							Tags: &scopepb.TagIdsList{TagIds: []string{"tournamentTag1"}},
						},
					},
					RetrySuffix: "retry1",
					StartTime:   timestamppb.New(now.Add(time.Hour * -2)),
					EndTime:     timestamppb.New(now),
					EventTags:   []string{"tournamentTag1"},
				},
				initiateMatchUpdateResp: &sportspb.InitiateMatchUpdateResponse{
					Status: rpc.StatusOk(),
				},
				pnJobSchedule: &schedulerpb.Schedule{
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_Once{
							Once: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
								TriggerJobsDeadlineInSeconds))),
						},
					},
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_AggrTriggerEvent{
							AggrTriggerEvent: &schedulerpb.AggrExecTriggerEvent{
								Data: &aggrconsumerpb.EventData{
									Event: &aggrconsumerpb.EventData_CricketAggrEvent{
										CricketAggrEvent: &aggrconsumerpb.CricketAggregationEvent{
											EventDay: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
												TriggerJobsDeadlineInSeconds))),
											MatchId: "slind_2021_odi_01",
										},
									},
								},
								AggrType: aggrpb.AggregationType_AGGREGATED_SPORTS_RULES_DEPOSIT_NOTIFICATION,
							},
						},
					},
					Description:     "Push Notification aggregation trigger event - slind_2021_odi_01",
					Type:            schedulerpb.ScheduleType_AGGR_EXEC_TRIGGER_EVENT,
					Status:          schedulerpb.ScheduleStatus_RUNNING,
					RecurrenceLimit: 1,
				},
				pnJob: &schedulerpb.Job{
					Schedule: &schedulerpb.Schedule{},
					ScheduledAt: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
						TriggerJobsDeadlineInSeconds))),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
			},

			expectedStopRecurrence: true,
		},
		{
			name: "trigger exec action with update match action",
			args: args{
				job: &schedulerpb.Job{
					Id: uuid.New().String(),
					Schedule: &schedulerpb.Schedule{
						Id:   uuid.New().String(),
						Type: schedulerpb.ScheduleType_CRICKET_MATCH,
						JobData: &schedulerpb.JobData{
							Data: &schedulerpb.JobData_CricketMatch{
								CricketMatch: &schedulerpb.CricketMatch{
									Innings:   []uint32{1, 2},
									MatchId:   "slind_2021_odi_01",
									EventTags: []string{"tournamentTag1"},
									MatchDetails: &schedulerpb.SportsMatchDetails{
										StartTime:         timestamppb.New(now.Add(time.Hour * -2)),
										EndTime:           timestamppb.New(now),
										MatchId:           "slind_2021_odi_01",
										OfficialStartTime: timestamppb.New(now.Add(time.Hour * -2)),
										TournamentTag:     "tournamentTag1",
										Teams: []*schedulerpb.SportTeam{{Name: "Chennai Super Kings", Code: "CSK"},
											{Name: "Mumbai Indians", Code: "MI"}},
										RetrySuffix: "retry1",
										EventTags:   []string{"tournamentTag1"},
									},
									Actions: []schedulerpb.SportsMatchAction{
										schedulerpb.SportsMatchAction_CRICKET_MATCH_TRIGGER_EXECUTION,
										schedulerpb.SportsMatchAction_CRICKET_MATCH_UPDATE_DETAILS,
									},
								},
							}},
						RecurrenceLimit:         5,
						StopRecurrenceOnFailure: false,
						Recurrence: &schedulerpb.Recurrence{
							Recurrence: &schedulerpb.Recurrence_Once{
								Once: timestamppb.New(now.Add(-2 * time.Hour)),
							},
						},
						Status: schedulerpb.ScheduleStatus_RUNNING,
					},
					ScheduledAt:     timestamppb.New(now.Add(-2 * time.Hour)),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
				vgFetchMatchReq: &vgfittt.FetchCricketMatchDetailsRequest{
					Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ROANUZ},
					MatchId: "slind_2021_odi_01",
				},
				vgFetchMatchResp: &vgfittt.FetchCricketMatchDetailsResponse{
					MatchStartAt:        timestamppb.New(now.Add(time.Hour * -2)),
					EstimatedMatchEnd:   timestamppb.New(now),
					ApproximateMatchEnd: timestamppb.New(now),
					MatchStatus:         vgfittt.MatchStatus_COMPLETED,
					MatchResult: &vgfittt.CricketMatchResult{
						ResultType:    vgfittt.CricketMatchResultType_CRICKET_MATCH_WON,
						WinningTeamId: "team1",
					},
					Status: rpc.StatusOk(),
				},
				enableUpdateMatchDaoCall: true,
				sportsMatch: &sportspb.Match{
					Vendor:        sportspb.Vendor_VENDOR_ROUNAZ,
					VendorMatchId: "slind_2021_odi_01",
					StartTime:     timestamppb.New(now.Add(time.Hour * -2)),
					EndTime:       timestamppb.New(now),
				},

				enableTriggerExecStep: true,
				initiateMatchUpdateReq: &sportspb.InitiateMatchUpdateRequest{
					MatchRequest: &sportspb.InitiateMatchUpdateRequest_CricketMatch{
						CricketMatch: &sportspb.CricketMatchRequest{
							MatchId: "slind_2021_odi_01",
							Innings: []uint32{1, 2},
						}},
					ExecScope: &scopepb.RuleExecutionScope{
						Scope: &scopepb.RuleExecutionScope_Tags{
							Tags: &scopepb.TagIdsList{TagIds: []string{"tournamentTag1"}},
						},
					},
					RetrySuffix: "retry1",
					StartTime:   timestamppb.New(now.Add(time.Hour * -2)),
					EndTime:     timestamppb.New(now),
					EventTags:   []string{"tournamentTag1"},
				},
				initiateMatchUpdateResp: &sportspb.InitiateMatchUpdateResponse{
					Status: rpc.StatusOk(),
				},
				pnJobSchedule: &schedulerpb.Schedule{
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_Once{
							Once: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
								TriggerJobsDeadlineInSeconds))),
						},
					},
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_AggrTriggerEvent{
							AggrTriggerEvent: &schedulerpb.AggrExecTriggerEvent{
								Data: &aggrconsumerpb.EventData{
									Event: &aggrconsumerpb.EventData_CricketAggrEvent{
										CricketAggrEvent: &aggrconsumerpb.CricketAggregationEvent{
											EventDay: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
												TriggerJobsDeadlineInSeconds))),
											MatchId: "slind_2021_odi_01",
										},
									},
								},
								AggrType: aggrpb.AggregationType_AGGREGATED_SPORTS_RULES_DEPOSIT_NOTIFICATION,
							},
						},
					},
					Description:     "Push Notification aggregation trigger event - slind_2021_odi_01",
					Type:            schedulerpb.ScheduleType_AGGR_EXEC_TRIGGER_EVENT,
					Status:          schedulerpb.ScheduleStatus_RUNNING,
					RecurrenceLimit: 1,
				},
				pnJob: &schedulerpb.Job{
					Schedule: &schedulerpb.Schedule{},
					ScheduledAt: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
						TriggerJobsDeadlineInSeconds))),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
			},

			expectedStopRecurrence: true,
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		ctx := context.Background()

		sportsClient := sportsmock.NewMockSportsManagerClient(ctrl)
		vgClient := vgmock.NewMockFitttClient(ctrl)
		jobsDaoMock := mocks.NewMockJobsDao(ctrl)
		schedulesDaoMock := mocks.NewMockSchedulesDao(ctrl)
		matchesDaoMock := sportsdaomock.NewMockMatchesDao(ctrl)
		txnExec := storagev2.NewGormTxnExecutor(db)
		vgClient.EXPECT().FetchCricketMatchDetails(gomock.Any(), protomocks.NewProtoMatcher(test.args.vgFetchMatchReq)).Return(test.args.
			vgFetchMatchResp, nil)
		if test.args.enableUpdateMatchDaoCall {
			matchesDaoMock.EXPECT().UpdateMatch(gomock.Any(), protomocks.NewProtoMatcher(updateMatchFm),
				protomocks.NewProtoMatcher(test.args.sportsMatch))
		}
		if test.args.enableTriggerExecStep {
			sportsClient.EXPECT().InitiateMatchUpdate(gomock.Any(), protomocks.NewProtoMatcher(test.args.initiateMatchUpdateReq)).Return(test.args.
				initiateMatchUpdateResp, nil)
			schedulesDaoMock.EXPECT().CreateSchedule(gomock.Any(), NewPNScheduleApproxTimeMatcher(test.args.pnJobSchedule)).Return(test.args.
				pnJobSchedule, nil)
			jobsDaoMock.EXPECT().CreateJob(gomock.Any(), NewPNJobApproxTimeMatcher(test.args.pnJob)).Return(test.args.pnJob, nil)

		}
		a := require.New(t)
		gconf, _ := genconf.NewSchedulerService()
		err = gconf.Set(conf, false, nil)
		a.NoError(err)
		action := actions.NewTriggerCricketMatchAction(sportsClient, vgClient, gconf, txnExec, jobsDaoMock, schedulesDaoMock, matchesDaoMock)
		stopRecc, err := action.Execute(ctx, test.args.job)

		a.Nil(err)
		a.Equal(test.expectedStopRecurrence, stopRecc)
	}
}

type PNScheduleApproxTimeMatcher struct {
	want *schedulerpb.Schedule
}

func NewPNScheduleApproxTimeMatcher(want *schedulerpb.Schedule) *PNScheduleApproxTimeMatcher {
	want = proto.Clone(want).(*schedulerpb.Schedule)
	want.Recurrence = &schedulerpb.Recurrence{
		Recurrence: &schedulerpb.Recurrence_Once{
			Once: getTimeInMinutePrecision(want.Recurrence.GetOnce()),
		},
	}
	if want.GetJobData().GetAggrTriggerEvent().GetData().GetCricketAggrEvent() != nil {
		want.GetJobData().GetAggrTriggerEvent().GetData().GetCricketAggrEvent().EventDay = getTimeInMinutePrecision(
			want.GetJobData().GetAggrTriggerEvent().GetData().GetCricketAggrEvent().EventDay)
	}
	return &PNScheduleApproxTimeMatcher{want: want}
}

func getTimeInMinutePrecision(ts *timestamppb.Timestamp) *timestamppb.Timestamp {
	t := ts.AsTime()
	return timestamppb.New(time.Unix(0, int64(math.Round(float64(t.UnixNano())/float64(time.Minute)))*int64(time.Minute)))
}

func (p *PNScheduleApproxTimeMatcher) Matches(got interface{}) bool {

	gotSchedule, ok := got.(*schedulerpb.Schedule)
	if !ok {
		return false
	}
	gotSchedule = proto.Clone(gotSchedule).(*schedulerpb.Schedule)
	gotSchedule.Recurrence = &schedulerpb.Recurrence{
		Recurrence: &schedulerpb.Recurrence_Once{
			Once: getTimeInMinutePrecision(gotSchedule.Recurrence.GetOnce()),
		},
	}
	if gotSchedule.GetJobData().GetAggrTriggerEvent().GetData().GetCricketAggrEvent() != nil {
		gotSchedule.GetJobData().GetAggrTriggerEvent().GetData().GetCricketAggrEvent().EventDay = getTimeInMinutePrecision(
			gotSchedule.GetJobData().GetAggrTriggerEvent().GetData().GetCricketAggrEvent().EventDay)
	}

	return cmp.Diff(p.want, gotSchedule, protocmp.Transform()) == ""
}

// String describes what the matcher matches.
func (p *PNScheduleApproxTimeMatcher) String() string {
	return fmt.Sprint(p.want)
}

type PNJobApproxTimeMatcher struct {
	want *schedulerpb.Job
}

func NewPNJobApproxTimeMatcher(want *schedulerpb.Job) *PNJobApproxTimeMatcher {
	want = proto.Clone(want).(*schedulerpb.Job)
	want.ScheduledAt = getTimeInMinutePrecision(want.ScheduledAt)
	return &PNJobApproxTimeMatcher{want: want}
}

func (p *PNJobApproxTimeMatcher) Matches(got interface{}) bool {

	gotSchedule, ok := got.(*schedulerpb.Job)
	if !ok {
		return false
	}
	gotSchedule = proto.Clone(gotSchedule).(*schedulerpb.Job)
	gotSchedule.ScheduledAt = getTimeInMinutePrecision(gotSchedule.ScheduledAt)
	return cmp.Diff(p.want, gotSchedule, protocmp.Transform()) == ""
}

// String describes what the matcher matches.
func (p *PNJobApproxTimeMatcher) String() string {
	return fmt.Sprint(p.want)
}

func TestFootballAction(t *testing.T) {
	now := time.Now()
	type args struct {
		job                     *schedulerpb.Job
		vgFetchMatchReq         *vgfittt.FetchFootballMatchDetailsRequest
		vgFetchMatchResp        *vgfittt.FetchFootballMatchDetailsResponse
		enableTriggerExecStep   bool
		initiateMatchUpdateReq  *sportspb.InitiateMatchUpdateRequest
		initiateMatchUpdateResp *sportspb.InitiateMatchUpdateResponse
		pnJobSchedule           *schedulerpb.Schedule
		pnJob                   *schedulerpb.Job
	}
	conf := &config.SchedulerService{MaxJobs: 5, BatchSize: 5,
		TriggerJobsDeadlineInSeconds: 9000000}
	tests := []struct {
		name                   string
		args                   args
		expectedStopRecurrence bool
	}{
		{
			name: "trigger exec action",
			args: args{
				job: &schedulerpb.Job{
					Id: uuid.New().String(),
					Schedule: &schedulerpb.Schedule{
						Id:   uuid.New().String(),
						Type: schedulerpb.ScheduleType_FOOTBALL_MATCH,
						JobData: &schedulerpb.JobData{
							Data: &schedulerpb.JobData_FootballMatch{
								FootballMatch: &schedulerpb.FootballMatch{
									MatchId:   "slind_2021_odi_01",
									EventTags: []string{"tournamentTag1"},
									MatchDetails: &schedulerpb.SportsMatchDetails{
										StartTime: timestamppb.New(now.Add(time.Hour * -2)),
										EndTime:   timestamppb.New(now),
										MatchId:   "slind_2021_odi_01",
										ExecScope: &scopepb.RuleExecutionScope{
											Scope: &scopepb.RuleExecutionScope_Tags{
												Tags: &scopepb.TagIdsList{TagIds: []string{"tournamentTag1"}},
											},
										},
										OfficialStartTime: timestamppb.New(now.Add(time.Hour * -2)),
										TournamentTag:     "tournamentTag1",
										Teams: []*schedulerpb.SportTeam{{Name: "Chennai Super Kings", Code: "CSK"},
											{Name: "Mumbai Indians", Code: "MI"}},
										RetrySuffix: "retry1",
										EventTags:   []string{"tournamentTag1"},
									},
								},
							}},
						RecurrenceLimit:         5,
						StopRecurrenceOnFailure: false,
						Recurrence: &schedulerpb.Recurrence{
							Recurrence: &schedulerpb.Recurrence_Once{
								Once: timestamppb.New(now.Add(-2 * time.Hour)),
							},
						},
						Status: schedulerpb.ScheduleStatus_RUNNING,
					},
					ScheduledAt:     timestamppb.New(now.Add(-2 * time.Hour)),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
				vgFetchMatchReq: &vgfittt.FetchFootballMatchDetailsRequest{
					Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ROANUZ},
					MatchId: "slind_2021_odi_01",
				},
				vgFetchMatchResp: &vgfittt.FetchFootballMatchDetailsResponse{
					MatchStartAt:        timestamppb.New(now.Add(time.Hour * -2)),
					ApproximateMatchEnd: timestamppb.New(now),
					MatchStatus:         vgfittt.MatchStatus_COMPLETED,
					MatchResult: &vgfittt.FootballMatchResult{
						ResultType:  vgfittt.FootballMatchResultType_FOOTBALL_MATCH_WON,
						WinningTeam: &vgfittt.Team{TeamId: "team1", Code: "T1"},
					},
					Status: rpc.StatusOk(),
				},
				enableTriggerExecStep: true,
				initiateMatchUpdateReq: &sportspb.InitiateMatchUpdateRequest{
					MatchRequest: &sportspb.InitiateMatchUpdateRequest_FootballMatch{
						FootballMatch: &sportspb.FootballMatchRequest{
							MatchId: "slind_2021_odi_01",
						}},
					ExecScope: &scopepb.RuleExecutionScope{
						Scope: &scopepb.RuleExecutionScope_Tags{
							Tags: &scopepb.TagIdsList{TagIds: []string{"tournamentTag1"}},
						},
					},
					RetrySuffix: "retry1",
					StartTime:   timestamppb.New(now.Add(time.Hour * -2)),
					EndTime:     timestamppb.New(now),
					EventTags:   []string{"tournamentTag1"},
				},
				initiateMatchUpdateResp: &sportspb.InitiateMatchUpdateResponse{
					Status: rpc.StatusOk(),
				},
				pnJobSchedule: &schedulerpb.Schedule{
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_Once{
							Once: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
								TriggerJobsDeadlineInSeconds))),
						},
					},
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_AggrTriggerEvent{
							AggrTriggerEvent: &schedulerpb.AggrExecTriggerEvent{
								Data: &aggrconsumerpb.EventData{
									Event: &aggrconsumerpb.EventData_FootballAggrEvent{
										FootballAggrEvent: &aggrconsumerpb.FootballAggregationEvent{
											MatchId: "slind_2021_odi_01",
										},
									},
								},
								AggrType: aggrpb.AggregationType_AGGREGATED_SPORTS_RULES_DEPOSIT_NOTIFICATION,
							},
						},
					},
					Description:     "Push Notification aggregation trigger event - slind_2021_odi_01",
					Type:            schedulerpb.ScheduleType_AGGR_EXEC_TRIGGER_EVENT,
					Status:          schedulerpb.ScheduleStatus_RUNNING,
					RecurrenceLimit: 1,
				},
				pnJob: &schedulerpb.Job{
					Schedule: &schedulerpb.Schedule{},
					ScheduledAt: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
						TriggerJobsDeadlineInSeconds))),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
			},
		},
		{
			name: "trigger exec action without exec scope",
			args: args{
				job: &schedulerpb.Job{
					Id: uuid.New().String(),
					Schedule: &schedulerpb.Schedule{
						Id:   uuid.New().String(),
						Type: schedulerpb.ScheduleType_FOOTBALL_MATCH,
						JobData: &schedulerpb.JobData{
							Data: &schedulerpb.JobData_FootballMatch{
								FootballMatch: &schedulerpb.FootballMatch{
									MatchId:   "slind_2021_odi_01",
									EventTags: []string{"tournamentTag1"},
									MatchDetails: &schedulerpb.SportsMatchDetails{
										StartTime:         timestamppb.New(now.Add(time.Hour * -2)),
										EndTime:           timestamppb.New(now),
										MatchId:           "slind_2021_odi_01",
										OfficialStartTime: timestamppb.New(now.Add(time.Hour * -2)),
										TournamentTag:     "tournamentTag1",
										Teams: []*schedulerpb.SportTeam{{Name: "Chennai Super Kings", Code: "CSK"},
											{Name: "Mumbai Indians", Code: "MI"}},
										RetrySuffix: "retry1",
										EventTags:   []string{"tournamentTag1"},
									},
								},
							}},
						RecurrenceLimit:         5,
						StopRecurrenceOnFailure: false,
						Recurrence: &schedulerpb.Recurrence{
							Recurrence: &schedulerpb.Recurrence_Once{
								Once: timestamppb.New(now.Add(-2 * time.Hour)),
							},
						},
						Status: schedulerpb.ScheduleStatus_RUNNING,
					},
					ScheduledAt:     timestamppb.New(now.Add(-2 * time.Hour)),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
				vgFetchMatchReq: &vgfittt.FetchFootballMatchDetailsRequest{
					Header:  &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ROANUZ},
					MatchId: "slind_2021_odi_01",
				},
				vgFetchMatchResp: &vgfittt.FetchFootballMatchDetailsResponse{
					MatchStartAt:        timestamppb.New(now.Add(time.Hour * -2)),
					ApproximateMatchEnd: timestamppb.New(now),
					MatchStatus:         vgfittt.MatchStatus_COMPLETED,
					MatchResult: &vgfittt.FootballMatchResult{
						ResultType:  vgfittt.FootballMatchResultType_FOOTBALL_MATCH_WON,
						WinningTeam: &vgfittt.Team{TeamId: "team1", Code: "T1"},
					},
					Status: rpc.StatusOk(),
				},
				enableTriggerExecStep: true,
				initiateMatchUpdateReq: &sportspb.InitiateMatchUpdateRequest{
					MatchRequest: &sportspb.InitiateMatchUpdateRequest_FootballMatch{
						FootballMatch: &sportspb.FootballMatchRequest{
							MatchId: "slind_2021_odi_01",
						}},
					ExecScope: &scopepb.RuleExecutionScope{
						Scope: &scopepb.RuleExecutionScope_Tags{
							Tags: &scopepb.TagIdsList{TagIds: []string{"tournamentTag1"}},
						},
					},
					RetrySuffix: "retry1",
					StartTime:   timestamppb.New(now.Add(time.Hour * -2)),
					EndTime:     timestamppb.New(now),
					EventTags:   []string{"tournamentTag1"},
				},
				initiateMatchUpdateResp: &sportspb.InitiateMatchUpdateResponse{
					Status: rpc.StatusOk(),
				},
				pnJobSchedule: &schedulerpb.Schedule{
					Recurrence: &schedulerpb.Recurrence{
						Recurrence: &schedulerpb.Recurrence_Once{
							Once: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
								TriggerJobsDeadlineInSeconds))),
						},
					},
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_AggrTriggerEvent{
							AggrTriggerEvent: &schedulerpb.AggrExecTriggerEvent{
								Data: &aggrconsumerpb.EventData{
									Event: &aggrconsumerpb.EventData_FootballAggrEvent{
										FootballAggrEvent: &aggrconsumerpb.FootballAggregationEvent{
											MatchId: "slind_2021_odi_01",
										},
									},
								},
								AggrType: aggrpb.AggregationType_AGGREGATED_SPORTS_RULES_DEPOSIT_NOTIFICATION,
							},
						},
					},
					Description:     "Push Notification aggregation trigger event - slind_2021_odi_01",
					Type:            schedulerpb.ScheduleType_AGGR_EXEC_TRIGGER_EVENT,
					Status:          schedulerpb.ScheduleStatus_RUNNING,
					RecurrenceLimit: 1,
				},
				pnJob: &schedulerpb.Job{
					Schedule: &schedulerpb.Schedule{},
					ScheduledAt: timestamppb.New(time.Now().Add(time.Second * time.Duration(conf.
						TriggerJobsDeadlineInSeconds))),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				},
			},
		},
	}

	for _, test := range tests {
		ctrl := gomock.NewController(t)
		ctx := context.Background()

		sportsClient := sportsmock.NewMockSportsManagerClient(ctrl)
		jobsDaoMock := mocks.NewMockJobsDao(ctrl)
		schedulesDaoMock := mocks.NewMockSchedulesDao(ctrl)
		txnExec := storagev2.NewGormTxnExecutor(db)
		if test.args.enableTriggerExecStep {
			sportsClient.EXPECT().InitiateMatchUpdate(gomock.Any(), protomocks.NewProtoMatcher(test.args.initiateMatchUpdateReq)).Return(test.args.
				initiateMatchUpdateResp, nil)
			schedulesDaoMock.EXPECT().CreateSchedule(gomock.Any(), NewPNScheduleApproxTimeMatcher(test.args.pnJobSchedule)).Return(test.args.
				pnJobSchedule, nil)
			jobsDaoMock.EXPECT().CreateJob(gomock.Any(), NewPNJobApproxTimeMatcher(test.args.pnJob)).Return(test.args.pnJob, nil)

		}
		a := require.New(t)
		gconf, _ := genconf.NewSchedulerService()
		err := gconf.Set(conf, false, nil)
		a.NoError(err)
		action := actions.NewTriggerFootballMatchAction(sportsClient, gconf, txnExec, jobsDaoMock, schedulesDaoMock)
		stopRecc, err := action.Execute(ctx, test.args.job)
		a.Nil(err)
		a.Equal(test.expectedStopRecurrence, stopRecc)
	}
}

func TestAggrTriggerAction(t *testing.T) {

	tests := []struct {
		name                   string
		job                    *schedulerpb.Job
		want                   *aggrconsumerpb.PerformAggregationRequest
		expectedStopRecurrence bool
	}{
		{
			name: "Aggr Trigger",
			job: &schedulerpb.Job{
				Id: uuid.New().String(),
				Schedule: &schedulerpb.Schedule{
					Id:   uuid.New().String(),
					Type: schedulerpb.ScheduleType_AGGR_EXEC_TRIGGER_EVENT,
					JobData: &schedulerpb.JobData{
						Data: &schedulerpb.JobData_AggrTriggerEvent{
							AggrTriggerEvent: &schedulerpb.AggrExecTriggerEvent{
								Data: &aggrconsumerpb.EventData{
									Event: &aggrconsumerpb.EventData_FootballAggrEvent{
										FootballAggrEvent: &aggrconsumerpb.FootballAggregationEvent{
											MatchId: "match1",
										},
									},
								},
								AggrType: aggrpb.AggregationType_AGGREGATED_SPORTS_RULES_DEPOSIT_NOTIFICATION,
							}}},
					Status: schedulerpb.ScheduleStatus_RUNNING,
				},
				ScheduledAt:     timestamppb.Now(),
				Status:          schedulerpb.JobStatus_SCHEDULED,
				RecurrenceCount: 1,
			},
			expectedStopRecurrence: false,
			want: &aggrconsumerpb.PerformAggregationRequest{
				EventData: &aggrconsumerpb.EventData{
					Event: &aggrconsumerpb.EventData_FootballAggrEvent{
						FootballAggrEvent: &aggrconsumerpb.FootballAggregationEvent{
							MatchId: "match1",
						},
					},
				},
				ExecType: aggrpb.AggregationType_AGGREGATED_SPORTS_RULES_DEPOSIT_NOTIFICATION,
			},
		},
	}

	for _, test := range tests {
		test := test
		t.Run(test.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			ctx := context.Background()
			aggrTriggerPublisher := qmocks.NewMockPublisher(ctrl)
			aggrTriggerPublisher.EXPECT().Publish(gomock.Any(), protomocks.NewProtoMatcher(test.want)).Return("msg-id", nil)
			action := actions.NewPublishAggrTriggerAction(aggrTriggerPublisher)
			stopRecc, err := action.Execute(ctx, test.job)
			a := require.New(t)
			a.Nil(err)
			a.Equal(test.expectedStopRecurrence, stopRecc)
		})
	}
}

func TestUpdateSportsChallengeStatsAction(t *testing.T) {
	type args struct {
		job *schedulerpb.Job
	}
	scheduleTime := time.Date(2023, 9, 25, 1, 0, 0, 0, datetime.IST)

	sportsChallengeJobData := &schedulerpb.Job{
		Id: uuid.New().String(),
		Schedule: &schedulerpb.Schedule{
			Id:   uuid.New().String(),
			Type: schedulerpb.ScheduleType_UPDATE_SPORTS_CHALLENGE_STATS,
			JobData: &schedulerpb.JobData{
				Data: &schedulerpb.JobData_SportsChallengeStatsScheduleData{
					SportsChallengeStatsScheduleData: &schedulerpb.SportsChallengeStatsScheduleData{
						TournamentTagId:                    "tournament1",
						SportsType:                         sportspb.SportsType_SPORTS_TYPE_CRICKET,
						StartTime:                          timestamppb.New(scheduleTime.Add(-21 * 24 * time.Hour)),
						EndTime:                            timestamppb.New(scheduleTime.Add(10 * 24 * time.Hour)),
						WeeklyRewardsPublishDay:            int32(time.Now().Weekday()),
						TournamentSecondWeekStartTimestamp: timestamppb.New(scheduleTime.Add(-14 * 24 * time.Hour)),
						NoOfUsersQualifiedForWeeklyRewards: 5,
					},
				}},
			Status: schedulerpb.ScheduleStatus_RUNNING,
			Recurrence: &schedulerpb.Recurrence{
				Recurrence: &schedulerpb.Recurrence_Once{
					Once: timestamppb.New(scheduleTime),
				},
			},
		},
		ScheduledAt:     timestamppb.New(scheduleTime),
		Status:          schedulerpb.JobStatus_SCHEDULED,
		RecurrenceCount: 1,
	}

	tests := []struct {
		name                                       string
		args                                       args
		updateSportsChallengeStatsRequest          *fittt.UpdateSportsChallengeStatsRequest
		enablePublishSportsChallengeWeeklyRewards  bool
		publishSportsChallengeWeeklyRewardsRequest *fittt.PublishSportsChallengeWeeklyRewardsRequest
		expectedStopRecurrence                     bool
	}{
		{
			name: "UpdateSportsChallengeStatsAction publishing configured for the weekday",
			args: args{job: sportsChallengeJobData},
			updateSportsChallengeStatsRequest: &fittt.UpdateSportsChallengeStatsRequest{
				TournamentTagId: "tournament1",
				SportsType:      sportspb.SportsType_SPORTS_TYPE_CRICKET,
				From:            sportsChallengeJobData.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetStartTime(),
				To:              sportsChallengeJobData.GetSchedule().GetJobData().GetSportsChallengeStatsScheduleData().GetEndTime(),
			},
			enablePublishSportsChallengeWeeklyRewards: true,
			publishSportsChallengeWeeklyRewardsRequest: &fittt.PublishSportsChallengeWeeklyRewardsRequest{
				TournamentId:                       "tournament1",
				WeekNo:                             actions.GetPreviousWeekNoForFiSavingsLeague(sportsChallengeJobData),
				NoOfUsersQualifiedForWeeklyRewards: 5,
			},
			expectedStopRecurrence: false,
		},
		{
			name: "UpdateSportsChallengeStatsAction without start and end time, publishing not configured for the weekday",
			args: args{
				job: &schedulerpb.Job{
					Id: uuid.New().String(),
					Schedule: &schedulerpb.Schedule{
						Id:   uuid.New().String(),
						Type: schedulerpb.ScheduleType_UPDATE_SPORTS_CHALLENGE_STATS,
						JobData: &schedulerpb.JobData{
							Data: &schedulerpb.JobData_SportsChallengeStatsScheduleData{
								SportsChallengeStatsScheduleData: &schedulerpb.SportsChallengeStatsScheduleData{
									TournamentTagId:                    "tournament1",
									SportsType:                         sportspb.SportsType_SPORTS_TYPE_CRICKET,
									WeeklyRewardsPublishDay:            int32(time.Now().Add(-24 * time.Hour).Weekday()),
									TournamentSecondWeekStartTimestamp: timestamppb.New(scheduleTime.Add(-14 * 24 * time.Hour)),
									NoOfUsersQualifiedForWeeklyRewards: 5,
								},
							}},
						Status: schedulerpb.ScheduleStatus_RUNNING,
						Recurrence: &schedulerpb.Recurrence{
							Recurrence: &schedulerpb.Recurrence_Once{
								Once: timestamppb.New(scheduleTime),
							},
						},
					},
					ScheduledAt:     timestamppb.New(time.Date(2023, 9, 25, 1, 0, 0, 0, datetime.IST)),
					Status:          schedulerpb.JobStatus_SCHEDULED,
					RecurrenceCount: 1,
				}},
			updateSportsChallengeStatsRequest: &fittt.UpdateSportsChallengeStatsRequest{
				TournamentTagId: "tournament1",
				SportsType:      sportspb.SportsType_SPORTS_TYPE_CRICKET,
				From:            timestamppb.New(datetime.StartOfDay(time.Now().Add(-24 * time.Hour))),
				To:              timestamppb.New(datetime.EndOfDay(time.Now().Add(-24 * time.Hour))),
			},
			enablePublishSportsChallengeWeeklyRewards: false,
			expectedStopRecurrence:                    false,
		},
	}

	for _, test := range tests {
		test := test
		t.Run(test.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			ctx := context.Background()
			fitttClient := fitttMock.NewMockFitttClient(ctrl)
			fitttClient.EXPECT().UpdateSportsChallengeStats(gomock.Any(), test.updateSportsChallengeStatsRequest).Return(
				&fittt.UpdateSportsChallengeStatsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			if test.enablePublishSportsChallengeWeeklyRewards {
				fitttClient.EXPECT().PublishSportsChallengeWeeklyRewards(gomock.Any(), test.publishSportsChallengeWeeklyRewardsRequest).Return(
					&fittt.PublishSportsChallengeWeeklyRewardsResponse{
						Status: rpc.StatusOk(),
					}, nil)
			}
			action := actions.NewUpdateSportsChallengeStatsAction(fitttClient)
			stopRecc, err := action.Execute(ctx, test.args.job)
			a := require.New(t)
			a.Nil(err)
			a.Equal(test.expectedStopRecurrence, stopRecc)
		})
	}
}
