package consumer

import (
	"context"

	queuePb "github.com/epifi/be-common/api/queue"
	p2pConsumePb "github.com/epifi/gamma/api/p2pinvestment/consumer"
	"github.com/epifi/gamma/p2pinvestment/dao"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
)

type Service struct {
	// UnimplementedP2PCxServer is embedded to have forward compatible implementations
	p2pConsumePb.UnimplementedConsumerServer
	investorDao dao.InvestorDao
}

func NewService(investorDao dao.InvestorDao) *Service {
	return &Service{
		investorDao: investorDao,
	}
}

// PerformP2PInvestmentDBOperationsAsync is used to add an Entry in DB
// With the help of retries it make sure we do the DB operation successfully
// In case there is some failure while writing to DB it returns transient failure so that operation can re-triggered
func (s *Service) PerformP2PInvestmentDBOperationsAsync(ctx context.Context, req *p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncRequest) (*p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse, error) {
	var res *p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse
	switch req.PerformDBOperations.(type) {
	case *p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncRequest_UpdateInvestorReq:
		iuErr := s.investorDao.Update(ctx, req.GetUpdateInvestorReq().GetInvestor(), req.GetUpdateInvestorReq().GetInvestorFieldMask())
		if iuErr != nil {
			logger.Error(ctx, "failed to update investor in db", zap.Error(iuErr))
			if req.RequestHeader.IsLastAttempt && (res.ResponseHeader.Status == queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE) {
				// TODO(Brijesh): Check if DLQ is configured
				logger.Info(ctx, "unable to store entry in db")
			}
			return PerformP2pInvestmentDBOperationsAsyncTransientFailureResponse(), nil
		}
	default:
		logger.Error(ctx, "unknown type of DB entry in request param - PerformP2pInvestmentDBOperationsAsync consumer")
		return PerformP2pInvestmentDBOperationsAsyncPermanentFailureResponse(), nil
	}
	return PerformP2pInvestmentDBOperationsAsyncSuccessResponse(), nil
}

func PerformP2pInvestmentDBOperationsAsyncTransientFailureResponse() *p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse {
	return &p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
		},
	}
}

func PerformP2pInvestmentDBOperationsAsyncSuccessResponse() *p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse {
	return &p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}
}

func PerformP2pInvestmentDBOperationsAsyncPermanentFailureResponse() *p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse {
	return &p2pConsumePb.PerformP2PInvestmentDBOperationsAsyncResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
		},
	}
}
