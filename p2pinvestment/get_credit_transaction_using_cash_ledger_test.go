package p2pinvestment_test

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	p2pIPb "github.com/epifi/gamma/api/p2pinvestment"
	p2pVgPb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"
)

func TestService_GetCreditTransactionUsingCashLedger(t *testing.T) {
	ctx := context.Background()
	type args struct {
		req *p2pIPb.GetCreditTransactionUsingCashLedgerRequest
	}
	tests := []struct {
		name       string
		args       args
		want       *p2pIPb.GetCreditTransactionUsingCashLedgerResponse
		wantErr    bool
		setupMocks func(md *mockP2pInvestmentDependencies)
	}{
		{
			name: "Success - Amount and timestamp maps exactly and cash ledger returns expected entry",
			args: args{
				req: &p2pIPb.GetCreditTransactionUsingCashLedgerRequest{
					ActorId:          actorId,
					CreditedAmount:   money.FromPaisa(15000 * 100),
					CreditedAt:       getTimestampWithDeltaDays(1),
					CreditIdentifier: "credit-1",
				},
			},
			setupMocks: func(md *mockP2pInvestmentDependencies) {
				md.mockInvestorDao.EXPECT().GetByActorIdAndVendor(ctx, actorId, p2pIPb.Vendor_VENDOR_LIQUILOANS).Return(getMockInvestor(), nil).Times(1)
				md.mockP2pVG.EXPECT().GetCashLedger(gomock.Any(), gomock.Any()).Return(getMockCashLedgerForGetCreditTransaction(money.FromPaisa(15000*100),
					getTimestampWithDeltaDays(1), p2pVgPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_PARTIAL_REPAYMENT), nil).Times(1)
				md.mockCashLedgerHelper.EXPECT().GetAndMapInvestmentTransactionFromCashLedger(gomock.Any(), gomock.Any()).Return(
					getMockTransactionForCreditNotification("txn-1", "credit-1"), nil)
			},
			want: &p2pIPb.GetCreditTransactionUsingCashLedgerResponse{
				Status:            rpc.StatusOk(),
				CreditTransaction: getMockTransactionForCreditNotification("txn-1", "credit-1"),
			},
		},
		{
			name: "Success - Amount is near and timestamp within 2 days and cash ledger returns expected entry",
			args: args{
				req: &p2pIPb.GetCreditTransactionUsingCashLedgerRequest{
					ActorId:          actorId,
					CreditedAmount:   money.FromPaisa(15000 * 100),
					CreditedAt:       getTimestampWithDeltaDays(1),
					CreditIdentifier: "credit-1",
				},
			},
			setupMocks: func(md *mockP2pInvestmentDependencies) {
				md.mockInvestorDao.EXPECT().GetByActorIdAndVendor(ctx, actorId, p2pIPb.Vendor_VENDOR_LIQUILOANS).Return(getMockInvestor(), nil).Times(1)
				md.mockP2pVG.EXPECT().GetCashLedger(gomock.Any(), gomock.Any()).Return(getMockCashLedgerForGetCreditTransaction(money.FromPaisa(15009*100),
					getTimestampWithDeltaDays(3), p2pVgPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_PARTIAL_REPAYMENT), nil).Times(1)
				md.mockCashLedgerHelper.EXPECT().GetAndMapInvestmentTransactionFromCashLedger(gomock.Any(), gomock.Any()).Return(
					getMockTransactionForCreditNotification("txn-1", "credit-1"), nil)
			},
			want: &p2pIPb.GetCreditTransactionUsingCashLedgerResponse{
				Status:            rpc.StatusOk(),
				CreditTransaction: getMockTransactionForCreditNotification("txn-1", "credit-1"),
			},
		},
		{
			name: "Not found - Amount map and timestamp not within 3 days",
			args: args{
				req: &p2pIPb.GetCreditTransactionUsingCashLedgerRequest{
					ActorId:          actorId,
					CreditedAmount:   money.FromPaisa(15000 * 100),
					CreditedAt:       getTimestampWithDeltaDays(1),
					CreditIdentifier: "credit-1",
				},
			},
			setupMocks: func(md *mockP2pInvestmentDependencies) {
				md.mockInvestorDao.EXPECT().GetByActorIdAndVendor(ctx, actorId, p2pIPb.Vendor_VENDOR_LIQUILOANS).Return(getMockInvestor(), nil).Times(1)
				md.mockP2pVG.EXPECT().GetCashLedger(gomock.Any(), gomock.Any()).Return(getMockCashLedgerForGetCreditTransaction(money.FromPaisa(15009*100),
					getTimestampWithDeltaDays(5), p2pVgPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_PARTIAL_REPAYMENT), nil).Times(1)
			},
			want: &p2pIPb.GetCreditTransactionUsingCashLedgerResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "Not found - Amount and timestamp maps but cash ledger helper returns nil",
			args: args{
				req: &p2pIPb.GetCreditTransactionUsingCashLedgerRequest{
					ActorId:          actorId,
					CreditedAmount:   money.FromPaisa(15000 * 100),
					CreditedAt:       getTimestampWithDeltaDays(1),
					CreditIdentifier: "credit-1",
				},
			},
			setupMocks: func(md *mockP2pInvestmentDependencies) {
				md.mockInvestorDao.EXPECT().GetByActorIdAndVendor(ctx, actorId, p2pIPb.Vendor_VENDOR_LIQUILOANS).Return(getMockInvestor(), nil).Times(1)
				md.mockP2pVG.EXPECT().GetCashLedger(gomock.Any(), gomock.Any()).Return(getMockCashLedgerForGetCreditTransaction(money.FromPaisa(15000*100),
					getTimestampWithDeltaDays(1), p2pVgPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_PARTIAL_REPAYMENT), nil).Times(1)
				md.mockCashLedgerHelper.EXPECT().GetAndMapInvestmentTransactionFromCashLedger(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &p2pIPb.GetCreditTransactionUsingCashLedgerResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "Not found - Amount and timestamp maps but cash ledger helper returns already mapped txm",
			args: args{
				req: &p2pIPb.GetCreditTransactionUsingCashLedgerRequest{
					ActorId:          actorId,
					CreditedAmount:   money.FromPaisa(15000 * 100),
					CreditedAt:       getTimestampWithDeltaDays(1),
					CreditIdentifier: "credit-1",
				},
			},
			setupMocks: func(md *mockP2pInvestmentDependencies) {
				md.mockInvestorDao.EXPECT().GetByActorIdAndVendor(ctx, actorId, p2pIPb.Vendor_VENDOR_LIQUILOANS).Return(getMockInvestor(), nil).Times(1)
				md.mockP2pVG.EXPECT().GetCashLedger(gomock.Any(), gomock.Any()).Return(getMockCashLedgerForGetCreditTransaction(money.FromPaisa(15000*100),
					getTimestampWithDeltaDays(1), p2pVgPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_PARTIAL_REPAYMENT), nil).Times(1)
				md.mockCashLedgerHelper.EXPECT().GetAndMapInvestmentTransactionFromCashLedger(gomock.Any(), gomock.Any()).Return(
					getMockTransactionForCreditNotification("txn-1", "credit-2"), nil)
			},
			want: &p2pIPb.GetCreditTransactionUsingCashLedgerResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := getServiceWithMocks(t)
			tt.setupMocks(md)
			got, err := s.GetCreditTransactionUsingCashLedger(ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCreditTransactionUsingCashLedger() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCreditTransactionUsingCashLedger() got = %v, want %v", got, tt.want)
			}
		})
	}
}

//nolint:unparam
func getMockCashLedgerForGetCreditTransaction(amount *moneyPb.Money,
	creditedAt *timestamppb.Timestamp, ledgerType p2pVgPb.CashLedgerTransactionSubSubType) *p2pVgPb.GetCashLedgerResponse {
	return &p2pVgPb.GetCashLedgerResponse{
		Status: rpc.StatusOk(),
		LedgerData: []*p2pVgPb.LedgerData{
			getMockLedgerDataForGetCreditTransaction(money.FromPaisa(150000*100), getTimestampWithDeltaDays(100),
				p2pVgPb.TransactionType_TRANSACTION_TYPE_CREDIT, p2pVgPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_INVESTMENT),
			getMockLedgerDataForGetCreditTransaction(amount, creditedAt, p2pVgPb.TransactionType_TRANSACTION_TYPE_DEBIT, ledgerType),
			getMockLedgerDataForGetCreditTransaction(money.FromPaisa(150000*100), getTimestampWithDeltaDays(100),
				p2pVgPb.TransactionType_TRANSACTION_TYPE_DEBIT, p2pVgPb.CashLedgerTransactionSubSubType_CL_TXN_SS_TYPE_FULL_REPAYMENT),
		},
	}
}

func getMockLedgerDataForGetCreditTransaction(amount *moneyPb.Money, creditedAt *timestamppb.Timestamp,
	txnType p2pVgPb.TransactionType, ledgerType p2pVgPb.CashLedgerTransactionSubSubType) *p2pVgPb.LedgerData {
	return &p2pVgPb.LedgerData{
		TransactionType: txnType,
		TransactionDate: creditedAt,
		ClTxnSubSubType: ledgerType,
		Amount:          amount,
	}
}

func getTimestampWithDeltaDays(deltaDays int) *timestamppb.Timestamp {
	return timestamppb.New(time.Date(2023,
		9, 27+deltaDays, 0, 0, 0, 0, datetime.IST))
}

//nolint:unparam
func getMockTransactionForCreditNotification(txnId string, paymentRefId string) *p2pIPb.InvestmentTransaction {
	return &p2pIPb.InvestmentTransaction{
		Id:           txnId,
		PaymentRefId: paymentRefId,
		Vendor:       p2pIPb.Vendor_VENDOR_LIQUILOANS,
	}
}
