package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/p2pinvestment/dao"
	"github.com/epifi/gamma/p2pinvestment/dao/model"

	"github.com/google/wire"
	"gorm.io/gorm"
)

var (
	MaturityActionDaoWireSet = wire.NewSet(NewCrdbMaturityActionDao, wire.Bind(new(dao.MaturityActionDao), new(*CrdbMaturityActionDao)))
)

type CrdbMaturityActionDao struct {
	db    *gorm.DB
	idGen idgen.IdGenerator
}

var _ dao.MaturityActionDao = &CrdbMaturityActionDao{}

var MaturityActionColumnNamesMap = map[p2pPb.MaturityActionFieldMask]string{
	p2pPb.MaturityActionFieldMask_MATURITY_ACTION_FIELD_MASK_STATUS:     "status",
	p2pPb.MaturityActionFieldMask_MATURITY_ACTION_FIELD_MASK_SUB_STATUS: "sub_status",
}

func NewCrdbMaturityActionDao(db *gorm.DB, idGen idgen.IdGenerator) *CrdbMaturityActionDao {
	return &CrdbMaturityActionDao{
		db:    db,
		idGen: idGen,
	}
}

//nolint:dupl
func (c *CrdbMaturityActionDao) Create(ctx context.Context, maturityAction *p2pPb.MaturityAction) (*p2pPb.MaturityAction, error) {
	defer metric_util.TrackDuration("p2pinvestment/dao/impl", "CrdbMaturityActionDao", "Create", time.Now())
	id, err := c.idGen.Get(idgen.P2pMaturityAction)
	if err != nil {
		return nil, fmt.Errorf("p2p maturity action id generation failed: %w", err)
	}
	maturityActionModel := model.NewMaturityAction(maturityAction)
	maturityActionModel.Id = id
	handle := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := handle.Create(maturityActionModel)
	if res.Error != nil {
		if storage.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}
	return maturityActionModel.GetProto(), nil
}

//nolint:dupl
func (c *CrdbMaturityActionDao) Update(ctx context.Context, maturityAction *p2pPb.MaturityAction, updateMasks []p2pPb.MaturityActionFieldMask) error {
	defer metric_util.TrackDuration("p2pinvestment/dao/impl", "CrdbMaturityActionDao", "Update", time.Now())
	if maturityAction.GetId() == "" {
		return errors.New("primary identifier can't be empty for an update operation")
	}
	if len(updateMasks) == 0 {
		return errors.New("update mask can't be empty")
	}
	MaturityActionModel := model.NewMaturityAction(maturityAction)
	updateColumns := selectedColumnsForUpdate(updateMasks)
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	whereClause := &model.MaturityAction{
		Id: maturityAction.GetId(),
	}
	res := db.Model(MaturityActionModel).Where(whereClause).Select(updateColumns).Updates(MaturityActionModel)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

//nolint:dupl
func (c *CrdbMaturityActionDao) GetById(ctx context.Context, id string) (*p2pPb.MaturityAction, error) {
	defer metric_util.TrackDuration("p2pinvestment/dao/impl", "CrdbMaturityActionDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id can't be blank")
	}
	var MaturityActionModel model.MaturityAction
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if err := db.Where("id = ?", id).First(&MaturityActionModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return MaturityActionModel.GetProto(), nil
}

//nolint:dupl
func (c *CrdbMaturityActionDao) GetByInvestmentTransactionIdAndStatuses(ctx context.Context, investmentTransactionId string, statuses []p2pPb.MaturityActionStatus) ([]*p2pPb.MaturityAction, error) {
	defer metric_util.TrackDuration("p2pinvestment/dao/impl", "CrdbMaturityActionDao", "GetByInvestmentTransactionIdAndStatuses", time.Now())
	if investmentTransactionId == "" {
		return nil, errors.New("investmentTransactionId can't be blank")
	}
	var maturityActionModel []model.MaturityAction
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	query := db.Where("investment_transaction_id = ?", investmentTransactionId)
	if statuses != nil {
		query = query.Where("status in (?)", statuses)
	}
	if err := query.Find(&maturityActionModel).Error; err != nil {
		return nil, err
	}
	var maturityAction []*p2pPb.MaturityAction
	for _, maturityModel := range maturityActionModel {
		maturityAction = append(maturityAction, maturityModel.GetProto())
	}
	return maturityAction, nil
}

//nolint:dupl
func selectedColumnsForUpdate(updateMasks []p2pPb.MaturityActionFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, MaturityActionColumnNamesMap[field])
	}
	return selectColumns
}
