name: Actor
on:
  # Trigger the workflow manually only through UI
  # https://github.blog/changelog/2020-07-06-github-actions-manual-triggers-with-workflow_dispatch/
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  run_checks:
    uses: ./.github/workflows/base-workflow.yml
    with:
      runner_type: "go.crdb.postgres"
      services: "actor"
      start_cockroachdb: false
      start_postgres: true
      start_pinot: false
      start_redis: true
      use_crdb_schema_backup: true
      run_test_parallel: true
      lci_stamp_key: "actor"
      paths_to_trigger: |
        impacted_paths:
          - 'gamma/actor/**'
          - 'gamma/cmd/servers/test/actor/config/actor-test.yml'
          - 'gamma/cmd/servers/config/actor-params.yml'
          - 'gamma/db/actor/fixture.sql'
          - 'gamma/db/actor/latest.sql'
          - 'gamma/.github/workflows/actor.yml'
          - '!gamma/actor/config/values/actor-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    secrets: inherit
