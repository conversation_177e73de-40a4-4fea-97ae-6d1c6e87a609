name: "LCI Checker Mandatory"
on:
  # Trigger the workflow when the pull request is labeled
  # This is done to avoid workflows triggers on each commit in the PR.
  pull_request:
    types: [ labeled]
  pull_request_review:
    types: [ submitted ]

env:
  GOPATH: /runner/_work/gringott/gringott/go
  GOPROXY: "https://goproxy.pointz.in"
  REPO_NAME: "gringott"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  pr_status:
    if: ${{ github.base_ref == 'master' && (github.event.label.name == 'RunWorkflows' || github.event.review.state == 'approved') }}
    name: Check PR Status - mandatory
    runs-on: [ self-hosted, gringott-small ]
    permissions:
      contents: read
      pull-requests: write
    timeout-minutes: 10
    outputs:
      trigger_lci_run: ${{ steps.lci_run.outputs.trigger_lci_run == 'true' }}
      trigger_lci_validation: ${{ steps.prereq.outputs.trigger_lci_validation == 'true' }}
    steps:
      - name: Get Token
        id: get_workflow_token
        uses: peter-murray/workflow-application-token-action@dc0413987a085fa17d19df9e47d4677cf81ffef3
        with:
          application_id: ${{ secrets.FI_GITHUB_APP_ID }}
          application_private_key: ${{ secrets.FI_GITHUB_APP_PRIVATE_KEY }}

      - uses: epifi/github-actions@434512a7c43a4edd8866037c0d72132f52e3ba54
        id: first_approval
        with:
          approvalsCount: '1'
          onlyEqual: true
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}

      - name: Skip checks if PR is already approved
        if: ${{ github.event.review.state == 'approved' && steps.first_approval.outputs.isApproved != 'true' }}
        id: first_approval_status
        run: |
          echo "PR already approved. Exiting..."
          echo "should_skip_checks=true" >> $GITHUB_OUTPUT
          exit 0

      - name: Compute Fetch Depth
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        id: pr_info
        env:
          NUM_COMMITS: ${{ github.event.pull_request.commits }}
        run: |
          # github.event.pull_request.commits is empty when the workflow is triggered by pull request reviews.
          # In that case default to 100 for the fetch depth.
          if [[ $NUM_COMMITS != '' ]]; then
            echo "num_commits=$NUM_COMMITS" >> $GITHUB_OUTPUT
            echo "fetch_depth=$(($NUM_COMMITS+1))" >> $GITHUB_OUTPUT
          else
            echo "fetch_depth=100" >> $GITHUB_OUTPUT
          fi

      - name: Delete gringott contents
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        run: |
          # Delete gringott repo contents, if they already exist. This is because gringott-small runners are not ephemeral
          # and hence gringott directory may exist when the workflow is scheduled on a new pod
          rm -rf /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott

      - name: Check out gringott repo
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        uses: actions/checkout@24cb9080177205b6e8c946b17badbe402adc938f
        with:
          path: go/src/github.com/epifi/gringott
          fetch-depth: ${{ steps.pr_info.outputs.fetch_depth }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Get gocache path
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        id: gocache_path
        run: |
          set -x
          if [ -f latest.txt ]; then
            CACHE_DIR="$(pwd)/$(cat latest.txt)"
            BUILD_CACHE="${CACHE_DIR}/go-build"
            MOD_CACHE="${CACHE_DIR}/mod"
          else
            BUILD_CACHE="$(go env GOCACHE)"
            MOD_CACHE="$(go env GOMODCACHE)"
          fi

          echo "build_cache=${BUILD_CACHE}" >> $GITHUB_OUTPUT
          echo "mod_cache=${MOD_CACHE}" >> $GITHUB_OUTPUT

          # Ensure the cache directory exists and set the permissions
          sudo mkdir -p "${BUILD_CACHE}"
          sudo mkdir -p "${MOD_CACHE}"
          sudo chmod -R 777 "${BUILD_CACHE}"
          sudo chmod -R 777 "${MOD_CACHE}"
        working-directory: /home/<USER>/.cache/go-build-custom

      - name: Get Be-Common Version
        id: get_be_common_version
        run: |
          version=$(go list -m -f '{{.Version}}' 'github.com/epifi/be-common')
          echo "be_common_version=${version}"
          go get github.com/epifi/be-common@${version}
          set -x
          cat "${GOMODCACHE}/github.com/epifi/be-common@${version}/tools/conf_gen/VERSION" || echo "conf_gen version not found"
          LCI_VERSION=$(cat "${GOMODCACHE}/github.com/epifi/be-common@${version}/tools/lci/VERSION" | sed 's/^v//')
          CONF_GEN_VERSION=$(cat "${GOMODCACHE}/github.com/epifi/be-common@${version}/tools/conf_gen/VERSION" | sed 's/^v//')
          echo "CONF GEN VERSION: ${CONF_GEN_VERSION}"
          echo "LCI VERSION: ${LCI_VERSION}"
          echo "conf_gen_version=${CONF_GEN_VERSION}" >> $GITHUB_OUTPUT
          echo "lci_version=${LCI_VERSION}" >> $GITHUB_OUTPUT
        shell: bash
        env:
          GOCACHE: ${{ steps.gocache_path.outputs.build_cache }}
          GOMODCACHE: ${{ steps.gocache_path.outputs.mod_cache }}
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott

      - uses: epifi/github-actions@434512a7c43a4edd8866037c0d72132f52e3ba54
        name: "Check approval status"
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' && !(startsWith(github.event.pull_request.base.ref, 'epifi/m')) }}
        id: approved
        with:
          approvalsCount: '1'
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}

      - name: Pre requisite checks
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        id: prereq
        run: |
          if [ "$IS_RELEASE_BRANCH" == "true" ]; then
            echo "trigger_lci_validation=false" >> $GITHUB_OUTPUT
          elif [ "$APPROVED" == "false" ]; then
            echo "trigger_lci_validation=false" >> $GITHUB_OUTPUT
            # early fail if PR not approved
            echo "PR not approved, failing."
            exit 1
          else
            echo "trigger_lci_validation=true" >> $GITHUB_OUTPUT
          fi
        env:
          APPROVED: ${{ steps.approved.outputs.isApproved == 'true' }}
          IS_RELEASE_BRANCH: ${{ startsWith(github.event.pull_request.base.ref, 'epifi/m') }}

      - name: Download LCI Binary
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        id: aws-paths
        run: |
          aws s3 cp s3://epifi-deploy-github-actions-cache/be-common/lci/linux/amd64/${{ steps.get_be_common_version.outputs.lci_version }} ./lci.zip --region=ap-south-1
          unzip -o ./lci.zip
          sudo chmod +x ./lci/lci_bin
          sudo mv ./lci/lci_bin /usr/local/bin/lci
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_SECRET }}
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi

      - name: Fetch git notes
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        run: |
          git fetch origin refs/notes/commits:refs/notes/commits
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott

      - name: Check if tests should be triggered
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' }}
        id: lci_run
        shell: bash
        run: |
          set -x
          rm $(which conf_gen) || echo "conf_gen not found. This is a temporary fix to use latest conf_gen tool based on the be-common module version"
          echo_in_green() {
            echo -e "\x1b[32m${1}\x1b[0m"
          }
          echo_in_red() {
            echo -e "\x1b[31m${1}\x1b[0m"
          }

          if [ "$IS_RELEASE_BRANCH" == "true" ]; then
            echo "trigger_lci_run=false" >> $GITHUB_OUTPUT
          elif [ "$APPROVED" == "false" ]; then
            echo "trigger_lci_run=false" >> $GITHUB_OUTPUT
            # early fail if PR not approved
            echo_in_red "PR not approved, failing."
            sleep 3
            exit 1
          else
            lci_workflows=$(lci --pr=${PR_NUMBER} --dry-run --gh-action | grep -o 'Starting workflows: .*' | sed 's/Starting workflows: //')
            echo "Workflows remaining to be triggered: ${lci_workflows}"
            # This step fails when output of grep is empty, so handle for failure in or condition
            has_build_apps=$(echo "${lci_workflows}" | grep -o 'build_apps') || has_build_apps=""
            has_precommit=$(echo "${lci_workflows}" | grep -o 'precommit') || has_precommit=""
            # Find the count of pending workflows
            pending_workflows_count=$(echo "${lci_workflows}" | tr ', ' '\n' | wc -w)

            # Fail early if LCI has not been run for build_apps or precommit already
            if [[ "${has_build_apps}" != '' || "${has_precommit}" != '' ]]; then
              echo_in_red "FAIL: No LCI stamp found for build_apps and precommit. Run LCI for build_apps and precommit before re-running"
              echo "trigger_lci_run=false" >> $GITHUB_OUTPUT
              sleep 3
              exit 1
            elif [ "$pending_workflows_count" -gt 10 ]; then
              echo_in_green "Proceeding with running LCI checks in runner since more than 10 checks are remaining..."
              echo "trigger_lci_run=true" >> $GITHUB_OUTPUT
            elif [ "$lci_workflows" != '' ]; then
              echo "Found unstamped checks triggered due to common_paths changes. Proceeding with running LCI checks in runner"
              echo "trigger_lci_run=true" >> $GITHUB_OUTPUT
            fi
          fi
        env:
          GOCACHE: ${{ steps.gocache_path.outputs.build_cache }}
          GOMODCACHE: ${{ steps.gocache_path.outputs.mod_cache }}
          APPROVED: ${{ steps.approved.outputs.isApproved == 'true' }}
          IS_RELEASE_BRANCH: ${{ startsWith(github.event.pull_request.base.ref, 'epifi/m') }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          GH_TOKEN: ${{ steps.get_workflow_token.outputs.token }}
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott

      - name: Run LCI Validation
        # Run this step only if run_lci_checks step is not going to be run
        if: ${{ steps.first_approval_status.outputs.should_skip_checks != 'true' && steps.lci_run.outputs.trigger_lci_run != 'true' && steps.prereq.outputs.trigger_lci_validation == 'true' }}
        uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/run_lci_validation"
        env:
          GOCACHE: ${{ steps.gocache_path.outputs.build_cache }}
          GOMODCACHE: ${{ steps.gocache_path.outputs.mod_cache }}
        with:
          workflow_token: ${{ steps.get_workflow_token.outputs.token }}

      - name: "Remove RunWorkflow label"
        if: always()
        uses: actions-ecosystem/action-remove-labels@2ce5d41b4b6aa8503e285553f75ed56e0a40bae0
        with:
          labels: 'RunWorkflows'
          fail_on_error: false

      - uses: LouisBrunner/checks-action@6b626ffbad7cc56fd58627f774b9067e6118af23
        # Publish the job status event only if run_lci_checks step is not going to be run
        if: ${{ always() && steps.first_approval_status.outputs.should_skip_checks != 'true' && steps.lci_run.outputs.trigger_lci_run != 'true' && steps.prereq.outputs.trigger_lci_validation == 'true' }}
        with:
          token: ${{ steps.get_workflow_token.outputs.token }}
          name: Verify build status - LCI Mandatory Checker
          status: "completed"
          conclusion: ${{ job.status }}
          output: |
            {"summary":"Marked by https://github.com/epiFi/gringott/actions/runs/${{ github.run.id }}?pr=${{ github.event.pull_request.number }}"}

  run_lci_checks:
    needs: [ pr_status ]
    if: ${{ github.base_ref == 'master' && needs.pr_status.outputs.trigger_lci_run == 'true' }}
    name: Run service checks
    runs-on: [ self-hosted, gringott-small ]
    timeout-minutes: 150
    steps:
      - name: Get Token
        id: get_workflow_token
        uses: peter-murray/workflow-application-token-action@dc0413987a085fa17d19df9e47d4677cf81ffef3
        with:
          application_id: ${{ secrets.FI_GITHUB_APP_ID }}
          application_private_key: ${{ secrets.FI_GITHUB_APP_PRIVATE_KEY }}

      - name: Compute Fetch Depth
        id: pr_info
        env:
          NUM_COMMITS: ${{ github.event.pull_request.commits }}
        run: |
          # github.event.pull_request.commits is empty when the workflow is triggered by pull request reviews.
          # In that case default to 100 for the fetch depth.
          if [[ $NUM_COMMITS != '' ]]; then
            echo "num_commits=$NUM_COMMITS" >> $GITHUB_OUTPUT
            echo "fetch_depth=$(($NUM_COMMITS+1))" >> $GITHUB_OUTPUT
          else
            echo "fetch_depth=100" >> $GITHUB_OUTPUT
          fi

      - name: Check out gringott repo
        uses: actions/checkout@v3
        with:
          path: go/src/github.com/epifi/gringott
          fetch-depth: ${{ steps.pr_info.outputs.fetch_depth }}
          ref: ${{ github.event.pull_request.head.ref }}
      - name: Get gocache path
        id: gocache_path
        run: |
          echo "build_cache=$(pwd)/$(cat latest.txt)/go-build" >> $GITHUB_OUTPUT
          echo "mod_cache=$(pwd)/$(cat latest.txt)/mod" >> $GITHUB_OUTPUT
        working-directory: /home/<USER>/.cache/go-build-custom

      - name: Get Be-Common Version
        id: get_be_common_version
        run: |
          version=$(go list -m -f '{{.Version}}' 'github.com/epifi/be-common')
          echo "be_common_version=${version}"
          go get github.com/epifi/be-common@${version}
          LCI_VERSION=$(cat "${GOMODCACHE}/github.com/epifi/be-common@${version}/tools/lci/VERSION" | sed 's/^v//')
          CONF_GEN_VERSION=$(cat "${GOMODCACHE}/github.com/epifi/be-common@${version}/tools/conf_gen/VERSION" | sed 's/^v//')
          echo "CONF GEN VERSION: ${CONF_GEN_VERSION}"
          echo "LCI VERSION: ${LCI_VERSION}"
          echo "conf_gen_version=${CONF_GEN_VERSION}" >> $GITHUB_OUTPUT
          echo "lci_version=${LCI_VERSION}" >> $GITHUB_OUTPUT
        env:
          GOCACHE: ${{ steps.gocache_path.outputs.build_cache }}
          GOMODCACHE: ${{ steps.gocache_path.outputs.mod_cache }}
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott


      - name: Fetch git notes
        run: |
          git fetch origin refs/notes/commits:refs/notes/commits
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott

      - name: Download Platform Binaries
        id: aws-paths
        run: |
          rm $(which conf_gen) || echo "conf_gen not found"
          which conf_gen || echo "prebuilt conf_gen cleaned up successfully"
          # We don't download the binary into gringott repo, since it will interfere with LCI run
          aws s3 cp s3://epifi-deploy-github-actions-cache/be-common/lci/linux/amd64/${{ steps.get_be_common_version.outputs.lci_version }} ./lci.zip --region=ap-south-1
          unzip -o ./lci.zip
          sudo chmod +x ./lci/lci_bin
          sudo mv ./lci/lci_bin /usr/local/bin/lci
          aws s3 cp s3://epifi-deploy-github-actions-cache/be-common/conf_gen/linux/amd64/${{ steps.get_be_common_version.outputs.conf_gen_version }} ./conf_gen.zip --region=ap-south-1
          unzip -o ./conf_gen.zip
          sudo chmod +x ./conf_gen/conf_gen_bin
          sudo mv ./conf_gen/conf_gen_bin /usr/local/bin/conf_gen
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.DEPLOY_GITHUB_ACTIONS_ACCESS_KEY_SECRET }}
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi

      - name: Setup CRDB
        run: |
          ./scripts/github_action_helpers/start_cockroachdb.sh gringott
        shell: bash
        working-directory: /runner/_work/gringott/gringott/go/pkg/mod/github.com/epifi/be-common@${{ steps.be-common-version.outputs.be_common_version }}

      - name: Setup Postgres
        run: |
          ./scripts/github_action_helpers/start_postgres.sh
        shell: bash
        working-directory: /runner/_work/gringott/gringott/go/pkg/mod/github.com/epifi/be-common@${{ steps.be-common-version.outputs.be_common_version }}

      - name: Setup Redis
        run: |
          ./scripts/github_action_helpers/start_redis.sh
        shell: bash
        working-directory: /runner/_work/gringott/gringott/go/pkg/mod/github.com/epifi/be-common@${{ steps.be-common-version.outputs.be_common_version }}

      - name: Start Pinot
        run: |
          ./scripts/github_action_helpers/start_pinot.sh
        shell: bash
        working-directory: /runner/_work/gringott/gringott/go/pkg/mod/github.com/epifi/be-common@${{ steps.be-common-version.outputs.be_common_version }}

      - name: Setup Git Config
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
        shell: bash
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott

      - name: Run LCI
        id: run-lci-checks
        run: |
          lci --pr=${PR_NUMBER} -e=all --gh-action
        env:
          GOCACHE: ${{ steps.gocache_path.outputs.build_cache }}
          GOMODCACHE: ${{ steps.gocache_path.outputs.mod_cache }}
          GH_TOKEN: ${{ steps.get_workflow_token.outputs.token }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
        working-directory: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott

      - name: Upload LCI error logs
        if: ${{ failure() && steps.run-lci-checks.outcome == 'failure' }}
        continue-on-error: true
        uses: actions/upload-artifact@v4
        with:
          name: lci-output-${{ github.event.pull_request.number }}
          path: /runner/_work/gringott/gringott/go/src/github.com/epifi/gringott/output/lci

      - name: Run LCI Validation
        if: ${{ always() && !cancelled() }}
        uses: "./go/src/github.com/epifi/gringott/tools/actions/lci/run_lci_validation"
        env:
          GOCACHE: ${{ steps.gocache_path.outputs.build_cache }}
          GOMODCACHE: ${{ steps.gocache_path.outputs.mod_cache }}
        with:
          workflow_token: ${{ steps.get_workflow_token.outputs.token }}

      - uses: LouisBrunner/checks-action@6b626ffbad7cc56fd58627f774b9067e6118af23
        if: ${{ always() }}
        with:
          token: ${{ steps.get_workflow_token.outputs.token }}
          name: Verify build status - LCI Mandatory Checker
          status: "completed"
          conclusion: ${{ job.status }}
          output: |
            {"summary":"Marked by https://github.com/epiFi/gringott/actions/runs/${{ github.run.id }}?pr=${{ github.event.pull_request.number }}"}
