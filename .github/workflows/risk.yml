name: Risk

on:
  # Trigger the workflow manually only through UI
  # https://github.blog/changelog/2020-07-06-github-actions-manual-triggers-with-workflow_dispatch/
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  run_checks:
    uses: ./.github/workflows/base-workflow.yml
    with:
      runner_type: "go.crdb.postgres"
      services: "risk"
      start_cockroachdb: true
      start_postgres: true
      start_pinot: false
      start_redis: false
      use_crdb_schema_backup: true
      lci_stamp_key: "risk"
      paths_to_trigger: |
        impacted_paths:
          - 'gamma/risk/**'
          - 'gamma/cmd/worker/risk'
          - 'gamma/.github/workflows/risk.yml'
          - 'gamma/db/epifi/fixture.sql'
          - 'gamma/db/epifi/latest.sql'
          - 'gamma/db/frm/fixture.sql'
          - 'gamma/db/frm/latest.sql'
          - 'gamma/db/frm_pgdb/fixtures.sql'
          - 'gamma/db/frm_pgdb/latest.sql'
          - 'gamma/pkg/accessrevoke/**'
          - 'be-common/pkg/constants'
          - 'be-common/pkg/counter'
          - '!gamma/risk/config/values/risk-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    secrets: inherit
