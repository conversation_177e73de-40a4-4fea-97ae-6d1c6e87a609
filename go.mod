module epifi/protos

go 1.24.2

require google.golang.org/protobuf v1.31.0

require (
	buf.build/gen/go/bufbuild/bufplugin/protocolbuffers/go v1.31.0-20250121211742-6d880cc6cc8d.2 // indirect
	buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go v1.31.0-20250612022732-297b8109523d.2 // indirect
	buf.build/gen/go/pluginrpc/pluginrpc/protocolbuffers/go v1.31.0-20241007202033-cf42259fcbfc.2 // indirect
	buf.build/go/spdx v0.2.0 // indirect
	github.com/antlr4-go/antlr/v4 v4.13.0 // indirect
	github.com/bufbuild/protovalidate-go v0.4.3 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/google/cel-go v0.20.1 // indirect
	github.com/googleapis/googleapis v0.0.0-20230801173531-078f670152ce // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/stoewer/go-strcase v1.3.0 // indirect
	golang.org/x/exp v0.0.0-20240823005443-9b4947da3948 // indirect
	golang.org/x/net v0.10.0 // indirect
	golang.org/x/sys v0.25.0 // indirect
	golang.org/x/text v0.18.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20230913181813-007df8e322eb // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20230913181813-007df8e322eb // indirect
	google.golang.org/grpc v1.57.0 // indirect
)
