[Unit]
Description= Cockroach DB Node Service
Requires=network.target
[Service]
Type=simple
WorkingDirectory=/var/lib/cockroach
TimeoutStopSec=300
Restart=always
RestartSec=30
User=cockroachdb
LimitNOFILE=60000
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=cockroachdb
ExecStart=/usr/local/bin/cockroach start --certs-dir=/var/lib/cockroach/certs --store=/data --advertise-addr={{.ipAddress}}:26257 --http-addr={{.ipAddress}}:8080 --join={{.joinAddresses}} --locality=region={{.region}},zone={{.zone}} --cache=.30 --max-sql-memory=.30
[Install]
WantedBy=default.target