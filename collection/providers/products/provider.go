package products

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	collectionTypesPb "github.com/epifi/gamma/api/collection/types"
)

//go:generate mockgen -source=provider.go -destination=./mocks/provider.go -package=mocks

type IProductProvider interface {
	GetProduct() collectionTypesPb.Product
	CreateAllocationAtCollection(ctx context.Context, accountId string, productVendor commonvgpb.Vendor, collectionVendor commonvgpb.Vendor) error
}
