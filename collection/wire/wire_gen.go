// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/collection/activity"
	"github.com/epifi/gamma/collection/dao/impl"
	"github.com/epifi/gamma/collection/dao/utils"
	"github.com/epifi/gamma/collection/developer"
	"github.com/epifi/gamma/collection/developer/processor"
	"github.com/epifi/gamma/collection/providers"
	loan2 "github.com/epifi/gamma/collection/providers/processors/credgenics/loan"
	"github.com/epifi/gamma/collection/providers/products"
	"github.com/epifi/gamma/collection/providers/products/loan"
	"github.com/epifi/gamma/collection/providers/vendors"
	credgenics2 "github.com/epifi/gamma/collection/providers/vendors/credgenics"
	"github.com/epifi/gamma/collection/service"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	"github.com/google/wire"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeCollectionService(celestialClient celestial.CelestialClient, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB]) *service.Service {
	dbProvider := utils.NewDBProvider(dbConnProvider)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	leadDaoPgdb := impl.NewLeadDaoPgdb(dbProvider, domainIdGenerator)
	serviceService := service.NewService(celestialClient, leadDaoPgdb)
	return serviceService
}

func InitializeActivityProcessor(workerConf *worker.Config, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], vgCredgenicsClient credgenics.CredgenicsClient, palClient preapprovedloan.PreApprovedLoanClient, usersClient user.UsersClient, eventBroker events.Broker, creditReportManagerClient creditreportv2.CreditReportManagerClient, salClient salaryprogram.SalaryProgramClient, profileClient profile.ProfileClient, authClient auth.AuthClient, cxTicketClient ticket.TicketClient, empClient employment.EmploymentClient) *activity.Processor {
	dbProvider := utils.NewDBProvider(dbConnProvider)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	leadDaoPgdb := impl.NewLeadDaoPgdb(dbProvider, domainIdGenerator)
	allocationDaoPgdb := impl.NewAllocationDaoPgdb(dbProvider, domainIdGenerator)
	communicationDaoPgdb := impl.NewCommunicationDaoPgdb(dbProvider)
	credgenicsProvider := credgenics2.NewCredgenicsProvider(leadDaoPgdb, allocationDaoPgdb, communicationDaoPgdb, txnExecutorProvider, vgCredgenicsClient, palClient)
	defaultTime := datetime.NewDefaultTime()
	loanProvider := loan.NewLoanProvider(leadDaoPgdb, allocationDaoPgdb, palClient, defaultTime, eventBroker)
	collections := collectionsConfProvider(workerConf)
	provider := loan2.NewProvider(credgenicsProvider, loanProvider, leadDaoPgdb, allocationDaoPgdb, communicationDaoPgdb, txnExecutorProvider, vgCredgenicsClient, palClient, usersClient, defaultTime, eventBroker, creditReportManagerClient, salClient, profileClient, empClient, authClient, cxTicketClient, collections)
	factoryImpl := newFactoryWithProviders(provider)
	processor := activity.NewProcessor(factoryImpl, allocationDaoPgdb)
	return processor
}

func InitializeDeveloperService(dbConnProvider *storagev2.DBResourceProvider[*gorm.DB]) *developer.Service {
	dbProvider := utils.NewDBProvider(dbConnProvider)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	leadDaoPgdb := impl.NewLeadDaoPgdb(dbProvider, domainIdGenerator)
	leadEntity := processor.NewLeadEntity(leadDaoPgdb)
	allocationDaoPgdb := impl.NewAllocationDaoPgdb(dbProvider, domainIdGenerator)
	allocationEntity := processor.NewAllocationEntity(allocationDaoPgdb)
	factory := developer.NewFactory(leadEntity, allocationEntity)
	developerService := developer.NewService(factory, leadDaoPgdb, allocationDaoPgdb)
	return developerService
}

// wire.go:

func newFactoryWithProviders(
	loanCredgenicsProcessor *loan2.Provider,
) *providers.FactoryImpl {
	return providers.NewFactory([]providers.IProviderWithShouldHandle{
		loanCredgenicsProcessor,
	})
}

var providersSet = wire.NewSet(wire.Bind(new(providers.Factory), new(*providers.FactoryImpl)), newFactoryWithProviders, loan2.NewProvider, wire.Bind(new(products.IProductProvider), new(*loan.LoanProvider)), wire.Bind(new(vendors.ICollectionVendorProvider), new(*credgenics2.CredgenicsProvider)), loan.NewLoanProvider, credgenics2.NewCredgenicsProvider)

func collectionsConfProvider(workerConf *worker.Config) *worker.Collections {
	return workerConf.Collections
}
