package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	authPb "github.com/epifi/gamma/api/investment/auth"
	pb "github.com/epifi/gamma/api/investment/auth"
	"github.com/epifi/gamma/investment/auth/dao/model"
)

type AuthAttemptCrdb struct {
	db    *gormv2.DB
	idGen idgen.IdGenerator
}

var (
	unspecifiedFieldFilter = func(field authPb.AuthAttemptFieldMask) bool {
		return authPb.AuthAttemptFieldMask_AuthAttemptFieldMask_UNSPECIFIED != field
	}
	fieldMaskToColumn = map[authPb.AuthAttemptFieldMask]string{
		authPb.AuthAttemptFieldMask_AuthAttemptFieldMask_AuthStatus: "auth_status",
		authPb.AuthAttemptFieldMask_AuthAttemptFieldMask_AuthInfo:   "auth_info",
	}
)

func NewAuthAttemptCrdb(db *gormv2.DB, idGen idgen.IdGenerator) *AuthAttemptCrdb {
	return &AuthAttemptCrdb{
		db:    db,
		idGen: idGen,
	}
}

//nolint:dupl
func (d *AuthAttemptCrdb) Create(ctx context.Context, authAttempt *authPb.AuthAttempt) (*authPb.AuthAttempt, error) {
	defer metric_util.TrackDuration("investment/auth/dao", "AuthAttemptCrdb", "Create", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	id, err := d.idGen.Get(idgen.InvestmentAuth)
	if err != nil {
		return nil, fmt.Errorf("model id generation failed: %w", err)
	}
	authModel := model.NewAuthAttemptModel(authAttempt)
	authModel.ID = id
	if err = db.Create(authModel).Error; err != nil {
		if storagev2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, err
	}
	return authModel.ToProto(), nil
}

//nolint:dupl
func (d *AuthAttemptCrdb) GetByAuthId(ctx context.Context, id string) (*authPb.AuthAttempt, error) {
	defer metric_util.TrackDuration("investment/auth/dao", "AuthAttemptCrdb", "GetByAuthId", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	authModel := &model.AuthAttempt{}
	if res := db.Where("id = ?", id).First(authModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to fetch authAttempt by id: %s: %w", id, res.Error)
	}
	return authModel.ToProto(), nil
}

func (d *AuthAttemptCrdb) GetByAuthIds(ctx context.Context, ids []string) ([]*authPb.AuthAttempt, error) {
	defer metric_util.TrackDuration("investment/auth/dao", "AuthAttemptCrdb", "GetByAuthIds", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var authModels []*model.AuthAttempt
	if err := db.Where("id IN (?)", ids).
		Find(&authModels).Error; err != nil {
		logger.Error(ctx, "Error when fetching list of funds", zap.Error(err))
		return nil, err
	}

	var authPbs []*pb.AuthAttempt
	for _, auth := range authModels {
		authPbs = append(authPbs, auth.ToProto())
	}
	return authPbs, nil
}

func (d *AuthAttemptCrdb) UpdateAuthById(ctx context.Context, id string, authAttempt *authPb.AuthAttempt, updateMasks []authPb.AuthAttemptFieldMask) error {
	defer metric_util.TrackDuration("investment/auth/dao", "AuthAttemptCrdb", "UpdateAuthById", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	updateMasks = filterFieldMaskSlice(updateMasks, unspecifiedFieldFilter)
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	updatedColumns := getSelectColumnsForUpdate(updateMasks)

	authModel := model.NewAuthAttemptModel(authAttempt)
	resp := db.Model(&model.AuthAttempt{ID: id}).Select(updatedColumns).Updates(authModel)
	if resp.Error != nil {
		return fmt.Errorf("unable to update mutual fund model for id %s, %w", authAttempt.Id, resp.Error)
	}
	if resp.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

// filterFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterFieldMaskSlice(fieldMasks []authPb.AuthAttemptFieldMask, check func(field pb.AuthAttemptFieldMask) bool) []pb.AuthAttemptFieldMask {
	var ret []pb.AuthAttemptFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// getSelectColumnsForUpdate converts update mask to string slice with column name
// corresponding to field name enums
func getSelectColumnsForUpdate(updateMask []pb.AuthAttemptFieldMask) []string {
	var selectColumns []string

	for _, field := range updateMask {
		selectColumns = append(selectColumns, fieldMaskToColumn[field])
	}
	return selectColumns
}
