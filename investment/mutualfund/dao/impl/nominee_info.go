package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/dao/model"
)

var (
	folioNomineeStatusUpdateFieldMaskToColumn = map[catalog.NomineeInfoFieldMask]string{
		catalog.NomineeInfoFieldMask_NomineeInfoFieldMask_UPDATE_STATUS:  "update_status",
		catalog.NomineeInfoFieldMask_NomineeInfoFieldMask_FAILURE_REASON: "failure_reason",
	}
	maskUnspecifiedFieldFilterForFolioNomineeStatusUpdate = func(field catalog.NomineeInfoFieldMask) bool {
		return catalog.NomineeInfoFieldMask_NomineeInfoFieldMask_UNSPECIFIED != field
	}
)

type NomineeInfoCrdb struct {
	db    *gormv2.DB
	idGen idgen.IdGenerator
}

func NewFolioNomineeUpdateStatusCrdb(db *gormv2.DB, idGen idgen.IdGenerator) *NomineeInfoCrdb {
	return &NomineeInfoCrdb{db: db, idGen: idGen}
}

var NomineeStatusWireSet = wire.NewSet(NewFolioNomineeUpdateStatusCrdb, wire.Bind(new(dao.FolioNomineeUpdateStatusDao), new(*NomineeInfoCrdb)))

//nolint:dupl
func (d *NomineeInfoCrdb) Create(ctx context.Context, obj *catalog.NomineeInfo) (*catalog.NomineeInfo, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "NomineeInfoCrdb", "Create", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection object in FolioNomineeUpdateStatusCrdb
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	id, err := d.idGen.Get(idgen.MFNomineeStatus)
	if err != nil {
		return nil, fmt.Errorf("model id generation failed: %w", err)
	}

	nomineeStatusModel, err := model.NewFolioNomineeUpdateStatusModel(obj)
	if err != nil {
		return nil, err
	}
	nomineeStatusModel.Id = id
	if err = db.Create(nomineeStatusModel).Error; err != nil {
		if storagev2.IsDuplicateRowError(err) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, err
	}

	return nomineeStatusModel.ToProto(), nil
}

func (d *NomineeInfoCrdb) Update(ctx context.Context, obj *catalog.NomineeInfo, updateMasks []catalog.NomineeInfoFieldMask) error {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "NomineeInfoCrdb", "Update", time.Now())
	if obj.Id == "" {
		return fmt.Errorf("id cannot be empty for the update operation")
	}
	// If ctx is carrying the gorm transaction connection object, use it.
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	updateMasks = filterFolioNomineeUpdateStatusFieldMaskSlice(updateMasks, maskUnspecifiedFieldFilterForFolioNomineeStatusUpdate)
	if len(updateMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}
	updatedColumns := getSelectColumnsFolioNomineeUpdateStatus(updateMasks)
	nomineeModel, err := model.NewFolioNomineeUpdateStatusModel(obj)
	if err != nil {
		return fmt.Errorf("error in converting to model, %w", err)
	}
	resp := db.Model(nomineeModel).Select(updatedColumns).Updates(nomineeModel)
	if resp.Error != nil {
		return fmt.Errorf("unable to update order model for id %s, %w", obj.GetId(), resp.Error)
	}
	if resp.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}
	return nil
}

//nolint:dupl
func (d *NomineeInfoCrdb) GetByActorId(ctx context.Context, actorId string) ([]*catalog.NomineeInfo, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "NomineeInfoCrdb", "GetByActorId", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	var nomineeMdls []*model.NomineeInfo

	if err := db.Where("actor_id = ? and nominee_id = ? and folio_id = ?", actorId).
		Find(&nomineeMdls).Error; err != nil {
		logger.Error(ctx, "Error nominee update status", zap.Error(err))
		return nil, err
	}
	objArr := make([]*catalog.NomineeInfo, 0)
	for _, nomineeMdl := range nomineeMdls {
		objArr = append(objArr, nomineeMdl.ToProto())
	}
	return objArr, nil
}

//nolint:dupl
func (d *NomineeInfoCrdb) GetByActorNomineeAndFolioId(ctx context.Context, actorId string, nomineeId string, folioId string) (*catalog.NomineeInfo, error) {
	defer metric_util.TrackDuration("investment/mutualfund/dao/impl", "NomineeInfoCrdb", "GetByActorNomineeAndFolioId", time.Now())
	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection object in FolioNomineeUpdateStatusCrdb
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	nomineeModel := &model.NomineeInfo{}
	if res := db.Where("actor_id = ? and nominee_id = ? and folio_id = ?", actorId, nomineeId, folioId).First(nomineeModel); res.Error != nil {
		if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
			fmt.Println("not found err")
			return nil, epifierrors.ErrRecordNotFound
		}
		fmt.Println("random error")
		return nil, fmt.Errorf("error in GetByActorNomineeAndFolioId: %s:%s:%s: %w", actorId, nomineeId, folioId, res.Error)
	}
	return nomineeModel.ToProto(), nil

}

// filterFolioNomineeUpdateStatusFieldMaskSlice filters out elements from a slice based on the check function passed in arg
// elements are filtered out if they dont pass the check function
// NOTE- func returns a new copy of the slice. No changes are made to the existing slice
func filterFolioNomineeUpdateStatusFieldMaskSlice(fieldMasks []catalog.NomineeInfoFieldMask,
	check func(field catalog.NomineeInfoFieldMask) bool) []catalog.NomineeInfoFieldMask {
	var ret []catalog.NomineeInfoFieldMask
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			ret = append(ret, fieldMask)
		}
	}
	return ret
}

// getSelectColumnsForOrderUpdate converts update mask to string slice with column name
// corresponding to field name enums
func getSelectColumnsFolioNomineeUpdateStatus(updateMask []catalog.NomineeInfoFieldMask) []string {
	var selectColumns []string

	for _, field := range updateMask {
		selectColumns = append(selectColumns, folioNomineeStatusUpdateFieldMaskToColumn[field])
	}
	return selectColumns
}
