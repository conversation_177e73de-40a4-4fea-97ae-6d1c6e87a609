package impl

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	test "github.com/epifi/be-common/pkg/test/v2"
)

type orderTestSuite struct {
	db   *gormv2.DB
	conf *genConf.Config
	dao  dao.OrderDao
}

func newOrderTestSuite(db *gormv2.DB, conf *genConf.Config, dao dao.OrderDao) *orderTestSuite {
	return &orderTestSuite{
		db:   db,
		conf: conf,
		dao:  dao,
	}
}

const (
	// picked from the fixture
	mfId1          = "MF211116PruFuSSJTO6zZmIs+LFEjg=="
	clientOrderId1 = "1214567890"
	clientOrderId2 = "1234567891"
)

var (
	orderTS                *orderTestSuite
	tablesAffectedInOrders = []string{"mutual_funds", "mf_orders"}
	sampleOrder            = &pb.Order{
		ActorId:      "actor1",
		MutualFundId: mfId1,
		ReinvType:    0,
		Units:        10,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        200,
			Nanos:        0,
		},
		OrderType:       pb.OrderType_BUY,
		FolioId:         "",
		Nav:             nil,
		OrderStatus:     pb.OrderStatus_CREATED,
		Client:          pb.OrderClient_FIT,
		ClientOrderId:   clientOrderId1,
		Rta:             commonvgpb.Vendor_CAMS,
		PaymentMode:     pb.PaymentMode_SI,
		PayFailReason:   pb.PaymentFailureReason_PAYMENT_FAILURE_REASON_UNSPECIFIED,
		FailureReason:   pb.FailureReason_FAILURE_REASON_UNSPECIFIED,
		VendorOrderId:   "500243312",
		ExternalOrderId: "1292921-321123-311",
	}
)

func TestOrderCrdb_Create(t *testing.T) {
	invalidOrder := &pb.Order{}
	_ = copier.Copy(invalidOrder, sampleOrder)
	invalidOrder.MutualFundId = "abc"

	newOrder := &pb.Order{}
	_ = copier.Copy(newOrder, sampleOrder)
	// set a new client order ID
	newOrder.ClientOrderId = "987654322"
	newOrder.VendorOrderId = ""

	type args struct {
		ctx   context.Context
		order *pb.Order
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
	}{
		{
			name: "fk violate",
			args: args{
				ctx:   context.Background(),
				order: invalidOrder,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "duplicate order", // based on client order id
			args: args{
				ctx:   context.Background(),
				order: sampleOrder,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "insert success",
			args: args{
				ctx:   context.Background(),
				order: newOrder,
			},
			want:    newOrder,
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			got, err := orderTS.dao.Create(tt.args.ctx, tt.args.order)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.VendorOrderId = got.GetVendorOrderId()
				tt.want.ExternalOrderId = got.GetExternalOrderId()
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestOrderCrdb_Create_IDOverRideDisable(t *testing.T) {
	newOrder := &pb.Order{}
	_ = copier.Copy(newOrder, sampleOrder)
	// set a new client order ID
	newOrder.ClientOrderId = "687654322"
	newOrder.VendorOrderId = "1234_ABCD"

	type args struct {
		ctx   context.Context
		order *pb.Order
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
	}{
		{
			name: "don't override vendor order id if an order already contains it",
			args: args{
				ctx:   context.Background(),
				order: newOrder,
			},
			want:    newOrder,
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			got, err := orderTS.dao.Create(tt.args.ctx, tt.args.order)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.ExternalOrderId = got.GetExternalOrderId()
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v", got, tt.want)
			}
		})
	}

}

func TestOrderCrdb_Create_Duplicates(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.ClientOrderId = uuid.New().String()
	sampleOrder2.ExternalOrderId = ""
	sampleOrder2.VendorOrderId = ""

	sampleOrder3 := &pb.Order{}
	_ = copier.Copy(sampleOrder3, sampleOrder)
	sampleOrder3.ClientOrderId = uuid.New().String()
	sampleOrder3.ExternalOrderId = ""
	sampleOrder3.VendorOrderId = ""

	sampleOrder4 := &pb.Order{}
	_ = copier.Copy(sampleOrder4, sampleOrder)
	sampleOrder4.ClientOrderId = uuid.New().String()
	sampleOrder3.ExternalOrderId = ""
	// existing vendor order id inserted via fixtures
	sampleOrder4.VendorOrderId = "500243312"

	type args struct {
		ctx                         context.Context
		order                       *pb.Order
		randomNumberGenerator       func(length int) string
		randomAlphaNumericGenerator func(length int) string
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
		err     error
	}{
		{
			name: "trying to generate an already existing external order id and getting failed always",
			args: args{
				ctx:   context.Background(),
				order: sampleOrder2,
				randomAlphaNumericGenerator: func(length int) string {
					// This external order id is already inserted via fixture
					return "1292921-321123-311"
				},
				randomNumberGenerator: idgen.RandSeqDigitsWithoutLeadingZeroes,
			},
			want:    nil,
			wantErr: true,
			err:     CollisionError,
		},
		{
			name: "trying to generate an already existing vendor order id and getting failed always",
			args: args{
				ctx:   context.Background(),
				order: sampleOrder3,
				randomNumberGenerator: func(length int) string {
					// This vendor order id is already inserted via fixture
					return "500243312"
				},
				randomAlphaNumericGenerator: idgen.RandAlphaNumericString,
			},
			want:    nil,
			wantErr: true,
			err:     CollisionError,
		},
		{
			name: "trying to insert an already existing vendor order id and getting failure",
			args: args{
				ctx:                         context.Background(),
				order:                       sampleOrder4,
				randomNumberGenerator:       idgen.RandSeqDigitsWithoutLeadingZeroes,
				randomAlphaNumericGenerator: idgen.RandAlphaNumericString,
			},
			want:    nil,
			wantErr: true,
			err:     epifierrors.ErrDuplicateEntry,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			randomNumberGenerator = tt.args.randomNumberGenerator
			randomAlphaNumericGenerator = tt.args.randomAlphaNumericGenerator
			got, err := orderTS.dao.Create(tt.args.ctx, tt.args.order)
			if err != nil {
				if !tt.wantErr {
					t.Errorf("TestOrderCrdb_Create_Duplicates() error = %v, wantErr %v", err, tt.wantErr)
				}
				if errors.Is(err, tt.err) {
					return
				}
				t.Errorf("TestOrderCrdb_Create_Duplicates() error = %v, wantErr %v", err, tt.err)
			}
			if tt.wantErr {
				t.Errorf("TestOrderCrdb_Create_Duplicates() error = %v, wantErr %v", err, tt.wantErr)
			}
			if got != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.VendorOrderId = got.GetVendorOrderId()
				tt.want.ExternalOrderId = got.GetExternalOrderId()
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v", got, tt.want)
			}
		})
	}

	// Resetting generators to default before exiting test
	randomAlphaNumericGenerator = idgen.RandAlphaNumericString
	randomNumberGenerator = idgen.RandSeqDigitsWithoutLeadingZeroes
}

func TestOrderCrdb_GetById(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	// set the correct mutual fund id to satisfy the FK constraint
	sampleOrder2.MutualFundId = mfId1
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}
	sampleOrder2.OrderConfirmationMetaData = &pb.OrderConfirmationMetadata{}

	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
	}{
		{
			name: "get successful", // using id='MFO211117J+5wdKl0RRawgLlVY4gDGw==' from fixture
			args: args{
				ctx: context.Background(),
				id:  "MFO211117J+5wdKl0RRawgLlVY4gDGw==",
			},
			want:    sampleOrder2,
			wantErr: false,
		},
		{
			name: "invalid id",
			args: args{
				ctx: context.Background(),
				id:  "invalid-id",
			},
			want:    nil,
			wantErr: true,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestOrderCrdb_GetOrdersByFilters(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	// set the correct mutual fund id to satisfy the FK constraint
	sampleOrder2.MutualFundId = mfId1
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	type args struct {
		ctx       context.Context
		filters   []storagev2.FilterOption
		pageSize  uint32
		pageToken *pagination.PageToken
	}
	tests := []struct {
		name          string
		args          args
		wantErr       bool
		wantLen       int
		wantHasBefore bool
		wantHasAfter  bool
	}{
		{
			name: "get when page size is less than total elements",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithActorId("paginationTestActor"),
					dao.WithMutualFundId("MF211116PruFuSSJTO6zZmIs+LFEjg=="),
				},
				pageSize:  2,
				pageToken: nil,
			},
			wantErr:       false,
			wantLen:       2,
			wantHasBefore: false,
			wantHasAfter:  true,
		},
		{
			name: "get when page size is greater than total elements",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithActorId("paginationTestActor"),
					dao.WithMutualFundId("MF211116PruFuSSJTO6zZmIs+LFEjg=="),
				},
				pageSize:  4,
				pageToken: nil,
			},
			wantErr:       false,
			wantLen:       3,
			wantHasBefore: false,
			wantHasAfter:  false,
		},
		{
			name: "get when page size is equal to total elements",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithActorId("paginationTestActor"),
					dao.WithMutualFundId("MF211116PruFuSSJTO6zZmIs+LFEjg=="),
				},
				pageSize:  3,
				pageToken: nil,
			},
			wantErr:       false,
			wantLen:       3,
			wantHasAfter:  false,
			wantHasBefore: false,
		},
		{
			name: "get with page token with only has before",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithActorId("paginationTestActor"),
					dao.WithMutualFundId("MF211116PruFuSSJTO6zZmIs+LFEjg=="),
				},
				pageSize:  2,
				pageToken: &pagination.PageToken{Offset: 2},
			},
			wantErr:       false,
			wantLen:       1,
			wantHasAfter:  false,
			wantHasBefore: true,
		},
		{
			name: "get with page token having both has and before after",
			args: args{
				ctx: context.Background(),
				filters: []storagev2.FilterOption{
					dao.WithActorId("paginationTestActor"),
					dao.WithMutualFundId("MF211116PruFuSSJTO6zZmIs+LFEjg=="),
				},
				pageSize:  1,
				pageToken: &pagination.PageToken{Offset: 1},
			},
			wantErr:       false,
			wantLen:       1,
			wantHasAfter:  true,
			wantHasBefore: true,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, pgctx, err := orderTS.dao.GetOrdersByFilters(tt.args.ctx, tt.args.pageToken, tt.args.pageSize, tt.args.filters...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrdersByFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(got) != tt.wantLen {
				t.Errorf("GetOrdersByFilters() got = %v,\n want %v", len(got), tt.wantLen)
			}

			if pgctx.HasAfter != tt.wantHasAfter {
				t.Errorf("GetOrdersByFilters() gotHasAfter = %v,\n want %v", pgctx.HasAfter, tt.wantHasAfter)
			}

			if pgctx.HasBefore != tt.wantHasBefore {
				t.Errorf("GetOrdersByFilters() gotHasBefore = %v,\n want %v", pgctx.HasBefore, tt.wantHasBefore)
			}

		})
	}
}

func TestOrderCrdb_GetOrderCountByMutualFundAndActorId(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	// set the correct mutual fund id to satisfy the FK constraint
	sampleOrder2.MutualFundId = mfId1
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	type args struct {
		ctx          context.Context
		actorId      string
		mutualFundId string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		wantLen int32
	}{
		{
			name: "get count",
			args: args{
				ctx:          context.Background(),
				actorId:      "paginationTestActor",
				mutualFundId: "MF211116PruFuSSJTO6zZmIs+LFEjg==",
			},
			wantErr: false,
			wantLen: 3,
		},
		{
			name: "get with page token having both has and before after",
			args: args{
				ctx:          context.Background(),
				actorId:      "invalid actor",
				mutualFundId: "invalid fund",
			},
			wantErr: false,
			wantLen: 0,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetOrderCountByMutualFundAndActorId(tt.args.ctx, tt.args.actorId, tt.args.mutualFundId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrdersByMutualFundIdAndActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got != tt.wantLen {
				t.Errorf("GetOrdersByMutualFundIdAndActorId() gotLen = %v, wantLen %v", got, tt.wantLen)
				return
			}

		})
	}
}

func TestOrderCrdb_Update(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.VendorOrderId = ""
	type args struct {
		ctx           context.Context
		updateMasks   []pb.OrderFieldMask
		prepareUpdate func(d dao.OrderDao) (*pb.Order, error)
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
	}{
		{
			name: "empty id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					// set a new client order ID
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					order.Id = ""
					return order, nil
				},
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "invalid order id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					// set a new client order ID
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					order.Id = "invalid"
					return order, nil
				},
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "update folio-id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_FOLIO_ID},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					// set the folio id
					order.FolioId = "12345"
					return order, nil
				},
			},
			wantErr: false,
			want:    sampleOrder2,
		},
		{
			name: "update multiple",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_FOLIO_ID, pb.OrderFieldMask_NAV, pb.OrderFieldMask_ORDER_STATUS},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					return order, nil
				},
			},
			wantErr: false,
			want:    sampleOrder2,
		},
		{
			name: "update rta confirmed amount and units",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_RTA_CONFIRMED_UNITS, pb.OrderFieldMask_RTA_CONFIRMED_AMOUNT},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					order.RtaConfirmedAmount = sampleOrder2.Amount
					order.RtaConfirmedUnits = sampleOrder2.Units

					return order, nil
				},
			},
			wantErr: false,
			want:    sampleOrder2,
		},
		{
			name: "update rta confirmed units to zero",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_RTA_CONFIRMED_UNITS, pb.OrderFieldMask_RTA_CONFIRMED_AMOUNT},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					order.RtaConfirmedUnits = 0.0
					return order, nil
				},
			},
			wantErr: false,
			want:    sampleOrder2,
		},
	}
	test.PrepareScopedDatabase(t, orderTS.conf.EpifiDb().GetName(), orderTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clock := idgen.NewClock()
			orderCrdb := NewOrderCrdb(db, idgen.NewDomainIdGenerator(clock))
			order, err := tt.args.prepareUpdate(orderCrdb)
			if err != nil {
				t.Error("unexpected error in create DB entry", err)
				return
			}

			// this is a hack to set the correct 'want' param.
			if tt.want != nil {
				tt.want = order
			}

			got, err := orderCrdb.Update(tt.args.ctx, order, tt.args.updateMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Update() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestOrderCrdb_GetByOrderIds(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	sampleOrder3 := &pb.Order{}
	_ = copier.Copy(sampleOrder3, sampleOrder)
	sampleOrder3.ClientOrderId = clientOrderId2
	sampleOrder3.PaymentInfo = &pb.PaymentInfo{}
	sampleOrder3.ActorId = "summaryActor"
	sampleOrder3.ClientOrderId = "1234567810"
	sampleOrder3.ExternalOrderId = "1222921-321223-311"
	sampleOrder3.OrderStatus = pb.OrderStatus_CONFIRMED_BY_RTA
	sampleOrder3.VendorOrderId = "5433312"
	sampleOrder3.FolioId = "12322/12"

	type args struct {
		ctx      context.Context
		orderIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.Order
		wantErr bool
	}{
		{
			name: "empty order id list",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{},
			},
			want:    []*pb.Order{},
			wantErr: false,
		},
		{
			name: "non-empty order id list",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{"MFO211117J+5wdKl0RRawgLlVY4gDGw==", "MFO7617J+5wdKl0RRawgLlYTreqa=="},
			},
			want:    []*pb.Order{sampleOrder2, sampleOrder3},
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetByOrderIds(tt.args.ctx, tt.args.orderIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByOrderIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, gotOrder := range got {
				if gotOrder != nil {
					tt.want[i].Id = gotOrder.GetId()
					tt.want[i].CreatedAt = gotOrder.GetCreatedAt()
					tt.want[i].UpdatedAt = gotOrder.GetUpdatedAt()
					tt.want[i].OrderConfirmationMetaData = &pb.OrderConfirmationMetadata{}
				}
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
				}

				if diff := cmp.Diff(gotOrder, tt.want[i], opts...); diff != "" {
					t.Errorf("Create() got = %v,\nwant %v,\ndiff %v", gotOrder, tt.want[i], diff)
				}
			}
		})
	}
}

func TestOrderCrdb_UpdateBatch(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.VendorOrderId = ""
	sampleOrder3 := &pb.Order{}
	_ = copier.Copy(sampleOrder3, sampleOrder)
	sampleOrder3.VendorOrderId = ""
	// set the correct mutual fund id to satisfy the FK constraint
	sampleOrder2.MutualFundId = mfId1

	type args struct {
		ctx           context.Context
		updateMasks   map[pb.OrderFieldMask]interface{}
		prepareUpdate func(d dao.OrderDao) ([]string, *pb.Order, error)
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
	}{
		{
			name: "No ids",
			args: args{
				ctx: context.Background(),
				updateMasks: map[pb.OrderFieldMask]interface{}{
					pb.OrderFieldMask_ORDER_STATUS: pb.OrderStatus_INITIATED,
				},
				prepareUpdate: func(d dao.OrderDao) ([]string, *pb.Order, error) {
					// set a new client order if
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, nil, err
					}
					order.Id = ""
					return []string{}, order, nil
				},
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "update folio-id",
			args: args{
				ctx:         context.Background(),
				updateMasks: map[pb.OrderFieldMask]interface{}{pb.OrderFieldMask_FOLIO_ID: "12345"},
				prepareUpdate: func(d dao.OrderDao) ([]string, *pb.Order, error) {
					// set a new client order if
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, nil, err
					}
					// set the folio id
					order.FolioId = "12345"
					return []string{order.Id}, order, nil
				},
			},
			wantErr: false,
			want:    sampleOrder2,
		},
		{
			name: "update multiple",
			args: args{
				ctx: context.Background(),
				updateMasks: map[pb.OrderFieldMask]interface{}{
					pb.OrderFieldMask_FOLIO_ID:     "12345",
					pb.OrderFieldMask_ORDER_STATUS: pb.OrderStatus_INITIATED},
				prepareUpdate: func(d dao.OrderDao) ([]string, *pb.Order, error) {
					// set a new client order if
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, nil, err
					}
					sampleOrder3.ClientOrderId = uuid.New().String()
					sampleOrder3.VendorOrderId = uuid.New().String()
					order2, err := d.Create(context.Background(), sampleOrder3)
					if err != nil {
						return nil, nil, err
					}
					// set the folio id
					order.FolioId = "12345"
					order.OrderStatus = pb.OrderStatus_INITIATED
					return []string{order.GetId(), order2.GetId()}, order, nil
				},
			},
			wantErr: false,
			want:    sampleOrder2,
		},
	}
	test.PrepareScopedDatabase(t, orderTS.conf.EpifiDb().GetName(), orderTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clock := idgen.NewClock()
			orderCrdb := NewOrderCrdb(db, idgen.NewDomainIdGenerator(clock))
			orderIds, order, err := tt.args.prepareUpdate(orderCrdb)
			if err != nil {
				t.Error("unexpected error in create DB entry", err)
				return
			}

			// this is a hack to set the correct 'want' param.
			if tt.want != nil {
				tt.want = order
			}

			err = orderCrdb.UpdateBatch(tt.args.ctx, orderIds, tt.args.updateMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			dbOrders, err := orderCrdb.GetByOrderIds(tt.args.ctx, orderIds)
			if err != nil {
				t.Error("error in Get after Batch update", err)
				return
			}
			for _, got := range dbOrders {
				if got != nil {
					tt.want.Id = got.GetId()
					tt.want.CreatedAt = got.GetCreatedAt()
					tt.want.UpdatedAt = got.GetUpdatedAt()
					tt.want.ClientOrderId = got.GetClientOrderId()
					tt.want.ExternalOrderId = got.GetExternalOrderId()
					tt.want.VendorOrderId = got.GetVendorOrderId()
				}
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt", "ClientOrderId"),
				}

				if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
					t.Errorf("Update() got = %v,\n want %v,\n diff %v", got, tt.want, diff)
				}
			}
		})
	}
}

func TestOrderCrdb_GetByClientOrderId(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	// set the correct mutual fund clientOrderId to satisfy the FK constraint
	sampleOrder2.MutualFundId = mfId1
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	type args struct {
		ctx           context.Context
		clientOrderId string
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
	}{
		{
			name: "get successful", // using clientOrderId='1234567890' from fixture
			args: args{
				ctx:           context.Background(),
				clientOrderId: clientOrderId1,
			},
			want:    sampleOrder2,
			wantErr: false,
		},
		{
			name: "invalid clientOrderId",
			args: args{
				ctx:           context.Background(),
				clientOrderId: "invalid-clientOrderId",
			},
			want:    nil,
			wantErr: true,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetByClientOrderId(tt.args.ctx, tt.args.clientOrderId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientOrderId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				tt.want.Id = got.GetId()
				tt.want.CreatedAt = got.GetCreatedAt()
				tt.want.UpdatedAt = got.GetUpdatedAt()
				tt.want.OrderConfirmationMetaData = &pb.OrderConfirmationMetadata{}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestOrderCrdb_UpdateWithStatusCheck(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	// set the correct mutual fund id to satisfy the FK constraint
	sampleOrder2.MutualFundId = mfId1
	sampleOrder2.VendorOrderId = ""

	type args struct {
		ctx           context.Context
		updateMasks   []pb.OrderFieldMask
		prepareUpdate func(d dao.OrderDao) (*pb.Order, error)
		currentStatus pb.OrderStatus
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.Order
		wantErr bool
	}{
		{
			name: "empty id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					// set a new client order if
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					order.Id = ""
					return order, nil
				},
				currentStatus: sampleOrder2.OrderStatus,
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "invalid order id",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					// set a new client order if
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					order.Id = "invalid"
					return order, nil
				},
				currentStatus: sampleOrder2.OrderStatus,
			},
			wantErr: true,
			want:    nil,
		},
		{
			name: "existing orderId with correct status",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					// set a new client order if
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					return order, nil
				},
				currentStatus: sampleOrder2.OrderStatus,
			},
			wantErr: false,
			want:    sampleOrder2,
		},
		{
			name: "existing orderId with incorrect status",
			args: args{
				ctx:         context.Background(),
				updateMasks: []pb.OrderFieldMask{pb.OrderFieldMask_FOLIO_ID, pb.OrderFieldMask_NAV, pb.OrderFieldMask_ORDER_STATUS},
				prepareUpdate: func(d dao.OrderDao) (*pb.Order, error) {
					// set a new client order if
					sampleOrder2.ClientOrderId = uuid.New().String()
					sampleOrder2.VendorOrderId = uuid.New().String()
					order, err := d.Create(context.Background(), sampleOrder2)
					if err != nil {
						return nil, err
					}
					return order, nil
				},
				currentStatus: pb.OrderStatus_ORDER_STATUS_UNSPECIFIED,
			},
			wantErr: true,
			want:    nil,
		},
	}
	test.PrepareScopedDatabase(t, orderTS.conf.EpifiDb().GetName(), orderTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clock := idgen.NewClock()
			orderCrdb := NewOrderCrdb(db, idgen.NewDomainIdGenerator(clock))
			order, err := tt.args.prepareUpdate(orderCrdb)
			if err != nil {
				t.Error("unexpected error in create DB entry", err)
				return
			}

			// this is a hack to set the correct 'want' param.
			if tt.want != nil {
				tt.want = order
			}

			err = orderCrdb.UpdateWithStatusCheck(tt.args.ctx, order, tt.args.currentStatus, tt.args.updateMasks)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestOrderCrdb_GetOrderIdsByUpdatedTillAndFilters(t *testing.T) {

	// This variable needs to be updated to the total number of orders present in the db
	totalOrdersInDB := 4

	type args struct {
		ctx         context.Context
		updatedTill *timestamppb.Timestamp
		limit       int
		filters     map[pb.OrderFieldFilter]interface{}
	}
	tests := []struct {
		name    string
		args    args
		wantLen int
		wantErr bool
	}{
		{
			name: "limit is equal to the db count", // using clientOrderId='1234567890' from fixture
			args: args{
				ctx:         context.Background(),
				updatedTill: timestamppb.Now(),
				limit:       totalOrdersInDB,
				filters:     make(map[pb.OrderFieldFilter]interface{}),
			},
			wantLen: totalOrdersInDB,
			wantErr: false,
		},
		{
			name: "limit is greater than db count", // using clientOrderId='1234567890' from fixture
			args: args{
				ctx:         context.Background(),
				updatedTill: timestamppb.Now(),
				limit:       totalOrdersInDB + 1,
				filters:     make(map[pb.OrderFieldFilter]interface{}),
			},
			wantLen: totalOrdersInDB + 1,
			wantErr: false,
		},
		{
			name: "limit is less than db count", // using clientOrderId='1234567890' from fixture
			args: args{
				ctx:         context.Background(),
				updatedTill: timestamppb.Now(),
				limit:       2,
				filters:     make(map[pb.OrderFieldFilter]interface{}),
			},
			wantLen: 2,
			wantErr: false,
		},
		{
			name: "limit is less than db count with filters", // using clientOrderId='1234567890' from fixture
			args: args{
				ctx:         context.Background(),
				updatedTill: timestamppb.Now(),
				limit:       2,
				filters: map[pb.OrderFieldFilter]interface{}{
					pb.OrderFieldFilter_ORDER_FIELD_FILTER_RTA: commonvgpb.Vendor_CAMS,
				},
			},
			wantLen: 2,
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetOrderIdsByUpdatedTillAndFilters(tt.args.ctx, tt.args.updatedTill, tt.args.filters, tt.args.limit)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrderIdsByUpdatedTillAndFilters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(got) != tt.wantLen {
				t.Errorf("GetOrderIdsByUpdatedTillAndFilters() got = %v,\n wantLen %v", got, tt.wantLen)

			}
		})
	}
}

func TestOrderCrdb_GetNonTerminalsOrdersByActorId(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "Some terminal some non terminal orders",
			args: args{
				ctx:     context.Background(),
				actorId: "summaryActor",
			},
			want:    []string{"MFO999999J+5wdKl0RRawgLlVY4gDGw=="},
			wantErr: false,
		},
		{
			name: "No orders",
			args: args{
				ctx:     context.Background(),
				actorId: "random",
			},
			want:    []string{},
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetNonTerminalOrdersByFilterOptions(tt.args.ctx, dao.WithActorId(tt.args.actorId))
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNonTerminalsOrderIdsByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if len(tt.want) != len(got) {
				t.Errorf("length mismatch for result and expected output, len(got) = %v, len(want) =%v", len(got), len(tt.want))
				return
			}
			for i, gotId := range got {
				if !strings.EqualFold(gotId.Id, tt.want[i]) {
					t.Errorf("Got different orderId than wanted, got = %s, want =%s", gotId, tt.want[i])
				}
			}
		})
	}
}

func TestOrderCrdb_GetByClientOrderIds(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	sampleOrder3 := &pb.Order{}
	_ = copier.Copy(sampleOrder3, sampleOrder)
	sampleOrder3.ClientOrderId = clientOrderId2
	sampleOrder3.PaymentInfo = &pb.PaymentInfo{}

	type args struct {
		ctx      context.Context
		orderIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.Order
		wantErr bool
	}{
		{
			name: "empty client order id list",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{},
			},
			want:    []*pb.Order{},
			wantErr: true,
		},
		{
			name: "get success",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{clientOrderId1, clientOrderId2},
			},
			want:    []*pb.Order{sampleOrder2, sampleOrder3},
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetByClientOrderIds(tt.args.ctx, tt.args.orderIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByOrderIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, gotOrder := range got {
				if gotOrder != nil {
					tt.want[i].Id = gotOrder.GetId()
					tt.want[i].VendorOrderId = gotOrder.GetVendorOrderId()
					gotOrder.OrderConfirmationMetaData = tt.want[i].GetOrderConfirmationMetaData()
					gotOrder.ExternalOrderId = tt.want[i].ExternalOrderId
					tt.want[i].CreatedAt = gotOrder.GetCreatedAt()
					tt.want[i].UpdatedAt = gotOrder.GetUpdatedAt()
				}
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
				}

				if diff := cmp.Diff(gotOrder, tt.want[i], opts...); diff != "" {
					t.Errorf("GetByOrderIds() got = %v,\n want %v diff %v", gotOrder, tt.want[i], diff)
				}
			}
		})
	}
}

func TestOrderCrdb_GetByExternalOrderIds(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	sampleOrder3 := &pb.Order{}
	_ = copier.Copy(sampleOrder3, sampleOrder)
	sampleOrder3.ClientOrderId = clientOrderId2
	sampleOrder3.PaymentInfo = &pb.PaymentInfo{}

	type args struct {
		ctx      context.Context
		orderIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.Order
		wantErr bool
	}{
		{
			name: "empty external order id list",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{},
			},
			want:    []*pb.Order{},
			wantErr: true,
		},
		{
			name: "get success",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{"1292921-321123-311", "1292921-321123-311"},
			},
			want:    []*pb.Order{sampleOrder2, sampleOrder3},
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetByExternalOrderIds(tt.args.ctx, tt.args.orderIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByOrderIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, gotOrder := range got {
				if gotOrder != nil {
					tt.want[i].Id = gotOrder.GetId()
					tt.want[i].VendorOrderId = gotOrder.GetVendorOrderId()
					gotOrder.OrderConfirmationMetaData = tt.want[i].GetOrderConfirmationMetaData()
					gotOrder.ExternalOrderId = tt.want[i].ExternalOrderId
					tt.want[i].CreatedAt = gotOrder.GetCreatedAt()
					tt.want[i].UpdatedAt = gotOrder.GetUpdatedAt()
				}
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
				}

				if diff := cmp.Diff(gotOrder, tt.want[i], opts...); diff != "" {
					t.Errorf("GetByOrderIds() got = %v,\n want %v diff %v", gotOrder, tt.want[i], diff)
				}
			}
		})
	}
}

func TestOrderCrdb_GetByVendorOrderIds(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	sampleOrder3 := &pb.Order{}
	_ = copier.Copy(sampleOrder3, sampleOrder)
	sampleOrder3.ClientOrderId = clientOrderId2
	sampleOrder3.PaymentInfo = &pb.PaymentInfo{}

	type args struct {
		ctx      context.Context
		orderIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.Order
		wantErr bool
	}{
		{
			name: "empty vendor order id list",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{},
			},
			want:    []*pb.Order{},
			wantErr: true,
		},
		{
			name: "get success",
			args: args{
				ctx:      context.Background(),
				orderIds: []string{"500243312", "54311312"},
			},
			want:    []*pb.Order{sampleOrder2, sampleOrder3},
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetByVendorOrderIds(tt.args.ctx, tt.args.orderIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByOrderIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, gotOrder := range got {
				if gotOrder != nil {
					tt.want[i].Id = gotOrder.GetId()
					tt.want[i].VendorOrderId = gotOrder.GetVendorOrderId()
					gotOrder.OrderConfirmationMetaData = tt.want[i].GetOrderConfirmationMetaData()
					gotOrder.ExternalOrderId = tt.want[i].ExternalOrderId
					tt.want[i].CreatedAt = gotOrder.GetCreatedAt()
					tt.want[i].UpdatedAt = gotOrder.GetUpdatedAt()
				}
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
				}

				if diff := cmp.Diff(gotOrder, tt.want[i], opts...); diff != "" {
					t.Errorf("GetByOrderIds() got = %v,\n want %v diff %v", gotOrder, tt.want[i], diff)
				}
			}
		})
	}
}

func TestOrderCrdb_GetInvestedMfIdsByActorId(t *testing.T) {
	sampleOrder2 := &pb.Order{}
	_ = copier.Copy(sampleOrder2, sampleOrder)
	sampleOrder2.PaymentInfo = &pb.PaymentInfo{}

	sampleOrder3 := &pb.Order{}
	_ = copier.Copy(sampleOrder3, sampleOrder)
	sampleOrder3.ClientOrderId = clientOrderId2
	sampleOrder3.PaymentInfo = &pb.PaymentInfo{}

	type args struct {
		ctx     context.Context
		actorId string
		mfIds   []string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "Get empty MF ID list if invalid actor ID",
			args: args{
				ctx:     context.Background(),
				actorId: "",
				mfIds:   nil,
			},
			want:    []string{},
			wantErr: false,
		},
		{
			name: "Get success, unique MF ID list if valid actor ID",
			args: args{
				ctx:     context.Background(),
				actorId: sampleOrder.ActorId,
				mfIds:   []string{mfId1, "someInvalidMfId"},
			},
			want:    []string{sampleOrder.MutualFundId},
			wantErr: false,
		},
	}
	test.PrepareScopedDatabase(t, mfTS.conf.EpifiDb().GetName(), mfTS.db, tablesAffectedInOrders)
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := orderTS.dao.GetInvestedMfIdsByActorIdAndMfIds(tt.args.ctx, tt.args.actorId, tt.args.mfIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInvestedMfIdsByActorIdAndMfIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i, gotMfId := range got {
				opts := []cmp.Option{
					protocmp.Transform(),
					cmpopts.IgnoreFields(pb.Order{}, "CreatedAt", "UpdatedAt"),
				}

				if diff := cmp.Diff(gotMfId, tt.want[i], opts...); diff != "" {
					t.Errorf("GetInvestedMfIdsByActorIdAndMfIds() got = %v,\n want %v diff %v", gotMfId, tt.want[i], diff)
				}
			}
		})
	}
}
