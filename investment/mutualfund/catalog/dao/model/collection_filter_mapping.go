package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	catPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
)

type CollectionFilterMapping struct {
	ID           string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	CollectionId string
	FilterId     string

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormv2.DeletedAt
}

func (m *CollectionFilterMapping) ToProto() *catPb.CollectionFilterMapping {
	collFilterMapProto := &catPb.CollectionFilterMapping{
		Id:           m.ID,
		CollectionId: m.CollectionId,
		FilterId:     m.FilterId,
		CreatedAt:    timestampPb.New(m.CreatedAt),
		UpdatedAt:    timestampPb.New(m.UpdatedAt),
	}
	if m.DeletedAt.Valid {
		collFilterMapProto.DeletedAt = timestampPb.New(m.DeletedAt.Time)
	}
	return collFilterMapProto
}

func NewCollectionFilterMapping(collFilterMapProto *catPb.CollectionFilterMapping) *CollectionFilterMapping {
	collFilterMapModel := &CollectionFilterMapping{
		ID:           collFilterMapProto.GetId(),
		CollectionId: collFilterMapProto.GetCollectionId(),
		FilterId:     collFilterMapProto.GetFilterId(),
	}
	if collFilterMapProto.GetCreatedAt() != nil {
		collFilterMapModel.CreatedAt = collFilterMapProto.GetCreatedAt().AsTime()
	}
	if collFilterMapProto.GetUpdatedAt() != nil {
		collFilterMapModel.UpdatedAt = collFilterMapProto.GetUpdatedAt().AsTime()
	}
	if collFilterMapProto.GetDeletedAt() != nil {
		collFilterMapModel.DeletedAt = gormv2.DeletedAt{Time: collFilterMapProto.GetDeletedAt().AsTime(), Valid: true}
	}
	return collFilterMapModel
}

func (CollectionFilterMapping) TableName() string {
	return "mf_collection_filter_mappings"
}
