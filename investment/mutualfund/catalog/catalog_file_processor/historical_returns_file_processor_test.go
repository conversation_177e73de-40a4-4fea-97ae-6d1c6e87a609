package catalog_file_processor

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/copier"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	mocks2 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/logger"

	mfpb "github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/investment/mutualfund/dao/mocks"
)

func TestHistoricalReturnsFileProcessor_ProcessCatalogFile(t *testing.T) {
	// init logger
	logger.Init("test")
	ctx := context.Background()
	ctr := gomock.NewController(t)
	const testPath = "tmp/path"
	successUpdateFile := "MStarID,FundName,Date,Value,\nF00000PCRK,ICICI Pru Nifty Next 50 Index Dir Gr,2022-6,-6.24245,\nF00000PCRK,ICICI Pru Nifty Next 50 Index Dir Gr,2022-5,-8.32968,"
	successPartialUpdateFile := "MStarID,FundName,Date,Value,\nF00000PCRK,ICICI Pru Nifty Next 50 Index Dir Gr,2022-6,-6.24245,"
	missingMstarIdFile := "MStarID,FundName,Date,Value,\n,ICICI Pru Nifty Next 50 Index Dir Gr,2022-6,-6.24245,"
	noSuccessUpdate := "MStarID,FundName,Date,Value,\nF00000PCRK,ICICI Pru Nifty Next 50 Index Dir Gr,2022-5,-8.32968,"
	type fields struct {
		mfDao    *mocks.MockMutualFundDao
		s3Client *mocks2.MockS3Client
	}
	type args struct {
		ctx      context.Context
		filePath string
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		setupMocks func(f *fields)
		wantErr    bool
	}{
		{
			name: "successful full load",
			fields: fields{
				s3Client: mocks2.NewMockS3Client(ctr),
				mfDao:    mocks.NewMockMutualFundDao(ctr),
			},
			args: args{
				ctx:      ctx,
				filePath: testPath,
			},
			setupMocks: func(f *fields) {
				fund := &mfpb.MutualFund{IsinNumber: "INF109K01Y80", HistoricalReturns: &mfpb.HistoricalReturns{}}
				f.s3Client.EXPECT().Read(gomock.Any(), testPath).Return([]byte(successUpdateFile), nil)
				f.mfDao.EXPECT().GetByMStarId(gomock.Any(), "F00000PCRK").
					Return(fund, nil)
				time1, _ := time.Parse("2006-1", "2022-6")
				time2, _ := time.Parse("2006-1", "2022-5")
				updatedFund := &mfpb.MutualFund{}
				err := copier.Copy(updatedFund, fund)
				if err != nil {
					return
				}
				updatedFund.HistoricalReturns = &mfpb.HistoricalReturns{
					MonthlyReturns: []*mfpb.ReturnData{
						{
							ReturnDate: timestampPb.New(time2),
							ReturnData: -8.32968,
						},
						{
							ReturnDate: timestampPb.New(time1),
							ReturnData: -6.24245,
						},
					},
				}
				f.mfDao.EXPECT().UpdateByMStarId(gomock.Any(), "F00000PCRK", updatedFund, []mfpb.MutualFundFieldMask{
					mfpb.MutualFundFieldMask_HISTORICAL_RETURNS,
				}).Return(nil, nil)
			},
			wantErr: false,
		},
		{
			name: "successful partial update load",
			fields: fields{
				s3Client: mocks2.NewMockS3Client(ctr),
				mfDao:    mocks.NewMockMutualFundDao(ctr),
			},
			args: args{
				ctx:      ctx,
				filePath: testPath,
			},
			setupMocks: func(f *fields) {
				fund := &mfpb.MutualFund{IsinNumber: "INF109K01Y80", HistoricalReturns: &mfpb.HistoricalReturns{}}
				f.s3Client.EXPECT().Read(gomock.Any(), testPath).Return([]byte(successPartialUpdateFile), nil)
				f.mfDao.EXPECT().GetByMStarId(gomock.Any(), "F00000PCRK").
					Return(fund, nil)
				time1, _ := time.Parse("2006-1", "2022-6")
				time2, _ := time.Parse("2006-1", "2022-5")
				updatedFund := &mfpb.MutualFund{}
				err := copier.Copy(updatedFund, fund)
				if err != nil {
					return
				}
				fund.HistoricalReturns = &mfpb.HistoricalReturns{
					MonthlyReturns: []*mfpb.ReturnData{
						{
							ReturnDate: timestampPb.New(time2),
							ReturnData: -8.32968,
						},
					},
				}
				updatedFund.HistoricalReturns = &mfpb.HistoricalReturns{
					MonthlyReturns: []*mfpb.ReturnData{
						{
							ReturnDate: timestampPb.New(time2),
							ReturnData: -8.32968,
						},
						{
							ReturnDate: timestampPb.New(time1),
							ReturnData: -6.24245,
						},
					},
				}
				f.mfDao.EXPECT().UpdateByMStarId(gomock.Any(), "F00000PCRK", updatedFund, []mfpb.MutualFundFieldMask{
					mfpb.MutualFundFieldMask_HISTORICAL_RETURNS,
				}).Return(nil, nil)
			},
			wantErr: false,
		},
		{
			name: "successful no update",
			fields: fields{
				s3Client: mocks2.NewMockS3Client(ctr),
				mfDao:    mocks.NewMockMutualFundDao(ctr),
			},
			args: args{
				ctx:      ctx,
				filePath: testPath,
			},
			setupMocks: func(f *fields) {
				fund := &mfpb.MutualFund{IsinNumber: "INF109K01Y80", HistoricalReturns: &mfpb.HistoricalReturns{}}
				f.s3Client.EXPECT().Read(gomock.Any(), testPath).Return([]byte(successUpdateFile), nil)
				time1, _ := time.Parse("2006-1", "2022-6")
				time2, _ := time.Parse("2006-1", "2022-5")
				fund.HistoricalReturns = &mfpb.HistoricalReturns{
					MonthlyReturns: []*mfpb.ReturnData{
						{
							ReturnDate: timestampPb.New(time2),
							ReturnData: -8.32968,
						},
						{
							ReturnDate: timestampPb.New(time1),
							ReturnData: -6.24245,
						},
					},
				}
				f.mfDao.EXPECT().GetByMStarId(gomock.Any(), "F00000PCRK").
					Return(fund, nil)
			},
			wantErr: false,
		},
		{
			name: "no updates",
			fields: fields{
				s3Client: mocks2.NewMockS3Client(ctr),
				mfDao:    mocks.NewMockMutualFundDao(ctr),
			},
			args: args{
				ctx:      ctx,
				filePath: testPath,
			},
			setupMocks: func(f *fields) {
				fund := &mfpb.MutualFund{IsinNumber: "INF109K01Y80", HistoricalReturns: &mfpb.HistoricalReturns{}}
				f.s3Client.EXPECT().Read(gomock.Any(), testPath).Return([]byte(noSuccessUpdate), nil)
				time1, _ := time.Parse("2006-1", "2022-6")
				time2, _ := time.Parse("2006-1", "2022-5")
				fund.HistoricalReturns = &mfpb.HistoricalReturns{
					MonthlyReturns: []*mfpb.ReturnData{
						{
							ReturnDate: timestampPb.New(time2),
							ReturnData: -8.32968,
						},
						{
							ReturnDate: timestampPb.New(time1),
							ReturnData: -6.24245,
						},
					},
				}
				f.mfDao.EXPECT().GetByMStarId(gomock.Any(), "F00000PCRK").
					Return(fund, nil)
			},
			wantErr: false,
		},
		{
			name: "record with missing mstar id should not be updated",
			fields: fields{
				s3Client: mocks2.NewMockS3Client(ctr),
				mfDao:    mocks.NewMockMutualFundDao(ctr),
			},
			args: args{
				ctx:      ctx,
				filePath: testPath,
			},
			setupMocks: func(f *fields) {
				fund := &mfpb.MutualFund{IsinNumber: "INF109K01Y80", HistoricalReturns: &mfpb.HistoricalReturns{}}
				f.s3Client.EXPECT().Read(gomock.Any(), testPath).Return([]byte(missingMstarIdFile), nil)

				time1, _ := time.Parse("2006-1", "2022-6")
				time2, _ := time.Parse("2006-1", "2022-5")
				updatedFund := &mfpb.MutualFund{}
				err := copier.Copy(updatedFund, fund)
				if err != nil {
					return
				}
				fund.HistoricalReturns = &mfpb.HistoricalReturns{
					MonthlyReturns: []*mfpb.ReturnData{
						{
							ReturnDate: timestampPb.New(time2),
							ReturnData: -8.32968,
						},
					},
				}
				updatedFund.HistoricalReturns = &mfpb.HistoricalReturns{
					MonthlyReturns: []*mfpb.ReturnData{
						{
							ReturnDate: timestampPb.New(time2),
							ReturnData: -8.32968,
						},
						{
							ReturnDate: timestampPb.New(time1),
							ReturnData: -6.24245,
						},
					},
				}
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			d := &HistoricalReturnsFileProcessor{
				s3Client: tt.fields.s3Client,
				mfDao:    tt.fields.mfDao,
			}
			tt.setupMocks(&tt.fields)
			if err := d.ProcessCatalogFile(tt.args.ctx, tt.args.filePath); (err != nil) != tt.wantErr {
				t.Errorf("ProcessCatalogFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
