package dynamic_elements_getter

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"

	"context"

	"github.com/google/uuid"
	"go.uber.org/zap"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fitttScreenOption "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/fittt"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/fittt"
	"github.com/epifi/be-common/pkg/logger"
)

type HomeTopBarAlertDynamicElementsGetter struct {
	conf              *genconf.Config
	fitttSIAggregator *fittt.StandingInstructionFitttAggregator
}

func NewHomeTopBarAlertDynamicElementsGetter(conf *genconf.Config,
	fitttSIAggregator *fittt.StandingInstructionFitttAggregator) *HomeTopBarAlertDynamicElementsGetter {
	return &HomeTopBarAlertDynamicElementsGetter{
		conf:              conf,
		fitttSIAggregator: fitttSIAggregator,
	}
}

func (s *HomeTopBarAlertDynamicElementsGetter) GetDynamicElements(ctx context.Context, actorId string, appPlatform commontypes.Platform, appVersion int32) ([]*dePb.DynamicElement, error) {
	_, _, amcActorIdList, aggErr := s.fitttSIAggregator.GetAllStandingInstructionsWithActiveSipsDueForExpiry(ctx, actorId)
	if aggErr != nil {
		return nil, aggErr
	}
	if len(amcActorIdList) > 0 {
		dl := &deeplink.Deeplink{Screen: deeplink.Screen_GET_INVESTMENT_SIPS_MODIFICATION_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&fitttScreenOption.GetUpdateInvestmentSipsWithConsentScreenOptions{
				UseCase: fitttScreenOption.SipUpdateUseCase_SIP_UPDATE_USE_CASE_RENEW_EXPIRING_SIPS.String(),
			})}
		titleTxt := "Your SIPs might fail due to expiring instructions. Please check and renew"
		//  Show app upgrade screen if app version is older
		if (appPlatform == commontypes.Platform_ANDROID && appVersion < int32(s.conf.Flags().SIPRenewSupportConfig().MinAndroidVersionSIPRenewSupport())) ||
			(appPlatform == commontypes.Platform_IOS && appVersion < int32(s.conf.Flags().SIPRenewSupportConfig().MinIOSVersionSIPRenewSupport())) {
			dl = &deeplink.Deeplink{
				Screen:        deeplink.Screen_UPDATE_APP_SCREEN,
				ScreenOptions: &deeplink.Deeplink_UpdateAppScreenOptions_{UpdateAppScreenOptions: &deeplink.Deeplink_UpdateAppScreenOptions{UpdateType: deeplink.Deeplink_UpdateAppScreenOptions_UPDATE_TYPE_FLEXIBLE}}}
			titleTxt = "Your SIPs might fail due to expiring instructions. Please update app to check and renew"
		}
		logger.Info(ctx, "showing alert sip banner at home", zap.String(logger.ACTOR_ID_V2, actorId))
		dynEleArr := []*dePb.DynamicElement{
			{
				OwnerService:  types.ServiceName_INVESTMENT_SERVICE,
				Id:            uuid.New().String(),
				UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
				StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
				Content: &dePb.ElementContent{
					Content: &dePb.ElementContent_BannerV2{
						BannerV2: &dePb.BannerElementContentV2{
							Title: &commontypes.Text{
								FontColor:    "#333333",
								DisplayValue: &commontypes.Text_PlainString{PlainString: titleTxt},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
							},
							BackgroundColor: &ui.BackgroundColour{
								Colour: &ui.BackgroundColour_BlockColour{
									BlockColour: "#D3B250",
								},
							},
							Deeplink: dl,
							Image: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/investments/landing/alert.png",
								Width:    20,
								Height:   20,
							},
						},
					},
				},
			},
		}
		return dynEleArr, nil
	}
	return nil, nil
}
