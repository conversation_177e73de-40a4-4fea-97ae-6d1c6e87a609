package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	updOrder "github.com/epifi/gamma/investment/mutualfund/order/update_order"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	filegen "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	filegenMocks "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator/mocks"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order/orchestrator"
	prereqMocks "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler/mocks"
	omsPb "github.com/epifi/gamma/api/order"
	omsMock "github.com/epifi/gamma/api/order/mocks"
	daoMocks "github.com/epifi/gamma/investment/mutualfund/dao/mocks"
	mock_sip "github.com/epifi/gamma/investment/mutualfund/order/sip_ledger_util/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

//goland:noinspection GoSnakeCaseUsage
const (
	BATCH_ID   = "testBatch1"
	ORDER_ID_1 = "order1"
	ORDER_ID_2 = "order2"
	ACTOR_1    = "ac1"
	ACTOR_2    = "ac2"

	FILE_PATH = "s3://epifi/testFile.txt"
)

// Sample daos for mock responses
var (
	txnExecutor storagev2.IdempotentTxnExecutor
	sampleBatch = &orderPb.BatchOrderProcessingDetails{
		Id:          BATCH_ID,
		Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED,
		OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
		CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
		Metadata:    nil,
		CreatedAt:   nil,
		UpdatedAt:   nil,
		DeletedAt:   nil,
	}
	sampOrder = &orderPb.Order{
		Id:           ORDER_ID_1,
		ActorId:      ACTOR_1,
		MutualFundId: "mf1",
		Units:        10,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        100,
			Nanos:        0.0,
		},
		OrderType:   orderPb.OrderType_BUY,
		OrderStatus: orderPb.OrderStatus_PRE_PROCESSING_COMPLETE,
		Amc:         mfPb.Amc_ICICI_PRUDENTIAL,
		OmsOrders:   nil,
	}
	sampleStepDetail = &orderPb.BatchOrderProcessingStepDetails{
		Id:      "step1",
		BatchId: BATCH_ID,
		Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
		Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
			OrderStatusData: &orderPb.OrderStatusStepData{
				SuccessOrderIds: []string{},
				FailedOrderIds:  []string{},
				PendingOrderIds: []string{},
			},
		}},
	}
	amcInfo = &mfPb.AmcInfo{
		Id:         "amc1",
		Amc:        mfPb.Amc_ICICI_PRUDENTIAL,
		AmcCode:    "amcCode",
		AmcName:    "ICICI",
		Rta:        commonvgpb.Vendor_CAMS,
		AmcActorId: "amcActorId",
	}
	sampleCreateOrderResp = &omsPb.CreateOrderResponse{
		Status: rpc.StatusOk(),
		Order: &omsPb.Order{
			Id: "omsOrder",
		},
		IsPinRequired: false,
	}

	// file gen
	sampleFileGenResp = &filegen.GenerateOrderFeedFileResponse{
		Status:                      rpc.StatusOk(),
		FileGenerationAttemptStatus: filegen.FileGenAttemptStatus_GENERATION_SUCCESS,
		SuccessfulOrderIds:          []string{ORDER_ID_1, ORDER_ID_2},
		FailedOrderInfo:             nil,
		FilePath:                    FILE_PATH,
	}
)

type MockDependencies struct {
	mockOrderDao       *daoMocks.MockOrderDao
	mockOrderStatusDao *daoMocks.MockOrderStatusUpdateDao
	mockAmcDao         *daoMocks.MockAmcInfoDao
	mockFolioDao       *daoMocks.MockFolioLedgerDao
	mockBatchProcDao   *daoMocks.MockBatchOrderProcessingDetailsDao
	mockStepDetailsDao *daoMocks.MockBatchOrderProcessingStepDetailsDao
	mockPreReqClient   *prereqMocks.MockPrerequisiteHandlerClient
	mockFileGenClient  *filegenMocks.MockFileGeneratorClient
	mockOrderClient    *omsMock.MockOrderServiceClient
	mockPreReqPub      *mock_queue.MockPublisher
	mockFileGenPub     *mock_queue.MockPublisher
	mockSipLedger      *mock_sip.MockISIPLedgerHelper
	mockPub            *InvestmentOrcPublishers
}

func setupService(ctr *gomock.Controller) (*Service, *MockDependencies) {
	mockOrderDao := daoMocks.NewMockOrderDao(ctr)
	mockOrderStatusDao := daoMocks.NewMockOrderStatusUpdateDao(ctr)
	mockAmcDao := daoMocks.NewMockAmcInfoDao(ctr)
	mockFolioDao := daoMocks.NewMockFolioLedgerDao(ctr)
	mockBatchProcDao := daoMocks.NewMockBatchOrderProcessingDetailsDao(ctr)
	mockStepDetailsDao := daoMocks.NewMockBatchOrderProcessingStepDetailsDao(ctr)
	mockPreReqClient := prereqMocks.NewMockPrerequisiteHandlerClient(ctr)
	mockFileGenClient := filegenMocks.NewMockFileGeneratorClient(ctr)
	mockOrderClient := omsMock.NewMockOrderServiceClient(ctr)
	mockPreReqPub := mock_queue.NewMockPublisher(ctr)
	mockFileGenPub := mock_queue.NewMockPublisher(ctr)
	mockSipLedger := mock_sip.NewMockISIPLedgerHelper(ctr)
	mockPub := &InvestmentOrcPublishers{
		PreReqCompletePublisher:  mockPreReqPub,
		FileGenCompletePublisher: mockFileGenPub,
	}
	return NewService(mockOrderDao, mockOrderStatusDao, mockAmcDao, mockBatchProcDao, mockStepDetailsDao, mockPreReqClient, mockFileGenClient, mockOrderClient, mockPub,
			txnExecutor, &updOrder.OrderStatusNotifierImpl{}, conf, mockFolioDao, mockSipLedger), &MockDependencies{
			mockOrderDao:       mockOrderDao,
			mockOrderStatusDao: mockOrderStatusDao,
			mockAmcDao:         mockAmcDao,
			mockFolioDao:       mockFolioDao,
			mockBatchProcDao:   mockBatchProcDao,
			mockStepDetailsDao: mockStepDetailsDao,
			mockPreReqClient:   mockPreReqClient,
			mockFileGenClient:  mockFileGenClient,
			mockOrderClient:    mockOrderClient,
			mockPreReqPub:      mockPreReqPub,
			mockFileGenPub:     mockFileGenPub,
			mockSipLedger:      mockSipLedger,
			mockPub:            mockPub,
		}

}

var capturedBatch *orderPb.BatchOrderProcessingDetails
var capturedOrders []*orderPb.Order
var capturedStep *orderPb.BatchOrderProcessingStepDetails

func Test_ProcessOrderFileGenerationSuccess(t *testing.T) {
	ctx := context.Background()
	ctr := gomock.NewController(t)

	testCases := []struct {
		name             string
		req              *pb.ProcessOrderFileGenerationSuccessRequest
		setupGetMocks    func(a *MockDependencies)
		setupUpdateMocks func(a *MockDependencies)
		expectedResponse *pb.ProcessOrderFileGenerationSuccessResponse
		expectedBatch    *orderPb.BatchOrderProcessingDetails
		// Note: Add only those orders for which something is getting updated since we are comparing with orders captured from MockDao update call
		expectedOrders     []*orderPb.Order
		expectedStepDetail *orderPb.BatchOrderProcessingStepDetails
		wantErr            bool
	}{
		{
			name: "Create OMS order. All orders successful",
			req: &pb.ProcessOrderFileGenerationSuccessRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func(a *MockDependencies) {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				a.mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderType = orderPb.OrderType_SELL
				ordList = append(ordList, ord1, ord2)
				a.mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				a.mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION).Return(nil, epifierrors.ErrRecordNotFound)
				a.mockStepDetailsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(stepDetail, nil)

				// amc
				a.mockAmcDao.EXPECT().GetByAmc(ctx, mfPb.Amc_ICICI_PRUDENTIAL).Return(amcInfo, nil)
			},
			setupUpdateMocks: func(a *MockDependencies) {
				// batch
				a.mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				a.mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil)
				a.mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil)
				logger.Info(ctx, "Orders", zap.Any("Orders", capturedOrders))
				a.mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)

				// stepDetail
				a.mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// oms
				a.mockOrderClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Times(2).Return(sampleCreateOrderResp, nil)

				a.mockSipLedger.EXPECT().UpdateSipLedgerIfNeeded(gomock.Any(), gomock.Any()).Return(nil).Times(2)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationSuccessResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_SUCCESS,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:           ORDER_ID_1,
					ActorId:      ACTOR_1,
					MutualFundId: "mf1",
					OrderType:    orderPb.OrderType_BUY,
					OrderStatus:  orderPb.OrderStatus_INITIATED,
					OmsOrders: &orderPb.OmsOrders{
						OrderIds: []string{"2960ca204fe97ec767b90067123666b9"},
					},
				},
				{
					Id:           ORDER_ID_2,
					ActorId:      ACTOR_2,
					MutualFundId: "mf1",
					OrderType:    orderPb.OrderType_SELL,
					OrderStatus:  orderPb.OrderStatus_INITIATED,
					OmsOrders: &orderPb.OmsOrders{
						OrderIds: []string{"8ccaf57a33b2c65bd582e28e343f2261"},
					},
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "One order successful, one order pending. First attempt, so should get transient failure",
			req: &pb.ProcessOrderFileGenerationSuccessRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func(a *MockDependencies) {
				// mockBatch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				a.mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ordList = append(ordList, ord1, ord2)
				a.mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				a.mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION).Return(nil, epifierrors.ErrRecordNotFound)
				a.mockStepDetailsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(stepDetail, nil)

				// amc
				a.mockAmcDao.EXPECT().GetByAmc(ctx, mfPb.Amc_ICICI_PRUDENTIAL).Return(amcInfo, nil)
			},
			setupUpdateMocks: func(a *MockDependencies) {
				// mockBatch
				a.mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)
				logger.Info(ctx, "Orders", zap.Any("mockBatch", capturedBatch))

				// order
				a.mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil)
				logger.Info(ctx, "Orders", zap.Any("Orders", capturedOrders))
				a.mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

				// stepDetail
				a.mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(sampleStepDetail, nil)
				logger.Info(ctx, "Orders", zap.Any("step", capturedStep))

				// oms -  throwing an error for second order
				a.mockOrderClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Times(1).Return(sampleCreateOrderResp, nil)
				failedCreateOrderResp := &omsPb.CreateOrderResponse{}
				_ = copier.Copy(failedCreateOrderResp, sampleCreateOrderResp)
				failedCreateOrderResp.Status = rpc.StatusAborted()
				a.mockOrderClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Times(1).Return(failedCreateOrderResp, nil)

				a.mockSipLedger.EXPECT().UpdateSipLedgerIfNeeded(gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationSuccessResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:           ORDER_ID_1,
					ActorId:      ACTOR_1,
					MutualFundId: "mf1",
					OrderType:    orderPb.OrderType_BUY,
					OrderStatus:  orderPb.OrderStatus_INITIATED,
					OmsOrders: &orderPb.OmsOrders{
						OrderIds: []string{"2960ca204fe97ec767b90067123666b9"},
					},
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{ORDER_ID_2},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_INITIATED,
			},
			wantErr: false,
		},
		{
			name: "One Order already successful, one order pending. Successful retry, not last attempt.",
			req: &pb.ProcessOrderFileGenerationSuccessRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func(a *MockDependencies) {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				a.mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_INITIATED
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ordList = append(ordList, ord1, ord2)
				a.mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Metadata.GetOrderStatusData().SuccessOrderIds = []string{ORDER_ID_1}
				stepDetail.Metadata.GetOrderStatusData().PendingOrderIds = []string{ORDER_ID_2}
				a.mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION).Return(stepDetail, nil)

				// amc
				a.mockAmcDao.EXPECT().GetByAmc(ctx, mfPb.Amc_ICICI_PRUDENTIAL).Return(amcInfo, nil)
			},
			setupUpdateMocks: func(a *MockDependencies) {
				// batch
				a.mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				a.mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil)
				logger.Info(ctx, "Orders", zap.Any("Orders", capturedOrders))
				a.mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

				// stepDetail
				a.mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// oms
				a.mockOrderClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Times(1).Return(sampleCreateOrderResp, nil)

				a.mockSipLedger.EXPECT().UpdateSipLedgerIfNeeded(gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationSuccessResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_SUCCESS,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:           ORDER_ID_2,
					ActorId:      ACTOR_2,
					MutualFundId: "mf1",
					OrderType:    orderPb.OrderType_BUY,
					OrderStatus:  orderPb.OrderStatus_INITIATED,
					OmsOrders: &orderPb.OmsOrders{
						OrderIds: []string{"8ccaf57a33b2c65bd582e28e343f2261"},
					},
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "One Order already successful, one order pending. Failed retry, not last attempt.",
			req: &pb.ProcessOrderFileGenerationSuccessRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func(a *MockDependencies) {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				a.mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_INITIATED
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ordList = append(ordList, ord1, ord2)
				a.mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Metadata.GetOrderStatusData().SuccessOrderIds = []string{ORDER_ID_1}
				stepDetail.Metadata.GetOrderStatusData().PendingOrderIds = []string{ORDER_ID_2}
				a.mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION).Return(stepDetail, nil)

				// amc
				a.mockAmcDao.EXPECT().GetByAmc(ctx, mfPb.Amc_ICICI_PRUDENTIAL).Return(amcInfo, nil)
			},
			setupUpdateMocks: func(a *MockDependencies) {
				// batch
				a.mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				a.mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil).Times(0)
				logger.Info(ctx, "Orders", zap.Any("Orders", capturedOrders))

				// stepDetail
				a.mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// oms
				failedCreateOrderResp := &omsPb.CreateOrderResponse{}
				_ = copier.Copy(failedCreateOrderResp, sampleCreateOrderResp)
				failedCreateOrderResp.Status = rpc.StatusAborted()
				a.mockOrderClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Times(1).Return(failedCreateOrderResp, nil)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationSuccessResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
			},
			expectedOrders: nil,
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{ORDER_ID_2},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_INITIATED,
			},
			wantErr: false,
		},
		{
			name: "One Order already successful, one order pending. Successful retry, last attempt.",
			req: &pb.ProcessOrderFileGenerationSuccessRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: true,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func(a *MockDependencies) {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				a.mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_INITIATED
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING_COMPLETE
				ordList = append(ordList, ord1, ord2)
				a.mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Metadata.GetOrderStatusData().SuccessOrderIds = []string{ORDER_ID_1}
				stepDetail.Metadata.GetOrderStatusData().PendingOrderIds = []string{ORDER_ID_2}
				a.mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION).Return(stepDetail, nil)

				// amc
				a.mockAmcDao.EXPECT().GetByAmc(ctx, mfPb.Amc_ICICI_PRUDENTIAL).Return(amcInfo, nil)
			},
			setupUpdateMocks: func(a *MockDependencies) {
				// batch
				a.mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				a.mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil)
				logger.Info(ctx, "Orders", zap.Any("Orders", capturedOrders))
				a.mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

				// stepDetail
				a.mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// oms
				a.mockOrderClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Times(1).Return(sampleCreateOrderResp, nil)

				a.mockSipLedger.EXPECT().UpdateSipLedgerIfNeeded(gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationSuccessResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_SUCCESS,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:           ORDER_ID_2,
					ActorId:      ACTOR_2,
					MutualFundId: "mf1",
					OrderType:    orderPb.OrderType_BUY,
					OrderStatus:  orderPb.OrderStatus_INITIATED,
					OmsOrders: &orderPb.OmsOrders{
						OrderIds: []string{"8ccaf57a33b2c65bd582e28e343f2261"},
					},
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "One Order already successful, one order pending. Failed retry, last attempt.",
			req: &pb.ProcessOrderFileGenerationSuccessRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: true,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func(a *MockDependencies) {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				a.mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_INITIATED
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ordList = append(ordList, ord1, ord2)
				a.mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Metadata.GetOrderStatusData().SuccessOrderIds = []string{ORDER_ID_1}
				stepDetail.Metadata.GetOrderStatusData().PendingOrderIds = []string{ORDER_ID_2}
				a.mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION).Return(stepDetail, nil)

				// amc
				a.mockAmcDao.EXPECT().GetByAmc(ctx, mfPb.Amc_ICICI_PRUDENTIAL).Return(amcInfo, nil)
			},
			setupUpdateMocks: func(a *MockDependencies) {
				// batch
				a.mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				// Update Batch takes orderIds instead of orders, hence creating a order with expected values for assertions to work
				a.mockOrderDao.EXPECT().UpdateBatch(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, orderIds []string, masks map[orderPb.OrderFieldMask]interface{}) {
						for _, ordId := range orderIds {
							order := &orderPb.Order{
								Id:          ordId,
								OrderStatus: masks[orderPb.OrderFieldMask_ORDER_STATUS].(orderPb.OrderStatus),
							}
							capturedOrders = append(capturedOrders, order)
						}
					}).Return(nil)
				logger.Info(ctx, "Orders", zap.Any("Orders", capturedOrders))
				a.mockOrderStatusDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

				// stepDetail
				a.mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// oms
				failedCreateOrderResp := &omsPb.CreateOrderResponse{}
				_ = copier.Copy(failedCreateOrderResp, sampleCreateOrderResp)
				failedCreateOrderResp.Status = rpc.StatusAborted()
				a.mockOrderClient.EXPECT().CreateOrder(gomock.Any(), gomock.Any()).Times(1).Return(failedCreateOrderResp, nil)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationSuccessResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_SUCCESS,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:          ORDER_ID_2,
					OrderStatus: orderPb.OrderStatus_MANUAL_INTERVENTION,
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_OMS_ORDER_CREATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{ORDER_ID_2},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS,
			},
			wantErr: false,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			// Reset global variables
			capturedBatch = nil
			capturedOrders = make([]*orderPb.Order, 0)
			capturedStep = nil
			// Need to reset this as copier.copy is still copying the nested pointer location
			sampleStepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
				OrderStatusData: &orderPb.OrderStatusStepData{
					SuccessOrderIds: []string{},
					FailedOrderIds:  []string{},
					PendingOrderIds: []string{},
				},
			}}
			orcSvc, mockDependencies := setupService(ctr)
			tt.setupGetMocks(mockDependencies)
			tt.setupUpdateMocks(mockDependencies)
			got, err := orcSvc.ProcessOrderFileGenerationSuccess(ctx, tt.req)
			logger.Info(ctx, "test", zap.Any("Got", got), zap.Error(err))
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessFileGenerationSuccess() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&orderPb.Order{}, "units", "amount", "amc"),
			}

			if diff := cmp.Diff(got, tt.expectedResponse, opts...); diff != "" {
				t.Errorf("Mismatch in Consumer Response got = %v,\n want %v", got, tt.expectedResponse)
				return
			}
			if diff := cmp.Diff(capturedBatch, tt.expectedBatch, opts...); diff != "" {
				t.Errorf("Mismatch in Batch got = %v,\n want %v", capturedBatch, tt.expectedBatch)
				return
			}
			for ind, val := range tt.expectedOrders {
				if diff := cmp.Diff(capturedOrders[ind], val, opts...); diff != "" {
					t.Errorf("Mismatch in Order got = %v,\n want %v", capturedOrders[ind], val)
					return
				}
			}
			if diff := cmp.Diff(capturedStep, tt.expectedStepDetail, opts...); diff != "" {
				t.Errorf("Mismatch in Step Details got = %v,\n want %v,diff %v", capturedStep, tt.expectedStepDetail, diff)
				return
			}
		})
	}
}

func Test_ProcessOrderFileGeneration(t *testing.T) {
	ctx := context.Background()
	ctr := gomock.NewController(t)
	mockOrderDao := daoMocks.NewMockOrderDao(ctr)
	mockOrderStatusDao := daoMocks.NewMockOrderStatusUpdateDao(ctr)
	mockFolioDao := daoMocks.NewMockFolioLedgerDao(ctr)
	mockAmcDao := daoMocks.NewMockAmcInfoDao(ctr)
	mockBatchProcDao := daoMocks.NewMockBatchOrderProcessingDetailsDao(ctr)
	mockStepDetailsDao := daoMocks.NewMockBatchOrderProcessingStepDetailsDao(ctr)
	mockPreReqClient := prereqMocks.NewMockPrerequisiteHandlerClient(ctr)
	mockFileGenClient := filegenMocks.NewMockFileGeneratorClient(ctr)
	mockOrderClient := omsMock.NewMockOrderServiceClient(ctr)
	mockPreReqPub := mock_queue.NewMockPublisher(ctr)
	mockFileGenPub := mock_queue.NewMockPublisher(ctr)
	mockSipLedger := mock_sip.NewMockISIPLedgerHelper(ctr)

	mockPub := &InvestmentOrcPublishers{
		PreReqCompletePublisher:  mockPreReqPub,
		FileGenCompletePublisher: mockFileGenPub,
	}
	orcSvc := NewService(mockOrderDao, mockOrderStatusDao, mockAmcDao, mockBatchProcDao, mockStepDetailsDao, mockPreReqClient, mockFileGenClient, mockOrderClient, mockPub,
		txnExecutor, &updOrder.OrderStatusNotifierImpl{}, conf, mockFolioDao, mockSipLedger)

	testCases := []struct {
		name             string
		req              *pb.ProcessOrderFileGenerationRequest
		setupGetMocks    func()
		setupUpdateMocks func()
		expectedResponse *pb.ProcessOrderFileGenerationResponse
		expectedBatch    *orderPb.BatchOrderProcessingDetails
		// Note: Add only those orders for which something is getting updated since we are comparing with orders captured from MockDao update call
		expectedOrders     []*orderPb.Order
		expectedStepDetail *orderPb.BatchOrderProcessingStepDetails
		wantErr            bool
	}{
		{
			name: "Generate Forward feed File. All orders successful",
			req: &pb.ProcessOrderFileGenerationRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func() {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				batch.CurrentStep = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				batch.Status = orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED
				mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2.OrderType = orderPb.OrderType_SELL
				ordList = append(ordList, ord1, ord2)
				mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				preReqStep := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(preReqStep, sampleStepDetail)
				preReqStep.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				preReqStep.Status = orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS
				preReqStep.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_PreRequisiteData{
					PreRequisiteData: &orderPb.PreRequisiteStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES).Return(preReqStep, nil)

				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION
				stepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION).Return(nil, epifierrors.ErrRecordNotFound)
				mockStepDetailsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(stepDetail, nil)

				// file generator
				mockFileGenClient.EXPECT().GenerateOrderFeedFile(gomock.Any(), gomock.Any(), gomock.Any()).Return(sampleFileGenResp, nil)
			},
			setupUpdateMocks: func() {
				// batch
				mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				mockOrderDao.EXPECT().UpdateBatch(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, orderIds []string, masks map[orderPb.OrderFieldMask]interface{}) {
						for _, ordId := range orderIds {
							order := &orderPb.Order{
								Id:          ordId,
								OrderStatus: masks[orderPb.OrderFieldMask_ORDER_STATUS].(orderPb.OrderStatus),
							}
							capturedOrders = append(capturedOrders, order)
						}
					}).Return(nil)
				mockOrderStatusDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

				// stepDetail
				mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// publish
				mockFileGenPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:          ORDER_ID_1,
					OrderStatus: orderPb.OrderStatus_PRE_PROCESSING_COMPLETE,
				},
				{
					Id:          ORDER_ID_2,
					OrderStatus: orderPb.OrderStatus_PRE_PROCESSING_COMPLETE,
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "Generate Forward feed File.One order successful, one order failed",
			req: &pb.ProcessOrderFileGenerationRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func() {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				batch.CurrentStep = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				batch.Status = orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED
				mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2.OrderType = orderPb.OrderType_SELL
				ordList = append(ordList, ord1, ord2)
				mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				preReqStep := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(preReqStep, sampleStepDetail)
				preReqStep.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				preReqStep.Status = orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS
				preReqStep.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_PreRequisiteData{
					PreRequisiteData: &orderPb.PreRequisiteStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES).Return(preReqStep, nil)

				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION
				stepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION).Return(nil, epifierrors.ErrRecordNotFound)
				mockStepDetailsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(stepDetail, nil)

				// file generator
				fileResp := &filegen.GenerateOrderFeedFileResponse{}
				_ = copier.Copy(fileResp, sampleFileGenResp)
				fileResp.SuccessfulOrderIds = []string{ORDER_ID_1}
				fileResp.FailedOrderInfo = map[string]filegen.EntitySubStatus{
					ORDER_ID_2: filegen.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_PAYMENT_DATA,
				}
				mockFileGenClient.EXPECT().GenerateOrderFeedFile(gomock.Any(), gomock.Any(), gomock.Any()).Return(fileResp, nil)
			},
			setupUpdateMocks: func() {
				// batch
				mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				mockOrderDao.EXPECT().UpdateBatch(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, orderIds []string, masks map[orderPb.OrderFieldMask]interface{}) {
						for _, ordId := range orderIds {
							order := &orderPb.Order{
								Id:          ordId,
								OrderStatus: masks[orderPb.OrderFieldMask_ORDER_STATUS].(orderPb.OrderStatus),
							}
							capturedOrders = append(capturedOrders, order)
						}
					}).Return(nil).Times(1)
				mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil).Times(1)
				mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
				mockOrderStatusDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)

				// stepDetail
				mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// publish
				mockFileGenPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:          ORDER_ID_1,
					OrderStatus: orderPb.OrderStatus_PRE_PROCESSING_COMPLETE,
				},
				{
					Id:            ORDER_ID_2,
					OrderStatus:   orderPb.OrderStatus_MANUAL_INTERVENTION,
					FailureReason: orderPb.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_ERROR,
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1},
						FailedOrderIds:  []string{ORDER_ID_2},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS,
			},
			wantErr: false,
		},
		{
			name: "Generate Forward feed File. Both orders failed",
			req: &pb.ProcessOrderFileGenerationRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func() {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				batch.CurrentStep = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				batch.Status = orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED
				mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2.OrderType = orderPb.OrderType_SELL
				ordList = append(ordList, ord1, ord2)
				mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				preReqStep := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(preReqStep, sampleStepDetail)
				preReqStep.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				preReqStep.Status = orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS
				preReqStep.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_PreRequisiteData{
					PreRequisiteData: &orderPb.PreRequisiteStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES).Return(preReqStep, nil)

				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION
				stepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION).Return(nil, epifierrors.ErrRecordNotFound)
				mockStepDetailsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(stepDetail, nil)

				// file generator
				fileResp := &filegen.GenerateOrderFeedFileResponse{}
				_ = copier.Copy(fileResp, sampleFileGenResp)
				fileResp.Status = rpc.StatusInternal()
				mockFileGenClient.EXPECT().GenerateOrderFeedFile(gomock.Any(), gomock.Any(), gomock.Any()).Return(fileResp, nil)
			},
			setupUpdateMocks: func() {
				// batch
				mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Times(0)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			expectedBatch:      nil,
			expectedOrders:     nil,
			expectedStepDetail: nil,
			wantErr:            false,
		},
		{
			name: "Generate Forward feed File. Both orders failed but got success response from File generator",
			req: &pb.ProcessOrderFileGenerationRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func() {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				batch.CurrentStep = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				batch.Status = orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED
				mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2.OrderType = orderPb.OrderType_SELL
				ordList = append(ordList, ord1, ord2)
				mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				preReqStep := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(preReqStep, sampleStepDetail)
				preReqStep.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				preReqStep.Status = orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS
				preReqStep.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_PreRequisiteData{
					PreRequisiteData: &orderPb.PreRequisiteStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES).Return(preReqStep, nil)

				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION
				stepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION).Return(nil, epifierrors.ErrRecordNotFound)
				mockStepDetailsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(stepDetail, nil)

				// file generator
				fileResp := &filegen.GenerateOrderFeedFileResponse{}
				_ = copier.Copy(fileResp, sampleFileGenResp)
				fileResp.Status = rpc.StatusOk()
				fileResp.SuccessfulOrderIds = []string{}
				failedMap := make(map[string]filegen.EntitySubStatus)
				failedMap[ORDER_ID_1] = filegen.EntitySubStatus_ENTITY_SUB_STATUS_PAYMENT_ERROR_INVALID_PIN
				failedMap[ORDER_ID_2] = filegen.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_CATALOGUE_DATA
				fileResp.FailedOrderInfo = failedMap
				mockFileGenClient.EXPECT().GenerateOrderFeedFile(gomock.Any(), gomock.Any(), gomock.Any()).Return(fileResp, nil)
			},
			setupUpdateMocks: func() {
				// batch
				mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil).Times(2)
				mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)

				// stepDetail
				mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

			},
			expectedResponse: &pb.ProcessOrderFileGenerationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_FAILED,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:            ORDER_ID_1,
					OrderStatus:   orderPb.OrderStatus_FAILURE,
					FailureReason: orderPb.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_CANCELLED_PAYMENT_ERROR_INVALID_PIN,
				},
				{
					Id:            ORDER_ID_2,
					OrderStatus:   orderPb.OrderStatus_MANUAL_INTERVENTION,
					FailureReason: orderPb.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_ERROR,
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{ORDER_ID_1, ORDER_ID_2},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_FAILED,
			},
			wantErr: false,
		},
		{
			name: "Generate Forward feed File. Got non success response from File generator and was last retry",
			req: &pb.ProcessOrderFileGenerationRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: true,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func() {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				batch.CurrentStep = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				batch.Status = orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED
				mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2.OrderType = orderPb.OrderType_SELL
				ordList = append(ordList, ord1, ord2)
				mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				preReqStep := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(preReqStep, sampleStepDetail)
				preReqStep.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				preReqStep.Status = orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS
				preReqStep.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_PreRequisiteData{
					PreRequisiteData: &orderPb.PreRequisiteStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES).Return(preReqStep, nil)

				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION
				stepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION).Return(nil, epifierrors.ErrRecordNotFound)
				mockStepDetailsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(stepDetail, nil)

				// file generator
				fileResp := &filegen.GenerateOrderFeedFileResponse{}
				_ = copier.Copy(fileResp, sampleFileGenResp)
				fileResp.Status = rpc.StatusOk()
				fileResp.SuccessfulOrderIds = []string{}
				fileResp.FileGenerationAttemptStatus = filegen.FileGenAttemptStatus_GENERATION_FAILED
				failedMap := make(map[string]filegen.EntitySubStatus)
				failedMap[ORDER_ID_1] = filegen.EntitySubStatus_ENTITY_SUB_STATUS_ALREADY_MAPPED_TO_ANOTHER_FILE
				failedMap[ORDER_ID_2] = filegen.EntitySubStatus_ENTITY_SUB_STATUS_ALREADY_MAPPED_TO_ANOTHER_FILE
				fileResp.FailedOrderInfo = failedMap
				mockFileGenClient.EXPECT().GenerateOrderFeedFile(gomock.Any(), gomock.Any(), gomock.Any()).Return(fileResp, nil)
			},
			setupUpdateMocks: func() {
				// batch
				mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				mockOrderDao.EXPECT().UpdateBatch(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, orderIds []string, masks map[orderPb.OrderFieldMask]interface{}) {
						for _, ordId := range orderIds {
							order := &orderPb.Order{
								Id:          ordId,
								OrderStatus: masks[orderPb.OrderFieldMask_ORDER_STATUS].(orderPb.OrderStatus),
							}
							capturedOrders = append(capturedOrders, order)
						}
					}).Return(nil).Times(1)
				mockOrderStatusDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)

				// stepDetail
				mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

			},
			expectedResponse: &pb.ProcessOrderFileGenerationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_FAILED,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:          ORDER_ID_1,
					OrderStatus: orderPb.OrderStatus_MANUAL_INTERVENTION,
				},
				{
					Id:          ORDER_ID_2,
					OrderStatus: orderPb.OrderStatus_MANUAL_INTERVENTION,
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{ORDER_ID_1, ORDER_ID_2},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_FAILED,
			},
			wantErr: false,
		},
		{
			name: "Generate Forward feed File. One order successful after retry, one order failed",
			req: &pb.ProcessOrderFileGenerationRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{
					IsLastAttempt: false,
				},
				Rta:     commonvgpb.Vendor_CAMS,
				BatchId: BATCH_ID,
			},
			setupGetMocks: func() {
				// batch
				batch := &orderPb.BatchOrderProcessingDetails{}
				_ = copier.Copy(batch, sampleBatch)
				batch.CurrentStep = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				batch.Status = orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED
				mockBatchProcDao.EXPECT().GetById(ctx, BATCH_ID).Return(batch, nil)

				// order
				ordList := make([]*orderPb.Order, 0)
				ord1 := &orderPb.Order{}
				_ = copier.Copy(ord1, sampOrder)
				ord1.Id = ORDER_ID_1
				ord1.ActorId = ACTOR_1
				ord1.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2 := &orderPb.Order{}
				_ = copier.Copy(ord2, sampOrder)
				ord2.Id = ORDER_ID_2
				ord2.ActorId = ACTOR_2
				ord2.OrderStatus = orderPb.OrderStatus_PRE_PROCESSING
				ord2.OrderType = orderPb.OrderType_SELL
				ordList = append(ordList, ord1, ord2)
				mockOrderDao.EXPECT().GetByOrderIds(ctx, []string{ORDER_ID_1, ORDER_ID_2}).Return(ordList, nil)

				// step
				preReqStep := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(preReqStep, sampleStepDetail)
				preReqStep.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES
				preReqStep.Status = orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS
				preReqStep.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_PreRequisiteData{
					PreRequisiteData: &orderPb.PreRequisiteStepData{
						SuccessOrderIds: []string{ORDER_ID_1, ORDER_ID_2},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_PRE_REQUISITES).Return(preReqStep, nil)

				stepDetail := &orderPb.BatchOrderProcessingStepDetails{}
				_ = copier.Copy(stepDetail, sampleStepDetail)
				stepDetail.Step = orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION
				stepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{},
						FailedOrderIds:  []string{},
						PendingOrderIds: []string{},
					},
				}}
				mockStepDetailsDao.EXPECT().GetByBatchIdAndStep(ctx, BATCH_ID, orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION).Return(stepDetail, nil)

				// file generator
				fileResp := &filegen.GenerateOrderFeedFileResponse{}
				_ = copier.Copy(fileResp, sampleFileGenResp)
				fileResp.SuccessfulOrderIds = []string{ORDER_ID_1}
				fileResp.FailedOrderInfo = map[string]filegen.EntitySubStatus{
					ORDER_ID_2: filegen.EntitySubStatus_ENTITY_SUB_STATUS_FAILED_MISSING_PAYMENT_DATA,
				}
				mockFileGenClient.EXPECT().GenerateOrderFeedFile(gomock.Any(), gomock.Any(), gomock.Any()).Return(fileResp, nil)
			},
			setupUpdateMocks: func() {
				// batch
				mockBatchProcDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, batchDet *orderPb.BatchOrderProcessingDetails, masks []orderPb.BatchOrderProcessingRequestFieldMask) {
						capturedBatch = batchDet
					}).Return(sampleBatch, nil)

				// order
				mockOrderDao.EXPECT().UpdateBatch(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, orderIds []string, masks map[orderPb.OrderFieldMask]interface{}) {
						for _, ordId := range orderIds {
							order := &orderPb.Order{
								Id:          ordId,
								OrderStatus: masks[orderPb.OrderFieldMask_ORDER_STATUS].(orderPb.OrderStatus),
							}
							capturedOrders = append(capturedOrders, order)
						}
					}).Return(nil).Times(1)
				mockOrderDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, order *orderPb.Order, masks []orderPb.OrderFieldMask) {
						capturedOrders = append(capturedOrders, order)
					}).Return(nil, nil).Times(1)
				mockOrderStatusDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

				// stepDetail
				mockStepDetailsDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(arg1 context.Context, step *orderPb.BatchOrderProcessingStepDetails, masks []orderPb.BatchOrderProcessingStepDetailsFieldMask) {
						capturedStep = step
					}).Return(capturedStep, nil)

				// publish
				mockFileGenPub.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", nil)
			},
			expectedResponse: &pb.ProcessOrderFileGenerationResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			expectedBatch: &orderPb.BatchOrderProcessingDetails{
				Id:          BATCH_ID,
				Status:      orderPb.BatchOrderProcessingStatus_PROCESSING_STATUS_INITIATED,
				OrderIds:    []string{ORDER_ID_1, ORDER_ID_2},
				CurrentStep: orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
			},
			expectedOrders: []*orderPb.Order{
				{
					Id:          ORDER_ID_1,
					OrderStatus: orderPb.OrderStatus_PRE_PROCESSING_COMPLETE,
				},
				{
					Id:            ORDER_ID_2,
					OrderStatus:   orderPb.OrderStatus_MANUAL_INTERVENTION,
					FailureReason: orderPb.FailureReason_FWD_ORDER_FEED_FILE_GENERATION_ERROR,
				},
			},
			expectedStepDetail: &orderPb.BatchOrderProcessingStepDetails{
				Id:      "step1",
				BatchId: BATCH_ID,
				Step:    orderPb.OrderProcessingStep_PROCESSING_STEP_FWD_FILE_GENERATION,
				Metadata: &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
					OrderStatusData: &orderPb.OrderStatusStepData{
						SuccessOrderIds: []string{ORDER_ID_1},
						FailedOrderIds:  []string{ORDER_ID_2},
						PendingOrderIds: []string{},
					},
				}},
				Status: orderPb.OrderProcessingStepStatus_STEP_STATUS_SUCCESS,
			},
			wantErr: false,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			// Reset global variables
			capturedBatch = nil
			capturedOrders = make([]*orderPb.Order, 0)
			capturedStep = nil
			// Need to reset this as copier.copy is still copying the nested pointer location
			sampleStepDetail.Metadata = &orderPb.BatchOrderProcessingStepMetadata{Data: &orderPb.BatchOrderProcessingStepMetadata_OrderStatusData{
				OrderStatusData: &orderPb.OrderStatusStepData{
					SuccessOrderIds: []string{},
					FailedOrderIds:  []string{},
					PendingOrderIds: []string{},
				},
			}}
			tt.setupGetMocks()
			tt.setupUpdateMocks()
			got, err := orcSvc.ProcessOrderFileGeneration(ctx, tt.req)
			logger.Info(ctx, "test", zap.Any("Got", got), zap.Error(err))
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessFileGenerationSuccess() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.expectedResponse, opts...); diff != "" {
				t.Errorf("Mismatch in Consumer Response got = %v,\n want %v", got, tt.expectedResponse)
			}
			if diff := cmp.Diff(capturedBatch, tt.expectedBatch, opts...); diff != "" {
				t.Errorf("Mismatch in Batch got = %v,\n want %v", capturedBatch, tt.expectedBatch)
			}
			for ind, val := range tt.expectedOrders {
				if diff := cmp.Diff(capturedOrders[ind], val, opts...); diff != "" {
					// if diff is present, verify the diff for the same order id.
					// This is to prevent diffs due to different ordering in the captured orders
					diffPresent := true
					for _, capturedOrder := range capturedOrders {
						if capturedOrder.Id == val.Id {
							// Setting this to handle multiple orders with same orderId in the captured orders
							diffPresent = true
							sameOrderDiff := cmp.Diff(capturedOrder, val, opts...)
							// if no diff present for the same orderId then set diffPresent as false
							if sameOrderDiff == "" {
								diffPresent = false
							}
						}
					}
					if diffPresent {
						t.Errorf("Mismatch in Order got = %v,\n want %v", capturedOrders[ind], val)
					}
				}
			}
			if diff := cmp.Diff(capturedStep, tt.expectedStepDetail, opts...); diff != "" {
				// Verify Metadata has diff or not
				if !areSlicesSame(capturedStep.GetMetadata().GetOrderStatusData().GetSuccessOrderIds(),
					tt.expectedStepDetail.GetMetadata().GetOrderStatusData().GetSuccessOrderIds()) {
					t.Errorf("Mismatch in Step Details got = %v,\n want %v", capturedStep, tt.expectedStepDetail)
				}
				if !areSlicesSame(capturedStep.GetMetadata().GetOrderStatusData().GetFailedOrderIds(),
					tt.expectedStepDetail.GetMetadata().GetOrderStatusData().GetFailedOrderIds()) {
					t.Errorf("Mismatch in Step Details got = %v,\n want %v", capturedStep, tt.expectedStepDetail)
				}
				if !areSlicesSame(capturedStep.GetMetadata().GetOrderStatusData().GetPendingOrderIds(),
					tt.expectedStepDetail.GetMetadata().GetOrderStatusData().GetPendingOrderIds()) {
					t.Errorf("Mismatch in Step Details got = %v,\n want %v", capturedStep, tt.expectedStepDetail)
				}
				// If no diff in Metadata check diff in other fields
				capturedStep.Metadata.Data = nil
				tt.expectedStepDetail.Metadata.Data = nil
				if diff := cmp.Diff(capturedStep, tt.expectedStepDetail, opts...); diff != "" {
					t.Errorf("Mismatch in Step Details got = %v,\n want %v", capturedStep, tt.expectedStepDetail)
				}
			}
		})
	}
}

func areSlicesSame(x, y []string) bool {
	if len(x) != len(y) {
		return false
	}
	// create a map of string in x -> number of occurrences
	diff := make(map[string]int, len(x))
	for _, xStr := range x {
		diff[xStr]++
	}
	for _, yStr := range y {
		// If yStr is not in diff map slices are not same
		if _, ok := diff[yStr]; !ok {
			return false
		}
		diff[yStr] -= 1
		// Delete entry from diff map if occurence is 0
		if diff[yStr] == 0 {
			delete(diff, yStr)
		}
	}
	// If no diff string present then slices are same otherwise they are not
	return len(diff) == 0
}
