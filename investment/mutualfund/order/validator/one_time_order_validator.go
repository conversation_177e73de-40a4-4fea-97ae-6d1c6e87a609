package validator

import (
	"context"
	"errors"
	"fmt"
	"strings"

	authpb "github.com/epifi/gamma/api/investment/auth"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

var (
	OneTimeOrderValidationError = errors.New("OneTimeOrderValidationError")
	OTPValidationError          = errors.New("OneTimeOrderValidationError")
	OneTimeDisabledISINs        = []string{"INF174KA1FQ7", "INF843K01AU1", "INF754K01LB7", "INF843K01IZ3", "INF843K01AW7", "INF843K01GI3", "INF843K01EC1", "INF204K01K15", "INF277K011O1",
		// Blocking IDBI ISINS temporarily
		"INF397L01LC1", "INF397L01KA7", "INF397L01554", "INF397L01BU4", "INF397L01LK4", "INF397L01KU5", "INF397L01JK8", "INF397L01AZ5", "INF397L01AW2", "INF397L01JS1", "INF397L01HF2", "INF397L01KM2", "INF397L01BV2", "INF397L01AH3", "INF397L01AP6", "INF397L01GG2", "INF397L01JC5", "INF397L01BG3", "INF397L01EC6", "INF397L01AV4",
	}
)

type OneTimeOrderValidator struct {
	conf                 *genConf.Config
	investmentAuthClient authpb.AuthClient
}

func NewOneTimeOrderValidator(conf *genConf.Config, investmentAuthClient authpb.AuthClient) *OneTimeOrderValidator {
	return &OneTimeOrderValidator{conf: conf, investmentAuthClient: investmentAuthClient}
}

// Validate checks if a fund available for investors to invest
// if a mutual fund's fund_investment_status is AVAILABLE_FOR_INVESTMENT then investment is allowed for all investors
// if a mutual fund's fund_investment_status is AVAILABLE_ONLY_FOR_EXISTING_INVESTORS then investment is only allowed for existing investors via SIP
// if a mutual fund's fund_investment_status is UNAVAILABLE_FOR_INVESTMENT then investment is not allowed for anyone
// if a mutual fund's fund_investment_status is Unspecified then investment is allowed for all investors
// send order to vendor will fail for such orders we are not disabling order creation
func (f *OneTimeOrderValidator) Validate(ctx context.Context, request *ValidationRequest) (error, string) {
	if request.Order.GetOrderSubType() != orderPb.OrderSubType_BUY_ONE_TIME_INVEST {
		return nil, ""
	}

	isin := request.FundInfo.GetIsinNumber()
	for _, val := range OneTimeDisabledISINs {
		if strings.Compare(val, isin) == 0 {
			return FundEligibilityValidationError, "fund not enabled for one-time invest orders"
		}
	}

	isOtpValidated, err := f.validateOTP(ctx, request)
	if err != nil {
		return OTPValidationError, fmt.Sprintf("error in validateOTP, err: %v", err)
	}

	if !isOtpValidated {
		return OTPValidationError, fmt.Sprintf("OTP not validated, err: %v", err)
	}

	return nil, ""

}

func (t *OneTimeOrderValidator) validateOTP(ctx context.Context, request *ValidationRequest) (bool, error) {

	if !t.conf.Flags().EnableOTPVerificationForOneTimeBuyOrder() {
		return true, nil
	}

	res, err := t.investmentAuthClient.GetAuthAttempt(ctx, &authpb.GetAuthAttemptRequest{Id: request.Order.GetAuthId()})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return false, te
	}

	if res.GetAuthAttempt().AuthStatus == authpb.AuthStatus_AUTH_SUCCESS {
		return true, nil
	}
	return false, nil
}
