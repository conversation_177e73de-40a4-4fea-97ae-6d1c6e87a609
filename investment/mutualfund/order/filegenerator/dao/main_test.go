package dao

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/investment/test"
	"github.com/epifi/be-common/pkg/idgen"
)

func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, db, teardown := test.InitTestServer()

	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	entityFileMapperCrdb := NewEntityFileMapperCrdb(db, domainIdGenerator)
	entityFileMapperTS = newEntityFileMapperTestSuite(db, conf, entityFileMapperCrdb)

	fileGenerationAttemptCrdb := NewFileGenerationAttemptCrdb(db)
	fileGenerationAttempTS = newFileDetailsTestSuite(db, conf, fileGenerationAttemptCrdb)

	fileSequenceGeneratorCrdb := NewFileSequenceGeneratorCrdb(db, domainIdGenerator)
	fileSequenceGeneratorTS = newFileSequenceGeneratorTestSuite(db, conf, fileSequenceGeneratorCrdb)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
