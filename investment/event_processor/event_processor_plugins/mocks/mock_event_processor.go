// Code generated by MockGen. DO NOT EDIT.
// Source: ./event_processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	comms "github.com/epifi/gamma/api/comms"
	event_processor "github.com/epifi/gamma/api/investment/event_processor"
	config "github.com/epifi/gamma/investment/config"
	event_processor_plugins "github.com/epifi/gamma/investment/event_processor/event_processor_plugins"
	gomock "github.com/golang/mock/gomock"
)

// MockEventProcessorPlugin is a mock of EventProcessorPlugin interface.
type MockEventProcessorPlugin struct {
	ctrl     *gomock.Controller
	recorder *MockEventProcessorPluginMockRecorder
}

// MockEventProcessorPluginMockRecorder is the mock recorder for MockEventProcessorPlugin.
type MockEventProcessorPluginMockRecorder struct {
	mock *MockEventProcessorPlugin
}

// NewMockEventProcessorPlugin creates a new mock instance.
func NewMockEventProcessorPlugin(ctrl *gomock.Controller) *MockEventProcessorPlugin {
	mock := &MockEventProcessorPlugin{ctrl: ctrl}
	mock.recorder = &MockEventProcessorPluginMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventProcessorPlugin) EXPECT() *MockEventProcessorPluginMockRecorder {
	return m.recorder
}

// GetJourneyStateForEvent mocks base method.
func (m *MockEventProcessorPlugin) GetJourneyStateForEvent(ctx context.Context, actorId string, event event_processor.InvestmentEvent, journeyType event_processor.JourneyType, eventProps map[string]string) (event_processor.JourneyState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJourneyStateForEvent", ctx, actorId, event, journeyType, eventProps)
	ret0, _ := ret[0].(event_processor.JourneyState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetJourneyStateForEvent indicates an expected call of GetJourneyStateForEvent.
func (mr *MockEventProcessorPluginMockRecorder) GetJourneyStateForEvent(ctx, actorId, event, journeyType, eventProps interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJourneyStateForEvent", reflect.TypeOf((*MockEventProcessorPlugin)(nil).GetJourneyStateForEvent), ctx, actorId, event, journeyType, eventProps)
}

// GetJourneyStatePriority mocks base method.
func (m *MockEventProcessorPlugin) GetJourneyStatePriority(journeyType event_processor.JourneyType, js1 event_processor.JourneyState) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJourneyStatePriority", journeyType, js1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetJourneyStatePriority indicates an expected call of GetJourneyStatePriority.
func (mr *MockEventProcessorPluginMockRecorder) GetJourneyStatePriority(journeyType, js1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJourneyStatePriority", reflect.TypeOf((*MockEventProcessorPlugin)(nil).GetJourneyStatePriority), journeyType, js1)
}

// GetNotificationContent mocks base method.
func (m *MockEventProcessorPlugin) GetNotificationContent(ctx context.Context, actorId string, notificationTemplate *config.EventBasedNotificationTemplate, campaignName comms.CampaignName, eventProps map[string]string) (*event_processor_plugins.NotificationContent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNotificationContent", ctx, actorId, notificationTemplate, campaignName, eventProps)
	ret0, _ := ret[0].(*event_processor_plugins.NotificationContent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNotificationContent indicates an expected call of GetNotificationContent.
func (mr *MockEventProcessorPluginMockRecorder) GetNotificationContent(ctx, actorId, notificationTemplate, campaignName, eventProps interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNotificationContent", reflect.TypeOf((*MockEventProcessorPlugin)(nil).GetNotificationContent), ctx, actorId, notificationTemplate, campaignName, eventProps)
}

// GetUserJourneyType mocks base method.
func (m *MockEventProcessorPlugin) GetUserJourneyType(ctx context.Context, actorId string) (event_processor.JourneyType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserJourneyType", ctx, actorId)
	ret0, _ := ret[0].(event_processor.JourneyType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserJourneyType indicates an expected call of GetUserJourneyType.
func (mr *MockEventProcessorPluginMockRecorder) GetUserJourneyType(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserJourneyType", reflect.TypeOf((*MockEventProcessorPlugin)(nil).GetUserJourneyType), ctx, actorId)
}

// IsJourneyComplete mocks base method.
func (m *MockEventProcessorPlugin) IsJourneyComplete(ctx context.Context, journey *event_processor.UserJourney) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsJourneyComplete", ctx, journey)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsJourneyComplete indicates an expected call of IsJourneyComplete.
func (mr *MockEventProcessorPluginMockRecorder) IsJourneyComplete(ctx, journey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsJourneyComplete", reflect.TypeOf((*MockEventProcessorPlugin)(nil).IsJourneyComplete), ctx, journey)
}
