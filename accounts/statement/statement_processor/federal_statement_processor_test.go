package statement_processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	bcPb "github.com/epifi/gamma/api/bankcust"
	bcMocks "github.com/epifi/gamma/api/bankcust/mocks"
	depositMock "github.com/epifi/gamma/api/deposit/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	vgAccountsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgAccountsMock "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts/mocks"
	"github.com/epifi/be-common/pkg/datetime"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
)

type mockFederalStatementProcessor struct {
	mockVgAccountClient *vgAccountsMock.MockAccountsClient
	mockDepositClient   *depositMock.MockDepositClient
	mockSavingsClient   *savingsMocks.MockSavingsClient
	mockActorClient     *actorMocks.MockActorClient
	mockUserClient      *userMocks.MockUsersClient
	mockBcClient        *bcMocks.MockBankCustomerServiceClient
	mockAuthClient      *authMocks.MockAuthClient
}

var (
	fromDate = &date.Date{
		Year:  2023,
		Month: 04,
		Day:   11,
	}

	toDate = &date.Date{
		Year:  2023,
		Month: 04,
		Day:   15,
	}

	fromDateForStatementApi = &date.Date{
		Year:  2023,
		Month: 04,
		Day:   14,
	}

	accountNumber = "*********"

	accountStatementFromDrStatementApi = &vgAccountsPb.GetAccountStatementResponse{
		Status: rpc.StatusOk(),
		TransactionData: []*vgAccountsPb.TransactionV1{
			{
				TransactionTimestamp: &timestamp.Timestamp{
					Seconds: **********,
				},
				TransactionId: "123456",
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        600,
				},
				TransactionType: vgAccountsPb.TransactionType_CREDIT,
				AccountNumber:   accountNumber,
				DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
					CbsId: "123456",
					TxnTime: &timestamp.Timestamp{
						Seconds: **********,
					},
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        600,
					},
				},
			},
			{
				TransactionTimestamp: &timestamp.Timestamp{
					Seconds: **********,
				},
				TransactionId: "12345",
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        100,
				},
				TransactionType: vgAccountsPb.TransactionType_CREDIT,
				AccountNumber:   accountNumber,
				DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
					CbsId: "12345",
					TxnTime: &timestamp.Timestamp{
						Seconds: **********,
					},
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
				},
			},
			{
				TransactionTimestamp: &timestamp.Timestamp{
					Seconds: **********,
				},
				TransactionId: "1234567",
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        300,
				},
				TransactionType: vgAccountsPb.TransactionType_CREDIT,
				AccountNumber:   accountNumber,
				DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
					CbsId: "1234567",
					TxnTime: &timestamp.Timestamp{
						Seconds: **********,
					},
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        300,
					},
				},
			},
		},
	}

	transactionData = []*vgAccountsPb.TransactionV1{
		{
			TransactionTimestamp: &timestamp.Timestamp{
				Seconds: **********,
			},
			TransactionId: "1234567",
			Amount: &money.Money{
				CurrencyCode: "INR",
				Units:        300,
			},
			TransactionType: vgAccountsPb.TransactionType_CREDIT,
			DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
				CbsId: "1234567",
				TxnTime: &timestamp.Timestamp{
					Seconds: **********,
				},
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        300,
				},
			},
		},
		{
			TransactionTimestamp: &timestamp.Timestamp{
				Seconds: **********,
			},
			TransactionId: "123456",
			Amount: &money.Money{
				CurrencyCode: "INR",
				Units:        600,
			},
			TransactionType: vgAccountsPb.TransactionType_CREDIT,
			DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
				CbsId: "123456",
				TxnTime: &timestamp.Timestamp{
					Seconds: **********,
				},
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        600,
				},
			},
		},
	}

	accountStatementFromMinStatementApi = &vgAccountsPb.GetMiniStatementResponse{
		Status:          rpc.StatusOk(),
		TransactionData: transactionData,
	}

	accountStatementFromMinStatementApi1 = &vgAccountsPb.GetMiniStatementResponse{
		Status: rpc.StatusOk(),
		TransactionData: []*vgAccountsPb.TransactionV1{
			{
				TransactionTimestamp: &timestamp.Timestamp{
					Seconds: **********,
				},
				TransactionId: "123",
				Amount: &money.Money{
					CurrencyCode: "INR",
					Units:        500,
				},
				TransactionType: vgAccountsPb.TransactionType_CREDIT,
				DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
					CbsId: "123",
					TxnTime: &timestamp.Timestamp{
						Seconds: **********,
					},
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				},
			},
		},
	}

	accountStatementFromRealTimeApi = &vgAccountsPb.GetAccountStatementResponse{
		Status:          rpc.StatusOk(),
		TransactionData: transactionData,
		HasMoreRows:     false,
		PageNumber:      4,
	}
)

func TestFederalStatementProcessor_GetStatementFromVendor(t *testing.T) {
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()

	mockVgAccountClient := vgAccountsMock.NewMockAccountsClient(ctr)
	mockDepositClient := depositMock.NewMockDepositClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockBcClient := bcMocks.NewMockBankCustomerServiceClient(ctr)
	mockAuthClient := authMocks.NewMockAuthClient(ctr)

	mockProcessor := &mockFederalStatementProcessor{
		mockVgAccountClient: mockVgAccountClient,
		mockDepositClient:   mockDepositClient,
		mockSavingsClient:   mockSavingsClient,
		mockActorClient:     mockActorClient,
		mockUserClient:      mockUserClient,
		mockBcClient:        mockBcClient,
		mockAuthClient:      mockAuthClient,
	}
	processor := NewFederalStatementProcessor(mockVgAccountClient, mockDepositClient, mockSavingsClient, mockActorClient, mockUserClient, mockBcClient, mockAuthClient)

	tests := []struct {
		name           string
		req            *GetStatementDataRequest
		setupMockCalls func()
		want           *GetStatementDataResponse
		wantErr        error
	}{
		{
			name: "got statement from combination of mini statement and Dr statement api",
			req: &GetStatementDataRequest{
				AccountId:   "account-id1",
				AccountType: accounts.Type_SAVINGS,
				FromDate:    fromDate,
				ToDate:      toDate,
			},
			setupMockCalls: func() {
				mockProcessor.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_Id{
						Id: "account-id1",
					},
				}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id:                   "account-id1",
					AccountNo:            accountNumber,
					IfscCode:             "SBI123",
					PrimaryAccountHolder: "entity-1",
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				}}, nil).AnyTimes()
				mockProcessor.mockVgAccountClient.EXPECT().GetMiniStatement(gomock.Any(), &vgAccountsPb.GetMiniStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      datetime.DateToTimestamp(fromDateForStatementApi, datetime.IST),
					ToDate:        datetime.DateToTimestamp(toDate, datetime.IST),
				}).Return(accountStatementFromMinStatementApi, nil)
				mockProcessor.mockVgAccountClient.EXPECT().GetAccountStatementByDrApi(gomock.Any(), &vgAccountsPb.GetAccountStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      fromDate,
					ToDate:        toDate,
					ApiType:       vgAccountsPb.GetAccountStatementRequest_API_TYPE_STATEMENT_DR_API,
				}).Return(accountStatementFromDrStatementApi, nil)
			},
			want: &GetStatementDataResponse{
				StatementData: []*vgAccountsPb.TransactionV1{
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "123456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        600,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "123456",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        600,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "12345",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						AccountNumber:   accountNumber,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "12345",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "1234567",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        300,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						AccountNumber:   accountNumber,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "1234567",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        300,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "123456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        600,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "123456",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        600,
							},
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "got statement from combination of Dr statement and Real time statement api",
			req: &GetStatementDataRequest{
				AccountId:   "account-id1",
				AccountType: accounts.Type_SAVINGS,
				FromDate:    fromDate,
				ToDate:      toDate,
			},
			setupMockCalls: func() {
				mockProcessor.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_Id{
						Id: "account-id1",
					},
				}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id:                   "account-id1",
					AccountNo:            accountNumber,
					IfscCode:             "SBI123",
					PrimaryAccountHolder: "entity-1",
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				}}, nil).AnyTimes()
				mockProcessor.mockVgAccountClient.EXPECT().GetMiniStatement(gomock.Any(), &vgAccountsPb.GetMiniStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      datetime.DateToTimestamp(fromDateForStatementApi, datetime.IST),
					ToDate:        datetime.DateToTimestamp(toDate, datetime.IST),
				}).Return(accountStatementFromMinStatementApi1, nil)
				mockProcessor.mockVgAccountClient.EXPECT().GetAccountStatementByDrApi(gomock.Any(), &vgAccountsPb.GetAccountStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      fromDate,
					ToDate:        toDate,
					ApiType:       vgAccountsPb.GetAccountStatementRequest_API_TYPE_STATEMENT_DR_API,
				}).Return(accountStatementFromDrStatementApi, nil)
				mockProcessor.mockVgAccountClient.EXPECT().GetAccountStatement(gomock.Any(), &vgAccountsPb.GetAccountStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					DeviceId:      "device-1",
					DeviceToken:   "token-1",
					CustomerId:    "cust-1",
					AccountNumber: accountNumber,
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					FromDate:   fromDateForStatementApi,
					ToDate:     toDate,
					PageNumber: 1,
				}).Return(accountStatementFromRealTimeApi, nil)
				mockProcessor.mockActorClient.EXPECT().GetActorByEntityId(gomock.Any(), &actorPb.GetActorByEntityIdRequest{
					Type:     types.Actor_USER,
					EntityId: "entity-1",
				}).Return(&actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-1",
						Type:     types.Actor_USER,
						EntityId: "entity-1",
					},
				}, nil)
				mockProcessor.mockUserClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: "entity-1"},
				}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Id: "entity-1",
						Profile: &userPb.Profile{
							PhoneNumber: &commontypes.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mockProcessor.mockBcClient.EXPECT().GetBankCustomer(gomock.Any(), &bcPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bcPb.GetBankCustomerRequest_UserId{UserId: "entity-1"},
				}).Return(&bcPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bcPb.BankCustomer{
						VendorCustomerId: "cust-1",
					},
				}, nil)
				mockProcessor.mockAuthClient.EXPECT().GetDeviceAuth(gomock.Any(), &authPb.GetDeviceAuthRequest{
					ActorId: "actor-1",
				}).Return(&authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-1",
					DeviceToken: "token-1",
					Device:      &commontypes.Device{DeviceId: "device-1"},
				}, nil)
			},
			want: &GetStatementDataResponse{
				StatementData: []*vgAccountsPb.TransactionV1{
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "123456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        600,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "123456",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        600,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "12345",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						AccountNumber:   accountNumber,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "12345",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "1234567",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        300,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						AccountNumber:   accountNumber,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "1234567",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        300,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "123456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        600,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "123456",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        600,
							},
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "record not found from mini statement, only send dr statement response",
			req: &GetStatementDataRequest{
				AccountId:   "account-id1",
				AccountType: accounts.Type_SAVINGS,
				FromDate:    fromDate,
				ToDate:      toDate,
			},
			setupMockCalls: func() {
				mockProcessor.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_Id{
						Id: "account-id1",
					},
				}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id:                   "account-id1",
					AccountNo:            accountNumber,
					IfscCode:             "SBI123",
					PrimaryAccountHolder: "entity-1",
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				}}, nil).AnyTimes()
				mockProcessor.mockVgAccountClient.EXPECT().GetMiniStatement(gomock.Any(), &vgAccountsPb.GetMiniStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      datetime.DateToTimestamp(fromDateForStatementApi, datetime.IST),
					ToDate:        datetime.DateToTimestamp(toDate, datetime.IST),
				}).Return(&vgAccountsPb.GetMiniStatementResponse{
					Status:          rpc.StatusRecordNotFound(),
					TransactionData: nil,
				}, nil)
				mockProcessor.mockVgAccountClient.EXPECT().GetAccountStatementByDrApi(gomock.Any(), &vgAccountsPb.GetAccountStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      fromDate,
					ToDate:        toDate,
					ApiType:       vgAccountsPb.GetAccountStatementRequest_API_TYPE_STATEMENT_DR_API,
				}).Return(accountStatementFromDrStatementApi, nil)
			},
			want: &GetStatementDataResponse{
				StatementData: []*vgAccountsPb.TransactionV1{
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "123456",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        600,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "123456",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        600,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "12345",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						AccountNumber:   accountNumber,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "12345",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
						},
					},
					{
						TransactionTimestamp: &timestamp.Timestamp{
							Seconds: **********,
						},
						TransactionId: "1234567",
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        300,
						},
						TransactionType: vgAccountsPb.TransactionType_CREDIT,
						AccountNumber:   accountNumber,
						DedupeId: &vgAccountsPb.StatementTransactionDedupeId{
							CbsId: "1234567",
							TxnTime: &timestamp.Timestamp{
								Seconds: **********,
							},
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        300,
							},
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "record not found from Dr statement api, send only mini statement response",
			req: &GetStatementDataRequest{
				AccountId:   "account-id1",
				AccountType: accounts.Type_SAVINGS,
				FromDate:    fromDate,
				ToDate:      toDate,
			},
			setupMockCalls: func() {
				mockProcessor.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_Id{
						Id: "account-id1",
					},
				}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{
					Id:                   "account-id1",
					AccountNo:            accountNumber,
					IfscCode:             "SBI123",
					PrimaryAccountHolder: "entity-1",
					PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
				}}, nil).AnyTimes()
				mockProcessor.mockVgAccountClient.EXPECT().GetMiniStatement(gomock.Any(), &vgAccountsPb.GetMiniStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      datetime.DateToTimestamp(fromDateForStatementApi, datetime.IST),
					ToDate:        datetime.DateToTimestamp(toDate, datetime.IST),
				}).Return(accountStatementFromMinStatementApi, nil)
				mockProcessor.mockVgAccountClient.EXPECT().GetAccountStatementByDrApi(gomock.Any(), &vgAccountsPb.GetAccountStatementRequest{
					Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					AccountNumber: accountNumber,
					FromDate:      fromDate,
					ToDate:        toDate,
					ApiType:       vgAccountsPb.GetAccountStatementRequest_API_TYPE_STATEMENT_DR_API,
				}).Return(&vgAccountsPb.GetAccountStatementResponse{
					Status:          rpc.StatusRecordNotFound(),
					TransactionData: nil,
				}, nil)
			},
			want: &GetStatementDataResponse{
				StatementData: transactionData,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := processor.GetStatementData(context.Background(), tt.req)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetStatementFromVendor() error = %v, wantErr %v", err, tt.wantErr)
			}

			if !isDataSame(got, tt.want) {
				t.Errorf("GetStatementFromVendor() got = %v, want %v", got, tt.want)
			}
		})
	}

}

func isDataSame(got, want *GetStatementDataResponse) bool {
	if got == nil && want == nil {
		return true
	}

	if got == nil || want == nil {
		return false
	}

	for iterator := range got.StatementData {
		id1, _ := got.StatementData[iterator].GetDedupeId().GetId()
		id2, _ := want.StatementData[iterator].GetDedupeId().GetId()
		if id1 != id2 {
			return false
		}
	}

	return true
}
