package executeRPNoAuth

import (
	"fmt"

	"github.com/google/wire"

	rpPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/recurringpayment/internal"
)

var executeRecurringPaymentNoAuthV1WireSet = wire.NewSet(
	NewStandingInstructionProcessor, NewUPIMandatesProcessor,
)

var ExecuteRecurringPaymentNoAuthFactoryWireSet = wire.NewSet(
	executeRecurringPaymentNoAuthV1WireSet, wire.NewSet(NewExecuteRecurringPaymentNoAuthFactoryImpl, wire.Bind(new(ExecuteRecurringPaymentNoAuthFactory), new(*ExecuteRecurringPaymentNoAuthFactoryImpl))),
)

var (
	_ internal.ExecuteRecurringPaymentNoAuthProcessorV1 = &StandingInstructionProcessor{}
	_ internal.ExecuteRecurringPaymentNoAuthProcessorV1 = &UPIMandatesProcessor{}
)

type ExecuteRecurringPaymentNoAuthFactory interface {
	GetProcessorImpl(paymentType rpPb.RecurringPaymentType) (internal.ExecuteRecurringPaymentNoAuthProcessorV1, error)
}

type ExecuteRecurringPaymentNoAuthFactoryImpl struct {
	StandingInstructionExecutor internal.ExecuteRecurringPaymentNoAuthProcessorV1
	UPIMandatesExecutor         internal.ExecuteRecurringPaymentNoAuthProcessorV1
}

func NewExecuteRecurringPaymentNoAuthFactoryImpl(
	standingInstructionExecutor *StandingInstructionProcessor,
	upiMandatesExecutor *UPIMandatesProcessor,
) *ExecuteRecurringPaymentNoAuthFactoryImpl {
	return &ExecuteRecurringPaymentNoAuthFactoryImpl{
		StandingInstructionExecutor: standingInstructionExecutor,
		UPIMandatesExecutor:         upiMandatesExecutor,
	}
}

func (a *ExecuteRecurringPaymentNoAuthFactoryImpl) GetProcessorImpl(paymentType rpPb.RecurringPaymentType) (internal.ExecuteRecurringPaymentNoAuthProcessorV1, error) {
	switch paymentType {
	case rpPb.RecurringPaymentType_STANDING_INSTRUCTION:
		return a.StandingInstructionExecutor, nil
	case rpPb.RecurringPaymentType_UPI_MANDATES:
		return a.UPIMandatesExecutor, nil
	default:
		return nil, fmt.Errorf("factory implementation not found for %s. unsupported recurring payment type", paymentType)
	}
}
