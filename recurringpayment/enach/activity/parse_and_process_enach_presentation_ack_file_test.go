package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachActivityPb "github.com/epifi/gamma/api/recurringpayment/enach/activity"
	enachDaoMocks "github.com/epifi/gamma/recurringpayment/enach/dao/mocks"
)

func TestProcessor_ParseAndProcessEnachPresentationAckFile(t *testing.T) {
	const (
		s3FilePath           = "presentation/04052023/NACH_DR_04052023_UTILITY_CODE_EpifiTechnologies_001-ACK.xml"
		enachMandateActionId = "enach-mandate-action-id-1"
	)
	var (
		parseEnachPresentationResponseFileReq = &enachActivityPb.ParseAndProcessEnachPresentationAckFileRequest{
			PresentationAckFileS3Path: s3FilePath,
		}

		failedEnachExecutionActionIdsList []string

		ackResponse1 = []byte(
			`<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <FIToFIPmtStsRpt>
    <GrpHdr>
      <MsgId>P230207001541083KC</MsgId>
      <CreDtTm>2023-02-07</CreDtTm>
      <InstdAgt>
        <FinInstnId>
          <ClrSysMmbId>
            <MmbId>FDRL0000379</MmbId>
          </ClrSysMmbId>
        </FinInstnId>
      </InstdAgt>
    </GrpHdr>
    <OrgnlGrpInfAndSts>
      <OrgnlMsgId>P230207001541083KC</OrgnlMsgId>
      <OrgnlMsgNmId>pacs.003.001.02</OrgnlMsgNmId>
      <GrpSts>ACCP</GrpSts>
    </OrgnlGrpInfAndSts>
  </FIToFIPmtStsRpt>
</Document>`)

		ackResponse2 = []byte(`<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <FIToFIPmtStsRpt>
    <OrgnlGrpInfAndSts>
      <OrgnlMsgId>P230207001541083KC</OrgnlMsgId>
      <OrgnlMsgNmId>pacs.003.001.02</OrgnlMsgNmId>
      <GrpSts>PART</GrpSts>
    </OrgnlGrpInfAndSts>
  </FIToFIPmtStsRpt>
</Document>`)

		ackResponse3 = []byte(`<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <FIToFIPmtStsRpt>
    <OrgnlGrpInfAndSts>
      <OrgnlMsgId>P230207001541083KC</OrgnlMsgId>
      <OrgnlMsgNmId>pacs.003.001.02</OrgnlMsgNmId>
      <GrpSts>RJCT</GrpSts>
    </OrgnlGrpInfAndSts>
  </FIToFIPmtStsRpt>
</Document>`)

		ackResponse4 = []byte(`
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.03" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <FIToFIPmtStsRpt>
    <OrgnlGrpInfAndSts>
      <OrgnlMsgId>P230207001541083KC</OrgnlMsgId>
      <OrgnlMsgNmId>pacs.003.001.02</OrgnlMsgNmId>
      <GrpSts>XYZ</GrpSts>
    </OrgnlGrpInfAndSts>
  </FIToFIPmtStsRpt>
</Document>`)
	)
	failedEnachExecutionActionIdsList = append(failedEnachExecutionActionIdsList, enachMandateActionId)

	enachMandateAction := &enachPb.EnachMandateAction{
		Id: enachMandateActionId,
	}

	tests := []struct {
		name       string
		req        *enachActivityPb.ParseAndProcessEnachPresentationAckFileRequest
		setupMocks func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao)
		want       *enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "should return retryable error if unable to get ack file bytes from s3",
			req:  parseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), s3FilePath).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should retrun non-retryable error if unable to parse the xml ack file",
			req:  parseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), s3FilePath).Return([]byte(``), nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return empty list of enach mandates if ack file GrpSts is ACCP",
			req:  parseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), s3FilePath).Return(ackResponse1, nil)
			},
			want: &enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse{
				FailedEnachExecutionActionIds: []string{},
			},
			wantErr: false,
		},
		{
			name: "should return empty list of enach mandates and non-retryable error if ack file GrpSts is PART",
			req:  parseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), s3FilePath).Return(ackResponse2, nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return non-retryable error if ack file GrpSts not in PART, ACCP or RJCT",
			req:  parseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), s3FilePath).Return(ackResponse4, nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "should return retryable  error if ack file GrpSts is RJCT and unable to fetch enach mandates actions from vendor request-id",
			req:  parseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), s3FilePath).Return(ackResponse3, nil)
				enachMandateActionDao.EXPECT().GetByVendorBatchRequestId(gomock.Any(), "P230207001541083KC").Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should return list of enach mandate actions if ack file GrpSts is RJCT and successfully fetches enach mandate actions by vendor request id",
			req:  parseEnachPresentationResponseFileReq,
			setupMocks: func(s3Client *s3Mocks.MockS3Client, enachMandateActionDao *enachDaoMocks.MockEnachMandateActionDao) {
				s3Client.EXPECT().Read(gomock.Any(), s3FilePath).Return(ackResponse3, nil)
				enachMandateActionDao.EXPECT().GetByVendorBatchRequestId(gomock.Any(), "P230207001541083KC").Return([]*enachPb.EnachMandateAction{enachMandateAction}, nil)
			},
			want: &enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse{
				FailedEnachExecutionActionIds: failedEnachExecutionActionIdsList,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()

			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)

			tt.setupMocks(md.s3Client, md.enachMandateActionDao)

			result := &enachActivityPb.ParseAndProcessEnachPresentationAckFileResponse{}
			got, err := env.ExecuteActivity(rpNs.ParseAndProcessEnachPresentationAckFile, tt.req)

			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ParseEnachPresentationAckFile() error = %v failed to fetch type value from convertible", getErr)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ParseEnachPresentationAckFile() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ParseEnachPresentationAckFile() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ParseEnachPresentationAckFile() got = %v, want %v", result, tt.want)
				return
			}
		})
	}

}
