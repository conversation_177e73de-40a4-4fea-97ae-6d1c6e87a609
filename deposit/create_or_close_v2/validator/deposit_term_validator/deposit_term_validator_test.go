package deposit_term_validator_test

import (
	"context"
	"errors"
	"testing"

	accountPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	types "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/gamma/deposit/create_or_close_v2/validator"
	"github.com/epifi/gamma/deposit/create_or_close_v2/validator/deposit_term_validator"
)

func TestDepositTermValidator_Validate(t *testing.T) {
	svc := deposit_term_validator.NewDepositTermValidator(testConf)
	type args struct {
		ctx context.Context
		req *validator.ValidationRequest
	}

	tests := []struct {
		name           string
		args           args
		setupMockCalls func(args)
		wantErr        error
	}{
		{
			name: "with term empty",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id", NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
						{
							NomineeId:       "nominee-1",
							PercentageShare: "75",
						},
						{
							NomineeId:       "nominee-2",
							PercentageShare: "25",
						},
					}}},
				},
			},
			wantErr: deposit_term_validator.InvalidDepositTermError,
		},
		{
			name: "if both day and month is zero",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   0,
							Months: 0,
						},
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: deposit_term_validator.InvalidDepositTermError,
		},
		{
			name: "if deposit fd is more than max",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   testConf.MaxFDDurationInDays() + 3,
							Months: 0,
						},
						Type: accountPb.Type_FIXED_DEPOSIT,
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: deposit_term_validator.InvalidDepositTermError,
		},
		{
			name: "if deposit fd is less than max",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   testConf.MaxFDDurationInDays() - 3,
							Months: 0,
						},
						Type: accountPb.Type_FIXED_DEPOSIT,
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: nil,
		},
		{
			name: "if deposit fd is less than max",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   testConf.MaxFDDurationInDays() - 3,
							Months: 0,
						},
						Type: accountPb.Type_FIXED_DEPOSIT,
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: nil,
		},
		{
			name: "if deposit sd is less than max",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   testConf.MaxSDDurationInDays() - 3,
							Months: 0,
						},
						Type: accountPb.Type_SMART_DEPOSIT,
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: nil,
		},
		{
			name: "if deposit sd is more than max",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   testConf.MaxSDDurationInDays() + 3,
							Months: 0,
						},
						Type: accountPb.Type_SMART_DEPOSIT,
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: deposit_term_validator.InvalidDepositTermError,
		},
		{
			name: "if deposit sd is valid",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   31,
							Months: 0,
						},
						Type: accountPb.Type_SMART_DEPOSIT,
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: nil,
		},
		{
			name: "if it is doesnt fall in condition",
			args: args{
				ctx: context.Background(),
				req: &validator.ValidationRequest{
					DepositInfo: &depositPb.DepositInfo{ActorId: "actor-id",
						Term: &types.DepositTerm{
							Days:   72,
							Months: 0,
						},
						Type: accountPb.Type_CURRENT,
						NomineeDetails: &depositPb.DepositNomineeDetails{NomineeInfoList: []*depositPb.DepositNomineeDetails_DepositNomineeInfo{
							{
								NomineeId:       "nominee-1",
								PercentageShare: "75",
							},
							{
								NomineeId:       "nominee-2",
								PercentageShare: "25",
							},
						}}},
				},
			},
			wantErr: deposit_term_validator.InvalidDepositTermError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := svc.Validate(tt.args.ctx, tt.args.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
