package preclose_already_exist_validator_test

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/deposit/config/genconf"
	"github.com/epifi/gamma/deposit/test"
)

var (
	testConf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()

	// nolint: dogsled
	_, testConf, _, teardown = test.InitTestServer()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)

}
