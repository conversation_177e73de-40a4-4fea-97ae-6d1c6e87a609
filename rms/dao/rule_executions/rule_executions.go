package rule_executions

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	rmsPkg "github.com/epifi/gamma/pkg/rms"
	"github.com/epifi/gamma/rms/dao"

	"github.com/google/uuid"
	"github.com/google/wire"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/logger"

	execPb "github.com/epifi/gamma/api/rms/command_processor"
	"github.com/epifi/gamma/rms/dao/model"
)

var RuleExecutionsDaoWireSet = wire.NewSet(NewRuleExecutionsPgDb, wire.Bind(new(dao.RuleExecutionsDao), new(*RuleExecutionsPgDb)))

type RuleExecutionsPgDb struct {
	db                                              *gormv2.DB
	executionsCountMaterializedViewRefreshScheduler *rmsPkg.MaterializedViewRefreshScheduler
	rand                                            *rand.Rand
}

func NewRuleExecutionsPgDb(db *gorm.DB, executionsCountMaterializedViewRefreshScheduler *rmsPkg.MaterializedViewRefreshScheduler) *RuleExecutionsPgDb {
	r := &RuleExecutionsPgDb{
		db: db,
		executionsCountMaterializedViewRefreshScheduler: executionsCountMaterializedViewRefreshScheduler,
		// nolint:gosec
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
	//nocustomlint:goroutine
	go rulesCountMaterialisedViewRefresherOnce.Do(r.RefreshExecutionCountMaterializedViewSchedule)
	return r
}

var rulesCountMaterialisedViewRefresherOnce = &sync.Once{}

const batchSize int = 100

func (r *RuleExecutionsPgDb) Create(ctx context.Context, execution *execPb.RuleExecution) (*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "Create", time.Now())
	if execution == nil {
		return nil, fmt.Errorf("Cannot create rule execution, request has nil reference ")
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	ruleExecutionModel := r.toModel(execution)

	err := db.Transaction(func(tx *gormv2.DB) error {
		// adding a new row for rule executions to database
		res := tx.Create(ruleExecutionModel)
		if res.Error != nil {
			logger.Error(ctx, "Error while creating user rule execution ", zap.String(logger.SUBSCRIPTION_ID, execution.SubscriptionId))
			return res.Error
		}

		if ruleExecutionModel.State == execPb.RuleExecutionState_ACTION_PROCESSING_SUCCESS {
			if err := tx.Model(&model.SubscriptionRuntimeInfo{Id: execution.GetSubscriptionId()}).Updates(&model.SubscriptionRuntimeInfo{LastExecutedAt: sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			}}).Error; err != nil {
				logger.Error(ctx, "Error while updating last updated at for subscription ", zap.String(logger.SUBSCRIPTION_ID, execution.SubscriptionId))
				return res.Error
			}
		}
		return nil
	})
	if err != nil {
		logger.Error(ctx, "Failed to create rule execution", zap.Error(err))
		return nil, err
	}

	return r.toProto(ruleExecutionModel), nil
}

func (r *RuleExecutionsPgDb) CreateBulk(ctx context.Context, executions []*execPb.RuleExecution) error {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "CreateBulk", time.Now())
	if executions == nil {
		return nil
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	var models []*model.RuleExecutions
	for _, execution := range executions {
		models = append(models, r.toModel(execution))
	}

	// adding new rows for rule executions to database in batches of 100 records per query
	i := 0
	for ; i < len(models)/batchSize; i++ {
		res := db.Create(models[i*batchSize : (i+1)*batchSize])
		if res.Error != nil {
			logger.Error(ctx, "Error while creating user rule executions ", zap.Error(res.Error))
			return res.Error
		}
	}
	// if there are some pending records to be inserted
	if len(models) > i*batchSize {
		res := db.Create(models[i*batchSize:])
		if res.Error != nil {
			logger.Error(ctx, "Error while creating user rule executions ", zap.Error(res.Error))
			return res.Error
		}
	}

	return nil
}

func (r *RuleExecutionsPgDb) UpdateState(ctx context.Context, executionId string, state execPb.RuleExecutionState, description string) (*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "UpdateState", time.Now())
	if _, err := uuid.Parse(executionId); err != nil || state == execPb.RuleExecutionState_RULE_EXECUTION_STATE_UNSPECIFIED {
		return nil, fmt.Errorf("Cannot update rule executions, execution id is not a valid uuid or state is unspecified ")
	}

	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	executionModel := &model.RuleExecutions{
		State:       state,
		Description: description,
	}
	var exec *model.RuleExecutions

	err := db.Transaction(func(tx *gormv2.DB) error {
		// updating state and description
		if err := tx.Model(&model.RuleExecutions{Id: executionId}).Updates(executionModel).Take(&exec).Error; err != nil {
			logger.Error(ctx, "Unable to update actor rule execution", zap.String(logger.EXECUTION_ID, executionId), zap.Error(err))
			return err
		}

		if executionModel.State == execPb.RuleExecutionState_ACTION_PROCESSING_SUCCESS {
			if err := tx.Model(&model.SubscriptionRuntimeInfo{Id: exec.RuleSubscriptionId}).Updates(&model.SubscriptionRuntimeInfo{LastExecutedAt: sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			}}).Error; err != nil {
				logger.Error(ctx, "Error while updating last updated at for subscription ", zap.String(logger.SUBSCRIPTION_ID, exec.RuleSubscriptionId))
				return err
			}
		}
		return nil
	})

	if err != nil {
		logger.Error(ctx, "Unable to update rule execution", zap.String(logger.EXECUTION_ID, executionId), zap.Error(err))
		return nil, err
	}

	return r.toProto(exec), nil
}

func (r *RuleExecutionsPgDb) GetById(ctx context.Context, executionId string) (*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetById", time.Now())
	if _, err := uuid.Parse(executionId); err != nil {
		return nil, fmt.Errorf("cannot get rule execution, execution id is not a valid uuid %v  error %w ", executionId, err)
	}

	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	execution := &model.RuleExecutions{}

	if err := db.Model(&model.RuleExecutions{}).
		Where(&model.RuleExecutions{
			Id: executionId,
		}).
		First(&execution).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch rule execution: %w", err)
	}

	return r.toProto(execution), nil
}

func (r *RuleExecutionsPgDb) GetByIds(ctx context.Context, executionIds []string) (map[string]*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetByIds", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	executions := make([]*model.RuleExecutions, 0)

	if err := db.Model(&model.RuleExecutions{}).
		Where("id in (?)", executionIds).
		Find(&executions).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch rule execution: %w", err)
	}

	executionProtos := make(map[string]*execPb.RuleExecution)
	for _, execution := range executions {
		executionProtos[execution.Id] = r.toProto(execution)
	}
	return executionProtos, nil
}

func (r *RuleExecutionsPgDb) GetClientRulesInitiatedExecutions(ctx context.Context, versionIds []string) (map[string][]*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetClientRulesInitiatedExecutions", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	executions := make([]*model.RuleExecutions, 0)

	if err := db.Model(&model.RuleExecutions{}).
		Where(" state = ? and subscription_version_id in (?) ", execPb.RuleExecutionState_CONDITION_EVALUATION_AT_CLIENT_PENDING.String(), versionIds).
		Find(&executions).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch rule execution: %w", err)
	}

	executionsProtoMap := make(map[string][]*execPb.RuleExecution)
	for _, execution := range executions {
		if _, ok := executionsProtoMap[execution.SubscriptionVersionId]; !ok {
			executionsProtoMap[execution.SubscriptionVersionId] = make([]*execPb.RuleExecution, 0)
		}
		executionsProtoMap[execution.SubscriptionVersionId] = append(executionsProtoMap[execution.SubscriptionVersionId], r.toProto(execution))
	}
	return executionsProtoMap, nil
}

// returns all executions of a particular rule, for an actor (i.e subscription)
func (r *RuleExecutionsPgDb) GetExecutionsBySubscriptionId(ctx context.Context, subscriptionId string) ([]*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetExecutionsBySubscriptionId", time.Now())
	if _, err := uuid.Parse(subscriptionId); err != nil {
		return nil, fmt.Errorf("cannot get rule executions, subscription id is not a valid uuid %v  error %w ", subscriptionId, err)
	}

	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	var executions []*model.RuleExecutions
	var executionsProto []*execPb.RuleExecution
	if err := db.Model(&model.RuleExecutions{}).
		Where(&model.RuleExecutions{
			RuleSubscriptionId: subscriptionId,
		}).
		Find(&executions).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch rule execution: %w", err)
	}

	for _, execution := range executions {
		executionsProto = append(executionsProto, r.toProto(execution))
	}

	return executionsProto, nil
}

// since on every GetClientMetricsAndPendingExecutions request from client, all the pending executions till that point are passed for condition evaluation to the user
// it can be assumed, that there is no successful/failed execution after a pending execution for zen mode
// which means, the first PENDING execution is the time from which user uninstalled the application
func (r *RuleExecutionsPgDb) GetFirstPendingClientRuleExecutions(ctx context.Context, subscriptionIds []string) (map[string]*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetFirstPendingClientRuleExecutions", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	var executions []*model.RuleExecutions
	if err := db.Raw("select * from rule_executions where (rule_subscription_id, created_at) in "+
		"(select rule_subscription_id, min(created_at) from rule_executions where state in (?) and rule_subscription_id in (?) group by rule_subscription_id)",
		[]string{execPb.RuleExecutionState_CONDITION_EVALUATION_AT_CLIENT_PENDING.String(), execPb.RuleExecutionState_CONDITION_EVALUATION_AT_CLIENT_QUEUED.String()},
		subscriptionIds).
		Find(&executions).Error; err != nil {
		logger.Error(ctx, "Failed to get latest subscriptions for provided subscriptionIds", zap.Error(err),
			zap.Strings(logger.SUBSCRIPTION_ID, subscriptionIds))
		return nil, err
	}

	execMap := make(map[string]*execPb.RuleExecution)
	for _, exec := range executions {
		execMap[exec.RuleSubscriptionId] = r.toProto(exec)
	}
	return execMap, nil
}

type RuleExecutionCount struct {
	RuleId string
	Count  int64
}

// returns execution count of a particular rule across actors
func (r *RuleExecutionsPgDb) GetExecutionsCountForRules(ctx context.Context, ruleIds []string) (map[string]int64, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetExecutionsCountForRules", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	var results []RuleExecutionCount
	err := db.Raw("select rule_id, count(*) from rule_executions, rule_subscriptions"+
		" where rule_executions.rule_subscription_id = rule_subscriptions.id and rule_executions.state = ? and rule_id in (?)"+
		"group by rule_id", execPb.RuleExecutionState_ACTION_PROCESSING_SUCCESS.String(), ruleIds).
		Scan(&results).
		Error

	if err != nil {
		return nil, fmt.Errorf("failed to fetch rule executions count: %w", err)
	}
	executionsCountMap := make(map[string]int64)
	for _, res := range results {
		executionsCountMap[res.RuleId] = res.Count
	}
	return executionsCountMap, nil
}

type RuleExecutionCountByNames struct {
	Name  string
	Count int64
}

// returns execution count of a particular rule across actors
// nolint:dupl
func (r *RuleExecutionsPgDb) GetExecutionsCountForRulesByNames(ctx context.Context, ruleNames []string) (map[string]int64, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetExecutionsCountForRulesByNames", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	results := make([]*RuleExecutionCountByNames, 0)
	err := db.Raw("select * from rule_executions_count_view where name in (?)", ruleNames).
		Scan(&results).
		Error

	if err != nil {
		return nil, fmt.Errorf("failed to fetch rule executions count: %w", err)
	}
	executionsCountMap := make(map[string]int64)
	for _, res := range results {
		executionsCountMap[res.Name] = res.Count
	}
	return executionsCountMap, nil
}

func (r *RuleExecutionsPgDb) GetExecutions(ctx context.Context, subscriptionId string, states []execPb.RuleExecutionState, actorId string,
	eventIds []string, createdAfter *time.Time, createdBefore *time.Time, limit int) ([]*execPb.RuleExecution, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetExecutions", time.Now())
	if subscriptionId == "" && actorId == "" {
		logger.Error(ctx, "expecting either subscriptionId or actorId to be non empty", zap.String(logger.SUBSCRIPTION_ID, subscriptionId))
		return nil, fmt.Errorf("expecting either subscriptionId or actorId to be non empty")
	}

	var execModels []*model.RuleExecutions
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	db = db.Model(&model.RuleExecutions{})
	if subscriptionId != "" {
		db = db.Where("rule_subscription_id = ?", subscriptionId)
	} else {
		db = db.Where("rule_subscription_id in (select id from subscription_runtime_infos where actor_id = ?)", actorId)
	}

	if len(states) > 0 {
		db = db.Where("state in (?)", states)
	}
	if len(eventIds) > 0 {
		db = db.Where("client_event_id in (?)", eventIds)
	}
	if createdAfter != nil {
		db = db.Where("created_at >= ?", createdAfter)
	}
	if createdBefore != nil {
		db = db.Where("created_at <= ?", createdBefore)
	}
	db = db.Order("created_at desc")
	if limit > int(dao.MaxPageSize) || limit == 0 {
		limit = int(dao.MaxPageSize)
	}
	db = db.Limit(limit)
	res := db.Find(&execModels)
	if res.Error != nil {
		logger.Error(ctx, "failed to get execs", zap.Error(res.Error))
		return nil, res.Error
	}

	execs := make([]*execPb.RuleExecution, 0, len(execModels))
	for _, exec := range execModels {
		execs = append(execs, r.toProto(exec))
	}
	return execs, nil
}

func (r *RuleExecutionsPgDb) ArchivePendingClientExecutions(ctx context.Context, subscriptionIds []string, reason string) error {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "ArchivePendingClientExecutions", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	updated := model.RuleExecutions{
		State:       execPb.RuleExecutionState_ARCHIVED_DUE_TO_NO_INTERNET_ACCESS_TO_APP,
		Description: reason,
	}
	// updating state and description
	if err := db.Model(&model.RuleExecutions{}).
		Where("state in (?) and rule_subscription_id in (?)", []string{execPb.RuleExecutionState_CONDITION_EVALUATION_AT_CLIENT_PENDING.String(),
			execPb.RuleExecutionState_CONDITION_EVALUATION_AT_CLIENT_QUEUED.String()}, subscriptionIds).Select([]string{model.StateColumnName, model.DescriptionColumnName}).
		Updates(updated).Error; err != nil {
		logger.Error(ctx, "Failed to archive rule execution", zap.Error(err))
		return err
	}
	return nil
}

func (r *RuleExecutionsPgDb) CheckEventIdempotency(ctx context.Context, subscriptionId, clientEventId, eventUniqueKey string) (bool, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "CheckEventIdempotency", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	var count int64
	if err := db.Model(&model.RuleExecutions{}).Where(&model.RuleExecutions{
		RuleSubscriptionId: subscriptionId,
		ClientEventId:      clientEventId,
		EventUniqueKey:     eventUniqueKey,
	}).Count(&count).Error; err != nil {
		logger.Error(ctx, "Failed to check idempotency for requested subscription event", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, subscriptionId),
			zap.String(logger.EVENT_ID, clientEventId), zap.String("uniqueKey", eventUniqueKey))
		return false, err
	}

	if count == 0 {
		return true, nil
	}
	return false, nil
}

func (r *RuleExecutionsPgDb) toModel(execution *execPb.RuleExecution) *model.RuleExecutions {
	return &model.RuleExecutions{
		Id:                    execution.ExecutionId,
		RuleSubscriptionId:    execution.SubscriptionId,
		ClientEventId:         execution.ClientEventId,
		State:                 execution.State,
		Description:           execution.Description,
		SubscriptionVersionId: execution.SubscriptionVersionId,
		EventUniqueKey:        execution.EventUniqueKey,
		BatchId:               execution.GetBatchId(),
	}
}

func (r *RuleExecutionsPgDb) toProto(execution *model.RuleExecutions) *execPb.RuleExecution {
	return &execPb.RuleExecution{
		ExecutionId:           execution.Id,
		SubscriptionId:        execution.RuleSubscriptionId,
		ClientEventId:         execution.ClientEventId,
		State:                 execution.State,
		Description:           execution.Description,
		CreatedAt:             timestamppb.New(execution.CreatedAt),
		UpdatedAt:             timestamppb.New(execution.UpdatedAt),
		SubscriptionVersionId: execution.SubscriptionVersionId,
		EventUniqueKey:        execution.EventUniqueKey,
		BatchId:               execution.BatchId,
	}
}

func (r *RuleExecutionsPgDb) RefreshExecutionCountMaterializedView(ctx context.Context, updateConcurrently bool) error {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "RefreshExecutionCountMaterializedView", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	concurrentRefreshQuery := "REFRESH MATERIALIZED VIEW CONCURRENTLY rule_executions_count_view"
	refreshQuery := "REFRESH MATERIALIZED VIEW rule_executions_count_view"
	var err error
	if updateConcurrently {
		err = db.Exec(concurrentRefreshQuery).Error
	} else {
		err = db.Exec(refreshQuery).Error
	}
	if err != nil {
		logger.Error(ctx, "Failed to refresh materialized view", zap.Error(err))
		return err
	}
	logger.Debug(ctx, "Executions count materialized view refreshed")
	return nil
}

func (r *RuleExecutionsPgDb) RefreshExecutionCountMaterializedViewSchedule() {
	defer logger.RecoverPanicAndError(context.Background())
	// nolint:staticcheck
	ch := time.Tick(time.Second * time.Duration(r.executionsCountMaterializedViewRefreshScheduler.RefreshInterval))
	for {
		func() {
			<-ch
			// wait for a random duration to avoid refreshing the materialised view at the same time from multiple
			// instances of FIT server.
			d := r.rand.Int63n(int64(r.executionsCountMaterializedViewRefreshScheduler.RandomiseWindow))
			time.Sleep(time.Second * time.Duration(d))
			ctx, cancelFn := context.WithTimeout(context.Background(), time.Minute)
			defer cancelFn()
			ctx = epificontext.WithTraceId(ctx, make(map[string][]string))
			logger.Info(ctx, "Running scheduler for rules count refresh")
			err := r.RefreshExecutionCountMaterializedView(ctx, true)
			logger.Info(ctx, "Trigger to refresh view completed", zap.Error(err))
		}()
	}
}

func (r *RuleExecutionsPgDb) GetExecutionMetrics(ctx context.Context, subId string) (*model.ExecutionMetrics, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetExecutionMetrics", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	var latestExecution *model.RuleExecutions
	var totalExecutions int64
	if err := db.Model(&model.RuleExecutions{}).Where(&model.RuleExecutions{RuleSubscriptionId: subId, State: execPb.RuleExecutionState_ACTION_PROCESSING_SUCCESS}).
		Order("updated_at desc").Count(&totalExecutions).Take(&latestExecution).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return &model.ExecutionMetrics{SubId: subId}, nil
		}
		logger.Error(ctx, "error in getting executions metrics for subscription", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, subId))
		return nil, err
	}
	return &model.ExecutionMetrics{
		SubId:           subId,
		TotalExecutions: totalExecutions,
		LastExecutedAt:  latestExecution.UpdatedAt,
	}, nil
}

func (r *RuleExecutionsPgDb) GetByFilters(ctx context.Context, pageSize uint32, pageToken *pagination.PageToken, filters ...storagev2.FilterOption) ([]*execPb.RuleExecution, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("rms/dao/rule_executions", "RuleExecutionsPgDb", "GetByFilters", time.Now())
	if pageSize < 1 {
		return nil, nil, errors.New("page size should be at least 1")
	}

	if pageSize > dao.MaxPageSize {
		return nil, nil, errors.New("page size should be at most " + fmt.Sprint(dao.MaxPageSize))
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	// Apply all provided filters and ignore dormant order status
	for _, opt := range filters {
		db = opt.ApplyInGorm(db)
	}
	db, err := pagination.AddPaginationOnGivenColumns(db, pageToken, pageSize, "rule_executions", "created_at", "id")
	if err != nil {
		return nil, nil, err
	}

	var ruleExecutionsModelList []*model.RuleExecutions
	resp := db.Find(&ruleExecutionsModelList)

	if resp.Error != nil {
		logger.Error(ctx, "Error when fetching list of rule executions", zap.Error(resp.Error))
		return nil, nil, resp.Error
	}

	if len(ruleExecutionsModelList) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	var pageCtxResp *rpc.PageContextResponse
	rows, res, err := pagination.NewPageCtxResp(pageToken, int(pageSize), model.RuleExecutionsModelRows(ruleExecutionsModelList))

	if err != nil {
		return nil, nil, err
	}

	ruleExecutionsModelList, ok := rows.(model.RuleExecutionsModelRows)
	if !ok {
		return nil, nil, fmt.Errorf("error in converting rows to RuleExecutionsModelRows")
	}

	pageCtxResp = res
	var ruleExecutionProtoList []*execPb.RuleExecution
	for _, ruleExecutions := range ruleExecutionsModelList {
		ruleExecutionProtoList = append(ruleExecutionProtoList, r.toProto(ruleExecutions))
	}
	return ruleExecutionProtoList, pageCtxResp, nil
}
