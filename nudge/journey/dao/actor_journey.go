package dao

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/pkg/errors"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"

	"github.com/epifi/gamma/nudge/journey/dao/model"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"gorm.io/gorm"

	journeyPb "github.com/epifi/gamma/api/nudge/journey"
)

type ActorJourneyDaoPostgres struct {
	db *gorm.DB
}

func NewActorJourneyDaoPostgres(db pkgTypes.NudgePGDB) *ActorJourneyDaoPostgres {
	return &ActorJourneyDaoPostgres{db: db}
}

var _ ActorJourneyDao = &ActorJourneyDaoPostgres{}

var actorJourneyColumnNameMap = map[journeyPb.ActorJourneyFieldMask]string{
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_ID:           "id",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_ACTOR_ID:     "actor_id",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_JOURNEY_ID:   "journey_id",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_STATUS:       "status",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_PROGRESS:     "progress",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_JOURNEY_TYPE: "journey_type",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_EXPIRE_AT:    "expire_at",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_CREATED_AT:   "created_at",
	journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_UPDATED_AT:   "updated_at",
}

// filter out fields which cannot be fetched or updated.
var actorJourneyFieldMaskFilter = func(actorJourneyFieldMask journeyPb.ActorJourneyFieldMask) bool {
	return actorJourneyFieldMask != journeyPb.ActorJourneyFieldMask_ACTOR_JOURNEY_FIELD_MASK_UNSPECIFIED
}

// BulkCreateActorJourney inserts actorJourneys in the table.
// This will fail if entry already exists for columns with unique indices.
func (a *ActorJourneyDaoPostgres) BulkCreateActorJourney(ctx context.Context, actorJourneys []*journeyPb.ActorJourney) ([]*journeyPb.ActorJourney, error) {
	defer metric_util.TrackDuration("nudge/journey/dao", "ActorJourneyDaoPostgres", "BulkCreateActorJourney", time.Now())
	if len(actorJourneys) == 0 {
		return actorJourneys, nil
	}
	for _, actorJourney := range actorJourneys {
		if actorJourney.GetActorId() == "" || actorJourney.GetJourneyId() == "" || actorJourney.GetStatus() == journeyPb.ActorJourneyStatus_ACTOR_JOURNEY_STATUS_UNSPECIFIED || actorJourney.GetProgress() == nil {
			return nil, fmt.Errorf("one of required fields actor id, journey id or status or progress are missing")
		}
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	actorJourneyModels := model.NewActorJourneyModels(actorJourneys)
	if err := db.Create(actorJourneyModels).Error; err != nil {
		if isDuplicateActorJourneyEntry(err) {
			return nil, errors.Wrap(err, fmt.Sprintf("%v: actor journeys have already been created", epifierrors.ErrDuplicateEntry))
		}
		return nil, fmt.Errorf("failed to create actorJourneys, err: %w", err)
	}

	return convertToActorJourneyProtos(actorJourneyModels), nil
}

// UpdateActorJourneyById updates the actor journey properties specified in the fieldMasks
// Since it updates by id, it'll affect only 1 row and will return error if no record is found
func (a *ActorJourneyDaoPostgres) UpdateActorJourneyById(ctx context.Context, actorJourney *journeyPb.ActorJourney, fieldMasks []journeyPb.ActorJourneyFieldMask) error {
	defer metric_util.TrackDuration("nudge/journey/dao", "ActorJourneyDaoPostgres", "UpdateActorJourneyById", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	if actorJourney.Id == "" {
		return fmt.Errorf("primary identifier can't be empty for an update operation")
	}

	// filter out field masks which cannot be updated
	filteredUpdateFieldMasks := filterActorJourneyFieldMasks(fieldMasks, actorJourneyFieldMaskFilter)
	if len(filteredUpdateFieldMasks) == 0 {
		return fmt.Errorf("update mask can't be empty")
	}

	actorJourneyModel := model.NewActorJourneyModel(actorJourney)

	// convert fields masks to column name
	updateColumns := getSelectColumnsForActorJourney(filteredUpdateFieldMasks)

	res := db.Model(&model.ActorJourney{}).Where("id = ?", actorJourneyModel.Id).Select(updateColumns).Updates(actorJourneyModel)
	if res.Error != nil {
		return fmt.Errorf("failed to update actor journey, id: %s, err: %w", actorJourneyModel.Id, res.Error)
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("failed to update actor journey, id: %s, err: %w", actorJourneyModel.Id, epifierrors.ErrRecordNotFound)
	}

	return nil
}

// FetchActorJourneys fetches actorJourneys for given actor and set of journeyIds
func (a *ActorJourneyDaoPostgres) FetchActorJourneys(ctx context.Context, actorId string, journeyIds []string, fieldMasks []journeyPb.ActorJourneyFieldMask) ([]*journeyPb.ActorJourney, error) {
	defer metric_util.TrackDuration("nudge/journey/dao", "ActorJourneyDaoPostgres", "FetchActorJourneys", time.Now())
	if actorId == "" || len(journeyIds) == 0 {
		return nil, fmt.Errorf("actor id or journey ids are empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	columns := getSelectColumnsForActorJourney(fieldMasks)

	actorJourneys := make([]*model.ActorJourney, 0)
	if err := db.Select(columns).Where("actor_id = ? AND journey_id IN (?)", actorId, journeyIds).
		Find(&actorJourneys).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch actorJourney by actorId: %s and journeyIds: %v, err: %w", actorId, journeyIds, err)
	}

	return convertToActorJourneyProtos(actorJourneys), nil
}

// nolint: dupl
func (a *ActorJourneyDaoPostgres) FetchActorJourneysByFilters(ctx context.Context, filters *model.ActorJourneyFilters, fieldMasks []journeyPb.ActorJourneyFieldMask, pageToken *pagination.PageToken, pageSize uint32) ([]*journeyPb.ActorJourney, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("nudge/journey/dao", "ActorJourneyDaoPostgres", "FetchActorJourneysByFilters", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, a.db)
	db = buildQueryForFetchActorJourneys(filters, db)

	// convert fields masks to column name
	columns := getSelectColumnsForActorJourney(fieldMasks)

	if pageToken != nil {
		if pageToken.GetIsReverse() {
			db = db.Where("created_at >= ?", pageToken.GetTimestamp().AsTime()).
				Order("created_at" + " ASC")
		} else {
			db = db.Where("created_at <= ?", pageToken.GetTimestamp().AsTime()).
				Order("created_at" + " DESC")
		}
		db = db.Offset(int(pageToken.Offset))
	} else {
		db = db.Order("created_at DESC")
	}
	// fetch pageSize + 1 extra row to compute next page availability.
	db = db.Limit(int(pageSize + 1))

	fetchedActorJourneys := make([]*model.ActorJourney, 0)
	if err := db.Select(columns).Find(&fetchedActorJourneys).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to fetch actor journeys from pgdb, err: %w", err)
	}

	var paginationRows pagination.Rows = model.ActorJourneyRows(fetchedActorJourneys)
	// if the page token is of reverse type, then fix the order of entries as paginated query fetches entries in a reverse order for applying offset.
	if pageToken != nil && pageToken.IsReverse {
		paginationRows = sortPaginationRows(paginationRows)
	}
	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, int(pageSize), paginationRows)
	if err != nil {
		return nil, nil, err
	}
	actorJourneys := rows.(model.ActorJourneyRows)
	return convertToActorJourneyProtos(actorJourneys), pageCtxResp, nil
}

func buildQueryForFetchActorJourneys(filters *model.ActorJourneyFilters, db *gorm.DB) *gorm.DB {
	db = db.Where("journey_type = ?", filters.JourneyType)

	if filters.ActorId != "" {
		db = db.Where("actor_id = ?", filters.ActorId)
	}
	if len(filters.Statuses) > 0 {
		db = db.Where("status IN (?)", filters.Statuses)
	}
	if len(filters.JourneyIds) > 0 {
		db = db.Where("journey_id IN (?)", filters.JourneyIds)
	}
	if filters.ExpireAt.IsValid() {
		db = db.Where("(expire_at is NULL OR expire_at >= ?)", filters.ExpireAt.AsTime())
	}
	return db
}

func (a *ActorJourneyDaoPostgres) FetchActorJourneyById(ctx context.Context, actorJourneyId string, fieldMasks []journeyPb.ActorJourneyFieldMask) (*journeyPb.ActorJourney, error) {
	defer metric_util.TrackDuration("nudge/journey/dao", "ActorJourneyDaoPostgres", "FetchActorJourneyById", time.Now())
	if actorJourneyId == "" {
		return nil, fmt.Errorf("actor_journey_id can't be empty")
	}

	db := gormctxv2.FromContextOrDefault(ctx, a.db)

	columns := getSelectColumnsForActorJourney(fieldMasks)

	actorJourney := &model.ActorJourney{}
	if err := db.Select(columns).Where("id = ?", actorJourneyId).Find(actorJourney).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while fetching actor journey record")
	}

	return actorJourney.GetProto(), nil
}

func (a *ActorJourneyDaoPostgres) GetByIdWithLock(ctx context.Context, actorJourneyId string) (*journeyPb.ActorJourney, error) {
	defer metric_util.TrackDuration("nudge/journey/dao", "ActorJourneyDaoPostgres", "GetByIdWithLock", time.Now())
	if actorJourneyId == "" {
		return nil, fmt.Errorf("actor journey id can't be empty for acquiring lock")
	}
	// Loading the existing db transaction block, as this method needs to execute in an existing txn block
	// If txn block not present return error.
	txn, ok := gormctxv2.FromContextWithLock(ctx)
	if !ok {
		return nil, errors.New("lock cannot be acquired, no ongoing transaction")
	}
	txn = txn.Session(&gorm.Session{Context: epificontext.CloneCtx(ctx)})
	actorJourney := &model.ActorJourney{}
	if res := txn.First(actorJourney, "id = ?", actorJourneyId); res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(res.Error, "error while fetching actor journey record")
	}

	return actorJourney.GetProto(), nil
}

// isDuplicateActorJourneyEntry checks whether the error is related to the violation of unique constraint of actor and journey id
// trying to create an actor nudge which has already been created irrespective of the state
func isDuplicateActorJourneyEntry(err error) bool {
	return strings.Contains(err.Error(), "duplicate key value violates unique constraint \"actor_journeys_actor_id_journey_id_unique_idx\"")
}

// convertToActorJourneyProtos converts list of actor journey models to list of actor journey protos
func convertToActorJourneyProtos(actorJourneyModels []*model.ActorJourney) []*journeyPb.ActorJourney {
	actorJourneys := make([]*journeyPb.ActorJourney, 0)
	for _, actorJourneyModel := range actorJourneyModels {
		actorJourneys = append(actorJourneys, actorJourneyModel.GetProto())
	}

	return actorJourneys
}

// filterActorJourneyFieldMasks filters out field masks based on the check function passed
// elements are filtered out if they don't pass the check function
func filterActorJourneyFieldMasks(fieldMasks []journeyPb.ActorJourneyFieldMask, check func(mask journeyPb.ActorJourneyFieldMask) bool) []journeyPb.ActorJourneyFieldMask {
	filteredFieldMasks := make([]journeyPb.ActorJourneyFieldMask, 0)
	for _, fieldMask := range fieldMasks {
		if check(fieldMask) {
			filteredFieldMasks = append(filteredFieldMasks, fieldMask)
		}
	}
	return filteredFieldMasks
}

// getSelectColumnsForActorJourney converts fieldMasks to column names
func getSelectColumnsForActorJourney(fieldMasks []journeyPb.ActorJourneyFieldMask) []string {
	var selectColumns []string
	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, actorJourneyColumnNameMap[field])
	}
	return selectColumns
}
