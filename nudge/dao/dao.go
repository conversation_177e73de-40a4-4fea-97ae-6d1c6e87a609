//go:generate mockgen -source=dao.go -destination=../test/mocks/dao/dao.go -package=mocks
//go:generate dao_metrics_gen .

package dao

import (
	"context"

	"github.com/epifi/gamma/nudge/dao/model"

	"github.com/google/wire"

	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	nudgePb "github.com/epifi/gamma/api/nudge"
)

var NudgeDaoWireSet = wire.NewSet(NewNudgeDaoPostgres, wire.Bind(new(NudgeDao), new(*NudgeDaoPostgres)))
var ActorNudgeDaoWireSet = wire.NewSet(NewActorNudgeDaoPostgres, wire.Bind(new(ActorNudgeDao), new(*ActorNudgeDaoPostgres)))

type NudgeDao interface {
	// CreateNudge inserts nudge in the table.
	CreateNudge(ctx context.Context, nudgeProto *nudgePb.Nudge) (*nudgePb.Nudge, error)

	// UpdateNudgeById updates nudge properties specified in the updateFieldMasks
	// Since it updates by id, it'll affect only 1 row and will return error if no record is found
	UpdateNudgeById(ctx context.Context, nudgeProto *nudgePb.Nudge, updateFieldMasks []nudgePb.NudgeUpdateFieldMask) error

	// UpdateStatus updates nudge status in accordance to a nudge lifecycle
	UpdateStatus(ctx context.Context, nudgeId string, newStatus nudgePb.NudgeStatus) (*nudgePb.Nudge, error)

	// FetchNudges fetches nudges as per the query filters provided.
	// Following parameters along with their comparison operators are supported:
	//  - ID eq op
	//  - ExitEvent eq op
	//  - Screens contain screen[0] passed
	//  - DisplayFrom greater than eq op
	//  - DisplayTill less than eq op (DisplayFrom <= t <= DisplayTill)
	//  - ActiveFrom greater than eq op
	//  - ActiveTill less than eq op (ActiveFrom <= t <= ActiveTill)
	//  - UserType IN (userType passed, ALL userType)
	FetchNudges(ctx context.Context, nudgeParams *nudgePb.Nudge) ([]*nudgePb.Nudge, error)

	// FetchCachedNudges returns cached nudges for screen and user type if present, else calls FetchNudges
	FetchCachedNudges(ctx context.Context, screen deepLinkPb.Screen, userType nudgePb.NudgeUserType) ([]*nudgePb.Nudge, error)

	// FetchNudgesByFilters returns nudges based on given query filters.
	FetchNudgesByFilters(ctx context.Context, filters *model.NudgeFilters) ([]*nudgePb.Nudge, error)
}

type ActorNudgeDao interface {
	// CreateActorNudge inserts actorNudge in the table.
	// This will fail if entry already exists for columns with unique indices.
	CreateActorNudge(ctx context.Context, actorNudge *nudgePb.ActorNudge) (*nudgePb.ActorNudge, error)

	// BulkCreateActorNudges inserts actorNudges in the table.
	// This will fail if entry already exists for columns with unique indices.
	BulkCreateActorNudges(ctx context.Context, actorNudges []*nudgePb.ActorNudge) ([]*nudgePb.ActorNudge, error)

	// BulkUpdateActorNudgeStatus bulk updates actorNudge status in the table.
	BulkUpdateActorNudgeStatus(ctx context.Context, actorNudgeIds []string, newStatus nudgePb.ActorNudgeStatus) error

	// UpsertActorNudge upserts actorNudge in the table.
	// If (actor_id, nudge_id, entry_event_id) already exists in the table then only `nudge_status` will be updated
	// else data will be inserted.
	UpsertActorNudge(ctx context.Context, actorNudge *nudgePb.ActorNudge) (*nudgePb.ActorNudge, error)

	// FetchActorNudges fetches actorNudges for a given actor and set of nudgeIds
	FetchActorNudges(ctx context.Context, actorId string, nudgeIds []string) ([]*nudgePb.ActorNudge, error)

	// UpdateActorNudgeById updates actor nudge properties specified in the updateFieldMasks
	// Since it updates by id, it'll affect only 1 row and will return error if no record is found
	UpdateActorNudgeById(ctx context.Context, actorNudge *nudgePb.ActorNudge, updateFieldMasks []nudgePb.ActorNudgeUpdateFieldMask) error

	// GetActorNudgeById gets actor nudge entity by actor nudge id
	GetActorNudgeById(ctx context.Context, actorNudgeId string) (*nudgePb.ActorNudge, error)

	// FetchActorNudgesByFilters fetches actorNudges for given filters.
	FetchActorNudgesByFilters(ctx context.Context, actorNudgeFilters *model.ActorNudgeFilters) ([]*nudgePb.ActorNudge, error)
}
