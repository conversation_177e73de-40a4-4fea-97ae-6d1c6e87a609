package datacollector

import (
	"context"

	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/logger"

	nudgeDataCollectorPb "github.com/epifi/gamma/api/nudge/datacollector"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/nudge/datacollector/model"
)

func (c *ConsumerService) ProcessKYCEvent(ctx context.Context, kycEvent *user.KycEvent) (*nudgeDataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected KYC event", zap.String(logger.ACTOR_ID_V2, kycEvent.GetActorId()))
	kycCollectedData := &model.KYCCollectedData{
		KycEvent: kycEvent,
	}
	protoCollectedData, err := kycCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting kyc collected data model to proto", zap.Error(err))
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	// Publish data to entry evaluator queue
	entryMessageId, err := c.entryEvaluatorSqsPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing kyc event collected data to nudge entry event queue",
			zap.String(logger.ID, protoCollectedData.GetId()),
			zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
			zap.String("entry_event_id", protoCollectedData.GetEntryEventId()),
			zap.Error(err),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	logger.Info(ctx, "Pushed kyc event to entry queue",
		zap.String(logger.QUEUE_MESSAGE_ID, entryMessageId),
		zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
	)

	exitMessageId, err := c.exitEvaluatorPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing kyc event collected data to exit queue",
			zap.String(logger.ID, protoCollectedData.GetId()),
			zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
			zap.String("entry_event_id", protoCollectedData.GetEntryEventId()),
			zap.Error(err),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	logger.Info(ctx, "Pushed kyc event to exit queue",
		zap.String(logger.QUEUE_MESSAGE_ID, exitMessageId),
		zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
	)
	return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
}
