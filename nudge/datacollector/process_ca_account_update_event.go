package datacollector

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/nudge/datacollector/model"

	nudgeDataCollectorPb "github.com/epifi/gamma/api/nudge/datacollector"
)

func (c *ConsumerService) ProcessCAAccountUpdateEvent(ctx context.Context, accountUpdateEvent *external.AccountUpdateEvent) (*nudgeDataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Received ca account update event",
		zap.String(logger.ACTOR_ID_V2, accountUpdateEvent.GetActorId()),
		zap.String(logger.ACCOUNT_ID, accountUpdateEvent.GetAccountId()),
		zap.String("accountStatus", accountUpdateEvent.GetAccountStatus().String()),
		zap.String(logger.ACC_INSTRUMENT_TYPE, accountUpdateEvent.GetAccInstrumentType().String()),
	)

	if accountUpdateEvent.GetAccountStatus() == enums.AccountStatus_ACCOUNT_STATUS_DELETED {
		logger.Info(ctx, "account delete event received, no processing needed",
			zap.String(logger.ACTOR_ID_V2, accountUpdateEvent.GetActorId()),
			zap.String(logger.ACCOUNT_ID, accountUpdateEvent.GetAccountId()),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS}}, nil
	}

	caAccountCollectedData := &model.CAAccountUpdateCollectedData{
		AccountUpdateEvent: accountUpdateEvent,
	}
	protoCollectedData, err := caAccountCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting ca account collected data model to proto",
			zap.Error(err), zap.Any("accountUpdateEvent", accountUpdateEvent),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	// Publish data to entry evaluator queue
	entryMessageId, err := c.entryEvaluatorSqsPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing ca account collected data to nudge entry event queue",
			zap.String(logger.ID, protoCollectedData.GetId()),
			zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
			zap.String("entry_event_id", protoCollectedData.GetEntryEventId()),
			zap.Error(err),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	logger.Info(ctx, "Pushed ca account update event to entry queue",
		zap.String(logger.QUEUE_MESSAGE_ID, entryMessageId),
		zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
	)

	exitMessageId, err := c.exitEvaluatorPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing ca account collected data to exit queue",
			zap.String(logger.ID, protoCollectedData.GetId()),
			zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
			zap.String("entry_event_id", protoCollectedData.GetEntryEventId()),
			zap.Error(err),
		)
		return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	logger.Info(ctx, "Pushed ca account update event to exit queue",
		zap.String(logger.QUEUE_MESSAGE_ID, exitMessageId),
		zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()),
	)

	return &nudgeDataCollectorPb.ConsumerResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS}}, nil
}
