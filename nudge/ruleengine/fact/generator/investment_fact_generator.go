package generator

import (
	"context"
	"fmt"

	nudgePb "github.com/epifi/gamma/api/nudge"
	nudgeDataCollectorPb "github.com/epifi/gamma/api/nudge/datacollector"
	"github.com/epifi/gamma/nudge/helper"
	"github.com/epifi/gamma/nudge/ruleengine/fact/common"
	factHelper "github.com/epifi/gamma/nudge/ruleengine/fact/helper"
	investment2 "github.com/epifi/gamma/nudge/ruleengine/fact/investment"
)

// InvestmentAnyInvestmentDoneFactGenerator empty struct for now, might want to inject helper services later
type InvestmentFactGenerator struct {
	factHelperSvc factHelper.IHelperService
	userHelperSvc helper.IUserHelperService
}

func NewInvestmentFactGenerator(factHelperSvc factHelper.IHelperService, userHelperSvc helper.IUserHelperService) *InvestmentFactGenerator {
	return &InvestmentFactGenerator{
		factHelperSvc: factHelperSvc,
		userHelperSvc: userHelperSvc,
	}
}

var _ IFactGenerator = &InvestmentFactGenerator{}

func (k *InvestmentFactGenerator) GenerateFacts(ctx context.Context, collectedData *nudgeDataCollectorPb.CollectedData, nudge *nudgePb.Nudge) ([]common.IFact, error) {
	entryEventIds, err := k.factHelperSvc.GetEntryEventIds(ctx, nudge, collectedData)
	if err != nil {
		return nil, fmt.Errorf("error while fetching entry event ids: %w", err)
	}

	switch nudge.GetExitEvent() {
	// ToDO(Junaid): Remove code for deprecated events after we migrate existing nudges in prod
	case nudgePb.NudgeEventDataType_INVESTMENT_EVENT:
		facts := make([]common.IFact, 0)
		for _, entryEventId := range entryEventIds {
			facts = append(facts, &investment2.InvestmentFact{
				CommonFact: &common.CommonFact{
					Ctx:               ctx,
					RefId:             collectedData.GetId(),
					ActorId:           collectedData.GetActorId(),
					ActionTime:        collectedData.GetActionTime(),
					ActionType:        collectedData.GetEventDataType().String(),
					Nudge:             nudge,
					EntryEventId:      entryEventId,
					UserHelperService: k.userHelperSvc,
				},
				InvestmentData: collectedData.GetInvestmentEvent(),
			})
		}
		return facts, nil
	default:
		return nil, fmt.Errorf("unsupported exit event type: %s", nudge.GetExitEvent().String())
	}
}
