package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/storage"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	ccLmsPb "github.com/epifi/gamma/api/firefly/lms"
	"github.com/epifi/gamma/firefly/lms/dao"
	"github.com/epifi/gamma/firefly/lms/dao/model"
)

type CrdbLoanAccountDao struct {
	db    *gorm.DB
	idGen idgen.IdGenerator
}

var _ dao.LoanAccountDao = &CrdbLoanAccountDao{}

var LoanAccountsDaoWireSet = wire.NewSet(NewCrdbLoanAccountDao, wire.Bind(new(dao.LoanAccountDao), new(*CrdbLoanAccountDao)))

var loanAccountColumnNamesMap = map[ccLmsPb.LoanAccountFieldMask]string{
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_ID:                         "id",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_ACTOR_ID:                   "actor_id",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_TRANSACTION_LOAN_OFFERS_ID: "transaction_loan_offers_id",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_VENDOR_LOAN_ID:             "vendor_loan_id",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_TENURE_IN_MONTHS:           "tenure_in_months",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_AMOUNT_INFO:                "amount_info",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_SCHEDULE:              "loan_schedule",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_SUMMARY:                    "summary",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DISBURSED_DATE:             "disbursed_date",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE:              "loan_end_date",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_UPDATED_AT:                 "updated_at",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_INTEREST_INFO:              "interest_info",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_FEE_INFO:                   "fee_info",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_REPAYMENT_INFO:             "repayment_info",
	ccLmsPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_STATUS:                     "status",
}

func NewCrdbLoanAccountDao(db *gorm.DB, idGen idgen.IdGenerator) *CrdbLoanAccountDao {
	return &CrdbLoanAccountDao{
		db:    db,
		idGen: idGen,
	}
}

func (c *CrdbLoanAccountDao) Create(ctx context.Context, account *ccLmsPb.LoanAccount) (*ccLmsPb.LoanAccount, error) {
	defer metric_util.TrackDuration("firefly/lms/dao/impl", "CrdbLoanAccountDao", "Create", time.Now())
	id, err := c.idGen.Get(idgen.CreditCardLoanAccount)
	if err != nil {
		return nil, errors.Wrap(err, "id generation failed")
	}

	loanAccountModel := model.NewLoanAccount(account)
	loanAccountModel.Id = id

	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := db.Create(loanAccountModel)
	if res.Error != nil {
		// if trying to create more than one entry for an actor_id return duplicate entry error
		if storage.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, res.Error
	}

	return loanAccountModel.GetProto(), nil
}

func (c *CrdbLoanAccountDao) Update(ctx context.Context, account *ccLmsPb.LoanAccount, updateMasks []ccLmsPb.LoanAccountFieldMask) error {
	defer metric_util.TrackDuration("firefly/lms/dao/impl", "CrdbLoanAccountDao", "Update", time.Now())
	if account.GetId() == "" {
		return errors.New("primary identifier can't be empty for an update operation")
	}
	if len(updateMasks) == 0 {
		return errors.New("update mask can't be empty")
	}

	loanAccountModel := model.NewLoanAccount(account)
	updateColumns := c.getColumnsFromFieldMask(updateMasks)
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	whereClause := &model.LoanAccount{
		Id: account.GetId(),
	}

	res := db.Model(loanAccountModel).Where(whereClause).Select(updateColumns).Updates(loanAccountModel)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}

	return nil
}

func (c *CrdbLoanAccountDao) GetById(ctx context.Context, id string, selectedFields []ccLmsPb.LoanAccountFieldMask) (*ccLmsPb.LoanAccount, error) {
	defer metric_util.TrackDuration("firefly/lms/dao/impl", "CrdbLoanAccountDao", "GetById", time.Now())
	if id == "" {
		return nil, errors.New("id can't be blank")
	}

	var loanAccountModel model.LoanAccount
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if len(selectedFields) > 0 {
		db = db.Select(c.getColumnsFromFieldMask(selectedFields))
	}

	if err := db.Where("id = ?", id).First(&loanAccountModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanAccountModel.GetProto(), nil
}

func (c *CrdbLoanAccountDao) GetByVendorLoanId(ctx context.Context, vendorLoanId string, selectedFields []ccLmsPb.LoanAccountFieldMask) (*ccLmsPb.LoanAccount, error) {
	defer metric_util.TrackDuration("firefly/lms/dao/impl", "CrdbLoanAccountDao", "GetByVendorLoanId", time.Now())
	if vendorLoanId == "" {
		return nil, errors.New("vendorLoanId can't be blank")
	}

	var loanAccountModel model.LoanAccount
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if len(selectedFields) > 0 {
		db = db.Select(c.getColumnsFromFieldMask(selectedFields))
	}

	if err := db.Where("vendor_loan_id = ?", vendorLoanId).First(&loanAccountModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanAccountModel.GetProto(), nil
}

func (c *CrdbLoanAccountDao) GetByVendorLoanIds(ctx context.Context, vendorLoanIds []string, selectedFields []ccLmsPb.LoanAccountFieldMask) ([]*ccLmsPb.LoanAccount, error) {
	defer metric_util.TrackDuration("firefly/lms/dao/impl", "CrdbLoanAccountDao", "GetByVendorLoanIds", time.Now())
	if lo.Contains(vendorLoanIds, "") {
		return nil, errors.New("vendorLoanId can't be blank")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if len(selectedFields) > 0 {
		db = db.Select(c.getColumnsFromFieldMask(selectedFields))
	}
	loanAccountModels := make([]*model.LoanAccount, 0)
	query := db.Where("vendor_loan_id in (?)", vendorLoanIds)

	if err := query.Order("created_at desc").Find(&loanAccountModels).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get loan account by vendor loan ids, %w", err)
	}

	if len(loanAccountModels) == 0 {
		return nil, fmt.Errorf("no loan accounts exist for vendor loan ids, %w", epifierrors.ErrRecordNotFound)
	}

	loanAccounts := make([]*ccLmsPb.LoanAccount, 0)
	for _, la := range loanAccountModels {
		loanAccounts = append(loanAccounts, la.GetProto())
	}
	return loanAccounts, nil
}

func (c *CrdbLoanAccountDao) GetByTransactionLoanOffersId(ctx context.Context, transactionLoanOffersId string, selectedFields []ccLmsPb.LoanAccountFieldMask) (*ccLmsPb.LoanAccount, error) {
	defer metric_util.TrackDuration("firefly/lms/dao/impl", "CrdbLoanAccountDao", "GetByTransactionLoanOffersId", time.Now())
	if transactionLoanOffersId == "" {
		return nil, errors.New("transactionLoanOffersId can't be blank")
	}

	var loanAccountModel model.LoanAccount
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if len(selectedFields) > 0 {
		db = db.Select(c.getColumnsFromFieldMask(selectedFields))
	}

	if err := db.Where("transaction_loan_offers_id = ?", transactionLoanOffersId).First(&loanAccountModel).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, err
	}
	return loanAccountModel.GetProto(), nil
}

func (c *CrdbLoanAccountDao) GetByActorIdAndStatus(ctx context.Context, actorId string, statuses []ccLmsPb.LoanAccountStatus, selectedFields []ccLmsPb.LoanAccountFieldMask) ([]*ccLmsPb.LoanAccount, error) {
	defer metric_util.TrackDuration("firefly/lms/dao/impl", "CrdbLoanAccountDao", "GetByActorIdAndStatus", time.Now())
	if actorId == "" {
		return nil, errors.New("actorId can't be blank")
	}

	if lo.Contains(statuses, ccLmsPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_UNSPECIFIED) {
		return nil, errors.New("status can't be unspecified")
	}

	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	if len(selectedFields) > 0 {
		db = db.Select(c.getColumnsFromFieldMask(selectedFields))
	}

	loanAccountModels := make([]*model.LoanAccount, 0)
	query := db.Where("actor_id = ?", actorId)
	if len(statuses) > 0 {
		query.Where("status in (?)", statuses)
	}
	if err := query.Order("created_at desc").Find(&loanAccountModels).Error; err != nil {
		if storage.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("failed to get loan account by actor id: %s and statuses: %v, %w", actorId, statuses, err)
	}

	if len(loanAccountModels) == 0 {
		return nil, fmt.Errorf("no loan account exist for actor id: %s and statuses: %v, %w", actorId, statuses, epifierrors.ErrRecordNotFound)
	}

	var loanAccounts []*ccLmsPb.LoanAccount
	for _, la := range loanAccountModels {
		loanAccounts = append(loanAccounts, la.GetProto())
	}

	return loanAccounts, nil
}

func (c *CrdbLoanAccountDao) getColumnsFromFieldMask(updateMasks []ccLmsPb.LoanAccountFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMasks {
		selectColumns = append(selectColumns, loanAccountColumnNamesMap[field])
	}
	return selectColumns
}
