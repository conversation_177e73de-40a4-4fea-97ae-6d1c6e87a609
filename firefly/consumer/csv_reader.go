package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"strings"

	"github.com/epifi/be-common/pkg/crypto/cryptormap"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/jszwec/csvutil"
	"github.com/pkg/errors"
)

type CsvRowData interface {
	*CardSentToPrintingRowData | *CardDispatchedRowData
}

type CsvReader[C CsvRowData] struct {
	s3Client s3.S3Client
}

func NewCsvReader[C CsvRowData](s3Client s3.S3Client) *CsvReader[C] {
	return &CsvReader[C]{
		s3Client: s3Client,
	}
}

// vendor needs to be provided in case isDecryptionNeeded param is true to initialize cryptor for that specific vendor
func (c *CsvReader[C]) readCsvFromS3(ctx context.Context, s3Client s3.S3Client, s3RecordPath string, isDecryptionNeeded bool, vendor commonvgpb.Vendor, cryptorMapStore *cryptormap.InMemoryCryptorStore) ([]C, error) {
	csvFileContents, readError := s3Client.Read(ctx, s3RecordPath)
	if readError != nil {
		return nil, errors.Wrap(readError, "read from s3Client failed")
	}

	if isDecryptionNeeded {
		decryptedData, decryptionError := decryptCsvFile(ctx, cryptorMapStore, csvFileContents, vendor)
		if decryptionError != nil {
			return nil, decryptionError
		}
		csvFileContents = decryptedData
	}

	sanitizedContent := sanitizeCsvData(csvFileContents)

	var rows []C
	if err := csvutil.Unmarshal(sanitizedContent, &rows); err != nil {
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	}

	return rows, nil
}

func sanitizeCsvData(raw []byte) []byte {
	return []byte(strings.ReplaceAll(string(raw), "\r", "\n"))
}

type CardSentToPrintingRowData struct {
	KitNumber string `csv:"kitNo"`
	Vendor    string `csv:"vendor"`
}

type CardDispatchedRowData struct {
	KitNumber       string `csv:"KitNumber"`
	AwbNumber       string `csv:"UD_AwbNo"`
	Carrier         string `csv:"Ud_Courier"`
	DispatchOrderId string `csv:"dispatch_order_id"`
}
