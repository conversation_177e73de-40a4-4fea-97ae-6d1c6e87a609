package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	ffPb "github.com/epifi/gamma/api/firefly"
	ffActPb "github.com/epifi/gamma/api/firefly/activity"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/logger"
)

// nolint: funlen
func (p *Processor) SetBillingDays(ctx context.Context, req *ffActPb.BillDateSetActivityRequest) (*ffActPb.BillDateSetActivityResponse, error) {
	res := &ffActPb.BillDateSetActivityResponse{}
	lg := activity.GetLogger(ctx)

	/*****  		Fetching the db entries for our card request stage and card request status/sub-status updates			*****/
	cardRequest, err := p.cardRequestDao.GetByOrchestrationId(ctx, req.GetRequestHeader().GetClientReqId(), []ccEnumsPb.CardRequestFieldMask{
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ID,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ACTOR_ID,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_VENDOR,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_ORCHESTRATION_ID,
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_STATUS,
	})

	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("no card request attached to client request id", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, epifitemporal.NewPermanentError(err)
	case err != nil:
		lg.Error("failed to read data from DB", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	// append actor-id to ctx
	ctx = epificontext.CtxWithActorId(ctx, cardRequest.GetActorId())

	if cardRequest.GetStatus() == ccEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
		return nil, errors.Wrap(epifierrors.ErrPermanent, "card request already failed")
	}

	cardRequestStage, err := p.cardRequestStageDao.GetByCardRequestIdAndStage(ctx, cardRequest.GetId(), ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_SET_BILLING_DAYS_AT_VENDOR, []ccEnumsPb.CardRequestStageFieldMask{
		ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_ID,
		ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS,
	})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		var createError error
		cardRequestStage, createError = p.createSetBillingDaysRequestStage(ctx, cardRequest, ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_SET_BILLING_DAYS_AT_VENDOR)
		if createError != nil {
			lg.Error("error in creating new card request stage for card creation activity", epifitemporal.GetError(createError))
			return nil, createError
		}

	case err != nil:
		lg.Error("failed to read data from DB", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	lg.Info(fmt.Sprintf("unsecured card onboarding stage: %s, of program: %s", cardRequestStage.GetStage().String(), ffPkg.GetCardProgramStringFromCardProgram(cardRequest.GetRequestDetails().GetCardProgram())))

	switch cardRequestStage.GetStatus() {
	// case for which the card creation has already been a success
	case ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS:
		return res, nil
	// case for which the card creation has already been a failure
	case ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED:
		return nil, epifitemporal.NewPermanentError(errors.New("the card creation activity is in permanent failed state"))
	// case for which the card creation requires manual intervention
	case ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION:
		return nil, epifitemporal.NewTransientError(errors.New("the card creation activity is in manual intervention state"))
	default:
		// the normal flow would  continue
	}

	if err := p.handleStageSuccessForBillGenDateSetActivity(ctx, cardRequestStage, cardRequest); err != nil {
		lg.Error("error in updating success state for bill set stage", zap.Error(err))
		return nil, err
	}
	p.triggerStageChangeEvent(ctx, cardRequest, cardRequestStage.GetStage().String())
	return res, nil
}

func (p *Processor) createSetBillingDaysRequestStage(ctx context.Context, cardRequest *ffPb.CardRequest, stage ccEnumsPb.CardRequestStageName) (*ffPb.CardRequestStage, error) {
	cardRequestStageCreateReq := &ffPb.CardRequestStage{
		CardRequestId:         cardRequest.GetId(),
		OrchestrationId:       cardRequest.GetOrchestrationId(),
		Stage:                 stage,
		StageExecutionDetails: nil,
		Workflow:              ccEnumsPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
		Status:                ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED,
		SubStatus:             ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_SET_BILLING_DAYS_INITIATED,
	}
	cardRequestStage, createErr := p.cardRequestStageDao.Create(ctx, cardRequestStageCreateReq)
	if createErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, createErr.Error())
	}
	return cardRequestStage, nil
}

func (p *Processor) handleStageSuccessForBillGenDateSetActivity(ctx context.Context, cardRequestStage *ffPb.CardRequestStage, cardRequest *ffPb.CardRequest) error {
	cardRequest.NextAction = ffHelper.GetCreditCardDashboardDeeplink()
	cardRequestStage.Status = ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS
	cardRequestStage.SubStatus = ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_SET_BILLING_DAYS_SUCCESS
	if err := p.updateCardRequestAndCardRequestStageTxn(ctx, cardRequest, cardRequestStage, []ccEnumsPb.CardRequestStageFieldMask{
		ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_SUB_STATUS,
		ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS,
	}, []ccEnumsPb.CardRequestFieldMask{
		ccEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
	}); err != nil {
		return errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error in running txn for updating card request and card request stage").Error())
	}
	return nil
}
