package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	ffActivityPb "github.com/epifi/gamma/api/firefly/activity"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (p *Processor) PollParentsName(ctx context.Context, req *ffActivityPb.FetchCardRequestStageRequest) (*ffActivityPb.FetchCardRequestStageResponse, error) {
	res := &ffActivityPb.FetchCardRequestStageResponse{}
	lg := activity.GetLogger(ctx)

	cardReq, cardRequestStage, err := p.getCardReqAndCreateCardReqStage(ctx, req.GetRequestHeader().GetClientReqId(),
		ccEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_PARENTS_NAME_COLLECTION,
		ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_CREATED, ccEnumsPb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED, "")
	if err != nil {
		lg.Error("error in getCardReqAndCreateCardReqStage", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(err, "error in getCardReqAndCreateCardReqStage")
	}

	finishActivity, err := p.checkIfStageAlreadyTerminated(cardRequestStage)
	switch {
	case err != nil:
		return nil, err
	case finishActivity:
		return res, nil
	default:
		// continue
	}

	userRes, err := p.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: cardReq.GetActorId(),
		},
	})
	if te := epifigrpc.RPCError(userRes, err); te != nil {
		lg.Error("error in getting user", zap.Error(te), zap.String(logger.CARD_REQUEST_ID, cardReq.GetId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
	}

	if userRes.GetUser().GetProfile().GetFatherName() != nil && userRes.GetUser().GetProfile().GetMotherName() != nil {
		cardRequestStage.Status = ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_SUCCESS
		err = p.cardRequestStageDao.Update(ctx, cardRequestStage, []ccEnumsPb.CardRequestStageFieldMask{ccEnumsPb.CardRequestStageFieldMask_CARD_REQUEST_STAGE_FIELD_MASK_STATUS})
		if err != nil {
			lg.Error("error updating card request stage", zap.Error(err), zap.String(logger.CARD_REQUEST_STAGE_ID, cardRequestStage.GetId()))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		res.CardRequest = cardReq
		res.CardRequestStage = cardRequestStage
		return res, nil
	}

	return nil, epifierrors.ErrTransient
}
