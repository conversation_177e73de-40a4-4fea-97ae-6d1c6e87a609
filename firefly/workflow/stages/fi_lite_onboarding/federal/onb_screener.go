// nolint
package federal

import (
	"go.temporal.io/sdk/workflow"

	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/firefly/workflow/stages"
	"github.com/epifi/gamma/firefly/workflow/stages/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
)

type ScreenerCheck struct {
	*common.PostProcessStageCommon
	*common.PreProcessStageCommon
	*common.DeeplinkCommon
}

func NewScreenerCheck() *ScreenerCheck {
	return &ScreenerCheck{}
}

var _ stages.Stage = &ScreenerCheck{}

func (t *ScreenerCheck) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		CardRequest:      req.CardRequest,
		CardRequestStage: req.CardRequestStage,
	}

	// TODO(akk) - fill in logic later
	return res, nil
}

func (t *ScreenerCheck) GetStageName() ffEnumsPb.CardRequestStageName {
	// TODO(akk) - add correct stage once added in enums
	return ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_SCREENER_CHECK
}

func (t *ScreenerCheck) GetCelestialStageName() epifitemporal.Stage {
	return ffNs.ScreenerCheck
}
