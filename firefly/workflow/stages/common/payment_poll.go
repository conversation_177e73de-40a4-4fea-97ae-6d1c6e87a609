package common

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"

	"github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
	ffActPb "github.com/epifi/gamma/api/firefly/activity"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/firefly/workflow/stages"
)

type PaymentPollingStage struct {
	*PostProcessStageCommon
	*PreProcessStageCommon
	*DeeplinkCommon
}

func NewPaymentPollingStage() *PaymentPollingStage {
	return &PaymentPollingStage{}
}

func (p *PaymentPollingStage) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		CardRequest:      req.CardRequest,
		CardRequestStage: req.CardRequestStage,
	}
	actRes := &ffActPb.GetPaymentStatusResponse{}
	actErr := activityPkg.Execute(ctx, ffNs.GetPaymentStatus, actRes, &ffActPb.GetPaymentStatusRequest{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: req.CardRequest.GetOrchestrationId(),
		},
		PaymentClientReqId:   req.CardRequest.GetRequestDetails().GetPaymentRequestDetails().GetPaymentClientReqId(),
		OrderId:              req.CardRequest.GetRequestDetails().GetPaymentRequestDetails().GetOrderId(),
		CardRequestStageName: p.GetStageName(),
	})
	if actErr != nil {
		res.ActivityError = errors.Wrap(actErr, "error in payment")
		return res, nil
	}

	crUpdateRes := &ffActPb.CardRequestUpdateResponse{}
	crUpdErr := activityPkg.Execute(ctx, ffNs.UpdateCardRequest, crUpdateRes, &ffActPb.CardRequestUpdateRequest{
		RequestHeader: &activity.RequestHeader{
			ClientReqId: req.CardRequest.GetOrchestrationId(),
		},
		NextAction: actRes.GetNextAction(),
		FieldMasks: []ffEnumsPb.CardRequestFieldMask{
			ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION,
		},
	})

	if crUpdErr != nil {
		res.ActivityError = errors.Wrap(crUpdErr, "error in updating next action")
		return res, nil
	}
	if actRes.GetPaymentStatus() != ffEnumsPb.PaymentStatus_PAYMENT_STATUS_SUCCESS {
		res.ActivityError = errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("non success payment status : %s", actRes.GetPaymentStatus().String()))
		return res, nil
	}

	return res, nil
}

func (p *PaymentPollingStage) GetStageName() ffEnumsPb.CardRequestStageName {
	return ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_CHECK_PAYMENT_STATUS
}

func (p *PaymentPollingStage) GetCelestialStageName() epifitemporal.Stage {
	return celestial.GetStageFromStageEnum(celestial.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT))
}

var _ stages.Stage = &PaymentPollingStage{}
