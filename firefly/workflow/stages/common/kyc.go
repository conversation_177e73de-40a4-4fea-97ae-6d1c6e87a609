// nolint
package common

import (
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	ffNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/firefly"
	ffActPb "github.com/epifi/gamma/api/firefly/activity"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/firefly/workflow/internal/helper"
	"github.com/epifi/gamma/firefly/workflow/stages"
)

type KycCheck struct {
	*DeeplinkCommon
}

func NewKycCheck() *KycCheck {
	return &KycCheck{}
}

var _ stages.Stage = &KycCheck{}

func (t *KycCheck) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		CardRequest:      req.CardRequest,
		CardRequestStage: req.CardRequestStage,
	}
	kycRes := &ffActPb.ProcessKycCheckResponse{}
	if err := helper.PerformStageWithPermanentFailableState(ctx, req.WfReqId, req.WfProcessingParams, ffNs.ProcessKycCheckV2, workflowPb.Stage_KYC, kycRes, &ffActPb.ProcessKycCheckRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
		},
	}); err != nil {
		res.ActivityError = err
	}
	return res, nil
}

func (t *KycCheck) PreProcess(ctx workflow.Context, req *stages.PreProcessRequest) (*stages.PreProcessResponse, error) {
	var (
		res = &stages.PreProcessResponse{}
	)
	if req.Deeplink != nil {
		crRes := &ffActPb.CardRequestUpdateResponse{
			ResponseHeader: &activityPb.ResponseHeader{},
		}
		err := activityPkg.Execute(ctx, ffNs.UpdateCardRequest, crRes, &ffActPb.CardRequestUpdateRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			},
			NextAction: req.Deeplink,
			FieldMasks: []ffEnumsPb.CardRequestFieldMask{ffEnumsPb.CardRequestFieldMask_CARD_REQUEST_FIELD_MASK_NEXT_ACTION},
		})
		if err != nil {
			return nil, errors.Wrap(err, "error updating card request")
		}
	}

	crsRes := &ffActPb.CreateCardRequestStageResponse{ResponseHeader: &activityPb.ResponseHeader{}}

	err := activityPkg.Execute(ctx, ffNs.CreateCardRequestStage, crsRes, &ffActPb.CreateCardRequestStageRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
		},
		StageName:         req.StageName,
		Status:            ffEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS,
		ExternalRequestId: uuid.NewString(),
	})
	if err != nil {
		return nil, errors.Wrap(err, "error creating card request stage")
	}

	res.CardRequest = crsRes.GetCardRequest()
	res.CardRequestStage = crsRes.GetCardRequestStage()
	return res, nil
}

func (t *KycCheck) PostProcess(ctx workflow.Context, req *stages.PostProcessRequest) (*stages.PostProcessResponse, error) {
	var (
		res = &stages.PostProcessResponse{}
	)

	fetchCardResp := &ffActPb.FetchCardRequestResponse{}
	err := activityPkg.Execute(ctx, ffNs.FetchCardRequest, fetchCardResp, &ffActPb.FetchCardRequestRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
		},
	})
	if err != nil {
		return nil, errors.Wrap(err, "error fetching card request")
	}
	res.CardRequest = fetchCardResp.GetCardRequest()
	res.CardRequestStage = req.Request.CardRequestStage
	switch fetchCardResp.GetCardRequest().GetStatus() {
	case ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE,
		ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_MANUAL_INTERVENTION,
		ffEnumsPb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
		res.ShouldNotContinueWorkflow = true
		return res, nil
	}

	return res, nil
}

func (t *KycCheck) GetStageName() ffEnumsPb.CardRequestStageName {
	return ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_VKYC
}

func (t *KycCheck) GetCelestialStageName() epifitemporal.Stage {
	return celestialPkg.GetStageFromStageEnum(celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_KYC))
}
