@Library('epifi-jenkins-libraries') _
pipeline {
    agent {label 'jenkins-cloud-deployment'}
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '20'))
    }
    tools {
            go 'Go stable'
    }
    parameters {
        string(name: 'APP_BRANCH', defaultValue: 'master', description: 'If IMAGE_ID is provided, then this value of branch is not used')
        choice(name: 'TARGET', choices: ['simulator'], description: 'Launching Pipeline for { simulator }')
        string(name: 'INFRA_BRANCH', defaultValue: 'master', description: 'Infra Branch')
        string(name: 'IMAGE_ID', defaultValue: '', description: 'If you provide Image Id e.g ami-xxxxxxxxxx, It will not build the binary.')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh <PERSON><PERSON><PERSON>.')
    }
    environment {
        TARGET = "${params.TARGET}"
        ENV = "${params.ENV}"
        BRANCH = "${params.APP_BRANCH}"
        INFRA_BRANCH = "${params.INFRA_BRANCH}"
        GOPROXY = 'https://goproxy.pointz.in'
    }
    stages {
        stage('Docker ECR login') {
          steps {
            sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 632884248997.dkr.ecr.ap-south-1.amazonaws.com"
          }
        }
        stage('Infra clone') {
            steps {
                gitCheckout(repoName: "infra", gitBranch: "${INFRA_BRANCH}", githubOrg: "epifi", cleanCheckout: true, shallow: false)
                script {
                    if (params.REFRESH == true) {
                        echo "Jenkins file was loaded....... finish build now"
                        currentBuild.displayName = "#${BUILD_NUMBER}-${TARGET}-Refresh"
                        currentBuild.description = "#${BUILD_NUMBER}-${TARGET}-Refresh"
                        currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                        sleep(5)
                    }
                    IMAGE_ID = "${params.IMAGE_ID}"
                    if (params.ENV == '') {
                        currentBuild.displayName = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-BUILD-AMI"
                        currentBuild.description = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-BUILD-AMI"
                    } else {
                        currentBuild.displayName = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-${ENV}"
                        currentBuild.description = "#${BUILD_NUMBER}-${TARGET}-${BRANCH}-${ENV}"
                    }
                }
            }
        }
        stage('Build binary') {
            when {
                expression { IMAGE_ID == '' }
            }
            steps {
                gitCheckout(repoName: "gamma", gitBranch: "${BRANCH}", githubOrg: "epifi")
                dir('gamma') {
                    sh "chmod 777 ${WORKSPACE}/scripts/binary.sh && ${WORKSPACE}/scripts/binary.sh"
                }
            }
        }
        stage('Build packer') {
            when {
                expression { IMAGE_ID == '' }
            }
            steps {
                dir('infra') {
                    sh "chmod 777 ${WORKSPACE}/scripts/packer.sh && ${env.WORKSPACE}/scripts/packer.sh"
                }
                script {
                    IMAGE_ID = readFile(file: './gamma/output/image_id.txt')
                    echo "${IMAGE_ID}"
                    echo "Adding build related tags to ${IMAGE_ID}"
                    sh "aws ec2 create-tags --tags Key=BUILD_REF,Value=\${BUILD_NUMBER} --resources ${IMAGE_ID}"
                }
            }
        }
        stage('Deployment') {
            when {
                expression { return IMAGE_ID.startsWith('ami-') }
                expression { params.ENV != '' }
            }
            steps {
                script {
                    def envs = [:]
                    "$ENV".split(',').each {
                        envs["${it}"] = {
                            stage("Get Blue/Green Versions") {
                                script {
                                    ROLE_ARN = sh(script: "set +x && echo \${${"${it}".toUpperCase()}_ACCOUNT_ROLE}", returnStdout: true).trim()
                                    echo "Role ARN to be used for ${it} is: ${ROLE_ARN}"
                                    aws_credentials_json = sh(script: "set +x && aws sts assume-role --role-arn '${ROLE_ARN}' --role-session-name ${it}-${env.TARGET}-deploy --region ap-south-1", returnStdout: true).trim()
                                    AWS_ACCESS_KEY_ID = sh(script: "set +x && echo '${aws_credentials_json}' | jq --exit-status --raw-output .Credentials.AccessKeyId", returnStdout: true).trim()
                                    AWS_SECRET_ACCESS_KEY = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SecretAccessKey", returnStdout: true).trim()
                                    AWS_SESSION_TOKEN = sh(script: "set +x && echo '$aws_credentials_json' | jq --exit-status --raw-output .Credentials.SessionToken", returnStdout: true).trim()
                                    lt_blue = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-blue --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                    lt_green = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-green --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                    sh "echo launch template version for blue: ${lt_blue} - Initial value"
                                    sh "echo launch template version for green: ${lt_green} - Initial value"
                                    if (lt_green == 'None') {
                                        echo "lt_green is ${lt_green}"
                                        if (lt_blue == 1) {
                                            echo "lt_blue is ${lt_blue}, so setting lt_green to 1"
                                            lt_green = 1
                                        } else {
                                            if (lt_blue == 'None') {
                                                echo "lt_green is ${lt_green} and lt_blue is ${lt_blue}. So setting both to 1"
                                                lt_blue = 1
                                                lt_green = 1
                                            } else {
                                                echo "lt_green is ${lt_green} and lt_blue is ${lt_blue} - in expr"
                                                lt_green = sh(script: "expr ${lt_blue} + 1", returnStdout: true).trim()
                                            }
                                        }
                                    }
                                    if (lt_blue == 'None') {
                                        echo "lt_blue is ${lt_blue}"
                                        if (lt_green == 1) {
                                            echo "lt_green is ${lt_green}, so setting lt_blue to 1"
                                            lt_blue = 1
                                        } else {
                                            echo "lt_blue is ${lt_blue} and lt_green is ${lt_green} - in expr"
                                            lt_blue = sh(script: "expr ${lt_green} + 1", returnStdout: true).trim()
                                        }
                                    }
                                    sh "echo launch template version for blue: ${lt_blue}"
                                    sh "echo launch template version for green: ${lt_green}"
                                }
                            }
                            stage("Deploy to ${it}") {
                                echo "Environment is: ${it}"
                                dir("${WORKSPACE}/infra/terraform/services/deployment/${env.TARGET}")
                                        {
                                            sh "pwd"
                                            is_passive_listener = sh(script: "set +x && chmod 777 ${WORKSPACE}/scripts/check_passive_listener.py && ${WORKSPACE}/scripts/check_passive_listener.py ${TARGET} ${it}", returnStdout: true).trim()
                                            sh "mkdir -p $HOME/.terraform.d/plugin-cache"
                                            sh "export TF_PLUGIN_CACHE_DIR=$HOME/.terraform.d/plugin-cache"
                                            sh "terraform init -backend-config=s3-backend-${it}.conf"
                                            sh "echo is_passive_listener: ${is_passive_listener}"
                                            if (is_passive_listener.toBoolean()) {
                                                sh "echo Passive listener exists before blue/green - so cleaning it up first....."
                                                sh "terraform apply -input=false -auto-approve -var swap=false -var rollback=false -var clean=true -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                            }
                                            sh "terraform apply -input=false -auto-approve -var swap=false -var rollback=false -var clean=false -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                }
                            }
                            stage("Swap target group") {
                                dir("${WORKSPACE}/infra/terraform/services/deployment/${env.TARGET}")
                                        {
                                            lt_blue = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-blue --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            lt_green = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-green --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            sh "echo launch template version for blue: ${lt_blue} - in swap"
                                            sh "echo launch template version for green: ${lt_green} - in swap"
                                            sh "terraform apply -input=false -auto-approve -var swap=true -var rollback=false -var clean=false -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                        }
                            }
                            stage("Finalize Deployment") {
                                        dir("${WORKSPACE}/infra/terraform/services/deployment/${env.TARGET}")
                                        {
                                            lt_blue = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-blue --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            lt_green = sh(script: "set +x && AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}  AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN} aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ${it}-${env.TARGET}-green --query 'AutoScalingGroups[0].LaunchTemplate.Version' --output text", returnStdout: true).trim()
                                            sh "echo launch template version for blue: ${lt_blue} - in clean"
                                            sh "echo launch template version for green: ${lt_green} - in clean"
                                            sh "echo going to wait for 1 minute before deleting old infra"
                                            sh "sleep 60"
                                            sh "terraform apply -input=false -auto-approve -var swap=false -var rollback=false -var clean=true -var blue_version=${lt_blue} -var green_version=${lt_green} -var-file=../../../env/${it}.tf -var min=1 -var max=1 -var desired=1 -var image_id=${IMAGE_ID}"
                                            sh "aws ec2 create-tags --tags Key=DEPLOY_REF_${it},Value=\${BUILD_NUMBER} --resources ${IMAGE_ID}"
                                        }
                            }
                        }
                    }
                    parallel envs
                }
            }
        }
    }
}
