// nolint:dupl
package securities

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	connectedAccountSecuritiesPb "github.com/epifi/gamma/api/connected_account/securities"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

func (s *Service) GetInvitHoldingsSummary(ctx context.Context, req *connectedAccountSecuritiesPb.GetInvitHoldingsSummaryRequest) (*connectedAccountSecuritiesPb.GetInvitHoldingsSummaryResponse, error) {
	var filterOpts []storagev2.FilterOption
	if req.GetIsin() != "" {
		filterOpts = append(filterOpts, dao.WithIsins([]string{req.GetIsin()}))
	}
	invitHoldings, err := s.aaInvitDao.GetHoldingsByAaAccountIds(ctx, req.GetAaAccountIds(), nil, filterOpts...)
	if err != nil {
		logger.Error(ctx, "failed to get invit holdings for account ids and isin", zap.Error(err), zap.Any(logger.ACCOUNT_ID, req.GetAaAccountIds()), zap.Any(logger.ISIN_NUMBER, req.GetIsin()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &connectedAccountSecuritiesPb.GetInvitHoldingsSummaryResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg("record not found for given aa account id"),
			}, nil
		}
		return &connectedAccountSecuritiesPb.GetInvitHoldingsSummaryResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to get invit holdings for account ids and isin"),
		}, nil
	}
	var extInvitHoldings []*connectedAccountExternalPb.InvitSummary_HoldingInfo
	for _, holding := range invitHoldings {
		extInvitHolding, convErr := holding.ConvertToInvitSummaryHoldingInfo()
		if convErr != nil {
			logger.Error(ctx, "failed to convert invit holding to external format", zap.Error(convErr))
			return &connectedAccountSecuritiesPb.GetInvitHoldingsSummaryResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("failed to convert invit holding to external format: %v", convErr)),
			}, nil
		}
		extInvitHoldings = append(extInvitHoldings, extInvitHolding)
	}

	return &connectedAccountSecuritiesPb.GetInvitHoldingsSummaryResponse{
		Status:       rpcPb.StatusOk(),
		HoldingsInfo: extInvitHoldings,
	}, nil
}
