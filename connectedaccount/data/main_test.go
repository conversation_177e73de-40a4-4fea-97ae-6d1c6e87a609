package data

import (
	"flag"
	"os"
	"testing"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/test"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
)

var (
	dynconf         *genconf.Config
	pgdb            *gormv2.DB
	mockTxnExecutor storagev2.TxnExecutor
)

var (
	consent1 = &caPb.Consent{
		Id:            "test-consent-1",
		ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
		Accounts: &caPb.Accounts{
			AccountList: []*caPb.Account{
				{
					FiType:        caEnumPb.FIType_FI_TYPE_DEPOSIT,
					FipId:         "HDFC-FIP",
					AccType:       caPkg.Savings,
					LinkRefNumber: "link-ref-number-hdfc",
				},
			},
		},
		ConsentId:         "aa-consent-id-1",
		ConsentRequestId:  "test-consent-request-id-1",
		ActorId:           "test-actor-id-1",
		CustomerId:        "**********@onemoney",
		DataRangeFrom:     timestampPb.New(time.Now().AddDate(-1, 0, 0)),
		DataRangeTo:       timestampPb.Now(),
		Signature:         "eyJhbGciOiJSUzI1NiIsImI2NCI6ZmFsc2UsImtpZCI6InRlc3QifQ..VhHkiw-Vz8Ob1WT5MP4ZHEIOV1gdRIc46v_2fZSx1jPD_ipnkGdm8g1hrbkXViy2yKTkDMgX4yLqSgYUGhsBYeV7Yw4JsBCupqFG1f-aOVQ1nk7ePjJuDVzMP0yus1O8Gh2K8c_dd9yf7sHDYbnSTIzqJBpEUSua3qrMg_egAAQiYCcSAplYWYSLomy8510M0fz0z2Xa197awBqeRObN2CfcBqnZKq17TfksZ4qnvIb58euf7KvJgnnhUM8JYjC7L4JoFQCCo6mL74IeLZI7qkwn_vRqR3dd6GZs_EMoYzpjpo5oCylR3xxzQbFqZrC9WnQov1KIRGDMDPPsSYQ2eA",
		HashedPhoneNumber: "test-hash",
		NextFetchAt:       timestampPb.New(time.Now().AddDate(0, 0, -1)),
	}
	consentRequest1 = &caPb.ConsentRequest{
		Id:                  consent1.GetConsentRequestId(),
		ActorId:             "test-actor-id-1",
		TransactionId:       "test-txn-id-1",
		ConsentHandle:       "test-consent-handle-1",
		ConsentHandleStatus: caEnumPb.ConsentHandleStatus_CONSENT_HANDLE_STATUS_READY,
		CreatedAt:           nil,
		UpdatedAt:           nil,
		Status:              caEnumPb.ConsentRequestStatus_CONSENT_REQUEST_STATUS_SUCCESS,
		AaEntity:            caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
		ConsentId:           "test-consent-id",
	}
	consent2 = &caPb.Consent{
		Id:            "test-consent-1",
		ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
		Accounts: &caPb.Accounts{
			AccountList: []*caPb.Account{
				{
					FiType:        caEnumPb.FIType_FI_TYPE_DEPOSIT,
					FipId:         "HDFC-FIP",
					AccType:       caPkg.Savings,
					LinkRefNumber: "ALinkRef",
				},
				{
					FiType:        caEnumPb.FIType_FI_TYPE_DEPOSIT,
					FipId:         "HDFC-FIP",
					AccType:       caPkg.Savings,
					LinkRefNumber: "BLinkRef",
				},
			},
		},
		ConsentId:         "aa-consent-id-1",
		ConsentRequestId:  "test-consent-request-id-1",
		ActorId:           "test-actor-id-1",
		CustomerId:        "**********@onemoney",
		DataRangeFrom:     timestampPb.New(time.Now().AddDate(-1, 0, 0)),
		DataRangeTo:       timestampPb.Now(),
		Signature:         "eyJhbGciOiJSUzI1NiIsImI2NCI6ZmFsc2UsImtpZCI6InRlc3QifQ..VhHkiw-Vz8Ob1WT5MP4ZHEIOV1gdRIc46v_2fZSx1jPD_ipnkGdm8g1hrbkXViy2yKTkDMgX4yLqSgYUGhsBYeV7Yw4JsBCupqFG1f-aOVQ1nk7ePjJuDVzMP0yus1O8Gh2K8c_dd9yf7sHDYbnSTIzqJBpEUSua3qrMg_egAAQiYCcSAplYWYSLomy8510M0fz0z2Xa197awBqeRObN2CfcBqnZKq17TfksZ4qnvIb58euf7KvJgnnhUM8JYjC7L4JoFQCCo6mL74IeLZI7qkwn_vRqR3dd6GZs_EMoYzpjpo5oCylR3xxzQbFqZrC9WnQov1KIRGDMDPPsSYQ2eA",
		HashedPhoneNumber: "test-hash",
		NextFetchAt:       timestampPb.New(time.Now().AddDate(0, 0, -1)),
	}
	acc1 = &caPb.AaAccount{
		Id:               "test-account-1",
		AccountStatus:    caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON,
		AccountSubStatus: caEnumPb.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_ACTIVE,
		AaEntity:         caEnumPb.AaEntity_AA_ENTITY_AA_ONE_MONEY,
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	testServer := test.InitTestServer()
	dynconf = testServer.DynConf
	pgdb = testServer.PgDB
	mockTxnExecutor = storagev2.NewGormTxnExecutor(pgdb)
	exitCode := m.Run()
	testServer.Cleanup()
	os.Exit(exitCode)
}
