//go:generate mockgen -source=fi.go -destination=../../test/mocks/mock_fi/mock_fi.go -package=mock_fi_types
//nolint:dupl,unused, unparam, depguard
package fi_types

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/epifi/gamma/connectedaccount/metrics"

	"github.com/pkg/errors"

	caExtPb "github.com/epifi/gamma/api/connected_account/external"

	"go.uber.org/zap"

	"github.com/golang/protobuf/ptypes/timestamp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/data/fi_types/xml_models"
	caError "github.com/epifi/gamma/connectedaccount/error"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
)

const Unknown = "UNKNOWN"

type FIAccountProcessor interface {
	StoreAccount(ctx context.Context, dataProcessAttempt *caPb.DataProcessAttempt, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	GetSummaryDetails(ctx context.Context, accountId string) (interface{}, error)
	GetSummaryDetailsBulk(ctx context.Context, accountIdList []string) (map[string]interface{}, error)
	GetProfileDetails(ctx context.Context, accountId string) (interface{}, error)
	GetProfileDetailsBulk(ctx context.Context, accountId []string) (map[string]interface{}, error)
}

type FITransactionProcessor interface {
	StoreTxnBatch(ctx context.Context, req *caCoPb.ProcessTransactionsBatchRequest, fipId string, txnEventType caEnumPb.TransactionEventTypeInitiatedBy) error
	GetTxnDetails(ctx context.Context, txnId string) (caPb.Parser, error)
}
type FIDepositProcessor interface {
	FIAccountProcessor
	FITransactionProcessor
	CreateDepositFiBatches(ctx context.Context, processAttemptId string, acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	CreateDepositTxnBatches(ctx context.Context, fetchAttemptId, processAttemptId string,
		depositXml *xml_models.RawFiDepositXml, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawDepositTransaction, error)
}

type FIRecurringDepositProcessor interface {
	FIAccountProcessor
	FITransactionProcessor
	CreateRdFiBatches(ctx context.Context, processAttemptId string, acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	CreateRdTxnBatches(ctx context.Context, fetchAttemptId string, processAttemptId string,
		recurringDepositXml *xml_models.RawFiRecurringDepositXml, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawRecurringDepositTransaction, error)
}

type FITermDepositProcessor interface {
	FIAccountProcessor
	FITransactionProcessor
	CreateTdFiBatches(ctx context.Context, processAttemptId string, acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	CreateTdTxnBatches(ctx context.Context, fetchAttemptId string, processAttemptId string,
		termDepositXml *xml_models.RawFiTermDepositXml, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawTermDepositTransaction, error)
}

type FiEquitiesProcessor interface {
	FIAccountProcessor
	FITransactionProcessor
	CreateEquityFiBatches(ctx context.Context, processAttemptId string, acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	CreateEquityTxnBatches(ctx context.Context, fetchAttemptId string, processAttemptId string,
		equityXml *xml_models.RawEquityXmlData, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawEquityTransaction, error)
}

type FiEtfProcessor interface {
	FIAccountProcessor
	FITransactionProcessor
	CreateEtfFiBatches(ctx context.Context, processAttemptId string, acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	CreateEtfTxnBatches(ctx context.Context, fetchAttemptId string, processAttemptId string,
		etfXml *xml_models.RawETFXmlData, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawEtfTransaction, error)
}

type FiReitProcessor interface {
	FIAccountProcessor
	FITransactionProcessor
	CreateReitFiBatches(ctx context.Context, processAttemptId string, acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	CreateReitTxnBatches(ctx context.Context, fetchAttemptId string, processAttemptId string,
		reitXml *xml_models.RawReitXmlData, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawReitTransaction, error)
}

type FiInvitProcessor interface {
	FIAccountProcessor
	FITransactionProcessor
	CreateInvitFiBatches(ctx context.Context, processAttemptId string, acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error
	CreateInvitTxnBatches(ctx context.Context, fetchAttemptId string, processAttemptId string,
		invitXml *xml_models.RawInvitXmlData, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawInvitTransaction, error)
}

type FiNpsProcessor interface {
	FIAccountProcessor
}

type BatchProcessor interface {
	PersistBatches(ctx context.Context, batches []*caPb.BatchProcessTransaction) ([]*caPb.BatchProcessTransaction, error)
	UpdateBatch(ctx context.Context, batch *caPb.BatchProcessTransaction, attempt *caPb.DataFetchAttempt, isLastBatch bool) error
}

type FITransactionExternalProcessor interface {
	PublishExternalTransactionEvents(ctx context.Context, txnList []*caPb.TransactionEventInternal, txnEventTypeInitiatedBy caEnumPb.TransactionEventTypeInitiatedBy) error
}

// In test cases we need to mock private method dependencies within a method
// hence for each such internal method we create a func sig and inject in service
type CreateDepositFiBatches func(context.Context, string, *caPb.AaAccount, []byte, *caPb.DataFetchAttempt) error
type CreateDepositTxnBatches func(context.Context, string, string, *xml_models.RawFiDepositXml, *caPb.AaAccount) (
	[]*caPb.BatchProcessTransaction, [][]*caPb.RawDepositTransaction, error)

type CreateRdFiBatches func(context.Context, string, *caPb.AaAccount, []byte, *caPb.DataFetchAttempt) error
type CreateRdTxnBatches func(context.Context, string, string, *xml_models.RawFiRecurringDepositXml, *caPb.AaAccount) (
	[]*caPb.BatchProcessTransaction, [][]*caPb.RawRecurringDepositTransaction, error)

type CreateTdFiBatches func(context.Context, string, *caPb.AaAccount, []byte, *caPb.DataFetchAttempt) error
type CreateTdTxnBatches func(context.Context, string, string, *xml_models.RawFiTermDepositXml, *caPb.AaAccount) (
	[]*caPb.BatchProcessTransaction, [][]*caPb.RawTermDepositTransaction, error)

func parseDepositAccountPending(pending *xml_models.DepositPending) (*caPb.Pending, error) {
	if pending == nil || pending.Amount == "" || pending.TransactionType == "" {
		return nil, nil
	}
	dTxnType, dTxnErr := getDepositTransactionType(pending.TransactionType)
	if dTxnErr != nil {
		return nil, dTxnErr
	}
	return &caPb.Pending{
		Amount:          pending.Amount,
		TransactionType: dTxnType,
	}, nil
}

func parseAccountFromDeposit(ctx context.Context, acc *caPb.AaAccount, attempt *caPb.DataFetchAttempt, processAttempt *caPb.DataProcessAttempt,
	rawDepositXml *xml_models.RawFiDepositXml) (*caPb.AaAccount, error) {
	profile, profileErr := getProfileFromDeposit(ctx, acc, rawDepositXml.Profile)
	if profileErr != nil {
		return nil, profileErr
	}
	accInstrumentType, accInstrumentTypeErr := caPkg.GetAccountInstrumentType(rawDepositXml.Type)
	if accInstrumentTypeErr != nil {
		return nil, accInstrumentTypeErr
	}
	resultAccount := &caPb.AaAccount{
		Id:                  acc.GetId(),
		ActorId:             attempt.GetActorId(),
		MaskedAccountNumber: rawDepositXml.MaskedAccNumber,
		LinkedAccountRef:    rawDepositXml.LinkedAccRef,
		Version:             rawDepositXml.Version,
		AccInstrumentType:   accInstrumentType,
		Profile:             profile,
		FipId:               processAttempt.GetFipId(),
		AccountStatus:       updateAccStatusOnFirstDataPullSuccess(acc.GetAccountStatus()),
		AccountSubStatus:    acc.GetAccountSubStatus(),
		LastSyncedAt:        timestampPb.Now(),
	}
	return resultAccount, nil
}

func getAaTransactionMode(transactionMode string) (caEnumPb.TransactionMode, error) {
	switch transactionMode {
	case caPkg.Cash:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_CASH, nil
	case caPkg.Atm:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_ATM, nil
	case caPkg.Card:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_CARD_PAYMENT, nil
	case caPkg.CardPayment:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_CARD_PAYMENT, nil
	case caPkg.Upi:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_UPI, nil
	case caPkg.FT:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_FT, nil
	case caPkg.Others:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_OTHERS, nil
	case caPkg.Check, caPkg.Cheque:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_CHEQUE, nil
	default:
		return caEnumPb.TransactionMode_TRANSACTION_MODE_TYPE_UNSPECIFIED,
			fmt.Errorf("could not map aa transaction mode = %v", transactionMode)
	}
}

func getAaTransactionType(txnType string) (caEnumPb.TransactionType, error) {
	txnType = strings.ToUpper(txnType)
	switch txnType {
	case caPkg.Credit, caPkg.C:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_CREDIT, nil
	case caPkg.Debit, caPkg.D:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_DEBIT, nil
	case caPkg.Opening:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_OPENING, nil
	case caPkg.Interest:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_INTEREST, nil
	case caPkg.Tds:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_TDS, nil
	case caPkg.Installment:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_INSTALLMENT, nil
	case caPkg.Closing:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_CLOSING, nil
	case caPkg.Others:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_OTHERS, nil
	default:
		return caEnumPb.TransactionType_TRANSACTION_TYPE_TYPE_UNSPECIFIED,
			fmt.Errorf("could not map aa transaction type = %v", txnType)

	}
}

func getDepositTransactionType(transactionType string) (caPb.Pending_TransactionType, error) {
	switch transactionType {
	case caPkg.Credit:
		return caPb.Pending_TRANSACTION_TYPE_CREDIT, nil
	case caPkg.Debit:
		return caPb.Pending_TRANSACTION_TYPE_DEBIT, nil
	default:
		return caPb.Pending_TRANSACTION_TYPE_TYPE_UNSPECIFIED, fmt.Errorf("could not map aa deposit transaction type = %v", transactionType)
	}
}

func getDepositAccountStatus(status string) (caEnumPb.DepositAccountStatus, error) {
	switch status {
	case caPkg.Active:
		return caEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_ACTIVE, nil
	case caPkg.Inactive:
		return caEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_INACTIVE, nil
	default:
		return caEnumPb.DepositAccountStatus_DEPOSIT_ACCOUNT_STATUS_TYPE_UNSPECIFIED, fmt.Errorf("could not map deposit account status = %v", status)
	}
}

func getNominee(nominee string) (caEnumPb.Nominee, error) {
	switch nominee {
	case caPkg.Registered:
		return caEnumPb.Nominee_NOMINEE_TYPE_REGISTERED, nil
	case caPkg.NotRegistered:
		return caEnumPb.Nominee_NOMINEE_TYPE_NOT_REGISTERED, nil
	default:
		return caEnumPb.Nominee_NOMINEE_TYPE_NOT_REGISTERED,
			fmt.Errorf("could not map nominee type = %v", nominee)
	}
}

func getHolderType(holderType string) (caEnumPb.HolderType, error) {
	switch holderType {
	case caPkg.Single:
		return caEnumPb.HolderType_HOLDER_TYPE_SINGLE, nil
	case caPkg.Joint:
		return caEnumPb.HolderType_HOLDER_TYPE_JOINT, nil
	default:
		// mapping unrecognizable holder types to unspecified
		return caEnumPb.HolderType_HOLDER_TYPE_TYPE_UNSPECIFIED, nil
	}
}

func getRecurringDepositCompoundingFrequency(compoundingFrequency string) (caEnumPb.CompoundingFrequency, error) {
	switch compoundingFrequency {
	case caPkg.Monthly:
		return caEnumPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_MONTHLY, nil
	case caPkg.Quarterly:
		return caEnumPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_QUARTERLY, nil
	case caPkg.HalfYearly:
		return caEnumPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_HALF_YEARLY, nil
	case caPkg.Yearly:
		return caEnumPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_YEARLY, nil
	default:
		return caEnumPb.CompoundingFrequency_COMPOUNDING_FREQUENCY_TYPE_UNSPECIFIED, fmt.Errorf("could not map rd compounding frequency type = %v", compoundingFrequency)
	}
}

func getRecurringDepositInterestComputation(interestComputation string) (caEnumPb.InterestComputation, error) {
	switch interestComputation {
	case caPkg.SimpleInterest:
		return caEnumPb.InterestComputation_INTEREST_COMPUTATION_SIMPLE, nil
	case caPkg.CompoundInterest:
		return caEnumPb.InterestComputation_INTEREST_COMPUTATION_COMPOUND, nil
	default:
		return caEnumPb.InterestComputation_INTEREST_COMPUTATION_TYPE_UNSPECIFIED, fmt.Errorf("could not map rd interest computation type = %v", interestComputation)
	}
}

func getRecurringDepositInterestPayout(interestPayout string) (caEnumPb.InterestPayout, error) {
	switch interestPayout {
	case caPkg.Monthly:
		return caEnumPb.InterestPayout_INTEREST_PAYOUT_MONTHLY, nil
	case caPkg.Quarterly:
		return caEnumPb.InterestPayout_INTEREST_PAYOUT_QUARTERLY, nil
	case caPkg.HalfYearly:
		return caEnumPb.InterestPayout_INTEREST_PAYOUT_HALF_YEARLY, nil
	case caPkg.Yearly:
		return caEnumPb.InterestPayout_INTEREST_PAYOUT_YEARLY, nil
	case caPkg.OnMaturity:
		return caEnumPb.InterestPayout_INTEREST_PAYOUT_ON_MATURITY, nil
	default:
		return caEnumPb.InterestPayout_INTEREST_PAYOUT_TYPE_UNSPECIFIED, fmt.Errorf("could not map rd interest payout type = %v", interestPayout)

	}
}

func getProfileFromDeposit(ctx context.Context, acc *caPb.AaAccount, profile *xml_models.DepositProfile) (*caPb.Profile, error) {
	if profile == nil {
		logger.Info(ctx, "received nil profile")
		metrics.RecordProcessAttemptFailure(acc.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT, metrics.NilRawHoldersOrProfileInXml)
		return nil, nil
	}
	holders, holdersErr := getHoldersFromDeposit(ctx, acc, profile.Holders)
	if holdersErr != nil {
		return nil, holdersErr
	}
	return &caPb.Profile{
		Holders: holders,
	}, nil
}

func getHoldersFromDeposit(ctx context.Context, acc *caPb.AaAccount, holders *xml_models.DepositHolders) (*caPb.Holders, error) {
	if holders == nil {
		metrics.RecordProcessAttemptFailure(acc.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT, metrics.NilRawHoldersOrProfileInXml)
		return nil, caError.ErrNilRawHolders
	}
	holder, holderErr := getHolderFromDeposit(ctx, holders.Holder)
	if holderErr != nil {
		return nil, holderErr
	}
	holderType, holderTypErr := getHolderType(holders.Type)
	if holderTypErr != nil {
		return nil, holderTypErr
	}
	// log in case holder type could not be mapped
	if holderType == caEnumPb.HolderType_HOLDER_TYPE_TYPE_UNSPECIFIED {
		logger.Error(ctx, fmt.Sprintf("could not map holder type = %v", holders.Type))
	}
	return &caPb.Holders{
		HolderType: holderType,
		Holder:     holder,
	}, nil
}

func getHolderFromDeposit(ctx context.Context, holder []*xml_models.DepositHolder) ([]*caPb.Holder, error) {
	var holderList []*caPb.Holder
	for _, h := range holder {
		dob, dobErr := datetime.ParseStringTimeStampProto(caPkg.DateLayout, h.Dob)
		if dobErr != nil {
			logger.Error(ctx, fmt.Sprintf("error parsing holder dob %v : %v", h.Dob, dobErr))
		}
		nominee, nomineeErr := getNominee(h.Nominee)
		if nomineeErr != nil {
			logger.Error(ctx, fmt.Sprintf("error parsing nominee value %v", nominee), zap.Error(nomineeErr))
		}
		holderList = append(holderList, &caPb.Holder{
			Address:        h.Address,
			CkycCompliance: h.CkycCompliance == "true",
			Dob:            dob,
			Email:          h.Email,
			Landline:       h.Landline,
			Mobile:         h.Mobile,
			Name:           h.Name,
			Nominee:        nominee,
			Pan:            h.Pan,
		})
	}
	return holderList, nil
}

func parseAccountFromRD(ctx context.Context, attempt *caPb.DataFetchAttempt, processAttempt *caPb.DataProcessAttempt, rawRdXml *xml_models.RawFiRecurringDepositXml,
	acc *caPb.AaAccount) (*caPb.AaAccount, error) {
	profile, profileErr := getProfileFromRD(ctx, rawRdXml.Profile)
	if profileErr != nil {
		return nil, profileErr
	}
	accInstrumentType, accInstrumentTypeErr := caPkg.GetAccountInstrumentType(rawRdXml.Type)
	if accInstrumentTypeErr != nil {
		return nil, accInstrumentTypeErr
	}

	resultAccount := &caPb.AaAccount{
		Id:                  acc.GetId(),
		ActorId:             attempt.GetActorId(),
		MaskedAccountNumber: rawRdXml.MaskedAccNumber,
		LinkedAccountRef:    rawRdXml.LinkedAccRef,
		Version:             rawRdXml.Version,
		AccInstrumentType:   accInstrumentType,
		Profile:             profile,
		FipId:               processAttempt.GetFipId(),
		AccountStatus:       updateAccStatusOnFirstDataPullSuccess(acc.GetAccountStatus()),
		AccountSubStatus:    acc.GetAccountSubStatus(),
		LastSyncedAt:        timestampPb.Now(),
	}
	return resultAccount, nil
}

func getProfileFromRD(ctx context.Context, profile *xml_models.RecurringDepositProfile) (*caPb.Profile, error) {
	if profile == nil {
		return nil, caError.ErrNilProfile
	}
	holders, holdersErr := getHoldersFromRD(ctx, profile.Holders)
	if holdersErr != nil {
		return nil, holdersErr
	}
	return &caPb.Profile{
		Holders: holders,
	}, nil
}

func getHoldersFromRD(ctx context.Context, holders *xml_models.RecurringDepositHolders) (*caPb.Holders, error) {
	if holders == nil {
		return nil, caError.ErrNilRawHolders
	}
	holder, holderErr := getHolderFromRD(ctx, holders.Holder)
	if holderErr != nil {
		return nil, holderErr
	}
	holderType, holderTypErr := getHolderType(holders.Type)
	if holderTypErr != nil {
		return nil, holderTypErr
	}
	// log in case holder type could not be mapped
	if holderType == caEnumPb.HolderType_HOLDER_TYPE_TYPE_UNSPECIFIED {
		logger.Error(ctx, fmt.Sprintf("could not map holder type = %v", holders.Type))
	}
	return &caPb.Holders{
		HolderType: holderType,
		Holder:     holder,
	}, nil
}

func getHolderFromRD(ctx context.Context, holder []*xml_models.RecurringDepositHolder) ([]*caPb.Holder, error) {
	var holderList []*caPb.Holder
	for _, h := range holder {
		dob, dobErr := datetime.ParseStringTimeStampProto(caPkg.DateLayout, h.Dob)
		if dobErr != nil {
			logger.Error(ctx, fmt.Sprintf("error parsing holder dob %v : %v", h.Dob, dobErr))
		}
		nominee, nomineeErr := getNominee(h.Nominee)
		if nomineeErr != nil {
			logger.Error(ctx, fmt.Sprintf("error parsing nominee value %v", nominee), zap.Error(nomineeErr))
		}
		holderList = append(holderList, &caPb.Holder{
			Address:        h.Address,
			CkycCompliance: h.CkycCompliance == "true",
			Dob:            dob,
			Email:          h.Email,
			Landline:       h.Landline,
			Mobile:         h.Mobile,
			Name:           h.Name,
			Nominee:        nominee,
			Pan:            h.Pan,
		})
	}
	return holderList, nil
}

func parseAccountFromTD(ctx context.Context, attempt *caPb.DataFetchAttempt, processAttempt *caPb.DataProcessAttempt, rawTdXml *xml_models.RawFiTermDepositXml,
	acc *caPb.AaAccount) (*caPb.AaAccount, error) {
	profile, profileErr := getProfileFromTD(ctx, rawTdXml.Profile)
	if profileErr != nil {
		return nil, profileErr
	}
	accInstrumentType, accInstrumentTypeErr := caPkg.GetAccountInstrumentType(rawTdXml.Type)
	if accInstrumentTypeErr != nil {
		return nil, accInstrumentTypeErr
	}

	resultAccount := &caPb.AaAccount{
		Id:                  acc.GetId(),
		ActorId:             attempt.GetActorId(),
		MaskedAccountNumber: rawTdXml.MaskedAccNumber,
		LinkedAccountRef:    rawTdXml.LinkedAccRef,
		Version:             rawTdXml.Version,
		AccInstrumentType:   accInstrumentType,
		Profile:             profile,
		FipId:               processAttempt.GetFipId(),
		AccountStatus:       updateAccStatusOnFirstDataPullSuccess(acc.GetAccountStatus()),
		AccountSubStatus:    acc.GetAccountSubStatus(),
		LastSyncedAt:        timestampPb.Now(),
	}
	return resultAccount, nil
}

func getProfileFromTD(ctx context.Context, profile *xml_models.TermDepositProfile) (*caPb.Profile, error) {
	if profile == nil {
		return nil, caError.ErrNilProfile
	}
	holders, holdersErr := getHoldersFromTD(ctx, profile.Holders)
	if holdersErr != nil {
		return nil, holdersErr
	}
	return &caPb.Profile{
		Holders: holders,
	}, nil
}

func getHoldersFromTD(ctx context.Context, holders *xml_models.TermDepositHolders) (*caPb.Holders, error) {
	if holders == nil {
		return nil, caError.ErrNilRawHolders
	}
	holder, holderErr := getHolderFromTD(ctx, holders.Holder)
	if holderErr != nil {
		return nil, holderErr
	}
	holderType, holderTypErr := getHolderType(holders.Type)
	if holderTypErr != nil {
		return nil, holderTypErr
	}
	// log in case holder type could not be mapped
	if holderType == caEnumPb.HolderType_HOLDER_TYPE_TYPE_UNSPECIFIED {
		logger.Error(ctx, fmt.Sprintf("could not map holder type = %v", holders.Type))
	}
	return &caPb.Holders{
		HolderType: holderType,
		Holder:     holder,
	}, nil
}

func getHolderFromTD(ctx context.Context, holder []*xml_models.TermDepositHolder) ([]*caPb.Holder, error) {
	var holderList []*caPb.Holder
	for _, h := range holder {
		dob, dobErr := datetime.ParseStringTimeStampProto(caPkg.DateLayout, h.Dob)
		if dobErr != nil {
			logger.Error(ctx, fmt.Sprintf("error parsing holder dob %v : %v", h.Dob, dobErr))
		}
		nominee, nomineeErr := getNominee(h.Nominee)
		if nomineeErr != nil {
			logger.Error(ctx, fmt.Sprintf("error parsing nominee value %v", nominee), zap.Error(nomineeErr))
		}
		holderList = append(holderList, &caPb.Holder{
			Address:        h.Address,
			CkycCompliance: h.CkycCompliance == "true",
			Dob:            dob,
			Email:          h.Email,
			Landline:       h.Landline,
			Mobile:         h.Mobile,
			Name:           h.Name,
			Nominee:        nominee,
			Pan:            h.Pan,
		})
	}
	return holderList, nil
}

func createBatch(fetchAttemptId string, processAttemptId string, batchNumber int32, startTimestamp *timestamp.Timestamp, endTimestamp *timestamp.Timestamp) *caPb.BatchProcessTransaction {
	return &caPb.BatchProcessTransaction{
		FetchAttemptId:    fetchAttemptId,
		ProcessAttemptId:  processAttemptId,
		BatchNumber:       batchNumber,
		TxnStartTimestamp: startTimestamp,
		TxnEndTimestamp:   endTimestamp,
		Status:            caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_PENDING,
	}
}

func updateAccStatusOnFirstDataPullSuccess(accStatus caEnumPb.AccountStatus) caEnumPb.AccountStatus {
	if accStatus == caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_PENDING {
		return caEnumPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_ON
	}
	return accStatus
}

func getEquityTransactionCategory(equityCategory string) (caEnumPb.EquityTransactionCategory, error) {
	switch equityCategory {
	case caPkg.Equity:
		return caEnumPb.EquityTransactionCategory_EQUITY_TRANSACTION_CATEGORY_EQUITY, nil
	default:
		return caEnumPb.EquityTransactionCategory_EQUITY_TRANSACTION_CATEGORY_UNSPECIFIED, fmt.Errorf("could not map equityCategory type = %v", equityCategory)
	}
}

func getEquityTransactionExchange(ctx context.Context, stockExchange string) caEnumPb.EquityStockExchange {
	switch stockExchange {
	case caPkg.NSE:
		return caEnumPb.EquityStockExchange_EQUITY_STOCK_EXCHANGE_NSE
	case caPkg.BSE:
		return caEnumPb.EquityStockExchange_EQUITY_STOCK_EXCHANGE_BSE
	case caPkg.Others:
		return caEnumPb.EquityStockExchange_EQUITY_STOCK_EXCHANGE_OTHERS
	default:
		logger.Error(ctx, fmt.Sprintf("Error mapping stock excahnge: %s", stockExchange))
		return caEnumPb.EquityStockExchange_EQUITY_STOCK_EXCHANGE_UNSPECIFIED
	}
}

func getEquityTransactionType(transactionType string) (caEnumPb.EquityTransactionType, error) {
	switch transactionType {
	case caPkg.Buy:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_BUY, nil
	case caPkg.Sell:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_SELL, nil
	case caPkg.Bonus:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_BONUS, nil
	case caPkg.Split:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_SPLIT, nil
	case caPkg.Dividend:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_DIVIDEND, nil
	case caPkg.Rights:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_RIGHTS, nil
	case caPkg.Others:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_OTHERS, nil
	default:
		return caEnumPb.EquityTransactionType_EQUITY_TRANSACTION_TYPE_UNSPECIFIED, fmt.Errorf("could not map equity transaction type = %v", transactionType)
	}
}

func getEtfTransactionType(transactionType string) (caEnumPb.EtfTransactionType, error) {
	switch transactionType {
	case caPkg.Buy:
		return caEnumPb.EtfTransactionType_ETF_TRANSACTION_TYPE_BUY, nil
	case caPkg.Sell:
		return caEnumPb.EtfTransactionType_ETF_TRANSACTION_TYPE_SELL, nil
	case caPkg.Bonus:
		return caEnumPb.EtfTransactionType_ETF_TRANSACTION_TYPE_BONUS, nil
	case caPkg.Split:
		return caEnumPb.EtfTransactionType_ETF_TRANSACTION_TYPE_SPLIT, nil
	case caPkg.Others:
		return caEnumPb.EtfTransactionType_ETF_TRANSACTION_TYPE_OTHERS, nil
	default:
		return caEnumPb.EtfTransactionType_ETF_TRANSACTION_TYPE_UNSPECIFIED, fmt.Errorf("could not map etf transaction type = %v", transactionType)
	}
}

func GetFipIdFromConsent(consent *caPb.Consent) string {
	accList := consent.GetAccounts().GetAccountList()
	if len(accList) > 0 {
		return accList[0].GetFipId()
	}
	return Unknown
}

// getTxnEventTypeByAttemptInitiatedBy returns the txn event type based on attempt initiated by, for instance
// in case of attempt initiated by user, the txn event is of first data pull.
// Moreover, if txn event type is from first data pull txn, then all the txns will publish to order-first-data-pull-txn-queue
// and by default txn event type i from periodic data pull txn, so order-aa-txn-queue will be used.
func getTxnEventTypeByAttemptInitiatedBy(attemptInitiatedBy caEnumPb.DataFetchAttemptInitiatedBy) caEnumPb.TransactionEventTypeInitiatedBy {
	switch attemptInitiatedBy {
	case caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER:
		return caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_FIRST_DATA_PULL_TXNS
	case caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB:
		return caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS
	default:
		return caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS
	}
}

// convertToExtProfileDetails converts the raw profile of an account data in DB to external proto message
func convertToExtProfileDetails(ctx context.Context, profile *caPb.Profile, fipId string) (*caExtPb.HoldersDetails, error) {
	if profile == nil {
		logger.Error(ctx, "profile is not present", zap.Any(logger.FIP_ID, fipId))
		return nil, nil
	}
	holderDetailsList, holderDetailsListErr := convertToExtHolderDetailsList(profile.GetHolders())
	if holderDetailsListErr != nil {
		return nil, errors.Wrap(holderDetailsListErr, "error converting to external holder details list")
	}
	return &caExtPb.HoldersDetails{
		HolderType:        profile.GetHolders().GetHolderType(),
		HolderDetailsList: holderDetailsList,
	}, nil
}

func convertToExtHolderDetailsList(holders *caPb.Holders) ([]*caExtPb.HolderDetails, error) {
	var result []*caExtPb.HolderDetails
	for _, holder := range holders.GetHolder() {
		phNum, phErr := strconv.ParseUint(holder.GetMobile(), 10, 64)
		if phErr != nil {
			return nil, fmt.Errorf("error while parsing phone number: %v", holder.GetMobile())
		}
		result = append(result, &caExtPb.HolderDetails{
			Address:        holder.GetAddress(),
			CkycCompliance: holder.GetCkycCompliance(),
			Dob:            holder.GetDob(),
			Email:          holder.GetEmail(),
			Landline:       holder.GetLandline(),
			PhoneNumber: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: phNum,
			},
			Name: &commontypes.Name{
				FirstName: holder.GetName(),
			},
			Nominee: holder.GetNominee(),
			Pan:     holder.GetPan(),
		})
	}
	return result, nil
}
