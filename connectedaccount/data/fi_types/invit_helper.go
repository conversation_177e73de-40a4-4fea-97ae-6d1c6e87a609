// nolint:dupl,unparam
package fi_types

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/connectedaccount/data/fi_types/xml_models"
	caError "github.com/epifi/gamma/connectedaccount/error"
	"github.com/epifi/gamma/connectedaccount/metrics"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

func (e *InvitProcessor) parseAaAccountFromRawXml(ctx context.Context, dataAttempt *caPb.DataFetchAttempt,
	processAttempt *caPb.DataProcessAttempt, rawInvitXml *xml_models.RawInvitXmlData, acc *caPb.AaAccount) (
	*caPb.AaAccount, error) {
	profile, profileErr := e.getProfileFromInvitXmlData(ctx, rawInvitXml.Profile)
	if profileErr != nil {
		return nil, profileErr
	}
	accInstrumentType, accInstrumentTypeErr := caPkg.GetAccountInstrumentType(rawInvitXml.Type)
	if accInstrumentTypeErr != nil {
		return nil, accInstrumentTypeErr
	}

	resultAccount := &caPb.AaAccount{
		Id:                  acc.GetId(),
		ActorId:             acc.GetActorId(),
		MaskedAccountNumber: rawInvitXml.MaskedDematId,
		LinkedAccountRef:    rawInvitXml.LinkedAccRef,
		Version:             rawInvitXml.Version,
		AccInstrumentType:   accInstrumentType,
		FipId:               acc.GetFipId(),
		AccountStatus:       updateAccStatusOnFirstDataPullSuccess(acc.GetAccountStatus()),
		AccountSubStatus:    acc.GetAccountSubStatus(),
		LastSyncedAt:        timestampPb.Now(),
		ConsentReferenceId:  dataAttempt.GetConsentReferenceId(),
		Profile: &caPb.Profile{
			AccountProfile: &caPb.AccountProfile{Profile: &caPb.AccountProfile_InvitHolders{
				InvitHolders: profile,
			}},
		},
	}
	return resultAccount, nil
}

func (e *InvitProcessor) getProfileFromInvitXmlData(ctx context.Context, profile *xml_models.InvitProfile) (
	*caPb.InvitHolders, error) {
	if profile == nil {
		return nil, fmt.Errorf("getProfileFromInvitXmlData: raw profile is nil")
	}
	if profile.Holders == nil {
		return nil, fmt.Errorf("getProfileFromInvitXmlData: holders in raw profile is nil")
	}
	holders, holdersErr := e.getHoldersFromInvitXmlData(ctx, profile.Holders.Holders)
	if holdersErr != nil {
		return nil, holdersErr
	}
	return &caPb.InvitHolders{
		InvitHolders: holders,
	}, nil
}

func (e *InvitProcessor) getHoldersFromInvitXmlData(ctx context.Context, invitHolders []*xml_models.InvitHolder) (
	[]*caPb.InvitHolder, error) {
	if invitHolders == nil {
		return nil, caError.ErrNilRawHolders
	}
	var holderList []*caPb.InvitHolder
	for _, holder := range invitHolders {
		dob, dobErr := datetime.ParseStringTimeStampProto(caPkg.DateLayout, holder.DOB)
		if dobErr != nil {
			logger.Error(ctx, fmt.Sprintf("getHoldersFromInvitXmlData: error parsing holder dob %v : %v",
				holder.DOB, dobErr))
		}
		if len(holder.DematId) < 4 {
			logger.Error(ctx, fmt.Sprintf("getHoldersFromInvitXmlData: length of demat Id is less than 4 chars %s", holder.DematId))
		}
		nominee, nomineeErr := getNominee(holder.Nominee)
		if nomineeErr != nil {
			logger.Error(ctx, fmt.Sprintf("getHoldersFromInvitXmlData: error parsing nominee value %v", nominee),
				zap.Error(nomineeErr))
		}
		holderList = append(holderList, &caPb.InvitHolder{
			Address:  holder.Address,
			Dob:      dob,
			Email:    holder.Email,
			Landline: holder.Landline,
			Mobile:   holder.Mobile,
			Name:     holder.Name,
			Nominee:  nominee,
			Pan:      holder.PAN,
			DematId:  holder.DematId,
		})
	}
	return holderList, nil
}

func (e *InvitProcessor) checkAaAccountDataValidity(oldAcc, parsedAcc *caPb.AaAccount) error {
	if oldAcc.GetLinkedAccountRef() != parsedAcc.GetLinkedAccountRef() {
		return errors.New(fmt.Sprintf("checkAccountDataValidity: link ref number mismatch in pulled account "+
			"details : %v, %v", oldAcc.GetLinkedAccountRef(), parsedAcc.GetLinkedAccountRef()))
	}
	if oldAcc.GetAccInstrumentType() != parsedAcc.GetAccInstrumentType() {
		return errors.New(fmt.Sprintf("checkAccountDataValidity; acc instrument type mismatch in pulled account "+
			"details : %v, %v", oldAcc.GetAccInstrumentType(), parsedAcc.GetAccInstrumentType()))
	}
	if len(parsedAcc.GetMaskedAccountNumber()) < 4 {
		return errors.New(fmt.Sprintf("checkAccountDataValidity: received invalid masked account number : %s",
			parsedAcc.GetMaskedAccountNumber()))
	}
	oldAccLastFourDigits := oldAcc.GetMaskedAccountNumber()[len(oldAcc.GetMaskedAccountNumber())-4:]
	parsedAccLastFourDigits := parsedAcc.GetMaskedAccountNumber()[len(parsedAcc.GetMaskedAccountNumber())-4:]
	if oldAccLastFourDigits != parsedAccLastFourDigits {
		return errors.New(fmt.Sprintf("checkAccountDataValidity: masked account number mismatch in pulled account "+
			"details : %v, %v", oldAccLastFourDigits, parsedAccLastFourDigits))
	}
	return nil
}

func (e *InvitProcessor) parseSummaryDetailsFromRawXml(ctx context.Context, aaAccount *caPb.AaAccount, rawInvitXml *xml_models.RawInvitXmlData) (
	*caPb.AaInvitAccount, error) {
	if rawInvitXml == nil || rawInvitXml.Summary == nil {
		return nil, fmt.Errorf("parseInvitSummaryDetailsFromRawXml: raw xml or summary is nil")
	}
	if rawInvitXml.Summary.CurrentValue == "" {
		return nil, fmt.Errorf("parseInvitSummaryDetailsFromRawXml: current value in summary is nil")
	}

	currentValue, parseErr := money.ParseString(rawInvitXml.Summary.CurrentValue, "INR")
	if parseErr != nil {
		return nil, fmt.Errorf("parseInvitSummaryDetailsFromRawXml: current value could not be parsed to Money")
	}
	return &caPb.AaInvitAccount{
		AaAccountId:  aaAccount.GetId(),
		CurrentValue: currentValue,
	}, nil
}

func (e *InvitProcessor) parseHoldingDetailsFromRawXml(ctx context.Context, aaAccount *caPb.AaAccount, rawInvitXml *xml_models.RawInvitXmlData) (
	[]*caPb.AaInvitHolding, error) {
	if rawInvitXml == nil || rawInvitXml.Summary == nil || rawInvitXml.Summary.Investment == nil ||
		rawInvitXml.Summary.Investment.Holdings == nil || rawInvitXml.Summary.Investment.Holdings.Holding == nil {
		return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: holdings can not be parsed from rawInvitXml")
	}

	var invitHoldings []*caPb.AaInvitHolding
	for _, rawHoldingData := range rawInvitXml.Summary.Investment.Holdings.Holding {
		if rawHoldingData.ISIN == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: isin is nil in raw holding data")
		}
		if rawHoldingData.Units == "" {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: units allocated is nil in raw holding data")
		}
		units, unitsParseErr := strconv.ParseFloat(rawHoldingData.Units, 32)
		if unitsParseErr != nil {
			return nil, fmt.Errorf("parseHoldingDetailsFromRawXml: units could not be parsed to float32")
		}

		invitHoldings = append(invitHoldings, &caPb.AaInvitHolding{
			AaAccountId: aaAccount.GetId(),
			Isin:        rawHoldingData.ISIN,
			Units:       float32(units),
			MetaData: &caPb.InvitHoldingMetaData{
				IsinDescription: rawHoldingData.ISINDescription,
				IssuerName:      rawHoldingData.IssuerName,
			},
		})
	}
	return invitHoldings, nil
}

func (e *InvitProcessor) checkSummaryDataAndRecordColumnUpdate(ctx context.Context, oldAcc, parsedAcc *caPb.AaInvitAccount) error {
	parsedAcc.Id = oldAcc.GetId()
	if oldAcc.GetCurrentValue() != parsedAcc.GetCurrentValue() {
		// assuming the column update timestamp as the current time of data processing.
		captureColumnUpdate(ctx, parsedAcc.GetAaAccountId(), caEnumPb.AaAccountMutableColumn_AA_ACCOUNT_MUTABLE_COLUMN_BALANCE,
			money.ToDisplayString(parsedAcc.GetCurrentValue()), timestampPb.New(time.Now()), e.captureColumnUpdatePub)
	}
	return nil
}

func (e *InvitProcessor) processTxn(ctx context.Context, inTransactionMethodImpl storagev2.InTransaction) error {
	return e.txnExecutor.RunTxn(ctx, inTransactionMethodImpl)
}

// nolint:funlen,dupl
func (e *InvitProcessor) batchUpsertTransactions(ctx context.Context, req *caCoPb.ProcessTransactionsBatchRequest, fipId string) ([]*caPb.TransactionEventInternal, error) {
	var txnPublishList []*caPb.TransactionEventInternal
	var newTxnList []*caPb.AaTransaction

	txnIdToRawInvitTxnMap := make(map[string]*caPb.RawInvitTransaction)

	for _, rawInvitTxn := range req.GetRawInvitTransactions().GetRawInvitTransactions() {
		parsedAaTxn, parsedTxnErr := e.parseAaTransactionFromRawInvitTxn(ctx, rawInvitTxn, req.GetAccountReferenceId(),
			req.GetActorId(), req.GetConsentReferenceId(), fipId)
		if parsedTxnErr != nil {
			return nil, errors.Wrap(parsedTxnErr, "error while parsing aa_transaction from raw invit transaction")
		}

		_, present := txnIdToRawInvitTxnMap[req.GetAccountReferenceId()+parsedAaTxn.GetDerivedTxnId()]
		if !present {
			newTxnList = append(newTxnList, parsedAaTxn)
			txnIdToRawInvitTxnMap[req.GetAccountReferenceId()+parsedAaTxn.GetDerivedTxnId()] = rawInvitTxn
		}
	}
	logger.Debug(ctx, "new txn list length", zap.Any("newTxnListLength", len(newTxnList)))
	txnRefIdToTxnMap := make(map[string]*caPb.AaTransaction)

	if len(newTxnList) == 0 {
		logger.Info(ctx, "new txn list length is 0, returning")
		return nil, nil
	}

	var newInvitTxnList []*caPb.AaInvitTransaction
	var createdTxnIdList []string
	createdTxnList, err := e.transactionDao.BatchUpsertTransactionList(ctx, newTxnList)
	if err != nil {
		return nil, errors.Wrap(err, "error in batch upserting invit transactions")
	}

	for _, createdTxn := range createdTxnList {
		createdTxnIdList = append(createdTxnIdList, createdTxn.GetId())
	}

	logger.Debug(ctx, "created txn list length", zap.Any("createdTxnListLength", len(createdTxnList)))
	existingInvitTxnList := make(map[string]bool)
	bulkDepTxns, bulkDepTxnsErr := e.invitDao.GetBulkTransactionsByRefIds(ctx, createdTxnIdList,
		caPb.AaInvitTransactionFieldMask_AA_INVIT_TRANSACTION_FIELD_MASK_ID,
		caPb.AaInvitTransactionFieldMask_AA_INVIT_TRANSACTION_FIELD_MASK_TRANSACTION_REF_ID)
	if bulkDepTxnsErr != nil && !errors.Is(bulkDepTxnsErr, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(bulkDepTxnsErr, "error bulk fetching transactions by ref ids")
	}
	for _, bulkDepTxn := range bulkDepTxns {
		existingInvitTxnList[bulkDepTxn.GetTransactionRefId()] = true
	}

	for _, createdTxn := range createdTxnList {
		if existingInvitTxnList[createdTxn.GetId()] {
			continue
		}
		parsedInvitTxn, parsedInvitTxnErr := parseRawInvitTransaction(txnIdToRawInvitTxnMap[createdTxn.GetAccountId()+createdTxn.GetDerivedTxnId()], createdTxn)
		if parsedInvitTxnErr != nil {
			return nil, errors.Wrap(parsedInvitTxnErr, "error while parsing aa_invit_transaction")
		}
		newInvitTxnList = append(newInvitTxnList, parsedInvitTxn)
		txnRefIdToTxnMap[createdTxn.GetId()] = createdTxn
	}

	if len(newInvitTxnList) == 0 {
		logger.Info(ctx, "found empty invit txn list, returning")
		return nil, nil
	}

	createdInvitTxnList, createdInvitTxnErr := e.invitDao.BatchCreateTxns(ctx, newInvitTxnList)
	if createdInvitTxnErr != nil {
		return nil, errors.Wrap(createdInvitTxnErr, "error while creating aa_invit_transaction")
	}
	logger.Debug(ctx, "created invit txn list length", zap.Any("createdInvitTxnListLength", len(createdInvitTxnList)))

	for _, createdInvitTxn := range createdInvitTxnList {
		txnPublishList = append(txnPublishList, &caPb.TransactionEventInternal{
			AaTransaction: txnRefIdToTxnMap[createdInvitTxn.GetTransactionRefId()],
			FITransaction: createdInvitTxn,
		})
	}
	return txnPublishList, nil
}

func (e *InvitProcessor) parseAaTransactionFromRawInvitTxn(ctx context.Context, txn *caPb.RawInvitTransaction, accountRefId, actorId, consentRefId, fipId string) (*caPb.AaTransaction, error) {
	if txn == nil {
		return nil, fmt.Errorf("Invit raw txn data is nil")
	}
	derivedTxnId, err := caPkg.GenerateUniqueDerivedTxnIdForIndianStockInstruments(txn.GetTransactionDateTime(), txn.GetTxnId(), txn.GetIsin(), txn.GetUnits())
	if err != nil {
		return nil, errors.Wrap(err, "error generating derived transaction id")
	}

	return &caPb.AaTransaction{
		AccountId:         accountRefId,
		TxnId:             txn.GetTxnId(),
		ActorId:           actorId,
		TransactionDate:   txn.GetTransactionDateTime(),
		TxnInstrumentType: caEnumPb.TxnInstrumentType_TXN_INSTRUMENT_TYPE_INVIT,
		DerivedTxnId:      derivedTxnId,
		TxnIdSource:       caEnumPb.TxnIdSource_TXN_ID_SOURCE_FIP,
	}, nil
}

// parseRawInvitTransaction converts raw XML data to proto defined struct
func parseRawInvitTransaction(rawTxn *caPb.RawInvitTransaction, aaTxn *caPb.AaTransaction) (*caPb.AaInvitTransaction, error) {
	if rawTxn == nil {
		return nil, fmt.Errorf("raw txn data is nil")
	}
	units, unitsParseErr := strconv.ParseFloat(rawTxn.GetUnits(), 32)
	if unitsParseErr != nil {
		return nil, fmt.Errorf("parseTransactionDetailsFromRawXml: units could not be parsed to float32")
	}

	return &caPb.AaInvitTransaction{
		TransactionRefId: aaTxn.GetId(),
		AaAccountId:      aaTxn.GetAccountId(),
		Metadata: &caPb.InvitTransactionMetadata{
			IsinDescription:        rawTxn.GetIsinDescription(),
			IssuerName:             rawTxn.GetIssuerName(),
			TransactionDescription: rawTxn.GetTransactionDescription(),
		},
		Isin:                rawTxn.GetIsin(),
		Units:               float32(units),
		TransactionDateTime: rawTxn.GetTransactionDateTime(),
	}, nil
}

func (e *InvitProcessor) categorizeOperationsOnHoldings(oldHoldings, newHoldings []*caPb.AaInvitHolding) (updatedAndAddedHoldings, deletedHoldings []*caPb.AaInvitHolding, err error) {
	if len(oldHoldings) == 0 {
		return newHoldings, nil, nil
	}

	deletedHoldings = []*caPb.AaInvitHolding{}
	updatedAndAddedHoldings = []*caPb.AaInvitHolding{}

	oldHoldingsMap := make(map[string]*caPb.AaInvitHolding)
	newHoldingsMap := make(map[string]*caPb.AaInvitHolding)

	// Create maps for both oldInvitHoldings and newInvitHoldings for faster lookups.
	for _, oldHolding := range oldHoldings {
		oldHoldingsMap[oldHolding.GetIsin()] = oldHolding
	}
	for _, newHolding := range newHoldings {
		newHoldingsMap[newHolding.GetIsin()] = newHolding
	}

	// Iterate through oldInvitHoldings to find deleted and updated/added holdings.
	for _, oldHolding := range oldHoldings {
		if newHolding, found := newHoldingsMap[oldHolding.GetIsin()]; found {
			// If the invit holding exists in both lists, compare each field.
			updatedHolding, updHoldingErr := e.compareAndEnrichInvitHolding(oldHolding, newHolding)
			if updHoldingErr != nil {
				return nil, nil, fmt.Errorf("error in compareAndEnrichInvitHolding updated holding, oldHoldingISIN: %s", oldHolding.GetIsin())
			}
			updatedAndAddedHoldings = append(updatedAndAddedHoldings, updatedHolding)
		} else {
			// If the invit holding doesn't exist in newInvitHoldings, mark it for deletion.
			deletedHoldings = append(deletedHoldings, oldHolding)
		}
	}

	// Iterate through newInvitHoldings to find added holdings.
	for _, newHolding := range newHoldings {
		if _, found := oldHoldingsMap[newHolding.GetIsin()]; !found {
			// If the invit holding exists in newInvitHoldings but not in oldInvitHoldings, mark it as added.
			updatedAndAddedHoldings = append(updatedAndAddedHoldings, newHolding)
		}
	}

	// At this point, deletedHoldings contains deleted invit holdings,
	// and updatedAndAddedHoldings contains both updated and new added invit holdings.
	return updatedAndAddedHoldings, deletedHoldings, nil
}

func (e *InvitProcessor) compareAndEnrichInvitHolding(oldHolding, newHolding *caPb.AaInvitHolding) (*caPb.AaInvitHolding, error) {
	updatedInvitHolding := oldHolding
	if oldHolding.GetIsin() != newHolding.GetIsin() {
		return nil, fmt.Errorf("ISIN is different in old and new holding: oldISIN:%s ; newISIN:%s ",
			oldHolding.GetIsin(), newHolding.GetIsin())
	}

	updatedInvitHolding.Units = newHolding.GetUnits()

	if len(newHolding.GetMetaData().GetIsinDescription()) > len(oldHolding.GetMetaData().GetIsinDescription()) {
		updatedInvitHolding.MetaData.IsinDescription = newHolding.GetMetaData().GetIsinDescription()
	}
	return updatedInvitHolding, nil
}

func (e *InvitProcessor) convertToExtInvitSummary(ctx context.Context, invitAccount *caPb.AaInvitAccount, invitHoldings []*caPb.AaInvitHolding) (*caExtPb.InvitSummary, error) {

	var holdingsInfo []*caExtPb.InvitSummary_HoldingInfo
	for _, eqHolding := range invitHoldings {
		holdingsInfo = append(holdingsInfo, &caExtPb.InvitSummary_HoldingInfo{
			Isin:             eqHolding.GetIsin(),
			IsinDescription:  eqHolding.GetMetaData().GetIsinDescription(),
			TotalNumberUnits: eqHolding.GetUnits(),
			IssuerName:       eqHolding.GetMetaData().GetIssuerName(),
		})
	}
	return &caExtPb.InvitSummary{
		AccountId:    invitAccount.GetAaAccountId(),
		CurrentValue: invitAccount.GetCurrentValue(),
		HoldingsInfo: holdingsInfo,
	}, nil
}

func (p *InvitProcessor) CreateInvitTxnBatches(ctx context.Context, fetchAttemptId string, processAttemptId string,
	invitXml *xml_models.RawInvitXmlData, acc *caPb.AaAccount) ([]*caPb.BatchProcessTransaction, [][]*caPb.RawInvitTransaction, error) {
	if invitXml == nil || invitXml.Transactions == nil || invitXml.Transactions.Transactions == nil {
		return nil, nil, caError.ErrEmptyBatchedTxns
	}
	invitTxns := invitXml.Transactions.Transactions
	batchSize := p.conf.BatchSize()
	var rawInvitTxns []*caPb.RawInvitTransaction
	// filter out transactions which don't have a timestamp associated to them
	for _, rawInvitTxn := range invitTxns {
		// try parsing, if any error occurs then drop the transaction from the list and move forward
		invitTxn, invitTxnErr := parseRawInvitTxnToProto(ctx, rawInvitTxn)
		if invitTxnErr != nil {
			logger.Error(ctx, "dropping invit txn due to parsing raw txn error",
				zap.String(logger.ATTEMPT_ID, fetchAttemptId), zap.String("process_attempt_id", processAttemptId),
				zap.Any("txn", rawInvitTxn), zap.Error(invitTxnErr))
			metrics.RecordDroppedTxn(acc.GetFipId(), acc.GetAccInstrumentType())
			continue
		}
		rawInvitTxns = append(rawInvitTxns, invitTxn)
	}
	if len(rawInvitTxns) == 0 {
		return nil, nil, caError.ErrEmptyBatchedTxns
	}
	// sorting the txns in descending order as per timestamp
	sort.Slice(rawInvitTxns, func(i, j int) bool {
		return rawInvitTxns[i].GetTransactionDateTime().AsTime().After(rawInvitTxns[j].GetTransactionDateTime().AsTime())
	})
	var batchedRdTxns [][]*caPb.RawInvitTransaction
	var currentBatchedInvitTxns []*caPb.RawInvitTransaction
	var batches []*caPb.BatchProcessTransaction
	batchNumber := int32(1)
	for batchIndex, txn := range rawInvitTxns {
		// push the txn into the current batch
		currentBatchedInvitTxns = append(currentBatchedInvitTxns, txn)
		if (batchIndex+1)%int(batchSize) == 0 {
			startTimestamp, endTimestamp := currentBatchedInvitTxns[0].GetTransactionDateTime(), currentBatchedInvitTxns[len(currentBatchedInvitTxns)-1].GetTransactionDateTime()
			batch := createBatch(fetchAttemptId, processAttemptId, batchNumber, startTimestamp, endTimestamp)
			batches = append(batches, batch)
			batchedRdTxns = append(batchedRdTxns, currentBatchedInvitTxns)
			// empty the current txn batch
			currentBatchedInvitTxns = []*caPb.RawInvitTransaction{}
			batchNumber++
		}
	}
	// still have some txns left over, create a new batch for them as well
	if len(currentBatchedInvitTxns) > 0 {
		startTimestamp, endTimestamp := currentBatchedInvitTxns[0].GetTransactionDateTime(), currentBatchedInvitTxns[len(currentBatchedInvitTxns)-1].GetTransactionDateTime()
		batch := createBatch(fetchAttemptId, processAttemptId, batchNumber, startTimestamp, endTimestamp)
		batches = append(batches, batch)
		batchedRdTxns = append(batchedRdTxns, currentBatchedInvitTxns)
	}
	if len(batches) != len(batchedRdTxns) {
		return nil, nil, caError.ErrNumberOfRecordsMismatch
	}
	return batches, batchedRdTxns, nil
}

func parseRawInvitTxnToProto(ctx context.Context, txn *xml_models.InvitTransaction) (*caPb.RawInvitTransaction, error) {
	txnDate, txnDateErr := datetime.ParseIso8601DateTimeString(txn.TransactionDateTime)
	if txnDateErr != nil {
		return nil, errors.Wrap(txnDateErr, fmt.Sprintf("cannot parse transaction date time stamp for invit txn, received %v", txn.TransactionDateTime))
	}

	return &caPb.RawInvitTransaction{
		TxnId:                  txn.TxnId,
		Isin:                   txn.ISIN,
		TransactionDateTime:    txnDate,
		IsinDescription:        txn.ISINDescription,
		Units:                  txn.Units,
		TransactionDescription: txn.TransactionDescription,
		IssuerName:             txn.IssuerName,
	}, nil
}

// convertToExtProfileDetails converts the raw profile of an account data in DB to external proto message
func (e *InvitProcessor) convertToExtProfileDetails(profile *caPb.Profile) (*caExtPb.InvitProfileDetails, error) {
	if profile == nil || profile.GetAccountProfile() == nil || profile.GetAccountProfile().GetInvitHolders() == nil ||
		profile.GetAccountProfile().GetInvitHolders().GetInvitHolders() == nil {
		return nil, fmt.Errorf("error converting to external profile due to nil invit account profile")
	}

	var holderDetailsList []*caExtPb.InvitHoldersDetails
	for _, holder := range profile.GetAccountProfile().GetInvitHolders().GetInvitHolders() {
		phNum, phErr := strconv.ParseUint(holder.GetMobile(), 10, 64)
		if phErr != nil {
			return nil, fmt.Errorf("error while parsing phone number in invit holders list: %v", holder.GetMobile())
		}
		holderDetailsList = append(holderDetailsList, &caExtPb.InvitHoldersDetails{
			FullName: &commontypes.Name{
				FirstName: holder.GetName(),
			},
			Dob: holder.GetDob(),
			Mobile: &commontypes.PhoneNumber{
				CountryCode:    91,
				NationalNumber: phNum,
			},
			Nominee: holder.GetNominee(),
			DematId: holder.GetDematId(),
			Landline: &commontypes.Landline{
				StdCode: Unknown,
				Number:  holder.GetLandline(),
			},
			Address:       holder.GetAddress(),
			KycCompliance: holder.GetKycCompliance(),
			Email:         holder.GetEmail(),
			Pan:           holder.GetPan(),
		})
	}

	return &caExtPb.InvitProfileDetails{
		InvitHoldersDetails: holderDetailsList,
	}, nil
}
