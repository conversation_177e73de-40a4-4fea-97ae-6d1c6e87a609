// nolint: funlen, unparam, goconst, goimports
package fi_types

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"strings"

	caExternalPb "github.com/epifi/gamma/api/connected_account/external"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/gamma/connectedaccount/metrics"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/data/fi_types/xml_models"
	caError "github.com/epifi/gamma/connectedaccount/error"
	"github.com/epifi/gamma/connectedaccount/typedef"
)

type EquitiesProcessor struct {
	conf                             *genconf.Config
	consentDao                       dao.ConsentDao
	accountDao                       dao.AaAccountDao
	equityDao                        dao.AaEquityFiDao
	transactionDao                   dao.AaTransactionDao
	captureColumnUpdatePub           queue.Publisher
	txnExecutor                      storagev2.TxnExecutor
	txnBatchProcessor                BatchProcessor
	dataValidationProcessor          IDataValidationProcessor
	accountDataSyncExternalPublisher typedef.AccountDataSyncExternalPublisher
}

func NewEquitiesProcessor(conf *genconf.Config, consentDao dao.ConsentDao, accountDao dao.AaAccountDao,
	equityDao dao.AaEquityFiDao, transactionDao dao.AaTransactionDao, captureColumnUpdatePub typedef.CaptureColumnUpdateSqsPublisher,
	txnExecutor storagev2.TxnExecutor, txnBatchProcessor BatchProcessor, dataValidationProcessor IDataValidationProcessor, accountDataSyncExternalPublisher typedef.AccountDataSyncExternalPublisher) *EquitiesProcessor {
	equitiesProcessor := &EquitiesProcessor{
		conf:                             conf,
		consentDao:                       consentDao,
		accountDao:                       accountDao,
		equityDao:                        equityDao,
		transactionDao:                   transactionDao,
		captureColumnUpdatePub:           captureColumnUpdatePub,
		txnExecutor:                      txnExecutor,
		txnBatchProcessor:                txnBatchProcessor,
		dataValidationProcessor:          dataValidationProcessor,
		accountDataSyncExternalPublisher: accountDataSyncExternalPublisher,
	}
	return equitiesProcessor
}
func (e *EquitiesProcessor) StoreAccount(ctx context.Context, dataProcessAttempt *caPb.DataProcessAttempt, dataXml []byte,
	attempt *caPb.DataFetchAttempt) error {
	decoder := xml.NewDecoder(bytes.NewReader(dataXml))
	decoder.Strict = false
	var equityXml xml_models.RawEquityXmlData

	dataDecoderXmlError := decoder.Decode(&equityXml)
	if dataDecoderXmlError != nil {
		return errors.Wrap(dataDecoderXmlError, "StoreEquityAccount: error while unmarshalling of base64 decoded equity account data")
	}

	// Get consent entity - fetch is only done on the consent status column
	consent, consentErr := e.consentDao.Get(ctx, attempt.GetConsentReferenceId(), caPb.ConsentFieldMask_CONSENT_FIELD_MASK_STATUS)
	if consentErr != nil {
		return errors.Wrap(consentErr, fmt.Sprintf("StoreEquityAccount: error getting consent by id: %v",
			attempt.GetConsentReferenceId()))
	}
	// If consent is in deleted status or expired, do not process data further
	// Log error and return nil error
	if consent.GetConsentStatus() == caEnumPb.ConsentStatus_CONSENT_STATUS_DELETED_EPIFI || consent.GetConsentStatus() ==
		caEnumPb.ConsentStatus_CONSENT_STATUS_EXPIRED {
		logger.Info(ctx, "StoreEquityAccount: consent is deleted/expired, not processing further",
			zap.String(logger.ATTEMPT_ID, attempt.GetConsentId()), zap.String(logger.CONSENT_ID, attempt.GetConsentId()),
			zap.String(logger.ACTOR_ID_V2, attempt.GetActorId()))
		return nil
	}

	aaAccount, getErr := e.accountDao.GetByActorIdAndLinkedAccountRef(ctx, attempt.GetActorId(), dataProcessAttempt.GetLinkRefNumber())
	if getErr != nil {
		err := errors.Wrap(getErr, fmt.Sprintf("StoreEquityAccount: error getting account for actor by linked ref "+
			"number for process_attempt_id : %v", dataProcessAttempt.GetId()))
		if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
			metrics.RecordProcessAttemptFailure(GetFipIdFromConsent(consent), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES, metrics.LinkRefNumNotFound)
			logger.Error(ctx, "StoreEquityAccount: link ref number record not found", zap.Error(err),
				zap.Any(logger.ACTOR_ID_V2, attempt.GetActorId()), zap.Any(logger.LINK_REF_NUM, dataProcessAttempt.GetLinkRefNumber()))
			return caError.ErrLinkRefNumNotFound
		}
		return err
	}

	parsedAccount, parsedAccountErr := e.parseAaAccountFromRawXml(ctx, attempt, dataProcessAttempt, &equityXml, aaAccount)
	if parsedAccountErr != nil {
		if strings.Contains(parsedAccountErr.Error(), "getProfileFromEquityXmlData") || errors.Is(parsedAccountErr, caError.ErrNilRawHolders) {
			metrics.RecordProcessAttemptFailure(aaAccount.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES, metrics.NilRawHoldersOrProfileInXml)
		} else {
			metrics.RecordProcessAttemptFailure(aaAccount.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES, metrics.Unknown)
			return errors.Wrap(parsedAccountErr, fmt.Sprintf("StoreEquityAccount: error while parsing aa account info"+
				" from raw equity data for process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
	}

	// data validation for unverified pan,
	// if pan is unverified, performing name check on actor name and name in parsed aa account
	// if pan present, checking for pan number in user profile and parsed aa account pan number
	if e.conf.EnableNameCheckForCADataImport() {
		dataValidationErr := e.dataValidationProcessor.PerformPanAndNameMatchValidation(ctx, parsedAccount)
		if dataValidationErr != nil {
			metrics.RecordProcessAttemptFailure(aaAccount.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES, metrics.NameCheckValidationFailed)
			return errors.Wrap(dataValidationErr, fmt.Sprintf("StoreEquityAccount: error in data validation for unverified pan, process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
	}

	// check diff in immutable fields - discard packet if found
	if vErr := e.checkAaAccountDataValidity(aaAccount, parsedAccount); vErr != nil {
		metrics.RecordProcessAttemptFailure(aaAccount.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES, metrics.AccountValidityFailedInUpdatedXml)
		return errors.Wrap(vErr, fmt.Sprintf("StoreEquityAccount: found diff in immutable fields in account details,"+
			" discarding packet for process_attempt_id : %v", dataProcessAttempt.GetId()))
	}

	parsedEqAcc, equityAccDetailsErr := e.parseSummaryDetailsFromRawXml(ctx, aaAccount, &equityXml)
	if equityAccDetailsErr != nil {
		metrics.RecordProcessAttemptFailure(aaAccount.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES, metrics.ParsingAccSummaryDetailsInXml)
		return errors.Wrap(equityAccDetailsErr, fmt.Sprintf("StoreEquityAccount: error while parsing equity "+
			"account details from raw data for process_attempt_id : %v", dataProcessAttempt.GetId()))
	}

	storedEqAcc, equityAccErr := e.equityDao.GetAccountByAaAccountId(ctx, aaAccount.GetId())
	if equityAccErr != nil {
		if !errors.Is(equityAccErr, epifierrors.ErrRecordNotFound) {
			return errors.Wrap(equityAccErr, fmt.Sprintf("StoreEquityAccount: error getting equity "+
				"account by id for process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
	} else {
		recErr := e.checkSummaryDataAndRecordColumnUpdate(ctx, storedEqAcc, parsedEqAcc)
		if recErr != nil {
			return errors.Wrap(recErr, fmt.Sprintf("StoreEquityAccount: error capturing col update"+
				" for process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
	}
	parsedHoldings, parsedHoldingsErr := e.parseHoldingDetailsFromRawXml(ctx, aaAccount, &equityXml)
	if parsedHoldingsErr != nil {
		metrics.RecordProcessAttemptFailure(aaAccount.GetFipId(), caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES, metrics.ParsingHoldingSummaryDetailsInXml)
		return errors.Wrap(parsedHoldingsErr, fmt.Sprintf("StoreEquityAccount: error parsing holding details from raw xml"+
			" for process_attempt_id : %v", dataProcessAttempt.GetId()))
	}
	txnErr := e.processTxn(ctx, func(txnCtx context.Context) error {
		_, updErr := e.accountDao.Update(txnCtx, parsedAccount, []caPb.AaAccountFieldMask{caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_VERSION,
			caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_PROFILE, caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_STATUS,
			caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_SUB_STATUS, caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_LAST_SYNCED_AT})
		if updErr != nil {
			return errors.Wrap(updErr, fmt.Sprintf("error while updating account info for equities type for process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
		if storedEqAcc == nil {
			_, createErr := e.equityDao.CreateAccount(txnCtx, parsedEqAcc)
			if createErr != nil {
				return errors.Wrap(createErr, fmt.Sprintf("error in create equity account for process_attempt_id : %v", dataProcessAttempt.GetId()))
			}
		} else {
			_, updateErr := e.equityDao.UpdateAccount(txnCtx, parsedEqAcc,
				[]caPb.AaEquityAccountFieldMask{caPb.AaEquityAccountFieldMask_AA_EQUITY_ACCOUNT_FIELD_MASK_CURRENT_VALUE})
			if updateErr != nil {
				return errors.Wrap(updateErr, fmt.Sprintf("error in update equity account for process_attempt_id : %v", dataProcessAttempt.GetId()))
			}
		}

		//	Storing Equity Holdings will be done in 5 steps:
		//		1. get all the stored equity holding for an accountId,
		//		2. Do in-memory processing of all the holdings i.e. Categorise all the holdings which are to be updated/inserted and deleted
		//		3. Perform batchupsert operation on the ones which are to be updated and inserted
		//		4. Perform hard deleted on the ones which are to be deleted.
		// 		5. Storing parsedHoldings in S3

		storedHoldings, storedHoldingsErr := e.equityDao.GetHoldingsByAaAccountId(txnCtx, aaAccount.GetId())
		if storedHoldingsErr != nil && !errors.Is(storedHoldingsErr, epifierrors.ErrRecordNotFound) {
			return errors.Wrap(storedHoldingsErr, fmt.Sprintf("error in getting all the holding for an equity account for process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
		holdingsToBeUpserted, holdingsToBeDeleted, categorizationsErr := e.categorizeOperationsOnHoldings(storedHoldings, parsedHoldings)
		if categorizationsErr != nil {
			return errors.Wrap(categorizationsErr, fmt.Sprintf("error in categorizeOperationsOnHoldings for process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
		if batchUpsertErr := e.equityDao.BatchUpsertEquityHoldings(txnCtx, holdingsToBeUpserted, []caPb.AaEquityHoldingFieldMask{
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_ACCOUNT_ID,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_ISIN,
		}, 100, []caPb.AaEquityHoldingFieldMask{
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_ID,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_ACCOUNT_ID,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_ISIN,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_ISSUER_NAME,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_TYPE,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_META_DATA,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_UNITS,
			caPb.AaEquityHoldingFieldMask_AA_EQUITY_HOLDING_FIELD_MASK_LAST_TRADED_PRICE,
		}); batchUpsertErr != nil {
			return errors.Wrap(batchUpsertErr, fmt.Sprintf("error in BatchUpsertEquityHoldings for process_attempt_id : %v", dataProcessAttempt.GetId()))
		}
		if softDeleteErr := e.equityDao.BatchHardDeleteHoldings(txnCtx,
			func(holdingsToBeDeleted []*caPb.AaEquityHolding) []string {
				var holdingsIds []string
				for _, holding := range holdingsToBeDeleted {
					holdingsIds = append(holdingsIds, holding.GetId())
				}
				return holdingsIds
			}(holdingsToBeDeleted)); softDeleteErr != nil {
			if errors.Is(softDeleteErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "StoreEquityAccount:no equity holdings to be soft deleted", zap.Error(softDeleteErr),
					zap.Any(logger.PROCESS_ATTEMPT_ID, dataProcessAttempt.GetId()))
			} else {
				return errors.Wrap(softDeleteErr, fmt.Sprintf("error in SoftBatchDeleteHoldings for process_attempt_id : %v", dataProcessAttempt.GetId()))
			}
		}
		// TODO: Store Equity Holdings in S3 also
		return nil
	})
	if txnErr != nil {
		return errors.Wrap(txnErr, fmt.Sprintf("transaction error in creating equity account for process_attempt_id : %v", dataProcessAttempt.GetId()))
	}

	if err := e.CreateEquityFiBatches(ctx, dataProcessAttempt.GetId(), aaAccount, dataXml, attempt); err != nil {
		logger.Error(ctx, "StoreAccount: error creating and storing equities Fi batches", zap.String(logger.FIP_ID, aaAccount.GetFipId()),
			zap.String(logger.ATTEMPT_ID, attempt.GetId()), zap.String(logger.ACCOUNT_ID, aaAccount.GetId()), zap.Error(err))
		return err
	}
	logger.Info(ctx, "StoreAccount: successfully created and stored equities Fi batches",
		zap.String(logger.FIP_ID, aaAccount.GetFipId()), zap.String(logger.ATTEMPT_ID, attempt.GetId()),
		zap.String(logger.ACCOUNT_ID, aaAccount.GetId()))

	// TODO(amrit): add this code to a common function if the number of usages increases
	_, err := e.accountDataSyncExternalPublisher.Publish(ctx, &caExternalPb.AccountDataSyncEvent{
		AccInstrumentType: caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
		ActorId:           attempt.GetActorId(),
		AccountId:         storedEqAcc.GetAaAccountId(),
		EventTimestamp:    timestamppb.Now(),
		DataSyncType:      caExternalPb.AccountDataSyncType_ACCOUNT_DATA_SYNC_TYPE_FULL_IMPORT,
	})
	if err != nil {
		return errors.Wrap(err, "error publishing account data sync message")
	}

	return nil
}

func (e *EquitiesProcessor) GetSummaryDetails(ctx context.Context, accountId string) (interface{}, error) {
	equityAccount, equityAccountErr := e.equityDao.GetAccountByAaAccountId(ctx, accountId)
	if equityAccountErr != nil {
		if !errors.Is(equityAccountErr, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(equityAccountErr, fmt.Sprintf("error in getting equity account by account id: %s", accountId))
		}
	}
	equityAccHoldings, equityAccHoldingsErr := e.equityDao.GetHoldingsByAaAccountId(ctx, accountId)
	if equityAccHoldingsErr != nil {
		if !errors.Is(equityAccHoldingsErr, epifierrors.ErrRecordNotFound) {
			return nil, errors.Wrap(equityAccHoldingsErr, fmt.Sprintf("error in getting equity account holdings by account id: %s", accountId))
		}
	}
	equitySummaryDetails, equitySummaryDetailsErr := e.convertToExtEquitySummary(ctx, equityAccount, equityAccHoldings)
	if equitySummaryDetailsErr != nil {
		return nil, errors.Wrap(equityAccountErr, fmt.Sprintf("error in getting equity summary details by account id: %s", accountId))
	}
	return equitySummaryDetails, nil
}

func (e *EquitiesProcessor) GetSummaryDetailsBulk(ctx context.Context, accountIdList []string) (map[string]interface{}, error) {
	// TODO: create bulk dao for equity accounts and separate holdings summary with pagination
	equitiesSummaryDetailsBulk := make(map[string]interface{})
	for _, acc := range accountIdList {
		accSummaryDetail, err := e.GetSummaryDetails(ctx, acc)
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error in getting bulk summary due to account id %v", acc))
		}
		equitiesSummaryDetailsBulk[acc] = accSummaryDetail
	}
	return equitiesSummaryDetailsBulk, nil
}

func (e *EquitiesProcessor) GetTxnDetails(ctx context.Context, txnId string) (caPb.Parser, error) {
	equityTxn, equityErr := e.equityDao.GetTransactionByRefId(ctx, txnId)
	if equityErr != nil {
		return nil, errors.Wrap(equityErr, "error while getting equity txn details")
	}
	return equityTxn, nil
}

func (e *EquitiesProcessor) StoreTxnBatch(ctx context.Context, req *caCoPb.ProcessTransactionsBatchRequest, fipId string, txnEventType caEnumPb.TransactionEventTypeInitiatedBy) error {
	txnErr := e.processTxn(ctx, func(txnCtx context.Context) error {
		var upsertErr error
		_, upsertErr = e.batchUpsertTransactions(txnCtx, req, fipId)
		if upsertErr != nil {
			logger.Error(ctx, "error batch upserting equity txns", zap.Error(upsertErr))
			return errors.Wrap(upsertErr, "error batch upserting equity txns")
		}
		return nil
	})
	if txnErr != nil {
		return errors.Wrap(txnErr, "Error in storing equity txn batches")
	}
	return nil
}

//nolint:dupl
func (e *EquitiesProcessor) CreateEquityFiBatches(ctx context.Context, processAttemptId string,
	acc *caPb.AaAccount, dataXml []byte, attempt *caPb.DataFetchAttempt) error {
	decoder := xml.NewDecoder(bytes.NewReader(dataXml))
	decoder.Strict = false
	var equityXml xml_models.RawEquityXmlData
	dataDecoderXmlError := decoder.Decode(&equityXml)
	if dataDecoderXmlError != nil {
		return errors.Wrap(dataDecoderXmlError, "error while unmarshalling of base64 decoded equity data while creating batches")
	}
	batches, rawEquityTxns, batchErr := e.CreateEquityTxnBatches(ctx, attempt.GetId(), processAttemptId, &equityXml, acc)
	if batchErr != nil {
		return errors.Wrap(batchErr, "error while creating equity txn batches")
	}
	// create batches
	storedBatches, storedBatchesErr := e.txnBatchProcessor.PersistBatches(ctx, batches)
	if storedBatchesErr != nil {
		return errors.Wrap(storedBatchesErr, "error persisting batches in DB for equity transactions")
	}
	if len(storedBatches) != len(batches) {
		return caError.ErrNumberOfRecordsMismatch
	}
	txnEventTypeInitiatedBy := getTxnEventTypeByAttemptInitiatedBy(attempt.GetDataFetchAttemptInitiatedBy())
	for batchIndex, storedBatch := range storedBatches {
		if storedBatch.GetStatus() != caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS {
			if storeErr := e.StoreTxnBatch(ctx, &caCoPb.ProcessTransactionsBatchRequest{
				BatchId:            storedBatch.GetId(),
				FiType:             caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
				AccountReferenceId: acc.GetId(),
				ActorId:            attempt.GetActorId(),
				Transaction: &caCoPb.ProcessTransactionsBatchRequest_RawEquityTransactions{
					RawEquityTransactions: &caPb.RawEquityTransactions{RawEquityTransactionList: rawEquityTxns[batchIndex]}},
				ConsentReferenceId: attempt.GetConsentReferenceId(),
			}, acc.GetFipId(), txnEventTypeInitiatedBy); storeErr != nil {
				return errors.Wrap(storeErr, fmt.Sprintf("error storing batch, batch_id : %v", storedBatch.GetId()))
			}
		}
		if updErr := e.txnBatchProcessor.UpdateBatch(ctx, storedBatch, attempt, batchIndex+1 == len(storedBatches)); updErr != nil {
			return updErr
		}
		logger.Info(ctx, "successfully processed equity FI batch", zap.String(logger.BATCH_ID, storedBatch.GetId()))
	}
	return nil
}

func (e *EquitiesProcessor) GetProfileDetailsBulk(ctx context.Context, accountIdList []string) (map[string]interface{}, error) {
	response := make(map[string]interface{})
	accList, accListErr := e.accountDao.GetBulkById(ctx, accountIdList)
	if accListErr != nil {
		if errors.Is(accListErr, epifierrors.ErrRecordNotFound) {
			return nil, accListErr
		}
		return nil, errors.Wrap(accListErr, "error while getting bulk equity deposit acc profiles")
	}
	for _, acc := range accList {
		externalConvertedProfileDetails, err := e.convertToExtProfileDetails(ctx, acc.GetProfile(), acc.GetFipId())
		if err != nil {
			return nil, errors.Wrap(err, "error while converting bulk equity deposit acc profiles")
		}
		response[acc.GetId()] = externalConvertedProfileDetails
	}
	return response, nil
}

func (e *EquitiesProcessor) GetProfileDetails(ctx context.Context, accountId string) (interface{}, error) {
	acc, accErr := e.accountDao.GetById(ctx, accountId, nil)
	if accErr != nil {
		if errors.Is(accErr, epifierrors.ErrRecordNotFound) {
			return nil, accErr
		}
		return nil, errors.Wrap(accErr, "error while getting equity deposit acc profile")
	}
	externalConvertedProfileDetails, err := e.convertToExtProfileDetails(ctx, acc.GetProfile(), acc.GetFipId())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting equity deposit acc profile")
	}
	return externalConvertedProfileDetails, nil
}

var _ FiEquitiesProcessor = &EquitiesProcessor{}
