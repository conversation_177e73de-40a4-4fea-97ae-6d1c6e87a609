// nolint: dupl
package consumer

import (
	"context"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	nudgePb "github.com/epifi/gamma/api/nudge"
	nudgeDataColletorPb "github.com/epifi/gamma/api/nudge/consumer"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	"github.com/epifi/gamma/rewards/datacollector/model"
	"github.com/epifi/gamma/rewards/metrics"

	"go.uber.org/zap"
)

// ProcessActorNudgeStatusUpdateEvent processes actor nudge status update event
// NOTE: Currently this event will receive only when actor's nudge status is in COMPLETED state.
func (rcs *RewardsConsumerService) ProcessActorNudgeStatusUpdateEvent(ctx context.Context, req *nudgeDataColletorPb.ActorNudgeStatusUpdateEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Debug(ctx, "Collected actor nudge status update event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Stringer("request", req))

	isActorEligible, err := rcs.userHelperClient.IsActorEligibleForEarningRewards(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while checking actor internal status", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.NUDGE_ID, req.GetNudgeId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}

	// If actor is not internal or eligible for reward, then success the event.
	if !isActorEligible {
		logger.Debug(ctx, "actor is not internal", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.NUDGE_ID, req.GetNudgeId()))
		return getSuccessRes(), nil
	}

	nudgeRes, err := rcs.nudgeClient.FetchNudgesByIds(ctx, &nudgePb.FetchNudgesByIdsRequest{NudgeIds: []string{req.GetNudgeId()}})
	if te := epifigrpc.RPCError(nudgeRes, err); te != nil {
		logger.Error(ctx, "error while fetching nudges by id", zap.String(logger.NUDGE_ID, req.GetNudgeId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("entry_event_id", req.GetEntryEventId()), zap.Error(te))
		return getTransientFailureRes(), nil
	}
	// if no nudge is found by given id, then success the event.
	if len(nudgeRes.GetNudges()) == 0 {
		return getSuccessRes(), nil
	}

	// Process only journey-type nudge events for rewards.
	// NOTE: If we decide to reward other nudge types in the future, we can add them here.
	// This helps avoid unnecessary processing by the rewards service, as actor nudge events have a high volume.
	if nudgeRes.GetNudges()[0].GetNudgeType() != nudgePb.NudgeType_NUDGE_TYPE_JOURNEY {
		return getSuccessRes(), nil
	}

	collectedData := &model.ActorNudgeStatusUpdateCollectedData{
		ActorNudgeStatusUpdateEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting actor nudge status update event data model to proto", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}

	msgId, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing actor nudge status update event collected data", zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()), zap.Error(err))
		recordDataCollectorCollectionError(protoCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	_, err = rcs.rewardUnlockerEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing actor nudge status update event to unlocker", zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()), zap.Error(err))
		return getTransientFailureRes(), nil
	}

	logger.Debug(ctx, "Pushed actor nudge status update event reward event", zap.String(logger.QUEUE_MESSAGE_ID, msgId), zap.String(logger.ACTOR_ID_V2, protoCollectedData.GetActorId()), zap.String(logger.ACTOR_NUDGE_ID, protoCollectedData.GetId()))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(protoCollectedData.GetDataType().String())

	return getSuccessRes(), nil
}
