package rewardfulfillment

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/pkg/errors"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	ffPb "github.com/epifi/gamma/api/firefly"
	ffBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ffBeBillingEnumsPb "github.com/epifi/gamma/api/firefly/billing/enums"
	"github.com/epifi/gamma/api/firefly/enums"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	genConf "github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/processor/dao"
)

type CreditCardBillEraserRewardProcessor struct {
	processingRequestDao dao.ProcessingRequestDao
	ffBillingClient      ffBillingPb.BillingClient
	fireflyClient        ffPb.FireflyClient
	dynConf              *genConf.Config
}

func NewCreditCardBillEraserRewardProcessor(processingRequestDao dao.ProcessingRequestDao, ffBillingClient ffBillingPb.BillingClient, fireflyClient ffPb.FireflyClient, dynConf *genConf.Config) *CreditCardBillEraserRewardProcessor {
	return &CreditCardBillEraserRewardProcessor{processingRequestDao: processingRequestDao, ffBillingClient: ffBillingClient, fireflyClient: fireflyClient, dynConf: dynConf}
}

// nolint:funlen
func (c *CreditCardBillEraserRewardProcessor) Process(ctx context.Context, request IRequest) (*Response, error) {
	// get CC bill eraser reward from request
	ccBillEraserReward, ok := request.GetRewardDetails().(*rewardsPb.CreditCardBillEraser)
	if !ok {
		return nil, fmt.Errorf("invalid CC bill eraser reward details in request")
	}
	// if amount is zero, then just return processed status as PROCESSED without
	// updating any processing ref in processingRequest table as no downstream
	// service has to be communicated.
	if ccBillEraserReward.GetAmount().GetUnits() == 0 {
		logger.Info(ctx, "processing zero value CC bill eraser reward", zap.Any("request", request))
		return &Response{Status: ProcessingStatus_PROCESSED}, nil
	}
	// fetch processing request by request id
	processingRequest, err := c.processingRequestDao.GetById(ctx, request.GetProcessingReqId())
	if err != nil {
		return nil, fmt.Errorf("error fetching processing request, err: %w", err)
	}

	// check if user has an active CC because we can't fulfil the reward if user doesn't own an active CC
	// todo: revert after grievance issue is resolved
	/*
		userHasActiveCc, err := c.checkIfUserHasActiveCc(ctx, request.GetActorId())
		if err != nil {
			return nil, fmt.Errorf("error while checking if user owns a CC, err: %w", err)
		}

		if !userHasActiveCc {
			logger.Error(ctx, "can't fulfil CC bill eraser reward as user doesn't own an active CC")
			return &Response{Status: ProcessingStatus_PROCESSING_FAILED}, nil
		}
	*/

	// if processing ref does not exist generate one, as it is used as clientReqId for CC Bill eraser
	if processingRequest.ProcessingRef == "" {
		// setting processing_ref same as processing_request.id as we want a unique ref for
		// each unique processing request and processing request id would provide such uniqueness
		processingRef := processingRequest.Id
		if updateErr := c.processingRequestDao.UpdateProcessingRef(ctx, processingRequest.Id, processingRef); updateErr != nil {
			logger.Error(ctx, "error updating processing ref in processing request", zap.Any("reward_processing_request", request), zap.Error(updateErr))
			return nil, fmt.Errorf("error updating processing ref in processing request, err: %w", err)
		}
		processingRequest.ProcessingRef = processingRef
	}

	paymentRes, err := c.ffBillingClient.GetAutoRepaymentStatus(ctx, &ffBillingPb.GetAutoRepaymentStatusRequest{ClientReqId: processingRequest.ProcessingRef})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching auto repayment status", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, processingRequest.ProcessingRef))
		return nil, fmt.Errorf("error in fetching auto repayment status, err: %w", err)
	case paymentRes.GetStatus().IsRecordNotFound():
		logger.Info(ctx, "no auto repayment found for the user, initiating", zap.String(logger.CLIENT_REQUEST_ID, processingRequest.ProcessingRef))
		if err = c.initiatePaymentRequest(ctx, request.GetActorId(), processingRequest.ProcessingRef, ccBillEraserReward.GetAmount()); err != nil {
			logger.Error(ctx, "error in initiating auto repayment", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, processingRequest.ProcessingRef))
			return nil, errors.Wrap(err, "error in initiating auto repayment")
		}
	case !paymentRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success response from repayment status fetch API", zap.String(logger.CLIENT_REQUEST_ID, processingRequest.ProcessingRef))
		return nil, errors.New("non success response from repayment status fetch API")
	default:
		status := getProcessorResponseFromAutoRepaymentStatus(paymentRes.GetRequestStatus())
		return &Response{Status: status}, nil
	}

	// waiting for some seconds for order to reach the terminal state
	time.Sleep(time.Duration(c.dynConf.RewardsPayoutConfig().TimeToWaitBeforeCheckingOrderStatus()) * time.Second)
	paymentRes, err = c.ffBillingClient.GetAutoRepaymentStatus(ctx, &ffBillingPb.GetAutoRepaymentStatusRequest{ClientReqId: processingRequest.ProcessingRef})
	if rpcErr := epifigrpc.RPCError(paymentRes, err); rpcErr != nil {
		logger.Error(ctx, "error while checking updated status of repayment", zap.String(logger.CLIENT_REQUEST_ID, processingRequest.ProcessingRef))
		return nil, fmt.Errorf("error while checking updated status of repayment, err: %w", rpcErr)
	}

	return &Response{Status: getProcessorResponseFromAutoRepaymentStatus(paymentRes.GetRequestStatus())}, nil
}

func (c *CreditCardBillEraserRewardProcessor) checkIfUserHasActiveCc(ctx context.Context, actorId string) (bool, error) {
	getCreditCardRes, err := c.fireflyClient.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: actorId}})
	if rpcErr := epifigrpc.RPCError(getCreditCardRes, err); rpcErr != nil && !getCreditCardRes.GetStatus().IsRecordNotFound() {
		return false, fmt.Errorf("error while checking if user has a CC, err: %w", err)
	}

	return lo.Contains([]enums.CardState{enums.CardState_CARD_STATE_ACTIVATED, enums.CardState_CARD_STATE_DIGITALLY_ACTIVATED}, getCreditCardRes.GetCreditCard().GetCardState()), nil
}

func getProcessorResponseFromAutoRepaymentStatus(status ffBillingPb.GetAutoRepaymentStatusResponse_RequestStatus) ProcessingStatus {
	switch status {
	case ffBillingPb.GetAutoRepaymentStatusResponse_REQUEST_STATUS_PAYMENT_FAILED:
		return ProcessingStatus_PROCESSING_FAILED
	case ffBillingPb.GetAutoRepaymentStatusResponse_REQUEST_STATUS_PAYMENT_SUCCESSFUL:
		return ProcessingStatus_PROCESSED
	default:
		return ProcessingStatus_PROCESSING_IN_PROGRESS
	}
}

func (c *CreditCardBillEraserRewardProcessor) initiatePaymentRequest(ctx context.Context, actorId, clientReqId string, amount *moneyPb.Money) error {
	initPaymentRes, err := c.ffBillingClient.InitiateAutoRepayment(ctx, &ffBillingPb.InitiateAutoRepaymentRequest{
		ActorId:           actorId,
		Amount:            amount,
		PaymentProvenance: ffBeBillingEnumsPb.PaymentProvenance_PAYMENT_PROVENANCE_FI_COIN_CATALOGUE,
		ClientReqId:       clientReqId,
	})
	if initPaymentRes.GetStatus().IsAlreadyExists() {
		return fmt.Errorf("record already exists for the given identifier, err: %w", epifierrors.ErrAlreadyExists)
	}
	if te := epifigrpc.RPCError(initPaymentRes, err); te != nil {
		return fmt.Errorf("error from initiate auto repayment API : %w", te)
	}
	return nil
}
