// Code generated by MockGen. DO NOT EDIT.
// Source: rewards/clawback/generator/factory.go

// Package mock_generator is a generated GoMock package.
package mock_generator

import (
	context "context"
	reflect "reflect"

	rewards "github.com/epifi/gamma/api/rewards"
	generator "github.com/epifi/gamma/rewards/clawback/generator"
	gomock "github.com/golang/mock/gomock"
)

// MockIFactory is a mock of IFactory interface
type MockIFactory struct {
	ctrl     *gomock.Controller
	recorder *MockIFactoryMockRecorder
}

// MockIFactoryMockRecorder is the mock recorder for MockIFactory
type MockIFactoryMockRecorder struct {
	mock *MockIFactory
}

// NewMockIFactory creates a new mock instance
func NewMockIFactory(ctrl *gomock.Controller) *MockIFactory {
	mock := &MockIFactory{ctrl: ctrl}
	mock.recorder = &MockIFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockIFactory) EXPECT() *MockIFactoryMockRecorder {
	return m.recorder
}

// GetClawbacksGenerator mocks base method
func (m *MockIFactory) GetClawbacksGenerator(ctx context.Context, clawbackEventType rewards.ClawbackEventType, offerType rewards.RewardOfferType) (generator.IClawbacksGenerator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClawbacksGenerator", ctx, clawbackEventType, offerType)
	ret0, _ := ret[0].(generator.IClawbacksGenerator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClawbacksGenerator indicates an expected call of GetClawbacksGenerator
func (mr *MockIFactoryMockRecorder) GetClawbacksGenerator(ctx, clawbackEventType, offerType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClawbacksGenerator", reflect.TypeOf((*MockIFactory)(nil).GetClawbacksGenerator), ctx, clawbackEventType, offerType)
}
