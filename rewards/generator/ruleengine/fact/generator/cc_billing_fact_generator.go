package generator

import (
	"context"
	"fmt"
	"time"

	fireflyPb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/pinot"
	datacollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/creditcardbilling"
	"github.com/epifi/gamma/rewards/helper"
)

type CcBillingFactGenerator struct {
	userHelperSvc       helper.IUserHelperService
	fireflyClient       fireflyPb.FireflyClient
	ffAccClient         ffAccPb.AccountingClient
	txnAggregatesClient pinot.TxnAggregatesClient
	dynConf             *genconf.Config
}

func NewCcBillingFactGenerator(userHelperSvc helper.IUserHelperService, fireflyClient fireflyPb.FireflyClient, ffAccClient ffAccPb.AccountingClient, txnAggregatesClient pinot.TxnAggregatesClient, dynConf *genconf.Config) *CcBillingFactGenerator {
	return &CcBillingFactGenerator{userHelperSvc: userHelperSvc, fireflyClient: fireflyClient, ffAccClient: ffAccClient, txnAggregatesClient: txnAggregatesClient, dynConf: dynConf}
}

func (c *CcBillingFactGenerator) GenerateFacts(ctx context.Context, collectedData *datacollectorPb.CollectedData, rewardOffer *rewardOffersPb.RewardOffer) ([]common.IFact, error) {
	ccBillingEvent := collectedData.GetCreditCardBillingEvent()
	ccBillWindowEndTime := datetime.DateToTime(ccBillingEvent.GetBillWindow().GetToDate(), datetime.IST)
	// CC billing rewards could be generated after some delay to take into account any reconciled txns, therefore return error if the event shouldn't be evaluated now.
	// this will be bypassed if ShouldProcessBillingEventImmediatelyForRewardEvaluation is set
	if !c.dynConf.CreditCardRewardsConfig().ShouldProcessBillingEventImmediatelyForRewardEvaluation && time.Now().Before(ccBillWindowEndTime.Add(c.dynConf.CreditCardRewardsConfig().MinReqDelayForRewardEvalRelativeToBillWindowEndDate)) {
		return nil, fmt.Errorf("cc billing event cannot be evalauted now for reward generation")
	}

	var facts []common.IFact

	facts = append(facts, &creditcardbilling.CcBillingFact{
		CommonFact: &common.CommonFact{
			Ctx:                  ctx,
			RewardOffer:          rewardOffer,
			ActorId:              ccBillingEvent.GetActorId(),
			RefId:                collectedData.GetId(),
			ActionType:           collectedData.GetDataType(),
			ActionTime:           collectedData.GetActionTime(),
			ActionCollectionTime: collectedData.GetCreationTime(),
			UserHelperService:    c.userHelperSvc,
		},
		CreditCardBillingEvent: ccBillingEvent,
		FfAccountingClient:     c.ffAccClient,
		FireflyClient:          c.fireflyClient,
		TxnAggregatesClient:    c.txnAggregatesClient,
		DynConf:                c.dynConf,
	})

	return facts, nil
}
