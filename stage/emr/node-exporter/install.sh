#!/bin/bash

sudo curl -L -O  https://github.com/prometheus/node_exporter/releases/download/v0.17.0/node_exporter-0.17.0.linux-amd64.tar.gz

sudo tar -xzvf node_exporter-0.17.0.linux-amd64.tar.gz
sudo mv node_exporter-0.17.0.linux-amd64 /home/<USER>/node_exporter
sudo rm node_exporter-0.17.0.linux-amd64.tar.gz
sudo chown -R hadoop /home/<USER>/node_exporter

sudo rm /etc/systemd/system/node_exporter.service
# Add node_exporter as systemd service
sudo tee -a /etc/systemd/system/node_exporter.service << END
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target
[Service]
User=hadoop
ExecStart=/home/<USER>/node_exporter/node_exporter
[Install]
WantedBy=default.target
END

sudo systemctl daemon-reload
sudo systemctl start node_exporter
sudo systemctl enable node_exporter