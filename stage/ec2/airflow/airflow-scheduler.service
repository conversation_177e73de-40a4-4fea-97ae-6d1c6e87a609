[Unit]
Description=Airflow scheduler daemon
After=network.target postgresql.service mysql.service redis.service rabbitmq-server.service
Wants=postgresql.service mysql.service redis.service rabbitmq-server.service
[Service]
RuntimeDirectory=/home/<USER>/airflow
RuntimeDirectoryMode=0775
User=ubuntu
Type=simple
ExecStart=/usr/bin/bash -c 'source /home/<USER>/airflow_venv/bin/activate; airflow scheduler'
Restart=always
RestartSec=5s
PrivateTmp=true
[Install]
WantedBy=multi-user.target