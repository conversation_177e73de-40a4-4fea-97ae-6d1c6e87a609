package dao

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"

	"github.com/epifi/be-common/pkg/logger"

	_ "github.com/lib/pq"
	"gorm.io/gorm"

	"github.com/epifi/gamma/simulator/dao/model"
)

// CustomerStorage implements the storage for the customer data in simulator
type CustomerStorage struct {
	DB *sql.DB

	// TODO(kunal): Will rename to DB later
	Db *gorm.DB
}

// NewCustomerStorage  an instance of storage. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewCustomerStorage(db *sql.DB, gormDb *gorm.DB) *CustomerStorage {
	return &CustomerStorage{DB: db, Db: gormDb}
}

func (cs *CustomerStorage) CreateCustomerNew(customer *model.Customer) (*model.Customer, error) {
	if err := cs.Db.Create(customer).Error; err != nil {
		return nil, fmt.Errorf("failed to insert customer: %w", err)
	}

	return customer, nil
}

func (cs *CustomerStorage) CheckCustomerExistsByPhoneNumber(phoneNumber string) bool {
	var customer model.Customer
	err := cs.Db.Where("phone_no = ?", phoneNumber).First(&customer).Error
	return err == nil
}

func (cs *CustomerStorage) GetCustomerExistsByPhoneNumber(phoneNumber string) (*model.Customer, error) {
	var customer model.Customer
	err := cs.Db.Where("phone_no = ?", phoneNumber).First(&customer).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get customer by phone number, %s: %w", phoneNumber, err)
	}
	return &customer, nil
}

func (cs *CustomerStorage) GetCustomerByRequestId(requestId string) (*model.Customer, error) {
	var customer model.Customer
	if err := cs.Db.Where("request_id = ?", requestId).First(&customer).Error; err != nil {
		return nil, fmt.Errorf("failed to get customer from DB, requestId: %s: %w", requestId, err)
	}

	return &customer, nil
}

func (cs *CustomerStorage) GetCustomerByCustomerId(custId string) (*model.Customer, error) {
	var customer model.Customer
	if err := cs.Db.Where("id = ?", custId).First(&customer).Error; err != nil {
		return nil, fmt.Errorf("failed to get customer from DB, customerId: %s: %w", custId, err)
	}

	return &customer, nil
}

// CreateCustomer writes the customer data to the persistent storage
func (cs *CustomerStorage) CreateCustomer(profile model.CustomerProfile) string {
	jsonstr, err := json.Marshal(profile)
	if err != nil {
		log.Panicf("Woops: %v", err)
	}

	//query := fmt.Sprintf("INSERT INTO simulator.customers (profile) VALUES ('%s') RETURNING id", string(jsonstr))
	//log.Printf("Executing: %s", query)

	// TODO(shankar): Fix lint check
	rows, err := cs.DB.Query("INSERT INTO simulator.customers (profile) VALUES ($1) RETURNING id", string(jsonstr)) //nolint:rowserrcheck
	if err != nil {
		log.Panicf("Error while creating customer: %v", err)
	}

	var id string
	if !rows.Next() {
		log.Panicln("Something weird happened. RETURNING doesn't seem to be working correctly.")
	}
	if err := rows.Scan(&id); err != nil {
		log.Println("Failed to scan. Err: ", err)
	}
	return id
}

// CreateCustomerAsync writes the customer data to the persistent storage and returns a
// channel that the caller can wait on. Returns 0 on the channel when the underlying operation is done.
func (cs *CustomerStorage) CreateCustomerAsync(profile model.CustomerProfile) <-chan int {
	done := make(chan int)
	go func() {
		cs.CreateCustomer(profile)
		done <- 0
	}()
	return done
}

// GetCustomerByID reads a Customer from the database given the customer id.
func (cs *CustomerStorage) GetCustomerByID(id string) model.Customer {
	row := cs.DB.QueryRow("SELECT profile FROM simulator.customers WHERE id = $1", id)
	var profileJSON []byte
	var profile model.CustomerProfile
	if err := row.Scan(&profileJSON); err != nil {
		log.Fatalf("Did not find customer. The following error occured: %v", err)
	}
	if err := json.Unmarshal(profileJSON, &profile); err != nil {
		log.Fatal(err)
	}
	log.Println("Read a customer with id", id)
	return model.Customer{ID: id, Profile: profile}
}

// GetCustomerByIDInTx reads a Customer from the database given the
// customer id, transactionally.
func GetCustomerByIDInTx(id string, tx *gorm.DB) (*model.Customer, error) {
	var cust model.Customer
	if err := tx.Model(&model.Customer{}).Where("id = ?", id).Take(&cust).Error; err != nil {
		return nil, err
	}
	logger.InfoNoCtx(fmt.Sprintf("Read a customer with id: %v, profile: %v", id, cust.Profile))
	return &cust, nil
}

// GetCustomerByIDAsync reads a Customer from the database given the customer id and returns a
// channel that the caller can wait on.
func (cs *CustomerStorage) GetCustomerByIDAsync(id string) chan model.Customer {
	done := make(chan model.Customer)
	go func() {
		done <- cs.GetCustomerByID(id)
	}()
	return done
}

// UpdateCustomer updates the customer. The updateMask controls what information is actually updated.
//
//nolint:gocritic
func UpdateCustomer(customer model.Customer, updateMask []model.CustomerMask, tx *gorm.DB) error {
	if len(updateMask) == 0 {
		return errors.New("Update unsuccessful. No updateMask specified. It is necessary to specify" +
			" it so only the desired information can be updated.")
	}
	customerToUpdate, err := GetCustomerByIDInTx(customer.ID, tx)
	if err != nil {
		return err
	}

	var profileUpdate, phoneUpdate bool
	for _, mask := range updateMask {
		switch mask {
		case model.FIRST_NAME:
			customerToUpdate.Profile.FirstName = customer.Profile.FirstName
			profileUpdate = true
		case model.LAST_NAME:
			customerToUpdate.Profile.LastName = customer.Profile.LastName
			profileUpdate = true
		case model.ADDRESS:
			customerToUpdate.Profile.Address = customer.Profile.Address
			profileUpdate = true
		case model.PHONE_NO:
			customerToUpdate.PhoneNo = customer.PhoneNo
			phoneUpdate = true
		}
	}
	jsonstr, _ := json.Marshal(customer.Profile)

	q := tx.Model(&model.Customer{}).
		Where("id = ?", customer.ID)

	if profileUpdate {
		q = q.Update("profile", jsonstr)
	}
	if phoneUpdate {
		q = q.Update("phone_no", customerToUpdate.PhoneNo)
	}

	if err := q.Error; err != nil {
		return fmt.Errorf("error updating customer: %v", err)
	}
	return err
}
