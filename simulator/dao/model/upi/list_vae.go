package upi

import (
	"encoding/xml"

	"github.com/epifi/gamma/api/vendors/federal/upi"
)

type ListVaeRequest struct {
	XMLName        xml.Name           `xml:"UPIMerchantListVaeReq"`
	Text           string             `xml:",chardata"`
	Fed            string             `xml:"fed,attr"`
	Upi            string             `xml:"upi,attr"`
	Xsi            string             `xml:"xsi,attr"`
	SchemaLocation string             `xml:"schemaLocation,attr"`
	MerchantHeader upi.MerchantHeader `xml:"MerchantHeader"`
	MerchantBody   struct {
		ReqListVae struct {
			Head upi.Head        `xml:"Head"`
			Txn  upi.Transaction `xml:"Txn"`
		} `xml:"ReqListVae"`
	} `xml:"MerchantBody"`
}
