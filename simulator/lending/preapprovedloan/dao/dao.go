//go:generate dao_metrics_gen .
package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	simPreapprovedPb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"

	"github.com/google/wire"
)

var (
	VendorResponseDaoWireSet = wire.NewSet(NewCrdbResponseDao, wire.Bind(new(VendorResponseDao), new(*CrdbVendorResponseDao)))
)

type VendorResponseDao interface {
	Create(ctx context.Context, res *simPreapprovedPb.VendorResponse) (*simPreapprovedPb.VendorResponse, error)
	GetById(ctx context.Context, id string, responseType simPreapprovedPb.VendorResponseType, vendor commonvgpb.Vendor) (*simPreapprovedPb.VendorResponse, error)
	Update(ctx context.Context, res *simPreapprovedPb.VendorResponse) error
	GetByApplication(ctx context.Context, applicationNumber string, responseType simPreapprovedPb.VendorResponseType, vendor commonvgpb.Vendor) (*simPreapprovedPb.VendorResponse, error)
	GetApplicationsById(ctx context.Context, id string, vendor commonvgpb.Vendor) ([]*simPreapprovedPb.VendorResponse, error)
}
