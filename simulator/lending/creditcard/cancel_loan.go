// nolint
package creditcard

import (
	"context"
	"errors"

	simcreditCardPb "github.com/epifi/gamma/api/simulator/lending/creditcard"
	ccPb "github.com/epifi/gamma/api/vendors/m2p/lending/creditcard"
	lmsVendorPb "github.com/epifi/gamma/api/vendors/m2p/lending/lms"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/simulator/lending/creditcard/dao/model"
)

func (s *Service) CancelLoan(ctx context.Context, req *lmsVendorPb.CancelLoanRequest) (*lmsVendorPb.CancelLoanResponse, error) {
	var (
		resp = &lmsVendorPb.CancelLoanResponse{}
	)
	loanId := req.GetLoanId()

	loanAccountInfo, err := s.CreditCardLoanAccountsDao.GetById(ctx, loanId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return s.getCancelLoanResponseInvalidId(), nil
	case err != nil:
		resp.Exception = getGenericException()
		return resp, nil
	}

	loanAccountInfo.Status = simcreditCardPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CANCELLED
	if err = s.CreditCardLoanAccountsDao.Update(ctx, loanAccountInfo); err != nil {
		return nil, err
	}

	response := generateCancelLoanResponse(loanAccountInfo)
	return response, nil
}

func generateCancelLoanResponse(loanInfo *model.CreditCardLoanAccounts) *lmsVendorPb.CancelLoanResponse {
	totalExpectedRepaymentMoney := loanInfo.RepaymentInfo.GetTotalExpectedRepayment()
	totalExpectedRepaymentFloat := float64(totalExpectedRepaymentMoney.GetUnits()) + float64(totalExpectedRepaymentMoney.GetNanos()/**********.0)
	return &lmsVendorPb.CancelLoanResponse{
		Result: &lmsVendorPb.CancelLoanResponseResult{
			TotalPayableAmount: totalExpectedRepaymentFloat,
			Interest:           loanInfo.InterestInfo.GetInterestRate(),
			InterestTax:        50,
			Fee:                100,
			FeeTax:             100,
		},
	}
}

func (s *Service) getCancelLoanResponseInvalidId() *lmsVendorPb.CancelLoanResponse {
	return &lmsVendorPb.CancelLoanResponse{
		Exception: &ccPb.Exception{
			ErrorCode:        "LE_018",
			Message:          "Invalid loan id",
			LocalizedMessage: "Invalid loan id",
		},
	}
}
