package fiftyfin_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/api/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	vgFiftyfinPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	vendorsFiftyfin "github.com/epifi/gamma/api/vendors/fiftyfin"
	fiftyfinDataProvider "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/fiftyfin"
)

func TestBaseProvider_GetRepaymentTimeline(t *testing.T) {
	t.Parallel()

	type args struct {
		ctx                      context.Context
		loanRepaymentDataRequest *fiftyfinDataProvider.LoanRepaymentDataRequest
	}
	tests := []struct {
		name    string
		args    args
		want    []time.Time
		wantErr bool
	}{
		{
			name: "successfully get repayment timeline if loan started before 10th",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "09-Sep-2023",
									ContractEndDate:   "31-Mar-2024",
								},
							},
						},
					},
					LoanInstallmentPayouts: []*preapprovedloan.LoanInstallmentPayout{},
				},
			},
			want: []time.Time{
				time.Date(2023, 10, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2023, 11, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2023, 12, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 1, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 3, 31, 0, 0, 0, 0, datetime.IST),
			},
			wantErr: false,
		},
		{
			name: "successfully get repayment timeline if loan started after 10th",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "23-Sep-2023",
									ContractEndDate:   "29-Feb-2024",
								},
							},
						},
					},
					LoanInstallmentPayouts: []*preapprovedloan.LoanInstallmentPayout{},
				},
			},
			want: []time.Time{
				time.Date(2023, 11, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2023, 12, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 1, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 2, 29, 0, 0, 0, 0, datetime.IST),
			},
			wantErr: false,
		},
		{
			name: "successfully get repayment timeline if loan started on 10th",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "10-Dec-2023",
									ContractEndDate:   "30-Apr-2024",
								},
							},
						},
					},
					LoanInstallmentPayouts: []*preapprovedloan.LoanInstallmentPayout{},
				},
			},
			want: []time.Time{
				time.Date(2024, 1, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 4, 30, 0, 0, 0, 0, datetime.IST),
			},
			wantErr: false,
		},
		{
			name: "successfully get repayment timeline if loan end date is before 7th",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "04-Apr-2024",
								},
							},
						},
					},
					LoanInstallmentPayouts: []*preapprovedloan.LoanInstallmentPayout{},
				},
			},
			want: []time.Time{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 4, 4, 0, 0, 0, 0, datetime.IST),
			},
			wantErr: false,
		},
		{
			name: "successfully get repayment timeline id entries already exist in installment payout table",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanSoaResp: nil,
					LoanInstallmentPayouts: []*preapprovedloan.LoanInstallmentPayout{
						{
							VendorInstallmentId: "id1",
							DueDate:             &date.Date{Year: 2023, Month: 10, Day: 7},
						},
						{
							VendorInstallmentId: "id2",
							DueDate:             &date.Date{Year: 2023, Month: 11, Day: 7},
						},
						{
							VendorInstallmentId: "id3",
							DueDate:             &date.Date{Year: 2023, Month: 12, Day: 7},
						},
						{
							VendorInstallmentId: "id4",
							DueDate:             &date.Date{Year: 2024, Month: 1, Day: 7},
						},
						{
							VendorInstallmentId: "id5",
							DueDate:             &date.Date{Year: 2024, Month: 1, Day: 31},
						},
					},
				},
			},
			want: []time.Time{
				time.Date(2023, 10, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2023, 11, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2023, 12, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 1, 7, 0, 0, 0, 0, datetime.IST),
				time.Date(2024, 1, 31, 0, 0, 0, 0, datetime.IST),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)

			p := InitBaseProvider(f)
			got, err := p.GetRepaymentTimeline(tt.args.ctx, tt.args.loanRepaymentDataRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRepaymentTimeline() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRepaymentTimeline() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBaseProvider_GetMinDueSchedule(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx                      context.Context
		loanRepaymentDataRequest *fiftyfinDataProvider.LoanRepaymentDataRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *BaseProviderMockFields)
		want       map[time.Time]*fiftyfinDataProvider.EmiDetails
		wantErr    bool
	}{
		{
			name: "successfully get min due schedule - current date is before 1st installment",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             12000,
									InterestAccruedNotDue: 83.17,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 January 2024": 12083.17,
									"25 January 2024": 12086.78,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        116,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        105,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        112,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        109,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get min due schedule - current date is between 1st and last installment",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             12000,
									InterestAccruedNotDue: 83.17,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 March 2024": 12010.0,
									"25 March 2024": 12013.61,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 3, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        116,
						CurrencyCode: "INR",
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        109,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get min due schedule - current date is after last installment",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             12000,
									InterestAccruedNotDue: 83.17,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 May 2024": 12010.0,
									"25 May 2024": 12013.61,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 5, 23, 12, 1, 1, 0, datetime.IST))
			},
			want:    map[time.Time]*fiftyfinDataProvider.EmiDetails{},
			wantErr: false,
		},
		{
			name: "successfully get min due schedule - current date is between 1st and 7th day of the month",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             25209,
									InterestAccruedNotDue: 250.23,
									OutstandingInterest:   0,
								},
								PaymentAmountWithDates: map[string]float64{
									"03 February 2024": 25459.23,
									"04 February 2024": 25466.46,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "29-Nov-2023",
									ContractEndDate:   "30-Nov-2024",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 2, 2, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        243,
						CurrencyCode: "INR",
						Nanos:        0,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        210,
						CurrencyCode: "INR",
						Nanos:        0,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        225,
						CurrencyCode: "INR",
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        217,
						Nanos:        0,
					},
				},
				time.Date(2024, 6, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 6, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
				time.Date(2024, 7, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 7, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        217,
						Nanos:        0,
					},
				},
				time.Date(2024, 8, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 8, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
				time.Date(2024, 9, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 9, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
				time.Date(2024, 10, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 10, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        217,
						Nanos:        0,
					},
				},
				time.Date(2024, 11, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 11, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get min due schedule - current date is 7th day of the month",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             25209,
									InterestAccruedNotDue: 43.38,
									OutstandingInterest:   243,
								},
								PaymentAmountWithDates: map[string]float64{
									"08 February 2024": 25502.61,
									"09 February 2024": 25509.84,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "29-Nov-2023",
									ContractEndDate:   "30-Nov-2024",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 2, 7, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        243,
						CurrencyCode: "INR",
						Nanos:        0,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        210,
						CurrencyCode: "INR",
						Nanos:        0,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        225,
						CurrencyCode: "INR",
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        217,
						Nanos:        0,
					},
				},
				time.Date(2024, 6, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 6, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
				time.Date(2024, 7, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 7, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        217,
						Nanos:        0,
					},
				},
				time.Date(2024, 8, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 8, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
				time.Date(2024, 9, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 9, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
				time.Date(2024, 10, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 10, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        217,
						Nanos:        0,
					},
				},
				time.Date(2024, 11, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 11, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        225,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get min due schedule - foreclosure principal is less than 0, interest accrued is more than 0",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             -100,
									InterestAccruedNotDue: 126.57,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 March 2024": 26.57,
									"25 March 2024": 26.57,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
								},
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 3, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						Units:        127,
						CurrencyCode: "INR",
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get min due schedule - daily interest value is not correct in foreclosure api",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             12000,
									InterestAccruedNotDue: 83.17,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 January 2024": 12083.17,
									"25 January 2024": 12083.17,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
								},
							},
						},
					},
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        116,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        105,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        113,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        109,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get min due schedule - PaymentAmountWithDates is empty",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             12000,
									InterestAccruedNotDue: 83.17,
								},
								PaymentAmountWithDates: map[string]float64{},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
								},
							},
						},
					},
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
							LoanTenure:   "5",
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        116,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        105,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        113,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        109,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			p := InitBaseProvider(f)
			got, err := p.GetMinDueSchedule(tt.args.ctx, tt.args.loanRepaymentDataRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMinDueSchedule() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetMinDueSchedule() got = %v,\n want %v \n diff %v", got, tt.want, diff)
			}
		})
	}
}

func TestBaseProvider_GetEmiSchedule(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx                      context.Context
		loanRepaymentDataRequest *fiftyfinDataProvider.LoanRepaymentDataRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *BaseProviderMockFields)
		want       map[time.Time]*fiftyfinDataProvider.EmiDetails
		wantErr    bool
	}{
		{
			name: "successfully get emi schedule - zero principal amount is repaid",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
							LoanTenure:   "5",
						},
					},
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             12000,
									InterestAccruedNotDue: 83.17,
									TotalDue:              12083.17,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 January 2024": 12083.17,
									"25 January 2024": 12086.78,
								},
								NetCharges: &vendorsFiftyfin.NetCharges{
									TotalDue:    12083.17,
									TotalCredit: 0,
									NetPayable:  12083.17,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
									LoanAmount:        12000,
								},
							},
						},
					},
					LoanOffer: &preapprovedloan.LoanOffer{
						Vendor: preapprovedloan.Vendor_FIFTYFIN,
						ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
							InterestRate: []*preapprovedloan.RangeData{
								{
									Value: &preapprovedloan.RangeData_Percentage{
										Percentage: 11,
									},
								},
							},
						},
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					},
					LoanAccount: &palPb.LoanAccount{
						Id: "loanAccountId1",
						LoanAmountInfo: &palPb.LoanAmountInfo{
							DisbursedAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        12000,
								Nanos:        0,
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2484,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get emi schedule - some principal amount is repaid",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
							LoanTenure:   "5",
						},
					},
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             4000,
									InterestAccruedNotDue: 26.52,
									TotalDue:              4026.52,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 March 2024": 4027.72,
									"25 March 2024": 4028.92,
								},
								NetCharges: &vendorsFiftyfin.NetCharges{
									TotalDue:    4026.52,
									TotalCredit: 0,
									NetPayable:  4026.52,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
									LoanAmount:        4000,
								},
							},
						},
					},
					LoanOffer: &preapprovedloan.LoanOffer{
						Vendor: preapprovedloan.Vendor_FIFTYFIN,
						ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
							InterestRate: []*preapprovedloan.RangeData{
								{
									Value: &preapprovedloan.RangeData_Percentage{
										Percentage: 11,
									},
								},
							},
						},
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					},
					LoanAccount: &palPb.LoanAccount{
						Id: "loanAccountId1",
						LoanAmountInfo: &palPb.LoanAmountInfo{
							DisbursedAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        12000,
								Nanos:        0,
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 3, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1592,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get emi schedule - last emi is left",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
							LoanTenure:   "5",
						},
					},
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             4000,
									InterestAccruedNotDue: 26.52,
									TotalDue:              4026.52,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 March 2024": 4027.72,
									"25 March 2024": 4028.92,
								},
								NetCharges: &vendorsFiftyfin.NetCharges{
									TotalDue:    4026.52,
									TotalCredit: 0,
									NetPayable:  4026.52,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
									LoanAmount:        4000,
								},
							},
						},
					},
					LoanOffer: &preapprovedloan.LoanOffer{
						Vendor: preapprovedloan.Vendor_FIFTYFIN,
						ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
							InterestRate: []*preapprovedloan.RangeData{
								{
									Value: &preapprovedloan.RangeData_Percentage{
										Percentage: 11,
									},
								},
							},
						},
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					},
					LoanAccount: &palPb.LoanAccount{
						Id: "loanAccountId1",
						LoanAmountInfo: &palPb.LoanAmountInfo{
							DisbursedAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        12000,
								Nanos:        0,
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 5, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        4037,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get emi schedule - principal amount is fully repaid, which partially covers interest and other charges",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
							LoanTenure:   "5",
						},
					},
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             -120,
									InterestAccruedNotDue: 50.0,
									TotalDue:              30.0,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 March 2024": 20.0,
									"25 March 2024": 20.0,
								},
								NetCharges: &vendorsFiftyfin.NetCharges{
									TotalDue:    30.0,
									TotalCredit: 0,
									NetPayable:  30.0,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
									LoanAmount:        0,
								},
							},
						},
					},
					LoanOffer: &preapprovedloan.LoanOffer{
						Vendor: preapprovedloan.Vendor_FIFTYFIN,
						ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
							InterestRate: []*preapprovedloan.RangeData{
								{
									Value: &preapprovedloan.RangeData_Percentage{
										Percentage: 11,
									},
								},
							},
						},
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					},
					LoanAccount: &palPb.LoanAccount{
						Id: "loanAccountId1",
						LoanAmountInfo: &palPb.LoanAmountInfo{
							DisbursedAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        12000,
								Nanos:        0,
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 3, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        30,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get emi schedule - principal amount is fully repaid, covers interest and other charges - extra pending amount",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
							LoanTenure:   "5",
						},
					},
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             -120,
									InterestAccruedNotDue: 0.0,
									TotalDue:              0.0,
								},
								PaymentAmountWithDates: map[string]float64{
									"24 March 2024": 20.0,
									"25 March 2024": 20.0,
								},
								TotalCredit: &vendorsFiftyfin.TotalCredit{
									AdvanceInterest: 0,
									ExcessMargin:    100.0,
									TotalCredit:     100.0,
								},
								NetCharges: &vendorsFiftyfin.NetCharges{
									TotalDue:    30.0,
									TotalCredit: 100.0,
									NetPayable:  -70.0,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
									LoanAmount:        0,
								},
							},
						},
					},
					LoanOffer: &preapprovedloan.LoanOffer{
						Vendor: preapprovedloan.Vendor_FIFTYFIN,
						ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
							InterestRate: []*preapprovedloan.RangeData{
								{
									Value: &preapprovedloan.RangeData_Percentage{
										Percentage: 11,
									},
								},
							},
						},
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					},
					LoanAccount: &palPb.LoanAccount{
						Id: "loanAccountId1",
						LoanAmountInfo: &palPb.LoanAmountInfo{
							DisbursedAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        12000,
								Nanos:        0,
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 3, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get emi schedule - daily interest is present in foreclouse api",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.105,
							LoanTenure:   "12",
						},
					},
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             25209,
									InterestAccruedNotDue: 43.38,
									OutstandingInterest:   243,
									TotalDue:              25495.38,
								},
								PaymentAmountWithDates: map[string]float64{
									"09 February 2024": 25502.61,
									"08 February 2024": 25509.84,
								},
								NetCharges: &vendorsFiftyfin.NetCharges{
									TotalDue:    25495.38,
									TotalCredit: 0,
									NetPayable:  25495.38,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "29-Nov-2023",
									ContractEndDate:   "30-Nov-2024",
									LoanAmount:        25209, // This value should not be used for original EMI calculation, as it has pending loan amount at that point in time
								},
							},
						},
					},
					LoanOffer: &preapprovedloan.LoanOffer{
						Vendor: preapprovedloan.Vendor_FIFTYFIN,
						ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
							InterestRate: []*preapprovedloan.RangeData{
								{
									Value: &preapprovedloan.RangeData_Percentage{
										Percentage: 10.5,
									},
								},
							},
						},
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					},
					LoanAccount: &palPb.LoanAccount{
						Id: "loanAccountId1",
						LoanAmountInfo: &palPb.LoanAmountInfo{
							DisbursedAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        31115,
								Nanos:        0,
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 6, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 6, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 7, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 7, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 8, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 8, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 9, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 9, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 10, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 10, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2743,
						Nanos:        0,
					},
				},
				time.Date(2024, 11, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 11, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1641,
						Nanos:        0,
					},
				},
				time.Date(2024, 11, 30, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 11, 30, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        0,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get emi schedule - PaymentAmountWithDates is null",
			args: args{
				ctx: context.Background(),
				loanRepaymentDataRequest: &fiftyfinDataProvider.LoanRepaymentDataRequest{
					LoanDetailsResp: &vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.IndividualLoanData{
							InterestRate: 0.11,
							LoanTenure:   "5",
						},
					},
					LoanForeclosureResp: &vgFiftyfinPb.GetLoanForeclosureStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanForeclosureStatementData{
							PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
								OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
									Principal:             12000,
									InterestAccruedNotDue: 83.17,
									TotalDue:              12083.17,
								},
								PaymentAmountWithDates: map[string]float64{},
								NetCharges: &vendorsFiftyfin.NetCharges{
									TotalDue:    12083.17,
									TotalCredit: 0,
									NetPayable:  12083.17,
								},
							},
						},
					},
					LoanSoaResp: &vgFiftyfinPb.GetLoanSoaStatementResponse{
						Status: rpcPb.StatusOk(),
						Data: &vendorsFiftyfin.LoanSoaStatementData{
							SoaData: &vendorsFiftyfin.LoanSoa{
								LoanDetails: &vendorsFiftyfin.LoanDetails{
									ContractStartDate: "01-Jan-2024",
									ContractEndDate:   "31-May-2024",
									LoanAmount:        12000,
								},
							},
						},
					},
					LoanOffer: &preapprovedloan.LoanOffer{
						Vendor: preapprovedloan.Vendor_FIFTYFIN,
						ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
							InterestRate: []*preapprovedloan.RangeData{
								{
									Value: &preapprovedloan.RangeData_Percentage{
										Percentage: 11,
									},
								},
							},
						},
						LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
					},
					LoanAccount: &palPb.LoanAccount{
						Id: "loanAccountId1",
						LoanAmountInfo: &palPb.LoanAmountInfo{
							DisbursedAmount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        12000,
								Nanos:        0,
							},
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST))
			},
			want: map[time.Time]*fiftyfinDataProvider.EmiDetails{
				time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 2, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 3, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 4, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 7, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
				},
				time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST): {
					DueDate: time.Date(2024, 5, 31, 0, 0, 0, 0, datetime.IST),
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2485,
						Nanos:        0,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			p := InitBaseProvider(f)
			got, err := p.GetEmiSchedule(tt.args.ctx, tt.args.loanRepaymentDataRequest)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEmiSchedule() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetEmiSchedule() got = %v,\n want %v \n diff %v", got, tt.want, diff)
			}
		})
	}
}

func TestBaseProvider_GetUpdatedInstallmentPayouts(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx         context.Context
		actorId     string
		userId      int
		loanId      int
		loanAccount *palPb.LoanAccount
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *BaseProviderMockFields)
		want       []*palPb.LoanInstallmentPayout
		wantErr    bool
	}{
		{
			name: "successfully get installment payouts for the first time",
			args: args{
				ctx:     context.Background(),
				actorId: "actorId",
				userId:  1234,
				loanId:  4567,
				loanAccount: &palPb.LoanAccount{
					Id: "loanId1",
					LoanAmountInfo: &palPb.LoanAmountInfo{
						DisbursedAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        12000,
							Nanos:        0,
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 1, 23, 12, 1, 1, 0, datetime.IST)).Times(3)

				f.vgClient.EXPECT().FetchIndividualLoanDetails(gomock.Any(), &vgFiftyfinPb.FetchIndividualLoanDetailsRequest{
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.IndividualLoanData{
						InterestRate: 0.11,
						LoanTenure:   "5",
					},
				}, nil)

				f.vendorDataProvider.EXPECT().GetLoanForeclosureStatement(gomock.Any(), &vgFiftyfinPb.GetLoanForeclosureStatementRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.GetLoanForeclosureStatementResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.LoanForeclosureStatementData{
						PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
							OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
								Principal:             12000,
								InterestAccruedNotDue: 83.17,
								TotalDue:              12083.17,
							},
							PaymentAmountWithDates: map[string]float64{
								"24 January 2024": 12083.17,
								"25 January 2024": 12086.78,
							},
						},
					},
				}, nil)

				f.vendorDataProvider.EXPECT().GetLoanSoaStatement(gomock.Any(), &vgFiftyfinPb.GetLoanSoaStatementRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.GetLoanSoaStatementResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.LoanSoaStatementData{
						SoaData: &vendorsFiftyfin.LoanSoa{
							LoanDetails: &vendorsFiftyfin.LoanDetails{
								ContractStartDate: "01-Jan-2024",
								ContractEndDate:   "31-May-2024",
								LoanAmount:        12000,
							},
						},
					},
				}, nil)

				f.loanOffersDao.EXPECT().GetLatestByActorIdVendorAndLoanProgram(gomock.Any(), "actorId", palPb.Vendor_FIFTYFIN, palPb.LoanProgram_LOAN_PROGRAM_LAMF).Return(&preapprovedloan.LoanOffer{
					Vendor: preapprovedloan.Vendor_FIFTYFIN,
					ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
						InterestRate: []*preapprovedloan.RangeData{
							{
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 11,
								},
							},
						},
					},
					LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				f.loanInstallmentInfoDao.EXPECT().GetByActiveAccountId(gomock.Any(), "loanId1").Return(&preapprovedloan.LoanInstallmentInfo{
					Id: "LII1",
				}, nil)

				f.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "LII1").Return([]*preapprovedloan.LoanInstallmentPayout{}, nil)
			},
			want: []*palPb.LoanInstallmentPayout{
				{
					DueDate:             &date.Date{Year: 2024, Month: 2, Day: 7},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:07Feb2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        116,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 3, Day: 7},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:07Mar2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        105,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 4, Day: 7},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:07Apr2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        112,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 5, Day: 7},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:07May2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        109,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 5, Day: 31},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:31May2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2484,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully get updated installment payouts",
			args: args{
				ctx:     context.Background(),
				actorId: "actorId",
				userId:  1234,
				loanId:  4567,
				loanAccount: &palPb.LoanAccount{
					Id: "loanId1",
					LoanAmountInfo: &palPb.LoanAmountInfo{
						DisbursedAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        12000,
							Nanos:        0,
						},
					},
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.timePkg.EXPECT().Now().Return(time.Date(2024, 3, 8, 12, 1, 1, 0, datetime.IST)).Times(3)

				f.vgClient.EXPECT().FetchIndividualLoanDetails(gomock.Any(), &vgFiftyfinPb.FetchIndividualLoanDetailsRequest{
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.IndividualLoanData{
						InterestRate: 0.11,
						LoanTenure:   "5",
					},
				}, nil)

				f.vendorDataProvider.EXPECT().GetLoanForeclosureStatement(gomock.Any(), &vgFiftyfinPb.GetLoanForeclosureStatementRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.GetLoanForeclosureStatementResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.LoanForeclosureStatementData{
						PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
							OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
								Principal:             4000,
								InterestAccruedNotDue: 8.43,
								TotalDue:              4008.43,
							},
							PaymentAmountWithDates: map[string]float64{
								"10 March 2024": 4021.20,
								"09 March 2024": 4020.0,
							},
						},
					},
				}, nil)

				f.vendorDataProvider.EXPECT().GetLoanSoaStatement(gomock.Any(), &vgFiftyfinPb.GetLoanSoaStatementRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.GetLoanSoaStatementResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.LoanSoaStatementData{
						SoaData: &vendorsFiftyfin.LoanSoa{
							LoanDetails: &vendorsFiftyfin.LoanDetails{
								ContractStartDate: "01-Jan-2024",
								ContractEndDate:   "31-May-2024",
								LoanAmount:        4000,
							},
						},
					},
				}, nil)

				f.loanOffersDao.EXPECT().GetLatestByActorIdVendorAndLoanProgram(gomock.Any(), "actorId", palPb.Vendor_FIFTYFIN, palPb.LoanProgram_LOAN_PROGRAM_LAMF).Return(&preapprovedloan.LoanOffer{
					Vendor: preapprovedloan.Vendor_FIFTYFIN,
					ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
						InterestRate: []*preapprovedloan.RangeData{
							{
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 11,
								},
							},
						},
					},
					LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				f.loanInstallmentInfoDao.EXPECT().GetByActiveAccountId(gomock.Any(), "loanId1").Return(&preapprovedloan.LoanInstallmentInfo{
					Id: "LII1",
				}, nil)

				f.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "LII1").Return([]*preapprovedloan.LoanInstallmentPayout{
					{
						DueDate:             &date.Date{Year: 2024, Month: 2, Day: 7},
						PayoutDate:          &date.Date{Year: 2024, Month: 2, Day: 7},
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
						VendorInstallmentId: "1234:4567:07Feb2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        116,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2467,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 3, Day: 7},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:07Mar2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        105,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2467,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 4, Day: 7},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:07Apr2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        112,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2467,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 5, Day: 7},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:07May2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        108,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2467,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 5, Day: 31},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:31May2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2484,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
				}, nil)
			},
			want: []*palPb.LoanInstallmentPayout{
				{
					DueDate:             &date.Date{Year: 2024, Month: 2, Day: 7},
					PayoutDate:          &date.Date{Year: 2024, Month: 2, Day: 7},
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
					VendorInstallmentId: "1234:4567:07Feb2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        116,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 3, Day: 7},
					PayoutDate:          &date.Date{Year: 2024, Month: 3, Day: 7},
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
					VendorInstallmentId: "1234:4567:07Mar2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        105,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 4, Day: 7},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:07Apr2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        38,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2467,
						Nanos:        0,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 5, Day: 7},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:07May2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        36,
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        1592,
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
				{
					DueDate:             &date.Date{Year: 2024, Month: 5, Day: 31},
					PayoutDate:          nil,
					Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
					VendorInstallmentId: "1234:4567:31May2024",
					Interest: &moneyPb.Money{
						CurrencyCode: "INR",
					},
					DueAmount: &moneyPb.Money{
						CurrencyCode: "INR",
					},
					Principal: &moneyPb.Money{CurrencyCode: "INR"},
					Amount:    &moneyPb.Money{CurrencyCode: "INR"},
				},
			},
			wantErr: false,
		},
		{
			name: "failed to get individual loan details",
			args: args{
				ctx:     context.Background(),
				actorId: "actorId",
				userId:  1234,
				loanId:  4567,
				loanAccount: &palPb.LoanAccount{
					Id: "loanId1",
				},
			},
			setupMocks: func(f *BaseProviderMockFields) {
				f.vgClient.EXPECT().FetchIndividualLoanDetails(gomock.Any(), &vgFiftyfinPb.FetchIndividualLoanDetailsRequest{
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.FetchIndividualLoanDetailsResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)

				f.vendorDataProvider.EXPECT().GetLoanForeclosureStatement(gomock.Any(), &vgFiftyfinPb.GetLoanForeclosureStatementRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.GetLoanForeclosureStatementResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.LoanForeclosureStatementData{
						PreCloseData: &vendorsFiftyfin.LoanPreCloseData{
							OutstandingCharges: &vendorsFiftyfin.OutstandingCharges{
								Principal:             4000,
								InterestAccruedNotDue: 8.43,
								TotalDue:              4008.43,
							},
							PaymentAmountWithDates: map[string]float64{
								"10 March 2024": 4021.20,
								"09 March 2024": 4020.0,
							},
						},
					},
				}, nil)

				f.vendorDataProvider.EXPECT().GetLoanSoaStatement(gomock.Any(), &vgFiftyfinPb.GetLoanSoaStatementRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FIFTYFIN,
					},
					LoanId: 4567,
					UserId: 1234,
				}).Return(&vgFiftyfinPb.GetLoanSoaStatementResponse{
					Status: rpcPb.StatusOk(),
					Data: &vendorsFiftyfin.LoanSoaStatementData{
						SoaData: &vendorsFiftyfin.LoanSoa{
							LoanDetails: &vendorsFiftyfin.LoanDetails{
								ContractStartDate: "01-Jan-2024",
								ContractEndDate:   "31-May-2024",
								LoanAmount:        12000,
							},
						},
					},
				}, nil)

				f.loanOffersDao.EXPECT().GetLatestByActorIdVendorAndLoanProgram(gomock.Any(), "actorId", palPb.Vendor_FIFTYFIN, palPb.LoanProgram_LOAN_PROGRAM_LAMF).Return(&preapprovedloan.LoanOffer{
					Vendor: preapprovedloan.Vendor_FIFTYFIN,
					ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
						InterestRate: []*preapprovedloan.RangeData{
							{
								Value: &preapprovedloan.RangeData_Percentage{
									Percentage: 11,
								},
							},
						},
					},
					LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
				}, nil)

				f.loanInstallmentInfoDao.EXPECT().GetByActiveAccountId(gomock.Any(), "loanId1").Return(&preapprovedloan.LoanInstallmentInfo{
					Id: "LII1",
				}, nil)

				f.loanInstallmentPayoutDao.EXPECT().GetByLoanInstallmentInfoId(gomock.Any(), "LII1").Return([]*preapprovedloan.LoanInstallmentPayout{
					{
						DueDate:             &date.Date{Year: 2024, Month: 2, Day: 7},
						PayoutDate:          &date.Date{Year: 2024, Month: 2, Day: 7},
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS,
						VendorInstallmentId: "1234:4567:07Feb2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        116,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2466,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 3, Day: 7},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:07Mar2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        105,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2466,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 4, Day: 7},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:07Apr2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        112,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2466,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 5, Day: 7},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:07May2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        108,
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2466,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
					{
						DueDate:             &date.Date{Year: 2024, Month: 5, Day: 31},
						PayoutDate:          nil,
						Status:              palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING,
						VendorInstallmentId: "1234:4567:31May2024",
						Interest: &moneyPb.Money{
							CurrencyCode: "INR",
						},
						DueAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2488,
							Nanos:        0,
						},
						Principal: &moneyPb.Money{CurrencyCode: "INR"},
						Amount:    &moneyPb.Money{CurrencyCode: "INR"},
					},
				}, nil)
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			f := InitMocks(ctrl)
			tt.setupMocks(f)

			p := InitBaseProvider(f)
			got, err := p.GetUpdatedInstallmentPayouts(tt.args.ctx, tt.args.actorId, tt.args.userId, tt.args.loanId, tt.args.loanAccount)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpdatedInstallmentPayouts() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetUpdatedInstallmentPayouts() got = %v,\n want %v \n diff %v", got, tt.want, diff)
			}
		})
	}
}
