package finflux_test

import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	mocks2 "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux/mocks"
	mock_dao "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/finflux"
	"github.com/epifi/gamma/preapprovedloan/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type BaseProviderMockFields struct {
	vgClient                 *mocks2.MockFinfluxClient
	loanAccountsDao          *mock_dao.MockLoanAccountsDao
	loanPaymentRequestDao    *mock_dao.MockLoanPaymentRequestsDao
	loanInstallmentPayoutDao *mock_dao.MockLoanInstallmentPayoutDao
	loanInstallmentInfoDao   *mock_dao.MockLoanInstallmentInfoDao
}

func InitMocks(ctr *gomock.Controller) *BaseProviderMockFields {
	return &BaseProviderMockFields{
		vgClient:                 mocks2.NewMockFinfluxClient(ctr),
		loanAccountsDao:          mock_dao.NewMockLoanAccountsDao(ctr),
		loanPaymentRequestDao:    mock_dao.NewMockLoanPaymentRequestsDao(ctr),
		loanInstallmentPayoutDao: mock_dao.NewMockLoanInstallmentPayoutDao(ctr),
		loanInstallmentInfoDao:   mock_dao.NewMockLoanInstallmentInfoDao(ctr),
	}
}

func InitBaseProvider(f *BaseProviderMockFields) *finflux.FinfluxBaseProvider {
	return finflux.NewFinfluxBaseProvider(f.vgClient, f.loanAccountsDao, f.loanPaymentRequestDao, f.loanInstallmentPayoutDao, f.loanInstallmentInfoDao)
}
