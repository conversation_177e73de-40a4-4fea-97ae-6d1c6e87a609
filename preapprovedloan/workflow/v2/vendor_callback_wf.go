//nolint:dupl
package v2

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epificontext"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/abfl"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/federal/realtime"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/fiftyfin/lamf"
	moneyviewPl "github.com/epifi/gamma/preapprovedloan/workflow/providers/moneyview/preapprovedloan"
)

//nolint:funlen,dupl
func ProcessVendorNotification(ctx workflow.Context, req *workflowPb.Request) error {
	// workflow logger
	lg := workflow.GetLogger(ctx)

	var vendorNotificationReq palWorkflowPb.VendorNotificationsPayload
	_, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, &vendorNotificationReq)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return errors.Wrap(err, "failed to fetch workflow processing params")
	}

	vendorNotificationProvider, err := getNotificationVendorProvider(vendorNotificationReq.GetLoanHeader())
	if err != nil {
		return fmt.Errorf("failed to get notification provider: %w", err)
	}

	ctx = epificontext.WorkflowContextWithOwnership(ctx, helper.GetPalOwnership(vendorNotificationReq.GetLoanHeader().GetVendor()))
	err = vendorNotificationProvider.ProcessNotification(ctx, &vendorNotificationReq)
	if err != nil {
		return fmt.Errorf("failed to process notification for vendor: %s, loanProgram: %s, err: %w", vendorNotificationReq.GetLoanHeader().GetVendor(),
			vendorNotificationReq.GetLoanHeader().GetLoanProgram(), err)
	}

	return nil
}

func getNotificationVendorProvider(lh *palPb.LoanHeader) (providers.IVendorNotificationProvider, error) {
	switch {
	case lh.GetVendor() == palPb.Vendor_FIFTYFIN && lh.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF:
		return lamf.NewVendorNotificationProvider(), nil
	case lh.GetVendor() == palPb.Vendor_ABFL && lh.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return abfl.NewVendorNotificationProvider(), nil
	case lh.GetVendor() == palPb.Vendor_MONEYVIEW:
		return moneyviewPl.NewVendorNotificationProvider(), nil
	case lh.GetVendor() == palPb.Vendor_FEDERAL && lh.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return realtime.NewVendorNotificationProvider(), nil
	default:
		return nil, fmt.Errorf("notification provider is not defined for given vendor: %s and loan program: %s", lh.GetVendor(), lh.GetLoanProgram())
	}
}
