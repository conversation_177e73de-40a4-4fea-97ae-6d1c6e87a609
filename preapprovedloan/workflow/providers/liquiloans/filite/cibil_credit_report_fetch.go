package filite

import (
	"fmt"
	"time"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
)

// FetchCibilCreditReport represents the stage which fetches the cibil report of the applicant
type FetchCibilCreditReport struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewFetchCibilCreditReport() *FetchCibilCreditReport {
	return &FetchCibilCreditReport{}
}

var _ stages.IStage = &FetchCibilCreditReport{}

// nolint:funlen
func (cr *FetchCibilCreditReport) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	lseStatus := palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	for lseStatus == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS {
		var errorToReturn error
		actReq := &palActivityPb.PalActivityRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.Request.GetLoanStep().GetOrchId(),
			},
			LoanStep:    req.Request.GetLoanStep(),
			Vendor:      req.Vendor,
			LoanProgram: req.LoanProgram,
		}
		actRes := &palActivityPb.PalActivityResponse{}
		actErr := activityPkg.Execute(ctx, palNs.SetCreditReportFetchScreen, actRes, actReq)
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, false) {
			return res, actErr
		}

		// if loan step is already success here (for cases where credit report fetch stage is skipped), no need to execute other activities
		// and return from here
		if actRes.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
			res.LseFieldMasks = actRes.GetLseFieldMasks()
			return res, nil
		}

		actReq = &palActivityPb.PalActivityRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.Request.GetLoanStep().GetOrchId(),
			},
			LoanStep:    req.Request.GetLoanStep(),
			Vendor:      req.Vendor,
			LoanProgram: req.LoanProgram,
		}
		actRes = &palActivityPb.PalActivityResponse{}
		getCRStatusFuture, err := activityPkg.ExecuteAsync(ctx, palNs.FetchUserActionStatus, actReq, actRes)
		if err != nil {
			return nil, err
		}

		getCRStatusFuture.AddFutureHandler(func(getCRStatusErr error, actRes *palActivityPb.PalActivityResponse) {
			workflow.GetLogger(ctx).Info("getCRStatus future finished")
			if getCRStatusErr != nil {
				workflow.GetLogger(ctx).Info("FetchUserActionStatus activity async processing failed", zap.Error(getCRStatusErr))
				errorToReturn = getCRStatusErr
			}
		})

		signalPayload := &emptyPb.Empty{}
		sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanCreditReportFetchSignal, signalPayload)
		sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
			workflow.GetLogger(ctx).Info("LoanCreditReportFetchSignal signal returned first")
			if getErr != nil {
				workflow.GetLogger(ctx).Info("LoanCreditReportFetchSignal signal processing failed", zap.Error(getErr))
				errorToReturn = getErr
			}
		})

		err = epifitemporal.ReceiveSignalWithFuture(ctx, getCRStatusFuture, sigChannel, maxCreditReportStatusWaitTimeHrs*time.Hour)
		if err != nil {
			workflow.GetLogger(ctx).Info("credit report status fetch signal with future processing failed", zap.Error(err))
			return res, err
		}

		if errorToReturn != nil {
			// we are checking for retryable error here, it will be in case aysnc activity or sync activity inside signal handler
			// gets exhausted. We will mark the lse expired in this case and return
			if epifitemporal.IsRetryableError(errorToReturn) {
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
			}
			return res, errorToReturn
		}

		actSyncReq := &palActivityPb.PalActivityRequest{
			LoanStep:    req.Request.GetLoanStep(),
			Vendor:      req.Vendor,
			LoanProgram: req.LoanProgram,
		}
		actSyncRes := &palActivityPb.PalActivityResponse{}
		actAsyncReq := &palActivityPb.PalActivityRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.Request.GetLoanStep().GetOrchId(),
			},
			LoanStep:    req.Request.GetLoanStep(),
			Vendor:      req.Vendor,
			LoanProgram: req.LoanProgram,
		}
		actAsyncRes := &palActivityPb.PalActivityResponse{}
		syncExecError := providers.PerformActionInSync(ctx, &providers.SyncPolledStageRequest{
			SyncActivityDetails: &providers.ActivityDetails{
				Name:         palNs.CreditReportFetchStatusSync,
				Request:      actSyncReq,
				SyncResponse: actSyncRes,
				SyncRequest:  actSyncReq,
			},
			AsyncActivityDetails: &providers.ActivityDetails{
				Name:          palNs.CreditReportFetchStatus,
				Request:       actAsyncReq,
				AsyncResponse: actAsyncRes,
			},
			ExecuteAsyncInParallel: true,
			StageName:              req.Request.GetLoanStep().GetStepName(),
			WfProcessingParams:     req.WfProcessingParams,
			LoanRequestId:          req.Request.GetLoanStep().GetRefId(),
			Vendor:                 req.Vendor,
			LoanProgram:            req.LoanProgram,
			TimeoutDurationInMins:  maxCreditReportStatusWaitTimeHrs * 60,
		})

		// if sync activity finished it's work, we will evaluate response from that, else from async activity execution
		actResp := actAsyncRes
		if actSyncRes.GetIsSyncTaskDone() {
			actResp = actSyncRes
		}
		if providers.IsActivityError(ctx, actResp.GetLoanStep(), nil, res, syncExecError, false) {
			errorToReturn = fmt.Errorf("LLgetCRStatus activity failed %w", syncExecError)
		} else {
			lseStatus = actResp.GetLoanStep().GetStatus()
		}
	}
	return res, nil
}

func (cr *FetchCibilCreditReport) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH
}

func (cr *FetchCibilCreditReport) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageCibilReportFetch
}
