package federal

import (
	"fmt"

	"go.temporal.io/sdk/workflow"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type FederalFkyc struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewFederalFkyc() *FederalFkyc {
	return &FederalFkyc{}
}

var _ stages.IStage = &FederalFkyc{}

const maxKycVerificationSignalWaitTimeInHours = 120

func (f *FederalFkyc) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}
	checkEligibilityReq := &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
		},
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
		LoanStep:    req.Request.GetLoanStep(),
	}
	checkEligibilityRes := &palActivityPb.PalActivityResponse{}
	actErr := activityPkg.Execute(ctx, palNs.CheckKycEligibilityV2, checkEligibilityRes, checkEligibilityReq)
	if providers.IsActivityError(ctx, checkEligibilityRes.GetLoanStep(), nil, res, actErr, true) {
		return res, actErr
	}
	res.LoanStep = checkEligibilityRes.GetLoanStep()
	res.LseFieldMasks = checkEligibilityRes.GetLseFieldMasks()

	if checkEligibilityRes.GetLoanStep().GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
		return res, nil
	}

	kycCheckReq := &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: checkEligibilityRes.GetLoanStep().GetOrchId(),
		},
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
		LoanStep:    res.LoanStep,
	}
	kycCheckRes := &palActivityPb.PalActivityResponse{}
	err := activityPkg.Execute(ctx, palNs.ProcessKycCheckV2, kycCheckRes, kycCheckReq)
	if providers.IsActivityError(ctx, kycCheckRes.GetLoanStep(), nil, res, err, false) {
		return res, fmt.Errorf("%s activity initiation failed: %w", palNs.ProcessKycCheckV2, err)
	}
	res.LoanStep = kycCheckRes.GetLoanStep()
	res.LseFieldMasks = kycCheckRes.GetLseFieldMasks()

	return res, nil
}

func (f *FederalFkyc) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC
}

func (f *FederalFkyc) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageCKYC
}
