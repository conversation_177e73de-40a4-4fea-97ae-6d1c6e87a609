package realtime

import (
	"fmt"

	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	ns "github.com/epifi/gamma/preapprovedloan/workflow/namespace"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/federal"
	fedRealTime "github.com/epifi/gamma/preapprovedloan/workflow/providers/federal/realtime"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
)

type LoanApplicationProvider struct{}

func NewLoanApplicationProvider() *LoanApplicationProvider {
	return &LoanApplicationProvider{}
}

var _ providers.IVendorProvider = &LoanApplicationProvider{}

func (p *LoanApplicationProvider) GetStages(ctx workflow.Context, req *providers.GetStagesRequest) (*providers.GetStagesResponse, error) {
	res := &providers.GetStagesResponse{}
	res.Stages = make([]stages.IStage, 0)

	switch req.GroupStage {
	case palPb.GroupStage_GROUP_STAGE_OFFER_CREATION:
		res.Stages = append(res.Stages,
			NewOnboardingStage(palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_VERIFICATION, ns.StagePanVerification),
			providers.NewAddress(), providers.NewVendorHardOfferCreation(), providers.NewUserOfferSelectionStage())
	case palPb.GroupStage_GROUP_STAGE_KYC:
		res.Stages = append(res.Stages,
			NewOnboardingStage(palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK, palNs.StageKyc))
	case palPb.GroupStage_GROUP_STAGE_MANDATE:
		res.Stages = append(res.Stages, NewPennyDrop(), NewMandate())
	case palPb.GroupStage_GROUP_STAGE_PRE_KFS:
		res.Stages = append(res.Stages, federal.NewFederalPreKfs())
	case palPb.GroupStage_GROUP_STAGE_E_SIGN:
		res.Stages = append(res.Stages, federal.NewFederalKfsStage())
	case palPb.GroupStage_GROUP_STAGE_VKYC:
		res.Stages = append(res.Stages, NewOnboardingStage(palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC, palNs.StageVKYC))
	case palPb.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION:
		res.Stages = append(res.Stages, fedRealTime.NewLoanAccountCreation())
	case palPb.GroupStage_GROUP_STAGE_DISBURSAL:
		res.Stages = append(res.Stages, fedRealTime.NewLoanDisbursal())
	default:
		return nil, fmt.Errorf("group stage not implemented by vendor provider, groupStage: %v", req.GroupStage)
	}

	return res, nil
}

func (p *LoanApplicationProvider) GetGroupStages(_ workflow.Context, _ *providers.GetGroupStagesRequest) (*providers.GetGroupStagesResponse, error) {
	res := &providers.GetGroupStagesResponse{}
	res.GroupStages = []palPb.GroupStage{
		palPb.GroupStage_GROUP_STAGE_OFFER_CREATION,
		palPb.GroupStage_GROUP_STAGE_KYC,
		palPb.GroupStage_GROUP_STAGE_MANDATE,
		palPb.GroupStage_GROUP_STAGE_PRE_KFS,
		palPb.GroupStage_GROUP_STAGE_E_SIGN,
		palPb.GroupStage_GROUP_STAGE_VKYC,
		palPb.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION,
		palPb.GroupStage_GROUP_STAGE_DISBURSAL,
	}
	return res, nil
}
