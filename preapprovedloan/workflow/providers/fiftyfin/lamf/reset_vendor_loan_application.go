package lamf

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

// FiftyfinAdditionalKycDetails represents the stage which gets additional kyc details from user and sends to vendor
type FiftyfinResetVendorLoanApplication struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
	*commonStageHelper
}

func NewFiftyfinResetVendorLoanApplication() *FiftyfinResetVendorLoanApplication {
	return &FiftyfinResetVendorLoanApplication{}
}

var _ stages.IStage = &FiftyfinResetVendorLoanApplication{}

func (f *FiftyfinResetVendorLoanApplication) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}
	lg := workflow.GetLogger(ctx)

	// restart loan but keep funds pledged
	voidLoanReq := f.newFiftyfinVoidLoanRequest(req, commontypes.BoolToBooleanEnum(true), commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
	voidLoanRes := &palActivityPb.FiftyfinVoidLoanResponse{}
	actErr := activityPkg.Execute(ctx, palNs.FiftyfinVoidLoan, voidLoanRes, voidLoanReq)
	if providers.IsActivityErrorV2(ctx, res, actErr, providers.FiftyfinVoidLoanResponseErrParams(voidLoanRes)) {
		lg.Error("error in FiftyfinVoidLoan activity", zap.Error(actErr))
		return res, actErr
	}

	setRetryScreenReq := f.getActivityRequest(req)
	setRetryScreenRes := &palActivityPb.PalActivityResponse{}
	actErr = activityPkg.Execute(ctx, palNs.FiftyfinSetLoanResetNextAction, setRetryScreenRes, setRetryScreenReq)
	if providers.IsActivityErrorV2(ctx, res, actErr, providers.PalActivityResponseErrParams(setRetryScreenRes).WithMarkRetryableErrsAsFailed(false)) {
		lg.Error("error in FiftyfinSetLoanResetNextAction activity", zap.Error(actErr))
		return res, actErr
	}

	waitActRes, err := f.waitForUserAction(ctx, req)
	v := workflow.GetVersion(ctx, "auto-close-loan-application", workflow.DefaultVersion, 1)
	if v == 1 {
		if epifitemporal.IsRetryableError(err) {
			lg.Info("restart vendor application retries exhausted, un-pledging funds and closing loan request")
			unpledgeFundsReq := f.newFiftyfinVoidLoanRequest(req, commontypes.BoolToBooleanEnum(false), commontypes.BoolToBooleanEnum(true))
			unpledgeFundsRes := &palActivityPb.FiftyfinVoidLoanResponse{}
			actErr = activityPkg.Execute(ctx, palNs.FiftyfinVoidLoan, unpledgeFundsRes, unpledgeFundsReq)
			if providers.IsActivityErrorV2(ctx, res, actErr, providers.FiftyfinVoidLoanResponseErrParams(unpledgeFundsRes)) {
				lg.Error("error in FiftyfinVoidLoan activity", zap.Error(actErr))
				return res, actErr
			}
			res.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
			return res, nil
		}
	}
	if providers.IsActivityErrorV2(ctx, res, err, providers.PalActivityResponseErrParams(waitActRes).WithMarkRetryableErrsAsFailed(false)) {
		lg.Error("error while waiting for user's action on loan process reset screen", zap.Error(err))
		return res, err
	}

	lg.Info("restarting vendor loan process")
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	res.NextGroupStage = palPb.GroupStage_GROUP_STAGE_INIT_LOAN
	return res, nil
}

func (f *FiftyfinResetVendorLoanApplication) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RESET_VENDOR_LOAN_APPLICATION
}

func (f *FiftyfinResetVendorLoanApplication) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageResetVendorLoanApplication
}

// nolint:dupl
func (f *FiftyfinResetVendorLoanApplication) waitForUserAction(ctx workflow.Context, req *stages.PerformRequest) (*palActivityPb.PalActivityResponse, error) {
	// wait for the user action to restart the loan process
	// poll for the lse sub status and wait for the signal as well
	lg := workflow.GetLogger(ctx)
	var errorToReturn error

	actReq := f.getActivityRequest(req)
	actRes := &palActivityPb.PalActivityResponse{}
	waitForUserActionFuture, err := activityPkg.ExecuteAsync(ctx, palNs.FiftyfinLoanResetLoanWaitForUserAction, actReq, actRes)
	if err != nil {
		return actRes, err
	}
	waitForUserActionFuture.AddFutureHandler(func(waitForUserActionErr error, resp *palActivityPb.PalActivityResponse) {
		lg.Info("waitForUserAction future finished")
		actRes = resp
		if waitForUserActionErr != nil {
			lg.Info("FiftyfinLoanResetWaitForUserAction activity async processing failed", zap.Error(waitForUserActionErr))
			errorToReturn = waitForUserActionErr
		}
	})

	signalPayload := &emptyPb.Empty{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.LoanApplicationResetVendorLoanProcessSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *emptyPb.Empty) {
		lg.Info("received loansProcessReset signal returned first")
		if getErr != nil {
			lg.Error("loansProcessReset signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, waitForUserActionFuture, sigChannel, 60*24*time.Hour)
	if err != nil {
		lg.Info("loanProcessReset signal processing failed", zap.Error(err))
		return actRes, err
	}
	if errorToReturn != nil {
		return actRes, errorToReturn
	}
	return actRes, nil
}
