// nolint: dupl,funlen
package abfl

import (
	"context"
	"fmt"
	"strings"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/latlng"
	json "google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"

	"github.com/epifi/gamma/api/auth/location"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	abflVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

const dobLayout = "02 January 2006"

// nolint: funlen
func (p *Processor) AbflPerformUkyc(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		user, userErr := p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
		if userErr != nil {
			lg.Error("failed to fetch user by actor ID", zap.Error(userErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID, err: %v", userErr))
		}

		// _, addressLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
		//	lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
		// if addressLseErr != nil {
		// 	lg.Error("failed to fetch address lse", zap.Error(addressLseErr))
		//	return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch address lse, err: %v", addressLseErr))
		// }

		breLse, breLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
			lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE)
		if breLseErr != nil {
			lg.Error("failed to fetch bre lse", zap.Error(breLseErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch bre lse, err: %v", breLseErr))
		}

		aadhaarLse, aadhaarLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
			lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR)
		if aadhaarLseErr != nil {
			lg.Error("failed to fetch aadhaar lse", zap.Error(aadhaarLseErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch aadhaar lse, err: %v", aadhaarLseErr))
		}

		aadhaarVgRes, aadhaarVgErr := p.fetchDigilockerStatusFromVendor(ctx, lr.GetVendorRequestId(), aadhaarLse.GetDetails().GetAadhaarData().GetAbfl().GetProfileId())
		if aadhaarVgErr != nil {
			lg.Error("error in fetching digilocker status from vendor", zap.Error(aadhaarVgErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching digilocker status from vendor, err: %v", aadhaarVgErr))
		}
		// ckycLse, ckycLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
		//	lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC)
		// if ckycLseErr != nil {
		//	lg.Error("failed to fetch ckyc lse", zap.Error(ckycLseErr))
		//	return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch ckyc lse, err: %v", ckycLseErr))
		// }
		// docBase64, docType, docErr := getBase64DocAndDocType(ckycLse.GetDetails().GetCkycStepData().GetAbfl())
		// if docErr != nil {
		//	lg.Error("failed to get base64 doc and doc type", zap.Error(docErr))
		//	return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get base64 doc and doc type, err: %v", docErr))
		// }

		// Get selfie lse for selfie image
		selfieLse, selfieLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
			lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE)
		if selfieLseErr != nil {
			lg.Error("failed to fetch selfie lse", zap.Error(selfieLseErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch selfie lse, err: %v", selfieLseErr))
		}

		selfieImage, readError := p.s3Client.Read(ctx, selfieLse.GetDetails().GetSelfieData().GetSelfieImage().GetImageUrl())
		if readError != nil {
			return nil, errors.Wrap(readError, "failed to get selfie image from S3")
		}

		txnId := "TxnId" + idgen.RandAlphaNumericString(12)

		lse.OrchId = txnId
		updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID})
		if updateErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, "unable to update orch id with txnId in the lse")
		}

		// fetching the latLong of the user
		latLong, err := p.getLatLongCoordinates(ctx, lse.GetActorId())
		if err != nil {
			return nil, err
		}

		vgRes, vgErr := p.abflVgClient.UnifiedCkyc(ctx, &abflVgPb.UnifiedKycRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
			AccountId:     lr.GetVendorRequestId(),
			CccId:         breLse.GetDetails().GetBreData().GetIdentifier(),
			TransactionId: txnId,
			Selfie:        string(selfieImage),
			// CkycDocFrontPhoto: docBase64,
			// CkycCustomerPhoto: ckycLse.GetDetails().GetCkycStepData().GetAbfl().GetPhotographBase64Image(),
			// DeclaredAddress: ckycLse.GetDetails().GetCkycStepData().GetAbfl().GetPermanentAddress(),
			// CkycDob:         ckycLse.GetDetails().GetCkycStepData().GetAbfl().GetDob(),
			// CkycName:        ckycLse.GetDetails().GetCkycStepData().GetAbfl().GetFullName(),
			// CkycDocType:     docType,
			// CkycAddress:     ckycLse.GetDetails().GetCkycStepData().GetAbfl().GetPermanentAddress(),
			DeclaredAddress: aadhaarVgRes.GetTasks()[0].GetAddress(),
			StreetAddress:   aadhaarVgRes.GetTasks()[0].GetStreetAddressString(),
			DeclaredName:    aadhaarVgRes.GetTasks()[0].GetName(),
			DeclaredDob:     aadhaarVgRes.GetTasks()[0].GetDob(),
			DeclaredPan:     user.GetProfile().GetPAN(),
			NsdlName:        user.GetProfile().GetPanName(),
			Gender:          aadhaarVgRes.GetTasks()[0].GetGender(),
			Email:           user.GetProfile().GetEmail(),
			Mobile:          user.GetProfile().GetPhoneNumber(),
			NsdlPan:         user.GetProfile().GetPAN(),
			OkycXml:         aadhaarVgRes.GetDigiAadhaarXml(),
			LatLng:          latLong,
		})
		if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
			lg.Error("error in vg res for ukyc", zap.Error(te))
			if vgRes.GetStatus().GetCode() == rpcPb.StatusPermanentFailure().GetCode() {
				goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
					p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewVendorFailureReasonEvent(
						lr.GetActorId(), lr.GetId(), lr.GetVendor().String(), lr.GetLoanProgram().String(), "UnifiedCkyc", vgRes.GetStatus().GetDebugMessage(), "",
					))
				})
				palActivity.MarkLoanStepFail(lse)
				res.LoanStep = lse
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in vg res for ukyc, err: %v", te))
		}
		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) AbflPollUkycStatus(ctx context.Context, req *palActivityPb.AbflPollingActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		breLse, breLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
			lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE)
		if breLseErr != nil {
			lg.Error("failed to fetch bre lse", zap.Error(breLseErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch bre lse, err: %v", breLseErr))
		}

		vgRes := &abflVgPb.UnifiedKycStatusResponse{}
		var vgErr error
		if req.GetCallbackData() != nil {
			lg.Info("ukyc callback got triggered")
			unmarshalErr := json.Unmarshal(req.GetCallbackData(), vgRes)
			if unmarshalErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to unmarshall the callback request data, err: %v", unmarshalErr))
			}
		} else {
			lg.Info("ukyc vg got triggered")
			vgRes, vgErr = p.abflVgClient.UnifiedKyStatus(ctx, &abflVgPb.UnifiedKycStatusRequest{
				Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ABFL},
				AccountId:     lr.GetVendorRequestId(),
				TransactionId: lse.GetOrchId(),
				CccId:         breLse.GetDetails().GetBreData().GetIdentifier(),
			})
		}
		if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
			lg.Error("error in vg res for ukyc status", zap.Error(te))
			if vgRes.GetStatus().GetCode() == rpcPb.StatusPermanentFailure().GetCode() {
				goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
					p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewVendorFailureReasonEvent(
						lr.GetActorId(), lr.GetId(), lr.GetVendor().String(), lr.GetLoanProgram().String(), "UnifiedKyStatus", vgRes.GetStatus().GetDebugMessage(), "",
					))
				})
				palActivity.MarkLoanStepFail(lse)
				res.LoanStep = lse
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in vg res for ukyc status, err: %v", te))
		}

		if vgRes.GetUnifiedKycStatusData().GetUkycStatus() == abflVgPb.UkycStatus_UKYC_STATUS_IN_PROGRESS {
			lg.Error("ukyc is in progress right now")
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("ukyc is in progress right now"))
		}

		res.LseFieldMasks = []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS}
		if vgRes.GetUnifiedKycStatusData().GetUkycStatus() == abflVgPb.UkycStatus_UKYC_STATUS_APPROVED {
			palActivity.MarkLoanStepSuccess(lse)
		} else {
			palActivity.MarkLoanStepFail(lse)
		}
		return res, nil
	})
	return actRes, actErr
}

func getBase64DocAndDocType(abflDetails *palPb.AbflCkycStepData) (string, typesPb.DocumentType, error) {
	switch {
	case abflDetails.GetAadhaarBase64Image() != "":
		return abflDetails.GetAadhaarBase64Image(), typesPb.DocumentType_AADHAR, nil
	case abflDetails.GetPassportBase64Image() != "":
		return abflDetails.GetPassportBase64Image(), typesPb.DocumentType_PASSPORT, nil
	// case abflDetails.GetDrivingLicenceBase64Image() != "":
	//	return abflDetails.GetDrivingLicenceBase64Image(), typesPb.DocumentType_DRIVING_LICENCE, nil
	// case abflDetails.GetPanBase64Image() != "":
	//	return abflDetails.GetPanBase64Image(), typesPb.DocumentType_PAN, nil
	// case abflDetails.GetVoterIdBase64Image() != "":
	//	return abflDetails.GetVoterIdBase64Image(), typesPb.DocumentType_VOTER_ID, nil
	default:
		return "", typesPb.DocumentType_DOCUMENT_TYPE_UNSPECIFIED, errors.New("doc and doc type not found")
	}
}

func (p *Processor) SetAbflKycDetailsScreen(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// ckycLse, ckycLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
		//	lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC)
		// if ckycLseErr != nil {
		//	lg.Error("failed to fetch ckyc lse", zap.Error(ckycLseErr))
		//	return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch ckyc lse, err: %v", ckycLseErr))
		// }

		user, userErr := p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
		if userErr != nil {
			lg.Error("failed to fetch user by actor ID", zap.Error(userErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID, err: %v", userErr))
		}

		aadhaarLse, aadhaarLseErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
			lse.GetFlow(), palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR)
		if aadhaarLseErr != nil {
			lg.Error("failed to fetch aadhaar lse", zap.Error(aadhaarLseErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch aadhaar lse, err: %v", aadhaarLseErr))
		}

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
		reviewScreenDl, dlErr := deeplinkProvider.GetReviewScreenDeeplink(deeplinkProvider.GetLoanHeader(), aadhaarLse.GetDetails().GetAadhaarData().GetAbfl().GetName(),
			strings.ToUpper(user.GetProfile().GetPAN()), aadhaarLse.GetDetails().GetAadhaarData().GetAbfl().GetAddress(),
			"", strings.ToUpper(datetime.DateToString(aadhaarLse.GetDetails().GetAadhaarData().GetAbfl().GetDob(), dobLayout, datetime.IST)),
			lse.GetRefId())
		if dlErr != nil {
			lg.Error("failed to get ReviewScreenDeeplink", zap.Error(dlErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get ReviewScreenDeeplink, err: %v", dlErr))
		}
		lr := &palPb.LoanRequest{
			Id:         lse.GetRefId(),
			NextAction: reviewScreenDl,
		}

		txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
		if txnExecErr != nil {
			lg.Error("failed to get txn executor by ownership", zap.Error(txnExecErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get txn executor by ownership, err: %v", txnExecErr))
		}
		txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			updateErr := p.loanStepExecutionDao.Update(txnCtx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
			if updateErr != nil {
				return fmt.Errorf("failed to update loan step execution, %w", updateErr)
			}
			updateErr = p.loanRequestDao.Update(txnCtx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION})
			if updateErr != nil {
				return fmt.Errorf("failed to update loan request, %w", updateErr)
			}
			return nil
		})
		if txnErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintln(txnErr))
		}
		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) AbflGetVerifyKycStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		if lse.GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED {
			lg.Error("waiting on user to verify kyc details")
			return nil, errors.Wrap(epifierrors.ErrTransient, "waiting on user to verify kyc details")
		}

		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) getLatLongCoordinates(ctx context.Context, actorId string) (*latlng.LatLng, error) {
	logger := activity.GetLogger(ctx)
	// fetching location token
	userDevPropRes, err := p.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId:       actorId,
		PropertyTypes: []typesPb.DeviceProperty{typesPb.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN},
	})
	if te := epifigrpc.RPCError(userDevPropRes, err); te != nil {
		logger.Error("error in getting user device properties", zap.Error(te))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting user device properties, err: %v", te))
	}

	var locToken string
	for _, prop := range userDevPropRes.GetUserDevicePropertyList() {
		if prop.GetDeviceProperty() == typesPb.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN {
			locToken = prop.GetPropertyValue().GetLocationToken()
			break
		}
	}
	if locToken == "" {
		logger.Error("empty location token")
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("empty location token, actor id : %v", actorId))
	}

	// fetching coordinates
	coordinates, err := p.locationClient.GetCoordinates(ctx, &location.GetCoordinatesRequest{
		LocationToken: locToken,
	})
	if te := epifigrpc.RPCError(coordinates, err); te != nil {
		logger.Error("error in getting coordinates from auth service", zap.Error(te))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting coordinates from auth service, err: %v", te))
	}

	return coordinates.GetLatLng(), nil
}
