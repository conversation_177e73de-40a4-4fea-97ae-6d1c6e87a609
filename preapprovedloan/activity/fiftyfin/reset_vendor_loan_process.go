package fiftyfin

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
)

func (p *Processor) FiftyfinSetLoanResetNextAction(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	return palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		initialiseLoanResetDetails(lse)
		updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		})
		if updateErr != nil {
			lg.Error("error while updating reset loan process details in lse", zap.Error(updateErr))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, fmt.Sprintf("error while updating reset loan process details in lse : %s", updateErr.Error()))
		}
		loanResetInfoScreen, err := p.deeplinkProvider.GetVendorLoanProcessResetScreen(ctx, lse.GetRefId(), lse.GetId())
		if err != nil {
			lg.Error("error while generating loan reset info screen deeplink", zap.Error(err))
			return nil, p.wrapErrWithCustomKey(epifierrors.ErrTransient, fmt.Sprintf("error while generating loan reset info screen deeplink : %s", err.Error()))
		}
		err = palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, lse.GetRefId(), loanResetInfoScreen)
		if err != nil {
			lg.Error("error while updating next action", zap.Error(err))
			return nil, err
		}
		return res, nil
	})
}

// nolint:dupl
func (p *Processor) FiftyfinLoanResetLoanWaitForUserAction(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	return palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		if lse.GetDetails().GetResetVendorLoanApplicationData().GetLamf().GetUserActionTaken() {
			return res, nil
		} else {
			return nil, errors.Wrap(epifierrors.ErrTransient, "action not taken yet")
		}

	})
}

func initialiseLoanResetDetails(lse *palPb.LoanStepExecution) {
	if lse.GetDetails() == nil {
		lse.Details = &palPb.LoanStepExecutionDetails{}
	}
	if lse.GetDetails().GetResetVendorLoanApplicationData() == nil {
		lse.Details.Details = &palPb.LoanStepExecutionDetails_ResetVendorLoanApplicationData{
			ResetVendorLoanApplicationData: &palPb.ResetVendorLoanApplicationData{
				Data: &palPb.ResetVendorLoanApplicationData_Lamf{
					Lamf: &palPb.LamfResetVendorLoanApplicationData{},
				},
			},
		}
	}
	if lse.GetDetails().GetLoanDetailsVerificationData().GetLamf() == nil {
		lse.GetDetails().GetResetVendorLoanApplicationData().Data = &palPb.ResetVendorLoanApplicationData_Lamf{
			Lamf: &palPb.LamfResetVendorLoanApplicationData{},
		}
	}
}
