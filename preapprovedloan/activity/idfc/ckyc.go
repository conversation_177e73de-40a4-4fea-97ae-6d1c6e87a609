package idfc

import (
	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	idfcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	vgPalIdfcPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
)

const dobLayout = "02 Jan 2006"

// nolint: funlen
func (p *Processor) IdfcCheckCkyc(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palpb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)
		var updateLrErr error
		// fetch user details
		userInfo, userErr := p.rpcHelper.GetUserByActorId(ctx, req.GetLoanStep().GetActorId())
		if userErr != nil {
			lg.Error("failed to fetch user by actor ID", zap.Error(userErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, userErr.Error())
		}

		// fetch the loan applicant to get the vendor_applicant_id that is to be used as req_id in vendor's api calls.
		applicant, err := p.loanApplicantDao.GetByActorId(ctx, req.GetLoanStep().GetActorId())
		if err != nil {
			lg.Error("error while getting loan applicant for the user", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		txnId := uuid.New().String()

		// search ckyc details
		ckycSearchRes, ckycSearchResErr := p.idfcPalVgClient.SearchCkyc(ctx, &idfcVgPb.SearchCkycRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_IDFC,
			},
			CorrelationId: uuid.New().String(),
			CkycRequest: &idfcVgPb.SearchCkycRequest_CkycRequest{
				RequestId: applicant.GetVendorRequestId(),
				Details: []*idfcVgPb.SearchCkycRequest_CkycRequest_CkycDetails{
					{
						TransactionId: txnId,
						InputIdType:   "C",
						InputIdNo:     userInfo.GetProfile().GetPAN(),
					},
				},
			},
		})
		rpcErr := epifigrpc.RPCError(ckycSearchRes, ckycSearchResErr)
		switch {
		case ckycSearchRes.GetStatus().IsRecordNotFound() || len(ckycSearchRes.GetCkycResponse().GetDetails()) < 1:
			updateLrErr2 := palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, res.GetLoanStep().GetRefId(), p.deeplinkProvider.GetFailedCkycCheckDeeplink(p.deeplinkProvider.GetLoanHeader()))
			if updateLrErr2 != nil {
				lg.Error("failed to update lr next action", zap.Error(updateLrErr2))
				return nil, errors.Wrap(epifierrors.ErrPermanent, updateLrErr2.Error())
			}
			res.LoanStep = lse
			palActivity.MarkLoanStepFailWithSubStatus(res, palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_RECORD_NOT_FOUND)
			return res, nil
		case rpcErr != nil:
			lg.Error("failed to verify ckyc", zap.Error(rpcErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, rpcErr.Error())
		}

		ckycId := ckycSearchRes.GetCkycResponse().GetDetails()[0].GetCkycId()

		// download ckyc details
		ckycRes, ckycResErr := p.idfcPalVgClient.DownloadCkyc(ctx, &idfcVgPb.DownloadCkycRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_IDFC,
			},
			CorrelationId: uuid.New().String(),
			RequestId:     applicant.GetVendorRequestId(),
			Details: []*idfcVgPb.DownloadCkycRequest_CkycDownloadDetails{
				{
					TransactionId: txnId,
					CkycNumber:    ckycId,
					Dob:           userInfo.GetProfile().GetDateOfBirth(),
					MobileNumber:  userInfo.GetProfile().GetPhoneNumber(),
				},
			},
		})

		rpcErr2 := epifigrpc.RPCError(ckycRes, ckycResErr)
		switch {
		case rpcErr2 == nil && ckycRes.GetStatus().IsSuccess():
			// if ckyc status is rejected, set the appropriate sub status in LSE and mark the application as failed
			if ckycRes.GetDetails().GetTransactionStatus() == vgPalIdfcPb.TransactionStatus_TRANSACTION_STATUS_FAILURE {
				res.GetLoanStep().SubStatus = lseSubStatusFromCkycRejection(ckycRes.GetDetails().GetCkycRejectReason())
				res.GetLoanStep().Status = palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
				res.LseFieldMasks = append(res.GetLseFieldMasks(), palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
				return res, nil
			}
			if len(ckycRes.GetCkycPersonalDetail()) == 0 {
				palActivity.MarkLoanStepFailWithSubStatus(res, palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_RECORD_NOT_FOUND)
				lg.Error("no personal details found in ckyc response")
				return res, nil
			}
			if ckycRes.GetPositiveConfirmation() == vgPalIdfcPb.PositiveConfirmation_POSITIVE_CONFIRMATION_NO && !p.isIdfcVkycEnabled(lse.GetActorId()) {
				lg.Error("received no as positive confirmation from ckyc")
				// TODO(mounish): set sub-status if we consider positive confirmation in future
				return nil, errors.Wrap(epifierrors.ErrPermanent, "received no as positive confirmation from ckyc")
			}

			var imageS3Path string
			if !p.isIdfcVkycEnabled(lse.GetActorId()) {
				imageS3Path, err = p.writeCkycImageToS3(ctx, req, ckycRes)
				if err != nil {
					lg.Error("failed to fetch ckyc image and upload to s3", zap.Error(err))
					return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
				}
			}
			positiveConfirmationStatus := getPositiveConfirmationStatus(ckycRes.GetPositiveConfirmation())
			addressPinCodeType := getCkycPinCodeTypeFromVg(ckycRes.GetCkycPersonalDetail()[0].GetAddressPinCodeType())
			palActivity.SetLoanStepDetails(lse, &palpb.LoanStepExecutionDetails{
				Details: &palpb.LoanStepExecutionDetails_CkycStepData{
					CkycStepData: &palpb.CkycStepData{
						CkycImagePath:         imageS3Path,
						CkycId:                ckycId,
						PositiveConfirmation:  positiveConfirmationStatus,
						Name:                  ckycRes.GetCkycPersonalDetail()[0].GetCkycFullName(),
						PAN:                   userInfo.GetProfile().GetPAN(),
						Gender:                ckycRes.GetCkycPersonalDetail()[0].GetCkycGender(),
						PermanentAddress:      ckycRes.GetCkycPersonalDetail()[0].GetPermanentAddress(),
						CorrespondenceAddress: ckycRes.GetCkycPersonalDetail()[0].GetCorrespondenceAddress(),
						Dob:                   ckycRes.GetCkycPersonalDetail()[0].GetCkycDob(),
						VendorSpecificDetails: &palpb.CkycStepData_Idfc{
							Idfc: &palpb.IdfcCkycStepData{
								AddressPinCodeType: addressPinCodeType,
							},
						},
					},
				},
			})

			nextAction, naErr := p.deeplinkProvider.GetReviewScreenDeeplink(p.deeplinkProvider.GetLoanHeader(), ckycRes.GetCkycPersonalDetail()[0].GetCkycFullName(), userInfo.GetProfile().GetPAN(), ckycRes.GetCkycPersonalDetail()[0].GetPermanentAddress(), ckycId, datetime.DateToString(ckycRes.GetCkycPersonalDetail()[0].GetCkycDob(), dobLayout, nil), lse.GetRefId())
			if naErr != nil {
				lg.Error("error while getting review details screen deeplink", zap.Error(naErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, naErr.Error())
			}

			txnErr := p.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
				// update lse to persist the ckyc image path
				lse.Status = palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				lseErr := p.loanStepExecutionDao.Update(txnCtx, lse,
					[]palpb.LoanStepExecutionFieldMask{palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
						palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
				if lseErr != nil {
					return errors.Wrap(lseErr, "failed to update ckyc image path in lse details")
				}

				// update the lr next action to Review details screen
				updateLrErr = palActivity.UpdateNextActionInLoanRequest(txnCtx, p.loanRequestDao, res.GetLoanStep().GetRefId(), nextAction)
				if updateLrErr != nil {
					lg.Error("error while getting review details screen deeplink", zap.Error(err))
					return errors.Wrap(updateLrErr, "error while getting review details screen deeplink")
				}
				return nil
			})
			if txnErr != nil {
				lg.Error("error while updating lse or next action", zap.Error(txnErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, txnErr.Error())
			}
			res.LoanStep = lse
			return res, nil
		case ckycRes.GetStatus().IsRecordNotFound():
			updateLrErr = palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, res.GetLoanStep().GetRefId(), p.deeplinkProvider.GetFailedCkycCheckDeeplink(p.deeplinkProvider.GetLoanHeader()))
			if updateLrErr != nil {
				lg.Error("failed to update lr next action", zap.Error(updateLrErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, updateLrErr.Error())
			}
			res.LoanStep = lse
			palActivity.MarkLoanStepFailWithSubStatus(res, palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_RECORD_NOT_FOUND)
			return res, nil
		default:
			lg.Error("failed to verify ckyc", zap.Error(rpcErr2))
			return nil, errors.Wrap(epifierrors.ErrTransient, rpcErr2.Error())
		}
	})
	return actRes, actErr
}

// IdfcCkycStatus activity is used to poll the sub status of loan step CKYC to check if ckyc details is verified for the user or not
// nolint: funlen
func (p *Processor) IdfcCkycStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// check positive confirmation - final status should be either yes or no
		positiveConfirmationStatus := lse.GetDetails().GetCkycStepData().GetPositiveConfirmation()

		if !checkFinalPositiveConfirmation(positiveConfirmationStatus) && !p.isIdfcVkycEnabled(lse.GetActorId()) {
			// fetch the loan applicant to get the vendor_applicant_id that is to be used as req_id in vendor's api calls
			applicant, err := p.loanApplicantDao.GetByActorId(ctx, req.GetLoanStep().GetActorId())
			if err != nil {
				lg.Error("error while getting loan applicant for the user", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
			}

			// vendor call to check status
			posConfResp, err := p.idfcPalVgClient.CheckPositiveConfirmation(ctx, &idfcVgPb.CheckPositiveConfirmationRequest{
				Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IDFC},
				CorrelationId: uuid.New().String(),
				ReqId:         applicant.GetVendorRequestId(),
			})
			if rpcErr := epifigrpc.RPCError(posConfResp, err); rpcErr != nil {
				lg.Error("failed to poll positive confirmation status", zap.Error(rpcErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, rpcErr.Error())
			}

			status := getPositiveConfirmationStatus(posConfResp.GetPositiveConfirmation())

			// update status if changed
			if status != positiveConfirmationStatus {
				palActivity.SetLoanStepDetails(lse, &palpb.LoanStepExecutionDetails{
					Details: &palpb.LoanStepExecutionDetails_CkycStepData{
						CkycStepData: &palpb.CkycStepData{
							PositiveConfirmation: status,
						},
					},
				})
				lseErr := p.loanStepExecutionDao.Update(ctx, lse,
					[]palpb.LoanStepExecutionFieldMask{palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
				if lseErr != nil {
					lg.Error("failed to update positive confirmation in lse details", zap.Error(lseErr))
					return nil, errors.Wrap(epifierrors.ErrTransient, lseErr.Error())
				}
			}

			// check if positive confirmation changed to yes or no, fail if not changed
			if !checkFinalPositiveConfirmation(status) {
				lg.Error("positive confirmation status pending", zap.String("POSITIVE_CONFIRMATION", status.String()))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("positive confirmation status pending: %s", status.String()))
			}
		}

		positiveConfirmationStatus = lse.GetDetails().GetCkycStepData().GetPositiveConfirmation()

		isCkycUserVerified := lse.GetSubStatus() == palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED
		// user verified the ckyc details, if vkyc flow is enabled, no need to consume positive confirmation status and return from here only.
		if isCkycUserVerified && p.isIdfcVkycEnabled(lse.GetActorId()) {
			return res, nil
		}

		isCkycPositiveConfirmationYes := positiveConfirmationStatus == palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_YES
		isCkycPositiveConfirmationNo := positiveConfirmationStatus == palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_NO

		if isCkycUserVerified && isCkycPositiveConfirmationYes {
			return res, nil
		}

		if isCkycUserVerified && isCkycPositiveConfirmationNo {
			// TODO(mounish): set sub-status if we consider positive confirmation in future
			return nil, errors.Wrap(epifierrors.ErrPermanent, "failed due to positive confirmation no")
		}

		return nil, errors.Wrap(epifierrors.ErrTransient, "ckyc details not verified by the user or positive confirmation failed")
	})
	return actRes, actErr
}

func (p *Processor) IdfcOccupationVerification(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// update the loan request next action to add details screen for Occupation details
		nextAction, err := p.deeplinkProvider.GetAddDetailsScreen(p.deeplinkProvider.GetLoanHeader(), preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION, "", "", lse.GetRefId())
		if err != nil {
			lg.Error("error while getting Add details screen deeplink", zap.Error(err))
		}
		updateLrErr := palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, res.GetLoanStep().GetRefId(), nextAction)
		if updateLrErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, updateLrErr.Error())
		}
		return res, nil
	})
	return actRes, actErr
}

// If ckyc image is available, it will write the image to s3 and return the s3 path, else return empty string
func (p *Processor) writeCkycImageToS3(ctx context.Context, req *palActivityPb.PalActivityRequest, ckycResponse *idfcVgPb.DownloadCkycResponse) (string, error) {
	if ckycResponse.GetImageDetails() == nil {
		return "", nil
	}
	photoImageFound := false
	var photoImage *idfcVgPb.DownloadCkycResponse_ImageDetails
	for _, details := range ckycResponse.GetImageDetails() {
		if details.GetType() == idfcVgPb.CkycImageType_CKYC_IMAGE_TYPE_PHOTO {
			photoImageFound = true
			photoImage = details
			break
		}
	}
	if !photoImageFound {
		return "", nil
	}

	applicant, applicantErr := p.loanApplicantDao.GetByActorId(ctx, req.GetLoanStep().GetActorId())
	if applicantErr != nil {
		return "", palActivity.GetActivityErrFromDaoError(applicantErr)
	}
	vendorReqId := applicant.GetVendorRequestId()

	awsDestinationPath := fmt.Sprintf("idfc_ckyc_images/%v.%v", vendorReqId, photoImage.GetExtension())

	s3Err := p.s3Client.Write(ctx, awsDestinationPath, []byte(photoImage.GetData()), string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if s3Err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in uploading image to s3, err: %v", s3Err))
	}

	return awsDestinationPath, nil
}

func getPositiveConfirmationStatus(status vgPalIdfcPb.PositiveConfirmation) palpb.CkycPositiveConfirmationStatus {
	switch status {
	case vgPalIdfcPb.PositiveConfirmation_POSITIVE_CONFIRMATION_INPROGRESS:
		return palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_INPROGRESS
	case vgPalIdfcPb.PositiveConfirmation_POSITIVE_CONFIRMATION_YES:
		return palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_YES
	case vgPalIdfcPb.PositiveConfirmation_POSITIVE_CONFIRMATION_NO:
		return palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_NO
	default:
		return palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_UNSPECIFIED
	}
}

func checkFinalPositiveConfirmation(status palpb.CkycPositiveConfirmationStatus) bool {
	switch status {
	case palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_YES:
		return true
	case palpb.CkycPositiveConfirmationStatus_CKYC_POSITIVE_CONFIRMATION_STATUS_NO:
		return true
	default:
		return false
	}
}

func getCkycPinCodeTypeFromVg(pinCodeType vgPalIdfcPb.CkycAddressPinCodeType) palpb.IdfcCkycAddressPinCodeType {
	switch pinCodeType {
	case vgPalIdfcPb.CkycAddressPinCodeType_CKYC_ADDRESS_PIN_CODE_TYPE_URBAN:
		return palpb.IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_URBAN
	case vgPalIdfcPb.CkycAddressPinCodeType_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_CORE:
		return palpb.IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_CORE
	case vgPalIdfcPb.CkycAddressPinCodeType_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_IFBL:
		return palpb.IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_RURAL_IFBL
	default:
		return palpb.IdfcCkycAddressPinCodeType_IDFC_CKYC_ADDRESS_PIN_CODE_TYPE_UNSPECIFIED
	}
}

func lseSubStatusFromCkycRejection(rejectReason vgPalIdfcPb.CkycRejectReason) palpb.LoanStepExecutionSubStatus {
	switch rejectReason {
	case vgPalIdfcPb.CkycRejectReason_CKYC_REJECT_REASON_PIN_CODE_UNSERVICEABLE:
		return palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE
	case vgPalIdfcPb.CkycRejectReason_CKYC_REJECT_REASON_PAN_AND_KYC_MISMATCH:
		return palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PAN_AND_KYC_MISMATCH
	case vgPalIdfcPb.CkycRejectReason_CKYC_REJECT_REASON_AUTHENTICATION_FAILED:
		return palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_AUTHENTICATION_FAILED
	case vgPalIdfcPb.CkycRejectReason_CKYC_REJECT_REASON_ACCOUNT_TYPE_4:
		return palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_CKYC_ACCOUNT_TYPE
	case vgPalIdfcPb.CkycRejectReason_CKYC_REJECT_REASON_DATA_VALIDATION_FAILED:
		return palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_VALIDATION_FAILED
	default:
		return palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED
	}
}
