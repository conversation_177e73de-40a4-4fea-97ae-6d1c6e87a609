package federal

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
)

//nolint:funlen
func (p *Processor) FederalProcessLoanApplicationV2(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res, err := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		loanRequest, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("no such loan request exists in FederalProcessLoanApplicationV2, err: %v", err))
			}
			lg.Error("failed to fetch loan request id orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request id orch id in FederalProcessLoanApplicationV2, err: %v", err))
		}

		loanOffer, err := p.loanOffersDao.GetById(ctx, loanRequest.GetOfferId())
		if err != nil {
			lg.Error("failed to fetch loan offer by offer id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan offer by offer id in FederalProcessLoanApplicationV2, err: %v", err))
		}

		// Check if application has already been initiated at vendor
		statusEnqRes, statusErr := p.palVgClient.GetInstantLoanStatusEnquiry(ctx, &palVgPb.GetInstantLoanStatusEnquiryRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
			ApplicationId: loanRequest.GetVendorRequestId(),
			ApiType:       palVgPb.ApiType_API_TYPE_LOAN_PROCESS,
		})
		if te := epifigrpc.RPCError(statusEnqRes, statusErr); te != nil && !statusEnqRes.GetStatus().IsRecordNotFound() {
			if statusEnqRes.GetStatus().GetCode() == rpc.StatusFailedPrecondition().GetCode() {
				logger.Error(ctx, "failed pre-condition for loan status enquiry api", zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed pre-condition for loan status enquiry api in FederalProcessLoanApplicationV2, err: %v", te))
			}
			logger.Error(ctx, "failed to fetch status for loan application", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch status for loan application in FederalProcessLoanApplicationV2, err: %v", te))
		}

		if statusEnqRes.GetStatus().IsRecordNotFound() {
			err = p.initiateNewLoanApplication(ctx, loanRequest, loanOffer, res.GetLoanStep())
			if err != nil {
				return nil, err
			}
		}

		statusRes, err := p.palVgClient.GetInstantLoanInfo(ctx, &palVgPb.GetInstantLoanInfoRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			},
			ApplicationId: loanRequest.GetVendorRequestId(),
		})
		if te := epifigrpc.RPCError(statusRes, err); te != nil {
			lg.Error("failed to fetch loan application status", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan application status in FederalProcessLoanApplicationV2, err: %v", te))
		}

		switch statusRes.GetLoanState() {
		case palVgPb.LoanState_LOAN_STATE_APPROVED_PENDING_DISBURSAL:
			res.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			updateLseErr := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			})
			if updateLseErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("could not update lse details in FederalProcessLoanApplicationV2. %v", updateLseErr))
			}

			// do nothing, loan state is in progress
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("no updates in FederalProcessLoanApplicationV2"))
		case palVgPb.LoanState_LOAN_STATE_DISBURSED:
			res.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		case palVgPb.LoanState_LOAN_STATE_FAILED:
			res.LoanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			return res, nil
		default:
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unknown loan state in FederalProcessLoanApplicationV2, %v", err))
		}
		return res, nil
	})
	return res, err
}

// nolint: funlen
func (p *Processor) initiateNewLoanApplication(ctx context.Context, loanRequest *palPb.LoanRequest, loanOffer *palPb.LoanOffer, loanStepExecution *palPb.LoanStepExecution) error {
	deviceId, _, _, err := p.rpcHelper.GetDeviceAuthDetails(ctx, loanRequest.GetActorId())
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to get device auth details in FederalProcessLoanApplicationV2, err: %v", err))
	}

	processingFeeAndGst, _ := money.Sum(loanRequest.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee(), loanRequest.GetDetails().GetLoanInfo().GetDeductions().GetGst())
	res, err := p.palVgClient.GetInstantLoanApplication(ctx, &palVgPb.GetInstantLoanApplicationRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		ApplicationId:       loanRequest.GetVendorRequestId(),
		OfferId:             loanOffer.GetVendorOfferId(),
		Otp:                 loanRequest.GetDetails().GetOtpInfo().GetLastEnteredOtp(),
		PhoneNumber:         loanRequest.GetDetails().GetPhoneNumber(),
		MaskedAccountNumber: loanRequest.GetDetails().GetMaskedAccountNumber(),
		ProcessingFee:       processingFeeAndGst,
		CustomerDeviceIp:    deviceId,
		LoanAmount:          loanRequest.GetDetails().GetLoanInfo().GetAmount(),
		EmiAmount:           loanRequest.GetDetails().GetLoanInfo().GetEmiAmount(),
		InterestRate:        loanRequest.GetDetails().GetLoanInfo().GetInterestRate(),
		TenureMonths:        loanRequest.GetDetails().GetLoanInfo().GetTenureInMonths(),
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		var isLastAttempt bool
		if res.GetStatus().GetCode() == rpc.StatusFailedPrecondition().GetCode() {
			if res.GetStatus().GetDebugMessage() == "ILE025" {
				isLastAttempt = p.handleOtpFailure(loanRequest, loanStepExecution)
				if isLastAttempt {
					return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("last attempt for the otp in FederalProcessLoanApplicationV2, err: %v", te))
				}
			}
			loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("vg api given failed precondition error in FederalProcessLoanApplicationV2, err: %v", te))
		}
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("vg api given error in FederalProcessLoanApplicationV2, err: %v", te))
	}
	deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: loanRequest.GetVendor(), LoanProgram: loanRequest.GetLoanProgram()})
	dl, err := deeplinkProvider.GetLoanApplicationStatusScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), loanRequest)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while generating loan application status deeplink: %v", err))
	}
	updateLrErr := p.updateNextActionInLoanRequest(ctx, loanStepExecution.GetRefId(), dl)
	if updateLrErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("could not update lr next action in FederalProcessLoanApplicationV2. %v", updateLrErr))
	}
	loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	updateLseErr := p.loanStepExecutionDao.Update(ctx, loanStepExecution, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	})
	if updateLseErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("could not update lse details in FederalProcessLoanApplicationV2. %v", updateLseErr))
	}
	return nil
}

func (p *Processor) handleOtpFailure(loanRequest *palPb.LoanRequest, loanStepExecution *palPb.LoanStepExecution) bool {
	isLastAttempt := false
	if loanRequest.GetDetails().GetOtpInfo().GetAttemptsCount() >= loanRequest.GetDetails().GetOtpInfo().GetMaxAttempts() {
		loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		isLastAttempt = true
	}
	return isLastAttempt
}
