package moneyview_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	moneyviewVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
)

func TestProcessor_MvFetchOffer(t *testing.T) {
	t.<PERSON>()

	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	lse1 := &preapprovedloan.LoanStepExecution{
		Id:      "lse-id-1",
		RefId:   "lr-id-1",
		ActorId: "actor-id-1",
		Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		mockFunc  func(md *processorMocks)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "should fail with transient error when dao call to fetch loan request fails",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					Vendor:        preapprovedloan.Vendor_MONEYVIEW,
					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
					LoanStep:      lse1,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse1, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with transient error when fetchOffer vg api fails with ISE status code",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      lse1,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse1, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(loanRequest1, nil)
				md.mvVgClient.EXPECT().GetOffers(gomock.Any(), &moneyviewVgPb.GetOffersRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
					LeadId: loanRequest1.GetVendorRequestId(),
				}).Return(&moneyviewVgPb.GetOffersResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "should fail with permanent error when fetchOffer vg api gives failed precondition status code",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      lse1,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse1, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(loanRequest1, nil)
				md.mvVgClient.EXPECT().GetOffers(gomock.Any(), &moneyviewVgPb.GetOffersRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
					LeadId: loanRequest1.GetVendorRequestId(),
				}).Return(&moneyviewVgPb.GetOffersResponse{Status: rpc.StatusFailedPrecondition()}, nil)
			},
			wantErr: true,
			want:    nil,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			}},

		{
			name: "should success when fetch offer vg rpc give ok response ",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      lse1,
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), "lse-id-1").Return(lse1, nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), lse1.GetRefId()).Return(loanRequest1, nil).Times(2)
				md.mvVgClient.EXPECT().GetOffers(gomock.Any(), &moneyviewVgPb.GetOffersRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
					LeadId: loanRequest1.GetVendorRequestId(),
				}).Return(&moneyviewVgPb.GetOffersResponse{Status: rpc.StatusOk()}, nil)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanRequestFieldMask{preapprovedloan.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}).Return(nil)
				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), []preapprovedloan.LoanStepExecutionFieldMask{preapprovedloan.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS}).Return(nil)
			},
			wantErr: false,
			want: &palActivityPb.PalActivityResponse{
				LoanStep: &preapprovedloan.LoanStepExecution{
					Id:      "lse-id-1",
					RefId:   "lr-id-1",
					ActorId: "actor-id-1",
					Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
				},
			},
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockFields := initProcessorMocks(ctrl)
			tt.mockFunc(mockFields)
			p := newActivityProcessorWithMocks(mockFields)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.MvFetchOffer, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("MvFetchOffer() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("MvFetchOffer() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("MvFetchOffer() error = %v assertion failed", err)
				return
			case !proto.Equal(result, tt.want):
				t.Errorf("MvFetchOffer() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
