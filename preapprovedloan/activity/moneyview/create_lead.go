//nolint:goimports
package moneyview

import (
	"google.golang.org/genproto/googleapis/type/postaladdress"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	employmentPb "github.com/epifi/gamma/api/employment"
	userpb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/preapprovedloan/helper"

	commonTypesPb "github.com/epifi/gamma/api/typesv2"

	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/userdata"
)

var (
	leadCreationVgRpcFailureStatusCodeToLseSubStatusMap = map[uint32]palPb.LoanStepExecutionSubStatus{
		uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_DUPLICATE_LEAD):              palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_DUPLICATE_LEAD_FAILURE,
		uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_INVALID_USER_AGE):            palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_AGE_CRITERIA_NOT_SATISFIED,
		uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_INVALID_USER_INCOME_DETAILS): palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED,
		uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_LEAD_REJECTED):               palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION,
	}
)

// nolint: funlen, dupl
func (p *Processor) MvCreateLead(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		loanRequest, loanRequestErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if loanRequestErr != nil {
			return nil, errors.Wrap(epifierrors.ErrPermanent, "failed  to get loan request by id")
		}

		loanOffer, loanOfferErr := p.loanOfferDao.GetById(ctx, loanRequest.GetOfferId())
		if loanOfferErr != nil {
			return nil, errors.Wrap(epifierrors.ErrPermanent, "failed  to get loan offer by id")
		}

		stepData, err := p.getCreateLeadStepData(ctx, lse.GetActorId())
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get create lead step data")
		}

		// storing the data which we shared to vendor in lse for auditing and debugging purposes.
		lse.Details = &palPb.LoanStepExecutionDetails{
			Details: &palPb.LoanStepExecutionDetails_CreateLeadStepData{
				CreateLeadStepData: stepData,
			},
		}

		annualFamilyIncome := moneyPkg.Multiply(stepData.GetDeclaredIncome(), decimal.NewFromInt(int64(12)))

		// create lead at vendor's end.
		vgRes, vgErr := p.mvVgClient.CreateLead(ctx, &moneyview.CreateLeadRequest{
			Header:             &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
			PartnerRefId:       loanOffer.GetVendorOfferId(),
			PhoneNumber:        stepData.GetPhoneNumber(),
			Pan:                stepData.GetPan(),
			Name:               stepData.GetName(),
			Gender:             stepData.GetGender(),
			Dob:                stepData.GetDob(),
			EmploymentType:     moneyview.EmploymentType(moneyview.EmploymentType_value[stepData.GetEmploymentType()]),
			DeclaredIncome:     stepData.GetDeclaredIncome(),
			AnnualFamilyIncome: annualFamilyIncome,
			Addresses: []*moneyview.CreateLeadRequest_AddressWithType{
				{
					Type:    moneyview.AddressType_ADDRESS_TYPE_CURRENT,
					Address: stepData.GetCurrentAddress(),
				},
			},
			EmailId:  stepData.GetEmail(),
			UserType: loanProgramToUserType[loanRequest.GetLoanProgram()],
		})
		if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
			lg.Error("got error in mv create lead vg api ", zap.String(logger.LEAD_ID, vgRes.GetLeadId()), zap.String(logger.ACTOR_ID_V2, lse.GetActorId()), zap.Error(te))

			switch vgRes.GetStatus().GetCode() {
			// we need to deactivate the offers and update the loan step to failed in permanent failure cases.
			case uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_DUPLICATE_LEAD), uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_INVALID_USER_AGE),
				uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_INVALID_USER_INCOME_DETAILS), uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION_LEAD_REJECTED),
				uint32(moneyview.CreateLeadResponse_FAILED_PRECONDITION):
				if deactivationErr := p.loanOfferDao.DeactivateLoanOffer(ctx, loanOffer.GetId()); deactivationErr != nil {
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error deactivating mv loan offer, err: %v", deactivationErr))
				}
				// if we get lead id from vendor, persist it in the db for analytics usecases.
				if vgRes.GetLeadId() != "" {
					loanRequest.VendorRequestId = vgRes.GetLeadId()
					updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID})
					if updateErr != nil {
						return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to update lr vendor request id, err: %v", updateErr))
					}
				}
				lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
				lse.SubStatus = leadCreationVgRpcFailureStatusCodeToLseSubStatusMap[vgRes.GetStatus().GetCode()]
				lse.GetDetails().GetCreateLeadStepData().LeadId = vgRes.GetLeadId()
				res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS)
				return res, nil
			default:
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in vg res for mv create lead, err: %v", te))
			}
		}

		loanRequest.VendorRequestId = vgRes.GetLeadId()
		lse.GetDetails().GetCreateLeadStepData().LeadId = vgRes.GetLeadId()

		updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID})
		if updateErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to update lr vendor request id, err: %v", updateErr))
		}

		res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS)
		res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		return res, nil
	})
	return actRes, actErr
}

//nolint:funlen,dupl
func (p *Processor) getCreateLeadStepData(ctx context.Context, actorId string) (*palPb.CreateLeadStepData, error) {
	userData, err := p.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{
		ActorId: actorId,
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch user by actor ID")
	}

	// Fetch employment type with fallback
	mvEmploymentType := moneyview.EmploymentType_EMPLOYMENT_TYPE_SALARIED

	if userData.GetEmploymentDetails().GetEmploymentType() == commonTypesPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED {
		employmentType, employmentErr := p.getEmploymentTypeOfUser(ctx, actorId)
		if employmentErr != nil {
			return nil, errors.Wrap(employmentErr, "error in getting employment type")
		}
		if employmentType != employmentPb.EmploymentType_SALARIED {
			mvEmploymentType = moneyview.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED
		}
	} else if userData.GetEmploymentDetails().GetEmploymentType() != commonTypesPb.EmploymentType_EMPLOYMENT_TYPE_SALARIED {
		mvEmploymentType = moneyview.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED
	}

	// Fetch address with fallback
	mvCurrentAddress := userData.GetAddress()

	var user *userpb.User
	if mvCurrentAddress == nil {
		user, err = p.rpcHelper.GetUserByActorId(ctx, actorId)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get user by actor id")
		}
		addresses := user.GetProfile().GetAddresses()

		// if no address are found in user profile, fetch the addresses using get address rpc to fetch the kyc address of the user.
		if len(addresses) == 0 {
			addressesRes, addressErr := p.usersClient.GetAllAddresses(ctx, &userpb.GetAllAddressesRequest{
				UserId: user.GetId(),
			})
			if rpcErr := epifigrpc.RPCError(addressesRes, addressErr); rpcErr != nil {
				return nil, errors.Wrap(rpcErr, "usersClient.GetAllAddresses rpc call failed")
			}
			addresses = make(map[string]*postaladdress.PostalAddress, len(addressesRes.GetAddresses()))
			for addressType, address := range addressesRes.GetAddresses() {
				if len(address.GetAddresses()) != 0 {
					addresses[addressType] = address.GetAddresses()[0]
				}
			}
		}
		sortedAddresses := helper.GetSortedAddressesForLoan(addresses, []commonTypesPb.AddressType{
			commonTypesPb.AddressType_LOAN_COMMUNICATION,
			commonTypesPb.AddressType_PERMANENT,
			commonTypesPb.AddressType_SHIPPING,
			commonTypesPb.AddressType_MAILING,
			commonTypesPb.AddressType_ALL,
			commonTypesPb.AddressType_ADDRESS_TYPE_UNSPECIFIED,
		})
		if len(sortedAddresses) != 0 {
			mvCurrentAddress = commonTypesPb.GetFromBeAddress(sortedAddresses[0].GetAddress())
		}
	}

	// Fetch income with fallback
	declaredMonthlyIncome := userData.GetEmploymentDetails().GetMonthlyIncome()

	if declaredMonthlyIncome.GetUnits() == 0 {
		if user == nil {
			user, err = p.rpcHelper.GetUserByActorId(ctx, actorId)
			if err != nil {
				return nil, errors.Wrap(err, "failed to get user by actor id")
			}
		}
		declaredMonthlyIncome = moneyPkg.AmountINR(int64(user.GetProfile().GetSalaryRange().GetMaxValue()) / 12).GetPb()
		if declaredMonthlyIncome.GetUnits() == 0 {
			// for now taking 15k as default monthly income value if income data is not present with us,
			// **Note** : this is a short temp solve to avoid MV funnel drop offs due to missing data.
			// todo (utkarsh) : discuss with Product team on a solve for this.
			declaredMonthlyIncome = moneyPkg.AmountINR(15000).GetPb()
		}
	}

	name := userData.GetBestName()
	if name == nil {
		if user == nil {
			user, err = p.rpcHelper.GetUserByActorId(ctx, actorId)
			if err != nil {
				return nil, errors.Wrap(err, "failed to get user by actor id")
			}
		}
		name = user.GetProfile().GetKycName()
	}

	gender := userData.GetGivenGender()
	if gender == commonTypesPb.Gender_GENDER_UNSPECIFIED {
		if user == nil {
			user, err = p.rpcHelper.GetUserByActorId(ctx, actorId)
			if err != nil {
				return nil, errors.Wrap(err, "failed to get user by actor id")
			}
		}
		gender = user.GetProfile().GetKycGender()
	}

	return &palPb.CreateLeadStepData{
		Pan:            userData.GetPan(),
		Name:           name,
		PhoneNumber:    userData.GetMobileNumber(),
		Gender:         gender,
		Dob:            userData.GetGivenDateOfBirth(),
		EmploymentType: mvEmploymentType.String(),
		DeclaredIncome: declaredMonthlyIncome,
		CurrentAddress: mvCurrentAddress,
		Email:          userData.GetEmail(),
	}, nil

}

func (p *Processor) getEmploymentTypeOfUser(ctx context.Context, actorId string) (employmentPb.EmploymentType, error) {
	employmentRes, employmentErr := p.employmentClient.GetEmploymentInfo(ctx, &employmentPb.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(employmentRes, employmentErr); te != nil {
		if employmentRes.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "employment details not found", zap.String(logger.ACTOR_ID_V2, actorId))
			return employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED, nil
		}
		return employmentPb.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED, fmt.Errorf("failed to fetch employment info %w", te)
	}
	return employmentRes.GetEmploymentData().GetEmploymentType(), nil
}
