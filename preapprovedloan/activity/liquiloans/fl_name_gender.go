// nolint : protogetter
package liquiloans

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

// NameGenderCheck activity checks if user data is available in user's table or not. If data is not present, this activity will set the next action
// to NAME_AND_GENDER screen with default name as blank and mark the lse status to IN_PROGRESS.
func (p *Processor) NameGenderCheck(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := p.ExecuteWork(ctx, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		user, userErr := p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
		if userErr != nil {
			lg.Error("failed to fetch user by actor ID", zap.Error(userErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch user by actor ID, err: %v", userErr))
		}

		dlProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})

		if user.GetProfile().GetGivenName() == nil || user.GetProfile().GetGivenGender() == typesPb.Gender_GENDER_UNSPECIFIED {
			txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
			if txnExecErr != nil {
				lg.Error("failed to get txn executor by ownership", zap.Error(txnExecErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get txn executor by ownership, err: %v", txnExecErr))
			}
			txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
				// set the next action to NAME_AND_GENDER
				if updateErr := p.updateNextActionInLoanRequest(txnCtx, lse.GetRefId(),
					dlProvider.GetNameAndGenderScreenDeeplink(dlProvider.GetLoanHeader(), lse.GetRefId(), user.GetProfile().GetGivenName().ToSentenceCaseString())); updateErr != nil {
					lg.Error("failed to update the LR next action to NAME_AND_GENDER", zap.Error(updateErr))
					return errors.Wrap(updateErr, fmt.Sprintf("failed to update LR next action in txn"))
				}

				// set the lse status to in_progress
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
				if err := p.loanStepExecutionDao.Update(txnCtx, res.GetLoanStep(), res.LseFieldMasks); err != nil {
					lg.Error("failed to update the lse status", zap.Error(err))
					return errors.Wrap(err, "failed to update LSE status in txn")
				}
				return nil
			})
			if txnErr != nil {
				lg.Error("failed to update the LR next action and LSE next action in transaction", zap.Error(txnErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update the LR next action and LSE next action in transaction, err: %v", txnErr))
			}
		} else {
			// set the lse status to success
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
			if err := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), res.LseFieldMasks); err != nil {
				lg.Error("failed to update the lse status", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LSE status in txn, err: %v", err))
			}
		}
		return res, nil
	})
	return actRes, actErr
}
