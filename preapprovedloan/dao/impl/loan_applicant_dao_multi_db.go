//nolint:dupl
package impl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"

	"github.com/google/wire"
	cmap "github.com/orcaman/concurrent-map"
	pkgErrors "github.com/pkg/errors"
	"gorm.io/gorm"
)

type CrdbLoanApplicantDaoMultiDB struct {
	*CrdbLoanApplicantDao
}

var _ dao.LoanApplicantDao = &CrdbLoanApplicantDaoMultiDB{}

var LoanApplicantDaoMultiDBWireSet = wire.NewSet(NewCrdbLoanApplicantDaoMultiDB, wire.Bind(new(dao.LoanApplicantDao), new(*CrdbLoanApplicantDaoMultiDB)))

func NewCrdbLoanApplicantDaoMultiDB(dbResourceProvider *storage.DBResourceProvider[*gorm.DB], idGen idgen.IdGenerator) *CrdbLoanApplicantDaoMultiDB {
	return &CrdbLoanApplicantDaoMultiDB{
		CrdbLoanApplicantDao: NewCrdbLoanApplicantDao(dbResourceProvider, idGen),
	}
}

func (c *CrdbLoanApplicantDaoMultiDB) GetById(ctx context.Context, id string) (*preapprovedloanPb.LoanApplicant, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanApplicantDaoMultiDB", "GetById", time.Now())
	_, ok := gormctxv2.FromContext(ctx, nil)
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	if ok || ow != commontypes.Ownership_EPIFI_TECH {
		return c.CrdbLoanApplicantDao.GetById(ctx, id)
	}
	palOwnerships := helper.GetPalOwnerships()
	dataMap := cmap.New()
	grp, grpCtx := errgroup.WithContext(ctx)
	for _, ownership := range palOwnerships {
		ow := ownership
		grp.Go(func() error {
			daoData, daoErr := c.CrdbLoanApplicantDao.GetById(epificontext.WithOwnership(grpCtx, ow), id)
			if daoErr != nil && !pkgErrors.Is(daoErr, epifierrors.ErrRecordNotFound) {
				return daoErr
			}
			if daoData != nil {
				dataMap.Set(ow.String(), daoData)
			}
			return nil
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, pkgErrors.Wrap(err, "error in grp executions for GetById")
	}
	if fedData, ok := dataMap.Get(commontypes.Ownership_FEDERAL_BANK.String()); ok {
		return fedData.(*preapprovedloanPb.LoanApplicant), nil
	}
	for _, data := range dataMap.Items() {
		if data != nil {
			return data.(*preapprovedloanPb.LoanApplicant), nil
		}
	}
	return nil, epifierrors.ErrRecordNotFound
}

func (c *CrdbLoanApplicantDaoMultiDB) GetByActorId(ctx context.Context, actorId string) (*preapprovedloanPb.LoanApplicant, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanApplicantDaoMultiDB", "GetByActorId", time.Now())
	_, ok := gormctxv2.FromContext(ctx, nil)
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	if ok || ow != commontypes.Ownership_EPIFI_TECH {
		return c.CrdbLoanApplicantDao.GetByActorId(ctx, actorId)
	}
	palOwnerships := helper.GetPalOwnerships()
	dataMap := cmap.New()
	grp, grpCtx := errgroup.WithContext(ctx)
	for _, ownership := range palOwnerships {
		ow := ownership
		grp.Go(func() error {
			daoData, daoErr := c.CrdbLoanApplicantDao.GetByActorId(epificontext.WithOwnership(grpCtx, ow), actorId)
			if daoErr != nil && !pkgErrors.Is(daoErr, epifierrors.ErrRecordNotFound) {
				return daoErr
			}
			if daoData != nil {
				dataMap.Set(ow.String(), daoData)
			}
			return nil
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, pkgErrors.Wrap(err, "error in grp executions for GetById")
	}
	if fedData, ok := dataMap.Get(commontypes.Ownership_FEDERAL_BANK.String()); ok {
		return fedData.(*preapprovedloanPb.LoanApplicant), nil
	}
	for _, data := range dataMap.Items() {
		if data != nil {
			return data.(*preapprovedloanPb.LoanApplicant), nil
		}
	}
	return nil, epifierrors.ErrRecordNotFound
}

func (c *CrdbLoanApplicantDaoMultiDB) GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx context.Context, actorId string, vendor preapprovedloanPb.Vendor, loanProgram preapprovedloanPb.LoanProgram, programVersion enums.LoanProgramVersion) (*preapprovedloanPb.LoanApplicant, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanApplicantDaoMultiDB", "GetByActorIdAndVendorAndLoanProgramAndProgramVersion", time.Now())
	if vendor == preapprovedloanPb.Vendor_VENDOR_UNSPECIFIED {
		_, ok := gormctxv2.FromContext(ctx, nil)
		ow := epificontext.OwnershipFromContext[context.Context](ctx)
		if ok || ow != commontypes.Ownership_EPIFI_TECH {
			vendor = helper.GetPalVendor(ow)
			return c.CrdbLoanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(epificontext.WithOwnership(ctx, ow), actorId, vendor, loanProgram, programVersion)
		}
		palOwnerships := helper.GetPalOwnerships()
		dataMap := cmap.New()
		grp, grpCtx := errgroup.WithContext(ctx)
		for _, ownership := range palOwnerships {
			ownshp := ownership
			vdr := helper.GetPalVendor(ownshp)
			lps := helper.GetLoanPrograms(vdr, loanProgram)
			for _, val := range lps {
				lp := val
				grp.Go(func() error {
					daoData, daoErr := c.CrdbLoanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(epificontext.WithOwnership(grpCtx, ownshp), actorId, vdr, lp, programVersion)
					if daoErr != nil && !pkgErrors.Is(daoErr, epifierrors.ErrRecordNotFound) {
						return daoErr
					}
					if daoData != nil {
						dataMap.Set(ownshp.String(), daoData)
					}
					return nil
				})
			}
		}
		if err := grp.Wait(); err != nil {
			return nil, pkgErrors.Wrap(err, "error in grp executions for GetByActorIdAndVendorAndLoanProgram")
		}
		if fedData, ok := dataMap.Get(commontypes.Ownership_FEDERAL_BANK.String()); ok {
			return fedData.(*preapprovedloanPb.LoanApplicant), nil
		}
		for _, data := range dataMap.Items() {
			if data != nil {
				return data.(*preapprovedloanPb.LoanApplicant), nil
			}
		}
		return nil, epifierrors.ErrRecordNotFound
	}
	return c.CrdbLoanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor)), actorId, vendor, loanProgram, programVersion)
}

func (c *CrdbLoanApplicantDaoMultiDB) GetByVendorRequestId(ctx context.Context, vendorRequestId string) (*preapprovedloanPb.LoanApplicant, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanApplicantDaoMultiDB", "GetByVendorRequestId", time.Now())
	_, ok := gormctxv2.FromContext(ctx, nil)
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	if ok || ow != commontypes.Ownership_EPIFI_TECH {
		return c.CrdbLoanApplicantDao.GetByVendorRequestId(ctx, vendorRequestId)
	}
	palOwnerships := helper.GetPalOwnerships()
	dataMap := cmap.New()
	grp, grpCtx := errgroup.WithContext(ctx)
	for _, ownership := range palOwnerships {
		ow := ownership
		grp.Go(func() error {
			daoData, daoErr := c.CrdbLoanApplicantDao.GetByVendorRequestId(epificontext.WithOwnership(grpCtx, ow), vendorRequestId)
			if daoErr != nil && !pkgErrors.Is(daoErr, epifierrors.ErrRecordNotFound) {
				return daoErr
			}
			if daoData != nil {
				dataMap.Set(ow.String(), daoData)
			}
			return nil
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, pkgErrors.Wrap(err, "error in grp executions for GetByVendorRequestId")
	}
	if fedData, ok := dataMap.Get(commontypes.Ownership_FEDERAL_BANK.String()); ok {
		return fedData.(*preapprovedloanPb.LoanApplicant), nil
	}
	for _, data := range dataMap.Items() {
		if data != nil {
			return data.(*preapprovedloanPb.LoanApplicant), nil
		}
	}
	return nil, epifierrors.ErrRecordNotFound
}

func (c *CrdbLoanApplicantDaoMultiDB) GetBatchByVendorApplicantIds(ctx context.Context, vendorApplicantIds []string) ([]*preapprovedloanPb.LoanApplicant, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanApplicantDaoMultiDB", "GetBatchByVendorApplicantIds", time.Now())
	_, ok := gormctxv2.FromContext(ctx, nil)
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	if ok || ow != commontypes.Ownership_EPIFI_TECH {
		return c.CrdbLoanApplicantDao.GetBatchByVendorApplicantIds(ctx, vendorApplicantIds)
	}
	palOwnerships := helper.GetPalOwnerships()
	dataMap := cmap.New()
	grp, grpCtx := errgroup.WithContext(ctx)
	for _, ownership := range palOwnerships {
		ow := ownership
		grp.Go(func() error {
			daoData, daoErr := c.CrdbLoanApplicantDao.GetBatchByVendorApplicantIds(epificontext.WithOwnership(grpCtx, ow), vendorApplicantIds)
			if daoErr != nil && !pkgErrors.Is(daoErr, epifierrors.ErrRecordNotFound) {
				return daoErr
			}
			if daoData != nil {
				dataMap.Set(ow.String(), daoData)
			}
			return nil
		})
	}
	if err := grp.Wait(); err != nil {
		return nil, pkgErrors.Wrap(err, "error in grp executions for GetByVendorRequestId")
	}

	var loanApplicantsProto []*preapprovedloanPb.LoanApplicant
	for _, data := range dataMap.Items() {
		if data != nil {
			loanApplicantsProto = append(loanApplicantsProto, data.([]*preapprovedloanPb.LoanApplicant)...)
		}
	}
	return loanApplicantsProto, epifierrors.ErrRecordNotFound
}
