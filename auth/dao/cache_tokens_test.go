package dao_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/auth/config/genconf"
	"github.com/epifi/gamma/auth/dao"
	"github.com/epifi/gamma/auth/dao/mocks"
	"github.com/epifi/gamma/auth/dao/model"
	mock_cache "github.com/epifi/be-common/pkg/cache/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTestv2 "github.com/epifi/be-common/pkg/test/v2"
)

type SetActivitySuite struct {
	daoPgdb *dao.TokenStoresPgdb
}

var (
	slaTS                  *SetActivitySuite
	gconf                  *genconf.Config
	timeFix                = time.Now()
	parsedTimeFix, timeErr = time.Parse(time.RFC3339, timeFix.Format(time.RFC3339))

	idFix1 = "1"
)

type mockedDependencies struct {
	mockCs              *mock_cache.MockCacheStorage
	mockIdCacheDao      *mocks.MockTokenStoresIdCacheDao
	mockSubjectCacheDao *mocks.MockTokenStoresSubjectCacheDao
	mockDB              *mocks.MockTokenStoresDBDao
	mockDBV2            *mocks.MockTokenStoresDBDaoV2
}

func TestTokenStoresCache_StoreToken(t *testing.T) {
	var (
		prefix = gconf.TokenStoresCacheConfig().TokenStoresPrefix()
		ctx    = context.Background()
	)
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, pgdb, pgdbName, []string{"token_stores"})
	t1Actor := newToken("1", 1231231231)
	t1Actor.CreatedAt = time.Now()
	t1Actor.UpdatedAt = t1Actor.CreatedAt
	t1Actor.TokenType = auth.TokenType_ACCESS_TOKEN

	t2Actor := newToken("2", 3213213213)
	t2Actor.CreatedAt = time.Now()
	t2Actor.UpdatedAt = t2Actor.CreatedAt
	t2Actor.TokenType = auth.TokenType_ACCESS_TOKEN

	t3Actor := newToken("3", 2312312312)
	t3Actor.DeviceRegistrationStatus = auth.DeviceRegistrationStatus_REGISTERED

	t4Actor := newToken("3", 2312312312)
	t4Actor.TokenType = auth.TokenType_APP_INSIGHTS_ACCESS_TOKEN
	t4Actor.DeviceRegistrationStatus = auth.DeviceRegistrationStatus_REGISTERED

	type args struct {
		token *model.TokenStore
	}
	type testParams struct {
		// to avoid model asserts when there is no DB call, we assert for RNF error
		ignoreDBAssert bool
	}
	tests := []struct {
		name       string
		args       args
		wantErr    error
		wantMocks  func(args args, md *mock_cache.MockCacheStorage)
		want       *model.TokenStore
		testParams *testParams
	}{
		{
			name: "store token success: redis error",
			args: args{
				token: t1Actor,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				tokenBytes, _ := json.Marshal(t1Actor)
				md.EXPECT().Set(ctx, prefix+args.token.ID, string(tokenBytes), gomock.Any()).Return(fmt.Errorf("redis error"))
			},
			want:       t1Actor,
			wantErr:    nil,
			testParams: &testParams{},
		},
		{
			name: "store token success: no error from redis",
			args: args{
				token: t2Actor,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				tokenBytes, _ := json.Marshal(t2Actor)
				md.EXPECT().Set(ctx, prefix+args.token.ID, string(tokenBytes), gomock.Any()).Return(nil)
			},
			want:       t2Actor,
			wantErr:    nil,
			testParams: &testParams{},
		},
		{
			name: "store token failed: unique constraint error",
			args: args{
				token: t2Actor,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				tokenBytes, _ := json.Marshal(t2Actor)
				md.EXPECT().Set(ctx, prefix+args.token.ID, string(tokenBytes), gomock.Any()).Return(nil)
			},
			want:       t2Actor,
			wantErr:    nil,
			testParams: &testParams{},
		},
		{
			name: "store token only in redis, ignore DB call as there is no ",
			args: args{
				token: t3Actor,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				tokenBytes, _ := json.Marshal(args.token)
				md.EXPECT().Set(ctx, prefix+args.token.ID, string(tokenBytes), gomock.Any()).Return(nil)
			},
			wantErr: nil,
			testParams: &testParams{
				ignoreDBAssert: true,
			},
		},
		{
			name: "store token both in redis and DB for other token types ",
			args: args{
				token: t4Actor,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				tokenBytes, _ := json.Marshal(args.token)
				md.EXPECT().Set(ctx, prefix+args.token.ID, string(tokenBytes), gomock.Any()).Return(nil)
			},
			want:       t4Actor,
			wantErr:    nil,
			testParams: &testParams{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockCache := mock_cache.NewMockCacheStorage(ctrl)
			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetEnableWritesOnlyForUnOnboardedUsers(true, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetUseTokenStoresDaoV2(false, true, nil)
			tsCache := dao.NewTokenStoresCache(slaTS.daoPgdb, nil, mockCache, gconf.TokenStoresCacheConfig())
			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, mockCache)
			}
			err := tsCache.StoreToken(ctx, tt.args.token, &model.StoreTokenParams{
				IsUnOnboardedUser: isUnOnboardedUserFlag(tt.args.token.DeviceRegistrationStatus),
			})
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}

			got, err := tokenStoresDao.GetTokenById(context.Background(), tt.args.token.ID)
			if err != nil && !storageV2.IsRecordNotFoundError(err) {
				t.Errorf("error in GetTokenById() error = %v", err)
			}
			if tt.testParams.ignoreDBAssert {
				assert.Equal(t, err, gorm.ErrRecordNotFound)
				return
			}
			assertTokenStoreModel(t, got, tt.want)
		})
	}

}

func TestTokenStoresCache_GetTokenById(t *testing.T) {
	var (
		prefix = gconf.TokenStoresCacheConfig().TokenStoresPrefix()
		ctx    = context.Background()
	)
	pkgTestv2.TruncateAndPopulateRdsFixtures(t, pgdb, pgdbName, []string{"token_stores"})
	t1Actor := newToken("1", 1231231231)
	t1Actor.TokenType = auth.TokenType_ACCESS_TOKEN

	t2Actor := newToken("2", 3213213213)
	t2Actor.TokenType = auth.TokenType_REFRESH_TOKEN

	err := insertTokensPgdb(pgdb, t1Actor)
	if err != nil {
		t.Errorf("error in creating tokens: (%v)", err)
	}

	type args struct {
		id string
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantMocks func(args args, md *mock_cache.MockCacheStorage)
		want      *model.TokenStore
	}{
		{
			name: "get token by ID: fetch from cache success",
			args: args{
				id: t1Actor.ID,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				tokenBytes, _ := json.Marshal(t1Actor)
				md.EXPECT().Get(ctx, prefix+args.id).Return(string(tokenBytes), nil)
			},
			want:    t1Actor,
			wantErr: nil,
		},
		{
			name: "get token by ID record not found: redis failed , CRDB record not found error",
			args: args{
				id: t2Actor.ID,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				md.EXPECT().Get(ctx, prefix+args.id).Return("", fmt.Errorf("redis error"))
			},
			want:    nil,
			wantErr: gorm.ErrRecordNotFound,
		},
		{
			name: "get token by ID found: redis failed , CRDB record found",
			args: args{
				id: t1Actor.ID,
			},
			wantMocks: func(args args, md *mock_cache.MockCacheStorage) {
				md.EXPECT().Get(ctx, prefix+args.id).Return("", fmt.Errorf("redis error"))
			},
			want:    t1Actor,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockCache := mock_cache.NewMockCacheStorage(ctrl)
			_ = gconf.TokenStoresCacheConfig().SetIsCachingEnabled(true, true, nil)
			_ = gconf.TokenStoresCacheConfig().SetUseTokenStoresDaoV2(false, true, nil)
			tsCache := dao.NewTokenStoresCache(slaTS.daoPgdb, nil, mockCache, gconf.TokenStoresCacheConfig())
			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, mockCache)
			}
			got, err := tsCache.GetTokenById(ctx, tt.args.id)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
			assertTokenStoreModel(t, got, tt.want)
		})
	}

}

func TestTokenStoresCache_ExpireTokenByPhoneNumber(t *testing.T) {
	var (
		ctx = context.Background()
	)

	t1Actor := newToken("1", 1231231231)
	t1Actor.TokenType = auth.TokenType_ACCESS_TOKEN

	type args struct {
		tokenType      auth.TokenType
		status         model.TokenStatus
		phoneNum       *commontypes.PhoneNumber
		exceptIds      []string
		deletionReason auth.TokenDeletionReason
	}
	tests := []struct {
		name      string
		args      args
		wantMocks func(args args, md *mockedDependencies)
		wantErr   error
		want      []*model.TokenStore
	}{
		{
			name: "delete token by phone number : error",
			args: args{
				tokenType:      auth.TokenType_ACCESS_TOKEN,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				exceptIds:      []string{t1Actor.ID},
				deletionReason: auth.TokenDeletionReason_TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDB.EXPECT().ExpireTokenByPhoneNumber(ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr: epifierrors.ErrRecordNotFound,
			want:    nil,
		},
		{
			name: "delete token by phone number refresh token : success",
			args: args{
				tokenType:      auth.TokenType_REFRESH_TOKEN,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				exceptIds:      []string{t1Actor.ID},
				deletionReason: auth.TokenDeletionReason_TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDB.EXPECT().ExpireTokenByPhoneNumber(ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return([]*model.TokenStore{t1Actor, tokenStoreFixture2}, nil)
				md.mockCs.EXPECT().Delete(ctx, getPrefix(args.tokenType)+t1Actor.ID).Return(nil)
				md.mockCs.EXPECT().Delete(ctx, getPrefix(args.tokenType)+tokenStoreFixture2.ID).Return(nil)
			},
			wantErr: nil,
			want:    []*model.TokenStore{t1Actor, tokenStoreFixture2},
		},
		{
			name: "delete token by phone number refresh token : error",
			args: args{
				tokenType:      auth.TokenType_REFRESH_TOKEN,
				status:         model.TokenExpired,
				phoneNum:       t1Actor.PhoneNumber,
				exceptIds:      []string{t1Actor.ID},
				deletionReason: auth.TokenDeletionReason_TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE,
			},
			wantMocks: func(args args, md *mockedDependencies) {
				md.mockDB.EXPECT().ExpireTokenByPhoneNumber(ctx, args.tokenType, args.status, args.phoneNum, args.deletionReason, args.exceptIds).Return([]*model.TokenStore{t1Actor}, nil)
				md.mockCs.EXPECT().Delete(ctx, getPrefix(args.tokenType)+t1Actor.ID).Return(errors.New("redis error"))
			},
			wantErr: errors.New("redis error"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockCache := mock_cache.NewMockCacheStorage(ctrl)
			mockDBDao := mocks.NewMockTokenStoresDBDao(ctrl)
			_ = gconf.TokenStoresCacheConfig().SetUseTokenStoresDaoV2(false, true, nil)
			tsCache := dao.NewTokenStoresCache(mockDBDao, nil, mockCache, gconf.TokenStoresCacheConfig())
			if tt.wantMocks != nil {
				tt.wantMocks(tt.args, &mockedDependencies{
					mockCs: mockCache,
					mockDB: mockDBDao,
				})
			}
			got, err := tsCache.ExpireTokenByPhoneNumber(ctx, tt.args.tokenType, tt.args.status, tt.args.phoneNum, tt.args.deletionReason, tt.args.exceptIds...)
			if tt.wantErr != nil && err.Error() != tt.wantErr.Error() {
				t.Errorf("unexpected error \nwant: %v\n got: %v\n", tt.wantErr, err)
			}
			assert.Equal(t, got, tt.want)
		})
	}
}

func getPrefix(tokenType auth.TokenType) string {
	return gconf.TokenStoresCacheConfig().TokenStoresPrefix()
}

// todo: remove duplication in another PR
func isUnOnboardedUserFlag(deviceReg auth.DeviceRegistrationStatus) commontypes.BooleanEnum {
	if deviceReg == auth.DeviceRegistrationStatus_UNREGISTERED {
		return commontypes.BooleanEnum_TRUE
	}
	return commontypes.BooleanEnum_FALSE
}
