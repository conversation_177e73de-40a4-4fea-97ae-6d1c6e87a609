package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/gamma/auth/dao/model"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/auth/developer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/auth/dao"
)

const maxOtpRecords = 10

type OtpEntity struct {
	AuthDao *dao.AuthDao
}

func NewOtpEntity(authDao *dao.AuthDao) *OtpEntity {
	return &OtpEntity{
		AuthDao: authDao,
	}
}

func (o *OtpEntity) FetchParamList(ctx context.Context, entity developer.AuthEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            phoneNumberFilter,
			Label:           "Phone Number",
			Type:            db_state.ParameterDataType_PHONE_NUMBER,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            limit,
			Label:           getLimitLabel(),
			Type:            db_state.ParameterDataType_INTEGER,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (o *OtpEntity) FetchData(ctx context.Context, entity developer.AuthEntity,
	filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", fmt.Errorf("filter cannot be nil")
	}
	var (
		phoneNum     *commontypes.PhoneNumber
		limitRecords int64
	)
	limitRecords = maxOtpRecords
	for _, filter := range filters {
		switch filter.ParameterName {
		case phoneNumberFilter:
			phoneNum = filter.GetPhoneNumber()
		case limit:
			limitVal := filter.GetIntegerValue()
			if limitVal > 0 && limitVal < maxOtpRecords {
				limitRecords = limitVal
			}
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
		}
	}
	otpRecords, err := o.AuthDao.GetOtpsByPhoneNumberLimit(ctx, phoneNum, limitRecords)
	if err != nil {
		logger.Error(ctx, "error getting otps by phone number", zap.Error(err))
		return "", fmt.Errorf("error while fetching record : %w", err)
	}
	otpResponses := getOtpResponseList(otpRecords)
	marshalled, err := json.Marshal(otpResponses)
	if err != nil {
		logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
		return "", fmt.Errorf("error in marshal to json: %v", err)
	}
	return string(marshalled), nil
}

type otpResponse struct {
	ID        uint
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time

	// Actor information
	PhoneNumber *commontypes.PhoneNumber
	Device      *commontypes.Device
	Email       string

	// Unique request id of Generate Request
	Token string

	// Number of invalid attempts. Any attempt after `x` invalid attempts are rejected
	VerifyAttempts uint8

	// Number of attempts made to delivery OTP via SMS
	SmsAttempts uint8

	// Time stamp of most recent SMS
	SmsTimestamp time.Time

	// Number of attempts made to deliver OTP
	SendAttempts uint8

	// Time stamp of most recent OTP sent
	LastSentAt time.Time

	// Represents if the entry is active. Once successfully verified, will be marked as inactive
	Active bool

	// ownership of the actor. Values can be - epifi_tech, epifi_wealth
	Ownership commontypes.Ownership
}

func getOtpResponse(otp *model.Otp) *otpResponse {
	return &otpResponse{
		ID:             otp.ID,
		CreatedAt:      otp.CreatedAt,
		UpdatedAt:      otp.UpdatedAt,
		DeletedAt:      &otp.DeletedAt.Time,
		PhoneNumber:    otp.PhoneNumber,
		Device:         otp.Device,
		Email:          otp.Email,
		Token:          otp.Token,
		VerifyAttempts: otp.VerifyAttempts,
		SmsAttempts:    otp.SmsAttempts,
		SmsTimestamp:   otp.SmsTimestamp,
		SendAttempts:   otp.SendAttempts,
		LastSentAt:     otp.LastSentAt,
		Active:         otp.Active,
		Ownership:      otp.Ownership,
	}
}

func getOtpResponseList(otpRecords []*model.Otp) []*otpResponse {
	var otpResponses []*otpResponse
	for _, otp := range otpRecords {
		otpResponses = append(otpResponses, getOtpResponse(otp))
	}
	return otpResponses
}

func getLimitLabel() string {
	return fmt.Sprintf("limit (< %d)", maxOtpRecords)
}
