package dao

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	redisV9 "github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/testing/protocmp"

	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/segment/config"
	segmentError "github.com/epifi/gamma/segment/dao/error"
)

var (
	sudts *SegmentUserDaoTestSuite
)

type SegmentUserDaoTestSuite struct {
	rdb    *redisV9.Client
	dao    SegmentUserDao
	config *config.Config
}

func NewSegmentUserDaoTestSuite(rdb *redisV9.Client, dao SegmentUserDao, config *config.Config) *SegmentUserDaoTestSuite {
	return &SegmentUserDaoTestSuite{
		rdb:    rdb,
		dao:    dao,
		config: config,
	}
}

func TestRedisSegmentUserDao_GetLatestSegmentInstances(t *testing.T) {
	type args struct {
		ctx            context.Context
		segmentIds     []string
		givenTimestamp int64
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]*segmentPb.SegmentInstance
		wantErr error
	}{
		{
			name: "should get all latest timestamps successfully",
			args: args{
				ctx:            context.Background(),
				segmentIds:     []string{"segment-1", "segment-2"},
				givenTimestamp: 1653313020,
			},
			want: map[string]*segmentPb.SegmentInstance{
				"segment-1": {
					SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
					Id:            "segment-1-instance-1",
					CreatedAt:     &timestamp.Timestamp{Seconds: 1653313011},
				},
				"segment-2": {
					SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
					Id:            "segment-2-instance-1",
					CreatedAt:     &timestamp.Timestamp{Seconds: 1653313018},
				},
			},
		},
		{
			name: "should respond segment status not found",
			args: args{
				ctx:            context.Background(),
				segmentIds:     []string{"segment-4"},
				givenTimestamp: 1653313020,
			},
			want: map[string]*segmentPb.SegmentInstance{
				"segment-4": {
					SegmentStatus: segmentPb.SegmentStatus_SEGMENT_NOT_FOUND,
					Id:            "",
					CreatedAt:     &timestamp.Timestamp{},
				},
			},
		},
		{
			name: "should respond segment status not found before timestamp",
			args: args{
				ctx:            context.Background(),
				segmentIds:     []string{"segment-3"},
				givenTimestamp: 1653313020,
			},
			want: map[string]*segmentPb.SegmentInstance{
				"segment-3": {
					SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_NOT_FOUND,
					Id:            "",
					CreatedAt:     &timestamp.Timestamp{},
				},
			},
		},
		{
			name: "should return empty map if empty list of segment ids is sent",
			args: args{
				ctx:            context.Background(),
				segmentIds:     []string{},
				givenTimestamp: 1653313020,
			},
			want: map[string]*segmentPb.SegmentInstance{},
		},
	}
	for _, tt := range tests {
		if err := prepareRDBForGetLatestSegmentInstances(sudts.rdb); err != nil {
			t.Errorf("error in preparing redis for tests")
			return
		}
		t.Run(tt.name, func(t *testing.T) {
			got, err := sudts.dao.GetLatestSegmentInstances(tt.args.ctx, tt.args.segmentIds, tt.args.givenTimestamp)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetLatestSegmentInstances() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr != nil {
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetLatestSegmentInstances() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedisSegmentUserDao_IsUserInSegments(t *testing.T) {
	type args struct {
		ctx                context.Context
		actorId            string
		segmentInstanceMap map[string]*segmentPb.SegmentInstance
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]*segmentPb.SegmentMembership
		wantErr error
	}{
		{
			name: "should check if user in segments",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				segmentInstanceMap: map[string]*segmentPb.SegmentInstance{
					"segment-1": {
						SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
						Id:            "segment-1-instance-1",
						CreatedAt:     &timestamp.Timestamp{Seconds: 1653313011},
					},
					"segment-2": {
						SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
						Id:            "segment-2-instance-1",
						CreatedAt:     &timestamp.Timestamp{Seconds: 1653313018},
					},
				},
			},
			want: map[string]*segmentPb.SegmentMembership{
				"segment-1": {
					SegmentStatus:    segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
					IsActorMember:    true,
					SegmentTimestamp: &timestamp.Timestamp{Seconds: 1653313011},
				},
				"segment-2": {
					SegmentStatus:    segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
					IsActorMember:    false,
					SegmentTimestamp: &timestamp.Timestamp{Seconds: 1653313018},
				},
			},
		},
		{
			name: "should respond internal error when segment instance is not present",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				segmentInstanceMap: map[string]*segmentPb.SegmentInstance{
					"segment-1": {
						SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
						Id:            "segment-1-instance-3",
						CreatedAt:     &timestamp.Timestamp{Seconds: 1653313011},
					},
				},
			},
			want: map[string]*segmentPb.SegmentMembership{
				"segment-1": {
					SegmentStatus: segmentPb.SegmentStatus_SEGMENT_INSTANCE_NOT_FOUND,
				},
			},
		},
		{
			name: "should return empty map if empty segment instance map is sent",
			args: args{
				ctx:                context.Background(),
				actorId:            "actor-1",
				segmentInstanceMap: map[string]*segmentPb.SegmentInstance{},
			},
			want: map[string]*segmentPb.SegmentMembership{},
		},
	}
	for _, tt := range tests {
		if err := prepareRDBForIsUserInSegments(sudts.rdb); err != nil {
			t.Errorf("error in preparing redis for tests")
			return
		}
		t.Run(tt.name, func(t *testing.T) {
			got, err := sudts.dao.IsUserInSegments(tt.args.ctx, tt.args.actorId, tt.args.segmentInstanceMap)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("IsUserInSegments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr != nil {
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("IsUserInSegments() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedisSegmentUserDao_GetUsersInSegmentInstance(t *testing.T) {
	type args struct {
		ctx               context.Context
		segmentId         string
		segmentInstanceId string
		cursor            uint64
		count             int64
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "should return a list of actor ids if segment instance exists",
			args: args{
				ctx:               context.Background(),
				segmentId:         "segment-1",
				segmentInstanceId: "segment-1-instance-1",
			},
			want:    []string{"actor-1"},
			wantErr: false,
		},
		{
			name: "should throw error if segment instance does not exists",
			args: args{
				ctx:               context.Background(),
				segmentId:         "segment-3",
				segmentInstanceId: "segment-3-instance-1",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if err := prepareRDBForIsUserInSegments(sudts.rdb); err != nil {
			t.Errorf("error in preparing redis for tests")
			return
		}
		t.Run(tt.name, func(t *testing.T) {
			got, _, err := sudts.dao.GetUsersInSegmentInstance(tt.args.ctx, tt.args.segmentId, tt.args.segmentInstanceId, tt.args.cursor, tt.args.count)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUsersInSegmentInstance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetUsersInSegmentInstance() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedisSegmentUserDao_DeleteSegmentInstances(t *testing.T) {
	type args struct {
		ctx                context.Context
		segmentId          string
		segmentInstanceIds []string
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "should throw segment not found error",
			args: args{
				ctx:       context.Background(),
				segmentId: "segment-2",
			},
			wantErr: segmentError.ErrSegmentNotFound,
		},
		{
			name: "should delete all segment instances successfully",
			args: args{
				ctx:       context.Background(),
				segmentId: "segment-1",
			},
		},
		{
			name: "should delete given segment instances successfully",
			args: args{
				ctx:                context.Background(),
				segmentId:          "segment-1",
				segmentInstanceIds: []string{"segment-1-instance-2"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := prepareRDBForDeleteSegmentInstances(sudts.rdb); err != nil {
				t.Errorf("error in preparing redis for tests")
				return
			}

			err := sudts.dao.DeleteSegmentInstances(tt.args.ctx, tt.args.segmentId, tt.args.segmentInstanceIds)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("IsUserInSegments() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func prepareRDBForGetLatestSegmentInstances(rdb *redisV9.Client) error {
	ctx := context.Background()

	// flush redis
	rdb.FlushDB(ctx)

	// insert test data
	testData := []*struct {
		SegmentId                string
		SegmentInstanceTimestamp float64
		SegmentInstanceId        string
	}{
		{
			SegmentId:                "segment-1",
			SegmentInstanceTimestamp: 1653313011,
			SegmentInstanceId:        "segment-1-instance-1",
		},
		{
			SegmentId:                "segment-1",
			SegmentInstanceTimestamp: 1653313020,
			SegmentInstanceId:        "segment-1-instance-2",
		},
		{
			SegmentId:                "segment-2",
			SegmentInstanceTimestamp: 1653313018,
			SegmentInstanceId:        "segment-2-instance-1",
		},
		{
			SegmentId:                "segment-3",
			SegmentInstanceTimestamp: 1653313022,
			SegmentInstanceId:        "segment-3-instance-1",
		},
	}
	for _, td := range testData {
		_, err := rdb.ZAdd(ctx, buildKeyForSegment(td.SegmentId), redisV9.Z{Score: td.SegmentInstanceTimestamp, Member: td.SegmentInstanceId}).Result()
		if err != nil {
			return fmt.Errorf("failed to insert test data in redis: %w", err)
		}
	}
	return nil
}

func prepareRDBForIsUserInSegments(rdb *redisV9.Client) error {
	ctx := context.Background()

	// flush redis
	rdb.FlushDB(ctx)

	// insert test data
	testData := []*struct {
		SegmentId         string
		SegmentInstanceId string
		ActorId           string
	}{
		{
			SegmentId:         "segment-1",
			SegmentInstanceId: "segment-1-instance-1",
			ActorId:           "actor-1",
		},
		{
			SegmentId:         "segment-2",
			SegmentInstanceId: "segment-2-instance-1",
			ActorId:           "actor-2",
		},
	}
	for _, td := range testData {
		_, err := rdb.SAdd(ctx, buildKeyForSegmentInstance(td.SegmentId, td.SegmentInstanceId), td.ActorId).Result()
		if err != nil {
			return fmt.Errorf("failed to insert test data in redis: %w", err)
		}
	}
	return nil

}

func prepareRDBForDeleteSegmentInstances(rdb *redisV9.Client) error {
	ctx := context.Background()

	// flush redis
	rdb.FlushDB(ctx)

	// insert test data
	testData1 := []*struct {
		SegmentId                string
		SegmentInstanceTimestamp float64
		SegmentInstanceId        string
	}{
		{
			SegmentId:                "segment-1",
			SegmentInstanceTimestamp: 1653313011,
			SegmentInstanceId:        "segment-1-instance-1",
		},
		{
			SegmentId:                "segment-1",
			SegmentInstanceTimestamp: 1653313020,
			SegmentInstanceId:        "segment-1-instance-2",
		},
	}
	for _, td := range testData1 {
		_, err := rdb.ZAdd(ctx, buildKeyForSegment(td.SegmentId), redisV9.Z{Score: td.SegmentInstanceTimestamp, Member: td.SegmentInstanceId}).Result()
		if err != nil {
			return fmt.Errorf("failed to insert test data in redis: %w", err)
		}
	}

	// insert test data
	testData2 := []*struct {
		SegmentId         string
		SegmentInstanceId string
		ActorIds          []string
	}{
		{
			SegmentId:         "segment-1",
			SegmentInstanceId: "segment-1-instance-1",
			ActorIds:          []string{"actor-1", "actor-2", "actor-3"},
		},
		{
			SegmentId:         "segment-1",
			SegmentInstanceId: "segment-1-instance-2",
			ActorIds:          []string{"actor-4"},
		},
	}
	for _, td := range testData2 {
		_, err := rdb.SAdd(ctx, buildKeyForSegmentInstance(td.SegmentId, td.SegmentInstanceId), td.ActorIds).Result()
		if err != nil {
			return fmt.Errorf("failed to insert test data in redis: %w", err)
		}
	}
	return nil
}

func TestRedisSegmentUserDao_AddUsersInSegmentInstance(t *testing.T) {
	type args struct {
		ctx                     context.Context
		segmentId               string
		segmentInstanceId       string
		actorIds                []string
		segmentInstanceAddOnTTL time.Duration
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "Should successfully add users in a segment instance",
			args: args{
				ctx:               context.Background(),
				segmentId:         "segment-3",
				segmentInstanceId: "segment-3-instance-1",
				actorIds:          []string{"actor-1", "actor-2"},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := sudts.dao.AddUsersInSegmentInstance(tt.args.ctx, tt.args.segmentId, tt.args.segmentInstanceId, tt.args.actorIds, tt.args.segmentInstanceAddOnTTL)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddUsersInSegmentInstance() error = %v, wantErr %v", err, tt.wantErr)
			}

			if int(got) != len(tt.args.actorIds) {
				t.Errorf("Members added: got %d, want %d", got, len(tt.args.actorIds))
			}
		})
	}
}

func TestRedisSegmentUserDao_AddSegmentInstance(t *testing.T) {
	type args struct {
		ctx               context.Context
		segmentId         string
		segmentInstanceId string
		recordEventTime   int64
	}

	tests := []struct {
		name    string
		args    args
		want    int64
		wantErr bool
	}{
		{
			name: "Should successfully add new segmentInstance to sorted set",
			args: args{
				ctx:               context.Background(),
				segmentId:         "segment-3",
				segmentInstanceId: "segment-3-instance-1",
				recordEventTime:   time.Now().Unix(),
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := sudts.dao.AddSegmentInstance(tt.args.ctx, tt.args.segmentId, tt.args.segmentInstanceId, tt.args.recordEventTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddSegmentInstance() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AddSegmentInstance() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRedisSegmentUserDao_AreSegmentsAvailable(t *testing.T) {
	type args struct {
		ctx        context.Context
		segmentIds []string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]error
		wantErr bool
	}{
		{
			name: "Should return nil error for segment present in redis or return SegmentNotFound error if segment is not present",
			args: args{
				ctx:        context.Background(),
				segmentIds: []string{"segment-1", "segment-2"},
			},
			want: map[string]error{
				"segment-1": nil,
				"segment-2": segmentError.ErrSegmentNotFound,
			},
			wantErr: false,
		},
	}

	if err := prepareRDBForAreSegmentsAvailable(sudts.rdb); err != nil {
		t.Errorf("error in preparing redis for tests")
		return
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := sudts.dao.AreSegmentsAvailable(tt.args.ctx, tt.args.segmentIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("AreSegmentsAvailable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AreSegmentsAvailable() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func prepareRDBForAreSegmentsAvailable(rdb *redisV9.Client) error {
	ctx := context.Background()

	// flush redis
	rdb.FlushDB(ctx)

	// insert test data
	_, err := rdb.ZAdd(ctx, buildKeyForSegment("segment-1"), redisV9.Z{Score: 1, Member: nil}).Result()
	if err != nil {
		return fmt.Errorf("failed to insert test data in redis: %w", err)
	}
	return nil
}

func prepareRDBForGetDiffBetweenSegmentInstances(rdb *redisV9.Client) error {
	ctx := context.Background()

	// flush redis
	rdb.FlushDB(ctx)

	// insert test data
	testData := []*struct {
		SegmentId         string
		SegmentInstanceId string
		ActorId           string
	}{
		{
			SegmentId:         "segment-1",
			SegmentInstanceId: "segment-1-instance-1",
			ActorId:           "actor-1",
		},
		{
			SegmentId:         "segment-1",
			SegmentInstanceId: "segment-1-instance-2",
			ActorId:           "actor-1",
		},
		{
			SegmentId:         "segment-1",
			SegmentInstanceId: "segment-1-instance-1",
			ActorId:           "actor-2",
		},
		{
			SegmentId:         "segment-1",
			SegmentInstanceId: "segment-1-instance-2",
			ActorId:           "actor-3",
		},
	}
	for _, td := range testData {
		_, err := rdb.SAdd(ctx, buildKeyForSegmentInstance(td.SegmentId, td.SegmentInstanceId), td.ActorId).Result()
		if err != nil {
			return fmt.Errorf("failed to insert test data in redis: %w", err)
		}
	}
	return nil
}

func TestRedisSegmentUserDao_GetDiffBetweenSegmentInstances(t *testing.T) {
	type fields struct {
		rdb *redisV9.Client
	}
	type args struct {
		ctx                context.Context
		segmentId          string
		segmentInstanceId1 string
		segmentInstanceId2 string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "success, should return diff for segment1 - segment2",
			fields: fields{
				rdb: sudts.rdb,
			},
			args: args{
				ctx:                context.Background(),
				segmentId:          "segment-1",
				segmentInstanceId1: "segment-1-instance-1",
				segmentInstanceId2: "segment-1-instance-2",
			},
			want:    []string{"actor-2"},
			wantErr: false,
		},
		{
			name: "success, should return diff for segment2 - segment1",
			fields: fields{
				rdb: sudts.rdb,
			},
			args: args{
				ctx:                context.Background(),
				segmentId:          "segment-1",
				segmentInstanceId1: "segment-1-instance-2",
				segmentInstanceId2: "segment-1-instance-1",
			},
			want:    []string{"actor-3"},
			wantErr: false,
		},
	}

	if err := prepareRDBForGetDiffBetweenSegmentInstances(sudts.rdb); err != nil {
		t.Errorf("error in preparing redis for tests")
		return
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &RedisSegmentUserDao{
				rdb: tt.fields.rdb,
			}
			got, err := r.GetDiffBetweenSegmentInstances(tt.args.ctx, tt.args.segmentId, tt.args.segmentInstanceId1, tt.args.segmentInstanceId2)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDiffBetweenSegmentInstances() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetDiffBetweenSegmentInstances() got = %v, want %v", got, tt.want)
			}
		})
	}
}
