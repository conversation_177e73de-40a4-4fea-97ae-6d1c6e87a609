// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/user/credit_report/notification
package notification

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &CreditReportDownloadEvent{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *CreditReportDownloadEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}
