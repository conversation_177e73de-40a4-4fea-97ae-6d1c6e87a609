// Code generated by MockGen. DO NOT EDIT.
// Source: api/actor/consumer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/actor/consumer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsumerClient is a mock of ConsumerClient interface.
type MockConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerClientMockRecorder
}

// MockConsumerClientMockRecorder is the mock recorder for MockConsumerClient.
type MockConsumerClientMockRecorder struct {
	mock *MockConsumerClient
}

// NewMockConsumerClient creates a new mock instance.
func NewMockConsumerClient(ctrl *gomock.Controller) *MockConsumerClient {
	mock := &MockConsumerClient{ctrl: ctrl}
	mock.recorder = &MockConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerClient) EXPECT() *MockConsumerClientMockRecorder {
	return m.recorder
}

// PurgeActorPiResolution mocks base method.
func (m *MockConsumerClient) PurgeActorPiResolution(ctx context.Context, in *consumer.PurgeActorPiResolutionRequest, opts ...grpc.CallOption) (*consumer.PurgeActorPiResolutionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PurgeActorPiResolution", varargs...)
	ret0, _ := ret[0].(*consumer.PurgeActorPiResolutionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PurgeActorPiResolution indicates an expected call of PurgeActorPiResolution.
func (mr *MockConsumerClientMockRecorder) PurgeActorPiResolution(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeActorPiResolution", reflect.TypeOf((*MockConsumerClient)(nil).PurgeActorPiResolution), varargs...)
}

// PurgeWealthActors mocks base method.
func (m *MockConsumerClient) PurgeWealthActors(ctx context.Context, in *consumer.PurgeWealthActorsRequest, opts ...grpc.CallOption) (*consumer.PurgeWealthActorsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PurgeWealthActors", varargs...)
	ret0, _ := ret[0].(*consumer.PurgeWealthActorsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PurgeWealthActors indicates an expected call of PurgeWealthActors.
func (mr *MockConsumerClientMockRecorder) PurgeWealthActors(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeWealthActors", reflect.TypeOf((*MockConsumerClient)(nil).PurgeWealthActors), varargs...)
}

// MockConsumerServer is a mock of ConsumerServer interface.
type MockConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerServerMockRecorder
}

// MockConsumerServerMockRecorder is the mock recorder for MockConsumerServer.
type MockConsumerServerMockRecorder struct {
	mock *MockConsumerServer
}

// NewMockConsumerServer creates a new mock instance.
func NewMockConsumerServer(ctrl *gomock.Controller) *MockConsumerServer {
	mock := &MockConsumerServer{ctrl: ctrl}
	mock.recorder = &MockConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerServer) EXPECT() *MockConsumerServerMockRecorder {
	return m.recorder
}

// PurgeActorPiResolution mocks base method.
func (m *MockConsumerServer) PurgeActorPiResolution(arg0 context.Context, arg1 *consumer.PurgeActorPiResolutionRequest) (*consumer.PurgeActorPiResolutionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PurgeActorPiResolution", arg0, arg1)
	ret0, _ := ret[0].(*consumer.PurgeActorPiResolutionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PurgeActorPiResolution indicates an expected call of PurgeActorPiResolution.
func (mr *MockConsumerServerMockRecorder) PurgeActorPiResolution(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeActorPiResolution", reflect.TypeOf((*MockConsumerServer)(nil).PurgeActorPiResolution), arg0, arg1)
}

// PurgeWealthActors mocks base method.
func (m *MockConsumerServer) PurgeWealthActors(arg0 context.Context, arg1 *consumer.PurgeWealthActorsRequest) (*consumer.PurgeWealthActorsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PurgeWealthActors", arg0, arg1)
	ret0, _ := ret[0].(*consumer.PurgeWealthActorsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PurgeWealthActors indicates an expected call of PurgeWealthActors.
func (mr *MockConsumerServerMockRecorder) PurgeWealthActors(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeWealthActors", reflect.TypeOf((*MockConsumerServer)(nil).PurgeWealthActors), arg0, arg1)
}

// MockUnsafeConsumerServer is a mock of UnsafeConsumerServer interface.
type MockUnsafeConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsumerServerMockRecorder
}

// MockUnsafeConsumerServerMockRecorder is the mock recorder for MockUnsafeConsumerServer.
type MockUnsafeConsumerServerMockRecorder struct {
	mock *MockUnsafeConsumerServer
}

// NewMockUnsafeConsumerServer creates a new mock instance.
func NewMockUnsafeConsumerServer(ctrl *gomock.Controller) *MockUnsafeConsumerServer {
	mock := &MockUnsafeConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsumerServer) EXPECT() *MockUnsafeConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsumerServer mocks base method.
func (m *MockUnsafeConsumerServer) mustEmbedUnimplementedConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsumerServer")
}

// mustEmbedUnimplementedConsumerServer indicates an expected call of mustEmbedUnimplementedConsumerServer.
func (mr *MockUnsafeConsumerServerMockRecorder) mustEmbedUnimplementedConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsumerServer", reflect.TypeOf((*MockUnsafeConsumerServer)(nil).mustEmbedUnimplementedConsumerServer))
}
