//go:generate gen_sql -types=IncomeDetails,EmploymentDetails,GuardianDetails,AddressDetails,AdditionalApplicantDetails

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/omegle/user_application.proto

package omegle

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	enums "github.com/epifi/gamma/api/omegle/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserApplication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Application ID is a unique identifier for each application. It is generated by omegle service, when we register the user
	// And it is sent as transaction id to federal, when we send the call details to federal.
	ApplicationId string                  `protobuf:"bytes,1,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Status        enums.ApplicationStatus `protobuf:"varint,2,opt,name=status,proto3,enum=omegle.enums.ApplicationStatus" json:"status,omitempty"`
	Client        vendorgateway.Vendor    `protobuf:"varint,3,opt,name=client,proto3,enum=vendorgateway.Vendor" json:"client,omitempty"`
	// Channel enums are provided to clients to specify the flow in which VKYC is being performed during registration.
	// By specifying the appropriate enum, clients help provide relevant information to agents and auditors during the verification of data.
	// This specification will also determine the data that needs to be collected and sent to VKYC by the client for the call.
	Channel                    enums.Channel               `protobuf:"varint,4,opt,name=channel,proto3,enum=omegle.enums.Channel" json:"channel,omitempty"`
	Name                       *common.Name                `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Dob                        *date.Date                  `protobuf:"bytes,6,opt,name=dob,proto3" json:"dob,omitempty"`
	Email                      string                      `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	Gender                     typesv2.Gender              `protobuf:"varint,8,opt,name=gender,proto3,enum=api.typesv2.Gender" json:"gender,omitempty"`
	IncomeDetails              *IncomeDetails              `protobuf:"bytes,9,opt,name=income_details,json=incomeDetails,proto3" json:"income_details,omitempty"`
	EmploymentDetails          *EmploymentDetails          `protobuf:"bytes,10,opt,name=employment_details,json=employmentDetails,proto3" json:"employment_details,omitempty"`
	GuardianDetails            *GuardianDetails            `protobuf:"bytes,11,opt,name=guardian_details,json=guardianDetails,proto3" json:"guardian_details,omitempty"`
	AddressDetails             *AddressDetails             `protobuf:"bytes,12,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	Image                      *common.Image               `protobuf:"bytes,13,opt,name=image,proto3" json:"image,omitempty"`
	MobileNumber               *common.PhoneNumber         `protobuf:"bytes,14,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	CreatedAt                  *timestamppb.Timestamp      `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt                  *timestamppb.Timestamp      `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt                  *timestamppb.Timestamp      `protobuf:"bytes,17,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	ApplicantType              enums.ApplicantType         `protobuf:"varint,18,opt,name=applicant_type,json=applicantType,proto3,enum=omegle.enums.ApplicantType" json:"applicant_type,omitempty"`
	ApplicationType            enums.ApplicationType       `protobuf:"varint,19,opt,name=application_type,json=applicationType,proto3,enum=omegle.enums.ApplicationType" json:"application_type,omitempty"`
	AdditionalApplicantDetails *AdditionalApplicantDetails `protobuf:"bytes,20,opt,name=additional_applicant_details,json=additionalApplicantDetails,proto3" json:"additional_applicant_details,omitempty"`
	// Ovd id is the passport number of the user
	// It is send as application id to federal, when we send the call details to federal.
	OvdId string `protobuf:"bytes,21,opt,name=ovd_id,json=ovdId,proto3" json:"ovd_id,omitempty"`
}

func (x *UserApplication) Reset() {
	*x = UserApplication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_user_application_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserApplication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserApplication) ProtoMessage() {}

func (x *UserApplication) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_user_application_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserApplication.ProtoReflect.Descriptor instead.
func (*UserApplication) Descriptor() ([]byte, []int) {
	return file_api_omegle_user_application_proto_rawDescGZIP(), []int{0}
}

func (x *UserApplication) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *UserApplication) GetStatus() enums.ApplicationStatus {
	if x != nil {
		return x.Status
	}
	return enums.ApplicationStatus(0)
}

func (x *UserApplication) GetClient() vendorgateway.Vendor {
	if x != nil {
		return x.Client
	}
	return vendorgateway.Vendor(0)
}

func (x *UserApplication) GetChannel() enums.Channel {
	if x != nil {
		return x.Channel
	}
	return enums.Channel(0)
}

func (x *UserApplication) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *UserApplication) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *UserApplication) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserApplication) GetGender() typesv2.Gender {
	if x != nil {
		return x.Gender
	}
	return typesv2.Gender(0)
}

func (x *UserApplication) GetIncomeDetails() *IncomeDetails {
	if x != nil {
		return x.IncomeDetails
	}
	return nil
}

func (x *UserApplication) GetEmploymentDetails() *EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *UserApplication) GetGuardianDetails() *GuardianDetails {
	if x != nil {
		return x.GuardianDetails
	}
	return nil
}

func (x *UserApplication) GetAddressDetails() *AddressDetails {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *UserApplication) GetImage() *common.Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *UserApplication) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *UserApplication) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UserApplication) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UserApplication) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *UserApplication) GetApplicantType() enums.ApplicantType {
	if x != nil {
		return x.ApplicantType
	}
	return enums.ApplicantType(0)
}

func (x *UserApplication) GetApplicationType() enums.ApplicationType {
	if x != nil {
		return x.ApplicationType
	}
	return enums.ApplicationType(0)
}

func (x *UserApplication) GetAdditionalApplicantDetails() *AdditionalApplicantDetails {
	if x != nil {
		return x.AdditionalApplicantDetails
	}
	return nil
}

func (x *UserApplication) GetOvdId() string {
	if x != nil {
		return x.OvdId
	}
	return ""
}

type IncomeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AbsoluteValue *typesv2.Money       `protobuf:"bytes,1,opt,name=absolute_value,json=absoluteValue,proto3" json:"absolute_value,omitempty"`
	SalaryRange   *typesv2.SalaryRange `protobuf:"bytes,2,opt,name=salary_range,json=salaryRange,proto3" json:"salary_range,omitempty"`
}

func (x *IncomeDetails) Reset() {
	*x = IncomeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_user_application_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncomeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncomeDetails) ProtoMessage() {}

func (x *IncomeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_user_application_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncomeDetails.ProtoReflect.Descriptor instead.
func (*IncomeDetails) Descriptor() ([]byte, []int) {
	return file_api_omegle_user_application_proto_rawDescGZIP(), []int{1}
}

func (x *IncomeDetails) GetAbsoluteValue() *typesv2.Money {
	if x != nil {
		return x.AbsoluteValue
	}
	return nil
}

func (x *IncomeDetails) GetSalaryRange() *typesv2.SalaryRange {
	if x != nil {
		return x.SalaryRange
	}
	return nil
}

type EmploymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmploymentTypeStr string `protobuf:"bytes,3,opt,name=employment_type_str,json=employmentTypeStr,proto3" json:"employment_type_str,omitempty"`
	OccupationTypeStr string `protobuf:"bytes,4,opt,name=occupation_type_str,json=occupationTypeStr,proto3" json:"occupation_type_str,omitempty"`
}

func (x *EmploymentDetails) Reset() {
	*x = EmploymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_user_application_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentDetails) ProtoMessage() {}

func (x *EmploymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_user_application_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentDetails.ProtoReflect.Descriptor instead.
func (*EmploymentDetails) Descriptor() ([]byte, []int) {
	return file_api_omegle_user_application_proto_rawDescGZIP(), []int{2}
}

func (x *EmploymentDetails) GetEmploymentTypeStr() string {
	if x != nil {
		return x.EmploymentTypeStr
	}
	return ""
}

func (x *EmploymentDetails) GetOccupationTypeStr() string {
	if x != nil {
		return x.OccupationTypeStr
	}
	return ""
}

type AddressDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommunicationAddress *postaladdress.PostalAddress `protobuf:"bytes,1,opt,name=communication_address,json=communicationAddress,proto3" json:"communication_address,omitempty"`
	PermanentAddress     *postaladdress.PostalAddress `protobuf:"bytes,2,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
}

func (x *AddressDetails) Reset() {
	*x = AddressDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_user_application_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressDetails) ProtoMessage() {}

func (x *AddressDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_user_application_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressDetails.ProtoReflect.Descriptor instead.
func (*AddressDetails) Descriptor() ([]byte, []int) {
	return file_api_omegle_user_application_proto_rawDescGZIP(), []int{3}
}

func (x *AddressDetails) GetCommunicationAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.CommunicationAddress
	}
	return nil
}

func (x *AddressDetails) GetPermanentAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

type GuardianDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FatherName *common.Name `protobuf:"bytes,1,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName *common.Name `protobuf:"bytes,2,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
}

func (x *GuardianDetails) Reset() {
	*x = GuardianDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_user_application_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GuardianDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuardianDetails) ProtoMessage() {}

func (x *GuardianDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_user_application_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuardianDetails.ProtoReflect.Descriptor instead.
func (*GuardianDetails) Descriptor() ([]byte, []int) {
	return file_api_omegle_user_application_proto_rawDescGZIP(), []int{4}
}

func (x *GuardianDetails) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *GuardianDetails) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

type AdditionalApplicantDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer_code_id is the dummy string value shared by the federal to identify the type of applicant
	CustomerCodeId string `protobuf:"bytes,1,opt,name=customer_code_id,json=customerCodeId,proto3" json:"customer_code_id,omitempty"`
}

func (x *AdditionalApplicantDetails) Reset() {
	*x = AdditionalApplicantDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_user_application_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdditionalApplicantDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdditionalApplicantDetails) ProtoMessage() {}

func (x *AdditionalApplicantDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_user_application_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdditionalApplicantDetails.ProtoReflect.Descriptor instead.
func (*AdditionalApplicantDetails) Descriptor() ([]byte, []int) {
	return file_api_omegle_user_application_proto_rawDescGZIP(), []int{5}
}

func (x *AdditionalApplicantDetails) GetCustomerCodeId() string {
	if x != nil {
		return x.CustomerCodeId
	}
	return ""
}

var File_api_omegle_user_application_proto protoreflect.FileDescriptor

var file_api_omegle_user_application_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x1a, 0x27, 0x61, 0x70, 0x69,
	0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x70, 0x6f,
	0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xa7, 0x09, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x37, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e,
	0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x07, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x2b, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0e,
	0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x69, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x12, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e,
	0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x10, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x47, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2f, 0x0a, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x42, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6f, 0x6d, 0x65, 0x67,
	0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x64, 0x0a, 0x1c, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1a, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x76, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x76, 0x64, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a,
	0x0d, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39,
	0x0a, 0x0e, 0x61, 0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x61, 0x62, 0x73, 0x6f,
	0x6c, 0x75, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x53, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x73, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x73, 0x0a, 0x11, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73,
	0x74, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x6f,
	0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73,
	0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x72, 0x22, 0xaa, 0x01, 0x0a, 0x0e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4f,
	0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x47, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x0f, 0x47, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0b,
	0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x46, 0x0a, 0x1a, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x28, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x42, 0x46, 0x0a, 0x21, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x5a,
	0x21, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67,
	0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_omegle_user_application_proto_rawDescOnce sync.Once
	file_api_omegle_user_application_proto_rawDescData = file_api_omegle_user_application_proto_rawDesc
)

func file_api_omegle_user_application_proto_rawDescGZIP() []byte {
	file_api_omegle_user_application_proto_rawDescOnce.Do(func() {
		file_api_omegle_user_application_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_omegle_user_application_proto_rawDescData)
	})
	return file_api_omegle_user_application_proto_rawDescData
}

var file_api_omegle_user_application_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_omegle_user_application_proto_goTypes = []interface{}{
	(*UserApplication)(nil),             // 0: omegle.UserApplication
	(*IncomeDetails)(nil),               // 1: omegle.IncomeDetails
	(*EmploymentDetails)(nil),           // 2: omegle.EmploymentDetails
	(*AddressDetails)(nil),              // 3: omegle.AddressDetails
	(*GuardianDetails)(nil),             // 4: omegle.GuardianDetails
	(*AdditionalApplicantDetails)(nil),  // 5: omegle.AdditionalApplicantDetails
	(enums.ApplicationStatus)(0),        // 6: omegle.enums.ApplicationStatus
	(vendorgateway.Vendor)(0),           // 7: vendorgateway.Vendor
	(enums.Channel)(0),                  // 8: omegle.enums.Channel
	(*common.Name)(nil),                 // 9: api.typesv2.common.Name
	(*date.Date)(nil),                   // 10: google.type.Date
	(typesv2.Gender)(0),                 // 11: api.typesv2.Gender
	(*common.Image)(nil),                // 12: api.typesv2.common.Image
	(*common.PhoneNumber)(nil),          // 13: api.typesv2.common.PhoneNumber
	(*timestamppb.Timestamp)(nil),       // 14: google.protobuf.Timestamp
	(enums.ApplicantType)(0),            // 15: omegle.enums.ApplicantType
	(enums.ApplicationType)(0),          // 16: omegle.enums.ApplicationType
	(*typesv2.Money)(nil),               // 17: api.typesv2.Money
	(*typesv2.SalaryRange)(nil),         // 18: api.typesv2.SalaryRange
	(*postaladdress.PostalAddress)(nil), // 19: google.type.PostalAddress
}
var file_api_omegle_user_application_proto_depIdxs = []int32{
	6,  // 0: omegle.UserApplication.status:type_name -> omegle.enums.ApplicationStatus
	7,  // 1: omegle.UserApplication.client:type_name -> vendorgateway.Vendor
	8,  // 2: omegle.UserApplication.channel:type_name -> omegle.enums.Channel
	9,  // 3: omegle.UserApplication.name:type_name -> api.typesv2.common.Name
	10, // 4: omegle.UserApplication.dob:type_name -> google.type.Date
	11, // 5: omegle.UserApplication.gender:type_name -> api.typesv2.Gender
	1,  // 6: omegle.UserApplication.income_details:type_name -> omegle.IncomeDetails
	2,  // 7: omegle.UserApplication.employment_details:type_name -> omegle.EmploymentDetails
	4,  // 8: omegle.UserApplication.guardian_details:type_name -> omegle.GuardianDetails
	3,  // 9: omegle.UserApplication.address_details:type_name -> omegle.AddressDetails
	12, // 10: omegle.UserApplication.image:type_name -> api.typesv2.common.Image
	13, // 11: omegle.UserApplication.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	14, // 12: omegle.UserApplication.created_at:type_name -> google.protobuf.Timestamp
	14, // 13: omegle.UserApplication.updated_at:type_name -> google.protobuf.Timestamp
	14, // 14: omegle.UserApplication.deleted_at:type_name -> google.protobuf.Timestamp
	15, // 15: omegle.UserApplication.applicant_type:type_name -> omegle.enums.ApplicantType
	16, // 16: omegle.UserApplication.application_type:type_name -> omegle.enums.ApplicationType
	5,  // 17: omegle.UserApplication.additional_applicant_details:type_name -> omegle.AdditionalApplicantDetails
	17, // 18: omegle.IncomeDetails.absolute_value:type_name -> api.typesv2.Money
	18, // 19: omegle.IncomeDetails.salary_range:type_name -> api.typesv2.SalaryRange
	19, // 20: omegle.AddressDetails.communication_address:type_name -> google.type.PostalAddress
	19, // 21: omegle.AddressDetails.permanent_address:type_name -> google.type.PostalAddress
	9,  // 22: omegle.GuardianDetails.father_name:type_name -> api.typesv2.common.Name
	9,  // 23: omegle.GuardianDetails.mother_name:type_name -> api.typesv2.common.Name
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_api_omegle_user_application_proto_init() }
func file_api_omegle_user_application_proto_init() {
	if File_api_omegle_user_application_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_omegle_user_application_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserApplication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_user_application_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncomeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_user_application_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_user_application_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_user_application_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GuardianDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_user_application_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdditionalApplicantDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_omegle_user_application_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_omegle_user_application_proto_goTypes,
		DependencyIndexes: file_api_omegle_user_application_proto_depIdxs,
		MessageInfos:      file_api_omegle_user_application_proto_msgTypes,
	}.Build()
	File_api_omegle_user_application_proto = out.File
	file_api_omegle_user_application_proto_rawDesc = nil
	file_api_omegle_user_application_proto_goTypes = nil
	file_api_omegle_user_application_proto_depIdxs = nil
}
