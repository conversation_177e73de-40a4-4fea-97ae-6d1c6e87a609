//go:generate gen_sql -types=MatchResult

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/omegle/matcher/matcher.proto

package matcher

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	enums "github.com/epifi/gamma/api/omegle/enums"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	date "google.golang.org/genproto/googleapis/type/date"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MatchResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Match score in %. The value is between 0-100.
	MatchScorePercent float32 `protobuf:"fixed32,1,opt,name=match_score_percent,json=matchScorePercent,proto3" json:"match_score_percent,omitempty"`
}

func (x *MatchResult) Reset() {
	*x = MatchResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_matcher_matcher_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MatchResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchResult) ProtoMessage() {}

func (x *MatchResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_matcher_matcher_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchResult.ProtoReflect.Descriptor instead.
func (*MatchResult) Descriptor() ([]byte, []int) {
	return file_api_omegle_matcher_matcher_proto_rawDescGZIP(), []int{0}
}

func (x *MatchResult) GetMatchScorePercent() float32 {
	if x != nil {
		return x.MatchScorePercent
	}
	return 0
}

type PANMatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PanNumber   string        `protobuf:"bytes,1,opt,name=pan_number,json=panNumber,proto3" json:"pan_number,omitempty"`
	Name        *common.Name  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DateOfBirth *date.Date    `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	Face        *common.Image `protobuf:"bytes,4,opt,name=face,proto3" json:"face,omitempty"`
	// use parent name if mother and father name is not available. eg In case of pan ocr we don't have mother and father name separately.
	ParentName *common.Name `protobuf:"bytes,5,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"`
	// use mother name if mother name is available. eg we collect mother name during onboarding.
	MotherName *common.Name `protobuf:"bytes,6,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	// use father name if father name is available. eg we collect father name during onboarding.
	FatherName *common.Name `protobuf:"bytes,7,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
}

func (x *PANMatchData) Reset() {
	*x = PANMatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_matcher_matcher_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PANMatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PANMatchData) ProtoMessage() {}

func (x *PANMatchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_matcher_matcher_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PANMatchData.ProtoReflect.Descriptor instead.
func (*PANMatchData) Descriptor() ([]byte, []int) {
	return file_api_omegle_matcher_matcher_proto_rawDescGZIP(), []int{1}
}

func (x *PANMatchData) GetPanNumber() string {
	if x != nil {
		return x.PanNumber
	}
	return ""
}

func (x *PANMatchData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *PANMatchData) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *PANMatchData) GetFace() *common.Image {
	if x != nil {
		return x.Face
	}
	return nil
}

func (x *PANMatchData) GetParentName() *common.Name {
	if x != nil {
		return x.ParentName
	}
	return nil
}

func (x *PANMatchData) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *PANMatchData) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

type PassportMatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PassportNumber string              `protobuf:"bytes,1,opt,name=passport_number,json=passportNumber,proto3" json:"passport_number,omitempty"`
	Name           *common.Name        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DateOfBirth    *date.Date          `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	Face           *common.Image       `protobuf:"bytes,4,opt,name=face,proto3" json:"face,omitempty"`
	Nationality    typesv2.Nationality `protobuf:"varint,5,opt,name=nationality,proto3,enum=api.typesv2.Nationality" json:"nationality,omitempty"`
	// Deprecated: Marked as deprecated in api/omegle/matcher/matcher.proto.
	IsIndianPassport bool                         `protobuf:"varint,7,opt,name=is_indian_passport,json=isIndianPassport,proto3" json:"is_indian_passport,omitempty"`
	PlaceOfBirth     string                       `protobuf:"bytes,8,opt,name=place_of_birth,json=placeOfBirth,proto3" json:"place_of_birth,omitempty"`
	PlaceOfIssue     string                       `protobuf:"bytes,9,opt,name=place_of_issue,json=placeOfIssue,proto3" json:"place_of_issue,omitempty"`
	DateOfIssue      *date.Date                   `protobuf:"bytes,10,opt,name=date_of_issue,json=dateOfIssue,proto3" json:"date_of_issue,omitempty"`
	DateOfExpiry     *date.Date                   `protobuf:"bytes,11,opt,name=date_of_expiry,json=dateOfExpiry,proto3" json:"date_of_expiry,omitempty"`
	FathersName      *common.Name                 `protobuf:"bytes,14,opt,name=fathers_name,json=fathersName,proto3" json:"fathers_name,omitempty"`
	MothersName      *common.Name                 `protobuf:"bytes,15,opt,name=mothers_name,json=mothersName,proto3" json:"mothers_name,omitempty"`
	SpousesName      *common.Name                 `protobuf:"bytes,16,opt,name=spouses_name,json=spousesName,proto3" json:"spouses_name,omitempty"`
	Address          *postaladdress.PostalAddress `protobuf:"bytes,17,opt,name=address,proto3" json:"address,omitempty"`
	FileNumber       string                       `protobuf:"bytes,21,opt,name=file_number,json=fileNumber,proto3" json:"file_number,omitempty"`
}

func (x *PassportMatchData) Reset() {
	*x = PassportMatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_matcher_matcher_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassportMatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassportMatchData) ProtoMessage() {}

func (x *PassportMatchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_matcher_matcher_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassportMatchData.ProtoReflect.Descriptor instead.
func (*PassportMatchData) Descriptor() ([]byte, []int) {
	return file_api_omegle_matcher_matcher_proto_rawDescGZIP(), []int{2}
}

func (x *PassportMatchData) GetPassportNumber() string {
	if x != nil {
		return x.PassportNumber
	}
	return ""
}

func (x *PassportMatchData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *PassportMatchData) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *PassportMatchData) GetFace() *common.Image {
	if x != nil {
		return x.Face
	}
	return nil
}

func (x *PassportMatchData) GetNationality() typesv2.Nationality {
	if x != nil {
		return x.Nationality
	}
	return typesv2.Nationality(0)
}

// Deprecated: Marked as deprecated in api/omegle/matcher/matcher.proto.
func (x *PassportMatchData) GetIsIndianPassport() bool {
	if x != nil {
		return x.IsIndianPassport
	}
	return false
}

func (x *PassportMatchData) GetPlaceOfBirth() string {
	if x != nil {
		return x.PlaceOfBirth
	}
	return ""
}

func (x *PassportMatchData) GetPlaceOfIssue() string {
	if x != nil {
		return x.PlaceOfIssue
	}
	return ""
}

func (x *PassportMatchData) GetDateOfIssue() *date.Date {
	if x != nil {
		return x.DateOfIssue
	}
	return nil
}

func (x *PassportMatchData) GetDateOfExpiry() *date.Date {
	if x != nil {
		return x.DateOfExpiry
	}
	return nil
}

func (x *PassportMatchData) GetFathersName() *common.Name {
	if x != nil {
		return x.FathersName
	}
	return nil
}

func (x *PassportMatchData) GetMothersName() *common.Name {
	if x != nil {
		return x.MothersName
	}
	return nil
}

func (x *PassportMatchData) GetSpousesName() *common.Name {
	if x != nil {
		return x.SpousesName
	}
	return nil
}

func (x *PassportMatchData) GetAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *PassportMatchData) GetFileNumber() string {
	if x != nil {
		return x.FileNumber
	}
	return ""
}

type EmiratesIdMatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   string                       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 *common.Name                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DateOfBirth          *date.Date                   `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	Face                 *common.Image                `protobuf:"bytes,4,opt,name=face,proto3" json:"face,omitempty"`
	FatherName           *common.Name                 `protobuf:"bytes,5,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName           *common.Name                 `protobuf:"bytes,6,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	Nationality          typesv2.Nationality          `protobuf:"varint,7,opt,name=nationality,proto3,enum=api.typesv2.Nationality" json:"nationality,omitempty"`
	CommunicationAddress *postaladdress.PostalAddress `protobuf:"bytes,10,opt,name=communication_address,json=communicationAddress,proto3" json:"communication_address,omitempty"`
	IssueDate            *date.Date                   `protobuf:"bytes,11,opt,name=issue_date,json=issueDate,proto3" json:"issue_date,omitempty"`
	DateOfExpiry         *date.Date                   `protobuf:"bytes,12,opt,name=date_of_expiry,json=dateOfExpiry,proto3" json:"date_of_expiry,omitempty"`
}

func (x *EmiratesIdMatchData) Reset() {
	*x = EmiratesIdMatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_matcher_matcher_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmiratesIdMatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmiratesIdMatchData) ProtoMessage() {}

func (x *EmiratesIdMatchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_matcher_matcher_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmiratesIdMatchData.ProtoReflect.Descriptor instead.
func (*EmiratesIdMatchData) Descriptor() ([]byte, []int) {
	return file_api_omegle_matcher_matcher_proto_rawDescGZIP(), []int{3}
}

func (x *EmiratesIdMatchData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EmiratesIdMatchData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *EmiratesIdMatchData) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *EmiratesIdMatchData) GetFace() *common.Image {
	if x != nil {
		return x.Face
	}
	return nil
}

func (x *EmiratesIdMatchData) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *EmiratesIdMatchData) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *EmiratesIdMatchData) GetNationality() typesv2.Nationality {
	if x != nil {
		return x.Nationality
	}
	return typesv2.Nationality(0)
}

func (x *EmiratesIdMatchData) GetCommunicationAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.CommunicationAddress
	}
	return nil
}

func (x *EmiratesIdMatchData) GetIssueDate() *date.Date {
	if x != nil {
		return x.IssueDate
	}
	return nil
}

func (x *EmiratesIdMatchData) GetDateOfExpiry() *date.Date {
	if x != nil {
		return x.DateOfExpiry
	}
	return nil
}

type QatarIdMatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string              `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name         *common.Name        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DateOfBirth  *date.Date          `protobuf:"bytes,3,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	Face         *common.Image       `protobuf:"bytes,4,opt,name=face,proto3" json:"face,omitempty"`
	Nationality  typesv2.Nationality `protobuf:"varint,5,opt,name=nationality,proto3,enum=api.typesv2.Nationality" json:"nationality,omitempty"`
	DateOfExpiry *date.Date          `protobuf:"bytes,6,opt,name=date_of_expiry,json=dateOfExpiry,proto3" json:"date_of_expiry,omitempty"`
}

func (x *QatarIdMatchData) Reset() {
	*x = QatarIdMatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_matcher_matcher_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QatarIdMatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QatarIdMatchData) ProtoMessage() {}

func (x *QatarIdMatchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_matcher_matcher_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QatarIdMatchData.ProtoReflect.Descriptor instead.
func (*QatarIdMatchData) Descriptor() ([]byte, []int) {
	return file_api_omegle_matcher_matcher_proto_rawDescGZIP(), []int{4}
}

func (x *QatarIdMatchData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *QatarIdMatchData) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *QatarIdMatchData) GetDateOfBirth() *date.Date {
	if x != nil {
		return x.DateOfBirth
	}
	return nil
}

func (x *QatarIdMatchData) GetFace() *common.Image {
	if x != nil {
		return x.Face
	}
	return nil
}

func (x *QatarIdMatchData) GetNationality() typesv2.Nationality {
	if x != nil {
		return x.Nationality
	}
	return typesv2.Nationality(0)
}

func (x *QatarIdMatchData) GetDateOfExpiry() *date.Date {
	if x != nil {
		return x.DateOfExpiry
	}
	return nil
}

type LocationMatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocationSourceType enums.LocationSourceType `protobuf:"varint,1,opt,name=location_source_type,json=locationSourceType,proto3,enum=omegle.enums.LocationSourceType" json:"location_source_type,omitempty"`
	// Types that are assignable to LocationValue:
	//
	//	*LocationMatchData_LocationToken
	//	*LocationMatchData_Address
	//	*LocationMatchData_IPToken
	LocationValue isLocationMatchData_LocationValue `protobuf_oneof:"LocationValue"`
}

func (x *LocationMatchData) Reset() {
	*x = LocationMatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_omegle_matcher_matcher_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationMatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationMatchData) ProtoMessage() {}

func (x *LocationMatchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_omegle_matcher_matcher_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationMatchData.ProtoReflect.Descriptor instead.
func (*LocationMatchData) Descriptor() ([]byte, []int) {
	return file_api_omegle_matcher_matcher_proto_rawDescGZIP(), []int{5}
}

func (x *LocationMatchData) GetLocationSourceType() enums.LocationSourceType {
	if x != nil {
		return x.LocationSourceType
	}
	return enums.LocationSourceType(0)
}

func (m *LocationMatchData) GetLocationValue() isLocationMatchData_LocationValue {
	if m != nil {
		return m.LocationValue
	}
	return nil
}

func (x *LocationMatchData) GetLocationToken() string {
	if x, ok := x.GetLocationValue().(*LocationMatchData_LocationToken); ok {
		return x.LocationToken
	}
	return ""
}

func (x *LocationMatchData) GetAddress() *postaladdress.PostalAddress {
	if x, ok := x.GetLocationValue().(*LocationMatchData_Address); ok {
		return x.Address
	}
	return nil
}

func (x *LocationMatchData) GetIPToken() string {
	if x, ok := x.GetLocationValue().(*LocationMatchData_IPToken); ok {
		return x.IPToken
	}
	return ""
}

type isLocationMatchData_LocationValue interface {
	isLocationMatchData_LocationValue()
}

type LocationMatchData_LocationToken struct {
	LocationToken string `protobuf:"bytes,2,opt,name=location_token,json=locationToken,proto3,oneof"`
}

type LocationMatchData_Address struct {
	Address *postaladdress.PostalAddress `protobuf:"bytes,3,opt,name=address,proto3,oneof"`
}

type LocationMatchData_IPToken struct {
	IPToken string `protobuf:"bytes,4,opt,name=i_p_token,json=iPToken,proto3,oneof"`
}

func (*LocationMatchData_LocationToken) isLocationMatchData_LocationValue() {}

func (*LocationMatchData_Address) isLocationMatchData_LocationValue() {}

func (*LocationMatchData_IPToken) isLocationMatchData_LocationValue() {}

var File_api_omegle_matcher_matcher_proto protoreflect.FileDescriptor

var file_api_omegle_matcher_matcher_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f,
	0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3d, 0x0a, 0x0b, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x11, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0xf2, 0x02, 0x0a, 0x0c, 0x50, 0x41,
	0x4e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x61, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x2d,
	0x0a, 0x04, 0x66, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x66, 0x61, 0x63, 0x65, 0x12, 0x39, 0x0a,
	0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x88,
	0x06, 0x0a, 0x11, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70,
	0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0d, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72,
	0x74, 0x68, 0x12, 0x2d, 0x0a, 0x04, 0x66, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x66, 0x61, 0x63,
	0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x30, 0x0a,
	0x12, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x69,
	0x73, 0x49, 0x6e, 0x64, 0x69, 0x61, 0x6e, 0x50, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x24, 0x0a, 0x0e, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x4f, 0x66,
	0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70,
	0x6c, 0x61, 0x63, 0x65, 0x4f, 0x66, 0x49, 0x73, 0x73, 0x75, 0x65, 0x12, 0x35, 0x0a, 0x0d, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x73, 0x73,
	0x75, 0x65, 0x12, 0x37, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x12, 0x3b, 0x0a, 0x0c, 0x66,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x66, 0x61, 0x74,
	0x68, 0x65, 0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x6d, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x73,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66,
	0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xa7, 0x04, 0x0a, 0x13, 0x45, 0x6d,
	0x69, 0x72, 0x61, 0x74, 0x65, 0x73, 0x49, 0x64, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f,
	0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x2d, 0x0a, 0x04, 0x66, 0x61, 0x63, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52,
	0x04, 0x66, 0x61, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x0a, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0b, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x4f, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0e, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x22, 0xab, 0x02, 0x0a, 0x10, 0x51, 0x61, 0x74, 0x61, 0x72, 0x49, 0x64, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x2d, 0x0a,
	0x04, 0x66, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x04, 0x66, 0x61, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x0b,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x4e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x0b, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x37, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6f, 0x66, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x22, 0xf7, 0x01, 0x0a, 0x11, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x52, 0x0a, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0e, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x36, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x48, 0x00, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x5f, 0x70, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x07, 0x69, 0x50, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x56, 0x0a, 0x29, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65,
	0x2e, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x6d, 0x65, 0x67, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_omegle_matcher_matcher_proto_rawDescOnce sync.Once
	file_api_omegle_matcher_matcher_proto_rawDescData = file_api_omegle_matcher_matcher_proto_rawDesc
)

func file_api_omegle_matcher_matcher_proto_rawDescGZIP() []byte {
	file_api_omegle_matcher_matcher_proto_rawDescOnce.Do(func() {
		file_api_omegle_matcher_matcher_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_omegle_matcher_matcher_proto_rawDescData)
	})
	return file_api_omegle_matcher_matcher_proto_rawDescData
}

var file_api_omegle_matcher_matcher_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_omegle_matcher_matcher_proto_goTypes = []interface{}{
	(*MatchResult)(nil),                 // 0: omegle.matcher.MatchResult
	(*PANMatchData)(nil),                // 1: omegle.matcher.PANMatchData
	(*PassportMatchData)(nil),           // 2: omegle.matcher.PassportMatchData
	(*EmiratesIdMatchData)(nil),         // 3: omegle.matcher.EmiratesIdMatchData
	(*QatarIdMatchData)(nil),            // 4: omegle.matcher.QatarIdMatchData
	(*LocationMatchData)(nil),           // 5: omegle.matcher.LocationMatchData
	(*common.Name)(nil),                 // 6: api.typesv2.common.Name
	(*date.Date)(nil),                   // 7: google.type.Date
	(*common.Image)(nil),                // 8: api.typesv2.common.Image
	(typesv2.Nationality)(0),            // 9: api.typesv2.Nationality
	(*postaladdress.PostalAddress)(nil), // 10: google.type.PostalAddress
	(enums.LocationSourceType)(0),       // 11: omegle.enums.LocationSourceType
}
var file_api_omegle_matcher_matcher_proto_depIdxs = []int32{
	6,  // 0: omegle.matcher.PANMatchData.name:type_name -> api.typesv2.common.Name
	7,  // 1: omegle.matcher.PANMatchData.date_of_birth:type_name -> google.type.Date
	8,  // 2: omegle.matcher.PANMatchData.face:type_name -> api.typesv2.common.Image
	6,  // 3: omegle.matcher.PANMatchData.parent_name:type_name -> api.typesv2.common.Name
	6,  // 4: omegle.matcher.PANMatchData.mother_name:type_name -> api.typesv2.common.Name
	6,  // 5: omegle.matcher.PANMatchData.father_name:type_name -> api.typesv2.common.Name
	6,  // 6: omegle.matcher.PassportMatchData.name:type_name -> api.typesv2.common.Name
	7,  // 7: omegle.matcher.PassportMatchData.date_of_birth:type_name -> google.type.Date
	8,  // 8: omegle.matcher.PassportMatchData.face:type_name -> api.typesv2.common.Image
	9,  // 9: omegle.matcher.PassportMatchData.nationality:type_name -> api.typesv2.Nationality
	7,  // 10: omegle.matcher.PassportMatchData.date_of_issue:type_name -> google.type.Date
	7,  // 11: omegle.matcher.PassportMatchData.date_of_expiry:type_name -> google.type.Date
	6,  // 12: omegle.matcher.PassportMatchData.fathers_name:type_name -> api.typesv2.common.Name
	6,  // 13: omegle.matcher.PassportMatchData.mothers_name:type_name -> api.typesv2.common.Name
	6,  // 14: omegle.matcher.PassportMatchData.spouses_name:type_name -> api.typesv2.common.Name
	10, // 15: omegle.matcher.PassportMatchData.address:type_name -> google.type.PostalAddress
	6,  // 16: omegle.matcher.EmiratesIdMatchData.name:type_name -> api.typesv2.common.Name
	7,  // 17: omegle.matcher.EmiratesIdMatchData.date_of_birth:type_name -> google.type.Date
	8,  // 18: omegle.matcher.EmiratesIdMatchData.face:type_name -> api.typesv2.common.Image
	6,  // 19: omegle.matcher.EmiratesIdMatchData.father_name:type_name -> api.typesv2.common.Name
	6,  // 20: omegle.matcher.EmiratesIdMatchData.mother_name:type_name -> api.typesv2.common.Name
	9,  // 21: omegle.matcher.EmiratesIdMatchData.nationality:type_name -> api.typesv2.Nationality
	10, // 22: omegle.matcher.EmiratesIdMatchData.communication_address:type_name -> google.type.PostalAddress
	7,  // 23: omegle.matcher.EmiratesIdMatchData.issue_date:type_name -> google.type.Date
	7,  // 24: omegle.matcher.EmiratesIdMatchData.date_of_expiry:type_name -> google.type.Date
	6,  // 25: omegle.matcher.QatarIdMatchData.name:type_name -> api.typesv2.common.Name
	7,  // 26: omegle.matcher.QatarIdMatchData.date_of_birth:type_name -> google.type.Date
	8,  // 27: omegle.matcher.QatarIdMatchData.face:type_name -> api.typesv2.common.Image
	9,  // 28: omegle.matcher.QatarIdMatchData.nationality:type_name -> api.typesv2.Nationality
	7,  // 29: omegle.matcher.QatarIdMatchData.date_of_expiry:type_name -> google.type.Date
	11, // 30: omegle.matcher.LocationMatchData.location_source_type:type_name -> omegle.enums.LocationSourceType
	10, // 31: omegle.matcher.LocationMatchData.address:type_name -> google.type.PostalAddress
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_api_omegle_matcher_matcher_proto_init() }
func file_api_omegle_matcher_matcher_proto_init() {
	if File_api_omegle_matcher_matcher_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_omegle_matcher_matcher_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MatchResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_matcher_matcher_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PANMatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_matcher_matcher_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PassportMatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_matcher_matcher_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmiratesIdMatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_matcher_matcher_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QatarIdMatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_omegle_matcher_matcher_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationMatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_omegle_matcher_matcher_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*LocationMatchData_LocationToken)(nil),
		(*LocationMatchData_Address)(nil),
		(*LocationMatchData_IPToken)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_omegle_matcher_matcher_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_omegle_matcher_matcher_proto_goTypes,
		DependencyIndexes: file_api_omegle_matcher_matcher_proto_depIdxs,
		MessageInfos:      file_api_omegle_matcher_matcher_proto_msgTypes,
	}.Build()
	File_api_omegle_matcher_matcher_proto = out.File
	file_api_omegle_matcher_matcher_proto_rawDesc = nil
	file_api_omegle_matcher_matcher_proto_goTypes = nil
	file_api_omegle_matcher_matcher_proto_depIdxs = nil
}
