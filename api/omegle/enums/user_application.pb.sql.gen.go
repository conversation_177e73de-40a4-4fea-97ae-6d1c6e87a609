// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/omegle/enums/user_application.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the ApplicationStatus in string format in DB
func (p ApplicationStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ApplicationStatus while reading from DB
func (p *ApplicationStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ApplicationStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ApplicationStatus value: %s", val)
	}
	*p = ApplicationStatus(valInt)
	return nil
}

// Marshaler interface implementation for ApplicationStatus
func (x ApplicationStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ApplicationStatus
func (x *ApplicationStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ApplicationStatus(ApplicationStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the Channel in string format in DB
func (p Channel) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Channel while reading from DB
func (p *Channel) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Channel_value[val]
	if !ok {
		return fmt.Errorf("unexpected Channel value: %s", val)
	}
	*p = Channel(valInt)
	return nil
}

// Marshaler interface implementation for Channel
func (x Channel) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Channel
func (x *Channel) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Channel(Channel_value[val])
	return nil
}

// Valuer interface implementation for storing the ApplicantType in string format in DB
func (p ApplicantType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ApplicantType while reading from DB
func (p *ApplicantType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ApplicantType_value[val]
	if !ok {
		return fmt.Errorf("unexpected ApplicantType value: %s", val)
	}
	*p = ApplicantType(valInt)
	return nil
}

// Marshaler interface implementation for ApplicantType
func (x ApplicantType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ApplicantType
func (x *ApplicantType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ApplicantType(ApplicantType_value[val])
	return nil
}

// Valuer interface implementation for storing the ApplicationType in string format in DB
func (p ApplicationType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ApplicationType while reading from DB
func (p *ApplicationType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ApplicationType_value[val]
	if !ok {
		return fmt.Errorf("unexpected ApplicationType value: %s", val)
	}
	*p = ApplicationType(valInt)
	return nil
}

// Marshaler interface implementation for ApplicationType
func (x ApplicationType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ApplicationType
func (x *ApplicationType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ApplicationType(ApplicationType_value[val])
	return nil
}
