// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/omegle/enums/call_info.pb.go

package enums

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the OverallStatus in string format in DB
func (p OverallStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing OverallStatus while reading from DB
func (p *OverallStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := OverallStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected OverallStatus value: %s", val)
	}
	*p = OverallStatus(valInt)
	return nil
}

// Marshaler interface implementation for OverallStatus
func (x OverallStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for OverallStatus
func (x *OverallStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = OverallStatus(OverallStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the CallSubStatus in string format in DB
func (p CallSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing CallSubStatus while reading from DB
func (p *CallSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CallSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected CallSubStatus value: %s", val)
	}
	*p = CallSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for CallSubStatus
func (x CallSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for CallSubStatus
func (x *CallSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = CallSubStatus(CallSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the FailureReason in string format in DB
func (p FailureReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing FailureReason while reading from DB
func (p *FailureReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := FailureReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected FailureReason value: %s", val)
	}
	*p = FailureReason(valInt)
	return nil
}

// Marshaler interface implementation for FailureReason
func (x FailureReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for FailureReason
func (x *FailureReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = FailureReason(FailureReason_value[val])
	return nil
}

// Valuer interface implementation for storing the CallPriority in string format in DB
func (p CallPriority) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing CallPriority while reading from DB
func (p *CallPriority) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := CallPriority_value[val]
	if !ok {
		return fmt.Errorf("unexpected CallPriority value: %s", val)
	}
	*p = CallPriority(valInt)
	return nil
}

// Marshaler interface implementation for CallPriority
func (x CallPriority) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for CallPriority
func (x *CallPriority) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = CallPriority(CallPriority_value[val])
	return nil
}

// Valuer interface implementation for storing the QuestionType in string format in DB
func (p QuestionType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing QuestionType while reading from DB
func (p *QuestionType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := QuestionType_value[val]
	if !ok {
		return fmt.Errorf("unexpected QuestionType value: %s", val)
	}
	*p = QuestionType(valInt)
	return nil
}

// Marshaler interface implementation for QuestionType
func (x QuestionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for QuestionType
func (x *QuestionType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = QuestionType(QuestionType_value[val])
	return nil
}

// Valuer interface implementation for storing the AuditorRejectionReason in string format in DB
func (p AuditorRejectionReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing AuditorRejectionReason while reading from DB
func (p *AuditorRejectionReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := AuditorRejectionReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected AuditorRejectionReason value: %s", val)
	}
	*p = AuditorRejectionReason(valInt)
	return nil
}

// Marshaler interface implementation for AuditorRejectionReason
func (x AuditorRejectionReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for AuditorRejectionReason
func (x *AuditorRejectionReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = AuditorRejectionReason(AuditorRejectionReason_value[val])
	return nil
}
