//go:generate gen_sql -types=SOFDocumentType
syntax = "proto3";

package api.pay.internationalfundtransfer;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer";
option java_package = "com.github.epifi.gamma.api.pay.internationalfundtransfer";

message InternationalFundTransferChecks {
  // actor id of the actor to whom these checks correspond to, these entries are created / updated
  // when the actor make an international fund transfer attempt
  string actor_id = 1;

  // LRS limit of the actor as specified by the RBI, used to fail fast if the user has already breached the limit
  google.type.Money lrs_limit_consumed = 2;

  // name of the active sof document which is can be used as SOF support document for a international fund transfer now
  // generally SOF document is valid for n months right now it is 2 months
  string sof_document_name = 3;

  // date when the sof document was created or updated, used as reference to check SOF document validity
  google.protobuf.Timestamp sof_document_updated_at = 4;

  google.protobuf.Timestamp created_at = 5;

  google.protobuf.Timestamp updated_at = 6;

  google.protobuf.Timestamp deleted_at = 7;

  // timestamp at which LRS limit was updated in the table
  google.protobuf.Timestamp lrs_limit_updated_at = 8;

  // pre-launch interested users are those users who have registered interest in the product before launch
  // applicable for any feature related to foreign remittance eg: US Stocks, P2P transfer etc
  bool is_pre_launch_interested_user = 9;

  // eg. Fi account statement, connected account statement etc.
  SOFDocumentType sof_doc_type = 10;
}

// SOFDocType represents the type of source of fund document,
enum SOFDocumentType {
  SOF_DOCUMENT_TYPE_UNSPECIFIED = 0;
  SOF_DOCUMENT_TYPE_FI_ACCOUNT_STATEMENT = 1;
  SOF_DOCUMENT_TYPE_CONNECTED_ACCOUNT_STATEMENT = 2;
}

enum InternationalFundTransferChecksFieldMask {
  INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_UNSPECIFIED = 0;
  INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_LRS_LIMIT_CONSUMED = 1;
  INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_SOF_DOCUMENT_NAME = 2;
  INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_SOF_DOCUMENT_UPDATED_AT = 3;
  INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_LRS_LIMIT_UPDATED_AT = 4;
  INTERNATIONAL_FUND_TRANSFER_CHECKS_FIELD_MASK_SOF_DOC_TYPE = 5;
}
