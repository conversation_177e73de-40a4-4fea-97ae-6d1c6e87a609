// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pay/workflow/filter/stage_filter.proto

package filter

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	order "github.com/epifi/gamma/api/order"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = order.OrderStage(0)
)

// Validate checks the field values on StageOption with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageOption with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageOptionMultiError, or
// nil if none found.
func (m *StageOption) ValidateAll() error {
	return m.validate(true)
}

func (m *StageOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatusFilterOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageOptionValidationError{
					field:  "StatusFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageOptionValidationError{
					field:  "StatusFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusFilterOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageOptionValidationError{
				field:  "StatusFilterOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Stage

	if len(errors) > 0 {
		return StageOptionMultiError(errors)
	}

	return nil
}

// StageOptionMultiError is an error wrapping multiple validation errors
// returned by StageOption.ValidateAll() if the designated constraints aren't met.
type StageOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageOptionMultiError) AllErrors() []error { return m }

// StageOptionValidationError is the validation error returned by
// StageOption.Validate if the designated constraints aren't met.
type StageOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageOptionValidationError) ErrorName() string { return "StageOptionValidationError" }

// Error satisfies the builtin error interface
func (e StageOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageOptionValidationError{}

// Validate checks the field values on StageWithSlaOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StageWithSlaOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageWithSlaOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StageWithSlaOptionMultiError, or nil if none found.
func (m *StageWithSlaOption) ValidateAll() error {
	return m.validate(true)
}

func (m *StageWithSlaOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStageFilterOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageWithSlaOptionValidationError{
					field:  "StageFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageWithSlaOptionValidationError{
					field:  "StageFilterOption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStageFilterOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageWithSlaOptionValidationError{
				field:  "StageFilterOption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSla()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageWithSlaOptionValidationError{
					field:  "Sla",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageWithSlaOptionValidationError{
					field:  "Sla",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSla()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageWithSlaOptionValidationError{
				field:  "Sla",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StageWithSlaOptionMultiError(errors)
	}

	return nil
}

// StageWithSlaOptionMultiError is an error wrapping multiple validation errors
// returned by StageWithSlaOption.ValidateAll() if the designated constraints
// aren't met.
type StageWithSlaOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageWithSlaOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageWithSlaOptionMultiError) AllErrors() []error { return m }

// StageWithSlaOptionValidationError is the validation error returned by
// StageWithSlaOption.Validate if the designated constraints aren't met.
type StageWithSlaOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageWithSlaOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageWithSlaOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageWithSlaOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageWithSlaOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageWithSlaOptionValidationError) ErrorName() string {
	return "StageWithSlaOptionValidationError"
}

// Error satisfies the builtin error interface
func (e StageWithSlaOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageWithSlaOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageWithSlaOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageWithSlaOptionValidationError{}
