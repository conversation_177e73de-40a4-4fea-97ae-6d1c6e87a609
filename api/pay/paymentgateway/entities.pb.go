//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/pay/paymentgateway/entities.proto

package paymentgateway

import (
	accounts "github.com/epifi/gamma/api/accounts"
	pg "github.com/epifi/gamma/api/vendorgateway/pg"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PgEntity int32

const (
	// Default unspecified value
	PgEntity_PG_ENTITY_UNSPECIFIED PgEntity = 0
	// Represents an order entity
	PgEntity_PG_ENTITY_ORDER PgEntity = 1
	// Represents a payment entity
	PgEntity_PG_ENTITY_PAYMENT PgEntity = 2
	// Represents a token entity
	PgEntity_PG_ENTITY_TOKEN PgEntity = 3
)

// Enum value maps for PgEntity.
var (
	PgEntity_name = map[int32]string{
		0: "PG_ENTITY_UNSPECIFIED",
		1: "PG_ENTITY_ORDER",
		2: "PG_ENTITY_PAYMENT",
		3: "PG_ENTITY_TOKEN",
	}
	PgEntity_value = map[string]int32{
		"PG_ENTITY_UNSPECIFIED": 0,
		"PG_ENTITY_ORDER":       1,
		"PG_ENTITY_PAYMENT":     2,
		"PG_ENTITY_TOKEN":       3,
	}
)

func (x PgEntity) Enum() *PgEntity {
	p := new(PgEntity)
	*p = x
	return p
}

func (x PgEntity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PgEntity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_pay_paymentgateway_entities_proto_enumTypes[0].Descriptor()
}

func (PgEntity) Type() protoreflect.EnumType {
	return &file_api_pay_paymentgateway_entities_proto_enumTypes[0]
}

func (x PgEntity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PgEntity.Descriptor instead.
func (PgEntity) EnumDescriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{0}
}

type PgOrderEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the order
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Type of entity (should be PG_ENTITY_ORDER)
	Entity PgEntity `protobuf:"varint,2,opt,name=entity,proto3,enum=api.pay.paymentgateway.PgEntity" json:"entity,omitempty"`
	// Amount of the order in smallest currency unit
	Amount int64 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// Amount paid for the order
	AmountPaid int64 `protobuf:"varint,4,opt,name=amount_paid,json=amountPaid,proto3" json:"amount_paid,omitempty"`
	// Amount due for the order
	AmountDue int64 `protobuf:"varint,5,opt,name=amount_due,json=amountDue,proto3" json:"amount_due,omitempty"`
	// Currency code of the order
	Currency string `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	// Receipt number of the order
	Receipt string `protobuf:"bytes,7,opt,name=receipt,proto3" json:"receipt,omitempty"`
	// Offer ID associated with the order
	OfferId string `protobuf:"bytes,8,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// Current status of the order
	Status pg.VendorOrderStatus `protobuf:"varint,9,opt,name=status,proto3,enum=vendorgateway.pg.VendorOrderStatus" json:"status,omitempty"`
	// Number of payment attempts made
	Attempts int32 `protobuf:"varint,10,opt,name=attempts,proto3" json:"attempts,omitempty"`
	// Timestamp of order creation
	CreatedAt int64 `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Token details for the order
	Token *Token `protobuf:"bytes,12,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *PgOrderEntity) Reset() {
	*x = PgOrderEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PgOrderEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PgOrderEntity) ProtoMessage() {}

func (x *PgOrderEntity) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PgOrderEntity.ProtoReflect.Descriptor instead.
func (*PgOrderEntity) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{0}
}

func (x *PgOrderEntity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PgOrderEntity) GetEntity() PgEntity {
	if x != nil {
		return x.Entity
	}
	return PgEntity_PG_ENTITY_UNSPECIFIED
}

func (x *PgOrderEntity) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PgOrderEntity) GetAmountPaid() int64 {
	if x != nil {
		return x.AmountPaid
	}
	return 0
}

func (x *PgOrderEntity) GetAmountDue() int64 {
	if x != nil {
		return x.AmountDue
	}
	return 0
}

func (x *PgOrderEntity) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *PgOrderEntity) GetReceipt() string {
	if x != nil {
		return x.Receipt
	}
	return ""
}

func (x *PgOrderEntity) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *PgOrderEntity) GetStatus() pg.VendorOrderStatus {
	if x != nil {
		return x.Status
	}
	return pg.VendorOrderStatus(0)
}

func (x *PgOrderEntity) GetAttempts() int32 {
	if x != nil {
		return x.Attempts
	}
	return 0
}

func (x *PgOrderEntity) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PgOrderEntity) GetToken() *Token {
	if x != nil {
		return x.Token
	}
	return nil
}

type Token struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Method of authorization
	Method pg.AuthorisationMethod `protobuf:"varint,1,opt,name=method,proto3,enum=vendorgateway.pg.AuthorisationMethod" json:"method,omitempty"`
	// Status of recurring payments
	RecurringStatus string `protobuf:"bytes,2,opt,name=recurring_status,json=recurringStatus,proto3" json:"recurring_status,omitempty"`
	// Reason for failure, if any
	FailureReason string `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
	// Currency code for the token
	Currency string `protobuf:"bytes,4,opt,name=currency,proto3" json:"currency,omitempty"`
	// Maximum amount allowed for the token
	MaxAmount int64 `protobuf:"varint,5,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	// Type of authentication
	// Deprecated in favour of auth_type_v2
	//
	// Deprecated: Marked as deprecated in api/pay/paymentgateway/entities.proto.
	AuthType string `protobuf:"bytes,6,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`
	// Expiration timestamp of the token
	ExpireAt int64 `protobuf:"varint,7,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	// Bank account details
	BankAccount *BankAccount `protobuf:"bytes,8,opt,name=bank_account,json=bankAccount,proto3" json:"bank_account,omitempty"`
	// Amount of the first payment
	FirstPaymentAmount int64 `protobuf:"varint,9,opt,name=first_payment_amount,json=firstPaymentAmount,proto3" json:"first_payment_amount,omitempty"`
	// The payment method used to authorise the mandate.
	AuthTypeV2 pg.AuthType `protobuf:"varint,10,opt,name=auth_type_v2,json=authTypeV2,proto3,enum=vendorgateway.pg.AuthType" json:"auth_type_v2,omitempty"`
}

func (x *Token) Reset() {
	*x = Token{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Token) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Token) ProtoMessage() {}

func (x *Token) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Token.ProtoReflect.Descriptor instead.
func (*Token) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{1}
}

func (x *Token) GetMethod() pg.AuthorisationMethod {
	if x != nil {
		return x.Method
	}
	return pg.AuthorisationMethod(0)
}

func (x *Token) GetRecurringStatus() string {
	if x != nil {
		return x.RecurringStatus
	}
	return ""
}

func (x *Token) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

func (x *Token) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Token) GetMaxAmount() int64 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

// Deprecated: Marked as deprecated in api/pay/paymentgateway/entities.proto.
func (x *Token) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *Token) GetExpireAt() int64 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *Token) GetBankAccount() *BankAccount {
	if x != nil {
		return x.BankAccount
	}
	return nil
}

func (x *Token) GetFirstPaymentAmount() int64 {
	if x != nil {
		return x.FirstPaymentAmount
	}
	return 0
}

func (x *Token) GetAuthTypeV2() pg.AuthType {
	if x != nil {
		return x.AuthTypeV2
	}
	return pg.AuthType(0)
}

type BankAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// IFSC code of the bank
	Ifsc string `protobuf:"bytes,1,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	// Name of the bank
	BankName string `protobuf:"bytes,2,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	// Name of the account holder
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Account number
	AccountNumber string `protobuf:"bytes,4,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Type of the account
	AccountType accounts.Type `protobuf:"varint,5,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// Email of the beneficiary
	BeneficiaryEmail string `protobuf:"bytes,6,opt,name=beneficiary_email,json=beneficiaryEmail,proto3" json:"beneficiary_email,omitempty"`
	// Mobile number of the beneficiary
	BeneficiaryMobile string `protobuf:"bytes,7,opt,name=beneficiary_mobile,json=beneficiaryMobile,proto3" json:"beneficiary_mobile,omitempty"`
}

func (x *BankAccount) Reset() {
	*x = BankAccount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccount) ProtoMessage() {}

func (x *BankAccount) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccount.ProtoReflect.Descriptor instead.
func (*BankAccount) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{2}
}

func (x *BankAccount) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *BankAccount) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *BankAccount) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BankAccount) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BankAccount) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *BankAccount) GetBeneficiaryEmail() string {
	if x != nil {
		return x.BeneficiaryEmail
	}
	return ""
}

func (x *BankAccount) GetBeneficiaryMobile() string {
	if x != nil {
		return x.BeneficiaryMobile
	}
	return ""
}

type PgPaymentEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the payment
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Type of entity (should be PG_ENTITY_PAYMENT)
	Entity PgEntity `protobuf:"varint,2,opt,name=entity,proto3,enum=api.pay.paymentgateway.PgEntity" json:"entity,omitempty"`
	// Amount of the payment in smallest currency unit
	Amount int64 `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// Currency code of the payment
	Currency string `protobuf:"bytes,4,opt,name=currency,proto3" json:"currency,omitempty"`
	// Current status of the payment
	Status pg.VendorPaymentStatus `protobuf:"varint,5,opt,name=status,proto3,enum=vendorgateway.pg.VendorPaymentStatus" json:"status,omitempty"`
	// Associated order ID
	OrderId string `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Associated invoice ID
	InvoiceId string `protobuf:"bytes,7,opt,name=invoice_id,json=invoiceId,proto3" json:"invoice_id,omitempty"`
	// Whether the payment is international
	International bool `protobuf:"varint,8,opt,name=international,proto3" json:"international,omitempty"`
	// Method of payment
	Method pg.PaymentMethod `protobuf:"varint,9,opt,name=method,proto3,enum=vendorgateway.pg.PaymentMethod" json:"method,omitempty"`
	// Amount refunded, if any
	AmountRefunded int64 `protobuf:"varint,10,opt,name=amount_refunded,json=amountRefunded,proto3" json:"amount_refunded,omitempty"`
	// Status of the refund
	RefundStatus pg.RefundStatus `protobuf:"varint,11,opt,name=refund_status,json=refundStatus,proto3,enum=vendorgateway.pg.RefundStatus" json:"refund_status,omitempty"`
	// Whether the payment has been captured
	Captured bool `protobuf:"varint,12,opt,name=captured,proto3" json:"captured,omitempty"`
	// Description of the payment
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// ID of the card used, if applicable
	CardId string `protobuf:"bytes,14,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// Bank used for the payment, if applicable
	Bank string `protobuf:"bytes,15,opt,name=bank,proto3" json:"bank,omitempty"`
	// Wallet used for the payment, if applicable
	Wallet string `protobuf:"bytes,16,opt,name=wallet,proto3" json:"wallet,omitempty"`
	// Virtual Payment Address, if applicable
	Vpa string `protobuf:"bytes,17,opt,name=vpa,proto3" json:"vpa,omitempty"`
	// Email of the payer
	Email string `protobuf:"bytes,18,opt,name=email,proto3" json:"email,omitempty"`
	// Contact number of the payer
	Contact string `protobuf:"bytes,19,opt,name=contact,proto3" json:"contact,omitempty"`
	// ID of the customer
	CustomerId string `protobuf:"bytes,20,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// ID of the token used, if applicable
	TokenId string `protobuf:"bytes,21,opt,name=token_id,json=tokenId,proto3" json:"token_id,omitempty"`
	// Fee charged for the payment
	Fee int64 `protobuf:"varint,22,opt,name=fee,proto3" json:"fee,omitempty"`
	// Tax applied to the payment
	Tax int64 `protobuf:"varint,23,opt,name=tax,proto3" json:"tax,omitempty"`
	// Error code, if any
	ErrorCode string `protobuf:"bytes,24,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// Description of the error
	ErrorDescription string `protobuf:"bytes,25,opt,name=error_description,json=errorDescription,proto3" json:"error_description,omitempty"`
	// Source of the error
	ErrorSource string `protobuf:"bytes,26,opt,name=error_source,json=errorSource,proto3" json:"error_source,omitempty"`
	// Step where the error occurred
	ErrorStep string `protobuf:"bytes,27,opt,name=error_step,json=errorStep,proto3" json:"error_step,omitempty"`
	// Reason for the error
	ErrorReason string `protobuf:"bytes,28,opt,name=error_reason,json=errorReason,proto3" json:"error_reason,omitempty"`
	// Data from the acquirer
	AcquirerData *AcquirerData `protobuf:"bytes,29,opt,name=acquirer_data,json=acquirerData,proto3" json:"acquirer_data,omitempty"`
	// Timestamp of payment creation
	CreatedAt int64 `protobuf:"varint,30,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *PgPaymentEntity) Reset() {
	*x = PgPaymentEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PgPaymentEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PgPaymentEntity) ProtoMessage() {}

func (x *PgPaymentEntity) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PgPaymentEntity.ProtoReflect.Descriptor instead.
func (*PgPaymentEntity) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{3}
}

func (x *PgPaymentEntity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PgPaymentEntity) GetEntity() PgEntity {
	if x != nil {
		return x.Entity
	}
	return PgEntity_PG_ENTITY_UNSPECIFIED
}

func (x *PgPaymentEntity) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PgPaymentEntity) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *PgPaymentEntity) GetStatus() pg.VendorPaymentStatus {
	if x != nil {
		return x.Status
	}
	return pg.VendorPaymentStatus(0)
}

func (x *PgPaymentEntity) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PgPaymentEntity) GetInvoiceId() string {
	if x != nil {
		return x.InvoiceId
	}
	return ""
}

func (x *PgPaymentEntity) GetInternational() bool {
	if x != nil {
		return x.International
	}
	return false
}

func (x *PgPaymentEntity) GetMethod() pg.PaymentMethod {
	if x != nil {
		return x.Method
	}
	return pg.PaymentMethod(0)
}

func (x *PgPaymentEntity) GetAmountRefunded() int64 {
	if x != nil {
		return x.AmountRefunded
	}
	return 0
}

func (x *PgPaymentEntity) GetRefundStatus() pg.RefundStatus {
	if x != nil {
		return x.RefundStatus
	}
	return pg.RefundStatus(0)
}

func (x *PgPaymentEntity) GetCaptured() bool {
	if x != nil {
		return x.Captured
	}
	return false
}

func (x *PgPaymentEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PgPaymentEntity) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *PgPaymentEntity) GetBank() string {
	if x != nil {
		return x.Bank
	}
	return ""
}

func (x *PgPaymentEntity) GetWallet() string {
	if x != nil {
		return x.Wallet
	}
	return ""
}

func (x *PgPaymentEntity) GetVpa() string {
	if x != nil {
		return x.Vpa
	}
	return ""
}

func (x *PgPaymentEntity) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PgPaymentEntity) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

func (x *PgPaymentEntity) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *PgPaymentEntity) GetTokenId() string {
	if x != nil {
		return x.TokenId
	}
	return ""
}

func (x *PgPaymentEntity) GetFee() int64 {
	if x != nil {
		return x.Fee
	}
	return 0
}

func (x *PgPaymentEntity) GetTax() int64 {
	if x != nil {
		return x.Tax
	}
	return 0
}

func (x *PgPaymentEntity) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *PgPaymentEntity) GetErrorDescription() string {
	if x != nil {
		return x.ErrorDescription
	}
	return ""
}

func (x *PgPaymentEntity) GetErrorSource() string {
	if x != nil {
		return x.ErrorSource
	}
	return ""
}

func (x *PgPaymentEntity) GetErrorStep() string {
	if x != nil {
		return x.ErrorStep
	}
	return ""
}

func (x *PgPaymentEntity) GetErrorReason() string {
	if x != nil {
		return x.ErrorReason
	}
	return ""
}

func (x *PgPaymentEntity) GetAcquirerData() *AcquirerData {
	if x != nil {
		return x.AcquirerData
	}
	return nil
}

func (x *PgPaymentEntity) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type AcquirerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Reference Retrieval Number
	Rrn string `protobuf:"bytes,1,opt,name=rrn,proto3" json:"rrn,omitempty"`
	// Authorization reference number
	AuthRefNumber string `protobuf:"bytes,2,opt,name=auth_ref_number,json=authRefNumber,proto3" json:"auth_ref_number,omitempty"`
	// Bank's transaction ID
	BankTransactionId string `protobuf:"bytes,3,opt,name=bank_transaction_id,json=bankTransactionId,proto3" json:"bank_transaction_id,omitempty"`
	// Authorization code
	AuthCode string `protobuf:"bytes,4,opt,name=auth_code,json=authCode,proto3" json:"auth_code,omitempty"`
}

func (x *AcquirerData) Reset() {
	*x = AcquirerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquirerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquirerData) ProtoMessage() {}

func (x *AcquirerData) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquirerData.ProtoReflect.Descriptor instead.
func (*AcquirerData) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{4}
}

func (x *AcquirerData) GetRrn() string {
	if x != nil {
		return x.Rrn
	}
	return ""
}

func (x *AcquirerData) GetAuthRefNumber() string {
	if x != nil {
		return x.AuthRefNumber
	}
	return ""
}

func (x *AcquirerData) GetBankTransactionId() string {
	if x != nil {
		return x.BankTransactionId
	}
	return ""
}

func (x *AcquirerData) GetAuthCode() string {
	if x != nil {
		return x.AuthCode
	}
	return ""
}

type PgTokenEntity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the token
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Type of entity (should be PG_ENTITY_TOKEN)
	Entity PgEntity `protobuf:"varint,2,opt,name=entity,proto3,enum=api.pay.paymentgateway.PgEntity" json:"entity,omitempty"`
	// The actual token value
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// Associated bank
	Bank string `protobuf:"bytes,4,opt,name=bank,proto3" json:"bank,omitempty"`
	// Associated wallet
	Wallet string `protobuf:"bytes,5,opt,name=wallet,proto3" json:"wallet,omitempty"`
	// Method of tokenization
	Method string `protobuf:"bytes,6,opt,name=method,proto3" json:"method,omitempty"`
	// Whether the token is for recurring payments
	Recurring bool `protobuf:"varint,7,opt,name=recurring,proto3" json:"recurring,omitempty"`
	// Details for recurring payments
	RecurringDetails *RecurringDetails `protobuf:"bytes,8,opt,name=recurring_details,json=recurringDetails,proto3" json:"recurring_details,omitempty"`
	// Type of authentication
	AuthType string `protobuf:"bytes,9,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`
	// Mandate Registration Number
	Mrn string `protobuf:"bytes,10,opt,name=mrn,proto3" json:"mrn,omitempty"`
	// Timestamp when the token was last used
	UsedAt int64 `protobuf:"varint,11,opt,name=used_at,json=usedAt,proto3" json:"used_at,omitempty"`
	// Timestamp of token creation
	CreatedAt int64 `protobuf:"varint,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Bank details associated with the token
	BankDetails *BankDetails `protobuf:"bytes,13,opt,name=bank_details,json=bankDetails,proto3" json:"bank_details,omitempty"`
	// Maximum amount allowed for the token
	MaxAmount int64 `protobuf:"varint,14,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	// Expiration timestamp of the token
	ExpiredAt int64 `protobuf:"varint,15,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	// Whether Dynamic Currency Conversion is enabled
	DccEnabled bool `protobuf:"varint,16,opt,name=dcc_enabled,json=dccEnabled,proto3" json:"dcc_enabled,omitempty"`
}

func (x *PgTokenEntity) Reset() {
	*x = PgTokenEntity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PgTokenEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PgTokenEntity) ProtoMessage() {}

func (x *PgTokenEntity) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PgTokenEntity.ProtoReflect.Descriptor instead.
func (*PgTokenEntity) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{5}
}

func (x *PgTokenEntity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PgTokenEntity) GetEntity() PgEntity {
	if x != nil {
		return x.Entity
	}
	return PgEntity_PG_ENTITY_UNSPECIFIED
}

func (x *PgTokenEntity) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *PgTokenEntity) GetBank() string {
	if x != nil {
		return x.Bank
	}
	return ""
}

func (x *PgTokenEntity) GetWallet() string {
	if x != nil {
		return x.Wallet
	}
	return ""
}

func (x *PgTokenEntity) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *PgTokenEntity) GetRecurring() bool {
	if x != nil {
		return x.Recurring
	}
	return false
}

func (x *PgTokenEntity) GetRecurringDetails() *RecurringDetails {
	if x != nil {
		return x.RecurringDetails
	}
	return nil
}

func (x *PgTokenEntity) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *PgTokenEntity) GetMrn() string {
	if x != nil {
		return x.Mrn
	}
	return ""
}

func (x *PgTokenEntity) GetUsedAt() int64 {
	if x != nil {
		return x.UsedAt
	}
	return 0
}

func (x *PgTokenEntity) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PgTokenEntity) GetBankDetails() *BankDetails {
	if x != nil {
		return x.BankDetails
	}
	return nil
}

func (x *PgTokenEntity) GetMaxAmount() int64 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

func (x *PgTokenEntity) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *PgTokenEntity) GetDccEnabled() bool {
	if x != nil {
		return x.DccEnabled
	}
	return false
}

type RecurringDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Status of the recurring mandate
	Status pg.MandateStatus `protobuf:"varint,1,opt,name=status,proto3,enum=vendorgateway.pg.MandateStatus" json:"status,omitempty"`
	// Reason for failure, if any
	FailureReason string `protobuf:"bytes,2,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *RecurringDetails) Reset() {
	*x = RecurringDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringDetails) ProtoMessage() {}

func (x *RecurringDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringDetails.ProtoReflect.Descriptor instead.
func (*RecurringDetails) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{6}
}

func (x *RecurringDetails) GetStatus() pg.MandateStatus {
	if x != nil {
		return x.Status
	}
	return pg.MandateStatus(0)
}

func (x *RecurringDetails) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type BankDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the beneficiary
	BeneficiaryName string `protobuf:"bytes,1,opt,name=beneficiary_name,json=beneficiaryName,proto3" json:"beneficiary_name,omitempty"`
	// Account number
	AccountNumber string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// IFSC code of the bank
	Ifsc string `protobuf:"bytes,3,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	// Type of the account
	AccountType accounts.Type `protobuf:"varint,4,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
}

func (x *BankDetails) Reset() {
	*x = BankDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankDetails) ProtoMessage() {}

func (x *BankDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_pay_paymentgateway_entities_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankDetails.ProtoReflect.Descriptor instead.
func (*BankDetails) Descriptor() ([]byte, []int) {
	return file_api_pay_paymentgateway_entities_proto_rawDescGZIP(), []int{7}
}

func (x *BankDetails) GetBeneficiaryName() string {
	if x != nil {
		return x.BeneficiaryName
	}
	return ""
}

func (x *BankDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BankDetails) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *BankDetails) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

var File_api_pay_paymentgateway_entities_proto protoreflect.FileDescriptor

var file_api_pay_paymentgateway_entities_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x1a,
	0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xaf, 0x03, 0x0a, 0x0d, 0x50, 0x67, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x38, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x50, 0x67, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64,
	0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x44, 0x75, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x67, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x33, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0xc9, 0x03, 0x0a, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3d, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x67, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08,
	0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a,
	0x14, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x3c, 0x0a, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x76, 0x32, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x67, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x56, 0x32, 0x22, 0x88, 0x02,
	0x0a, 0x0b, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x66, 0x73,
	0x63, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11,
	0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x72, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2d, 0x0a, 0x12, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61,
	0x72, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x22, 0x95, 0x08, 0x0a, 0x0f, 0x50, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x06,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x50, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x06,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x67, 0x2e, 0x56, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x12, 0x37, 0x0a, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x67, 0x2e, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66,
	0x75, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0d, 0x72,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x70, 0x67, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x63, 0x61, 0x70, 0x74, 0x75, 0x72, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x77,
	0x61, 0x6c, 0x6c, 0x65, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x61, 0x6c,
	0x6c, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x61, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x70, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49,
	0x64, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x66, 0x65, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x78, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x74, 0x61, 0x78, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x73, 0x74,
	0x65, 0x70, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x53,
	0x74, 0x65, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x49, 0x0a, 0x0d, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0x95, 0x01, 0x0a, 0x0c, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x72, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x72, 0x72, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x61, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xb6, 0x04, 0x0a, 0x0d, 0x50, 0x67, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x06, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x50, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x06, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x61,
	0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x61, 0x6e, 0x6b, 0x12, 0x16,
	0x0a, 0x06, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x55, 0x0a, 0x11,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61,
	0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x10, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x72, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x72, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x46, 0x0a, 0x0c, 0x62, 0x61,
	0x6e, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x63, 0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x63, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x22, 0x72, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x70, 0x67, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xa6, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63,
	0x69, 0x61, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x66, 0x73, 0x63, 0x12, 0x31, 0x0a, 0x0c, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x2a, 0x66,
	0x0a, 0x08, 0x50, 0x67, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x47,
	0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x49,
	0x54, 0x59, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x47,
	0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x02, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54,
	0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x03, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x61, 0x79, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_pay_paymentgateway_entities_proto_rawDescOnce sync.Once
	file_api_pay_paymentgateway_entities_proto_rawDescData = file_api_pay_paymentgateway_entities_proto_rawDesc
)

func file_api_pay_paymentgateway_entities_proto_rawDescGZIP() []byte {
	file_api_pay_paymentgateway_entities_proto_rawDescOnce.Do(func() {
		file_api_pay_paymentgateway_entities_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_pay_paymentgateway_entities_proto_rawDescData)
	})
	return file_api_pay_paymentgateway_entities_proto_rawDescData
}

var file_api_pay_paymentgateway_entities_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_pay_paymentgateway_entities_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_pay_paymentgateway_entities_proto_goTypes = []interface{}{
	(PgEntity)(0),               // 0: api.pay.paymentgateway.PgEntity
	(*PgOrderEntity)(nil),       // 1: api.pay.paymentgateway.PgOrderEntity
	(*Token)(nil),               // 2: api.pay.paymentgateway.Token
	(*BankAccount)(nil),         // 3: api.pay.paymentgateway.BankAccount
	(*PgPaymentEntity)(nil),     // 4: api.pay.paymentgateway.PgPaymentEntity
	(*AcquirerData)(nil),        // 5: api.pay.paymentgateway.AcquirerData
	(*PgTokenEntity)(nil),       // 6: api.pay.paymentgateway.PgTokenEntity
	(*RecurringDetails)(nil),    // 7: api.pay.paymentgateway.RecurringDetails
	(*BankDetails)(nil),         // 8: api.pay.paymentgateway.BankDetails
	(pg.VendorOrderStatus)(0),   // 9: vendorgateway.pg.VendorOrderStatus
	(pg.AuthorisationMethod)(0), // 10: vendorgateway.pg.AuthorisationMethod
	(pg.AuthType)(0),            // 11: vendorgateway.pg.AuthType
	(accounts.Type)(0),          // 12: accounts.Type
	(pg.VendorPaymentStatus)(0), // 13: vendorgateway.pg.VendorPaymentStatus
	(pg.PaymentMethod)(0),       // 14: vendorgateway.pg.PaymentMethod
	(pg.RefundStatus)(0),        // 15: vendorgateway.pg.RefundStatus
	(pg.MandateStatus)(0),       // 16: vendorgateway.pg.MandateStatus
}
var file_api_pay_paymentgateway_entities_proto_depIdxs = []int32{
	0,  // 0: api.pay.paymentgateway.PgOrderEntity.entity:type_name -> api.pay.paymentgateway.PgEntity
	9,  // 1: api.pay.paymentgateway.PgOrderEntity.status:type_name -> vendorgateway.pg.VendorOrderStatus
	2,  // 2: api.pay.paymentgateway.PgOrderEntity.token:type_name -> api.pay.paymentgateway.Token
	10, // 3: api.pay.paymentgateway.Token.method:type_name -> vendorgateway.pg.AuthorisationMethod
	3,  // 4: api.pay.paymentgateway.Token.bank_account:type_name -> api.pay.paymentgateway.BankAccount
	11, // 5: api.pay.paymentgateway.Token.auth_type_v2:type_name -> vendorgateway.pg.AuthType
	12, // 6: api.pay.paymentgateway.BankAccount.account_type:type_name -> accounts.Type
	0,  // 7: api.pay.paymentgateway.PgPaymentEntity.entity:type_name -> api.pay.paymentgateway.PgEntity
	13, // 8: api.pay.paymentgateway.PgPaymentEntity.status:type_name -> vendorgateway.pg.VendorPaymentStatus
	14, // 9: api.pay.paymentgateway.PgPaymentEntity.method:type_name -> vendorgateway.pg.PaymentMethod
	15, // 10: api.pay.paymentgateway.PgPaymentEntity.refund_status:type_name -> vendorgateway.pg.RefundStatus
	5,  // 11: api.pay.paymentgateway.PgPaymentEntity.acquirer_data:type_name -> api.pay.paymentgateway.AcquirerData
	0,  // 12: api.pay.paymentgateway.PgTokenEntity.entity:type_name -> api.pay.paymentgateway.PgEntity
	7,  // 13: api.pay.paymentgateway.PgTokenEntity.recurring_details:type_name -> api.pay.paymentgateway.RecurringDetails
	8,  // 14: api.pay.paymentgateway.PgTokenEntity.bank_details:type_name -> api.pay.paymentgateway.BankDetails
	16, // 15: api.pay.paymentgateway.RecurringDetails.status:type_name -> vendorgateway.pg.MandateStatus
	12, // 16: api.pay.paymentgateway.BankDetails.account_type:type_name -> accounts.Type
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_api_pay_paymentgateway_entities_proto_init() }
func file_api_pay_paymentgateway_entities_proto_init() {
	if File_api_pay_paymentgateway_entities_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_pay_paymentgateway_entities_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PgOrderEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_pay_paymentgateway_entities_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Token); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_pay_paymentgateway_entities_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_pay_paymentgateway_entities_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PgPaymentEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_pay_paymentgateway_entities_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquirerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_pay_paymentgateway_entities_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PgTokenEntity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_pay_paymentgateway_entities_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_pay_paymentgateway_entities_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_pay_paymentgateway_entities_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_pay_paymentgateway_entities_proto_goTypes,
		DependencyIndexes: file_api_pay_paymentgateway_entities_proto_depIdxs,
		EnumInfos:         file_api_pay_paymentgateway_entities_proto_enumTypes,
		MessageInfos:      file_api_pay_paymentgateway_entities_proto_msgTypes,
	}.Build()
	File_api_pay_paymentgateway_entities_proto = out.File
	file_api_pay_paymentgateway_entities_proto_rawDesc = nil
	file_api_pay_paymentgateway_entities_proto_goTypes = nil
	file_api_pay_paymentgateway_entities_proto_depIdxs = nil
}
