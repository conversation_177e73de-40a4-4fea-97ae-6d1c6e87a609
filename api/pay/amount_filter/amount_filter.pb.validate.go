// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/pay/amount_filter/amount_filter.proto

package amount_filter

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AmountFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AmountFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AmountFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AmountFilterMultiError, or
// nil if none found.
func (m *AmountFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *AmountFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AmountFilterType

	switch v := m.AmountFilter.(type) {
	case *AmountFilter_BinaryOperationFilter:
		if v == nil {
			err := AmountFilterValidationError{
				field:  "AmountFilter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBinaryOperationFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AmountFilterValidationError{
						field:  "BinaryOperationFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AmountFilterValidationError{
						field:  "BinaryOperationFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBinaryOperationFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AmountFilterValidationError{
					field:  "BinaryOperationFilter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AmountFilter_RangeOperationFilter:
		if v == nil {
			err := AmountFilterValidationError{
				field:  "AmountFilter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRangeOperationFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AmountFilterValidationError{
						field:  "RangeOperationFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AmountFilterValidationError{
						field:  "RangeOperationFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRangeOperationFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AmountFilterValidationError{
					field:  "RangeOperationFilter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AmountFilterMultiError(errors)
	}

	return nil
}

// AmountFilterMultiError is an error wrapping multiple validation errors
// returned by AmountFilter.ValidateAll() if the designated constraints aren't met.
type AmountFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AmountFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AmountFilterMultiError) AllErrors() []error { return m }

// AmountFilterValidationError is the validation error returned by
// AmountFilter.Validate if the designated constraints aren't met.
type AmountFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AmountFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AmountFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AmountFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AmountFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AmountFilterValidationError) ErrorName() string { return "AmountFilterValidationError" }

// Error satisfies the builtin error interface
func (e AmountFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAmountFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AmountFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AmountFilterValidationError{}

// Validate checks the field values on BinaryOperationFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BinaryOperationFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BinaryOperationFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BinaryOperationFilterMultiError, or nil if none found.
func (m *BinaryOperationFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *BinaryOperationFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Operation

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BinaryOperationFilterValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BinaryOperationFilterValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BinaryOperationFilterValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BinaryOperationFilterMultiError(errors)
	}

	return nil
}

// BinaryOperationFilterMultiError is an error wrapping multiple validation
// errors returned by BinaryOperationFilter.ValidateAll() if the designated
// constraints aren't met.
type BinaryOperationFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BinaryOperationFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BinaryOperationFilterMultiError) AllErrors() []error { return m }

// BinaryOperationFilterValidationError is the validation error returned by
// BinaryOperationFilter.Validate if the designated constraints aren't met.
type BinaryOperationFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BinaryOperationFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BinaryOperationFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BinaryOperationFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BinaryOperationFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BinaryOperationFilterValidationError) ErrorName() string {
	return "BinaryOperationFilterValidationError"
}

// Error satisfies the builtin error interface
func (e BinaryOperationFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBinaryOperationFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BinaryOperationFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BinaryOperationFilterValidationError{}

// Validate checks the field values on RangeOperationFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RangeOperationFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RangeOperationFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RangeOperationFilterMultiError, or nil if none found.
func (m *RangeOperationFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *RangeOperationFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFromAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RangeOperationFilterValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RangeOperationFilterValidationError{
					field:  "FromAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RangeOperationFilterValidationError{
				field:  "FromAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RangeOperationFilterValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RangeOperationFilterValidationError{
					field:  "ToAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RangeOperationFilterValidationError{
				field:  "ToAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RangeOperationFilterMultiError(errors)
	}

	return nil
}

// RangeOperationFilterMultiError is an error wrapping multiple validation
// errors returned by RangeOperationFilter.ValidateAll() if the designated
// constraints aren't met.
type RangeOperationFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RangeOperationFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RangeOperationFilterMultiError) AllErrors() []error { return m }

// RangeOperationFilterValidationError is the validation error returned by
// RangeOperationFilter.Validate if the designated constraints aren't met.
type RangeOperationFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RangeOperationFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RangeOperationFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RangeOperationFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RangeOperationFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RangeOperationFilterValidationError) ErrorName() string {
	return "RangeOperationFilterValidationError"
}

// Error satisfies the builtin error interface
func (e RangeOperationFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRangeOperationFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RangeOperationFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RangeOperationFilterValidationError{}
