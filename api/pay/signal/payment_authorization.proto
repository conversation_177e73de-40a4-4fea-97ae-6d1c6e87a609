// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package pay.signal;

option go_package = "github.com/epifi/gamma/api/pay/signal";
option java_package = "com.github.epifi.gamma.api.pay.signal";

// Signal payload to be used by payment workflows to notify about the authorization status
message PaymentAuthorizationSignal {
  // Authorization status is the status indicating a success or failure in fund transfer at vendor's end
  pay.signal.AuthorizationStatus status = 1;

  // Unique identifier associated with txn used internally.
  string txn_id = 2;

  // CBS Id is the UTR for INTRA bank which we will share with federal bank in swift transfer file in ops flow
  string cbs_id = 3;

  // External Reference id, used by federal in ops flows to identify order from externally
  string external_reference_id = 4;
}

// Authorization status is the status indicating a success or failure in fund transfer at vendor's end
enum AuthorizationStatus {
  AUTHORIZATION_STATUS_UNSPECIFIED = 0;

  AUTHORIZATION_STATUS_SUCCESS = 1;

  AUTHORIZATION_STATUS_FAILURE = 2;
}
