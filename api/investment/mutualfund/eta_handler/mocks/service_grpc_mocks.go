// Code generated by MockGen. DO NOT EDIT.
// Source: api/investment/mutualfund/eta_handler/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	eta_handler "github.com/epifi/gamma/api/investment/mutualfund/eta_handler"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockETAHandlerConsumerClient is a mock of ETAHandlerConsumerClient interface.
type MockETAHandlerConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockETAHandlerConsumerClientMockRecorder
}

// MockETAHandlerConsumerClientMockRecorder is the mock recorder for MockETAHandlerConsumerClient.
type MockETAHandlerConsumerClientMockRecorder struct {
	mock *MockETAHandlerConsumerClient
}

// NewMockETAHandlerConsumerClient creates a new mock instance.
func NewMockETAHandlerConsumerClient(ctrl *gomock.Controller) *MockETAHandlerConsumerClient {
	mock := &MockETAHandlerConsumerClient{ctrl: ctrl}
	mock.recorder = &MockETAHandlerConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockETAHandlerConsumerClient) EXPECT() *MockETAHandlerConsumerClientMockRecorder {
	return m.recorder
}

// ProcessOrderETA mocks base method.
func (m *MockETAHandlerConsumerClient) ProcessOrderETA(ctx context.Context, in *eta_handler.ProcessOrderETARequest, opts ...grpc.CallOption) (*eta_handler.ProcessOrderETAResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessOrderETA", varargs...)
	ret0, _ := ret[0].(*eta_handler.ProcessOrderETAResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderETA indicates an expected call of ProcessOrderETA.
func (mr *MockETAHandlerConsumerClientMockRecorder) ProcessOrderETA(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderETA", reflect.TypeOf((*MockETAHandlerConsumerClient)(nil).ProcessOrderETA), varargs...)
}

// MockETAHandlerConsumerServer is a mock of ETAHandlerConsumerServer interface.
type MockETAHandlerConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockETAHandlerConsumerServerMockRecorder
}

// MockETAHandlerConsumerServerMockRecorder is the mock recorder for MockETAHandlerConsumerServer.
type MockETAHandlerConsumerServerMockRecorder struct {
	mock *MockETAHandlerConsumerServer
}

// NewMockETAHandlerConsumerServer creates a new mock instance.
func NewMockETAHandlerConsumerServer(ctrl *gomock.Controller) *MockETAHandlerConsumerServer {
	mock := &MockETAHandlerConsumerServer{ctrl: ctrl}
	mock.recorder = &MockETAHandlerConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockETAHandlerConsumerServer) EXPECT() *MockETAHandlerConsumerServerMockRecorder {
	return m.recorder
}

// ProcessOrderETA mocks base method.
func (m *MockETAHandlerConsumerServer) ProcessOrderETA(arg0 context.Context, arg1 *eta_handler.ProcessOrderETARequest) (*eta_handler.ProcessOrderETAResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessOrderETA", arg0, arg1)
	ret0, _ := ret[0].(*eta_handler.ProcessOrderETAResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessOrderETA indicates an expected call of ProcessOrderETA.
func (mr *MockETAHandlerConsumerServerMockRecorder) ProcessOrderETA(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessOrderETA", reflect.TypeOf((*MockETAHandlerConsumerServer)(nil).ProcessOrderETA), arg0, arg1)
}

// MockUnsafeETAHandlerConsumerServer is a mock of UnsafeETAHandlerConsumerServer interface.
type MockUnsafeETAHandlerConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeETAHandlerConsumerServerMockRecorder
}

// MockUnsafeETAHandlerConsumerServerMockRecorder is the mock recorder for MockUnsafeETAHandlerConsumerServer.
type MockUnsafeETAHandlerConsumerServerMockRecorder struct {
	mock *MockUnsafeETAHandlerConsumerServer
}

// NewMockUnsafeETAHandlerConsumerServer creates a new mock instance.
func NewMockUnsafeETAHandlerConsumerServer(ctrl *gomock.Controller) *MockUnsafeETAHandlerConsumerServer {
	mock := &MockUnsafeETAHandlerConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeETAHandlerConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeETAHandlerConsumerServer) EXPECT() *MockUnsafeETAHandlerConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedETAHandlerConsumerServer mocks base method.
func (m *MockUnsafeETAHandlerConsumerServer) mustEmbedUnimplementedETAHandlerConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedETAHandlerConsumerServer")
}

// mustEmbedUnimplementedETAHandlerConsumerServer indicates an expected call of mustEmbedUnimplementedETAHandlerConsumerServer.
func (mr *MockUnsafeETAHandlerConsumerServerMockRecorder) mustEmbedUnimplementedETAHandlerConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedETAHandlerConsumerServer", reflect.TypeOf((*MockUnsafeETAHandlerConsumerServer)(nil).mustEmbedUnimplementedETAHandlerConsumerServer))
}
