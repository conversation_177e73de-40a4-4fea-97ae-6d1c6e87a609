// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/mutualfund/oms_payload.proto

package mutualfund

import (
	payment_handler "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Metadata stored in the order payload for Mutual funds Investment Workflows
type MutualFundsInvestmentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderId     string                      `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Amount      *money.Money                `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	PaymentMode payment_handler.PaymentMode `protobuf:"varint,3,opt,name=payment_mode,json=paymentMode,proto3,enum=api.investment.mutualfund.payment_handler.PaymentMode" json:"payment_mode,omitempty"`
	ForcedRetry bool                        `protobuf:"varint,4,opt,name=forced_retry,json=forcedRetry,proto3" json:"forced_retry,omitempty"`
}

func (x *MutualFundsInvestmentInfo) Reset() {
	*x = MutualFundsInvestmentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_oms_payload_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MutualFundsInvestmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MutualFundsInvestmentInfo) ProtoMessage() {}

func (x *MutualFundsInvestmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_oms_payload_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MutualFundsInvestmentInfo.ProtoReflect.Descriptor instead.
func (*MutualFundsInvestmentInfo) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_oms_payload_proto_rawDescGZIP(), []int{0}
}

func (x *MutualFundsInvestmentInfo) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *MutualFundsInvestmentInfo) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *MutualFundsInvestmentInfo) GetPaymentMode() payment_handler.PaymentMode {
	if x != nil {
		return x.PaymentMode
	}
	return payment_handler.PaymentMode(0)
}

func (x *MutualFundsInvestmentInfo) GetForcedRetry() bool {
	if x != nil {
		return x.ForcedRetry
	}
	return false
}

var File_api_investment_mutualfund_oms_payload_proto protoreflect.FileDescriptor

var file_api_investment_mutualfund_oms_payload_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x6f, 0x6d, 0x73, 0x5f,
	0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75,
	0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0, 0x01, 0x0a, 0x19, 0x4d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x59, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x72, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64, 0x52, 0x65, 0x74, 0x72, 0x79, 0x42, 0x64, 0x0a,
	0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e,
	0x64, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66,
	0x75, 0x6e, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_investment_mutualfund_oms_payload_proto_rawDescOnce sync.Once
	file_api_investment_mutualfund_oms_payload_proto_rawDescData = file_api_investment_mutualfund_oms_payload_proto_rawDesc
)

func file_api_investment_mutualfund_oms_payload_proto_rawDescGZIP() []byte {
	file_api_investment_mutualfund_oms_payload_proto_rawDescOnce.Do(func() {
		file_api_investment_mutualfund_oms_payload_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_mutualfund_oms_payload_proto_rawDescData)
	})
	return file_api_investment_mutualfund_oms_payload_proto_rawDescData
}

var file_api_investment_mutualfund_oms_payload_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_investment_mutualfund_oms_payload_proto_goTypes = []interface{}{
	(*MutualFundsInvestmentInfo)(nil), // 0: api.investment.mutualfund.MutualFundsInvestmentInfo
	(*money.Money)(nil),               // 1: google.type.Money
	(payment_handler.PaymentMode)(0),  // 2: api.investment.mutualfund.payment_handler.PaymentMode
}
var file_api_investment_mutualfund_oms_payload_proto_depIdxs = []int32{
	1, // 0: api.investment.mutualfund.MutualFundsInvestmentInfo.amount:type_name -> google.type.Money
	2, // 1: api.investment.mutualfund.MutualFundsInvestmentInfo.payment_mode:type_name -> api.investment.mutualfund.payment_handler.PaymentMode
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_investment_mutualfund_oms_payload_proto_init() }
func file_api_investment_mutualfund_oms_payload_proto_init() {
	if File_api_investment_mutualfund_oms_payload_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_investment_mutualfund_oms_payload_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MutualFundsInvestmentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_mutualfund_oms_payload_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_investment_mutualfund_oms_payload_proto_goTypes,
		DependencyIndexes: file_api_investment_mutualfund_oms_payload_proto_depIdxs,
		MessageInfos:      file_api_investment_mutualfund_oms_payload_proto_msgTypes,
	}.Build()
	File_api_investment_mutualfund_oms_payload_proto = out.File
	file_api_investment_mutualfund_oms_payload_proto_rawDesc = nil
	file_api_investment_mutualfund_oms_payload_proto_goTypes = nil
	file_api_investment_mutualfund_oms_payload_proto_depIdxs = nil
}
