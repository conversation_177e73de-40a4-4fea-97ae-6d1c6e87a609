// Code generated by MockGen. DO NOT EDIT.
// Source: api/investment/mutualfund/order/prerequisite_handler/consumer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	consumer "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler/consumer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPrerequisiteConsumerClient is a mock of PrerequisiteConsumerClient interface.
type MockPrerequisiteConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockPrerequisiteConsumerClientMockRecorder
}

// MockPrerequisiteConsumerClientMockRecorder is the mock recorder for MockPrerequisiteConsumerClient.
type MockPrerequisiteConsumerClientMockRecorder struct {
	mock *MockPrerequisiteConsumerClient
}

// NewMockPrerequisiteConsumerClient creates a new mock instance.
func NewMockPrerequisiteConsumerClient(ctrl *gomock.Controller) *MockPrerequisiteConsumerClient {
	mock := &MockPrerequisiteConsumerClient{ctrl: ctrl}
	mock.recorder = &MockPrerequisiteConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPrerequisiteConsumerClient) EXPECT() *MockPrerequisiteConsumerClientMockRecorder {
	return m.recorder
}

// GetPreRequisiteStatusRetry mocks base method.
func (m *MockPrerequisiteConsumerClient) GetPreRequisiteStatusRetry(ctx context.Context, in *consumer.GetPreRequisiteStatusRetryRequest, opts ...grpc.CallOption) (*consumer.GetPreRequisiteStatusRetryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPreRequisiteStatusRetry", varargs...)
	ret0, _ := ret[0].(*consumer.GetPreRequisiteStatusRetryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreRequisiteStatusRetry indicates an expected call of GetPreRequisiteStatusRetry.
func (mr *MockPrerequisiteConsumerClientMockRecorder) GetPreRequisiteStatusRetry(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreRequisiteStatusRetry", reflect.TypeOf((*MockPrerequisiteConsumerClient)(nil).GetPreRequisiteStatusRetry), varargs...)
}

// ProcessPrerequisiteFile mocks base method.
func (m *MockPrerequisiteConsumerClient) ProcessPrerequisiteFile(ctx context.Context, in *consumer.ProcessPrerequisiteFileRequest, opts ...grpc.CallOption) (*consumer.ProcessPrerequisiteFileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessPrerequisiteFile", varargs...)
	ret0, _ := ret[0].(*consumer.ProcessPrerequisiteFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPrerequisiteFile indicates an expected call of ProcessPrerequisiteFile.
func (mr *MockPrerequisiteConsumerClientMockRecorder) ProcessPrerequisiteFile(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPrerequisiteFile", reflect.TypeOf((*MockPrerequisiteConsumerClient)(nil).ProcessPrerequisiteFile), varargs...)
}

// MockPrerequisiteConsumerServer is a mock of PrerequisiteConsumerServer interface.
type MockPrerequisiteConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockPrerequisiteConsumerServerMockRecorder
}

// MockPrerequisiteConsumerServerMockRecorder is the mock recorder for MockPrerequisiteConsumerServer.
type MockPrerequisiteConsumerServerMockRecorder struct {
	mock *MockPrerequisiteConsumerServer
}

// NewMockPrerequisiteConsumerServer creates a new mock instance.
func NewMockPrerequisiteConsumerServer(ctrl *gomock.Controller) *MockPrerequisiteConsumerServer {
	mock := &MockPrerequisiteConsumerServer{ctrl: ctrl}
	mock.recorder = &MockPrerequisiteConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPrerequisiteConsumerServer) EXPECT() *MockPrerequisiteConsumerServerMockRecorder {
	return m.recorder
}

// GetPreRequisiteStatusRetry mocks base method.
func (m *MockPrerequisiteConsumerServer) GetPreRequisiteStatusRetry(arg0 context.Context, arg1 *consumer.GetPreRequisiteStatusRetryRequest) (*consumer.GetPreRequisiteStatusRetryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreRequisiteStatusRetry", arg0, arg1)
	ret0, _ := ret[0].(*consumer.GetPreRequisiteStatusRetryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreRequisiteStatusRetry indicates an expected call of GetPreRequisiteStatusRetry.
func (mr *MockPrerequisiteConsumerServerMockRecorder) GetPreRequisiteStatusRetry(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreRequisiteStatusRetry", reflect.TypeOf((*MockPrerequisiteConsumerServer)(nil).GetPreRequisiteStatusRetry), arg0, arg1)
}

// ProcessPrerequisiteFile mocks base method.
func (m *MockPrerequisiteConsumerServer) ProcessPrerequisiteFile(arg0 context.Context, arg1 *consumer.ProcessPrerequisiteFileRequest) (*consumer.ProcessPrerequisiteFileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessPrerequisiteFile", arg0, arg1)
	ret0, _ := ret[0].(*consumer.ProcessPrerequisiteFileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPrerequisiteFile indicates an expected call of ProcessPrerequisiteFile.
func (mr *MockPrerequisiteConsumerServerMockRecorder) ProcessPrerequisiteFile(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPrerequisiteFile", reflect.TypeOf((*MockPrerequisiteConsumerServer)(nil).ProcessPrerequisiteFile), arg0, arg1)
}

// MockUnsafePrerequisiteConsumerServer is a mock of UnsafePrerequisiteConsumerServer interface.
type MockUnsafePrerequisiteConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePrerequisiteConsumerServerMockRecorder
}

// MockUnsafePrerequisiteConsumerServerMockRecorder is the mock recorder for MockUnsafePrerequisiteConsumerServer.
type MockUnsafePrerequisiteConsumerServerMockRecorder struct {
	mock *MockUnsafePrerequisiteConsumerServer
}

// NewMockUnsafePrerequisiteConsumerServer creates a new mock instance.
func NewMockUnsafePrerequisiteConsumerServer(ctrl *gomock.Controller) *MockUnsafePrerequisiteConsumerServer {
	mock := &MockUnsafePrerequisiteConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePrerequisiteConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePrerequisiteConsumerServer) EXPECT() *MockUnsafePrerequisiteConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPrerequisiteConsumerServer mocks base method.
func (m *MockUnsafePrerequisiteConsumerServer) mustEmbedUnimplementedPrerequisiteConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPrerequisiteConsumerServer")
}

// mustEmbedUnimplementedPrerequisiteConsumerServer indicates an expected call of mustEmbedUnimplementedPrerequisiteConsumerServer.
func (mr *MockUnsafePrerequisiteConsumerServerMockRecorder) mustEmbedUnimplementedPrerequisiteConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPrerequisiteConsumerServer", reflect.TypeOf((*MockUnsafePrerequisiteConsumerServer)(nil).mustEmbedUnimplementedPrerequisiteConsumerServer))
}
