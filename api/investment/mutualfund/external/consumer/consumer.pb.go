// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/mutualfund/external/consumer/consumer.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	smallcase "github.com/epifi/gamma/api/vendornotification/investment/smallcase"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessMFHoldingsCallbackConsumerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Types that are assignable to Identifier:
	//
	//	*ProcessMFHoldingsCallbackConsumerRequest_SmallcaseMfHoldingsPayload
	Identifier isProcessMFHoldingsCallbackConsumerRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *ProcessMFHoldingsCallbackConsumerRequest) Reset() {
	*x = ProcessMFHoldingsCallbackConsumerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessMFHoldingsCallbackConsumerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessMFHoldingsCallbackConsumerRequest) ProtoMessage() {}

func (x *ProcessMFHoldingsCallbackConsumerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessMFHoldingsCallbackConsumerRequest.ProtoReflect.Descriptor instead.
func (*ProcessMFHoldingsCallbackConsumerRequest) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessMFHoldingsCallbackConsumerRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (m *ProcessMFHoldingsCallbackConsumerRequest) GetIdentifier() isProcessMFHoldingsCallbackConsumerRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *ProcessMFHoldingsCallbackConsumerRequest) GetSmallcaseMfHoldingsPayload() *smallcase.SmallCaseMFHoldingsPayLoad {
	if x, ok := x.GetIdentifier().(*ProcessMFHoldingsCallbackConsumerRequest_SmallcaseMfHoldingsPayload); ok {
		return x.SmallcaseMfHoldingsPayload
	}
	return nil
}

type isProcessMFHoldingsCallbackConsumerRequest_Identifier interface {
	isProcessMFHoldingsCallbackConsumerRequest_Identifier()
}

type ProcessMFHoldingsCallbackConsumerRequest_SmallcaseMfHoldingsPayload struct {
	SmallcaseMfHoldingsPayload *smallcase.SmallCaseMFHoldingsPayLoad `protobuf:"bytes,2,opt,name=smallcase_mf_holdings_payload,json=smallcaseMfHoldingsPayload,proto3,oneof"`
}

func (*ProcessMFHoldingsCallbackConsumerRequest_SmallcaseMfHoldingsPayload) isProcessMFHoldingsCallbackConsumerRequest_Identifier() {
}

type ProcessMFHoldingsCallbackConsumerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessMFHoldingsCallbackConsumerResponse) Reset() {
	*x = ProcessMFHoldingsCallbackConsumerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessMFHoldingsCallbackConsumerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessMFHoldingsCallbackConsumerResponse) ProtoMessage() {}

func (x *ProcessMFHoldingsCallbackConsumerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessMFHoldingsCallbackConsumerResponse.ProtoReflect.Descriptor instead.
func (*ProcessMFHoldingsCallbackConsumerResponse) Descriptor() ([]byte, []int) {
	return file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessMFHoldingsCallbackConsumerResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_investment_mutualfund_external_consumer_consumer_proto protoreflect.FileDescriptor

var file_api_investment_mutualfund_external_consumer_consumer_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2f, 0x63, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2b, 0x61, 0x70,
	0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74,
	0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x39, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73,
	0x6d, 0x61, 0x6c, 0x6c, 0x63, 0x61, 0x73, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x02, 0x0a, 0x28, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4d, 0x46, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x88, 0x01, 0x0a, 0x1d, 0x73, 0x6d, 0x61,
	0x6c, 0x6c, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x66, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x73, 0x6d, 0x61, 0x6c, 0x6c, 0x63, 0x61, 0x73, 0x65, 0x2e, 0x53, 0x6d, 0x61, 0x6c, 0x6c,
	0x43, 0x61, 0x73, 0x65, 0x4d, 0x46, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x61,
	0x79, 0x4c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x1a, 0x73, 0x6d, 0x61, 0x6c, 0x6c, 0x63, 0x61,
	0x73, 0x65, 0x4d, 0x66, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x50, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x22, 0x73, 0x0a, 0x29, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x46, 0x48, 0x6f,
	0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46,
	0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0xec, 0x01, 0x0a, 0x15, 0x4d, 0x46, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x53, 0x76, 0x63,
	0x12, 0xd2, 0x01, 0x0a, 0x21, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x46, 0x48, 0x6f,
	0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x55, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75,
	0x6e, 0x64, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x46, 0x48, 0x6f,
	0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x56, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x4d, 0x46, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x66, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x66, 0x75, 0x6e, 0x64, 0x2f, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescOnce sync.Once
	file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescData = file_api_investment_mutualfund_external_consumer_consumer_proto_rawDesc
)

func file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescGZIP() []byte {
	file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescOnce.Do(func() {
		file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescData)
	})
	return file_api_investment_mutualfund_external_consumer_consumer_proto_rawDescData
}

var file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_investment_mutualfund_external_consumer_consumer_proto_goTypes = []interface{}{
	(*ProcessMFHoldingsCallbackConsumerRequest)(nil),  // 0: api.investment.mutualfund.external.consumer.ProcessMFHoldingsCallbackConsumerRequest
	(*ProcessMFHoldingsCallbackConsumerResponse)(nil), // 1: api.investment.mutualfund.external.consumer.ProcessMFHoldingsCallbackConsumerResponse
	(*queue.ConsumerRequestHeader)(nil),               // 2: queue.ConsumerRequestHeader
	(*smallcase.SmallCaseMFHoldingsPayLoad)(nil),      // 3: vendornotification.investment.smallcase.SmallCaseMFHoldingsPayLoad
	(*queue.ConsumerResponseHeader)(nil),              // 4: queue.ConsumerResponseHeader
}
var file_api_investment_mutualfund_external_consumer_consumer_proto_depIdxs = []int32{
	2, // 0: api.investment.mutualfund.external.consumer.ProcessMFHoldingsCallbackConsumerRequest.request_header:type_name -> queue.ConsumerRequestHeader
	3, // 1: api.investment.mutualfund.external.consumer.ProcessMFHoldingsCallbackConsumerRequest.smallcase_mf_holdings_payload:type_name -> vendornotification.investment.smallcase.SmallCaseMFHoldingsPayLoad
	4, // 2: api.investment.mutualfund.external.consumer.ProcessMFHoldingsCallbackConsumerResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 3: api.investment.mutualfund.external.consumer.MFExternalConsumerSvc.ProcessMFHoldingsCallbackConsumer:input_type -> api.investment.mutualfund.external.consumer.ProcessMFHoldingsCallbackConsumerRequest
	1, // 4: api.investment.mutualfund.external.consumer.MFExternalConsumerSvc.ProcessMFHoldingsCallbackConsumer:output_type -> api.investment.mutualfund.external.consumer.ProcessMFHoldingsCallbackConsumerResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_investment_mutualfund_external_consumer_consumer_proto_init() }
func file_api_investment_mutualfund_external_consumer_consumer_proto_init() {
	if File_api_investment_mutualfund_external_consumer_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessMFHoldingsCallbackConsumerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessMFHoldingsCallbackConsumerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ProcessMFHoldingsCallbackConsumerRequest_SmallcaseMfHoldingsPayload)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_mutualfund_external_consumer_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_investment_mutualfund_external_consumer_consumer_proto_goTypes,
		DependencyIndexes: file_api_investment_mutualfund_external_consumer_consumer_proto_depIdxs,
		MessageInfos:      file_api_investment_mutualfund_external_consumer_consumer_proto_msgTypes,
	}.Build()
	File_api_investment_mutualfund_external_consumer_consumer_proto = out.File
	file_api_investment_mutualfund_external_consumer_consumer_proto_rawDesc = nil
	file_api_investment_mutualfund_external_consumer_consumer_proto_goTypes = nil
	file_api_investment_mutualfund_external_consumer_consumer_proto_depIdxs = nil
}
