// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/investment/mutualfund/external/consumer/consumer.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessMFHoldingsCallbackConsumerRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessMFHoldingsCallbackConsumerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessMFHoldingsCallbackConsumerRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessMFHoldingsCallbackConsumerRequestMultiError, or nil if none found.
func (m *ProcessMFHoldingsCallbackConsumerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessMFHoldingsCallbackConsumerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessMFHoldingsCallbackConsumerRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessMFHoldingsCallbackConsumerRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessMFHoldingsCallbackConsumerRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Identifier.(type) {
	case *ProcessMFHoldingsCallbackConsumerRequest_SmallcaseMfHoldingsPayload:
		if v == nil {
			err := ProcessMFHoldingsCallbackConsumerRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSmallcaseMfHoldingsPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessMFHoldingsCallbackConsumerRequestValidationError{
						field:  "SmallcaseMfHoldingsPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessMFHoldingsCallbackConsumerRequestValidationError{
						field:  "SmallcaseMfHoldingsPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSmallcaseMfHoldingsPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessMFHoldingsCallbackConsumerRequestValidationError{
					field:  "SmallcaseMfHoldingsPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProcessMFHoldingsCallbackConsumerRequestMultiError(errors)
	}

	return nil
}

// ProcessMFHoldingsCallbackConsumerRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessMFHoldingsCallbackConsumerRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessMFHoldingsCallbackConsumerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessMFHoldingsCallbackConsumerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessMFHoldingsCallbackConsumerRequestMultiError) AllErrors() []error { return m }

// ProcessMFHoldingsCallbackConsumerRequestValidationError is the validation
// error returned by ProcessMFHoldingsCallbackConsumerRequest.Validate if the
// designated constraints aren't met.
type ProcessMFHoldingsCallbackConsumerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessMFHoldingsCallbackConsumerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessMFHoldingsCallbackConsumerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessMFHoldingsCallbackConsumerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessMFHoldingsCallbackConsumerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessMFHoldingsCallbackConsumerRequestValidationError) ErrorName() string {
	return "ProcessMFHoldingsCallbackConsumerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessMFHoldingsCallbackConsumerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessMFHoldingsCallbackConsumerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessMFHoldingsCallbackConsumerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessMFHoldingsCallbackConsumerRequestValidationError{}

// Validate checks the field values on
// ProcessMFHoldingsCallbackConsumerResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessMFHoldingsCallbackConsumerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessMFHoldingsCallbackConsumerResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessMFHoldingsCallbackConsumerResponseMultiError, or nil if none found.
func (m *ProcessMFHoldingsCallbackConsumerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessMFHoldingsCallbackConsumerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessMFHoldingsCallbackConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessMFHoldingsCallbackConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessMFHoldingsCallbackConsumerResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessMFHoldingsCallbackConsumerResponseMultiError(errors)
	}

	return nil
}

// ProcessMFHoldingsCallbackConsumerResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessMFHoldingsCallbackConsumerResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessMFHoldingsCallbackConsumerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessMFHoldingsCallbackConsumerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessMFHoldingsCallbackConsumerResponseMultiError) AllErrors() []error { return m }

// ProcessMFHoldingsCallbackConsumerResponseValidationError is the validation
// error returned by ProcessMFHoldingsCallbackConsumerResponse.Validate if the
// designated constraints aren't met.
type ProcessMFHoldingsCallbackConsumerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessMFHoldingsCallbackConsumerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessMFHoldingsCallbackConsumerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessMFHoldingsCallbackConsumerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessMFHoldingsCallbackConsumerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessMFHoldingsCallbackConsumerResponseValidationError) ErrorName() string {
	return "ProcessMFHoldingsCallbackConsumerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessMFHoldingsCallbackConsumerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessMFHoldingsCallbackConsumerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessMFHoldingsCallbackConsumerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessMFHoldingsCallbackConsumerResponseValidationError{}
