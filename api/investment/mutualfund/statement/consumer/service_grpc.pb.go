//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/investment/mutualfund/statement/consumer/service.proto

package consumer

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	StatementConsumer_GenerateStatement_FullMethodName = "/api.investment.mutualfund.statement.consumer.StatementConsumer/GenerateStatement"
)

// StatementConsumerClient is the client API for StatementConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StatementConsumerClient interface {
	GenerateStatement(ctx context.Context, in *GenerateStatementRequest, opts ...grpc.CallOption) (*GenerateStatementResponse, error)
}

type statementConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewStatementConsumerClient(cc grpc.ClientConnInterface) StatementConsumerClient {
	return &statementConsumerClient{cc}
}

func (c *statementConsumerClient) GenerateStatement(ctx context.Context, in *GenerateStatementRequest, opts ...grpc.CallOption) (*GenerateStatementResponse, error) {
	out := new(GenerateStatementResponse)
	err := c.cc.Invoke(ctx, StatementConsumer_GenerateStatement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatementConsumerServer is the server API for StatementConsumer service.
// All implementations should embed UnimplementedStatementConsumerServer
// for forward compatibility
type StatementConsumerServer interface {
	GenerateStatement(context.Context, *GenerateStatementRequest) (*GenerateStatementResponse, error)
}

// UnimplementedStatementConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedStatementConsumerServer struct {
}

func (UnimplementedStatementConsumerServer) GenerateStatement(context.Context, *GenerateStatementRequest) (*GenerateStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateStatement not implemented")
}

// UnsafeStatementConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StatementConsumerServer will
// result in compilation errors.
type UnsafeStatementConsumerServer interface {
	mustEmbedUnimplementedStatementConsumerServer()
}

func RegisterStatementConsumerServer(s grpc.ServiceRegistrar, srv StatementConsumerServer) {
	s.RegisterService(&StatementConsumer_ServiceDesc, srv)
}

func _StatementConsumer_GenerateStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatementConsumerServer).GenerateStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StatementConsumer_GenerateStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatementConsumerServer).GenerateStatement(ctx, req.(*GenerateStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StatementConsumer_ServiceDesc is the grpc.ServiceDesc for StatementConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StatementConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.investment.mutualfund.statement.consumer.StatementConsumer",
	HandlerType: (*StatementConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateStatement",
			Handler:    _StatementConsumer_GenerateStatement_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/investment/mutualfund/statement/consumer/service.proto",
}
