syntax = "proto3";

package api.investment.dynamic_ui_element;


option go_package = "github.com/epifi/gamma/api/investment/dynamic_ui_element";
option java_package = "com.github.epifi.gamma.api.investment.dynamic_ui_element";


enum USSCollectionScreenExperiment {
  USS_COLLECTION_SCREEN_EXP_UNSPECIFIED = 0;
  // DEFAULT variant is the variant for which the already existing business logic of the code is executed.
  USS_COLLECTION_SCREEN_EXP_DEFAULT = 1;
  // CONTROL_1 variant is one of the variants for which an experiment business logic of the code is executed.
  USS_COLLECTION_SCREEN_EXP_CONTROL_1 = 2;
}

enum USSLandingPageExperiment {
  USS_LANDING_PAGE_EXP_UNSPECIFIED = 0;
  // DEFAULT variant is the variant for which the already existing business logic of the code is executed.
  USS_LANDING_PAGE_EXP_DEFAULT = 1;
  // CONTROL_1 variant is one of the variants for which an experiment business logic of the code is executed.
  USS_LANDING_PAGE_EXP_CONTROL_1 = 2;
}

enum InvHomePromotionalExperiment {
  INV_HOME_PROMOTIONAL_EXPERIMENT_UNSPECIFIED = 0;
  // DEFAULT variant is the variant for which the already existing business logic of the code is executed.
  INV_HOME_PROMOTIONAL_EXPERIMENT_DEFAULT = 1;
  // CONTROL_1 variant is one of the variants for which an experiment business logic of the code is executed.
  INV_HOME_PROMOTIONAL_EXPERIMENT_CONTROL_1 = 2;
}
