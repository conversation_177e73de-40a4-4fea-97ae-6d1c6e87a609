// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/investment/profile/investment_risk_survey.proto

//go:generate gen_sql -types=RiskSurveyData,SurveyStatus

package profile

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RiskSurveyQuestionType int32

const (
	RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_UNSPECIFIED RiskSurveyQuestionType = 0
	// Replaced with net-worth.
	// Net-worth includes both assets and liabilities.
	// existing liabilities and loans
	//
	// Deprecated: Marked as deprecated in api/investment/profile/investment_risk_survey.proto.
	RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_LIABILITIES RiskSurveyQuestionType = 1
	// expected best and worst case returns for a hypothetical investment scenario
	RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_EXPECTED_RETURNS RiskSurveyQuestionType = 2
	// existing categories of investments
	RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_EXISTING_INVESTMENTS RiskSurveyQuestionType = 3
	// Duration of investment, e.g., long-term, short-term, etc.
	RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_INVESTMENT_DURATION RiskSurveyQuestionType = 4
	// Willingness to absorb loss
	RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_RISK_TOLERANCE RiskSurveyQuestionType = 5
	// Net of assets and liabilities
	RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_NET_WORTH RiskSurveyQuestionType = 6
)

// Enum value maps for RiskSurveyQuestionType.
var (
	RiskSurveyQuestionType_name = map[int32]string{
		0: "RISK_SURVEY_QUESTION_TYPE_UNSPECIFIED",
		1: "RISK_SURVEY_QUESTION_TYPE_LIABILITIES",
		2: "RISK_SURVEY_QUESTION_TYPE_EXPECTED_RETURNS",
		3: "RISK_SURVEY_QUESTION_TYPE_EXISTING_INVESTMENTS",
		4: "RISK_SURVEY_QUESTION_TYPE_INVESTMENT_DURATION",
		5: "RISK_SURVEY_QUESTION_TYPE_RISK_TOLERANCE",
		6: "RISK_SURVEY_QUESTION_TYPE_NET_WORTH",
	}
	RiskSurveyQuestionType_value = map[string]int32{
		"RISK_SURVEY_QUESTION_TYPE_UNSPECIFIED":          0,
		"RISK_SURVEY_QUESTION_TYPE_LIABILITIES":          1,
		"RISK_SURVEY_QUESTION_TYPE_EXPECTED_RETURNS":     2,
		"RISK_SURVEY_QUESTION_TYPE_EXISTING_INVESTMENTS": 3,
		"RISK_SURVEY_QUESTION_TYPE_INVESTMENT_DURATION":  4,
		"RISK_SURVEY_QUESTION_TYPE_RISK_TOLERANCE":       5,
		"RISK_SURVEY_QUESTION_TYPE_NET_WORTH":            6,
	}
)

func (x RiskSurveyQuestionType) Enum() *RiskSurveyQuestionType {
	p := new(RiskSurveyQuestionType)
	*p = x
	return p
}

func (x RiskSurveyQuestionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskSurveyQuestionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[0].Descriptor()
}

func (RiskSurveyQuestionType) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[0]
}

func (x RiskSurveyQuestionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskSurveyQuestionType.Descriptor instead.
func (RiskSurveyQuestionType) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{0}
}

// Liability is replaced with NetWorth for more inclusivity.
// Net-worth includes both assets and liabilities.
// Answer choices for RISK_SURVEY_QUESTION_TYPE_LIABILITIES
//
// Deprecated: Marked as deprecated in api/investment/profile/investment_risk_survey.proto.
type Liability int32

const (
	Liability_LIABILITY_UNSPECIFIED       Liability = 0
	Liability_LIABILITY_LESS_THAN_10_LAKH Liability = 1
	Liability_LIABILITY_10_TO_25_LAKH     Liability = 2
	Liability_LIABILITY_26_TO_50_LAKH     Liability = 3
	Liability_LIABILITY_MORE_THAN_50_LAKH Liability = 4
	Liability_LIABILITY_NONE              Liability = 5
)

// Enum value maps for Liability.
var (
	Liability_name = map[int32]string{
		0: "LIABILITY_UNSPECIFIED",
		1: "LIABILITY_LESS_THAN_10_LAKH",
		2: "LIABILITY_10_TO_25_LAKH",
		3: "LIABILITY_26_TO_50_LAKH",
		4: "LIABILITY_MORE_THAN_50_LAKH",
		5: "LIABILITY_NONE",
	}
	Liability_value = map[string]int32{
		"LIABILITY_UNSPECIFIED":       0,
		"LIABILITY_LESS_THAN_10_LAKH": 1,
		"LIABILITY_10_TO_25_LAKH":     2,
		"LIABILITY_26_TO_50_LAKH":     3,
		"LIABILITY_MORE_THAN_50_LAKH": 4,
		"LIABILITY_NONE":              5,
	}
)

func (x Liability) Enum() *Liability {
	p := new(Liability)
	*p = x
	return p
}

func (x Liability) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Liability) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[1].Descriptor()
}

func (Liability) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[1]
}

func (x Liability) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Liability.Descriptor instead.
func (Liability) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{1}
}

// Expected returns based on a hypothetical investment scenario.
// Answer choices for RISK_SURVEY_QUESTION_TYPE_EXPECTED_RETURNS
type ExpectedReturns int32

const (
	ExpectedReturns_EXPECTED_RETURNS_UNSPECIFIED ExpectedReturns = 0
	// For and example investment of 1 lakh, returns for a year
	// Fixed profit of 7000, no loss
	ExpectedReturns_EXPECTED_RETURNS_FIXED ExpectedReturns = 1
	// Best case 8000 profit and worst case 3000 loss
	ExpectedReturns_EXPECTED_RETURNS_CONSERVATIVE ExpectedReturns = 2
	// Best case 12000 profit and worst case 6000 loss
	ExpectedReturns_EXPECTED_RETURNS_MODERATE ExpectedReturns = 3
	// Best case 25000 profit and worst case 15000 loss
	ExpectedReturns_EXPECTED_RETURNS_AGGRESSIVE ExpectedReturns = 4
)

// Enum value maps for ExpectedReturns.
var (
	ExpectedReturns_name = map[int32]string{
		0: "EXPECTED_RETURNS_UNSPECIFIED",
		1: "EXPECTED_RETURNS_FIXED",
		2: "EXPECTED_RETURNS_CONSERVATIVE",
		3: "EXPECTED_RETURNS_MODERATE",
		4: "EXPECTED_RETURNS_AGGRESSIVE",
	}
	ExpectedReturns_value = map[string]int32{
		"EXPECTED_RETURNS_UNSPECIFIED":  0,
		"EXPECTED_RETURNS_FIXED":        1,
		"EXPECTED_RETURNS_CONSERVATIVE": 2,
		"EXPECTED_RETURNS_MODERATE":     3,
		"EXPECTED_RETURNS_AGGRESSIVE":   4,
	}
)

func (x ExpectedReturns) Enum() *ExpectedReturns {
	p := new(ExpectedReturns)
	*p = x
	return p
}

func (x ExpectedReturns) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExpectedReturns) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[2].Descriptor()
}

func (ExpectedReturns) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[2]
}

func (x ExpectedReturns) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExpectedReturns.Descriptor instead.
func (ExpectedReturns) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{2}
}

// InvestmentInstrumentCombo denotes a combination of investment instruments a user might have invested in.
// This is not an exhaustive combination and new ones can be added when needed.
// This enum serves as the answer choices for the question type: RISK_SURVEY_QUESTION_TYPE_EXISTING_INVESTMENTS
type InvestmentInstrumentCombo int32

const (
	InvestmentInstrumentCombo_INVESTMENT_INSTRUMENT_COMBO_UNSPECIFIED InvestmentInstrumentCombo = 0
	// stocks and equity mutual funds
	InvestmentInstrumentCombo_INVESTMENT_INSTRUMENT_COMBO_STOCKS_EQUITY_MF InvestmentInstrumentCombo = 1
	// debt mutual funds
	InvestmentInstrumentCombo_INVESTMENT_INSTRUMENT_COMBO_DEBT_MF InvestmentInstrumentCombo = 2
	// fixed deposits, public provident fund and small savings scheme or none
	//
	// Deprecated: Marked as deprecated in api/investment/profile/investment_risk_survey.proto.
	InvestmentInstrumentCombo_INVESTMENT_INSTRUMENT_COMBO_FD_PPF InvestmentInstrumentCombo = 3
	// REIT, AIF and real estate
	InvestmentInstrumentCombo_INVESTMENT_INSTRUMENT_COMBO_OTHERS InvestmentInstrumentCombo = 4
	// no investments done yet
	//
	// Deprecated: Marked as deprecated in api/investment/profile/investment_risk_survey.proto.
	InvestmentInstrumentCombo_INVESTMENT_INSTRUMENT_COMBO_NONE InvestmentInstrumentCombo = 5
	// Investments in fixed return instruments like FD, PPF, etc. or no investements at all.
	InvestmentInstrumentCombo_INVESTMENT_INSTRUMENT_COMBO_FIXED_RETURNS_OR_NONE InvestmentInstrumentCombo = 6
)

// Enum value maps for InvestmentInstrumentCombo.
var (
	InvestmentInstrumentCombo_name = map[int32]string{
		0: "INVESTMENT_INSTRUMENT_COMBO_UNSPECIFIED",
		1: "INVESTMENT_INSTRUMENT_COMBO_STOCKS_EQUITY_MF",
		2: "INVESTMENT_INSTRUMENT_COMBO_DEBT_MF",
		3: "INVESTMENT_INSTRUMENT_COMBO_FD_PPF",
		4: "INVESTMENT_INSTRUMENT_COMBO_OTHERS",
		5: "INVESTMENT_INSTRUMENT_COMBO_NONE",
		6: "INVESTMENT_INSTRUMENT_COMBO_FIXED_RETURNS_OR_NONE",
	}
	InvestmentInstrumentCombo_value = map[string]int32{
		"INVESTMENT_INSTRUMENT_COMBO_UNSPECIFIED":           0,
		"INVESTMENT_INSTRUMENT_COMBO_STOCKS_EQUITY_MF":      1,
		"INVESTMENT_INSTRUMENT_COMBO_DEBT_MF":               2,
		"INVESTMENT_INSTRUMENT_COMBO_FD_PPF":                3,
		"INVESTMENT_INSTRUMENT_COMBO_OTHERS":                4,
		"INVESTMENT_INSTRUMENT_COMBO_NONE":                  5,
		"INVESTMENT_INSTRUMENT_COMBO_FIXED_RETURNS_OR_NONE": 6,
	}
)

func (x InvestmentInstrumentCombo) Enum() *InvestmentInstrumentCombo {
	p := new(InvestmentInstrumentCombo)
	*p = x
	return p
}

func (x InvestmentInstrumentCombo) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvestmentInstrumentCombo) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[3].Descriptor()
}

func (InvestmentInstrumentCombo) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[3]
}

func (x InvestmentInstrumentCombo) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvestmentInstrumentCombo.Descriptor instead.
func (InvestmentInstrumentCombo) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{3}
}

// Duration of investment, e.g., long-term, short-term, etc.
type InvestmentDuration int32

const (
	InvestmentDuration_INVESTMENT_DURATION_UNSPECIFIED InvestmentDuration = 0
	// Greater than 10-year investment duration
	InvestmentDuration_INVESTMENT_DURATION_LONG_TERM InvestmentDuration = 1
	// A 5 to 10-year investment duration
	InvestmentDuration_INVESTMENT_DURATION_MID_TERM InvestmentDuration = 2
	// A 1 to 5-year investment duration
	InvestmentDuration_INVESTMENT_DURATION_SHORT_TERM InvestmentDuration = 3
	// Less than 1-year investment duration, i.e., emergency fund.
	InvestmentDuration_INVESTMENT_DURATION_ULTRA_SHORT_TERM InvestmentDuration = 4
)

// Enum value maps for InvestmentDuration.
var (
	InvestmentDuration_name = map[int32]string{
		0: "INVESTMENT_DURATION_UNSPECIFIED",
		1: "INVESTMENT_DURATION_LONG_TERM",
		2: "INVESTMENT_DURATION_MID_TERM",
		3: "INVESTMENT_DURATION_SHORT_TERM",
		4: "INVESTMENT_DURATION_ULTRA_SHORT_TERM",
	}
	InvestmentDuration_value = map[string]int32{
		"INVESTMENT_DURATION_UNSPECIFIED":      0,
		"INVESTMENT_DURATION_LONG_TERM":        1,
		"INVESTMENT_DURATION_MID_TERM":         2,
		"INVESTMENT_DURATION_SHORT_TERM":       3,
		"INVESTMENT_DURATION_ULTRA_SHORT_TERM": 4,
	}
)

func (x InvestmentDuration) Enum() *InvestmentDuration {
	p := new(InvestmentDuration)
	*p = x
	return p
}

func (x InvestmentDuration) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvestmentDuration) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[4].Descriptor()
}

func (InvestmentDuration) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[4]
}

func (x InvestmentDuration) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvestmentDuration.Descriptor instead.
func (InvestmentDuration) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{4}
}

// Willingness to absorb loss
type RiskTolerance int32

const (
	RiskTolerance_RISK_TOLERANCE_UNSPECIFIED RiskTolerance = 0
	// Less than 10%, i.e., can absorb losses & would likely stay invested
	RiskTolerance_RISK_TOLERANCE_LOW RiskTolerance = 1
	// Up to 20%, i.e., can tolerate moderate losses
	RiskTolerance_RISK_TOLERANCE_MEDIUM RiskTolerance = 2
	// Up to 35%, i.e., higher losses are tolerable, but will re-evaluate investment strategy
	RiskTolerance_RISK_TOLERANCE_HIGH RiskTolerance = 3
	// Greater than 35%, i.e., will take action/pull out investments if the losses are stacked this high
	RiskTolerance_RISK_TOLERANCE_VERY_HIGH RiskTolerance = 4
)

// Enum value maps for RiskTolerance.
var (
	RiskTolerance_name = map[int32]string{
		0: "RISK_TOLERANCE_UNSPECIFIED",
		1: "RISK_TOLERANCE_LOW",
		2: "RISK_TOLERANCE_MEDIUM",
		3: "RISK_TOLERANCE_HIGH",
		4: "RISK_TOLERANCE_VERY_HIGH",
	}
	RiskTolerance_value = map[string]int32{
		"RISK_TOLERANCE_UNSPECIFIED": 0,
		"RISK_TOLERANCE_LOW":         1,
		"RISK_TOLERANCE_MEDIUM":      2,
		"RISK_TOLERANCE_HIGH":        3,
		"RISK_TOLERANCE_VERY_HIGH":   4,
	}
)

func (x RiskTolerance) Enum() *RiskTolerance {
	p := new(RiskTolerance)
	*p = x
	return p
}

func (x RiskTolerance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskTolerance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[5].Descriptor()
}

func (RiskTolerance) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[5]
}

func (x RiskTolerance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskTolerance.Descriptor instead.
func (RiskTolerance) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{5}
}

// Net-worth is net of assets and liabilities.
type NetWorth int32

const (
	NetWorth_NET_WORTH_UNSPECIFIED NetWorth = 0
	// Assets exceed liabilities by more than 40%
	NetWorth_NET_WORTH_POSITIVE NetWorth = 1
	// Assets exceed liabilities by more than 15%
	NetWorth_NET_WORTH_SLIGHTLY_POSITIVE NetWorth = 2
	// Assets and liabilities are almost the same
	NetWorth_NET_WORTH_BREAK_EVEN NetWorth = 3
	// Liabilities exceed assets
	NetWorth_NET_WORTH_NEGATIVE NetWorth = 4
)

// Enum value maps for NetWorth.
var (
	NetWorth_name = map[int32]string{
		0: "NET_WORTH_UNSPECIFIED",
		1: "NET_WORTH_POSITIVE",
		2: "NET_WORTH_SLIGHTLY_POSITIVE",
		3: "NET_WORTH_BREAK_EVEN",
		4: "NET_WORTH_NEGATIVE",
	}
	NetWorth_value = map[string]int32{
		"NET_WORTH_UNSPECIFIED":       0,
		"NET_WORTH_POSITIVE":          1,
		"NET_WORTH_SLIGHTLY_POSITIVE": 2,
		"NET_WORTH_BREAK_EVEN":        3,
		"NET_WORTH_NEGATIVE":          4,
	}
)

func (x NetWorth) Enum() *NetWorth {
	p := new(NetWorth)
	*p = x
	return p
}

func (x NetWorth) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetWorth) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[6].Descriptor()
}

func (NetWorth) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[6]
}

func (x NetWorth) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetWorth.Descriptor instead.
func (NetWorth) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{6}
}

// status of the survey for a user
type SurveyStatus int32

const (
	SurveyStatus_SURVEY_STATUS_UNSPECIFIED SurveyStatus = 0
	// answers to all the questions are submitted
	// currently we create entry in DB for all the answers together
	// so, only submitted status is enough now
	SurveyStatus_SURVEY_STATUS_SUBMITTED SurveyStatus = 1
)

// Enum value maps for SurveyStatus.
var (
	SurveyStatus_name = map[int32]string{
		0: "SURVEY_STATUS_UNSPECIFIED",
		1: "SURVEY_STATUS_SUBMITTED",
	}
	SurveyStatus_value = map[string]int32{
		"SURVEY_STATUS_UNSPECIFIED": 0,
		"SURVEY_STATUS_SUBMITTED":   1,
	}
)

func (x SurveyStatus) Enum() *SurveyStatus {
	p := new(SurveyStatus)
	*p = x
	return p
}

func (x SurveyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SurveyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_investment_profile_investment_risk_survey_proto_enumTypes[7].Descriptor()
}

func (SurveyStatus) Type() protoreflect.EnumType {
	return &file_api_investment_profile_investment_risk_survey_proto_enumTypes[7]
}

func (x SurveyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SurveyStatus.Descriptor instead.
func (SurveyStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{7}
}

// InvestmentRiskSurvey is a set of questions posed to customer and answers given by the customer
type InvestmentRiskSurvey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// primary identifier for investment_risk_survey DB model
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor id of the customer
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// survey data collected from customer
	SurveyData *RiskSurveyData `protobuf:"bytes,3,opt,name=survey_data,json=surveyData,proto3" json:"survey_data,omitempty"`
	// status of survey data collection
	SurveyStatus SurveyStatus `protobuf:"varint,4,opt,name=survey_status,json=surveyStatus,proto3,enum=api.investment.profile.SurveyStatus" json:"survey_status,omitempty"`
	// Standard timestamp fields
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAtUnix int64                  `protobuf:"varint,7,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
}

func (x *InvestmentRiskSurvey) Reset() {
	*x = InvestmentRiskSurvey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentRiskSurvey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentRiskSurvey) ProtoMessage() {}

func (x *InvestmentRiskSurvey) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentRiskSurvey.ProtoReflect.Descriptor instead.
func (*InvestmentRiskSurvey) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{0}
}

func (x *InvestmentRiskSurvey) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InvestmentRiskSurvey) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InvestmentRiskSurvey) GetSurveyData() *RiskSurveyData {
	if x != nil {
		return x.SurveyData
	}
	return nil
}

func (x *InvestmentRiskSurvey) GetSurveyStatus() SurveyStatus {
	if x != nil {
		return x.SurveyStatus
	}
	return SurveyStatus_SURVEY_STATUS_UNSPECIFIED
}

func (x *InvestmentRiskSurvey) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *InvestmentRiskSurvey) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *InvestmentRiskSurvey) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

type RiskSurveyData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list of questions and corresponding answers given by the customer
	QuestionsAndAnswers []*RiskSurveyQuestionAndAnswer `protobuf:"bytes,1,rep,name=questions_and_answers,json=questionsAndAnswers,proto3" json:"questions_and_answers,omitempty"`
	// date of birth of the user collected from epifi tech
	Dob *date.Date `protobuf:"bytes,2,opt,name=dob,proto3" json:"dob,omitempty"`
	// income range of the user collected from epifi tech
	IncomeRange *IncomeRange `protobuf:"bytes,3,opt,name=income_range,json=incomeRange,proto3" json:"income_range,omitempty"`
}

func (x *RiskSurveyData) Reset() {
	*x = RiskSurveyData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskSurveyData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSurveyData) ProtoMessage() {}

func (x *RiskSurveyData) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSurveyData.ProtoReflect.Descriptor instead.
func (*RiskSurveyData) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{1}
}

func (x *RiskSurveyData) GetQuestionsAndAnswers() []*RiskSurveyQuestionAndAnswer {
	if x != nil {
		return x.QuestionsAndAnswers
	}
	return nil
}

func (x *RiskSurveyData) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *RiskSurveyData) GetIncomeRange() *IncomeRange {
	if x != nil {
		return x.IncomeRange
	}
	return nil
}

type IncomeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinValue int32 `protobuf:"varint,1,opt,name=min_value,json=minValue,proto3" json:"min_value,omitempty"`
	MaxValue int32 `protobuf:"varint,2,opt,name=max_value,json=maxValue,proto3" json:"max_value,omitempty"`
}

func (x *IncomeRange) Reset() {
	*x = IncomeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncomeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncomeRange) ProtoMessage() {}

func (x *IncomeRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncomeRange.ProtoReflect.Descriptor instead.
func (*IncomeRange) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{2}
}

func (x *IncomeRange) GetMinValue() int32 {
	if x != nil {
		return x.MinValue
	}
	return 0
}

func (x *IncomeRange) GetMaxValue() int32 {
	if x != nil {
		return x.MaxValue
	}
	return 0
}

type RiskSurveyQuestionAndAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type of question
	QuestionType RiskSurveyQuestionType `protobuf:"varint,1,opt,name=question_type,json=questionType,proto3,enum=api.investment.profile.RiskSurveyQuestionType" json:"question_type,omitempty"`
	// type of answer selected
	//
	// Types that are assignable to Answer:
	//
	//	*RiskSurveyQuestionAndAnswer_Liability
	//	*RiskSurveyQuestionAndAnswer_ExpectedReturns
	//	*RiskSurveyQuestionAndAnswer_ExistingInvestments
	//	*RiskSurveyQuestionAndAnswer_InvestmentDurationAnswer
	//	*RiskSurveyQuestionAndAnswer_RiskToleranceAnswer
	//	*RiskSurveyQuestionAndAnswer_NetWorthAnswer
	Answer isRiskSurveyQuestionAndAnswer_Answer `protobuf_oneof:"answer"`
}

func (x *RiskSurveyQuestionAndAnswer) Reset() {
	*x = RiskSurveyQuestionAndAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskSurveyQuestionAndAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSurveyQuestionAndAnswer) ProtoMessage() {}

func (x *RiskSurveyQuestionAndAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSurveyQuestionAndAnswer.ProtoReflect.Descriptor instead.
func (*RiskSurveyQuestionAndAnswer) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{3}
}

func (x *RiskSurveyQuestionAndAnswer) GetQuestionType() RiskSurveyQuestionType {
	if x != nil {
		return x.QuestionType
	}
	return RiskSurveyQuestionType_RISK_SURVEY_QUESTION_TYPE_UNSPECIFIED
}

func (m *RiskSurveyQuestionAndAnswer) GetAnswer() isRiskSurveyQuestionAndAnswer_Answer {
	if m != nil {
		return m.Answer
	}
	return nil
}

// Deprecated: Marked as deprecated in api/investment/profile/investment_risk_survey.proto.
func (x *RiskSurveyQuestionAndAnswer) GetLiability() *LiabilityAnswer {
	if x, ok := x.GetAnswer().(*RiskSurveyQuestionAndAnswer_Liability); ok {
		return x.Liability
	}
	return nil
}

func (x *RiskSurveyQuestionAndAnswer) GetExpectedReturns() *ExpectedReturnsAnswer {
	if x, ok := x.GetAnswer().(*RiskSurveyQuestionAndAnswer_ExpectedReturns); ok {
		return x.ExpectedReturns
	}
	return nil
}

func (x *RiskSurveyQuestionAndAnswer) GetExistingInvestments() *ExistingInvestmentsAnswer {
	if x, ok := x.GetAnswer().(*RiskSurveyQuestionAndAnswer_ExistingInvestments); ok {
		return x.ExistingInvestments
	}
	return nil
}

func (x *RiskSurveyQuestionAndAnswer) GetInvestmentDurationAnswer() *InvestmentDurationAnswer {
	if x, ok := x.GetAnswer().(*RiskSurveyQuestionAndAnswer_InvestmentDurationAnswer); ok {
		return x.InvestmentDurationAnswer
	}
	return nil
}

func (x *RiskSurveyQuestionAndAnswer) GetRiskToleranceAnswer() *RiskToleranceAnswer {
	if x, ok := x.GetAnswer().(*RiskSurveyQuestionAndAnswer_RiskToleranceAnswer); ok {
		return x.RiskToleranceAnswer
	}
	return nil
}

func (x *RiskSurveyQuestionAndAnswer) GetNetWorthAnswer() *NetWorthAnswer {
	if x, ok := x.GetAnswer().(*RiskSurveyQuestionAndAnswer_NetWorthAnswer); ok {
		return x.NetWorthAnswer
	}
	return nil
}

type isRiskSurveyQuestionAndAnswer_Answer interface {
	isRiskSurveyQuestionAndAnswer_Answer()
}

type RiskSurveyQuestionAndAnswer_Liability struct {
	// LiabilityAnswer is replaced with NetWorthAnswer for more inclusivity.
	// Net-worth includes both assets and liabilities.
	//
	// Deprecated: Marked as deprecated in api/investment/profile/investment_risk_survey.proto.
	Liability *LiabilityAnswer `protobuf:"bytes,2,opt,name=liability,proto3,oneof"`
}

type RiskSurveyQuestionAndAnswer_ExpectedReturns struct {
	ExpectedReturns *ExpectedReturnsAnswer `protobuf:"bytes,3,opt,name=expected_returns,json=expectedReturns,proto3,oneof"`
}

type RiskSurveyQuestionAndAnswer_ExistingInvestments struct {
	ExistingInvestments *ExistingInvestmentsAnswer `protobuf:"bytes,4,opt,name=existing_investments,json=existingInvestments,proto3,oneof"`
}

type RiskSurveyQuestionAndAnswer_InvestmentDurationAnswer struct {
	InvestmentDurationAnswer *InvestmentDurationAnswer `protobuf:"bytes,5,opt,name=investment_duration_answer,json=investmentDurationAnswer,proto3,oneof"`
}

type RiskSurveyQuestionAndAnswer_RiskToleranceAnswer struct {
	RiskToleranceAnswer *RiskToleranceAnswer `protobuf:"bytes,6,opt,name=risk_tolerance_answer,json=riskToleranceAnswer,proto3,oneof"`
}

type RiskSurveyQuestionAndAnswer_NetWorthAnswer struct {
	NetWorthAnswer *NetWorthAnswer `protobuf:"bytes,7,opt,name=net_worth_answer,json=netWorthAnswer,proto3,oneof"`
}

func (*RiskSurveyQuestionAndAnswer_Liability) isRiskSurveyQuestionAndAnswer_Answer() {}

func (*RiskSurveyQuestionAndAnswer_ExpectedReturns) isRiskSurveyQuestionAndAnswer_Answer() {}

func (*RiskSurveyQuestionAndAnswer_ExistingInvestments) isRiskSurveyQuestionAndAnswer_Answer() {}

func (*RiskSurveyQuestionAndAnswer_InvestmentDurationAnswer) isRiskSurveyQuestionAndAnswer_Answer() {}

func (*RiskSurveyQuestionAndAnswer_RiskToleranceAnswer) isRiskSurveyQuestionAndAnswer_Answer() {}

func (*RiskSurveyQuestionAndAnswer_NetWorthAnswer) isRiskSurveyQuestionAndAnswer_Answer() {}

// LiabilityAnswer is replaced with NetWorthAnswer for more inclusivity.
// Net-worth includes both assets and liabilities.
// answer selected for RISK_SURVEY_QUESTION_TYPE_LIABILITIES
//
// Deprecated: Marked as deprecated in api/investment/profile/investment_risk_survey.proto.
type LiabilityAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Liability Liability `protobuf:"varint,1,opt,name=liability,proto3,enum=api.investment.profile.Liability" json:"liability,omitempty"`
}

func (x *LiabilityAnswer) Reset() {
	*x = LiabilityAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LiabilityAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LiabilityAnswer) ProtoMessage() {}

func (x *LiabilityAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LiabilityAnswer.ProtoReflect.Descriptor instead.
func (*LiabilityAnswer) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{4}
}

func (x *LiabilityAnswer) GetLiability() Liability {
	if x != nil {
		return x.Liability
	}
	return Liability_LIABILITY_UNSPECIFIED
}

// answer selected for RISK_SURVEY_QUESTION_TYPE_BEST_AND_WORST_CASE_RETURNS
type ExpectedReturnsAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpectedReturns ExpectedReturns `protobuf:"varint,1,opt,name=expected_returns,json=expectedReturns,proto3,enum=api.investment.profile.ExpectedReturns" json:"expected_returns,omitempty"`
}

func (x *ExpectedReturnsAnswer) Reset() {
	*x = ExpectedReturnsAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpectedReturnsAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpectedReturnsAnswer) ProtoMessage() {}

func (x *ExpectedReturnsAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpectedReturnsAnswer.ProtoReflect.Descriptor instead.
func (*ExpectedReturnsAnswer) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{5}
}

func (x *ExpectedReturnsAnswer) GetExpectedReturns() ExpectedReturns {
	if x != nil {
		return x.ExpectedReturns
	}
	return ExpectedReturns_EXPECTED_RETURNS_UNSPECIFIED
}

// answer selected for RISK_SURVEY_QUESTION_TYPE_EXISTING_INVESTMENTS
type ExistingInvestmentsAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExistingInvestments []InvestmentInstrumentCombo `protobuf:"varint,1,rep,packed,name=existing_investments,json=existingInvestments,proto3,enum=api.investment.profile.InvestmentInstrumentCombo" json:"existing_investments,omitempty"`
}

func (x *ExistingInvestmentsAnswer) Reset() {
	*x = ExistingInvestmentsAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExistingInvestmentsAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExistingInvestmentsAnswer) ProtoMessage() {}

func (x *ExistingInvestmentsAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExistingInvestmentsAnswer.ProtoReflect.Descriptor instead.
func (*ExistingInvestmentsAnswer) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{6}
}

func (x *ExistingInvestmentsAnswer) GetExistingInvestments() []InvestmentInstrumentCombo {
	if x != nil {
		return x.ExistingInvestments
	}
	return nil
}

// Duration of investment, e.g., long-term, short-term, etc.
type InvestmentDurationAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentDuration InvestmentDuration `protobuf:"varint,1,opt,name=investment_duration,json=investmentDuration,proto3,enum=api.investment.profile.InvestmentDuration" json:"investment_duration,omitempty"`
}

func (x *InvestmentDurationAnswer) Reset() {
	*x = InvestmentDurationAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentDurationAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentDurationAnswer) ProtoMessage() {}

func (x *InvestmentDurationAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentDurationAnswer.ProtoReflect.Descriptor instead.
func (*InvestmentDurationAnswer) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{7}
}

func (x *InvestmentDurationAnswer) GetInvestmentDuration() InvestmentDuration {
	if x != nil {
		return x.InvestmentDuration
	}
	return InvestmentDuration_INVESTMENT_DURATION_UNSPECIFIED
}

// Willingness to absorb loss
type RiskToleranceAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RiskTolerance RiskTolerance `protobuf:"varint,1,opt,name=risk_tolerance,json=riskTolerance,proto3,enum=api.investment.profile.RiskTolerance" json:"risk_tolerance,omitempty"`
}

func (x *RiskToleranceAnswer) Reset() {
	*x = RiskToleranceAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskToleranceAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskToleranceAnswer) ProtoMessage() {}

func (x *RiskToleranceAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskToleranceAnswer.ProtoReflect.Descriptor instead.
func (*RiskToleranceAnswer) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{8}
}

func (x *RiskToleranceAnswer) GetRiskTolerance() RiskTolerance {
	if x != nil {
		return x.RiskTolerance
	}
	return RiskTolerance_RISK_TOLERANCE_UNSPECIFIED
}

// Net-worth is net of assets and liabilities.
type NetWorthAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetWorth NetWorth `protobuf:"varint,1,opt,name=net_worth,json=netWorth,proto3,enum=api.investment.profile.NetWorth" json:"net_worth,omitempty"`
}

func (x *NetWorthAnswer) Reset() {
	*x = NetWorthAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetWorthAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetWorthAnswer) ProtoMessage() {}

func (x *NetWorthAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_investment_profile_investment_risk_survey_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetWorthAnswer.ProtoReflect.Descriptor instead.
func (*NetWorthAnswer) Descriptor() ([]byte, []int) {
	return file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP(), []int{9}
}

func (x *NetWorthAnswer) GetNetWorth() NetWorth {
	if x != nil {
		return x.NetWorth
	}
	return NetWorth_NET_WORTH_UNSPECIFIED
}

var File_api_investment_profile_investment_risk_survey_proto protoreflect.FileDescriptor

var file_api_investment_profile_investment_risk_survey_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x02, 0x0a, 0x14, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0b, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x0d, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0c, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x22, 0xe6, 0x01, 0x0a,
	0x0e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x67, 0x0a, 0x15, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x61, 0x6e, 0x64,
	0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x52, 0x13, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x6e,
	0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x46, 0x0a,
	0x0c, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x49, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0b, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x47, 0x0a, 0x0b, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb6,
	0x05, 0x0a, 0x1b, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x53,
	0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x52,
	0x69, 0x73, 0x6b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x4b, 0x0a, 0x09, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x42,
	0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x09, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x5a, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x73, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x65, 0x78, 0x70,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x12, 0x66, 0x0a, 0x14,
	0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x45, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x13, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x70, 0x0a, 0x1a, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x48, 0x00, 0x52, 0x18, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x61, 0x0a, 0x15, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74,
	0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x52,
	0x69, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x13, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x10, 0x6e, 0x65, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0e, 0x6e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x42, 0x08, 0x0a,
	0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x22, 0x56, 0x0a, 0x0f, 0x4c, 0x69, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x09, 0x6c, 0x69,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x09, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x3a, 0x02, 0x18, 0x01, 0x22,
	0x6b, 0x0a, 0x15, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x73, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x52, 0x0f, 0x65, 0x78, 0x70,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x73, 0x22, 0x81, 0x01, 0x0a,
	0x19, 0x45, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x64, 0x0a, 0x14, 0x65, 0x78,
	0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x52, 0x13, 0x65, 0x78, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0x77, 0x0a, 0x18, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x5b, 0x0a, 0x13,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x63, 0x0a, 0x13, 0x52, 0x69, 0x73,
	0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x12, 0x4c, 0x0a, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x0d, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x4f,
	0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x12, 0x3d, 0x0a, 0x09, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x08, 0x6e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x2a,
	0xe0, 0x02, 0x0a, 0x16, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x25, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x55,
	0x52, 0x56, 0x45, 0x59, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x01,
	0x1a, 0x02, 0x08, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x52,
	0x56, 0x45, 0x59, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52,
	0x4e, 0x53, 0x10, 0x02, 0x12, 0x32, 0x0a, 0x2e, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x52,
	0x56, 0x45, 0x59, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x03, 0x12, 0x31, 0x0a, 0x2d, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x52,
	0x49, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x4f,
	0x4c, 0x45, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x05, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x10, 0x06, 0x2a, 0xba, 0x01, 0x0a, 0x09, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x19, 0x0a, 0x15, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4c,
	0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48,
	0x41, 0x4e, 0x5f, 0x31, 0x30, 0x5f, 0x4c, 0x41, 0x4b, 0x48, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17,
	0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x31, 0x30, 0x5f, 0x54, 0x4f, 0x5f,
	0x32, 0x35, 0x5f, 0x4c, 0x41, 0x4b, 0x48, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x49, 0x41,
	0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x32, 0x36, 0x5f, 0x54, 0x4f, 0x5f, 0x35, 0x30, 0x5f,
	0x4c, 0x41, 0x4b, 0x48, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c,
	0x49, 0x54, 0x59, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x35, 0x30,
	0x5f, 0x4c, 0x41, 0x4b, 0x48, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x49, 0x41, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x05, 0x1a, 0x02, 0x18, 0x01, 0x2a,
	0xb2, 0x01, 0x0a, 0x0f, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x52, 0x45,
	0x54, 0x55, 0x52, 0x4e, 0x53, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x52, 0x56, 0x41, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x52, 0x41, 0x54,
	0x45, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f,
	0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x53, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x53, 0x53, 0x49,
	0x56, 0x45, 0x10, 0x04, 0x2a, 0xd8, 0x02, 0x0a, 0x19, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d,
	0x62, 0x6f, 0x12, 0x2b, 0x0a, 0x27, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x42,
	0x4f, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x30, 0x0a, 0x2c, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x4f, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x5f, 0x4d, 0x46, 0x10,
	0x01, 0x12, 0x27, 0x0a, 0x23, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x4f,
	0x5f, 0x44, 0x45, 0x42, 0x54, 0x5f, 0x4d, 0x46, 0x10, 0x02, 0x12, 0x2a, 0x0a, 0x22, 0x49, 0x4e,
	0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x4f, 0x5f, 0x46, 0x44, 0x5f, 0x50, 0x50, 0x46,
	0x10, 0x03, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x43, 0x4f, 0x4d, 0x42, 0x4f, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x53, 0x10, 0x04, 0x12, 0x28,
	0x0a, 0x20, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x53,
	0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x4f, 0x5f, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x05, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x35, 0x0a, 0x31, 0x49, 0x4e, 0x56, 0x45,
	0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x42, 0x4f, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x52, 0x45,
	0x54, 0x55, 0x52, 0x4e, 0x53, 0x5f, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x06, 0x2a,
	0xcc, 0x01, 0x0a, 0x12, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x1f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x49,
	0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x10, 0x01, 0x12, 0x20,
	0x0a, 0x1c, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x55, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x49, 0x44, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x10, 0x02,
	0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44,
	0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x45,
	0x52, 0x4d, 0x10, 0x03, 0x12, 0x28, 0x0a, 0x24, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4c, 0x54, 0x52,
	0x41, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x10, 0x04, 0x2a, 0x99,
	0x01, 0x0a, 0x0d, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x4f, 0x4c, 0x45, 0x52, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x4f, 0x4c, 0x45, 0x52, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x54, 0x4f, 0x4c, 0x45, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x55,
	0x4d, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x4f, 0x4c, 0x45,
	0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18,
	0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x4f, 0x4c, 0x45, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x56,
	0x45, 0x52, 0x59, 0x5f, 0x48, 0x49, 0x47, 0x48, 0x10, 0x04, 0x2a, 0x90, 0x01, 0x0a, 0x08, 0x4e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x15, 0x4e, 0x45, 0x54, 0x5f, 0x57,
	0x4f, 0x52, 0x54, 0x48, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x4e, 0x45,
	0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x53, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x4c, 0x59,
	0x5f, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x4e,
	0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x4b, 0x5f, 0x45,
	0x56, 0x45, 0x4e, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x5f, 0x4e, 0x45, 0x47, 0x41, 0x54, 0x49, 0x56, 0x45, 0x10, 0x04, 0x2a, 0x4a, 0x0a,
	0x0c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a,
	0x19, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17,
	0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55,
	0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x01, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_investment_profile_investment_risk_survey_proto_rawDescOnce sync.Once
	file_api_investment_profile_investment_risk_survey_proto_rawDescData = file_api_investment_profile_investment_risk_survey_proto_rawDesc
)

func file_api_investment_profile_investment_risk_survey_proto_rawDescGZIP() []byte {
	file_api_investment_profile_investment_risk_survey_proto_rawDescOnce.Do(func() {
		file_api_investment_profile_investment_risk_survey_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_investment_profile_investment_risk_survey_proto_rawDescData)
	})
	return file_api_investment_profile_investment_risk_survey_proto_rawDescData
}

var file_api_investment_profile_investment_risk_survey_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_investment_profile_investment_risk_survey_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_investment_profile_investment_risk_survey_proto_goTypes = []interface{}{
	(RiskSurveyQuestionType)(0),         // 0: api.investment.profile.RiskSurveyQuestionType
	(Liability)(0),                      // 1: api.investment.profile.Liability
	(ExpectedReturns)(0),                // 2: api.investment.profile.ExpectedReturns
	(InvestmentInstrumentCombo)(0),      // 3: api.investment.profile.InvestmentInstrumentCombo
	(InvestmentDuration)(0),             // 4: api.investment.profile.InvestmentDuration
	(RiskTolerance)(0),                  // 5: api.investment.profile.RiskTolerance
	(NetWorth)(0),                       // 6: api.investment.profile.NetWorth
	(SurveyStatus)(0),                   // 7: api.investment.profile.SurveyStatus
	(*InvestmentRiskSurvey)(nil),        // 8: api.investment.profile.InvestmentRiskSurvey
	(*RiskSurveyData)(nil),              // 9: api.investment.profile.RiskSurveyData
	(*IncomeRange)(nil),                 // 10: api.investment.profile.IncomeRange
	(*RiskSurveyQuestionAndAnswer)(nil), // 11: api.investment.profile.RiskSurveyQuestionAndAnswer
	(*LiabilityAnswer)(nil),             // 12: api.investment.profile.LiabilityAnswer
	(*ExpectedReturnsAnswer)(nil),       // 13: api.investment.profile.ExpectedReturnsAnswer
	(*ExistingInvestmentsAnswer)(nil),   // 14: api.investment.profile.ExistingInvestmentsAnswer
	(*InvestmentDurationAnswer)(nil),    // 15: api.investment.profile.InvestmentDurationAnswer
	(*RiskToleranceAnswer)(nil),         // 16: api.investment.profile.RiskToleranceAnswer
	(*NetWorthAnswer)(nil),              // 17: api.investment.profile.NetWorthAnswer
	(*timestamppb.Timestamp)(nil),       // 18: google.protobuf.Timestamp
	(*date.Date)(nil),                   // 19: google.type.Date
}
var file_api_investment_profile_investment_risk_survey_proto_depIdxs = []int32{
	9,  // 0: api.investment.profile.InvestmentRiskSurvey.survey_data:type_name -> api.investment.profile.RiskSurveyData
	7,  // 1: api.investment.profile.InvestmentRiskSurvey.survey_status:type_name -> api.investment.profile.SurveyStatus
	18, // 2: api.investment.profile.InvestmentRiskSurvey.created_at:type_name -> google.protobuf.Timestamp
	18, // 3: api.investment.profile.InvestmentRiskSurvey.updated_at:type_name -> google.protobuf.Timestamp
	11, // 4: api.investment.profile.RiskSurveyData.questions_and_answers:type_name -> api.investment.profile.RiskSurveyQuestionAndAnswer
	19, // 5: api.investment.profile.RiskSurveyData.dob:type_name -> google.type.Date
	10, // 6: api.investment.profile.RiskSurveyData.income_range:type_name -> api.investment.profile.IncomeRange
	0,  // 7: api.investment.profile.RiskSurveyQuestionAndAnswer.question_type:type_name -> api.investment.profile.RiskSurveyQuestionType
	12, // 8: api.investment.profile.RiskSurveyQuestionAndAnswer.liability:type_name -> api.investment.profile.LiabilityAnswer
	13, // 9: api.investment.profile.RiskSurveyQuestionAndAnswer.expected_returns:type_name -> api.investment.profile.ExpectedReturnsAnswer
	14, // 10: api.investment.profile.RiskSurveyQuestionAndAnswer.existing_investments:type_name -> api.investment.profile.ExistingInvestmentsAnswer
	15, // 11: api.investment.profile.RiskSurveyQuestionAndAnswer.investment_duration_answer:type_name -> api.investment.profile.InvestmentDurationAnswer
	16, // 12: api.investment.profile.RiskSurveyQuestionAndAnswer.risk_tolerance_answer:type_name -> api.investment.profile.RiskToleranceAnswer
	17, // 13: api.investment.profile.RiskSurveyQuestionAndAnswer.net_worth_answer:type_name -> api.investment.profile.NetWorthAnswer
	1,  // 14: api.investment.profile.LiabilityAnswer.liability:type_name -> api.investment.profile.Liability
	2,  // 15: api.investment.profile.ExpectedReturnsAnswer.expected_returns:type_name -> api.investment.profile.ExpectedReturns
	3,  // 16: api.investment.profile.ExistingInvestmentsAnswer.existing_investments:type_name -> api.investment.profile.InvestmentInstrumentCombo
	4,  // 17: api.investment.profile.InvestmentDurationAnswer.investment_duration:type_name -> api.investment.profile.InvestmentDuration
	5,  // 18: api.investment.profile.RiskToleranceAnswer.risk_tolerance:type_name -> api.investment.profile.RiskTolerance
	6,  // 19: api.investment.profile.NetWorthAnswer.net_worth:type_name -> api.investment.profile.NetWorth
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_investment_profile_investment_risk_survey_proto_init() }
func file_api_investment_profile_investment_risk_survey_proto_init() {
	if File_api_investment_profile_investment_risk_survey_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentRiskSurvey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskSurveyData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncomeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskSurveyQuestionAndAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LiabilityAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpectedReturnsAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExistingInvestmentsAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentDurationAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskToleranceAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_investment_profile_investment_risk_survey_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetWorthAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_investment_profile_investment_risk_survey_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*RiskSurveyQuestionAndAnswer_Liability)(nil),
		(*RiskSurveyQuestionAndAnswer_ExpectedReturns)(nil),
		(*RiskSurveyQuestionAndAnswer_ExistingInvestments)(nil),
		(*RiskSurveyQuestionAndAnswer_InvestmentDurationAnswer)(nil),
		(*RiskSurveyQuestionAndAnswer_RiskToleranceAnswer)(nil),
		(*RiskSurveyQuestionAndAnswer_NetWorthAnswer)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_investment_profile_investment_risk_survey_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_investment_profile_investment_risk_survey_proto_goTypes,
		DependencyIndexes: file_api_investment_profile_investment_risk_survey_proto_depIdxs,
		EnumInfos:         file_api_investment_profile_investment_risk_survey_proto_enumTypes,
		MessageInfos:      file_api_investment_profile_investment_risk_survey_proto_msgTypes,
	}.Build()
	File_api_investment_profile_investment_risk_survey_proto = out.File
	file_api_investment_profile_investment_risk_survey_proto_rawDesc = nil
	file_api_investment_profile_investment_risk_survey_proto_goTypes = nil
	file_api_investment_profile_investment_risk_survey_proto_depIdxs = nil
}
