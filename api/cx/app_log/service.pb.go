// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/app_log/service.proto

package app_log

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	cx "github.com/epifi/gamma/api/cx"
	customer_auth "github.com/epifi/gamma/api/cx/customer_auth"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// enum to get the source flow from where the logs upload was triggered.
type PushAppLogsRequest_Source int32

const (
	// Default value for the Source enum.
	PushAppLogsRequest_SOURCE_UNSPECIFIED PushAppLogsRequest_Source = 0
	// The call for uploading logs is from the profile section of the app inside settings
	PushAppLogsRequest_SOURCE_PROFILE PushAppLogsRequest_Source = 1
	// Logs upload was triggered from the crash screen in the app
	PushAppLogsRequest_SOURCE_CRASH_SCREEN PushAppLogsRequest_Source = 2
	// Logs upload was triggered via the push notification sent for exporting logs
	PushAppLogsRequest_SOURCE_PUSH_NOTIFICATIONS PushAppLogsRequest_Source = 3
)

// Enum value maps for PushAppLogsRequest_Source.
var (
	PushAppLogsRequest_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "SOURCE_PROFILE",
		2: "SOURCE_CRASH_SCREEN",
		3: "SOURCE_PUSH_NOTIFICATIONS",
	}
	PushAppLogsRequest_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED":        0,
		"SOURCE_PROFILE":            1,
		"SOURCE_CRASH_SCREEN":       2,
		"SOURCE_PUSH_NOTIFICATIONS": 3,
	}
)

func (x PushAppLogsRequest_Source) Enum() *PushAppLogsRequest_Source {
	p := new(PushAppLogsRequest_Source)
	*p = x
	return p
}

func (x PushAppLogsRequest_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PushAppLogsRequest_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_app_log_service_proto_enumTypes[0].Descriptor()
}

func (PushAppLogsRequest_Source) Type() protoreflect.EnumType {
	return &file_api_cx_app_log_service_proto_enumTypes[0]
}

func (x PushAppLogsRequest_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PushAppLogsRequest_Source.Descriptor instead.
func (PushAppLogsRequest_Source) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{0, 0}
}

type PushAppLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// current app version of user pushing the logs
	AppVersion string `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// app log file as bytes
	Logs []byte `protobuf:"bytes,3,opt,name=logs,proto3" json:"logs,omitempty"`
	// source via which the logs are uploaded
	Source PushAppLogsRequest_Source `protobuf:"varint,4,opt,name=source,proto3,enum=cx.app_log.PushAppLogsRequest_Source" json:"source,omitempty"`
	// server generated identifier which is linked to identifier value (actorId, phone number)
	LogId string `protobuf:"bytes,5,opt,name=log_id,json=logId,proto3" json:"log_id,omitempty"`
}

func (x *PushAppLogsRequest) Reset() {
	*x = PushAppLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushAppLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushAppLogsRequest) ProtoMessage() {}

func (x *PushAppLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushAppLogsRequest.ProtoReflect.Descriptor instead.
func (*PushAppLogsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{0}
}

func (x *PushAppLogsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *PushAppLogsRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *PushAppLogsRequest) GetLogs() []byte {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *PushAppLogsRequest) GetSource() PushAppLogsRequest_Source {
	if x != nil {
		return x.Source
	}
	return PushAppLogsRequest_SOURCE_UNSPECIFIED
}

func (x *PushAppLogsRequest) GetLogId() string {
	if x != nil {
		return x.LogId
	}
	return ""
}

// proto for AppLog model
type AppLogDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId    string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AppVersion string `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// time at which backend received the app logs
	Timestamp string `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// source screen of the trigger for Pushing app logs. eg: profile screen, crash screen
	LogSource PushAppLogsRequest_Source `protobuf:"varint,4,opt,name=log_source,json=logSource,proto3,enum=cx.app_log.PushAppLogsRequest_Source" json:"log_source,omitempty"`
	// actual logs data
	Logs string `protobuf:"bytes,5,opt,name=logs,proto3" json:"logs,omitempty"`
}

func (x *AppLogDetail) Reset() {
	*x = AppLogDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppLogDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppLogDetail) ProtoMessage() {}

func (x *AppLogDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppLogDetail.ProtoReflect.Descriptor instead.
func (*AppLogDetail) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{1}
}

func (x *AppLogDetail) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AppLogDetail) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *AppLogDetail) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *AppLogDetail) GetLogSource() PushAppLogsRequest_Source {
	if x != nil {
		return x.LogSource
	}
	return PushAppLogsRequest_SOURCE_UNSPECIFIED
}

func (x *AppLogDetail) GetLogs() string {
	if x != nil {
		return x.Logs
	}
	return ""
}

type PushAppLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PushAppLogsResponse) Reset() {
	*x = PushAppLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushAppLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushAppLogsResponse) ProtoMessage() {}

func (x *PushAppLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushAppLogsResponse.ProtoReflect.Descriptor instead.
func (*PushAppLogsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{2}
}

func (x *PushAppLogsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetExportedLogsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token is mandatory
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Deprecated: Marked as deprecated in api/cx/app_log/service.proto.
	ActorId         string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	FromDate        *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	ToDate          *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	IdentifierType  IdentifierType         `protobuf:"varint,5,opt,name=identifier_type,json=identifierType,proto3,enum=cx.app_log.IdentifierType" json:"identifier_type,omitempty"`
	IdentifierValue string                 `protobuf:"bytes,6,opt,name=identifier_value,json=identifierValue,proto3" json:"identifier_value,omitempty"`
}

func (x *GetExportedLogsListRequest) Reset() {
	*x = GetExportedLogsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExportedLogsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExportedLogsListRequest) ProtoMessage() {}

func (x *GetExportedLogsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExportedLogsListRequest.ProtoReflect.Descriptor instead.
func (*GetExportedLogsListRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetExportedLogsListRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

// Deprecated: Marked as deprecated in api/cx/app_log/service.proto.
func (x *GetExportedLogsListRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetExportedLogsListRequest) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetExportedLogsListRequest) GetToDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *GetExportedLogsListRequest) GetIdentifierType() IdentifierType {
	if x != nil {
		return x.IdentifierType
	}
	return IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED
}

func (x *GetExportedLogsListRequest) GetIdentifierValue() string {
	if x != nil {
		return x.IdentifierValue
	}
	return ""
}

type AppLogMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId         string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	MobileNumber    *common.PhoneNumber    `protobuf:"bytes,2,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	ExportTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=export_timestamp,json=exportTimestamp,proto3" json:"export_timestamp,omitempty"`
	AppVersion      string                 `protobuf:"bytes,4,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	// log key to identify the actual log data file stored (currently stored in S3)
	LogKey string `protobuf:"bytes,5,opt,name=log_key,json=logKey,proto3" json:"log_key,omitempty"`
	// source of the trigger for Pushing app logs
	LogSource PushAppLogsRequest_Source `protobuf:"varint,6,opt,name=log_source,json=logSource,proto3,enum=cx.app_log.PushAppLogsRequest_Source" json:"log_source,omitempty"`
}

func (x *AppLogMeta) Reset() {
	*x = AppLogMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppLogMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppLogMeta) ProtoMessage() {}

func (x *AppLogMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppLogMeta.ProtoReflect.Descriptor instead.
func (*AppLogMeta) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{4}
}

func (x *AppLogMeta) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AppLogMeta) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *AppLogMeta) GetExportTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ExportTimestamp
	}
	return nil
}

func (x *AppLogMeta) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *AppLogMeta) GetLogKey() string {
	if x != nil {
		return x.LogKey
	}
	return ""
}

func (x *AppLogMeta) GetLogSource() PushAppLogsRequest_Source {
	if x != nil {
		return x.LogSource
	}
	return PushAppLogsRequest_SOURCE_UNSPECIFIED
}

type GetExportedLogsListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AppLogMeta []*AppLogMeta `protobuf:"bytes,2,rep,name=app_log_meta,json=appLogMeta,proto3" json:"app_log_meta,omitempty"`
}

func (x *GetExportedLogsListResponse) Reset() {
	*x = GetExportedLogsListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExportedLogsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExportedLogsListResponse) ProtoMessage() {}

func (x *GetExportedLogsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExportedLogsListResponse.ProtoReflect.Descriptor instead.
func (*GetExportedLogsListResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetExportedLogsListResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExportedLogsListResponse) GetAppLogMeta() []*AppLogMeta {
	if x != nil {
		return x.AppLogMeta
	}
	return nil
}

type GetLogsDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agent email and access token is mandatory
	Header  *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ActorId string     `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// log key in redis
	LogKey string `protobuf:"bytes,3,opt,name=log_key,json=logKey,proto3" json:"log_key,omitempty"`
	// page size if fixed for now just pass the page tokens for now
	PageContext *cx.PageContextRequest `protobuf:"bytes,5,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetLogsDataRequest) Reset() {
	*x = GetLogsDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLogsDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogsDataRequest) ProtoMessage() {}

func (x *GetLogsDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogsDataRequest.ProtoReflect.Descriptor instead.
func (*GetLogsDataRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetLogsDataRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetLogsDataRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetLogsDataRequest) GetLogKey() string {
	if x != nil {
		return x.LogKey
	}
	return ""
}

func (x *GetLogsDataRequest) GetPageContext() *cx.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetLogsDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Logs        string                  `protobuf:"bytes,2,opt,name=logs,proto3" json:"logs,omitempty"`
	PageContext *cx.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetLogsDataResponse) Reset() {
	*x = GetLogsDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLogsDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLogsDataResponse) ProtoMessage() {}

func (x *GetLogsDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLogsDataResponse.ProtoReflect.Descriptor instead.
func (*GetLogsDataResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetLogsDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLogsDataResponse) GetLogs() string {
	if x != nil {
		return x.Logs
	}
	return ""
}

func (x *GetLogsDataResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type PullAppLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ticket_id is mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *PullAppLogsRequest) Reset() {
	*x = PullAppLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullAppLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullAppLogsRequest) ProtoMessage() {}

func (x *PullAppLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullAppLogsRequest.ProtoReflect.Descriptor instead.
func (*PullAppLogsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{8}
}

func (x *PullAppLogsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type PullAppLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return
	// OK for success
	// Internal for server errors
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// will contain deeplink if customer auth is not passed and agent needs to be redirected to auth screen
	SherlockDeepLink *customer_auth.SherlockDeepLink `protobuf:"bytes,2,opt,name=sherlock_deep_link,json=sherlockDeepLink,proto3" json:"sherlock_deep_link,omitempty"`
}

func (x *PullAppLogsResponse) Reset() {
	*x = PullAppLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullAppLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullAppLogsResponse) ProtoMessage() {}

func (x *PullAppLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullAppLogsResponse.ProtoReflect.Descriptor instead.
func (*PullAppLogsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{9}
}

func (x *PullAppLogsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *PullAppLogsResponse) GetSherlockDeepLink() *customer_auth.SherlockDeepLink {
	if x != nil {
		return x.SherlockDeepLink
	}
	return nil
}

type GetExportedLogsListForAgentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ticket_id is mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetExportedLogsListForAgentsRequest) Reset() {
	*x = GetExportedLogsListForAgentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExportedLogsListForAgentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExportedLogsListForAgentsRequest) ProtoMessage() {}

func (x *GetExportedLogsListForAgentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExportedLogsListForAgentsRequest.ProtoReflect.Descriptor instead.
func (*GetExportedLogsListForAgentsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetExportedLogsListForAgentsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type AppLogMetaForAgents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExportTimestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=export_timestamp,json=exportTimestamp,proto3" json:"export_timestamp,omitempty"`
	AppVersion      string                 `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
}

func (x *AppLogMetaForAgents) Reset() {
	*x = AppLogMetaForAgents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppLogMetaForAgents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppLogMetaForAgents) ProtoMessage() {}

func (x *AppLogMetaForAgents) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppLogMetaForAgents.ProtoReflect.Descriptor instead.
func (*AppLogMetaForAgents) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{11}
}

func (x *AppLogMetaForAgents) GetExportTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ExportTimestamp
	}
	return nil
}

func (x *AppLogMetaForAgents) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type GetExportedLogsListForAgentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AppLogMeta []*AppLogMetaForAgents `protobuf:"bytes,2,rep,name=app_log_meta,json=appLogMeta,proto3" json:"app_log_meta,omitempty"`
}

func (x *GetExportedLogsListForAgentsResponse) Reset() {
	*x = GetExportedLogsListForAgentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExportedLogsListForAgentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExportedLogsListForAgentsResponse) ProtoMessage() {}

func (x *GetExportedLogsListForAgentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExportedLogsListForAgentsResponse.ProtoReflect.Descriptor instead.
func (*GetExportedLogsListForAgentsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetExportedLogsListForAgentsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExportedLogsListForAgentsResponse) GetAppLogMeta() []*AppLogMetaForAgents {
	if x != nil {
		return x.AppLogMeta
	}
	return nil
}

type SendPushAppLogsDeeplinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	MessageIdList []string    `protobuf:"bytes,2,rep,name=message_id_list,json=messageIdList,proto3" json:"message_id_list,omitempty"`
}

func (x *SendPushAppLogsDeeplinkResponse) Reset() {
	*x = SendPushAppLogsDeeplinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPushAppLogsDeeplinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushAppLogsDeeplinkResponse) ProtoMessage() {}

func (x *SendPushAppLogsDeeplinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushAppLogsDeeplinkResponse.ProtoReflect.Descriptor instead.
func (*SendPushAppLogsDeeplinkResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{13}
}

func (x *SendPushAppLogsDeeplinkResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SendPushAppLogsDeeplinkResponse) GetMessageIdList() []string {
	if x != nil {
		return x.MessageIdList
	}
	return nil
}

type SendPushAppLogsDeeplinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier type will determine the medium of notification being sent
	IdentifierType IdentifierType `protobuf:"varint,1,opt,name=identifier_type,json=identifierType,proto3,enum=cx.app_log.IdentifierType" json:"identifier_type,omitempty"`
	// identifier value for which the notification needs to be sent
	//
	// Types that are assignable to IdentifierValue:
	//
	//	*SendPushAppLogsDeeplinkRequest_ActorId
	//	*SendPushAppLogsDeeplinkRequest_PhoneNumber
	//	*SendPushAppLogsDeeplinkRequest_EmailId
	IdentifierValue isSendPushAppLogsDeeplinkRequest_IdentifierValue `protobuf_oneof:"identifier_value"`
}

func (x *SendPushAppLogsDeeplinkRequest) Reset() {
	*x = SendPushAppLogsDeeplinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_app_log_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPushAppLogsDeeplinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushAppLogsDeeplinkRequest) ProtoMessage() {}

func (x *SendPushAppLogsDeeplinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_app_log_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushAppLogsDeeplinkRequest.ProtoReflect.Descriptor instead.
func (*SendPushAppLogsDeeplinkRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_app_log_service_proto_rawDescGZIP(), []int{14}
}

func (x *SendPushAppLogsDeeplinkRequest) GetIdentifierType() IdentifierType {
	if x != nil {
		return x.IdentifierType
	}
	return IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED
}

func (m *SendPushAppLogsDeeplinkRequest) GetIdentifierValue() isSendPushAppLogsDeeplinkRequest_IdentifierValue {
	if m != nil {
		return m.IdentifierValue
	}
	return nil
}

func (x *SendPushAppLogsDeeplinkRequest) GetActorId() string {
	if x, ok := x.GetIdentifierValue().(*SendPushAppLogsDeeplinkRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *SendPushAppLogsDeeplinkRequest) GetPhoneNumber() *common.PhoneNumber {
	if x, ok := x.GetIdentifierValue().(*SendPushAppLogsDeeplinkRequest_PhoneNumber); ok {
		return x.PhoneNumber
	}
	return nil
}

func (x *SendPushAppLogsDeeplinkRequest) GetEmailId() string {
	if x, ok := x.GetIdentifierValue().(*SendPushAppLogsDeeplinkRequest_EmailId); ok {
		return x.EmailId
	}
	return ""
}

type isSendPushAppLogsDeeplinkRequest_IdentifierValue interface {
	isSendPushAppLogsDeeplinkRequest_IdentifierValue()
}

type SendPushAppLogsDeeplinkRequest_ActorId struct {
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3,oneof"`
}

type SendPushAppLogsDeeplinkRequest_PhoneNumber struct {
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

type SendPushAppLogsDeeplinkRequest_EmailId struct {
	EmailId string `protobuf:"bytes,4,opt,name=email_id,json=emailId,proto3,oneof"`
}

func (*SendPushAppLogsDeeplinkRequest_ActorId) isSendPushAppLogsDeeplinkRequest_IdentifierValue() {}

func (*SendPushAppLogsDeeplinkRequest_PhoneNumber) isSendPushAppLogsDeeplinkRequest_IdentifierValue() {
}

func (*SendPushAppLogsDeeplinkRequest_EmailId) isSendPushAppLogsDeeplinkRequest_IdentifierValue() {}

var File_api_cx_app_log_service_proto protoreflect.FileDescriptor

var file_api_cx_app_log_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x02, 0x0a, 0x12, 0x50, 0x75,
	0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x6c, 0x6f, 0x67, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73,
	0x12, 0x3d, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x50, 0x75,
	0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6c, 0x6f, 0x67, 0x49, 0x64, 0x22, 0x6c, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x43, 0x52, 0x41, 0x53, 0x48, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f,
	0x50, 0x55, 0x53, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x10, 0x03, 0x22, 0xc2, 0x01, 0x0a, 0x0c, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x44, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67,
	0x2e, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x22, 0x3a, 0x0a, 0x13, 0x50, 0x75, 0x73,
	0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xc7, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x1d, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x43, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70,
	0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xb4, 0x02, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x45, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x67, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x67, 0x4b, 0x65, 0x79,
	0x12, 0x44, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f,
	0x67, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x6c, 0x6f, 0x67,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x7c, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x70,
	0x70, 0x4c, 0x6f, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x4c, 0x6f, 0x67,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0xb1, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x67, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x67, 0x4b, 0x65, 0x79, 0x12, 0x39, 0x0a,
	0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x8a, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x67, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x42, 0x0a, 0x12, 0x50, 0x75, 0x6c, 0x6c, 0x41, 0x70, 0x70,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8c, 0x01, 0x0a, 0x13, 0x50, 0x75,
	0x6c, 0x6c, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x12, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f,
	0x63, 0x6b, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x53, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b, 0x44, 0x65,
	0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x10, 0x73, 0x68, 0x65, 0x72, 0x6c, 0x6f, 0x63, 0x6b,
	0x44, 0x65, 0x65, 0x70, 0x4c, 0x69, 0x6e, 0x6b, 0x22, 0x53, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x46,
	0x6f, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x7d, 0x0a,
	0x13, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x45, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8e, 0x01, 0x0a,
	0x24, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x41, 0x70,
	0x70, 0x4c, 0x6f, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x6e, 0x0a,
	0x1f, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xf9, 0x01,
	0x0a, 0x1e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67,
	0x73, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x43, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x78, 0x2e, 0x61,
	0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x44, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x49, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xc0, 0x06, 0x0a, 0x06, 0x41, 0x70,
	0x70, 0x4c, 0x6f, 0x67, 0x12, 0x76, 0x0a, 0x0b, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c,
	0x6f, 0x67, 0x73, 0x12, 0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67,
	0x2e, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67,
	0x2e, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b,
	0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00,
	0xa8, 0x8d, 0xe8, 0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x28, 0x01, 0x12, 0x98, 0x01, 0x0a,
	0x17, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73,
	0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x2a, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70,
	0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70,
	0x70, 0x4c, 0x6f, 0x67, 0x73, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f,
	0x67, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67,
	0x73, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53,
	0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x00, 0xa8, 0x8d, 0xe8,
	0x6a, 0x00, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x26, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70,
	0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x4c, 0x6f, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45,
	0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a,
	0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x74, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c,
	0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c,
	0x6f, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7,
	0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x74, 0x0a, 0x0b,
	0x50, 0x75, 0x6c, 0x6c, 0x41, 0x70, 0x70, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x1e, 0x2e, 0x63, 0x78,
	0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x50, 0x75, 0x6c, 0x6c, 0x41, 0x70, 0x70,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x78,
	0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x50, 0x75, 0x6c, 0x6c, 0x41, 0x70, 0x70,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba,
	0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54,
	0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8,
	0x6a, 0x01, 0x12, 0xa7, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x01, 0xda, 0xc2, 0xd7,
	0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x01, 0x42, 0x4e, 0x0a, 0x25,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x61, 0x70,
	0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x6f, 0x67, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_app_log_service_proto_rawDescOnce sync.Once
	file_api_cx_app_log_service_proto_rawDescData = file_api_cx_app_log_service_proto_rawDesc
)

func file_api_cx_app_log_service_proto_rawDescGZIP() []byte {
	file_api_cx_app_log_service_proto_rawDescOnce.Do(func() {
		file_api_cx_app_log_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_app_log_service_proto_rawDescData)
	})
	return file_api_cx_app_log_service_proto_rawDescData
}

var file_api_cx_app_log_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_cx_app_log_service_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_api_cx_app_log_service_proto_goTypes = []interface{}{
	(PushAppLogsRequest_Source)(0),               // 0: cx.app_log.PushAppLogsRequest.Source
	(*PushAppLogsRequest)(nil),                   // 1: cx.app_log.PushAppLogsRequest
	(*AppLogDetail)(nil),                         // 2: cx.app_log.AppLogDetail
	(*PushAppLogsResponse)(nil),                  // 3: cx.app_log.PushAppLogsResponse
	(*GetExportedLogsListRequest)(nil),           // 4: cx.app_log.GetExportedLogsListRequest
	(*AppLogMeta)(nil),                           // 5: cx.app_log.AppLogMeta
	(*GetExportedLogsListResponse)(nil),          // 6: cx.app_log.GetExportedLogsListResponse
	(*GetLogsDataRequest)(nil),                   // 7: cx.app_log.GetLogsDataRequest
	(*GetLogsDataResponse)(nil),                  // 8: cx.app_log.GetLogsDataResponse
	(*PullAppLogsRequest)(nil),                   // 9: cx.app_log.PullAppLogsRequest
	(*PullAppLogsResponse)(nil),                  // 10: cx.app_log.PullAppLogsResponse
	(*GetExportedLogsListForAgentsRequest)(nil),  // 11: cx.app_log.GetExportedLogsListForAgentsRequest
	(*AppLogMetaForAgents)(nil),                  // 12: cx.app_log.AppLogMetaForAgents
	(*GetExportedLogsListForAgentsResponse)(nil), // 13: cx.app_log.GetExportedLogsListForAgentsResponse
	(*SendPushAppLogsDeeplinkResponse)(nil),      // 14: cx.app_log.SendPushAppLogsDeeplinkResponse
	(*SendPushAppLogsDeeplinkRequest)(nil),       // 15: cx.app_log.SendPushAppLogsDeeplinkRequest
	(*rpc.Status)(nil),                           // 16: rpc.Status
	(*cx.Header)(nil),                            // 17: cx.Header
	(*timestamppb.Timestamp)(nil),                // 18: google.protobuf.Timestamp
	(IdentifierType)(0),                          // 19: cx.app_log.IdentifierType
	(*common.PhoneNumber)(nil),                   // 20: api.typesv2.common.PhoneNumber
	(*cx.PageContextRequest)(nil),                // 21: cx.PageContextRequest
	(*cx.PageContextResponse)(nil),               // 22: cx.PageContextResponse
	(*customer_auth.SherlockDeepLink)(nil),       // 23: cx.customer_auth.SherlockDeepLink
}
var file_api_cx_app_log_service_proto_depIdxs = []int32{
	0,  // 0: cx.app_log.PushAppLogsRequest.source:type_name -> cx.app_log.PushAppLogsRequest.Source
	0,  // 1: cx.app_log.AppLogDetail.log_source:type_name -> cx.app_log.PushAppLogsRequest.Source
	16, // 2: cx.app_log.PushAppLogsResponse.status:type_name -> rpc.Status
	17, // 3: cx.app_log.GetExportedLogsListRequest.header:type_name -> cx.Header
	18, // 4: cx.app_log.GetExportedLogsListRequest.from_date:type_name -> google.protobuf.Timestamp
	18, // 5: cx.app_log.GetExportedLogsListRequest.to_date:type_name -> google.protobuf.Timestamp
	19, // 6: cx.app_log.GetExportedLogsListRequest.identifier_type:type_name -> cx.app_log.IdentifierType
	20, // 7: cx.app_log.AppLogMeta.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	18, // 8: cx.app_log.AppLogMeta.export_timestamp:type_name -> google.protobuf.Timestamp
	0,  // 9: cx.app_log.AppLogMeta.log_source:type_name -> cx.app_log.PushAppLogsRequest.Source
	16, // 10: cx.app_log.GetExportedLogsListResponse.status:type_name -> rpc.Status
	5,  // 11: cx.app_log.GetExportedLogsListResponse.app_log_meta:type_name -> cx.app_log.AppLogMeta
	17, // 12: cx.app_log.GetLogsDataRequest.header:type_name -> cx.Header
	21, // 13: cx.app_log.GetLogsDataRequest.page_context:type_name -> cx.PageContextRequest
	16, // 14: cx.app_log.GetLogsDataResponse.status:type_name -> rpc.Status
	22, // 15: cx.app_log.GetLogsDataResponse.page_context:type_name -> cx.PageContextResponse
	17, // 16: cx.app_log.PullAppLogsRequest.header:type_name -> cx.Header
	16, // 17: cx.app_log.PullAppLogsResponse.status:type_name -> rpc.Status
	23, // 18: cx.app_log.PullAppLogsResponse.sherlock_deep_link:type_name -> cx.customer_auth.SherlockDeepLink
	17, // 19: cx.app_log.GetExportedLogsListForAgentsRequest.header:type_name -> cx.Header
	18, // 20: cx.app_log.AppLogMetaForAgents.export_timestamp:type_name -> google.protobuf.Timestamp
	16, // 21: cx.app_log.GetExportedLogsListForAgentsResponse.status:type_name -> rpc.Status
	12, // 22: cx.app_log.GetExportedLogsListForAgentsResponse.app_log_meta:type_name -> cx.app_log.AppLogMetaForAgents
	16, // 23: cx.app_log.SendPushAppLogsDeeplinkResponse.status:type_name -> rpc.Status
	19, // 24: cx.app_log.SendPushAppLogsDeeplinkRequest.identifier_type:type_name -> cx.app_log.IdentifierType
	20, // 25: cx.app_log.SendPushAppLogsDeeplinkRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	1,  // 26: cx.app_log.AppLog.PushAppLogs:input_type -> cx.app_log.PushAppLogsRequest
	15, // 27: cx.app_log.AppLog.SendPushAppLogsDeeplink:input_type -> cx.app_log.SendPushAppLogsDeeplinkRequest
	4,  // 28: cx.app_log.AppLog.GetExportedLogsList:input_type -> cx.app_log.GetExportedLogsListRequest
	7,  // 29: cx.app_log.AppLog.GetLogsData:input_type -> cx.app_log.GetLogsDataRequest
	9,  // 30: cx.app_log.AppLog.PullAppLogs:input_type -> cx.app_log.PullAppLogsRequest
	11, // 31: cx.app_log.AppLog.GetExportedLogsListForAgents:input_type -> cx.app_log.GetExportedLogsListForAgentsRequest
	3,  // 32: cx.app_log.AppLog.PushAppLogs:output_type -> cx.app_log.PushAppLogsResponse
	14, // 33: cx.app_log.AppLog.SendPushAppLogsDeeplink:output_type -> cx.app_log.SendPushAppLogsDeeplinkResponse
	6,  // 34: cx.app_log.AppLog.GetExportedLogsList:output_type -> cx.app_log.GetExportedLogsListResponse
	8,  // 35: cx.app_log.AppLog.GetLogsData:output_type -> cx.app_log.GetLogsDataResponse
	10, // 36: cx.app_log.AppLog.PullAppLogs:output_type -> cx.app_log.PullAppLogsResponse
	13, // 37: cx.app_log.AppLog.GetExportedLogsListForAgents:output_type -> cx.app_log.GetExportedLogsListForAgentsResponse
	32, // [32:38] is the sub-list for method output_type
	26, // [26:32] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_cx_app_log_service_proto_init() }
func file_api_cx_app_log_service_proto_init() {
	if File_api_cx_app_log_service_proto != nil {
		return
	}
	file_api_cx_app_log_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_app_log_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushAppLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppLogDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushAppLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExportedLogsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppLogMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExportedLogsListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLogsDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLogsDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullAppLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullAppLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExportedLogsListForAgentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppLogMetaForAgents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExportedLogsListForAgentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPushAppLogsDeeplinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_app_log_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPushAppLogsDeeplinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_cx_app_log_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*SendPushAppLogsDeeplinkRequest_ActorId)(nil),
		(*SendPushAppLogsDeeplinkRequest_PhoneNumber)(nil),
		(*SendPushAppLogsDeeplinkRequest_EmailId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_app_log_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_app_log_service_proto_goTypes,
		DependencyIndexes: file_api_cx_app_log_service_proto_depIdxs,
		EnumInfos:         file_api_cx_app_log_service_proto_enumTypes,
		MessageInfos:      file_api_cx_app_log_service_proto_msgTypes,
	}.Build()
	File_api_cx_app_log_service_proto = out.File
	file_api_cx_app_log_service_proto_rawDesc = nil
	file_api_cx_app_log_service_proto_goTypes = nil
	file_api_cx_app_log_service_proto_depIdxs = nil
}
