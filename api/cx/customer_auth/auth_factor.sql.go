package customer_auth

import (
	"database/sql/driver"
	"fmt"
)

// Helper methods to translate Auth Factor Enum in Code to String in DB and vice versa

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x AuthFactor) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *AuthFactor) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt := AuthFactor_value[val]
	*x = AuthFactor(valInt)
	return nil
}
