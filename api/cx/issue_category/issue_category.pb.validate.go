// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/cx/issue_category/issue_category.proto

package issue_category

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on IssueCategory with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IssueCategory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IssueCategory with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IssueCategoryMultiError, or
// nil if none found.
func (m *IssueCategory) ValidateAll() error {
	return m.validate(true)
}

func (m *IssueCategory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ProductCategory

	// no validation rules for ProductCategoryDetails

	// no validation rules for SubCategory

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IssueCategoryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IssueCategoryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IssueCategoryValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IssueCategoryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IssueCategoryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IssueCategoryValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IssueCategoryMultiError(errors)
	}

	return nil
}

// IssueCategoryMultiError is an error wrapping multiple validation errors
// returned by IssueCategory.ValidateAll() if the designated constraints
// aren't met.
type IssueCategoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IssueCategoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IssueCategoryMultiError) AllErrors() []error { return m }

// IssueCategoryValidationError is the validation error returned by
// IssueCategory.Validate if the designated constraints aren't met.
type IssueCategoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IssueCategoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IssueCategoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IssueCategoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IssueCategoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IssueCategoryValidationError) ErrorName() string { return "IssueCategoryValidationError" }

// Error satisfies the builtin error interface
func (e IssueCategoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIssueCategory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IssueCategoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IssueCategoryValidationError{}

// Validate checks the field values on IssueCategoryFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IssueCategoryFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IssueCategoryFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IssueCategoryFilterMultiError, or nil if none found.
func (m *IssueCategoryFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *IssueCategoryFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIssueCategory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IssueCategoryFilterValidationError{
					field:  "IssueCategory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IssueCategoryFilterValidationError{
					field:  "IssueCategory",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIssueCategory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IssueCategoryFilterValidationError{
				field:  "IssueCategory",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilterDepth

	if len(errors) > 0 {
		return IssueCategoryFilterMultiError(errors)
	}

	return nil
}

// IssueCategoryFilterMultiError is an error wrapping multiple validation
// errors returned by IssueCategoryFilter.ValidateAll() if the designated
// constraints aren't met.
type IssueCategoryFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IssueCategoryFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IssueCategoryFilterMultiError) AllErrors() []error { return m }

// IssueCategoryFilterValidationError is the validation error returned by
// IssueCategoryFilter.Validate if the designated constraints aren't met.
type IssueCategoryFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IssueCategoryFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IssueCategoryFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IssueCategoryFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IssueCategoryFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IssueCategoryFilterValidationError) ErrorName() string {
	return "IssueCategoryFilterValidationError"
}

// Error satisfies the builtin error interface
func (e IssueCategoryFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIssueCategoryFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IssueCategoryFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IssueCategoryFilterValidationError{}

// Validate checks the field values on IssueCategoryTree with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IssueCategoryTree) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IssueCategoryTree with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IssueCategoryTreeMultiError, or nil if none found.
func (m *IssueCategoryTree) ValidateAll() error {
	return m.validate(true)
}

func (m *IssueCategoryTree) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetProductCategoryNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IssueCategoryTreeValidationError{
						field:  fmt.Sprintf("ProductCategoryNodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IssueCategoryTreeValidationError{
						field:  fmt.Sprintf("ProductCategoryNodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IssueCategoryTreeValidationError{
					field:  fmt.Sprintf("ProductCategoryNodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return IssueCategoryTreeMultiError(errors)
	}

	return nil
}

// IssueCategoryTreeMultiError is an error wrapping multiple validation errors
// returned by IssueCategoryTree.ValidateAll() if the designated constraints
// aren't met.
type IssueCategoryTreeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IssueCategoryTreeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IssueCategoryTreeMultiError) AllErrors() []error { return m }

// IssueCategoryTreeValidationError is the validation error returned by
// IssueCategoryTree.Validate if the designated constraints aren't met.
type IssueCategoryTreeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IssueCategoryTreeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IssueCategoryTreeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IssueCategoryTreeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IssueCategoryTreeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IssueCategoryTreeValidationError) ErrorName() string {
	return "IssueCategoryTreeValidationError"
}

// Error satisfies the builtin error interface
func (e IssueCategoryTreeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIssueCategoryTree.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IssueCategoryTreeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IssueCategoryTreeValidationError{}

// Validate checks the field values on IssueCategoryNode with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IssueCategoryNode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IssueCategoryNode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IssueCategoryNodeMultiError, or nil if none found.
func (m *IssueCategoryNode) ValidateAll() error {
	return m.validate(true)
}

func (m *IssueCategoryNode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	// no validation rules for Level

	for idx, item := range m.GetChildren() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IssueCategoryNodeValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IssueCategoryNodeValidationError{
						field:  fmt.Sprintf("Children[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IssueCategoryNodeValidationError{
					field:  fmt.Sprintf("Children[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return IssueCategoryNodeMultiError(errors)
	}

	return nil
}

// IssueCategoryNodeMultiError is an error wrapping multiple validation errors
// returned by IssueCategoryNode.ValidateAll() if the designated constraints
// aren't met.
type IssueCategoryNodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IssueCategoryNodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IssueCategoryNodeMultiError) AllErrors() []error { return m }

// IssueCategoryNodeValidationError is the validation error returned by
// IssueCategoryNode.Validate if the designated constraints aren't met.
type IssueCategoryNodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IssueCategoryNodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IssueCategoryNodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IssueCategoryNodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IssueCategoryNodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IssueCategoryNodeValidationError) ErrorName() string {
	return "IssueCategoryNodeValidationError"
}

// Error satisfies the builtin error interface
func (e IssueCategoryNodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIssueCategoryNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IssueCategoryNodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IssueCategoryNodeValidationError{}
