// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/federal/service.proto

package federal

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	cx "github.com/epifi/gamma/api/cx"
	freshdesk "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFederalEscalationTicketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// page size is fixed on backend for now just pass the page tokens
	PageContext *cx.PageContextRequest `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetFederalEscalationTicketsRequest) Reset() {
	*x = GetFederalEscalationTicketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFederalEscalationTicketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFederalEscalationTicketsRequest) ProtoMessage() {}

func (x *GetFederalEscalationTicketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFederalEscalationTicketsRequest.ProtoReflect.Descriptor instead.
func (*GetFederalEscalationTicketsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetFederalEscalationTicketsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetFederalEscalationTicketsRequest) GetPageContext() *cx.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetFederalEscalationTicketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return
	// NOT_FOUND if there are no tickets to return for current page
	// INTERNAL for freshdesk or server errors
	// OK for success
	Status      *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Tickets     []*freshdesk.Ticket     `protobuf:"bytes,2,rep,name=tickets,proto3" json:"tickets,omitempty"`
	PageContext *cx.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetFederalEscalationTicketsResponse) Reset() {
	*x = GetFederalEscalationTicketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFederalEscalationTicketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFederalEscalationTicketsResponse) ProtoMessage() {}

func (x *GetFederalEscalationTicketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFederalEscalationTicketsResponse.ProtoReflect.Descriptor instead.
func (*GetFederalEscalationTicketsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetFederalEscalationTicketsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFederalEscalationTicketsResponse) GetTickets() []*freshdesk.Ticket {
	if x != nil {
		return x.Tickets
	}
	return nil
}

func (x *GetFederalEscalationTicketsResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetTicketConversationsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Ticket id is mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetTicketConversationsRequest) Reset() {
	*x = GetTicketConversationsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketConversationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketConversationsRequest) ProtoMessage() {}

func (x *GetTicketConversationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketConversationsRequest.ProtoReflect.Descriptor instead.
func (*GetTicketConversationsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetTicketConversationsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetTicketConversationsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return
	// NOT_FOUND if ticket not found for given id
	// INTERNAL for freshdesk or server errors
	// OK for success
	Status       *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PrivateNotes []string    `protobuf:"bytes,2,rep,name=private_notes,json=privateNotes,proto3" json:"private_notes,omitempty"`
	PublicNotes  []string    `protobuf:"bytes,3,rep,name=public_notes,json=publicNotes,proto3" json:"public_notes,omitempty"`
}

func (x *GetTicketConversationsResponse) Reset() {
	*x = GetTicketConversationsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketConversationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketConversationsResponse) ProtoMessage() {}

func (x *GetTicketConversationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketConversationsResponse.ProtoReflect.Descriptor instead.
func (*GetTicketConversationsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetTicketConversationsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTicketConversationsResponse) GetPrivateNotes() []string {
	if x != nil {
		return x.PrivateNotes
	}
	return nil
}

func (x *GetTicketConversationsResponse) GetPublicNotes() []string {
	if x != nil {
		return x.PublicNotes
	}
	return nil
}

type GetAllTicketGroupsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetAllTicketGroupsRequest) Reset() {
	*x = GetAllTicketGroupsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllTicketGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllTicketGroupsRequest) ProtoMessage() {}

func (x *GetAllTicketGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllTicketGroupsRequest.ProtoReflect.Descriptor instead.
func (*GetAllTicketGroupsRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllTicketGroupsRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetAllTicketGroupsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return
	// INTERNAL server errors
	// OK for success
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of groups available
	Groups []string `protobuf:"bytes,2,rep,name=groups,proto3" json:"groups,omitempty"`
}

func (x *GetAllTicketGroupsResponse) Reset() {
	*x = GetAllTicketGroupsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllTicketGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllTicketGroupsResponse) ProtoMessage() {}

func (x *GetAllTicketGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllTicketGroupsResponse.ProtoReflect.Descriptor instead.
func (*GetAllTicketGroupsResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAllTicketGroupsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllTicketGroupsResponse) GetGroups() []string {
	if x != nil {
		return x.Groups
	}
	return nil
}

type UpdateTicketGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ticket id is mandatory in header
	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// group to which ticket needs to be assigned to
	// should be one of the values returned by GetAllTicketGroups
	NewGroup string `protobuf:"bytes,2,opt,name=new_group,json=newGroup,proto3" json:"new_group,omitempty"`
	// note to be addded to ticket
	// this will be used to add any comments/reason federal agents might have while updating the ticket
	Note string `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
}

func (x *UpdateTicketGroupRequest) Reset() {
	*x = UpdateTicketGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketGroupRequest) ProtoMessage() {}

func (x *UpdateTicketGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketGroupRequest.ProtoReflect.Descriptor instead.
func (*UpdateTicketGroupRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateTicketGroupRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *UpdateTicketGroupRequest) GetNewGroup() string {
	if x != nil {
		return x.NewGroup
	}
	return ""
}

func (x *UpdateTicketGroupRequest) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

type UpdateTicketGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// will return
	// INTERNAL for freshdesk or server errors
	// OK for success
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateTicketGroupResponse) Reset() {
	*x = UpdateTicketGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_federal_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTicketGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTicketGroupResponse) ProtoMessage() {}

func (x *UpdateTicketGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_federal_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTicketGroupResponse.ProtoReflect.Descriptor instead.
func (*UpdateTicketGroupResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_federal_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateTicketGroupResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_cx_federal_service_proto protoreflect.FileDescriptor

var file_api_cx_federal_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70,
	0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x78, 0x2f, 0x66, 0x72, 0x65, 0x73, 0x68, 0x64, 0x65,
	0x73, 0x6b, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x39, 0x0a,
	0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xb6, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74,
	0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x64, 0x65, 0x73, 0x6b, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x07, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x78,
	0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x22, 0x4d, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x8d, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4e, 0x6f, 0x74, 0x65, 0x73,
	0x22, 0x49, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e,
	0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x59, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x79, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x77, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74,
	0x65, 0x22, 0x40, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x32, 0xdd, 0x04, 0x0a, 0x07, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x12,
	0xa4, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x45, 0x73,
	0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12,
	0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45,
	0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a,
	0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x95, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x29, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63,
	0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda,
	0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0,
	0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x89,
	0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63,
	0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b,
	0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01,
	0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8,
	0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49,
	0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92,
	0xe8, 0x6a, 0x00, 0x42, 0x4e, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x78, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x5a, 0x25, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_federal_service_proto_rawDescOnce sync.Once
	file_api_cx_federal_service_proto_rawDescData = file_api_cx_federal_service_proto_rawDesc
)

func file_api_cx_federal_service_proto_rawDescGZIP() []byte {
	file_api_cx_federal_service_proto_rawDescOnce.Do(func() {
		file_api_cx_federal_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_federal_service_proto_rawDescData)
	})
	return file_api_cx_federal_service_proto_rawDescData
}

var file_api_cx_federal_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_cx_federal_service_proto_goTypes = []interface{}{
	(*GetFederalEscalationTicketsRequest)(nil),  // 0: cx.federal.GetFederalEscalationTicketsRequest
	(*GetFederalEscalationTicketsResponse)(nil), // 1: cx.federal.GetFederalEscalationTicketsResponse
	(*GetTicketConversationsRequest)(nil),       // 2: cx.federal.GetTicketConversationsRequest
	(*GetTicketConversationsResponse)(nil),      // 3: cx.federal.GetTicketConversationsResponse
	(*GetAllTicketGroupsRequest)(nil),           // 4: cx.federal.GetAllTicketGroupsRequest
	(*GetAllTicketGroupsResponse)(nil),          // 5: cx.federal.GetAllTicketGroupsResponse
	(*UpdateTicketGroupRequest)(nil),            // 6: cx.federal.UpdateTicketGroupRequest
	(*UpdateTicketGroupResponse)(nil),           // 7: cx.federal.UpdateTicketGroupResponse
	(*cx.Header)(nil),                           // 8: cx.Header
	(*cx.PageContextRequest)(nil),               // 9: cx.PageContextRequest
	(*rpc.Status)(nil),                          // 10: rpc.Status
	(*freshdesk.Ticket)(nil),                    // 11: cx.freshdesk.Ticket
	(*cx.PageContextResponse)(nil),              // 12: cx.PageContextResponse
}
var file_api_cx_federal_service_proto_depIdxs = []int32{
	8,  // 0: cx.federal.GetFederalEscalationTicketsRequest.header:type_name -> cx.Header
	9,  // 1: cx.federal.GetFederalEscalationTicketsRequest.page_context:type_name -> cx.PageContextRequest
	10, // 2: cx.federal.GetFederalEscalationTicketsResponse.status:type_name -> rpc.Status
	11, // 3: cx.federal.GetFederalEscalationTicketsResponse.tickets:type_name -> cx.freshdesk.Ticket
	12, // 4: cx.federal.GetFederalEscalationTicketsResponse.page_context:type_name -> cx.PageContextResponse
	8,  // 5: cx.federal.GetTicketConversationsRequest.header:type_name -> cx.Header
	10, // 6: cx.federal.GetTicketConversationsResponse.status:type_name -> rpc.Status
	8,  // 7: cx.federal.GetAllTicketGroupsRequest.header:type_name -> cx.Header
	10, // 8: cx.federal.GetAllTicketGroupsResponse.status:type_name -> rpc.Status
	8,  // 9: cx.federal.UpdateTicketGroupRequest.header:type_name -> cx.Header
	10, // 10: cx.federal.UpdateTicketGroupResponse.status:type_name -> rpc.Status
	0,  // 11: cx.federal.Federal.GetFederalEscalationTickets:input_type -> cx.federal.GetFederalEscalationTicketsRequest
	2,  // 12: cx.federal.Federal.GetTicketConversations:input_type -> cx.federal.GetTicketConversationsRequest
	4,  // 13: cx.federal.Federal.GetAllTicketGroups:input_type -> cx.federal.GetAllTicketGroupsRequest
	6,  // 14: cx.federal.Federal.UpdateTicketGroup:input_type -> cx.federal.UpdateTicketGroupRequest
	1,  // 15: cx.federal.Federal.GetFederalEscalationTickets:output_type -> cx.federal.GetFederalEscalationTicketsResponse
	3,  // 16: cx.federal.Federal.GetTicketConversations:output_type -> cx.federal.GetTicketConversationsResponse
	5,  // 17: cx.federal.Federal.GetAllTicketGroups:output_type -> cx.federal.GetAllTicketGroupsResponse
	7,  // 18: cx.federal.Federal.UpdateTicketGroup:output_type -> cx.federal.UpdateTicketGroupResponse
	15, // [15:19] is the sub-list for method output_type
	11, // [11:15] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_cx_federal_service_proto_init() }
func file_api_cx_federal_service_proto_init() {
	if File_api_cx_federal_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_cx_federal_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFederalEscalationTicketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_federal_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFederalEscalationTicketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_federal_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketConversationsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_federal_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketConversationsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_federal_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllTicketGroupsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_federal_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllTicketGroupsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_federal_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_federal_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTicketGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_federal_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_federal_service_proto_goTypes,
		DependencyIndexes: file_api_cx_federal_service_proto_depIdxs,
		MessageInfos:      file_api_cx_federal_service_proto_msgTypes,
	}.Build()
	File_api_cx_federal_service_proto = out.File
	file_api_cx_federal_service_proto_rawDesc = nil
	file_api_cx_federal_service_proto_goTypes = nil
	file_api_cx_federal_service_proto_depIdxs = nil
}
