// Code generated by MockGen. DO NOT EDIT.
// Source: api/cx/admin_actions/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	admin_actions "github.com/epifi/gamma/api/cx/admin_actions"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAdminActonsClient is a mock of AdminActonsClient interface.
type MockAdminActonsClient struct {
	ctrl     *gomock.Controller
	recorder *MockAdminActonsClientMockRecorder
}

// MockAdminActonsClientMockRecorder is the mock recorder for MockAdminActonsClient.
type MockAdminActonsClientMockRecorder struct {
	mock *MockAdminActonsClient
}

// NewMockAdminActonsClient creates a new mock instance.
func NewMockAdminActonsClient(ctrl *gomock.Controller) *MockAdminActonsClient {
	mock := &MockAdminActonsClient{ctrl: ctrl}
	mock.recorder = &MockAdminActonsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAdminActonsClient) EXPECT() *MockAdminActonsClientMockRecorder {
	return m.recorder
}

// ExecuteAction mocks base method.
func (m *MockAdminActonsClient) ExecuteAction(ctx context.Context, in *admin_actions.ExecuteActionRequest, opts ...grpc.CallOption) (*admin_actions.ExecuteActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ExecuteAction", varargs...)
	ret0, _ := ret[0].(*admin_actions.ExecuteActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteAction indicates an expected call of ExecuteAction.
func (mr *MockAdminActonsClientMockRecorder) ExecuteAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteAction", reflect.TypeOf((*MockAdminActonsClient)(nil).ExecuteAction), varargs...)
}

// GetAvailableAdminActions mocks base method.
func (m *MockAdminActonsClient) GetAvailableAdminActions(ctx context.Context, in *admin_actions.GetAvailableAdminActionsRequest, opts ...grpc.CallOption) (*admin_actions.GetAvailableAdminActionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAvailableAdminActions", varargs...)
	ret0, _ := ret[0].(*admin_actions.GetAvailableAdminActionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableAdminActions indicates an expected call of GetAvailableAdminActions.
func (mr *MockAdminActonsClientMockRecorder) GetAvailableAdminActions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableAdminActions", reflect.TypeOf((*MockAdminActonsClient)(nil).GetAvailableAdminActions), varargs...)
}

// GetParameterListForAdminAction mocks base method.
func (m *MockAdminActonsClient) GetParameterListForAdminAction(ctx context.Context, in *admin_actions.GetParameterListForAdminActionRequest, opts ...grpc.CallOption) (*admin_actions.GetParameterListForAdminActionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterListForAdminAction", varargs...)
	ret0, _ := ret[0].(*admin_actions.GetParameterListForAdminActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterListForAdminAction indicates an expected call of GetParameterListForAdminAction.
func (mr *MockAdminActonsClientMockRecorder) GetParameterListForAdminAction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterListForAdminAction", reflect.TypeOf((*MockAdminActonsClient)(nil).GetParameterListForAdminAction), varargs...)
}

// MockAdminActonsServer is a mock of AdminActonsServer interface.
type MockAdminActonsServer struct {
	ctrl     *gomock.Controller
	recorder *MockAdminActonsServerMockRecorder
}

// MockAdminActonsServerMockRecorder is the mock recorder for MockAdminActonsServer.
type MockAdminActonsServerMockRecorder struct {
	mock *MockAdminActonsServer
}

// NewMockAdminActonsServer creates a new mock instance.
func NewMockAdminActonsServer(ctrl *gomock.Controller) *MockAdminActonsServer {
	mock := &MockAdminActonsServer{ctrl: ctrl}
	mock.recorder = &MockAdminActonsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAdminActonsServer) EXPECT() *MockAdminActonsServerMockRecorder {
	return m.recorder
}

// ExecuteAction mocks base method.
func (m *MockAdminActonsServer) ExecuteAction(arg0 context.Context, arg1 *admin_actions.ExecuteActionRequest) (*admin_actions.ExecuteActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteAction", arg0, arg1)
	ret0, _ := ret[0].(*admin_actions.ExecuteActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteAction indicates an expected call of ExecuteAction.
func (mr *MockAdminActonsServerMockRecorder) ExecuteAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteAction", reflect.TypeOf((*MockAdminActonsServer)(nil).ExecuteAction), arg0, arg1)
}

// GetAvailableAdminActions mocks base method.
func (m *MockAdminActonsServer) GetAvailableAdminActions(arg0 context.Context, arg1 *admin_actions.GetAvailableAdminActionsRequest) (*admin_actions.GetAvailableAdminActionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableAdminActions", arg0, arg1)
	ret0, _ := ret[0].(*admin_actions.GetAvailableAdminActionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableAdminActions indicates an expected call of GetAvailableAdminActions.
func (mr *MockAdminActonsServerMockRecorder) GetAvailableAdminActions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableAdminActions", reflect.TypeOf((*MockAdminActonsServer)(nil).GetAvailableAdminActions), arg0, arg1)
}

// GetParameterListForAdminAction mocks base method.
func (m *MockAdminActonsServer) GetParameterListForAdminAction(arg0 context.Context, arg1 *admin_actions.GetParameterListForAdminActionRequest) (*admin_actions.GetParameterListForAdminActionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterListForAdminAction", arg0, arg1)
	ret0, _ := ret[0].(*admin_actions.GetParameterListForAdminActionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterListForAdminAction indicates an expected call of GetParameterListForAdminAction.
func (mr *MockAdminActonsServerMockRecorder) GetParameterListForAdminAction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterListForAdminAction", reflect.TypeOf((*MockAdminActonsServer)(nil).GetParameterListForAdminAction), arg0, arg1)
}

// MockUnsafeAdminActonsServer is a mock of UnsafeAdminActonsServer interface.
type MockUnsafeAdminActonsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAdminActonsServerMockRecorder
}

// MockUnsafeAdminActonsServerMockRecorder is the mock recorder for MockUnsafeAdminActonsServer.
type MockUnsafeAdminActonsServerMockRecorder struct {
	mock *MockUnsafeAdminActonsServer
}

// NewMockUnsafeAdminActonsServer creates a new mock instance.
func NewMockUnsafeAdminActonsServer(ctrl *gomock.Controller) *MockUnsafeAdminActonsServer {
	mock := &MockUnsafeAdminActonsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAdminActonsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAdminActonsServer) EXPECT() *MockUnsafeAdminActonsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAdminActonsServer mocks base method.
func (m *MockUnsafeAdminActonsServer) mustEmbedUnimplementedAdminActonsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAdminActonsServer")
}

// mustEmbedUnimplementedAdminActonsServer indicates an expected call of mustEmbedUnimplementedAdminActonsServer.
func (mr *MockUnsafeAdminActonsServerMockRecorder) mustEmbedUnimplementedAdminActonsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAdminActonsServer", reflect.TypeOf((*MockUnsafeAdminActonsServer)(nil).mustEmbedUnimplementedAdminActonsServer))
}
