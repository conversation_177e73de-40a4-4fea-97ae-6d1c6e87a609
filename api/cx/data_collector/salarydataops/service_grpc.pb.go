// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/cx/data_collector/salarydataops/service.proto

package salarydataops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SalaryDataOps_UpdateUsersEmploymentDetails_FullMethodName                     = "/cx.data_collector.salarydataops.SalaryDataOps/UpdateUsersEmploymentDetails"
	SalaryDataOps_SearchUsingGSTINOrName_FullMethodName                           = "/cx.data_collector.salarydataops.SalaryDataOps/SearchUsingGSTINOrName"
	SalaryDataOps_GetSalaryTxnVerificationRequestsByStatus_FullMethodName         = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsByStatus"
	SalaryDataOps_GetDetailedSalaryTxnVerificationRequestsByFilter_FullMethodName = "/cx.data_collector.salarydataops.SalaryDataOps/GetDetailedSalaryTxnVerificationRequestsByFilter"
	SalaryDataOps_UpdateVerificationRequestStatus_FullMethodName                  = "/cx.data_collector.salarydataops.SalaryDataOps/UpdateVerificationRequestStatus"
	SalaryDataOps_RaiseAndVerifySalaryTxnVerificationReq_FullMethodName           = "/cx.data_collector.salarydataops.SalaryDataOps/RaiseAndVerifySalaryTxnVerificationReq"
	SalaryDataOps_GetUserDetailsNeededForVerification_FullMethodName              = "/cx.data_collector.salarydataops.SalaryDataOps/GetUserDetailsNeededForVerification"
	SalaryDataOps_GetSalaryAccountDetailsForAgents_FullMethodName                 = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryAccountDetailsForAgents"
	SalaryDataOps_GetSalaryAccountDetailsForSalaryDataOps_FullMethodName          = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryAccountDetailsForSalaryDataOps"
	SalaryDataOps_GetSalaryTxnVerificationRequestsForUser_FullMethodName          = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsForUser"
	SalaryDataOps_GetSalaryTxnVerificationRequestsForSalaryDataOps_FullMethodName = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsForSalaryDataOps"
	SalaryDataOps_GetPossibleSalaryTxnsForActor_FullMethodName                    = "/cx.data_collector.salarydataops.SalaryDataOps/GetPossibleSalaryTxnsForActor"
	SalaryDataOps_GetUserDetailsNeededForRaisingSalaryVerification_FullMethodName = "/cx.data_collector.salarydataops.SalaryDataOps/GetUserDetailsNeededForRaisingSalaryVerification"
	SalaryDataOps_GetPossibleActorsForSalaryVerification_FullMethodName           = "/cx.data_collector.salarydataops.SalaryDataOps/GetPossibleActorsForSalaryVerification"
	SalaryDataOps_UpdateSalaryVerificationEligibilityStatus_FullMethodName        = "/cx.data_collector.salarydataops.SalaryDataOps/UpdateSalaryVerificationEligibilityStatus"
	SalaryDataOps_GetHealthInsuranceUserPolicyDetailsForAgents_FullMethodName     = "/cx.data_collector.salarydataops.SalaryDataOps/GetHealthInsuranceUserPolicyDetailsForAgents"
	SalaryDataOps_GetHealthInsuranceGeneralPolicyInfo_FullMethodName              = "/cx.data_collector.salarydataops.SalaryDataOps/GetHealthInsuranceGeneralPolicyInfo"
	SalaryDataOps_GetSalaryProgramActivationStatusAtTimeForAgents_FullMethodName  = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryProgramActivationStatusAtTimeForAgents"
	SalaryDataOps_GetSalaryLiteMandateDetailsForAgents_FullMethodName             = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryLiteMandateDetailsForAgents"
	SalaryDataOps_GetSalaryLiteMandateExecutionDetailsForAgents_FullMethodName    = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryLiteMandateExecutionDetailsForAgents"
	SalaryDataOps_GetSalaryTxnVerificationRequestsCount_FullMethodName            = "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsCount"
)

// SalaryDataOpsClient is the client API for SalaryDataOps service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SalaryDataOpsClient interface {
	// UpdateUsersEmploymentDetails rpc is used to update a users employer
	UpdateUsersEmploymentDetails(ctx context.Context, in *UpdateUsersEmploymentDetailsRequest, opts ...grpc.CallOption) (*UpdateUsersEmploymentDetailsResponse, error)
	// SearchUsingGSTINOrName rpc gets employer suggestions by matching an input string with
	// employer name or gstin number
	SearchUsingGSTINOrName(ctx context.Context, in *SearchUsingGSTINOrNameRequest, opts ...grpc.CallOption) (*SearchUsingGSTINOrNameResponse, error)
	// GetSalaryTxnVerificationRequestsByStatus rpc fetches all the salary transactions that were raised to be manually
	// reviewed by Data Ops team. Details can be fetched by using the status of the salary transaction verification request.
	GetSalaryTxnVerificationRequestsByStatus(ctx context.Context, in *GetSalaryTxnVerificationRequestsByStatusRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsByStatusResponse, error)
	// GetDetailedSalaryTxnVerificationRequestsByFilter rpc is used to get detailed info such as actor info or employer info
	// along with their salary transaction verification requests
	GetDetailedSalaryTxnVerificationRequestsByFilter(ctx context.Context, in *GetDetailedSalaryTxnVerificationRequestsByFilterRequest, opts ...grpc.CallOption) (*GetDetailedSalaryTxnVerificationRequestsByFilterResponse, error)
	// UpdateVerificationRequestStatus rpc updates the status of the salary transaction verification request.
	// Data Ops will send a combination of inputs that will mark a salary transaction verification request
	// as VERIFIED/REJECTED/ESCALATE_TO_ENGG_TEAM
	UpdateVerificationRequestStatus(ctx context.Context, in *UpdateVerificationRequestStatusRequest, opts ...grpc.CallOption) (*UpdateVerificationRequestStatusResponse, error)
	// RaiseAndVerifySalaryTxnVerificationReq raise salary txn verification request and then verify (as of now it mark the request as VERIFIED) the request
	RaiseAndVerifySalaryTxnVerificationReq(ctx context.Context, in *RaiseAndVerifySalaryTxnVerificationReqRequest, opts ...grpc.CallOption) (*RaiseAndVerifySalaryTxnVerificationReqResponse, error)
	// GetUserDetailsNeededForVerification rpc is used to get relevant employment information
	// needed to verify a salary transaction
	GetUserDetailsNeededForVerification(ctx context.Context, in *GetUserDetailsNeededForVerificationRequest, opts ...grpc.CallOption) (*GetUserDetailsNeededForVerificationResponse, error)
	// GetSalaryAccountDetailsForAgents fetches all details relevant to
	// salary program for a user using actor and user passed in header
	GetSalaryAccountDetailsForAgents(ctx context.Context, in *GetSalaryAccountDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryAccountDetailsForAgentsResponse, error)
	// GetSalaryAccountDetailsForSalaryDataOps fetches all details relevant to salary program
	// using phone number/email id
	GetSalaryAccountDetailsForSalaryDataOps(ctx context.Context, in *GetSalaryAccountDetailsForSalaryDataOpsRequest, opts ...grpc.CallOption) (*GetSalaryAccountDetailsForSalaryDataOpsResponse, error)
	// GetSalaryTxnVerificationRequestsForUser rpc fetches all the salary transactions that were raised for a user.
	// This is for cx agents to know about salary txn verification status for a user who reaches out.
	// Actor details are expected to be passed in the header
	GetSalaryTxnVerificationRequestsForUser(ctx context.Context, in *GetSalaryTxnVerificationRequestsForUserRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsForUserResponse, error)
	// GetSalaryTxnVerificationRequestsForSalaryDataOps rpc fetches the salary transactions details for salary transaction verification request of a user
	GetSalaryTxnVerificationRequestsForSalaryDataOps(ctx context.Context, in *GetSalaryTxnVerificationRequestsForSalaryDataOpsRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse, error)
	// GetPossibleSalaryTxnsForActor rpc fetches all the transactions which can be possible salary transactions for actor.
	// For now it fetches txns after 25 days and before 60 days of last-verified-salary-txn time.
	GetPossibleSalaryTxnsForActor(ctx context.Context, in *GetPossibleSalaryTxnsForActorRequest, opts ...grpc.CallOption) (*GetPossibleSalaryTxnsForActorResponse, error)
	// GetUserDetailsNeededForRaisingSalaryVerification rpc fetches the user details required for raising salary verification from sherlock.
	// It fetches user details, user's employment info and last verified salary txn details.
	GetUserDetailsNeededForRaisingSalaryVerification(ctx context.Context, in *GetUserDetailsNeededForRaisingSalaryVerificationRequest, opts ...grpc.CallOption) (*GetUserDetailsNeededForRaisingSalaryVerificationResponse, error)
	// GetPossibleActorsForSalaryVerification rpc fetches the list of possible actors for whom salary verification request can be raised.
	GetPossibleActorsForSalaryVerification(ctx context.Context, in *GetPossibleActorsForSalaryVerificationRequest, opts ...grpc.CallOption) (*GetPossibleActorsForSalaryVerificationResponse, error)
	// UpdateSalaryVerificationEligibilityStatus rpc updates the status of the user who is into consideration for raising salary verification request.
	// salary txn verification request is raised on updating the status to COMPLETE.
	UpdateSalaryVerificationEligibilityStatus(ctx context.Context, in *UpdateSalaryVerificationEligibilityStatusRequest, opts ...grpc.CallOption) (*UpdateSalaryVerificationEligibilityStatusResponse, error)
	// GetHealthInsuranceUserPolicyDetails fetches all details relevant to health insurance policy details specific to the user
	// eg. policy_number, activated_on_date
	// using phone number/email id/actor id
	GetHealthInsuranceUserPolicyDetailsForAgents(ctx context.Context, in *GetHealthInsuranceUserPolicyDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetHealthInsuranceUserPolicyDetailsForAgentsResponse, error)
	// GetHealthInsuranceGeneralPolicyInfo fetches all details relevant to health insurance policy
	// eg. insurer contact details, riskcovry contact details etc
	GetHealthInsuranceGeneralPolicyInfo(ctx context.Context, in *GetHealthInsuranceGeneralPolicyInfoRequest, opts ...grpc.CallOption) (*GetHealthInsuranceGeneralPolicyInfoResponse, error)
	// GetSalaryActivationStatusAtTime checks if the salary program was active for user at the given time
	// using phone number/email id
	GetSalaryProgramActivationStatusAtTimeForAgents(ctx context.Context, in *GetSalaryProgramActivationStatusAtTimeForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryProgramActivationStatusAtTimeForAgentsResponse, error)
	// GetSalaryLiteMandateDetailsForAgents fetches salary lite mandate details
	// for a user using actor and user passed in header
	GetSalaryLiteMandateDetailsForAgents(ctx context.Context, in *GetSalaryLiteMandateDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryLiteMandateDetailsForAgentsResponse, error)
	// GetSalaryLiteMandateExecutionDetailsForAgents fetches salary lite mandate execution (monthly transfer) details
	// for a user using actor and user passed in header
	GetSalaryLiteMandateExecutionDetailsForAgents(ctx context.Context, in *GetSalaryLiteMandateExecutionDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryLiteMandateExecutionDetailsForAgentsResponse, error)
	// This rpc is used to get the number of salary transaction verification requests, by data ops team
	GetSalaryTxnVerificationRequestsCount(ctx context.Context, in *GetSalaryTxnVerificationRequestsCountRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsCountResponse, error)
}

type salaryDataOpsClient struct {
	cc grpc.ClientConnInterface
}

func NewSalaryDataOpsClient(cc grpc.ClientConnInterface) SalaryDataOpsClient {
	return &salaryDataOpsClient{cc}
}

func (c *salaryDataOpsClient) UpdateUsersEmploymentDetails(ctx context.Context, in *UpdateUsersEmploymentDetailsRequest, opts ...grpc.CallOption) (*UpdateUsersEmploymentDetailsResponse, error) {
	out := new(UpdateUsersEmploymentDetailsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_UpdateUsersEmploymentDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) SearchUsingGSTINOrName(ctx context.Context, in *SearchUsingGSTINOrNameRequest, opts ...grpc.CallOption) (*SearchUsingGSTINOrNameResponse, error) {
	out := new(SearchUsingGSTINOrNameResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_SearchUsingGSTINOrName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryTxnVerificationRequestsByStatus(ctx context.Context, in *GetSalaryTxnVerificationRequestsByStatusRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsByStatusResponse, error) {
	out := new(GetSalaryTxnVerificationRequestsByStatusResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryTxnVerificationRequestsByStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetDetailedSalaryTxnVerificationRequestsByFilter(ctx context.Context, in *GetDetailedSalaryTxnVerificationRequestsByFilterRequest, opts ...grpc.CallOption) (*GetDetailedSalaryTxnVerificationRequestsByFilterResponse, error) {
	out := new(GetDetailedSalaryTxnVerificationRequestsByFilterResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetDetailedSalaryTxnVerificationRequestsByFilter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) UpdateVerificationRequestStatus(ctx context.Context, in *UpdateVerificationRequestStatusRequest, opts ...grpc.CallOption) (*UpdateVerificationRequestStatusResponse, error) {
	out := new(UpdateVerificationRequestStatusResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_UpdateVerificationRequestStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) RaiseAndVerifySalaryTxnVerificationReq(ctx context.Context, in *RaiseAndVerifySalaryTxnVerificationReqRequest, opts ...grpc.CallOption) (*RaiseAndVerifySalaryTxnVerificationReqResponse, error) {
	out := new(RaiseAndVerifySalaryTxnVerificationReqResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_RaiseAndVerifySalaryTxnVerificationReq_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetUserDetailsNeededForVerification(ctx context.Context, in *GetUserDetailsNeededForVerificationRequest, opts ...grpc.CallOption) (*GetUserDetailsNeededForVerificationResponse, error) {
	out := new(GetUserDetailsNeededForVerificationResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetUserDetailsNeededForVerification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryAccountDetailsForAgents(ctx context.Context, in *GetSalaryAccountDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryAccountDetailsForAgentsResponse, error) {
	out := new(GetSalaryAccountDetailsForAgentsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryAccountDetailsForAgents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryAccountDetailsForSalaryDataOps(ctx context.Context, in *GetSalaryAccountDetailsForSalaryDataOpsRequest, opts ...grpc.CallOption) (*GetSalaryAccountDetailsForSalaryDataOpsResponse, error) {
	out := new(GetSalaryAccountDetailsForSalaryDataOpsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryAccountDetailsForSalaryDataOps_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryTxnVerificationRequestsForUser(ctx context.Context, in *GetSalaryTxnVerificationRequestsForUserRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsForUserResponse, error) {
	out := new(GetSalaryTxnVerificationRequestsForUserResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryTxnVerificationRequestsForUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryTxnVerificationRequestsForSalaryDataOps(ctx context.Context, in *GetSalaryTxnVerificationRequestsForSalaryDataOpsRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse, error) {
	out := new(GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryTxnVerificationRequestsForSalaryDataOps_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetPossibleSalaryTxnsForActor(ctx context.Context, in *GetPossibleSalaryTxnsForActorRequest, opts ...grpc.CallOption) (*GetPossibleSalaryTxnsForActorResponse, error) {
	out := new(GetPossibleSalaryTxnsForActorResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetPossibleSalaryTxnsForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetUserDetailsNeededForRaisingSalaryVerification(ctx context.Context, in *GetUserDetailsNeededForRaisingSalaryVerificationRequest, opts ...grpc.CallOption) (*GetUserDetailsNeededForRaisingSalaryVerificationResponse, error) {
	out := new(GetUserDetailsNeededForRaisingSalaryVerificationResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetUserDetailsNeededForRaisingSalaryVerification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetPossibleActorsForSalaryVerification(ctx context.Context, in *GetPossibleActorsForSalaryVerificationRequest, opts ...grpc.CallOption) (*GetPossibleActorsForSalaryVerificationResponse, error) {
	out := new(GetPossibleActorsForSalaryVerificationResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetPossibleActorsForSalaryVerification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) UpdateSalaryVerificationEligibilityStatus(ctx context.Context, in *UpdateSalaryVerificationEligibilityStatusRequest, opts ...grpc.CallOption) (*UpdateSalaryVerificationEligibilityStatusResponse, error) {
	out := new(UpdateSalaryVerificationEligibilityStatusResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_UpdateSalaryVerificationEligibilityStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetHealthInsuranceUserPolicyDetailsForAgents(ctx context.Context, in *GetHealthInsuranceUserPolicyDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetHealthInsuranceUserPolicyDetailsForAgentsResponse, error) {
	out := new(GetHealthInsuranceUserPolicyDetailsForAgentsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetHealthInsuranceUserPolicyDetailsForAgents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetHealthInsuranceGeneralPolicyInfo(ctx context.Context, in *GetHealthInsuranceGeneralPolicyInfoRequest, opts ...grpc.CallOption) (*GetHealthInsuranceGeneralPolicyInfoResponse, error) {
	out := new(GetHealthInsuranceGeneralPolicyInfoResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetHealthInsuranceGeneralPolicyInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryProgramActivationStatusAtTimeForAgents(ctx context.Context, in *GetSalaryProgramActivationStatusAtTimeForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryProgramActivationStatusAtTimeForAgentsResponse, error) {
	out := new(GetSalaryProgramActivationStatusAtTimeForAgentsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryProgramActivationStatusAtTimeForAgents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryLiteMandateDetailsForAgents(ctx context.Context, in *GetSalaryLiteMandateDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryLiteMandateDetailsForAgentsResponse, error) {
	out := new(GetSalaryLiteMandateDetailsForAgentsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryLiteMandateDetailsForAgents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryLiteMandateExecutionDetailsForAgents(ctx context.Context, in *GetSalaryLiteMandateExecutionDetailsForAgentsRequest, opts ...grpc.CallOption) (*GetSalaryLiteMandateExecutionDetailsForAgentsResponse, error) {
	out := new(GetSalaryLiteMandateExecutionDetailsForAgentsResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryLiteMandateExecutionDetailsForAgents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salaryDataOpsClient) GetSalaryTxnVerificationRequestsCount(ctx context.Context, in *GetSalaryTxnVerificationRequestsCountRequest, opts ...grpc.CallOption) (*GetSalaryTxnVerificationRequestsCountResponse, error) {
	out := new(GetSalaryTxnVerificationRequestsCountResponse)
	err := c.cc.Invoke(ctx, SalaryDataOps_GetSalaryTxnVerificationRequestsCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SalaryDataOpsServer is the server API for SalaryDataOps service.
// All implementations should embed UnimplementedSalaryDataOpsServer
// for forward compatibility
type SalaryDataOpsServer interface {
	// UpdateUsersEmploymentDetails rpc is used to update a users employer
	UpdateUsersEmploymentDetails(context.Context, *UpdateUsersEmploymentDetailsRequest) (*UpdateUsersEmploymentDetailsResponse, error)
	// SearchUsingGSTINOrName rpc gets employer suggestions by matching an input string with
	// employer name or gstin number
	SearchUsingGSTINOrName(context.Context, *SearchUsingGSTINOrNameRequest) (*SearchUsingGSTINOrNameResponse, error)
	// GetSalaryTxnVerificationRequestsByStatus rpc fetches all the salary transactions that were raised to be manually
	// reviewed by Data Ops team. Details can be fetched by using the status of the salary transaction verification request.
	GetSalaryTxnVerificationRequestsByStatus(context.Context, *GetSalaryTxnVerificationRequestsByStatusRequest) (*GetSalaryTxnVerificationRequestsByStatusResponse, error)
	// GetDetailedSalaryTxnVerificationRequestsByFilter rpc is used to get detailed info such as actor info or employer info
	// along with their salary transaction verification requests
	GetDetailedSalaryTxnVerificationRequestsByFilter(context.Context, *GetDetailedSalaryTxnVerificationRequestsByFilterRequest) (*GetDetailedSalaryTxnVerificationRequestsByFilterResponse, error)
	// UpdateVerificationRequestStatus rpc updates the status of the salary transaction verification request.
	// Data Ops will send a combination of inputs that will mark a salary transaction verification request
	// as VERIFIED/REJECTED/ESCALATE_TO_ENGG_TEAM
	UpdateVerificationRequestStatus(context.Context, *UpdateVerificationRequestStatusRequest) (*UpdateVerificationRequestStatusResponse, error)
	// RaiseAndVerifySalaryTxnVerificationReq raise salary txn verification request and then verify (as of now it mark the request as VERIFIED) the request
	RaiseAndVerifySalaryTxnVerificationReq(context.Context, *RaiseAndVerifySalaryTxnVerificationReqRequest) (*RaiseAndVerifySalaryTxnVerificationReqResponse, error)
	// GetUserDetailsNeededForVerification rpc is used to get relevant employment information
	// needed to verify a salary transaction
	GetUserDetailsNeededForVerification(context.Context, *GetUserDetailsNeededForVerificationRequest) (*GetUserDetailsNeededForVerificationResponse, error)
	// GetSalaryAccountDetailsForAgents fetches all details relevant to
	// salary program for a user using actor and user passed in header
	GetSalaryAccountDetailsForAgents(context.Context, *GetSalaryAccountDetailsForAgentsRequest) (*GetSalaryAccountDetailsForAgentsResponse, error)
	// GetSalaryAccountDetailsForSalaryDataOps fetches all details relevant to salary program
	// using phone number/email id
	GetSalaryAccountDetailsForSalaryDataOps(context.Context, *GetSalaryAccountDetailsForSalaryDataOpsRequest) (*GetSalaryAccountDetailsForSalaryDataOpsResponse, error)
	// GetSalaryTxnVerificationRequestsForUser rpc fetches all the salary transactions that were raised for a user.
	// This is for cx agents to know about salary txn verification status for a user who reaches out.
	// Actor details are expected to be passed in the header
	GetSalaryTxnVerificationRequestsForUser(context.Context, *GetSalaryTxnVerificationRequestsForUserRequest) (*GetSalaryTxnVerificationRequestsForUserResponse, error)
	// GetSalaryTxnVerificationRequestsForSalaryDataOps rpc fetches the salary transactions details for salary transaction verification request of a user
	GetSalaryTxnVerificationRequestsForSalaryDataOps(context.Context, *GetSalaryTxnVerificationRequestsForSalaryDataOpsRequest) (*GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse, error)
	// GetPossibleSalaryTxnsForActor rpc fetches all the transactions which can be possible salary transactions for actor.
	// For now it fetches txns after 25 days and before 60 days of last-verified-salary-txn time.
	GetPossibleSalaryTxnsForActor(context.Context, *GetPossibleSalaryTxnsForActorRequest) (*GetPossibleSalaryTxnsForActorResponse, error)
	// GetUserDetailsNeededForRaisingSalaryVerification rpc fetches the user details required for raising salary verification from sherlock.
	// It fetches user details, user's employment info and last verified salary txn details.
	GetUserDetailsNeededForRaisingSalaryVerification(context.Context, *GetUserDetailsNeededForRaisingSalaryVerificationRequest) (*GetUserDetailsNeededForRaisingSalaryVerificationResponse, error)
	// GetPossibleActorsForSalaryVerification rpc fetches the list of possible actors for whom salary verification request can be raised.
	GetPossibleActorsForSalaryVerification(context.Context, *GetPossibleActorsForSalaryVerificationRequest) (*GetPossibleActorsForSalaryVerificationResponse, error)
	// UpdateSalaryVerificationEligibilityStatus rpc updates the status of the user who is into consideration for raising salary verification request.
	// salary txn verification request is raised on updating the status to COMPLETE.
	UpdateSalaryVerificationEligibilityStatus(context.Context, *UpdateSalaryVerificationEligibilityStatusRequest) (*UpdateSalaryVerificationEligibilityStatusResponse, error)
	// GetHealthInsuranceUserPolicyDetails fetches all details relevant to health insurance policy details specific to the user
	// eg. policy_number, activated_on_date
	// using phone number/email id/actor id
	GetHealthInsuranceUserPolicyDetailsForAgents(context.Context, *GetHealthInsuranceUserPolicyDetailsForAgentsRequest) (*GetHealthInsuranceUserPolicyDetailsForAgentsResponse, error)
	// GetHealthInsuranceGeneralPolicyInfo fetches all details relevant to health insurance policy
	// eg. insurer contact details, riskcovry contact details etc
	GetHealthInsuranceGeneralPolicyInfo(context.Context, *GetHealthInsuranceGeneralPolicyInfoRequest) (*GetHealthInsuranceGeneralPolicyInfoResponse, error)
	// GetSalaryActivationStatusAtTime checks if the salary program was active for user at the given time
	// using phone number/email id
	GetSalaryProgramActivationStatusAtTimeForAgents(context.Context, *GetSalaryProgramActivationStatusAtTimeForAgentsRequest) (*GetSalaryProgramActivationStatusAtTimeForAgentsResponse, error)
	// GetSalaryLiteMandateDetailsForAgents fetches salary lite mandate details
	// for a user using actor and user passed in header
	GetSalaryLiteMandateDetailsForAgents(context.Context, *GetSalaryLiteMandateDetailsForAgentsRequest) (*GetSalaryLiteMandateDetailsForAgentsResponse, error)
	// GetSalaryLiteMandateExecutionDetailsForAgents fetches salary lite mandate execution (monthly transfer) details
	// for a user using actor and user passed in header
	GetSalaryLiteMandateExecutionDetailsForAgents(context.Context, *GetSalaryLiteMandateExecutionDetailsForAgentsRequest) (*GetSalaryLiteMandateExecutionDetailsForAgentsResponse, error)
	// This rpc is used to get the number of salary transaction verification requests, by data ops team
	GetSalaryTxnVerificationRequestsCount(context.Context, *GetSalaryTxnVerificationRequestsCountRequest) (*GetSalaryTxnVerificationRequestsCountResponse, error)
}

// UnimplementedSalaryDataOpsServer should be embedded to have forward compatible implementations.
type UnimplementedSalaryDataOpsServer struct {
}

func (UnimplementedSalaryDataOpsServer) UpdateUsersEmploymentDetails(context.Context, *UpdateUsersEmploymentDetailsRequest) (*UpdateUsersEmploymentDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUsersEmploymentDetails not implemented")
}
func (UnimplementedSalaryDataOpsServer) SearchUsingGSTINOrName(context.Context, *SearchUsingGSTINOrNameRequest) (*SearchUsingGSTINOrNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUsingGSTINOrName not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryTxnVerificationRequestsByStatus(context.Context, *GetSalaryTxnVerificationRequestsByStatusRequest) (*GetSalaryTxnVerificationRequestsByStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryTxnVerificationRequestsByStatus not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetDetailedSalaryTxnVerificationRequestsByFilter(context.Context, *GetDetailedSalaryTxnVerificationRequestsByFilterRequest) (*GetDetailedSalaryTxnVerificationRequestsByFilterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDetailedSalaryTxnVerificationRequestsByFilter not implemented")
}
func (UnimplementedSalaryDataOpsServer) UpdateVerificationRequestStatus(context.Context, *UpdateVerificationRequestStatusRequest) (*UpdateVerificationRequestStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVerificationRequestStatus not implemented")
}
func (UnimplementedSalaryDataOpsServer) RaiseAndVerifySalaryTxnVerificationReq(context.Context, *RaiseAndVerifySalaryTxnVerificationReqRequest) (*RaiseAndVerifySalaryTxnVerificationReqResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RaiseAndVerifySalaryTxnVerificationReq not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetUserDetailsNeededForVerification(context.Context, *GetUserDetailsNeededForVerificationRequest) (*GetUserDetailsNeededForVerificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDetailsNeededForVerification not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryAccountDetailsForAgents(context.Context, *GetSalaryAccountDetailsForAgentsRequest) (*GetSalaryAccountDetailsForAgentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryAccountDetailsForAgents not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryAccountDetailsForSalaryDataOps(context.Context, *GetSalaryAccountDetailsForSalaryDataOpsRequest) (*GetSalaryAccountDetailsForSalaryDataOpsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryAccountDetailsForSalaryDataOps not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryTxnVerificationRequestsForUser(context.Context, *GetSalaryTxnVerificationRequestsForUserRequest) (*GetSalaryTxnVerificationRequestsForUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryTxnVerificationRequestsForUser not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryTxnVerificationRequestsForSalaryDataOps(context.Context, *GetSalaryTxnVerificationRequestsForSalaryDataOpsRequest) (*GetSalaryTxnVerificationRequestsForSalaryDataOpsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryTxnVerificationRequestsForSalaryDataOps not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetPossibleSalaryTxnsForActor(context.Context, *GetPossibleSalaryTxnsForActorRequest) (*GetPossibleSalaryTxnsForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPossibleSalaryTxnsForActor not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetUserDetailsNeededForRaisingSalaryVerification(context.Context, *GetUserDetailsNeededForRaisingSalaryVerificationRequest) (*GetUserDetailsNeededForRaisingSalaryVerificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDetailsNeededForRaisingSalaryVerification not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetPossibleActorsForSalaryVerification(context.Context, *GetPossibleActorsForSalaryVerificationRequest) (*GetPossibleActorsForSalaryVerificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPossibleActorsForSalaryVerification not implemented")
}
func (UnimplementedSalaryDataOpsServer) UpdateSalaryVerificationEligibilityStatus(context.Context, *UpdateSalaryVerificationEligibilityStatusRequest) (*UpdateSalaryVerificationEligibilityStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSalaryVerificationEligibilityStatus not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetHealthInsuranceUserPolicyDetailsForAgents(context.Context, *GetHealthInsuranceUserPolicyDetailsForAgentsRequest) (*GetHealthInsuranceUserPolicyDetailsForAgentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHealthInsuranceUserPolicyDetailsForAgents not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetHealthInsuranceGeneralPolicyInfo(context.Context, *GetHealthInsuranceGeneralPolicyInfoRequest) (*GetHealthInsuranceGeneralPolicyInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHealthInsuranceGeneralPolicyInfo not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryProgramActivationStatusAtTimeForAgents(context.Context, *GetSalaryProgramActivationStatusAtTimeForAgentsRequest) (*GetSalaryProgramActivationStatusAtTimeForAgentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryProgramActivationStatusAtTimeForAgents not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryLiteMandateDetailsForAgents(context.Context, *GetSalaryLiteMandateDetailsForAgentsRequest) (*GetSalaryLiteMandateDetailsForAgentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryLiteMandateDetailsForAgents not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryLiteMandateExecutionDetailsForAgents(context.Context, *GetSalaryLiteMandateExecutionDetailsForAgentsRequest) (*GetSalaryLiteMandateExecutionDetailsForAgentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryLiteMandateExecutionDetailsForAgents not implemented")
}
func (UnimplementedSalaryDataOpsServer) GetSalaryTxnVerificationRequestsCount(context.Context, *GetSalaryTxnVerificationRequestsCountRequest) (*GetSalaryTxnVerificationRequestsCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSalaryTxnVerificationRequestsCount not implemented")
}

// UnsafeSalaryDataOpsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SalaryDataOpsServer will
// result in compilation errors.
type UnsafeSalaryDataOpsServer interface {
	mustEmbedUnimplementedSalaryDataOpsServer()
}

func RegisterSalaryDataOpsServer(s grpc.ServiceRegistrar, srv SalaryDataOpsServer) {
	s.RegisterService(&SalaryDataOps_ServiceDesc, srv)
}

func _SalaryDataOps_UpdateUsersEmploymentDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUsersEmploymentDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).UpdateUsersEmploymentDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_UpdateUsersEmploymentDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).UpdateUsersEmploymentDetails(ctx, req.(*UpdateUsersEmploymentDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_SearchUsingGSTINOrName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUsingGSTINOrNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).SearchUsingGSTINOrName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_SearchUsingGSTINOrName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).SearchUsingGSTINOrName(ctx, req.(*SearchUsingGSTINOrNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryTxnVerificationRequestsByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryTxnVerificationRequestsByStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryTxnVerificationRequestsByStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsByStatus(ctx, req.(*GetSalaryTxnVerificationRequestsByStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetDetailedSalaryTxnVerificationRequestsByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDetailedSalaryTxnVerificationRequestsByFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetDetailedSalaryTxnVerificationRequestsByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetDetailedSalaryTxnVerificationRequestsByFilter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetDetailedSalaryTxnVerificationRequestsByFilter(ctx, req.(*GetDetailedSalaryTxnVerificationRequestsByFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_UpdateVerificationRequestStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVerificationRequestStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).UpdateVerificationRequestStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_UpdateVerificationRequestStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).UpdateVerificationRequestStatus(ctx, req.(*UpdateVerificationRequestStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_RaiseAndVerifySalaryTxnVerificationReq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RaiseAndVerifySalaryTxnVerificationReqRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).RaiseAndVerifySalaryTxnVerificationReq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_RaiseAndVerifySalaryTxnVerificationReq_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).RaiseAndVerifySalaryTxnVerificationReq(ctx, req.(*RaiseAndVerifySalaryTxnVerificationReqRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetUserDetailsNeededForVerification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDetailsNeededForVerificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetUserDetailsNeededForVerification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetUserDetailsNeededForVerification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetUserDetailsNeededForVerification(ctx, req.(*GetUserDetailsNeededForVerificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryAccountDetailsForAgents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryAccountDetailsForAgentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryAccountDetailsForAgents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryAccountDetailsForAgents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryAccountDetailsForAgents(ctx, req.(*GetSalaryAccountDetailsForAgentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryAccountDetailsForSalaryDataOps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryAccountDetailsForSalaryDataOpsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryAccountDetailsForSalaryDataOps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryAccountDetailsForSalaryDataOps_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryAccountDetailsForSalaryDataOps(ctx, req.(*GetSalaryAccountDetailsForSalaryDataOpsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryTxnVerificationRequestsForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryTxnVerificationRequestsForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryTxnVerificationRequestsForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsForUser(ctx, req.(*GetSalaryTxnVerificationRequestsForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryTxnVerificationRequestsForSalaryDataOps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryTxnVerificationRequestsForSalaryDataOpsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsForSalaryDataOps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryTxnVerificationRequestsForSalaryDataOps_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsForSalaryDataOps(ctx, req.(*GetSalaryTxnVerificationRequestsForSalaryDataOpsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetPossibleSalaryTxnsForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPossibleSalaryTxnsForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetPossibleSalaryTxnsForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetPossibleSalaryTxnsForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetPossibleSalaryTxnsForActor(ctx, req.(*GetPossibleSalaryTxnsForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetUserDetailsNeededForRaisingSalaryVerification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDetailsNeededForRaisingSalaryVerificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetUserDetailsNeededForRaisingSalaryVerification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetUserDetailsNeededForRaisingSalaryVerification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetUserDetailsNeededForRaisingSalaryVerification(ctx, req.(*GetUserDetailsNeededForRaisingSalaryVerificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetPossibleActorsForSalaryVerification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPossibleActorsForSalaryVerificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetPossibleActorsForSalaryVerification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetPossibleActorsForSalaryVerification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetPossibleActorsForSalaryVerification(ctx, req.(*GetPossibleActorsForSalaryVerificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_UpdateSalaryVerificationEligibilityStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSalaryVerificationEligibilityStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).UpdateSalaryVerificationEligibilityStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_UpdateSalaryVerificationEligibilityStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).UpdateSalaryVerificationEligibilityStatus(ctx, req.(*UpdateSalaryVerificationEligibilityStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetHealthInsuranceUserPolicyDetailsForAgents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHealthInsuranceUserPolicyDetailsForAgentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetHealthInsuranceUserPolicyDetailsForAgents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetHealthInsuranceUserPolicyDetailsForAgents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetHealthInsuranceUserPolicyDetailsForAgents(ctx, req.(*GetHealthInsuranceUserPolicyDetailsForAgentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetHealthInsuranceGeneralPolicyInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHealthInsuranceGeneralPolicyInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetHealthInsuranceGeneralPolicyInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetHealthInsuranceGeneralPolicyInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetHealthInsuranceGeneralPolicyInfo(ctx, req.(*GetHealthInsuranceGeneralPolicyInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryProgramActivationStatusAtTimeForAgents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryProgramActivationStatusAtTimeForAgentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryProgramActivationStatusAtTimeForAgents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryProgramActivationStatusAtTimeForAgents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryProgramActivationStatusAtTimeForAgents(ctx, req.(*GetSalaryProgramActivationStatusAtTimeForAgentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryLiteMandateDetailsForAgents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryLiteMandateDetailsForAgentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryLiteMandateDetailsForAgents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryLiteMandateDetailsForAgents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryLiteMandateDetailsForAgents(ctx, req.(*GetSalaryLiteMandateDetailsForAgentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryLiteMandateExecutionDetailsForAgents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryLiteMandateExecutionDetailsForAgentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryLiteMandateExecutionDetailsForAgents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryLiteMandateExecutionDetailsForAgents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryLiteMandateExecutionDetailsForAgents(ctx, req.(*GetSalaryLiteMandateExecutionDetailsForAgentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalaryDataOps_GetSalaryTxnVerificationRequestsCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSalaryTxnVerificationRequestsCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SalaryDataOps_GetSalaryTxnVerificationRequestsCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalaryDataOpsServer).GetSalaryTxnVerificationRequestsCount(ctx, req.(*GetSalaryTxnVerificationRequestsCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SalaryDataOps_ServiceDesc is the grpc.ServiceDesc for SalaryDataOps service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SalaryDataOps_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cx.data_collector.salarydataops.SalaryDataOps",
	HandlerType: (*SalaryDataOpsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateUsersEmploymentDetails",
			Handler:    _SalaryDataOps_UpdateUsersEmploymentDetails_Handler,
		},
		{
			MethodName: "SearchUsingGSTINOrName",
			Handler:    _SalaryDataOps_SearchUsingGSTINOrName_Handler,
		},
		{
			MethodName: "GetSalaryTxnVerificationRequestsByStatus",
			Handler:    _SalaryDataOps_GetSalaryTxnVerificationRequestsByStatus_Handler,
		},
		{
			MethodName: "GetDetailedSalaryTxnVerificationRequestsByFilter",
			Handler:    _SalaryDataOps_GetDetailedSalaryTxnVerificationRequestsByFilter_Handler,
		},
		{
			MethodName: "UpdateVerificationRequestStatus",
			Handler:    _SalaryDataOps_UpdateVerificationRequestStatus_Handler,
		},
		{
			MethodName: "RaiseAndVerifySalaryTxnVerificationReq",
			Handler:    _SalaryDataOps_RaiseAndVerifySalaryTxnVerificationReq_Handler,
		},
		{
			MethodName: "GetUserDetailsNeededForVerification",
			Handler:    _SalaryDataOps_GetUserDetailsNeededForVerification_Handler,
		},
		{
			MethodName: "GetSalaryAccountDetailsForAgents",
			Handler:    _SalaryDataOps_GetSalaryAccountDetailsForAgents_Handler,
		},
		{
			MethodName: "GetSalaryAccountDetailsForSalaryDataOps",
			Handler:    _SalaryDataOps_GetSalaryAccountDetailsForSalaryDataOps_Handler,
		},
		{
			MethodName: "GetSalaryTxnVerificationRequestsForUser",
			Handler:    _SalaryDataOps_GetSalaryTxnVerificationRequestsForUser_Handler,
		},
		{
			MethodName: "GetSalaryTxnVerificationRequestsForSalaryDataOps",
			Handler:    _SalaryDataOps_GetSalaryTxnVerificationRequestsForSalaryDataOps_Handler,
		},
		{
			MethodName: "GetPossibleSalaryTxnsForActor",
			Handler:    _SalaryDataOps_GetPossibleSalaryTxnsForActor_Handler,
		},
		{
			MethodName: "GetUserDetailsNeededForRaisingSalaryVerification",
			Handler:    _SalaryDataOps_GetUserDetailsNeededForRaisingSalaryVerification_Handler,
		},
		{
			MethodName: "GetPossibleActorsForSalaryVerification",
			Handler:    _SalaryDataOps_GetPossibleActorsForSalaryVerification_Handler,
		},
		{
			MethodName: "UpdateSalaryVerificationEligibilityStatus",
			Handler:    _SalaryDataOps_UpdateSalaryVerificationEligibilityStatus_Handler,
		},
		{
			MethodName: "GetHealthInsuranceUserPolicyDetailsForAgents",
			Handler:    _SalaryDataOps_GetHealthInsuranceUserPolicyDetailsForAgents_Handler,
		},
		{
			MethodName: "GetHealthInsuranceGeneralPolicyInfo",
			Handler:    _SalaryDataOps_GetHealthInsuranceGeneralPolicyInfo_Handler,
		},
		{
			MethodName: "GetSalaryProgramActivationStatusAtTimeForAgents",
			Handler:    _SalaryDataOps_GetSalaryProgramActivationStatusAtTimeForAgents_Handler,
		},
		{
			MethodName: "GetSalaryLiteMandateDetailsForAgents",
			Handler:    _SalaryDataOps_GetSalaryLiteMandateDetailsForAgents_Handler,
		},
		{
			MethodName: "GetSalaryLiteMandateExecutionDetailsForAgents",
			Handler:    _SalaryDataOps_GetSalaryLiteMandateExecutionDetailsForAgents_Handler,
		},
		{
			MethodName: "GetSalaryTxnVerificationRequestsCount",
			Handler:    _SalaryDataOps_GetSalaryTxnVerificationRequestsCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/cx/data_collector/salarydataops/service.proto",
}
