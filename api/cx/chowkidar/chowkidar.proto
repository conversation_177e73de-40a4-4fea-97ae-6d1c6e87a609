syntax = "proto3";

package cx.chowkidar;

import "api/cx/chowkidar/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/cx/chowkidar";
option java_package = "com.github.epifi.gamma.api.cx.chowkidar";

// ChowkidarAlert represents an alert in chowkidar service
message ChowkidarAlert {
  // specifies the id in db record
  string id = 1;
  // specifies the identifier used in alertmanager
  string alert_name = 2;
  // specifies the grpc method name for which we have received the alert
  string method_name = 3;
  // specifies the current status of alert Ex. Firing, Resolved
  Status status = 4;
  // specifies the time when the alert got triggered
  google.protobuf.Timestamp start_time = 5;
  // specifies the time when the alert got resolved
  google.protobuf.Timestamp end_time = 6;
  // specifies the severity of the alert (p0, p1, p2)
  string severity = 7;
  // specifies the id of alert, which is same as this alert but came before it and is still active
  string parent_alert_id = 8;
  // timestamp referring to moment when entry was created in the DB.
  google.protobuf.Timestamp created_at = 9;
  // timestamp referring to moment when entry was last updated in the DB.
  google.protobuf.Timestamp updated_at = 10;
}

// This message represents the banner detail for an alert
message AlertToBannerMapping {
  // specifies the id in db record
  string id = 1;
  // specifies the alert associated with banner
  string alert_id = 2;
  // specifies the banner_id for the banner created
  string banner_id = 3;
  // specifies the type of banner Ex. Firing, Resolved
  // banner_type is not same as alert status, in future we might have different type of banners for different alerts
  BannerType banner_type = 4;
  // timestamp referring to moment when entry was created in the DB.
  google.protobuf.Timestamp created_at = 5;
  // timestamp referring to moment when entry was last updated in the DB.
  google.protobuf.Timestamp updated_at = 6;
}
