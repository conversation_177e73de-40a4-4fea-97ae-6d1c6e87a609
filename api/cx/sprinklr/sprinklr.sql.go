package sprinklr

import (
	"github.com/epifi/be-common/api/pkg/proto/backport"

	"database/sql/driver"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (s *SprinklrPayload) Value() (driver.Value, error) {
	if s == nil {
		return nil, nil
	}
	return protojson.Marshal(s)
}

// Scan implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (s *SprinklrPayload) Scan(input interface{}) error {
	val, ok := input.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", input)
		return err
	}
	return backport.SafeUnmarshal(protojson.Unmarshal, val, s)
}
