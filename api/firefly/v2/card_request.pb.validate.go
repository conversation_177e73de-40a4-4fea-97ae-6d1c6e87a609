// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/v2/card_request.proto

package v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/v2/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CardRequestType(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on CardRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CardRequestMultiError, or
// nil if none found.
func (m *CardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for Type

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetRequestDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "RequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "RequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "RequestDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStageDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "StageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "StageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStageDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "StageDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for ExternalUserId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardRequestMultiError(errors)
	}

	return nil
}

// CardRequestMultiError is an error wrapping multiple validation errors
// returned by CardRequest.ValidateAll() if the designated constraints aren't met.
type CardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestMultiError) AllErrors() []error { return m }

// CardRequestValidationError is the validation error returned by
// CardRequest.Validate if the designated constraints aren't met.
type CardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestValidationError) ErrorName() string { return "CardRequestValidationError" }

// Error satisfies the builtin error interface
func (e CardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestValidationError{}

// Validate checks the field values on CardRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardRequestDetailsMultiError, or nil if none found.
func (m *CardRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *CardRequestDetails_OnboardingRequestDetails:
		if v == nil {
			err := CardRequestDetailsValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOnboardingRequestDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "OnboardingRequestDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "OnboardingRequestDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOnboardingRequestDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardRequestDetailsValidationError{
					field:  "OnboardingRequestDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CardRequestDetails_CardDeliveryTrackingDetails:
		if v == nil {
			err := CardRequestDetailsValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCardDeliveryTrackingDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "CardDeliveryTrackingDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardRequestDetailsValidationError{
						field:  "CardDeliveryTrackingDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCardDeliveryTrackingDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardRequestDetailsValidationError{
					field:  "CardDeliveryTrackingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CardRequestDetailsMultiError(errors)
	}

	return nil
}

// CardRequestDetailsMultiError is an error wrapping multiple validation errors
// returned by CardRequestDetails.ValidateAll() if the designated constraints
// aren't met.
type CardRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestDetailsMultiError) AllErrors() []error { return m }

// CardRequestDetailsValidationError is the validation error returned by
// CardRequestDetails.Validate if the designated constraints aren't met.
type CardRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestDetailsValidationError) ErrorName() string {
	return "CardRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CardRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestDetailsValidationError{}

// Validate checks the field values on CardDeliveryTrackingDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardDeliveryTrackingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardDeliveryTrackingDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardDeliveryTrackingDetailsMultiError, or nil if none found.
func (m *CardDeliveryTrackingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CardDeliveryTrackingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrderId

	// no validation rules for Awb

	// no validation rules for Carrier

	// no validation rules for DeliveryState

	for idx, item := range m.GetScans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardDeliveryTrackingDetailsValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardDeliveryTrackingDetailsValidationError{
						field:  fmt.Sprintf("Scans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardDeliveryTrackingDetailsValidationError{
					field:  fmt.Sprintf("Scans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPickupDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingDetailsValidationError{
					field:  "PickupDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingDetailsValidationError{
					field:  "PickupDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPickupDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingDetailsValidationError{
				field:  "PickupDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUploadedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingDetailsValidationError{
					field:  "UploadedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingDetailsValidationError{
					field:  "UploadedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUploadedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingDetailsValidationError{
				field:  "UploadedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PrintingVendor

	if all {
		switch v := interface{}(m.GetExpectedDeliveryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardDeliveryTrackingDetailsValidationError{
					field:  "ExpectedDeliveryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardDeliveryTrackingDetailsValidationError{
					field:  "ExpectedDeliveryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDeliveryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardDeliveryTrackingDetailsValidationError{
				field:  "ExpectedDeliveryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TrackingUrl

	if len(errors) > 0 {
		return CardDeliveryTrackingDetailsMultiError(errors)
	}

	return nil
}

// CardDeliveryTrackingDetailsMultiError is an error wrapping multiple
// validation errors returned by CardDeliveryTrackingDetails.ValidateAll() if
// the designated constraints aren't met.
type CardDeliveryTrackingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardDeliveryTrackingDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardDeliveryTrackingDetailsMultiError) AllErrors() []error { return m }

// CardDeliveryTrackingDetailsValidationError is the validation error returned
// by CardDeliveryTrackingDetails.Validate if the designated constraints
// aren't met.
type CardDeliveryTrackingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardDeliveryTrackingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardDeliveryTrackingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardDeliveryTrackingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardDeliveryTrackingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardDeliveryTrackingDetailsValidationError) ErrorName() string {
	return "CardDeliveryTrackingDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CardDeliveryTrackingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardDeliveryTrackingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardDeliveryTrackingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardDeliveryTrackingDetailsValidationError{}

// Validate checks the field values on Scan with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Scan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Scan with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ScanMultiError, or nil if none found.
func (m *Scan) ValidateAll() error {
	return m.validate(true)
}

func (m *Scan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Location

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScanValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScanValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScanValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StatusDescription

	if len(errors) > 0 {
		return ScanMultiError(errors)
	}

	return nil
}

// ScanMultiError is an error wrapping multiple validation errors returned by
// Scan.ValidateAll() if the designated constraints aren't met.
type ScanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScanMultiError) AllErrors() []error { return m }

// ScanValidationError is the validation error returned by Scan.Validate if the
// designated constraints aren't met.
type ScanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScanValidationError) ErrorName() string { return "ScanValidationError" }

// Error satisfies the builtin error interface
func (e ScanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScanValidationError{}

// Validate checks the field values on CardOnboardingRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardOnboardingRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardOnboardingRequestDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardOnboardingRequestDetailsMultiError, or nil if none found.
func (m *CardOnboardingRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CardOnboardingRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	if all {
		switch v := interface{}(m.GetCardProgram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardOnboardingRequestDetailsValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardOnboardingRequestDetailsValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardProgram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardOnboardingRequestDetailsValidationError{
				field:  "CardProgram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorWorkflowId

	// no validation rules for ApplicantType

	if len(errors) > 0 {
		return CardOnboardingRequestDetailsMultiError(errors)
	}

	return nil
}

// CardOnboardingRequestDetailsMultiError is an error wrapping multiple
// validation errors returned by CardOnboardingRequestDetails.ValidateAll() if
// the designated constraints aren't met.
type CardOnboardingRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardOnboardingRequestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardOnboardingRequestDetailsMultiError) AllErrors() []error { return m }

// CardOnboardingRequestDetailsValidationError is the validation error returned
// by CardOnboardingRequestDetails.Validate if the designated constraints
// aren't met.
type CardOnboardingRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardOnboardingRequestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardOnboardingRequestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardOnboardingRequestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardOnboardingRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardOnboardingRequestDetailsValidationError) ErrorName() string {
	return "CardOnboardingRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CardOnboardingRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardOnboardingRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardOnboardingRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardOnboardingRequestDetailsValidationError{}

// Validate checks the field values on CardRequestStageDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardRequestStageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardRequestStageDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardRequestStageDetailsMultiError, or nil if none found.
func (m *CardRequestStageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CardRequestStageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetStages()))
		i := 0
		for key := range m.GetStages() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetStages()[key]
			_ = val

			// no validation rules for Stages[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CardRequestStageDetailsValidationError{
							field:  fmt.Sprintf("Stages[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CardRequestStageDetailsValidationError{
							field:  fmt.Sprintf("Stages[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CardRequestStageDetailsValidationError{
						field:  fmt.Sprintf("Stages[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CardRequestStageDetailsMultiError(errors)
	}

	return nil
}

// CardRequestStageDetailsMultiError is an error wrapping multiple validation
// errors returned by CardRequestStageDetails.ValidateAll() if the designated
// constraints aren't met.
type CardRequestStageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardRequestStageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardRequestStageDetailsMultiError) AllErrors() []error { return m }

// CardRequestStageDetailsValidationError is the validation error returned by
// CardRequestStageDetails.Validate if the designated constraints aren't met.
type CardRequestStageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardRequestStageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardRequestStageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardRequestStageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardRequestStageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardRequestStageDetailsValidationError) ErrorName() string {
	return "CardRequestStageDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CardRequestStageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardRequestStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardRequestStageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardRequestStageDetailsValidationError{}

// Validate checks the field values on StageInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageInfoMultiError, or nil
// if none found.
func (m *StageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetLastUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageInfoValidationError{
				field:  "LastUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageInfoValidationError{
				field:  "StartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StageInfoMultiError(errors)
	}

	return nil
}

// StageInfoMultiError is an error wrapping multiple validation errors returned
// by StageInfo.ValidateAll() if the designated constraints aren't met.
type StageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageInfoMultiError) AllErrors() []error { return m }

// StageInfoValidationError is the validation error returned by
// StageInfo.Validate if the designated constraints aren't met.
type StageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageInfoValidationError) ErrorName() string { return "StageInfoValidationError" }

// Error satisfies the builtin error interface
func (e StageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageInfoValidationError{}
