// Code generated by MockGen. DO NOT EDIT.
// Source: api/firefly/pinot/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	categorizer "github.com/epifi/gamma/api/categorizer"
	consumer "github.com/epifi/gamma/api/firefly/accounting/consumer"
	pinot "github.com/epifi/gamma/api/firefly/pinot"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsumerClient is a mock of ConsumerClient interface.
type MockConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerClientMockRecorder
}

// MockConsumerClientMockRecorder is the mock recorder for MockConsumerClient.
type MockConsumerClientMockRecorder struct {
	mock *MockConsumerClient
}

// NewMockConsumerClient creates a new mock instance.
func NewMockConsumerClient(ctrl *gomock.Controller) *MockConsumerClient {
	mock := &MockConsumerClient{ctrl: ctrl}
	mock.recorder = &MockConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerClient) EXPECT() *MockConsumerClientMockRecorder {
	return m.recorder
}

// ProcessCCTransactionEvent mocks base method.
func (m *MockConsumerClient) ProcessCCTransactionEvent(ctx context.Context, in *consumer.CreditCardTransactionEvent, opts ...grpc.CallOption) (*pinot.ProcessCCTransactionEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCCTransactionEvent", varargs...)
	ret0, _ := ret[0].(*pinot.ProcessCCTransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCCTransactionEvent indicates an expected call of ProcessCCTransactionEvent.
func (mr *MockConsumerClientMockRecorder) ProcessCCTransactionEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCCTransactionEvent", reflect.TypeOf((*MockConsumerClient)(nil).ProcessCCTransactionEvent), varargs...)
}

// ProcessCategoryUpdateEvent mocks base method.
func (m *MockConsumerClient) ProcessCategoryUpdateEvent(ctx context.Context, in *categorizer.TransactionCategoryUpdateEvent, opts ...grpc.CallOption) (*pinot.ProcessCategoryUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCategoryUpdateEvent", varargs...)
	ret0, _ := ret[0].(*pinot.ProcessCategoryUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategoryUpdateEvent indicates an expected call of ProcessCategoryUpdateEvent.
func (mr *MockConsumerClientMockRecorder) ProcessCategoryUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategoryUpdateEvent", reflect.TypeOf((*MockConsumerClient)(nil).ProcessCategoryUpdateEvent), varargs...)
}

// MockConsumerServer is a mock of ConsumerServer interface.
type MockConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerServerMockRecorder
}

// MockConsumerServerMockRecorder is the mock recorder for MockConsumerServer.
type MockConsumerServerMockRecorder struct {
	mock *MockConsumerServer
}

// NewMockConsumerServer creates a new mock instance.
func NewMockConsumerServer(ctrl *gomock.Controller) *MockConsumerServer {
	mock := &MockConsumerServer{ctrl: ctrl}
	mock.recorder = &MockConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerServer) EXPECT() *MockConsumerServerMockRecorder {
	return m.recorder
}

// ProcessCCTransactionEvent mocks base method.
func (m *MockConsumerServer) ProcessCCTransactionEvent(arg0 context.Context, arg1 *consumer.CreditCardTransactionEvent) (*pinot.ProcessCCTransactionEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCCTransactionEvent", arg0, arg1)
	ret0, _ := ret[0].(*pinot.ProcessCCTransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCCTransactionEvent indicates an expected call of ProcessCCTransactionEvent.
func (mr *MockConsumerServerMockRecorder) ProcessCCTransactionEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCCTransactionEvent", reflect.TypeOf((*MockConsumerServer)(nil).ProcessCCTransactionEvent), arg0, arg1)
}

// ProcessCategoryUpdateEvent mocks base method.
func (m *MockConsumerServer) ProcessCategoryUpdateEvent(arg0 context.Context, arg1 *categorizer.TransactionCategoryUpdateEvent) (*pinot.ProcessCategoryUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCategoryUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*pinot.ProcessCategoryUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategoryUpdateEvent indicates an expected call of ProcessCategoryUpdateEvent.
func (mr *MockConsumerServerMockRecorder) ProcessCategoryUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategoryUpdateEvent", reflect.TypeOf((*MockConsumerServer)(nil).ProcessCategoryUpdateEvent), arg0, arg1)
}

// MockUnsafeConsumerServer is a mock of UnsafeConsumerServer interface.
type MockUnsafeConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsumerServerMockRecorder
}

// MockUnsafeConsumerServerMockRecorder is the mock recorder for MockUnsafeConsumerServer.
type MockUnsafeConsumerServerMockRecorder struct {
	mock *MockUnsafeConsumerServer
}

// NewMockUnsafeConsumerServer creates a new mock instance.
func NewMockUnsafeConsumerServer(ctrl *gomock.Controller) *MockUnsafeConsumerServer {
	mock := &MockUnsafeConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsumerServer) EXPECT() *MockUnsafeConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsumerServer mocks base method.
func (m *MockUnsafeConsumerServer) mustEmbedUnimplementedConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsumerServer")
}

// mustEmbedUnimplementedConsumerServer indicates an expected call of mustEmbedUnimplementedConsumerServer.
func (mr *MockUnsafeConsumerServerMockRecorder) mustEmbedUnimplementedConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsumerServer", reflect.TypeOf((*MockUnsafeConsumerServer)(nil).mustEmbedUnimplementedConsumerServer))
}
