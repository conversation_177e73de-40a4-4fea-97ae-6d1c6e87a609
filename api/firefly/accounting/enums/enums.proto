// protolint:disable MA<PERSON>_LINE_LENGTH
//go:generate gen_sql -types=TransactionStatus,TransactionCategory,TransactionOrigin,TransactionType,TransactionFailureType,DisputeState,TransactionAuthorizationStatus
syntax = "proto3";

package firefly.accounting.enums;

option go_package = "github.com/epifi/gamma/api/firefly/accounting/enums";
option java_package = "com.github.epifi.gamma.api.firefly.accounting.enums";

enum TransactionStatus {
  TRANSACTION_STATUS_UNSPECIFIED = 0;

  TRANSACTION_STATUS_SUCCESS = 1;

  TRANSACTION_STATUS_FAILURE = 2;
}

enum TransactionCategory {
  TRANSACTION_CATEGORY_UNSPECIFIED = 0;

  TRANSACTION_CATEGORY_ATM_WITHDRAWAL = 1;

  TRANSACTION_CATEGORY_ECOM = 2;

  TRANSACTION_CATEGORY_POS = 3;

  TRANSACTION_CATEGORY_ATM_REVERSAL = 4;

  TRANSACTION_CATEGORY_ECOM_REVERSAL = 5;

  TRANSACTION_CATEGORY_POS_REVERSAL = 6;

  TRANSACTION_CATEGORY_REFUND = 7;

  TRANSACTION_CATEGORY_CASH_AT_POS = 8;

  TRANSACTION_CATEGORY_VIRTUAL_ACCOUNT_CREDIT = 9;

  TRANSACTION_CATEGORY_IMPS_DEBIT = 10;

  TRANSACTION_CATEGORY_IMPS_DEBIT_REVERSAL = 11;

  TRANSACTION_CATEGORY_FEES = 12;

  TRANSACTION_CATEGORY_FEES_REVERSAL = 13;

  TRANSACTION_CATEGORY_FUND_POST_CREDIT = 14;

  TRANSACTION_CATEGORY_FUND_POST_DEBIT = 15;

  TRANSACTION_CATEGORY_C2M = 16;

  TRANSACTION_CATEGORY_C2C = 17;

  TRANSACTION_CATEGORY_M2C = 18;

  TRANSACTION_CATEGORY_SERVICE_TAX = 19;

  TRANSACTION_CATEGORY_SERVICE_TAX_REVERSAL = 20;

  TRANSACTION_CATEGORY_INTEREST = 21;

  TRANSACTION_CATEGORY_DIRECT_CREDIT = 22;

  TRANSACTION_CATEGORY_PG = 23;

  TRANSACTION_CATEGORY_UPI_COLLECT_CREDIT = 24;

  TRANSACTION_CATEGORY_NETC_REGISTERED_FAILURE = 25;

  TRANSACTION_CATEGORY_NETC_REGISTERED_SUCCESS = 26;

  TRANSACTION_CATEGORY_NETC_CORPORATE_DEBIT = 27;

  TRANSACTION_CATEGORY_NETC_RETAIL_DEBIT = 28;

  TRANSACTION_CATEGORY_CASHBACK_CREDIT = 29;

  TRANSACTION_CATEGORY_CHARGEBACK_CREDIT = 30;

  TRANSACTION_CATEGORY_CASH_WITHDRAWAL_FEE = 31;
  TRANSACTION_CATEGORY_DIRECT_DEBIT = 32;
  TRANSACTION_CATEGORY_LOAN_PRINCIPAL = 33;
  TRANSACTION_CATEGORY_LOAN_INTEREST = 34;
  TRANSACTION_CATEGORY_LOAN_INTEREST_TAX = 35;
  TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES = 36;
  TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_TAX = 37;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PRINCIPAL = 38;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_INTEREST = 39;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_INTEREST_TAX = 40;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_FEES = 41;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_FEES_TAX = 42;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PROCESSING_FEES = 43;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_PROCESSING_FEES_TAX = 44;
  TRANSACTION_CATEGORY_LOAN_PRE_CLOSURE_CREDIT = 45;
  TRANSACTION_CATEGORY_LOAN_CANCEL_CREDIT = 46;
  TRANSACTION_CATEGORY_LOAN_CANCEL = 47;
  TRANSACTION_CATEGORY_LOAN_CANCEL_INTEREST = 48;
  TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_REVERSAL = 49;
  TRANSACTION_CATEGORY_LOAN_PROCESSING_FEES_TAX_REVERSAL = 50;
  TRANSACTION_CATEGORY_LOAN_REVERSAL = 51;
  TRANSACTION_CATEGORY_EMI_CONVERSION = 52;
}

enum TransactionOrigin {
  TRANSACTION_ORIGIN_UNSPECIFIED = 0;

  TRANSACTION_ORIGIN_ATM = 1;

  TRANSACTION_ORIGIN_POS = 2;

  TRANSACTION_ORIGIN_ECOM = 3;

  TRANSACTION_ORIGIN_MVISA = 4;

  TRANSACTION_ORIGIN_ECOLLECT = 5;

  TRANSACTION_ORIGIN_MOBILE = 6;

  TRANSACTION_ORIGIN_CHARGEBACK = 7;

  TRANSACTION_ORIGIN_FEES = 8;

  TRANSACTION_ORIGIN_FUND_POST = 9;

  TRANSACTION_ORIGIN_CASH_AT_POS = 10;

  TRANSACTION_ORIGIN_UPI_COLLECT = 11;

  TRANSACTION_ORIGIN_NFC = 12;
  TRANSACTION_ORIGIN_LOAN = 13;
  TRANSACTION_ORIGIN_WEB = 14;
}

enum TransactionType {
  TRANSACTION_TYPE_UNSPECIFIED = 0;

  TRANSACTION_TYPE_CREDIT = 1;

  TRANSACTION_TYPE_DEBIT = 2;
}

enum CardTransactionFieldMask {
  CARD_TRANSACTION_FIELD_MASK_UNSPECIFIED = 0;
  CARD_TRANSACTION_FIELD_MASK_ID = 1;
  CARD_TRANSACTION_FIELD_MASK_ACCOUNT_ID = 2;
  CARD_TRANSACTION_FIELD_MASK_CARD_ID = 3;
  CARD_TRANSACTION_FIELD_MASK_AMOUNT = 4;
  CARD_TRANSACTION_FIELD_MASK_BALANCE = 5;
  CARD_TRANSACTION_FIELD_MASK_TXN_TIME = 6;
  CARD_TRANSACTION_FIELD_MASK_TXN_STATUS = 7;
  CARD_TRANSACTION_FIELD_MASK_TXN_CATEGORY = 8;
  CARD_TRANSACTION_FIELD_MASK_TXN_ORIGIN = 9;
  CARD_TRANSACTION_FIELD_MASK_TXN_TYPE = 10;
  CARD_TRANSACTION_FIELD_MASK_BENEFICIARY_INFO = 11;
  CARD_TRANSACTION_FIELD_MASK_CONVERSION_INFO = 12;
  CARD_TRANSACTION_FIELD_MASK_DESCRIPTION = 13;
  CARD_TRANSACTION_FIELD_MASK_DISPUTE_INFO = 14;
  CARD_TRANSACTION_FIELD_MASK_EXTERNAL_TXN_ID = 15;
  CARD_TRANSACTION_FIELD_MASK_BILL_REF_NO = 16;
  CARD_TRANSACTION_FIELD_MASK_BANK_TXN_ID = 17;
  CARD_TRANSACTION_FIELD_MASK_AUTH_CODE = 18;
  CARD_TRANSACTION_FIELD_MASK_ACQUIRER_ID = 19;
  CARD_TRANSACTION_FIELD_MASK_RETRIEVAL_REFERENCE_NO = 20;
  CARD_TRANSACTION_FIELD_MASK_SOR_TXN_ID = 21;
  CARD_TRANSACTION_FIELD_MASK_TXN_REFERENCE_NO = 22;
  CARD_TRANSACTION_FIELD_MASK_CREATED_AT = 23;
  CARD_TRANSACTION_FIELD_MASK_UPDATED_AT = 24;
  CARD_TRANSACTION_FIELD_MASK_DELETED_AT = 25;
  CARD_TRANSACTION_FIELD_MASK_FAILURE_INFO = 26;
  CARD_TRANSACTION_FIELD_MASK_VENDOR_EXT_TXN_ID = 27;
  CARD_TRANSACTION_FIELD_MASK_PARENT_TRANSACTION_ID = 28;
  CARD_TRANSACTION_FIELD_MASK_CHILD_TRANSACTION_IDS = 29;
  CARD_TRANSACTION_FIELD_MASK_DEDUPE_ID = 30;
  CARD_TRANSACTION_FIELD_MASK_TRANSACTION_AUTHORIZATION_STATUS = 31;
}

enum CreditAccountFieldMask {
  CREDIT_ACCOUNT_FIELD_MASK_UNSPECIFIED = 0;
  CREDIT_ACCOUNT_FIELD_MASK_ID = 1;
  CREDIT_ACCOUNT_FIELD_MASK_ACTOR_ID = 2;
  CREDIT_ACCOUNT_FIELD_MASK_REFERENCE_ID = 3;
  CREDIT_ACCOUNT_FIELD_MASK_BILLED_AMOUNT = 4;
  CREDIT_ACCOUNT_FIELD_MASK_UNBILLED_AMOUNT = 5;
  CREDIT_ACCOUNT_FIELD_MASK_AVAILABLE_LIMIT = 6 [deprecated = true];
  CREDIT_ACCOUNT_FIELD_MASK_TOTAL_OUTSTANDING = 7;
  CREDIT_ACCOUNT_FIELD_MASK_CREATED_AT = 8;
  CREDIT_ACCOUNT_FIELD_MASK_UPDATED_AT = 9;
  CREDIT_ACCOUNT_FIELD_MASK_DELETED_AT = 10;
  CREDIT_ACCOUNT_FIELD_MASK_TOTAL_LIMIT = 11;
  CREDIT_ACCOUNT_FIELD_MASK_LAST_SYNCED_AT = 12;
  CREDIT_ACCOUNT_FIELD_MASK_COLLATERAL_DETAILS = 13;
  CREDIT_ACCOUNT_FIELD_MASK_CARD_PROGRAM = 14;
  CREDIT_ACCOUNT_FIELD_MASK_DUE_INFO = 15;
}

enum TransactionFailureType {
  TRANSACTION_FAILURE_TYPE_UNSPECIFIED = 0;
  TRANSACTION_FAILURE_TYPE_CARD_UNALLOCATED = 1;
  TRANSACTION_FAILURE_TYPE_CARD_ALLOCATED = 2;
  TRANSACTION_FAILURE_TYPE_CARD_LOCKED = 3;
  TRANSACTION_FAILURE_TYPE_CARD_BLOCKED = 4;
  TRANSACTION_FAILURE_TYPE_CARD_SURRENDERD = 5;
  TRANSACTION_FAILURE_TYPE_CARD_DELINKED = 6;
  TRANSACTION_FAILURE_TYPE_CARD_REPLACED = 7;
  TRANSACTION_FAILURE_TYPE_CARD_DORMANT = 8;
  TRANSACTION_FAILURE_TYPE_CARD_PICKUP = 9;
  TRANSACTION_FAILURE_TYPE_CARD_EXPIRED = 10;
  TRANSACTION_FAILURE_TYPE_CARD_STOLEN = 11;
  TRANSACTION_FAILURE_TYPE_CARD_LOST = 12;
  TRANSACTION_FAILURE_TYPE_CARD_RESTRICTED = 13;
  TRANSACTION_FAILURE_TYPE_CARD_FRAUD = 14;
  // Transactions routed by merchant without OTP verification will be declined as ISSUER ATTEMPT
  TRANSACTION_FAILURE_TYPE_ISSUER_ATTEMPT = 15;
  // Credential on File or Card on file transactions if not allowed as per rule, will be declined under
  // this category
  TRANSACTION_FAILURE_TYPE_COF_NOT_ALLOWED = 16;
  // Transactions which are routed by merchant without CAVV (Cardholder Authentication Verification Value
  // will be declined if not allowed as per rule
  TRANSACTION_FAILURE_TYPE_NO_CAVV = 17;
  // Transactions which are routed by merchant without CVV2(Card Verification Value) will be declined if not
  // allowed as per rule.
  TRANSACTION_FAILURE_TYPE_NO_CVV2 = 18;
  // International transactions if not allowed for the product will be declined under this category
  TRANSACTION_FAILURE_TYPE_INTL_NA = 19;
  // Mail Order or Telephone Order (MOTO) if not allowed for the product will be declined under
  // this category.
  TRANSACTION_FAILURE_TYPE_MOTO_NOT_ALLOWED = 20;
  // Recurring Payment (RP) if not allowed for the product will be declined under this category
  TRANSACTION_FAILURE_TYPE_RP_NOT_ALLOWED = 21;
  // Installment Payment (IP) if not allowed for the product will be declined under this category
  TRANSACTION_FAILURE_TYPE_IP_NOT_ALLOWED = 22;
  // Prestored Transaction if not allowed for the product will be declined under this
  // category.
  TRANSACTION_FAILURE_TYPE_PRESTORED_NOT_ALLOWED = 23;
  // Dynamic Currency Conversion (DCC) if not allowed for the product will be declined under this category.
  // This is usually in place for international transactions where the merchant himself adds markup to the actual transaction
  // amount
  TRANSACTION_FAILURE_TYPE_DCC_NA = 24;
  TRANSACTION_FAILURE_TYPE_LIMIT_BREACH = 25;
  TRANSACTION_FAILURE_TYPE_EMV_FAIL = 26;
  TRANSACTION_FAILURE_TYPE_CAVV_FAIL = 27;
  // No expected authentication values present to honor the transaction, Transaction without CVV2 in scope not allowed.
  TRANSACTION_FAILURE_TYPE_CVV2_FAIL = 28;
  TRANSACTION_FAILURE_TYPE_CVV_FAIL = 29;
  // Invalid expiry date passed by the merchant during ECOM transaction
  TRANSACTION_FAILURE_TYPE_INVALID_EXPIRY_DATE = 30;
  // CVV2 not passed by the merchant during ECOM transaction
  TRANSACTION_FAILURE_TYPE_INVALID_CAVV = 31;
  // If a transaction is failed at authentication level due to preference failure
  TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE = 32;
  // When the per transaction amount exceeds the allowed limit, For E.g. If Maximum per transaction
  // amount is set to be Rs.10000 and if the transaction is attempted to Rs.15000 then transaction will be declined under
  // this category
  TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT = 33;
  // When the customer does not sufficient funds in the wallet
  TRANSACTION_FAILURE_TYPE_INSUFFICIENT_FUND = 34;
  // Transactions being declined with Incorrect PIN
  TRANSACTION_FAILURE_TYPE_INVALID_PIN = 35;
  // INVALID MAC error only occurs on ATM transactions where the MAC value generated during the transaction does not
  // match with the expected value.
  TRANSACTION_FAILURE_TYPE_INVALID_MAC = 36;
  // Pin retry attempts exceeded, If the customer inputs incorrect PIN for 3 times, his card activities will be blocked
  // for 24 hours, and no transactions will be allowed on his/her card.
  TRANSACTION_FAILURE_TYPE_PIN_RETRIES_EXCEEDED = 37;
  TRANSACTION_FAILURE_TYPE_MCC_NOT_ALLOWED = 38;
  // When the cumulative debit Limit of the customer is exceeded.
  TRANSACTION_FAILURE_TYPE_DEBIT_TXN_LIMIT_EXCEEDED = 39;
  // When the cumulative credit limit of the customer is exceeded
  TRANSACTION_FAILURE_TYPE_CREDIT_TXN_LIMIT_EXCEEDED = 40;
  // Transaction Count for this transaction type exceeded
  TRANSACTION_FAILURE_TYPE_TXN_COUNT_LIMIT_EXCEEDED = 41;
  // Generic transaction failure due to some error
  TRANSACTION_FAILURE_TYPE_ERROR = 42;
  // When user hasn't paid credit card bill
  TRANSACTION_FAILURE_TYPE_CREDIT_CARD_BILL_UNPAID = 43;
  // When a user's card is not active
  TRANSACTION_FAILURE_TYPE_CARD_NOT_ACTIVE = 44;
  // Customer preference failed: txnOrigin
  TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE_TXN_ORIGIN = 46;
  // Customer preference failed: INTERNATIONAL
  TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE_INTERNATIONAL = 47;
  // Customer preference failed: CONTACTLESS
  TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE_CONTACTLESS = 48;
  //  TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT for contactless payments
  TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT_CONTACTLESS = 49;
}

enum TransactionAdditionalInfoFieldMask {
  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_UNSPECIFIED = 0;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_ID = 1;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_TRANSACTION_ID = 2;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_PI_TO = 3;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_PI_FROM = 4;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_CREATED_AT = 5;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_UPDATED_AT = 6;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_DELETED_AT = 7;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_ACTOR_FROM = 8;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_ACTOR_TO = 9;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_TXN_TIME = 10;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_ENRICHED_BENEFICIARY_INFO = 11;

  TRANSACTION_ADDITIONAL_INFO_FIELD_MASK_BILL_REF_ID = 12;
}

enum DisputeState {
  DISPUTE_STATE_UNSPECIFIED = 0;
  DISPUTE_STATE_INITIATED = 1;
  DISPUTE_STATE_ACCEPTED = 2;
  DISPUTE_STATE_REJECTED = 3;
}

enum DisputedTransactionFieldMask {
  DISPUTED_TRANSACTION_MASK_UNSPECIFIED = 0;

  DISPUTED_TRANSACTION_FIELD_MASK_ID = 1;

  DISPUTED_TRANSACTION_FIELD_MASK_TRANSACTION_ID = 2;

  DISPUTED_TRANSACTION_FIELD_MASK_ACTOR_ID = 3;

  DISPUTED_TRANSACTION_FIELD_MASK_ACCOUNT_ID = 4;

  DISPUTED_TRANSACTION_FIELD_MASK_EXT_DISPUTE_REF = 5;

  DISPUTED_TRANSACTION_FIELD_MASK_DISPUTE_STATE = 6;

  DISPUTED_TRANSACTION_FIELD_MASK_DISPUTED_AT = 7;

  DISPUTED_TRANSACTION_FIELD_MASK_CREATED_AT = 8;

  DISPUTED_TRANSACTION_FIELD_MASK_UPDATED_AT = 9;

  DISPUTED_TRANSACTION_FIELD_MASK_DELETED_AT = 10;

  DISPUTED_TRANSACTION_FIELD_MASK_DISPUTE_DETAILS = 11;
}

enum TransactionAuthorizationStatus {
  TRANSACTION_AUTHORIZATION_STATUS_UNSPECIFIED = 0;

  TRANSACTION_AUTHORIZATION_STATUS_AUTHORIZED = 1;

  TRANSACTION_AUTHORIZATION_STATUS_SETTLED = 2;

  TRANSACTION_AUTHORIZATION_STATUS_UNSETTLED = 3;

  TRANSACTION_AUTHORIZATION_STATUS_REVERSED = 4;
}

enum AcsTransactionType {
  ACS_TRANSACTION_TYPE_UNSPECIFIED = 0;

  ACS_TRANSACTION_TYPE_PURCHASE = 1;
}
