syntax = "proto3";

package firefly.workflow;

import "api/firefly/enums/enums.proto";
import "api/firefly/internal/card_request.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/vendorgateway/vendor.proto";
import "google/type/date.proto";
import "google/type/money.proto";


option go_package = "github.com/epifi/gamma/api/firefly/workflow";
option java_package = "com.github.epifi.gamma.api.firefly.workflow";

// Workflow payload for freeze/unfreeze request
message FreezeUnfreezePayload {
  // request type indicating if its freeze or unfreeze request
  firefly.enums.CardRequestType request_type = 1;
  // origin of the request
  firefly.enums.Provenance provenance = 2;
}

// Workflow payload for card request status update request
message UpdateCardRequestStatusPayload {
  // card request state to update
  firefly.enums.CardRequestStatus state_to_update = 1;
}


message ReissueCardPayload {
  // card id of the card which needs to be blocked
  string card_id = 1;
}

message IssuePhysicalCreditCardPayload {
  string card_id = 1;
  string card_req_id = 2;
}

// Workflow payload for card limit request
message SetCardLimitsPayload {
  bool is_auth_required = 1;
  string credit_card_id = 2;
}

// Workflow payload for card usage request
message SetCardUsagePayload {
  bool is_auth_required = 1;
  string credit_card_id = 2;
}

message ResetCardPinPayload {
  string credit_card_id = 1;
  string card_request_id = 2;
}

message CreditCardOnboardingPayload {
  string card_req_id = 1;
  bool skip_bill_gen_date_capture_stage = 2;
}

// Workflow Payload for bill generation
message BillGenerationPayload {
  string reference_id = 1;
  google.type.Date statement_date = 2;
  vendorgateway.Vendor vendor = 3;
  string card_request_id = 4;
  string actor_id = 5;
  string card_id = 6;
}

// Workflow payload for payments flow
message CreditCardPaymentPayload {
  string orchestration_id = 1;
  string card_request_id = 2;
  string actor_id = 3;
  google.type.Money amount = 4;
  string card_id = 5;
}

// Workflow payload for export statement
message ExportStatementPayload {
  string card_request_id = 1;
  string bill_id = 2;
}

// Workflow payload for claiming welcome offer
message ProcessWelcomeOfferPayload {
  string reward_id = 1;
  string reward_option_id = 2;
  string card_request_id = 3;
}

message RealtimeCardEligibilityCheckRequest {
  string actor_id = 1;
  string card_request_id = 2;
}

// workflow payload for realtime profile validation check for a user
// we will not call any vendor api here and just run profile validation rules on our end to check if user is good user
// from a credit risk perspective
message RealtimeProfileValidationCheck {
  string actor_id = 1;
  string card_request_id = 2;
  // client req id with which auth is already verified for fetching credit score
  string auth_client_req_id = 3;
  // email id of the user
  string email_id = 4;
}

message SyncProxyRequest {
  string target_workflow_id = 1;
}

message SyncProxyResponse {
  frontend.deeplink.Deeplink next_action = 1;
  firefly.CardRequest card_request = 2;
  string error = 3;
}

// Payload for performing user communication.
message PerformUserCommunicationPayload {
  // The unique identifier of the actor initiating the communication.
  string actor_id = 1;
}
