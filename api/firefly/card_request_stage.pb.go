// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/internal/card_request_stage.proto

package firefly

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	enums "github.com/epifi/gamma/api/firefly/enums"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Message to store the stage specific details for a card request
//
//go:generate gen_sql -types=CardRequestStageExecutionDetails,EmailStatementDetails,PaymentReminderNotificationDetails
type CardRequestStage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// card request identifier for which stage processing is being done
	CardRequestId string `protobuf:"bytes,2,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
	// Orchestration identifier which has started this execution
	OrchestrationId string `protobuf:"bytes,3,opt,name=orchestration_id,json=orchestrationId,proto3" json:"orchestration_id,omitempty"`
	// Id to uniquely identify card request at vendor
	ExternalRequestId string `protobuf:"bytes,4,opt,name=external_request_id,json=externalRequestId,proto3" json:"external_request_id,omitempty"`
	// current stage of the card request
	Stage enums.CardRequestStageName `protobuf:"varint,5,opt,name=stage,proto3,enum=firefly.enums.CardRequestStageName" json:"stage,omitempty"`
	// Metadata for a given stage
	StageExecutionDetails *CardRequestStageExecutionDetails `protobuf:"bytes,6,opt,name=stage_execution_details,json=stageExecutionDetails,proto3" json:"stage_execution_details,omitempty"`
	// Card request type
	Workflow enums.CardRequestWorkFlow `protobuf:"varint,7,opt,name=workflow,proto3,enum=firefly.enums.CardRequestWorkFlow" json:"workflow,omitempty"`
	// Status of the request
	Status enums.CardRequestStageStatus `protobuf:"varint,8,opt,name=status,proto3,enum=firefly.enums.CardRequestStageStatus" json:"status,omitempty"`
	// granular info on status
	SubStatus enums.CardRequestStageSubStatus `protobuf:"varint,9,opt,name=sub_status,json=subStatus,proto3,enum=firefly.enums.CardRequestStageSubStatus" json:"sub_status,omitempty"`
	// will be used to make step stale so that re-execution of the step can be done
	StaledAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=staled_at,json=staledAt,proto3" json:"staled_at,omitempty"`
	// timestamp denoting the completion of this stage
	CompletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt   *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *CardRequestStage) Reset() {
	*x = CardRequestStage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequestStage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequestStage) ProtoMessage() {}

func (x *CardRequestStage) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequestStage.ProtoReflect.Descriptor instead.
func (*CardRequestStage) Descriptor() ([]byte, []int) {
	return file_api_firefly_internal_card_request_stage_proto_rawDescGZIP(), []int{0}
}

func (x *CardRequestStage) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CardRequestStage) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

func (x *CardRequestStage) GetOrchestrationId() string {
	if x != nil {
		return x.OrchestrationId
	}
	return ""
}

func (x *CardRequestStage) GetExternalRequestId() string {
	if x != nil {
		return x.ExternalRequestId
	}
	return ""
}

func (x *CardRequestStage) GetStage() enums.CardRequestStageName {
	if x != nil {
		return x.Stage
	}
	return enums.CardRequestStageName(0)
}

func (x *CardRequestStage) GetStageExecutionDetails() *CardRequestStageExecutionDetails {
	if x != nil {
		return x.StageExecutionDetails
	}
	return nil
}

func (x *CardRequestStage) GetWorkflow() enums.CardRequestWorkFlow {
	if x != nil {
		return x.Workflow
	}
	return enums.CardRequestWorkFlow(0)
}

func (x *CardRequestStage) GetStatus() enums.CardRequestStageStatus {
	if x != nil {
		return x.Status
	}
	return enums.CardRequestStageStatus(0)
}

func (x *CardRequestStage) GetSubStatus() enums.CardRequestStageSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return enums.CardRequestStageSubStatus(0)
}

func (x *CardRequestStage) GetStaledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StaledAt
	}
	return nil
}

func (x *CardRequestStage) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *CardRequestStage) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardRequestStage) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardRequestStage) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type CardRequestStageExecutionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Data:
	//
	//	*CardRequestStageExecutionDetails_EmailStatementDetails
	//	*CardRequestStageExecutionDetails_PaymentReminderNotificationDetails
	//	*CardRequestStageExecutionDetails_CardUsageDetails
	//	*CardRequestStageExecutionDetails_InhouseBreDetails
	Data isCardRequestStageExecutionDetails_Data `protobuf_oneof:"Data"`
}

func (x *CardRequestStageExecutionDetails) Reset() {
	*x = CardRequestStageExecutionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardRequestStageExecutionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardRequestStageExecutionDetails) ProtoMessage() {}

func (x *CardRequestStageExecutionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardRequestStageExecutionDetails.ProtoReflect.Descriptor instead.
func (*CardRequestStageExecutionDetails) Descriptor() ([]byte, []int) {
	return file_api_firefly_internal_card_request_stage_proto_rawDescGZIP(), []int{1}
}

func (m *CardRequestStageExecutionDetails) GetData() isCardRequestStageExecutionDetails_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (x *CardRequestStageExecutionDetails) GetEmailStatementDetails() *EmailStatementDetails {
	if x, ok := x.GetData().(*CardRequestStageExecutionDetails_EmailStatementDetails); ok {
		return x.EmailStatementDetails
	}
	return nil
}

func (x *CardRequestStageExecutionDetails) GetPaymentReminderNotificationDetails() *PaymentReminderNotificationDetails {
	if x, ok := x.GetData().(*CardRequestStageExecutionDetails_PaymentReminderNotificationDetails); ok {
		return x.PaymentReminderNotificationDetails
	}
	return nil
}

func (x *CardRequestStageExecutionDetails) GetCardUsageDetails() *CardUsageDetails {
	if x, ok := x.GetData().(*CardRequestStageExecutionDetails_CardUsageDetails); ok {
		return x.CardUsageDetails
	}
	return nil
}

func (x *CardRequestStageExecutionDetails) GetInhouseBreDetails() *InhouseBreDetails {
	if x, ok := x.GetData().(*CardRequestStageExecutionDetails_InhouseBreDetails); ok {
		return x.InhouseBreDetails
	}
	return nil
}

type isCardRequestStageExecutionDetails_Data interface {
	isCardRequestStageExecutionDetails_Data()
}

type CardRequestStageExecutionDetails_EmailStatementDetails struct {
	EmailStatementDetails *EmailStatementDetails `protobuf:"bytes,1,opt,name=email_statement_details,json=emailStatementDetails,proto3,oneof"`
}

type CardRequestStageExecutionDetails_PaymentReminderNotificationDetails struct {
	PaymentReminderNotificationDetails *PaymentReminderNotificationDetails `protobuf:"bytes,2,opt,name=payment_reminder_notification_details,json=paymentReminderNotificationDetails,proto3,oneof"`
}

type CardRequestStageExecutionDetails_CardUsageDetails struct {
	CardUsageDetails *CardUsageDetails `protobuf:"bytes,3,opt,name=card_usage_details,json=cardUsageDetails,proto3,oneof"`
}

type CardRequestStageExecutionDetails_InhouseBreDetails struct {
	InhouseBreDetails *InhouseBreDetails `protobuf:"bytes,4,opt,name=inhouse_bre_details,json=inhouseBreDetails,proto3,oneof"`
}

func (*CardRequestStageExecutionDetails_EmailStatementDetails) isCardRequestStageExecutionDetails_Data() {
}

func (*CardRequestStageExecutionDetails_PaymentReminderNotificationDetails) isCardRequestStageExecutionDetails_Data() {
}

func (*CardRequestStageExecutionDetails_CardUsageDetails) isCardRequestStageExecutionDetails_Data() {}

func (*CardRequestStageExecutionDetails_InhouseBreDetails) isCardRequestStageExecutionDetails_Data() {
}

type InhouseBreDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RejectionReasons []string           `protobuf:"bytes,1,rep,name=rejection_reasons,json=rejectionReasons,proto3" json:"rejection_reasons,omitempty"`
	IsOverride       common.BooleanEnum `protobuf:"varint,2,opt,name=is_override,json=isOverride,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_override,omitempty"`
}

func (x *InhouseBreDetails) Reset() {
	*x = InhouseBreDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InhouseBreDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InhouseBreDetails) ProtoMessage() {}

func (x *InhouseBreDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InhouseBreDetails.ProtoReflect.Descriptor instead.
func (*InhouseBreDetails) Descriptor() ([]byte, []int) {
	return file_api_firefly_internal_card_request_stage_proto_rawDescGZIP(), []int{2}
}

func (x *InhouseBreDetails) GetRejectionReasons() []string {
	if x != nil {
		return x.RejectionReasons
	}
	return nil
}

func (x *InhouseBreDetails) GetIsOverride() common.BooleanEnum {
	if x != nil {
		return x.IsOverride
	}
	return common.BooleanEnum(0)
}

type EmailStatementDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommsMessageId string `protobuf:"bytes,1,opt,name=comms_message_id,json=commsMessageId,proto3" json:"comms_message_id,omitempty"`
}

func (x *EmailStatementDetails) Reset() {
	*x = EmailStatementDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailStatementDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailStatementDetails) ProtoMessage() {}

func (x *EmailStatementDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailStatementDetails.ProtoReflect.Descriptor instead.
func (*EmailStatementDetails) Descriptor() ([]byte, []int) {
	return file_api_firefly_internal_card_request_stage_proto_rawDescGZIP(), []int{3}
}

func (x *EmailStatementDetails) GetCommsMessageId() string {
	if x != nil {
		return x.CommsMessageId
	}
	return ""
}

type PaymentReminderNotificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommsMessageIds []string `protobuf:"bytes,2,rep,name=comms_message_ids,json=commsMessageIds,proto3" json:"comms_message_ids,omitempty"`
}

func (x *PaymentReminderNotificationDetails) Reset() {
	*x = PaymentReminderNotificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentReminderNotificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentReminderNotificationDetails) ProtoMessage() {}

func (x *PaymentReminderNotificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentReminderNotificationDetails.ProtoReflect.Descriptor instead.
func (*PaymentReminderNotificationDetails) Descriptor() ([]byte, []int) {
	return file_api_firefly_internal_card_request_stage_proto_rawDescGZIP(), []int{4}
}

func (x *PaymentReminderNotificationDetails) GetCommsMessageIds() []string {
	if x != nil {
		return x.CommsMessageIds
	}
	return nil
}

type CardUsageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsFirstEnable bool         `protobuf:"varint,1,opt,name=is_first_enable,json=isFirstEnable,proto3" json:"is_first_enable,omitempty"`
	Limit         *money.Money `protobuf:"bytes,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *CardUsageDetails) Reset() {
	*x = CardUsageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardUsageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardUsageDetails) ProtoMessage() {}

func (x *CardUsageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_internal_card_request_stage_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardUsageDetails.ProtoReflect.Descriptor instead.
func (*CardUsageDetails) Descriptor() ([]byte, []int) {
	return file_api_firefly_internal_card_request_stage_proto_rawDescGZIP(), []int{5}
}

func (x *CardUsageDetails) GetIsFirstEnable() bool {
	if x != nil {
		return x.IsFirstEnable
	}
	return false
}

func (x *CardUsageDetails) GetLimit() *money.Money {
	if x != nil {
		return x.Limit
	}
	return nil
}

var File_api_firefly_internal_card_request_stage_proto protoreflect.FileDescriptor

var file_api_firefly_internal_card_request_stage_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x06, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x29, 0x0a, 0x10, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x72, 0x63, 0x68,
	0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x61, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x67, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x15, 0x73, 0x74, 0x61, 0x67, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x08, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x46, 0x6c, 0x6f, 0x77, 0x52,
	0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x75, 0x62,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x37, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x08, 0x73, 0x74, 0x61, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xa0, 0x03, 0x0a, 0x20, 0x43,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x58, 0x0a, 0x17, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x48, 0x00, 0x52, 0x15, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x80, 0x01, 0x0a, 0x25, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x22, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x49, 0x0a, 0x12,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x55, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x10, 0x63, 0x61, 0x72, 0x64, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x13, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x49,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x42, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x48, 0x00, 0x52, 0x11, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x42, 0x72, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x06, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x22, 0x82, 0x01,
	0x0a, 0x11, 0x49, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x42, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73,
	0x12, 0x40, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x69, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69,
	0x64, 0x65, 0x22, 0x41, 0x0a, 0x15, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x50, 0x0a, 0x22, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x49, 0x64, 0x73, 0x22, 0x64, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x42, 0x48, 0x0a,
	0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_internal_card_request_stage_proto_rawDescOnce sync.Once
	file_api_firefly_internal_card_request_stage_proto_rawDescData = file_api_firefly_internal_card_request_stage_proto_rawDesc
)

func file_api_firefly_internal_card_request_stage_proto_rawDescGZIP() []byte {
	file_api_firefly_internal_card_request_stage_proto_rawDescOnce.Do(func() {
		file_api_firefly_internal_card_request_stage_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_internal_card_request_stage_proto_rawDescData)
	})
	return file_api_firefly_internal_card_request_stage_proto_rawDescData
}

var file_api_firefly_internal_card_request_stage_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_firefly_internal_card_request_stage_proto_goTypes = []interface{}{
	(*CardRequestStage)(nil),                   // 0: firefly.CardRequestStage
	(*CardRequestStageExecutionDetails)(nil),   // 1: firefly.CardRequestStageExecutionDetails
	(*InhouseBreDetails)(nil),                  // 2: firefly.InhouseBreDetails
	(*EmailStatementDetails)(nil),              // 3: firefly.EmailStatementDetails
	(*PaymentReminderNotificationDetails)(nil), // 4: firefly.PaymentReminderNotificationDetails
	(*CardUsageDetails)(nil),                   // 5: firefly.CardUsageDetails
	(enums.CardRequestStageName)(0),            // 6: firefly.enums.CardRequestStageName
	(enums.CardRequestWorkFlow)(0),             // 7: firefly.enums.CardRequestWorkFlow
	(enums.CardRequestStageStatus)(0),          // 8: firefly.enums.CardRequestStageStatus
	(enums.CardRequestStageSubStatus)(0),       // 9: firefly.enums.CardRequestStageSubStatus
	(*timestamppb.Timestamp)(nil),              // 10: google.protobuf.Timestamp
	(common.BooleanEnum)(0),                    // 11: api.typesv2.common.BooleanEnum
	(*money.Money)(nil),                        // 12: google.type.Money
}
var file_api_firefly_internal_card_request_stage_proto_depIdxs = []int32{
	6,  // 0: firefly.CardRequestStage.stage:type_name -> firefly.enums.CardRequestStageName
	1,  // 1: firefly.CardRequestStage.stage_execution_details:type_name -> firefly.CardRequestStageExecutionDetails
	7,  // 2: firefly.CardRequestStage.workflow:type_name -> firefly.enums.CardRequestWorkFlow
	8,  // 3: firefly.CardRequestStage.status:type_name -> firefly.enums.CardRequestStageStatus
	9,  // 4: firefly.CardRequestStage.sub_status:type_name -> firefly.enums.CardRequestStageSubStatus
	10, // 5: firefly.CardRequestStage.staled_at:type_name -> google.protobuf.Timestamp
	10, // 6: firefly.CardRequestStage.completed_at:type_name -> google.protobuf.Timestamp
	10, // 7: firefly.CardRequestStage.created_at:type_name -> google.protobuf.Timestamp
	10, // 8: firefly.CardRequestStage.updated_at:type_name -> google.protobuf.Timestamp
	10, // 9: firefly.CardRequestStage.deleted_at:type_name -> google.protobuf.Timestamp
	3,  // 10: firefly.CardRequestStageExecutionDetails.email_statement_details:type_name -> firefly.EmailStatementDetails
	4,  // 11: firefly.CardRequestStageExecutionDetails.payment_reminder_notification_details:type_name -> firefly.PaymentReminderNotificationDetails
	5,  // 12: firefly.CardRequestStageExecutionDetails.card_usage_details:type_name -> firefly.CardUsageDetails
	2,  // 13: firefly.CardRequestStageExecutionDetails.inhouse_bre_details:type_name -> firefly.InhouseBreDetails
	11, // 14: firefly.InhouseBreDetails.is_override:type_name -> api.typesv2.common.BooleanEnum
	12, // 15: firefly.CardUsageDetails.limit:type_name -> google.type.Money
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_firefly_internal_card_request_stage_proto_init() }
func file_api_firefly_internal_card_request_stage_proto_init() {
	if File_api_firefly_internal_card_request_stage_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_firefly_internal_card_request_stage_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequestStage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_internal_card_request_stage_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardRequestStageExecutionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_internal_card_request_stage_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InhouseBreDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_internal_card_request_stage_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailStatementDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_internal_card_request_stage_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentReminderNotificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_internal_card_request_stage_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardUsageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_firefly_internal_card_request_stage_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*CardRequestStageExecutionDetails_EmailStatementDetails)(nil),
		(*CardRequestStageExecutionDetails_PaymentReminderNotificationDetails)(nil),
		(*CardRequestStageExecutionDetails_CardUsageDetails)(nil),
		(*CardRequestStageExecutionDetails_InhouseBreDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_internal_card_request_stage_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_firefly_internal_card_request_stage_proto_goTypes,
		DependencyIndexes: file_api_firefly_internal_card_request_stage_proto_depIdxs,
		MessageInfos:      file_api_firefly_internal_card_request_stage_proto_msgTypes,
	}.Build()
	File_api_firefly_internal_card_request_stage_proto = out.File
	file_api_firefly_internal_card_request_stage_proto_rawDesc = nil
	file_api_firefly_internal_card_request_stage_proto_goTypes = nil
	file_api_firefly_internal_card_request_stage_proto_depIdxs = nil
}
