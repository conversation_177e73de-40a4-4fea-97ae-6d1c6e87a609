// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/billing/internal/credit_card_bill.proto

package billing

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/billing/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.MerchantRewardType(0)
)

// Validate checks the field values on CreditCardBill with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreditCardBill) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditCardBill with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CreditCardBillMultiError,
// or nil if none found.
func (m *CreditCardBill) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardBill) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetLastStatementBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "LastStatementBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "LastStatementBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastStatementBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "LastStatementBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentStatementAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "CurrentStatementAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "CurrentStatementAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentStatementAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "CurrentStatementAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalCredit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "TotalCredit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "TotalCredit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalCredit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "TotalCredit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalDebit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "TotalDebit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "TotalDebit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDebit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "TotalDebit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCash()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "Cash",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "Cash",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCash()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "Cash",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPurchase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "Purchase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "Purchase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPurchase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "Purchase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "MinDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "MinDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "MinDue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "TotalDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "TotalDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "TotalDue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatementDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "StatementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "StatementDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatementDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "StatementDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSoftDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "SoftDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "SoftDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSoftDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "SoftDueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHardDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "HardDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "HardDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHardDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "HardDueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardsInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "RewardsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "RewardsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardsInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "RewardsInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnalyticsInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "AnalyticsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "AnalyticsInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalyticsInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "AnalyticsInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for S3Path

	if all {
		switch v := interface{}(m.GetStatementSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "StatementSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "StatementSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatementSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "StatementSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillValidationError{
					field:  "AvailableLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillValidationError{
				field:  "AvailableLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardId

	if len(errors) > 0 {
		return CreditCardBillMultiError(errors)
	}

	return nil
}

// CreditCardBillMultiError is an error wrapping multiple validation errors
// returned by CreditCardBill.ValidateAll() if the designated constraints
// aren't met.
type CreditCardBillMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardBillMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardBillMultiError) AllErrors() []error { return m }

// CreditCardBillValidationError is the validation error returned by
// CreditCardBill.Validate if the designated constraints aren't met.
type CreditCardBillValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardBillValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardBillValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardBillValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardBillValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardBillValidationError) ErrorName() string { return "CreditCardBillValidationError" }

// Error satisfies the builtin error interface
func (e CreditCardBillValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardBill.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardBillValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardBillValidationError{}

// Validate checks the field values on AnalyticsInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AnalyticsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalyticsInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnalyticsInfoMultiError, or
// nil if none found.
func (m *AnalyticsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalyticsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AnalyticsInfoMultiError(errors)
	}

	return nil
}

// AnalyticsInfoMultiError is an error wrapping multiple validation errors
// returned by AnalyticsInfo.ValidateAll() if the designated constraints
// aren't met.
type AnalyticsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalyticsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalyticsInfoMultiError) AllErrors() []error { return m }

// AnalyticsInfoValidationError is the validation error returned by
// AnalyticsInfo.Validate if the designated constraints aren't met.
type AnalyticsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalyticsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalyticsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalyticsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalyticsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalyticsInfoValidationError) ErrorName() string { return "AnalyticsInfoValidationError" }

// Error satisfies the builtin error interface
func (e AnalyticsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalyticsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalyticsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalyticsInfoValidationError{}

// Validate checks the field values on RewardsInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardsInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RewardsInfoMultiError, or
// nil if none found.
func (m *RewardsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalRewardsCoins

	// no validation rules for RewardCoinsEarned

	for idx, item := range m.GetTopMerchantRewards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RewardsInfoValidationError{
						field:  fmt.Sprintf("TopMerchantRewards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RewardsInfoValidationError{
						field:  fmt.Sprintf("TopMerchantRewards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RewardsInfoValidationError{
					field:  fmt.Sprintf("TopMerchantRewards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetExtraRewardsConstructInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardsInfoValidationError{
					field:  "ExtraRewardsConstructInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardsInfoValidationError{
					field:  "ExtraRewardsConstructInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtraRewardsConstructInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardsInfoValidationError{
				field:  "ExtraRewardsConstructInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecuredCardsRewardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardsInfoValidationError{
					field:  "SecuredCardsRewardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardsInfoValidationError{
					field:  "SecuredCardsRewardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecuredCardsRewardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardsInfoValidationError{
				field:  "SecuredCardsRewardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardsConstructInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardsInfoValidationError{
					field:  "RewardsConstructInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardsInfoValidationError{
					field:  "RewardsConstructInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardsConstructInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardsInfoValidationError{
				field:  "RewardsConstructInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardsInfoMultiError(errors)
	}

	return nil
}

// RewardsInfoMultiError is an error wrapping multiple validation errors
// returned by RewardsInfo.ValidateAll() if the designated constraints aren't met.
type RewardsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardsInfoMultiError) AllErrors() []error { return m }

// RewardsInfoValidationError is the validation error returned by
// RewardsInfo.Validate if the designated constraints aren't met.
type RewardsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardsInfoValidationError) ErrorName() string { return "RewardsInfoValidationError" }

// Error satisfies the builtin error interface
func (e RewardsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardsInfoValidationError{}

// Validate checks the field values on MerchantRewardsInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MerchantRewardsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MerchantRewardsInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MerchantRewardsInfoMultiError, or nil if none found.
func (m *MerchantRewardsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MerchantRewardsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MerchantId

	// no validation rules for TotalRewardCoins

	if all {
		switch v := interface{}(m.GetAmountSpent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MerchantRewardsInfoValidationError{
					field:  "AmountSpent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MerchantRewardsInfoValidationError{
					field:  "AmountSpent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountSpent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MerchantRewardsInfoValidationError{
				field:  "AmountSpent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MerchantRewardType

	// no validation rules for MerchantName

	if len(errors) > 0 {
		return MerchantRewardsInfoMultiError(errors)
	}

	return nil
}

// MerchantRewardsInfoMultiError is an error wrapping multiple validation
// errors returned by MerchantRewardsInfo.ValidateAll() if the designated
// constraints aren't met.
type MerchantRewardsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MerchantRewardsInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MerchantRewardsInfoMultiError) AllErrors() []error { return m }

// MerchantRewardsInfoValidationError is the validation error returned by
// MerchantRewardsInfo.Validate if the designated constraints aren't met.
type MerchantRewardsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MerchantRewardsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MerchantRewardsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MerchantRewardsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MerchantRewardsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MerchantRewardsInfoValidationError) ErrorName() string {
	return "MerchantRewardsInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MerchantRewardsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMerchantRewardsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MerchantRewardsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MerchantRewardsInfoValidationError{}

// Validate checks the field values on ExtraRewardsConstructInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExtraRewardsConstructInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExtraRewardsConstructInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExtraRewardsConstructInfoMultiError, or nil if none found.
func (m *ExtraRewardsConstructInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ExtraRewardsConstructInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Projected_2XRewardsCoins

	// no validation rules for Projected_5XRewardsCoins

	// no validation rules for Total_1XRewardsCoins

	// no validation rules for TotalRewardCoins

	if len(errors) > 0 {
		return ExtraRewardsConstructInfoMultiError(errors)
	}

	return nil
}

// ExtraRewardsConstructInfoMultiError is an error wrapping multiple validation
// errors returned by ExtraRewardsConstructInfo.ValidateAll() if the
// designated constraints aren't met.
type ExtraRewardsConstructInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExtraRewardsConstructInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExtraRewardsConstructInfoMultiError) AllErrors() []error { return m }

// ExtraRewardsConstructInfoValidationError is the validation error returned by
// ExtraRewardsConstructInfo.Validate if the designated constraints aren't met.
type ExtraRewardsConstructInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExtraRewardsConstructInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExtraRewardsConstructInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExtraRewardsConstructInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExtraRewardsConstructInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExtraRewardsConstructInfoValidationError) ErrorName() string {
	return "ExtraRewardsConstructInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ExtraRewardsConstructInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExtraRewardsConstructInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExtraRewardsConstructInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExtraRewardsConstructInfoValidationError{}

// Validate checks the field values on SecuredCardsRewardInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecuredCardsRewardInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecuredCardsRewardInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecuredCardsRewardInfoMultiError, or nil if none found.
func (m *SecuredCardsRewardInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SecuredCardsRewardInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectedWeekdayRewardCoins

	// no validation rules for ProjectedWeekendRewardCoins

	// no validation rules for ProjectedTotalRewardCoins

	if len(errors) > 0 {
		return SecuredCardsRewardInfoMultiError(errors)
	}

	return nil
}

// SecuredCardsRewardInfoMultiError is an error wrapping multiple validation
// errors returned by SecuredCardsRewardInfo.ValidateAll() if the designated
// constraints aren't met.
type SecuredCardsRewardInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecuredCardsRewardInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecuredCardsRewardInfoMultiError) AllErrors() []error { return m }

// SecuredCardsRewardInfoValidationError is the validation error returned by
// SecuredCardsRewardInfo.Validate if the designated constraints aren't met.
type SecuredCardsRewardInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecuredCardsRewardInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecuredCardsRewardInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecuredCardsRewardInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecuredCardsRewardInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecuredCardsRewardInfoValidationError) ErrorName() string {
	return "SecuredCardsRewardInfoValidationError"
}

// Error satisfies the builtin error interface
func (e SecuredCardsRewardInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecuredCardsRewardInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecuredCardsRewardInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecuredCardsRewardInfoValidationError{}

// Validate checks the field values on RewardsConstructInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardsConstructInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardsConstructInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardsConstructInfoMultiError, or nil if none found.
func (m *RewardsConstructInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardsConstructInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectedBaseRewardCoins

	// no validation rules for ProjectedAcceleratedRewardCoins

	// no validation rules for ProjectedTotalRewardCoins

	if len(errors) > 0 {
		return RewardsConstructInfoMultiError(errors)
	}

	return nil
}

// RewardsConstructInfoMultiError is an error wrapping multiple validation
// errors returned by RewardsConstructInfo.ValidateAll() if the designated
// constraints aren't met.
type RewardsConstructInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardsConstructInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardsConstructInfoMultiError) AllErrors() []error { return m }

// RewardsConstructInfoValidationError is the validation error returned by
// RewardsConstructInfo.Validate if the designated constraints aren't met.
type RewardsConstructInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardsConstructInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardsConstructInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardsConstructInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardsConstructInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardsConstructInfoValidationError) ErrorName() string {
	return "RewardsConstructInfoValidationError"
}

// Error satisfies the builtin error interface
func (e RewardsConstructInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardsConstructInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardsConstructInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardsConstructInfoValidationError{}
