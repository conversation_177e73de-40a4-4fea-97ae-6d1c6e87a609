// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/consumer/service.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.NonFinancialNotificationType(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on
// ProcessNonFinancialNotificationEventRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessNonFinancialNotificationEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessNonFinancialNotificationEventRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessNonFinancialNotificationEventRequestMultiError, or nil if none found.
func (m *ProcessNonFinancialNotificationEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessNonFinancialNotificationEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessNonFinancialNotificationEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessNonFinancialNotificationEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessNonFinancialNotificationEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityId

	// no validation rules for Vendor

	// no validation rules for NotificationType

	// no validation rules for Dpd

	if len(errors) > 0 {
		return ProcessNonFinancialNotificationEventRequestMultiError(errors)
	}

	return nil
}

// ProcessNonFinancialNotificationEventRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessNonFinancialNotificationEventRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessNonFinancialNotificationEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessNonFinancialNotificationEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessNonFinancialNotificationEventRequestMultiError) AllErrors() []error { return m }

// ProcessNonFinancialNotificationEventRequestValidationError is the validation
// error returned by ProcessNonFinancialNotificationEventRequest.Validate if
// the designated constraints aren't met.
type ProcessNonFinancialNotificationEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessNonFinancialNotificationEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessNonFinancialNotificationEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessNonFinancialNotificationEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessNonFinancialNotificationEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessNonFinancialNotificationEventRequestValidationError) ErrorName() string {
	return "ProcessNonFinancialNotificationEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessNonFinancialNotificationEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessNonFinancialNotificationEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessNonFinancialNotificationEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessNonFinancialNotificationEventRequestValidationError{}

// Validate checks the field values on
// ProcessNonFinancialNotificationEventResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessNonFinancialNotificationEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessNonFinancialNotificationEventResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessNonFinancialNotificationEventResponseMultiError, or nil if none found.
func (m *ProcessNonFinancialNotificationEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessNonFinancialNotificationEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessNonFinancialNotificationEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessNonFinancialNotificationEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessNonFinancialNotificationEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessNonFinancialNotificationEventResponseMultiError(errors)
	}

	return nil
}

// ProcessNonFinancialNotificationEventResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessNonFinancialNotificationEventResponse.ValidateAll() if the
// designated constraints aren't met.
type ProcessNonFinancialNotificationEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessNonFinancialNotificationEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessNonFinancialNotificationEventResponseMultiError) AllErrors() []error { return m }

// ProcessNonFinancialNotificationEventResponseValidationError is the
// validation error returned by
// ProcessNonFinancialNotificationEventResponse.Validate if the designated
// constraints aren't met.
type ProcessNonFinancialNotificationEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessNonFinancialNotificationEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessNonFinancialNotificationEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessNonFinancialNotificationEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessNonFinancialNotificationEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessNonFinancialNotificationEventResponseValidationError) ErrorName() string {
	return "ProcessNonFinancialNotificationEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessNonFinancialNotificationEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessNonFinancialNotificationEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessNonFinancialNotificationEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessNonFinancialNotificationEventResponseValidationError{}

// Validate checks the field values on ProcessCreditReportDownloadEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCreditReportDownloadEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCreditReportDownloadEventResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessCreditReportDownloadEventResponseMultiError, or nil if none found.
func (m *ProcessCreditReportDownloadEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCreditReportDownloadEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCreditReportDownloadEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCreditReportDownloadEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCreditReportDownloadEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCreditReportDownloadEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCreditReportDownloadEventResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCreditReportDownloadEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCreditReportDownloadEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCreditReportDownloadEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCreditReportDownloadEventResponseMultiError) AllErrors() []error { return m }

// ProcessCreditReportDownloadEventResponseValidationError is the validation
// error returned by ProcessCreditReportDownloadEventResponse.Validate if the
// designated constraints aren't met.
type ProcessCreditReportDownloadEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCreditReportDownloadEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCreditReportDownloadEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCreditReportDownloadEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCreditReportDownloadEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCreditReportDownloadEventResponseValidationError) ErrorName() string {
	return "ProcessCreditReportDownloadEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCreditReportDownloadEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCreditReportDownloadEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCreditReportDownloadEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCreditReportDownloadEventResponseValidationError{}

// Validate checks the field values on ProcessAuthFactorUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessAuthFactorUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAuthFactorUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessAuthFactorUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessAuthFactorUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAuthFactorUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAuthFactorUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAuthFactorUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAuthFactorUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAuthFactorUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessAuthFactorUpdateEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessAuthFactorUpdateEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessAuthFactorUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAuthFactorUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAuthFactorUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessAuthFactorUpdateEventResponseValidationError is the validation error
// returned by ProcessAuthFactorUpdateEventResponse.Validate if the designated
// constraints aren't met.
type ProcessAuthFactorUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAuthFactorUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAuthFactorUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessAuthFactorUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAuthFactorUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAuthFactorUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAuthFactorUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAuthFactorUpdateEventResponseValidationError{}

// Validate checks the field values on ProcessEligibleUsersFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessEligibleUsersFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessEligibleUsersFileRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessEligibleUsersFileRequestMultiError, or nil if none found.
func (m *ProcessEligibleUsersFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessEligibleUsersFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEligibleUsersFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEligibleUsersFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEligibleUsersFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessEligibleUsersFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessEligibleUsersFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessEligibleUsersFileRequestValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessEligibleUsersFileRequestMultiError(errors)
	}

	return nil
}

// ProcessEligibleUsersFileRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessEligibleUsersFileRequest.ValidateAll()
// if the designated constraints aren't met.
type ProcessEligibleUsersFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessEligibleUsersFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessEligibleUsersFileRequestMultiError) AllErrors() []error { return m }

// ProcessEligibleUsersFileRequestValidationError is the validation error
// returned by ProcessEligibleUsersFileRequest.Validate if the designated
// constraints aren't met.
type ProcessEligibleUsersFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessEligibleUsersFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessEligibleUsersFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessEligibleUsersFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessEligibleUsersFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessEligibleUsersFileRequestValidationError) ErrorName() string {
	return "ProcessEligibleUsersFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessEligibleUsersFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessEligibleUsersFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessEligibleUsersFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessEligibleUsersFileRequestValidationError{}

// Validate checks the field values on ProcessEligibleUsersFileResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessEligibleUsersFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessEligibleUsersFileResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessEligibleUsersFileResponseMultiError, or nil if none found.
func (m *ProcessEligibleUsersFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessEligibleUsersFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessEligibleUsersFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessEligibleUsersFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessEligibleUsersFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessEligibleUsersFileResponseMultiError(errors)
	}

	return nil
}

// ProcessEligibleUsersFileResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessEligibleUsersFileResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessEligibleUsersFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessEligibleUsersFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessEligibleUsersFileResponseMultiError) AllErrors() []error { return m }

// ProcessEligibleUsersFileResponseValidationError is the validation error
// returned by ProcessEligibleUsersFileResponse.Validate if the designated
// constraints aren't met.
type ProcessEligibleUsersFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessEligibleUsersFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessEligibleUsersFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessEligibleUsersFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessEligibleUsersFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessEligibleUsersFileResponseValidationError) ErrorName() string {
	return "ProcessEligibleUsersFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessEligibleUsersFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessEligibleUsersFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessEligibleUsersFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessEligibleUsersFileResponseValidationError{}

// Validate checks the field values on ProcessCreditCardOfferCsvFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCreditCardOfferCsvFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCreditCardOfferCsvFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCreditCardOfferCsvFileRequestMultiError, or nil if none found.
func (m *ProcessCreditCardOfferCsvFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCreditCardOfferCsvFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCreditCardOfferCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCreditCardOfferCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCreditCardOfferCsvFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessCreditCardOfferCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessCreditCardOfferCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessCreditCardOfferCsvFileRequestValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessCreditCardOfferCsvFileRequestMultiError(errors)
	}

	return nil
}

// ProcessCreditCardOfferCsvFileRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCreditCardOfferCsvFileRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCreditCardOfferCsvFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCreditCardOfferCsvFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCreditCardOfferCsvFileRequestMultiError) AllErrors() []error { return m }

// ProcessCreditCardOfferCsvFileRequestValidationError is the validation error
// returned by ProcessCreditCardOfferCsvFileRequest.Validate if the designated
// constraints aren't met.
type ProcessCreditCardOfferCsvFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCreditCardOfferCsvFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCreditCardOfferCsvFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCreditCardOfferCsvFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCreditCardOfferCsvFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCreditCardOfferCsvFileRequestValidationError) ErrorName() string {
	return "ProcessCreditCardOfferCsvFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCreditCardOfferCsvFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCreditCardOfferCsvFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCreditCardOfferCsvFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCreditCardOfferCsvFileRequestValidationError{}

// Validate checks the field values on ProcessCreditCardOfferCsvFileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCreditCardOfferCsvFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCreditCardOfferCsvFileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCreditCardOfferCsvFileResponseMultiError, or nil if none found.
func (m *ProcessCreditCardOfferCsvFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCreditCardOfferCsvFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCreditCardOfferCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCreditCardOfferCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCreditCardOfferCsvFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCreditCardOfferCsvFileResponseMultiError(errors)
	}

	return nil
}

// ProcessCreditCardOfferCsvFileResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCreditCardOfferCsvFileResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCreditCardOfferCsvFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCreditCardOfferCsvFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCreditCardOfferCsvFileResponseMultiError) AllErrors() []error { return m }

// ProcessCreditCardOfferCsvFileResponseValidationError is the validation error
// returned by ProcessCreditCardOfferCsvFileResponse.Validate if the
// designated constraints aren't met.
type ProcessCreditCardOfferCsvFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCreditCardOfferCsvFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCreditCardOfferCsvFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCreditCardOfferCsvFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCreditCardOfferCsvFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCreditCardOfferCsvFileResponseValidationError) ErrorName() string {
	return "ProcessCreditCardOfferCsvFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCreditCardOfferCsvFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCreditCardOfferCsvFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCreditCardOfferCsvFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCreditCardOfferCsvFileResponseValidationError{}

// Validate checks the field values on
// ProcessCardsSentForPrintingCsvFileRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessCardsSentForPrintingCsvFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardsSentForPrintingCsvFileRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessCardsSentForPrintingCsvFileRequestMultiError, or nil if none found.
func (m *ProcessCardsSentForPrintingCsvFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardsSentForPrintingCsvFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardsSentForPrintingCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardsSentForPrintingCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardsSentForPrintingCsvFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessCardsSentForPrintingCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessCardsSentForPrintingCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessCardsSentForPrintingCsvFileRequestValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessCardsSentForPrintingCsvFileRequestMultiError(errors)
	}

	return nil
}

// ProcessCardsSentForPrintingCsvFileRequestMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardsSentForPrintingCsvFileRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardsSentForPrintingCsvFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardsSentForPrintingCsvFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardsSentForPrintingCsvFileRequestMultiError) AllErrors() []error { return m }

// ProcessCardsSentForPrintingCsvFileRequestValidationError is the validation
// error returned by ProcessCardsSentForPrintingCsvFileRequest.Validate if the
// designated constraints aren't met.
type ProcessCardsSentForPrintingCsvFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardsSentForPrintingCsvFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardsSentForPrintingCsvFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardsSentForPrintingCsvFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardsSentForPrintingCsvFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardsSentForPrintingCsvFileRequestValidationError) ErrorName() string {
	return "ProcessCardsSentForPrintingCsvFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardsSentForPrintingCsvFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardsSentForPrintingCsvFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardsSentForPrintingCsvFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardsSentForPrintingCsvFileRequestValidationError{}

// Validate checks the field values on
// ProcessCardsSentForPrintingCsvFileResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessCardsSentForPrintingCsvFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProcessCardsSentForPrintingCsvFileResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ProcessCardsSentForPrintingCsvFileResponseMultiError, or nil if none found.
func (m *ProcessCardsSentForPrintingCsvFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardsSentForPrintingCsvFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardsSentForPrintingCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardsSentForPrintingCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardsSentForPrintingCsvFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardsSentForPrintingCsvFileResponseMultiError(errors)
	}

	return nil
}

// ProcessCardsSentForPrintingCsvFileResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardsSentForPrintingCsvFileResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardsSentForPrintingCsvFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardsSentForPrintingCsvFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardsSentForPrintingCsvFileResponseMultiError) AllErrors() []error { return m }

// ProcessCardsSentForPrintingCsvFileResponseValidationError is the validation
// error returned by ProcessCardsSentForPrintingCsvFileResponse.Validate if
// the designated constraints aren't met.
type ProcessCardsSentForPrintingCsvFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardsSentForPrintingCsvFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardsSentForPrintingCsvFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardsSentForPrintingCsvFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardsSentForPrintingCsvFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardsSentForPrintingCsvFileResponseValidationError) ErrorName() string {
	return "ProcessCardsSentForPrintingCsvFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardsSentForPrintingCsvFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardsSentForPrintingCsvFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardsSentForPrintingCsvFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardsSentForPrintingCsvFileResponseValidationError{}

// Validate checks the field values on ProcessCardsDispatchedCsvFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardsDispatchedCsvFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardsDispatchedCsvFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardsDispatchedCsvFileRequestMultiError, or nil if none found.
func (m *ProcessCardsDispatchedCsvFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardsDispatchedCsvFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardsDispatchedCsvFileRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessCardsDispatchedCsvFileRequestValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessCardsDispatchedCsvFileRequestValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessCardsDispatchedCsvFileRequestMultiError(errors)
	}

	return nil
}

// ProcessCardsDispatchedCsvFileRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCardsDispatchedCsvFileRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardsDispatchedCsvFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardsDispatchedCsvFileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardsDispatchedCsvFileRequestMultiError) AllErrors() []error { return m }

// ProcessCardsDispatchedCsvFileRequestValidationError is the validation error
// returned by ProcessCardsDispatchedCsvFileRequest.Validate if the designated
// constraints aren't met.
type ProcessCardsDispatchedCsvFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardsDispatchedCsvFileRequestValidationError) ErrorName() string {
	return "ProcessCardsDispatchedCsvFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardsDispatchedCsvFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardsDispatchedCsvFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardsDispatchedCsvFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardsDispatchedCsvFileRequestValidationError{}

// Validate checks the field values on ProcessCardsDispatchedCsvFileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessCardsDispatchedCsvFileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCardsDispatchedCsvFileResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCardsDispatchedCsvFileResponseMultiError, or nil if none found.
func (m *ProcessCardsDispatchedCsvFileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCardsDispatchedCsvFileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCardsDispatchedCsvFileResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCardsDispatchedCsvFileResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCardsDispatchedCsvFileResponseMultiError(errors)
	}

	return nil
}

// ProcessCardsDispatchedCsvFileResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessCardsDispatchedCsvFileResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCardsDispatchedCsvFileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCardsDispatchedCsvFileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCardsDispatchedCsvFileResponseMultiError) AllErrors() []error { return m }

// ProcessCardsDispatchedCsvFileResponseValidationError is the validation error
// returned by ProcessCardsDispatchedCsvFileResponse.Validate if the
// designated constraints aren't met.
type ProcessCardsDispatchedCsvFileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCardsDispatchedCsvFileResponseValidationError) ErrorName() string {
	return "ProcessCardsDispatchedCsvFileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCardsDispatchedCsvFileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCardsDispatchedCsvFileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCardsDispatchedCsvFileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCardsDispatchedCsvFileResponseValidationError{}
