syntax = "proto3";

import "api/rpc/status.proto";
import "api/typesv2/firefly.proto";

package firefly.card_recommendation;

option go_package = "github.com/epifi/gamma/api/firefly/card_recommendation";
option java_package = "com.github.epifi.gamma.api.firefly.card_recommendation";

service CardRecommendationService {
  // EvaluateCardRecommendationsForUser rpc returns recommended card programs for actor
  // returns only card programs for which user is eligible for,
  // card programs will be ranked in decreasing order of there recommendation factor
  rpc EvaluateCardRecommendationsForUser (EvaluateCardRecommendationsForUserRequest) returns (EvaluateCardRecommendationsForUserResponse);
  // FetchCcRecommendationInfoForUser rpc fetches recommendation info for user. If recommendation engine has run multiple time for user,
  // then it returns recommendation info from last run
  rpc FetchCcRecommendationInfoForUser (FetchCcRecommendationInfoForUserRequest) returns (FetchCcRecommendationInfoForUserResponse);
}

message FetchCcRecommendationInfoForUserRequest {
  string actor_id = 1;
}

message FetchCcRecommendationInfoForUserResponse {
  rpc.Status status = 1;
  // card program for which user is eligible to opt for, ranked in decreasing order of recommendation factor
  repeated CardRecommendation card_recommendations = 2;
}

message EvaluateCardRecommendationsForUserRequest {
  string actor_id = 1;
  api.typesv2.CreditCardRequestHeader credit_card_request_header = 2;
}

message EvaluateCardRecommendationsForUserResponse {
  rpc.Status status = 1;
  // card program for which user is eligible to opt for, ranked in decreasing order of recommendation factor
  repeated CardRecommendation card_recommendations = 2;
}

// CardRecommendation holds card program and weightage for that particular card program that was evaluated by card recommendation rule engine
message CardRecommendation {
  api.typesv2.CardProgram card_program = 1;
  float card_program_weightage = 2;
}
