// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/kyc/v2/event.proto

package kyc

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KycStatusUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard header to be added to all requests
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// unique identifier for identifying the kyc attempt, e.g. client request id or uuid of kyc attempt
	Identifier string `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// metadata related to kyc status update
	//
	// Types that are assignable to StatusUpdateEvent:
	//
	//	*KycStatusUpdateEvent_IdfcVkycStatus
	StatusUpdateEvent isKycStatusUpdateEvent_StatusUpdateEvent `protobuf_oneof:"StatusUpdateEvent"`
}

func (x *KycStatusUpdateEvent) Reset() {
	*x = KycStatusUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_kyc_v2_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycStatusUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycStatusUpdateEvent) ProtoMessage() {}

func (x *KycStatusUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_kyc_v2_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycStatusUpdateEvent.ProtoReflect.Descriptor instead.
func (*KycStatusUpdateEvent) Descriptor() ([]byte, []int) {
	return file_api_kyc_v2_event_proto_rawDescGZIP(), []int{0}
}

func (x *KycStatusUpdateEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *KycStatusUpdateEvent) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (m *KycStatusUpdateEvent) GetStatusUpdateEvent() isKycStatusUpdateEvent_StatusUpdateEvent {
	if m != nil {
		return m.StatusUpdateEvent
	}
	return nil
}

func (x *KycStatusUpdateEvent) GetIdfcVkycStatus() *VKYCProfileStatusCallbackEvent {
	if x, ok := x.GetStatusUpdateEvent().(*KycStatusUpdateEvent_IdfcVkycStatus); ok {
		return x.IdfcVkycStatus
	}
	return nil
}

type isKycStatusUpdateEvent_StatusUpdateEvent interface {
	isKycStatusUpdateEvent_StatusUpdateEvent()
}

type KycStatusUpdateEvent_IdfcVkycStatus struct {
	IdfcVkycStatus *VKYCProfileStatusCallbackEvent `protobuf:"bytes,3,opt,name=idfc_vkyc_status,json=idfcVkycStatus,proto3,oneof"`
}

func (*KycStatusUpdateEvent_IdfcVkycStatus) isKycStatusUpdateEvent_StatusUpdateEvent() {}

var File_api_kyc_v2_event_proto protoreflect.FileDescriptor

var file_api_kyc_v2_event_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6b, 0x79, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x6b, 0x79, 0x63, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f,
	0x76, 0x32, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x69, 0x64, 0x66, 0x63, 0x5f, 0x76, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xe8, 0x01, 0x0a, 0x14, 0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1e,
	0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x56,
	0x0a, 0x10, 0x69, 0x64, 0x66, 0x63, 0x5f, 0x76, 0x6b, 0x79, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x76,
	0x32, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x56, 0x4b, 0x59, 0x43, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0e, 0x69, 0x64, 0x66, 0x63, 0x56, 0x6b, 0x79, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x13, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x4e, 0x0a, 0x25, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x76, 0x32,
	0x2e, 0x6b, 0x79, 0x63, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x76, 0x32, 0x2f, 0x6b, 0x79, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_kyc_v2_event_proto_rawDescOnce sync.Once
	file_api_kyc_v2_event_proto_rawDescData = file_api_kyc_v2_event_proto_rawDesc
)

func file_api_kyc_v2_event_proto_rawDescGZIP() []byte {
	file_api_kyc_v2_event_proto_rawDescOnce.Do(func() {
		file_api_kyc_v2_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_kyc_v2_event_proto_rawDescData)
	})
	return file_api_kyc_v2_event_proto_rawDescData
}

var file_api_kyc_v2_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_kyc_v2_event_proto_goTypes = []interface{}{
	(*KycStatusUpdateEvent)(nil),           // 0: kyc.v2.kyc.KycStatusUpdateEvent
	(*queue.ConsumerRequestHeader)(nil),    // 1: queue.ConsumerRequestHeader
	(*VKYCProfileStatusCallbackEvent)(nil), // 2: kyc.v2.kyc.VKYCProfileStatusCallbackEvent
}
var file_api_kyc_v2_event_proto_depIdxs = []int32{
	1, // 0: kyc.v2.kyc.KycStatusUpdateEvent.request_header:type_name -> queue.ConsumerRequestHeader
	2, // 1: kyc.v2.kyc.KycStatusUpdateEvent.idfc_vkyc_status:type_name -> kyc.v2.kyc.VKYCProfileStatusCallbackEvent
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_kyc_v2_event_proto_init() }
func file_api_kyc_v2_event_proto_init() {
	if File_api_kyc_v2_event_proto != nil {
		return
	}
	file_api_kyc_v2_status_update_idfc_vkyc_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_kyc_v2_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycStatusUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_kyc_v2_event_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*KycStatusUpdateEvent_IdfcVkycStatus)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_kyc_v2_event_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_kyc_v2_event_proto_goTypes,
		DependencyIndexes: file_api_kyc_v2_event_proto_depIdxs,
		MessageInfos:      file_api_kyc_v2_event_proto_msgTypes,
	}.Build()
	File_api_kyc_v2_event_proto = out.File
	file_api_kyc_v2_event_proto_rawDesc = nil
	file_api_kyc_v2_event_proto_goTypes = nil
	file_api_kyc_v2_event_proto_depIdxs = nil
}
