// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/kyc/service.proto

package kyc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	kyc "github.com/epifi/gamma/api/kyc/v2/kyc"

	pan "github.com/epifi/gamma/api/pan"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = kyc.Vendor(0)

	_ = pan.PanType(0)
)

// Validate checks the field values on CaptureSignatureRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaptureSignatureRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptureSignatureRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaptureSignatureRequestMultiError, or nil if none found.
func (m *CaptureSignatureRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptureSignatureRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetSignatureImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaptureSignatureRequestValidationError{
					field:  "SignatureImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaptureSignatureRequestValidationError{
					field:  "SignatureImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSignatureImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaptureSignatureRequestValidationError{
				field:  "SignatureImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaptureSignatureRequestMultiError(errors)
	}

	return nil
}

// CaptureSignatureRequestMultiError is an error wrapping multiple validation
// errors returned by CaptureSignatureRequest.ValidateAll() if the designated
// constraints aren't met.
type CaptureSignatureRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptureSignatureRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptureSignatureRequestMultiError) AllErrors() []error { return m }

// CaptureSignatureRequestValidationError is the validation error returned by
// CaptureSignatureRequest.Validate if the designated constraints aren't met.
type CaptureSignatureRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptureSignatureRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptureSignatureRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptureSignatureRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptureSignatureRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptureSignatureRequestValidationError) ErrorName() string {
	return "CaptureSignatureRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CaptureSignatureRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptureSignatureRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptureSignatureRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptureSignatureRequestValidationError{}

// Validate checks the field values on CaptureSignatureResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaptureSignatureResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptureSignatureResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CaptureSignatureResponseMultiError, or nil if none found.
func (m *CaptureSignatureResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptureSignatureResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaptureSignatureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaptureSignatureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaptureSignatureResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaptureSignatureResponseMultiError(errors)
	}

	return nil
}

// CaptureSignatureResponseMultiError is an error wrapping multiple validation
// errors returned by CaptureSignatureResponse.ValidateAll() if the designated
// constraints aren't met.
type CaptureSignatureResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptureSignatureResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptureSignatureResponseMultiError) AllErrors() []error { return m }

// CaptureSignatureResponseValidationError is the validation error returned by
// CaptureSignatureResponse.Validate if the designated constraints aren't met.
type CaptureSignatureResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptureSignatureResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptureSignatureResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptureSignatureResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptureSignatureResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptureSignatureResponseValidationError) ErrorName() string {
	return "CaptureSignatureResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CaptureSignatureResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptureSignatureResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptureSignatureResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptureSignatureResponseValidationError{}

// Validate checks the field values on GetKYCAttemptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCAttemptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCAttemptRequestMultiError, or nil if none found.
func (m *GetKYCAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetKYCAttemptRequest_ClientReqIdType:
		if v == nil {
			err := GetKYCAttemptRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetClientReqIdType()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetKYCAttemptRequestValidationError{
						field:  "ClientReqIdType",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetKYCAttemptRequestValidationError{
						field:  "ClientReqIdType",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetClientReqIdType()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetKYCAttemptRequestValidationError{
					field:  "ClientReqIdType",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetKYCAttemptRequest_EkycRrn:
		if v == nil {
			err := GetKYCAttemptRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for EkycRrn
	case *GetKYCAttemptRequest_KycAttemptId:
		if v == nil {
			err := GetKYCAttemptRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for KycAttemptId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetKYCAttemptRequestMultiError(errors)
	}

	return nil
}

// GetKYCAttemptRequestMultiError is an error wrapping multiple validation
// errors returned by GetKYCAttemptRequest.ValidateAll() if the designated
// constraints aren't met.
type GetKYCAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCAttemptRequestMultiError) AllErrors() []error { return m }

// GetKYCAttemptRequestValidationError is the validation error returned by
// GetKYCAttemptRequest.Validate if the designated constraints aren't met.
type GetKYCAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCAttemptRequestValidationError) ErrorName() string {
	return "GetKYCAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCAttemptRequestValidationError{}

// Validate checks the field values on GetKYCAttemptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCAttemptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCAttemptResponseMultiError, or nil if none found.
func (m *GetKYCAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKycAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCAttemptResponseValidationError{
					field:  "KycAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCAttemptResponseValidationError{
					field:  "KycAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCAttemptResponseValidationError{
				field:  "KycAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetKYCAttemptResponseMultiError(errors)
	}

	return nil
}

// GetKYCAttemptResponseMultiError is an error wrapping multiple validation
// errors returned by GetKYCAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type GetKYCAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCAttemptResponseMultiError) AllErrors() []error { return m }

// GetKYCAttemptResponseValidationError is the validation error returned by
// GetKYCAttemptResponse.Validate if the designated constraints aren't met.
type GetKYCAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCAttemptResponseValidationError) ErrorName() string {
	return "GetKYCAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCAttemptResponseValidationError{}

// Validate checks the field values on InitiateCKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateCKYCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateCKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateCKYCRequestMultiError, or nil if none found.
func (m *InitiateCKYCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCKYCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetIdProof()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "IdProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "IdProof",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdProof()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCKYCRequestValidationError{
				field:  "IdProof",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCKYCRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNameByUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "NameByUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "NameByUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNameByUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCKYCRequestValidationError{
				field:  "NameByUser",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCKYCRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNameOnPan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "NameOnPan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCKYCRequestValidationError{
					field:  "NameOnPan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNameOnPan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCKYCRequestValidationError{
				field:  "NameOnPan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ForceRetry

	if len(errors) > 0 {
		return InitiateCKYCRequestMultiError(errors)
	}

	return nil
}

// InitiateCKYCRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateCKYCRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateCKYCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCKYCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCKYCRequestMultiError) AllErrors() []error { return m }

// InitiateCKYCRequestValidationError is the validation error returned by
// InitiateCKYCRequest.Validate if the designated constraints aren't met.
type InitiateCKYCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCKYCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCKYCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCKYCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCKYCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCKYCRequestValidationError) ErrorName() string {
	return "InitiateCKYCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCKYCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCKYCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCKYCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCKYCRequestValidationError{}

// Validate checks the field values on InitiateCKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateCKYCResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateCKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateCKYCResponseMultiError, or nil if none found.
func (m *InitiateCKYCResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateCKYCResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateCKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateCKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateCKYCResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycAttemptId

	if len(errors) > 0 {
		return InitiateCKYCResponseMultiError(errors)
	}

	return nil
}

// InitiateCKYCResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateCKYCResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateCKYCResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateCKYCResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateCKYCResponseMultiError) AllErrors() []error { return m }

// InitiateCKYCResponseValidationError is the validation error returned by
// InitiateCKYCResponse.Validate if the designated constraints aren't met.
type InitiateCKYCResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateCKYCResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateCKYCResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateCKYCResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateCKYCResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateCKYCResponseValidationError) ErrorName() string {
	return "InitiateCKYCResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateCKYCResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateCKYCResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateCKYCResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateCKYCResponseValidationError{}

// Validate checks the field values on CheckKYCStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckKYCStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckKYCStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckKYCStatusRequestMultiError, or nil if none found.
func (m *CheckKYCStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckKYCStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for IgnoreLiveness

	if len(errors) > 0 {
		return CheckKYCStatusRequestMultiError(errors)
	}

	return nil
}

// CheckKYCStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckKYCStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckKYCStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckKYCStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckKYCStatusRequestMultiError) AllErrors() []error { return m }

// CheckKYCStatusRequestValidationError is the validation error returned by
// CheckKYCStatusRequest.Validate if the designated constraints aren't met.
type CheckKYCStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckKYCStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckKYCStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckKYCStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckKYCStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckKYCStatusRequestValidationError) ErrorName() string {
	return "CheckKYCStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckKYCStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckKYCStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckKYCStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckKYCStatusRequestValidationError{}

// Validate checks the field values on LivenessOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LivenessOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessOptionsMultiError, or nil if none found.
func (m *LivenessOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for Otp

	// no validation rules for VideoReceived

	// no validation rules for ErrorLastLivenessFailure

	// no validation rules for DisableFaceTrackingAndroid

	if len(errors) > 0 {
		return LivenessOptionsMultiError(errors)
	}

	return nil
}

// LivenessOptionsMultiError is an error wrapping multiple validation errors
// returned by LivenessOptions.ValidateAll() if the designated constraints
// aren't met.
type LivenessOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessOptionsMultiError) AllErrors() []error { return m }

// LivenessOptionsValidationError is the validation error returned by
// LivenessOptions.Validate if the designated constraints aren't met.
type LivenessOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessOptionsValidationError) ErrorName() string { return "LivenessOptionsValidationError" }

// Error satisfies the builtin error interface
func (e LivenessOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessOptionsValidationError{}

// Validate checks the field values on CheckKYCStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckKYCStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckKYCStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckKYCStatusResponseMultiError, or nil if none found.
func (m *CheckKYCStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckKYCStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKYCStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycLevel

	// no validation rules for KycProvider

	// no validation rules for KycType

	// no validation rules for KycStatus

	// no validation rules for LivenessStatus

	if all {
		switch v := interface{}(m.GetLivenessOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "LivenessOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "LivenessOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKYCStatusResponseValidationError{
				field:  "LivenessOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LivenessRequestId

	if all {
		switch v := interface{}(m.GetExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "Expiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKYCStatusResponseValidationError{
				field:  "Expiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	// no validation rules for DataValidationMaxRetries

	// no validation rules for DataValidationMaxRetriesCount

	// no validation rules for DataValidationCurrentRetriesCount

	if all {
		switch v := interface{}(m.GetLivenessParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "LivenessParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "LivenessParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKYCStatusResponseValidationError{
				field:  "LivenessParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKYCStatusResponseValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKYCStatusResponseValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKYCStatusResponseValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycState

	if len(errors) > 0 {
		return CheckKYCStatusResponseMultiError(errors)
	}

	return nil
}

// CheckKYCStatusResponseMultiError is an error wrapping multiple validation
// errors returned by CheckKYCStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckKYCStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckKYCStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckKYCStatusResponseMultiError) AllErrors() []error { return m }

// CheckKYCStatusResponseValidationError is the validation error returned by
// CheckKYCStatusResponse.Validate if the designated constraints aren't met.
type CheckKYCStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckKYCStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckKYCStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckKYCStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckKYCStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckKYCStatusResponseValidationError) ErrorName() string {
	return "CheckKYCStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckKYCStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckKYCStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckKYCStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckKYCStatusResponseValidationError{}

// Validate checks the field values on GetKYCRecordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCRecordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCRecordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCRecordRequestMultiError, or nil if none found.
func (m *GetKYCRecordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCRecordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for SignImage

	// no validation rules for ForceBkycInReviewRecord

	if len(errors) > 0 {
		return GetKYCRecordRequestMultiError(errors)
	}

	return nil
}

// GetKYCRecordRequestMultiError is an error wrapping multiple validation
// errors returned by GetKYCRecordRequest.ValidateAll() if the designated
// constraints aren't met.
type GetKYCRecordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCRecordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCRecordRequestMultiError) AllErrors() []error { return m }

// GetKYCRecordRequestValidationError is the validation error returned by
// GetKYCRecordRequest.Validate if the designated constraints aren't met.
type GetKYCRecordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCRecordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCRecordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCRecordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCRecordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCRecordRequestValidationError) ErrorName() string {
	return "GetKYCRecordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCRecordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCRecordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCRecordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCRecordRequestValidationError{}

// Validate checks the field values on GetKYCRecordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCRecordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCRecordResponseMultiError, or nil if none found.
func (m *GetKYCRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCRecordResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycType

	// no validation rules for KycProvider

	// no validation rules for KycStatus

	if all {
		switch v := interface{}(m.GetKycRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCRecordResponseValidationError{
					field:  "KycRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCRecordResponseValidationError{
					field:  "KycRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCRecordResponseValidationError{
				field:  "KycRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCkycAttempt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCRecordResponseValidationError{
					field:  "CkycAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCRecordResponseValidationError{
					field:  "CkycAttempt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCkycAttempt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCRecordResponseValidationError{
				field:  "CkycAttempt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycAttemptId

	if len(errors) > 0 {
		return GetKYCRecordResponseMultiError(errors)
	}

	return nil
}

// GetKYCRecordResponseMultiError is an error wrapping multiple validation
// errors returned by GetKYCRecordResponse.ValidateAll() if the designated
// constraints aren't met.
type GetKYCRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCRecordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCRecordResponseMultiError) AllErrors() []error { return m }

// GetKYCRecordResponseValidationError is the validation error returned by
// GetKYCRecordResponse.Validate if the designated constraints aren't met.
type GetKYCRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCRecordResponseValidationError) ErrorName() string {
	return "GetKYCRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCRecordResponseValidationError{}

// Validate checks the field values on ValidateCustomerNameRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateCustomerNameRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateCustomerNameRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateCustomerNameRequestMultiError, or nil if none found.
func (m *ValidateCustomerNameRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateCustomerNameRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateCustomerNameRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateCustomerNameRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateCustomerNameRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateCustomerNameRequestMultiError(errors)
	}

	return nil
}

// ValidateCustomerNameRequestMultiError is an error wrapping multiple
// validation errors returned by ValidateCustomerNameRequest.ValidateAll() if
// the designated constraints aren't met.
type ValidateCustomerNameRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateCustomerNameRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateCustomerNameRequestMultiError) AllErrors() []error { return m }

// ValidateCustomerNameRequestValidationError is the validation error returned
// by ValidateCustomerNameRequest.Validate if the designated constraints
// aren't met.
type ValidateCustomerNameRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateCustomerNameRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateCustomerNameRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateCustomerNameRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateCustomerNameRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateCustomerNameRequestValidationError) ErrorName() string {
	return "ValidateCustomerNameRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateCustomerNameRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateCustomerNameRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateCustomerNameRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateCustomerNameRequestValidationError{}

// Validate checks the field values on ValidateCustomerNameResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateCustomerNameResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateCustomerNameResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateCustomerNameResponseMultiError, or nil if none found.
func (m *ValidateCustomerNameResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateCustomerNameResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateCustomerNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateCustomerNameResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateCustomerNameResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsValid

	// no validation rules for UNNameCheckStatus

	if len(errors) > 0 {
		return ValidateCustomerNameResponseMultiError(errors)
	}

	return nil
}

// ValidateCustomerNameResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateCustomerNameResponse.ValidateAll() if
// the designated constraints aren't met.
type ValidateCustomerNameResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateCustomerNameResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateCustomerNameResponseMultiError) AllErrors() []error { return m }

// ValidateCustomerNameResponseValidationError is the validation error returned
// by ValidateCustomerNameResponse.Validate if the designated constraints
// aren't met.
type ValidateCustomerNameResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateCustomerNameResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateCustomerNameResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateCustomerNameResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateCustomerNameResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateCustomerNameResponseValidationError) ErrorName() string {
	return "ValidateCustomerNameResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateCustomerNameResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateCustomerNameResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateCustomerNameResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateCustomerNameResponseValidationError{}

// Validate checks the field values on InitiateEKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateEKYCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateEKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateEKYCRequestMultiError, or nil if none found.
func (m *InitiateEKYCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateEKYCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ForceRetry

	// no validation rules for ForcedKycLevel

	// no validation rules for EkycSource

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return InitiateEKYCRequestMultiError(errors)
	}

	return nil
}

// InitiateEKYCRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateEKYCRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateEKYCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateEKYCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateEKYCRequestMultiError) AllErrors() []error { return m }

// InitiateEKYCRequestValidationError is the validation error returned by
// InitiateEKYCRequest.Validate if the designated constraints aren't met.
type InitiateEKYCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateEKYCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateEKYCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateEKYCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateEKYCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateEKYCRequestValidationError) ErrorName() string {
	return "InitiateEKYCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateEKYCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateEKYCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateEKYCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateEKYCRequestValidationError{}

// Validate checks the field values on InitiateEKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateEKYCResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateEKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateEKYCResponseMultiError, or nil if none found.
func (m *InitiateEKYCResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateEKYCResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateEKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateEKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateEKYCResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycAttemptId

	// no validation rules for SecurityToken

	// no validation rules for DowntimeMessage

	if len(errors) > 0 {
		return InitiateEKYCResponseMultiError(errors)
	}

	return nil
}

// InitiateEKYCResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateEKYCResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateEKYCResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateEKYCResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateEKYCResponseMultiError) AllErrors() []error { return m }

// InitiateEKYCResponseValidationError is the validation error returned by
// InitiateEKYCResponse.Validate if the designated constraints aren't met.
type InitiateEKYCResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateEKYCResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateEKYCResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateEKYCResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateEKYCResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateEKYCResponseValidationError) ErrorName() string {
	return "InitiateEKYCResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateEKYCResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateEKYCResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateEKYCResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateEKYCResponseValidationError{}

// Validate checks the field values on UploadEKYCRecordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadEKYCRecordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadEKYCRecordRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadEKYCRecordRequestMultiError, or nil if none found.
func (m *UploadEKYCRecordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadEKYCRecordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadEKYCRecordRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNameByUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "NameByUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "NameByUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNameByUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadEKYCRecordRequestValidationError{
				field:  "NameByUser",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SkipNameDobValidation

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadEKYCRecordRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDedupeName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "DedupeName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "DedupeName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedupeName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadEKYCRecordRequestValidationError{
				field:  "DedupeName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEkycNumberMismatch()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "EkycNumberMismatch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadEKYCRecordRequestValidationError{
					field:  "EkycNumberMismatch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEkycNumberMismatch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadEKYCRecordRequestValidationError{
				field:  "EkycNumberMismatch",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadEKYCRecordRequestMultiError(errors)
	}

	return nil
}

// UploadEKYCRecordRequestMultiError is an error wrapping multiple validation
// errors returned by UploadEKYCRecordRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadEKYCRecordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadEKYCRecordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadEKYCRecordRequestMultiError) AllErrors() []error { return m }

// UploadEKYCRecordRequestValidationError is the validation error returned by
// UploadEKYCRecordRequest.Validate if the designated constraints aren't met.
type UploadEKYCRecordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadEKYCRecordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadEKYCRecordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadEKYCRecordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadEKYCRecordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadEKYCRecordRequestValidationError) ErrorName() string {
	return "UploadEKYCRecordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadEKYCRecordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadEKYCRecordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadEKYCRecordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadEKYCRecordRequestValidationError{}

// Validate checks the field values on UploadBKYCRecordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadBKYCRecordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadBKYCRecordRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadBKYCRecordRequestMultiError, or nil if none found.
func (m *UploadBKYCRecordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadBKYCRecordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AgentActorId

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadBKYCRecordRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadBKYCRecordRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadBKYCRecordRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttemptSuccessful

	// no validation rules for AgentVerificationSuccessful

	// no validation rules for FailureReason

	// no validation rules for CustomerDueDiligenceConsentId

	if all {
		switch v := interface{}(m.GetPanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadBKYCRecordRequestValidationError{
					field:  "PanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadBKYCRecordRequestValidationError{
					field:  "PanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadBKYCRecordRequestValidationError{
				field:  "PanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadBKYCRecordRequestMultiError(errors)
	}

	return nil
}

// UploadBKYCRecordRequestMultiError is an error wrapping multiple validation
// errors returned by UploadBKYCRecordRequest.ValidateAll() if the designated
// constraints aren't met.
type UploadBKYCRecordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadBKYCRecordRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadBKYCRecordRequestMultiError) AllErrors() []error { return m }

// UploadBKYCRecordRequestValidationError is the validation error returned by
// UploadBKYCRecordRequest.Validate if the designated constraints aren't met.
type UploadBKYCRecordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadBKYCRecordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadBKYCRecordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadBKYCRecordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadBKYCRecordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadBKYCRecordRequestValidationError) ErrorName() string {
	return "UploadBKYCRecordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadBKYCRecordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadBKYCRecordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadBKYCRecordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadBKYCRecordRequestValidationError{}

// Validate checks the field values on UploadBKYCRecordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadBKYCRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadBKYCRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadBKYCRecordResponseMultiError, or nil if none found.
func (m *UploadBKYCRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadBKYCRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadBKYCRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadBKYCRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadBKYCRecordResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadBKYCRecordResponseMultiError(errors)
	}

	return nil
}

// UploadBKYCRecordResponseMultiError is an error wrapping multiple validation
// errors returned by UploadBKYCRecordResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadBKYCRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadBKYCRecordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadBKYCRecordResponseMultiError) AllErrors() []error { return m }

// UploadBKYCRecordResponseValidationError is the validation error returned by
// UploadBKYCRecordResponse.Validate if the designated constraints aren't met.
type UploadBKYCRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadBKYCRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadBKYCRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadBKYCRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadBKYCRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadBKYCRecordResponseValidationError) ErrorName() string {
	return "UploadBKYCRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadBKYCRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadBKYCRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadBKYCRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadBKYCRecordResponseValidationError{}

// Validate checks the field values on UploadEKYCRecordResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadEKYCRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadEKYCRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadEKYCRecordResponseMultiError, or nil if none found.
func (m *UploadEKYCRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadEKYCRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadEKYCRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadEKYCRecordResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadEKYCRecordResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadEKYCRecordResponseMultiError(errors)
	}

	return nil
}

// UploadEKYCRecordResponseMultiError is an error wrapping multiple validation
// errors returned by UploadEKYCRecordResponse.ValidateAll() if the designated
// constraints aren't met.
type UploadEKYCRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadEKYCRecordResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadEKYCRecordResponseMultiError) AllErrors() []error { return m }

// UploadEKYCRecordResponseValidationError is the validation error returned by
// UploadEKYCRecordResponse.Validate if the designated constraints aren't met.
type UploadEKYCRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadEKYCRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadEKYCRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadEKYCRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadEKYCRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadEKYCRecordResponseValidationError) ErrorName() string {
	return "UploadEKYCRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadEKYCRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadEKYCRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadEKYCRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadEKYCRecordResponseValidationError{}

// Validate checks the field values on AllowLivenessRetryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllowLivenessRetryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllowLivenessRetryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AllowLivenessRetryRequestMultiError, or nil if none found.
func (m *AllowLivenessRetryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AllowLivenessRetryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return AllowLivenessRetryRequestMultiError(errors)
	}

	return nil
}

// AllowLivenessRetryRequestMultiError is an error wrapping multiple validation
// errors returned by AllowLivenessRetryRequest.ValidateAll() if the
// designated constraints aren't met.
type AllowLivenessRetryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllowLivenessRetryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllowLivenessRetryRequestMultiError) AllErrors() []error { return m }

// AllowLivenessRetryRequestValidationError is the validation error returned by
// AllowLivenessRetryRequest.Validate if the designated constraints aren't met.
type AllowLivenessRetryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllowLivenessRetryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllowLivenessRetryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllowLivenessRetryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllowLivenessRetryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllowLivenessRetryRequestValidationError) ErrorName() string {
	return "AllowLivenessRetryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AllowLivenessRetryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllowLivenessRetryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllowLivenessRetryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllowLivenessRetryRequestValidationError{}

// Validate checks the field values on AllowLivenessRetryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllowLivenessRetryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllowLivenessRetryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AllowLivenessRetryResponseMultiError, or nil if none found.
func (m *AllowLivenessRetryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AllowLivenessRetryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllowLivenessRetryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllowLivenessRetryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllowLivenessRetryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AllowLivenessRetryResponseMultiError(errors)
	}

	return nil
}

// AllowLivenessRetryResponseMultiError is an error wrapping multiple
// validation errors returned by AllowLivenessRetryResponse.ValidateAll() if
// the designated constraints aren't met.
type AllowLivenessRetryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllowLivenessRetryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllowLivenessRetryResponseMultiError) AllErrors() []error { return m }

// AllowLivenessRetryResponseValidationError is the validation error returned
// by AllowLivenessRetryResponse.Validate if the designated constraints aren't met.
type AllowLivenessRetryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllowLivenessRetryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllowLivenessRetryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllowLivenessRetryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllowLivenessRetryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllowLivenessRetryResponseValidationError) ErrorName() string {
	return "AllowLivenessRetryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AllowLivenessRetryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllowLivenessRetryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllowLivenessRetryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllowLivenessRetryResponseValidationError{}

// Validate checks the field values on GetKYCHistoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCHistoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCHistoryRequestMultiError, or nil if none found.
func (m *GetKYCHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetKYCHistoryRequestMultiError(errors)
	}

	return nil
}

// GetKYCHistoryRequestMultiError is an error wrapping multiple validation
// errors returned by GetKYCHistoryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetKYCHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCHistoryRequestMultiError) AllErrors() []error { return m }

// GetKYCHistoryRequestValidationError is the validation error returned by
// GetKYCHistoryRequest.Validate if the designated constraints aren't met.
type GetKYCHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCHistoryRequestValidationError) ErrorName() string {
	return "GetKYCHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCHistoryRequestValidationError{}

// Validate checks the field values on GetKYCHistoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCHistoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCHistoryResponseMultiError, or nil if none found.
func (m *GetKYCHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCHistoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetKycAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetKYCHistoryResponseValidationError{
						field:  fmt.Sprintf("KycAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetKYCHistoryResponseValidationError{
						field:  fmt.Sprintf("KycAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetKYCHistoryResponseValidationError{
					field:  fmt.Sprintf("KycAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetKycSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCHistoryResponseValidationError{
					field:  "KycSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCHistoryResponseValidationError{
					field:  "KycSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCHistoryResponseValidationError{
				field:  "KycSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetKYCHistoryResponseMultiError(errors)
	}

	return nil
}

// GetKYCHistoryResponseMultiError is an error wrapping multiple validation
// errors returned by GetKYCHistoryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetKYCHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCHistoryResponseMultiError) AllErrors() []error { return m }

// GetKYCHistoryResponseValidationError is the validation error returned by
// GetKYCHistoryResponse.Validate if the designated constraints aren't met.
type GetKYCHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCHistoryResponseValidationError) ErrorName() string {
	return "GetKYCHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCHistoryResponseValidationError{}

// Validate checks the field values on UpdateLivenessStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLivenessStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLivenessStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLivenessStatusRequestMultiError, or nil if none found.
func (m *UpdateLivenessStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLivenessStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for LivenessStatus

	// no validation rules for FaceMatchStatus

	// no validation rules for LivenessS3Url

	if len(errors) > 0 {
		return UpdateLivenessStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateLivenessStatusRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateLivenessStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateLivenessStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLivenessStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLivenessStatusRequestMultiError) AllErrors() []error { return m }

// UpdateLivenessStatusRequestValidationError is the validation error returned
// by UpdateLivenessStatusRequest.Validate if the designated constraints
// aren't met.
type UpdateLivenessStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLivenessStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLivenessStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLivenessStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLivenessStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLivenessStatusRequestValidationError) ErrorName() string {
	return "UpdateLivenessStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLivenessStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLivenessStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLivenessStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLivenessStatusRequestValidationError{}

// Validate checks the field values on UpdateLivenessStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateLivenessStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateLivenessStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateLivenessStatusResponseMultiError, or nil if none found.
func (m *UpdateLivenessStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateLivenessStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateLivenessStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateLivenessStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateLivenessStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateLivenessStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateLivenessStatusResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateLivenessStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateLivenessStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateLivenessStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateLivenessStatusResponseMultiError) AllErrors() []error { return m }

// UpdateLivenessStatusResponseValidationError is the validation error returned
// by UpdateLivenessStatusResponse.Validate if the designated constraints
// aren't met.
type UpdateLivenessStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateLivenessStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateLivenessStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateLivenessStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateLivenessStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateLivenessStatusResponseValidationError) ErrorName() string {
	return "UpdateLivenessStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateLivenessStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateLivenessStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateLivenessStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateLivenessStatusResponseValidationError{}

// Validate checks the field values on ValidateUserDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateUserDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateUserDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateUserDetailsRequestMultiError, or nil if none found.
func (m *ValidateUserDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateUserDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUserDetailsRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUserDetailsRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUserDetailsRequestValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUserDetailsRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUserDetailsRequestValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateUserDetailsRequestMultiError(errors)
	}

	return nil
}

// ValidateUserDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by ValidateUserDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type ValidateUserDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateUserDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateUserDetailsRequestMultiError) AllErrors() []error { return m }

// ValidateUserDetailsRequestValidationError is the validation error returned
// by ValidateUserDetailsRequest.Validate if the designated constraints aren't met.
type ValidateUserDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateUserDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateUserDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateUserDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateUserDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateUserDetailsRequestValidationError) ErrorName() string {
	return "ValidateUserDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateUserDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateUserDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateUserDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateUserDetailsRequestValidationError{}

// Validate checks the field values on ValidateUserDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateUserDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateUserDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateUserDetailsResponseMultiError, or nil if none found.
func (m *ValidateUserDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateUserDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateUserDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateUserDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateUserDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateUserDetailsResponseMultiError(errors)
	}

	return nil
}

// ValidateUserDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateUserDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type ValidateUserDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateUserDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateUserDetailsResponseMultiError) AllErrors() []error { return m }

// ValidateUserDetailsResponseValidationError is the validation error returned
// by ValidateUserDetailsResponse.Validate if the designated constraints
// aren't met.
type ValidateUserDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateUserDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateUserDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateUserDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateUserDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateUserDetailsResponseValidationError) ErrorName() string {
	return "ValidateUserDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateUserDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateUserDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateUserDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateUserDetailsResponseValidationError{}

// Validate checks the field values on KycSummary with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KycSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KycSummaryMultiError, or
// nil if none found.
func (m *KycSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *KycSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for KycAttemptId

	// no validation rules for Status

	// no validation rules for LivenessStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycSummaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycSummaryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycSummaryValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycSummaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycSummaryValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycSummaryValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KycSummaryMultiError(errors)
	}

	return nil
}

// KycSummaryMultiError is an error wrapping multiple validation errors
// returned by KycSummary.ValidateAll() if the designated constraints aren't met.
type KycSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycSummaryMultiError) AllErrors() []error { return m }

// KycSummaryValidationError is the validation error returned by
// KycSummary.Validate if the designated constraints aren't met.
type KycSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycSummaryValidationError) ErrorName() string { return "KycSummaryValidationError" }

// Error satisfies the builtin error interface
func (e KycSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycSummaryValidationError{}

// Validate checks the field values on PurgeKYCDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PurgeKYCDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurgeKYCDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PurgeKYCDataRequestMultiError, or nil if none found.
func (m *PurgeKYCDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PurgeKYCDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExpiryTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurgeKYCDataRequestValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurgeKYCDataRequestValidationError{
					field:  "ExpiryTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurgeKYCDataRequestValidationError{
				field:  "ExpiryTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurgeKYCDataRequestMultiError(errors)
	}

	return nil
}

// PurgeKYCDataRequestMultiError is an error wrapping multiple validation
// errors returned by PurgeKYCDataRequest.ValidateAll() if the designated
// constraints aren't met.
type PurgeKYCDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurgeKYCDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurgeKYCDataRequestMultiError) AllErrors() []error { return m }

// PurgeKYCDataRequestValidationError is the validation error returned by
// PurgeKYCDataRequest.Validate if the designated constraints aren't met.
type PurgeKYCDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurgeKYCDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurgeKYCDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurgeKYCDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurgeKYCDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurgeKYCDataRequestValidationError) ErrorName() string {
	return "PurgeKYCDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PurgeKYCDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurgeKYCDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurgeKYCDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurgeKYCDataRequestValidationError{}

// Validate checks the field values on PurgeKYCDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PurgeKYCDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PurgeKYCDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PurgeKYCDataResponseMultiError, or nil if none found.
func (m *PurgeKYCDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PurgeKYCDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PurgeKYCDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PurgeKYCDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PurgeKYCDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PurgeKYCDataResponseMultiError(errors)
	}

	return nil
}

// PurgeKYCDataResponseMultiError is an error wrapping multiple validation
// errors returned by PurgeKYCDataResponse.ValidateAll() if the designated
// constraints aren't met.
type PurgeKYCDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PurgeKYCDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PurgeKYCDataResponseMultiError) AllErrors() []error { return m }

// PurgeKYCDataResponseValidationError is the validation error returned by
// PurgeKYCDataResponse.Validate if the designated constraints aren't met.
type PurgeKYCDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PurgeKYCDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PurgeKYCDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PurgeKYCDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PurgeKYCDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PurgeKYCDataResponseValidationError) ErrorName() string {
	return "PurgeKYCDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PurgeKYCDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPurgeKYCDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PurgeKYCDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PurgeKYCDataResponseValidationError{}

// Validate checks the field values on ResetKYCNameDOBRetryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetKYCNameDOBRetryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetKYCNameDOBRetryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetKYCNameDOBRetryRequestMultiError, or nil if none found.
func (m *ResetKYCNameDOBRetryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetKYCNameDOBRetryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return ResetKYCNameDOBRetryRequestMultiError(errors)
	}

	return nil
}

// ResetKYCNameDOBRetryRequestMultiError is an error wrapping multiple
// validation errors returned by ResetKYCNameDOBRetryRequest.ValidateAll() if
// the designated constraints aren't met.
type ResetKYCNameDOBRetryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetKYCNameDOBRetryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetKYCNameDOBRetryRequestMultiError) AllErrors() []error { return m }

// ResetKYCNameDOBRetryRequestValidationError is the validation error returned
// by ResetKYCNameDOBRetryRequest.Validate if the designated constraints
// aren't met.
type ResetKYCNameDOBRetryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetKYCNameDOBRetryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetKYCNameDOBRetryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetKYCNameDOBRetryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetKYCNameDOBRetryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetKYCNameDOBRetryRequestValidationError) ErrorName() string {
	return "ResetKYCNameDOBRetryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResetKYCNameDOBRetryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetKYCNameDOBRetryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetKYCNameDOBRetryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetKYCNameDOBRetryRequestValidationError{}

// Validate checks the field values on ResetKYCNameDOBRetryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResetKYCNameDOBRetryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResetKYCNameDOBRetryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResetKYCNameDOBRetryResponseMultiError, or nil if none found.
func (m *ResetKYCNameDOBRetryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ResetKYCNameDOBRetryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResetKYCNameDOBRetryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResetKYCNameDOBRetryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResetKYCNameDOBRetryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResetKYCNameDOBRetryResponseMultiError(errors)
	}

	return nil
}

// ResetKYCNameDOBRetryResponseMultiError is an error wrapping multiple
// validation errors returned by ResetKYCNameDOBRetryResponse.ValidateAll() if
// the designated constraints aren't met.
type ResetKYCNameDOBRetryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResetKYCNameDOBRetryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResetKYCNameDOBRetryResponseMultiError) AllErrors() []error { return m }

// ResetKYCNameDOBRetryResponseValidationError is the validation error returned
// by ResetKYCNameDOBRetryResponse.Validate if the designated constraints
// aren't met.
type ResetKYCNameDOBRetryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResetKYCNameDOBRetryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResetKYCNameDOBRetryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResetKYCNameDOBRetryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResetKYCNameDOBRetryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResetKYCNameDOBRetryResponseValidationError) ErrorName() string {
	return "ResetKYCNameDOBRetryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ResetKYCNameDOBRetryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResetKYCNameDOBRetryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResetKYCNameDOBRetryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResetKYCNameDOBRetryResponseValidationError{}

// Validate checks the field values on GetKYCVendorDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCVendorDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCVendorDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCVendorDataRequestMultiError, or nil if none found.
func (m *GetKYCVendorDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCVendorDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	switch v := m.Identifier.(type) {
	case *GetKYCVendorDataRequest_ActorIdV2:
		if v == nil {
			err := GetKYCVendorDataRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorIdV2
	case *GetKYCVendorDataRequest_AttemptId:
		if v == nil {
			err := GetKYCVendorDataRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AttemptId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetKYCVendorDataRequestMultiError(errors)
	}

	return nil
}

// GetKYCVendorDataRequestMultiError is an error wrapping multiple validation
// errors returned by GetKYCVendorDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetKYCVendorDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCVendorDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCVendorDataRequestMultiError) AllErrors() []error { return m }

// GetKYCVendorDataRequestValidationError is the validation error returned by
// GetKYCVendorDataRequest.Validate if the designated constraints aren't met.
type GetKYCVendorDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCVendorDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCVendorDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCVendorDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCVendorDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCVendorDataRequestValidationError) ErrorName() string {
	return "GetKYCVendorDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCVendorDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCVendorDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCVendorDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCVendorDataRequestValidationError{}

// Validate checks the field values on GetKYCVendorDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCVendorDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCVendorDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCVendorDataResponseMultiError, or nil if none found.
func (m *GetKYCVendorDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCVendorDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCVendorDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCVendorDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCVendorDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCVendorDataResponseValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCVendorDataResponseValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCVendorDataResponseValidationError{
				field:  "VendorData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetKYCVendorDataResponseMultiError(errors)
	}

	return nil
}

// GetKYCVendorDataResponseMultiError is an error wrapping multiple validation
// errors returned by GetKYCVendorDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetKYCVendorDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCVendorDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCVendorDataResponseMultiError) AllErrors() []error { return m }

// GetKYCVendorDataResponseValidationError is the validation error returned by
// GetKYCVendorDataResponse.Validate if the designated constraints aren't met.
type GetKYCVendorDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCVendorDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCVendorDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCVendorDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCVendorDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCVendorDataResponseValidationError) ErrorName() string {
	return "GetKYCVendorDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCVendorDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCVendorDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCVendorDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCVendorDataResponseValidationError{}

// Validate checks the field values on ValidateBKYCEligibilityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateBKYCEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateBKYCEligibilityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateBKYCEligibilityRequestMultiError, or nil if none found.
func (m *ValidateBKYCEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateBKYCEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return ValidateBKYCEligibilityRequestMultiError(errors)
	}

	return nil
}

// ValidateBKYCEligibilityRequestMultiError is an error wrapping multiple
// validation errors returned by ValidateBKYCEligibilityRequest.ValidateAll()
// if the designated constraints aren't met.
type ValidateBKYCEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateBKYCEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateBKYCEligibilityRequestMultiError) AllErrors() []error { return m }

// ValidateBKYCEligibilityRequestValidationError is the validation error
// returned by ValidateBKYCEligibilityRequest.Validate if the designated
// constraints aren't met.
type ValidateBKYCEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateBKYCEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateBKYCEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateBKYCEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateBKYCEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateBKYCEligibilityRequestValidationError) ErrorName() string {
	return "ValidateBKYCEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateBKYCEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateBKYCEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateBKYCEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateBKYCEligibilityRequestValidationError{}

// Validate checks the field values on ValidateBKYCEligibilityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateBKYCEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateBKYCEligibilityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateBKYCEligibilityResponseMultiError, or nil if none found.
func (m *ValidateBKYCEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateBKYCEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateBKYCEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateBKYCEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateBKYCEligibilityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateBKYCEligibilityResponseMultiError(errors)
	}

	return nil
}

// ValidateBKYCEligibilityResponseMultiError is an error wrapping multiple
// validation errors returned by ValidateBKYCEligibilityResponse.ValidateAll()
// if the designated constraints aren't met.
type ValidateBKYCEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateBKYCEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateBKYCEligibilityResponseMultiError) AllErrors() []error { return m }

// ValidateBKYCEligibilityResponseValidationError is the validation error
// returned by ValidateBKYCEligibilityResponse.Validate if the designated
// constraints aren't met.
type ValidateBKYCEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateBKYCEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateBKYCEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateBKYCEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateBKYCEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateBKYCEligibilityResponseValidationError) ErrorName() string {
	return "ValidateBKYCEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateBKYCEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateBKYCEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateBKYCEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateBKYCEligibilityResponseValidationError{}

// Validate checks the field values on InitiateBKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateBKYCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateBKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateBKYCRequestMultiError, or nil if none found.
func (m *InitiateBKYCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateBKYCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AgentActorId

	// no validation rules for BkycSource

	if len(errors) > 0 {
		return InitiateBKYCRequestMultiError(errors)
	}

	return nil
}

// InitiateBKYCRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateBKYCRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateBKYCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateBKYCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateBKYCRequestMultiError) AllErrors() []error { return m }

// InitiateBKYCRequestValidationError is the validation error returned by
// InitiateBKYCRequest.Validate if the designated constraints aren't met.
type InitiateBKYCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateBKYCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateBKYCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateBKYCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateBKYCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateBKYCRequestValidationError) ErrorName() string {
	return "InitiateBKYCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateBKYCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateBKYCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateBKYCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateBKYCRequestValidationError{}

// Validate checks the field values on InitiateBKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateBKYCResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateBKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateBKYCResponseMultiError, or nil if none found.
func (m *InitiateBKYCResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateBKYCResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateBKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateBKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateBKYCResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SecurityToken

	// no validation rules for DowntimeMessage

	if len(errors) > 0 {
		return InitiateBKYCResponseMultiError(errors)
	}

	return nil
}

// InitiateBKYCResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateBKYCResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateBKYCResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateBKYCResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateBKYCResponseMultiError) AllErrors() []error { return m }

// InitiateBKYCResponseValidationError is the validation error returned by
// InitiateBKYCResponse.Validate if the designated constraints aren't met.
type InitiateBKYCResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateBKYCResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateBKYCResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateBKYCResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateBKYCResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateBKYCResponseValidationError) ErrorName() string {
	return "InitiateBKYCResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateBKYCResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateBKYCResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateBKYCResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateBKYCResponseValidationError{}

// Validate checks the field values on ConfirmBKYCDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmBKYCDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmBKYCDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConfirmBKYCDetailsRequestMultiError, or nil if none found.
func (m *ConfirmBKYCDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmBKYCDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetCommAddressInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmBKYCDetailsRequestValidationError{
					field:  "CommAddressInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmBKYCDetailsRequestValidationError{
					field:  "CommAddressInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommAddressInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmBKYCDetailsRequestValidationError{
				field:  "CommAddressInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmBKYCDetailsRequestMultiError(errors)
	}

	return nil
}

// ConfirmBKYCDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by ConfirmBKYCDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type ConfirmBKYCDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmBKYCDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmBKYCDetailsRequestMultiError) AllErrors() []error { return m }

// ConfirmBKYCDetailsRequestValidationError is the validation error returned by
// ConfirmBKYCDetailsRequest.Validate if the designated constraints aren't met.
type ConfirmBKYCDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmBKYCDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmBKYCDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmBKYCDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmBKYCDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmBKYCDetailsRequestValidationError) ErrorName() string {
	return "ConfirmBKYCDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmBKYCDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmBKYCDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmBKYCDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmBKYCDetailsRequestValidationError{}

// Validate checks the field values on ConfirmBKYCDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConfirmBKYCDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfirmBKYCDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConfirmBKYCDetailsResponseMultiError, or nil if none found.
func (m *ConfirmBKYCDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmBKYCDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmBKYCDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmBKYCDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmBKYCDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmBKYCDetailsResponseMultiError(errors)
	}

	return nil
}

// ConfirmBKYCDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by ConfirmBKYCDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type ConfirmBKYCDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmBKYCDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmBKYCDetailsResponseMultiError) AllErrors() []error { return m }

// ConfirmBKYCDetailsResponseValidationError is the validation error returned
// by ConfirmBKYCDetailsResponse.Validate if the designated constraints aren't met.
type ConfirmBKYCDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmBKYCDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfirmBKYCDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfirmBKYCDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfirmBKYCDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmBKYCDetailsResponseValidationError) ErrorName() string {
	return "ConfirmBKYCDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmBKYCDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmBKYCDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmBKYCDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmBKYCDetailsResponseValidationError{}

// Validate checks the field values on GetBKYCConsentDeeplinkRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBKYCConsentDeeplinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBKYCConsentDeeplinkRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBKYCConsentDeeplinkRequestMultiError, or nil if none found.
func (m *GetBKYCConsentDeeplinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBKYCConsentDeeplinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetBKYCConsentDeeplinkRequestMultiError(errors)
	}

	return nil
}

// GetBKYCConsentDeeplinkRequestMultiError is an error wrapping multiple
// validation errors returned by GetBKYCConsentDeeplinkRequest.ValidateAll()
// if the designated constraints aren't met.
type GetBKYCConsentDeeplinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBKYCConsentDeeplinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBKYCConsentDeeplinkRequestMultiError) AllErrors() []error { return m }

// GetBKYCConsentDeeplinkRequestValidationError is the validation error
// returned by GetBKYCConsentDeeplinkRequest.Validate if the designated
// constraints aren't met.
type GetBKYCConsentDeeplinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBKYCConsentDeeplinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBKYCConsentDeeplinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBKYCConsentDeeplinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBKYCConsentDeeplinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBKYCConsentDeeplinkRequestValidationError) ErrorName() string {
	return "GetBKYCConsentDeeplinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBKYCConsentDeeplinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBKYCConsentDeeplinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBKYCConsentDeeplinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBKYCConsentDeeplinkRequestValidationError{}

// Validate checks the field values on GetBKYCConsentDeeplinkResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBKYCConsentDeeplinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBKYCConsentDeeplinkResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBKYCConsentDeeplinkResponseMultiError, or nil if none found.
func (m *GetBKYCConsentDeeplinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBKYCConsentDeeplinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBKYCConsentDeeplinkResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBKYCConsentDeeplinkResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBKYCConsentDeeplinkResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBKYCConsentDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBKYCConsentDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBKYCConsentDeeplinkResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBKYCConsentDeeplinkResponseMultiError(errors)
	}

	return nil
}

// GetBKYCConsentDeeplinkResponseMultiError is an error wrapping multiple
// validation errors returned by GetBKYCConsentDeeplinkResponse.ValidateAll()
// if the designated constraints aren't met.
type GetBKYCConsentDeeplinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBKYCConsentDeeplinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBKYCConsentDeeplinkResponseMultiError) AllErrors() []error { return m }

// GetBKYCConsentDeeplinkResponseValidationError is the validation error
// returned by GetBKYCConsentDeeplinkResponse.Validate if the designated
// constraints aren't met.
type GetBKYCConsentDeeplinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBKYCConsentDeeplinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBKYCConsentDeeplinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBKYCConsentDeeplinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBKYCConsentDeeplinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBKYCConsentDeeplinkResponseValidationError) ErrorName() string {
	return "GetBKYCConsentDeeplinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBKYCConsentDeeplinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBKYCConsentDeeplinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBKYCConsentDeeplinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBKYCConsentDeeplinkResponseValidationError{}

// Validate checks the field values on InitiateKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateKYCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateKYCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateKYCRequestMultiError, or nil if none found.
func (m *InitiateKYCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateKYCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	// no validation rules for Vendor

	// no validation rules for Flow

	// no validation rules for KYCType

	// no validation rules for ProcType

	if all {
		switch v := interface{}(m.GetRedirectionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateKYCRequestValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateKYCRequestValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateKYCRequestValidationError{
				field:  "RedirectionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateKYCRequestMultiError(errors)
	}

	return nil
}

// InitiateKYCRequestMultiError is an error wrapping multiple validation errors
// returned by InitiateKYCRequest.ValidateAll() if the designated constraints
// aren't met.
type InitiateKYCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateKYCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateKYCRequestMultiError) AllErrors() []error { return m }

// InitiateKYCRequestValidationError is the validation error returned by
// InitiateKYCRequest.Validate if the designated constraints aren't met.
type InitiateKYCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateKYCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateKYCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateKYCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateKYCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateKYCRequestValidationError) ErrorName() string {
	return "InitiateKYCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateKYCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateKYCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateKYCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateKYCRequestValidationError{}

// Validate checks the field values on InitiateKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateKYCResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateKYCResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateKYCResponseMultiError, or nil if none found.
func (m *InitiateKYCResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateKYCResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateKYCResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateKYCResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KYCStatus

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateKYCResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateKYCResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateKYCResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateKYCResponseMultiError(errors)
	}

	return nil
}

// InitiateKYCResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateKYCResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateKYCResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateKYCResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateKYCResponseMultiError) AllErrors() []error { return m }

// InitiateKYCResponseValidationError is the validation error returned by
// InitiateKYCResponse.Validate if the designated constraints aren't met.
type InitiateKYCResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateKYCResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateKYCResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateKYCResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateKYCResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateKYCResponseValidationError) ErrorName() string {
	return "InitiateKYCResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateKYCResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateKYCResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateKYCResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateKYCResponseValidationError{}

// Validate checks the field values on GetKYCStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCStatusRequestMultiError, or nil if none found.
func (m *GetKYCStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ClientReqId

	if all {
		switch v := interface{}(m.GetRedirectionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCStatusRequestValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCStatusRequestValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCStatusRequestValidationError{
				field:  "RedirectionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetKYCStatusRequestMultiError(errors)
	}

	return nil
}

// GetKYCStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetKYCStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetKYCStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCStatusRequestMultiError) AllErrors() []error { return m }

// GetKYCStatusRequestValidationError is the validation error returned by
// GetKYCStatusRequest.Validate if the designated constraints aren't met.
type GetKYCStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCStatusRequestValidationError) ErrorName() string {
	return "GetKYCStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCStatusRequestValidationError{}

// Validate checks the field values on GetKYCStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCStatusResponseMultiError, or nil if none found.
func (m *GetKYCStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KYCStatus

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetKYCStatusResponseMultiError(errors)
	}

	return nil
}

// GetKYCStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetKYCStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetKYCStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCStatusResponseMultiError) AllErrors() []error { return m }

// GetKYCStatusResponseValidationError is the validation error returned by
// GetKYCStatusResponse.Validate if the designated constraints aren't met.
type GetKYCStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCStatusResponseValidationError) ErrorName() string {
	return "GetKYCStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCStatusResponseValidationError{}

// Validate checks the field values on KYCAttemptIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KYCAttemptIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KYCAttemptIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KYCAttemptIdentifierMultiError, or nil if none found.
func (m *KYCAttemptIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *KYCAttemptIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for KYCType

	// no validation rules for Vendor

	if len(errors) > 0 {
		return KYCAttemptIdentifierMultiError(errors)
	}

	return nil
}

// KYCAttemptIdentifierMultiError is an error wrapping multiple validation
// errors returned by KYCAttemptIdentifier.ValidateAll() if the designated
// constraints aren't met.
type KYCAttemptIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KYCAttemptIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KYCAttemptIdentifierMultiError) AllErrors() []error { return m }

// KYCAttemptIdentifierValidationError is the validation error returned by
// KYCAttemptIdentifier.Validate if the designated constraints aren't met.
type KYCAttemptIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KYCAttemptIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KYCAttemptIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KYCAttemptIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KYCAttemptIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KYCAttemptIdentifierValidationError) ErrorName() string {
	return "KYCAttemptIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e KYCAttemptIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKYCAttemptIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KYCAttemptIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KYCAttemptIdentifierValidationError{}

// Validate checks the field values on GetKYCDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetKYCDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCDataRequestMultiError, or nil if none found.
func (m *GetKYCDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetKYCDataRequest_KYCAttemptIdentifier:
		if v == nil {
			err := GetKYCDataRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetKYCAttemptIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetKYCDataRequestValidationError{
						field:  "KYCAttemptIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetKYCDataRequestValidationError{
						field:  "KYCAttemptIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetKYCAttemptIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetKYCDataRequestValidationError{
					field:  "KYCAttemptIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetKYCDataRequest_ClientReqId:
		if v == nil {
			err := GetKYCDataRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ClientReqId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetKYCDataRequestMultiError(errors)
	}

	return nil
}

// GetKYCDataRequestMultiError is an error wrapping multiple validation errors
// returned by GetKYCDataRequest.ValidateAll() if the designated constraints
// aren't met.
type GetKYCDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCDataRequestMultiError) AllErrors() []error { return m }

// GetKYCDataRequestValidationError is the validation error returned by
// GetKYCDataRequest.Validate if the designated constraints aren't met.
type GetKYCDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCDataRequestValidationError) ErrorName() string {
	return "GetKYCDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCDataRequestValidationError{}

// Validate checks the field values on GetKYCDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKYCDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKYCDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKYCDataResponseMultiError, or nil if none found.
func (m *GetKYCDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVendorData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCDataResponseValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCDataResponseValidationError{
					field:  "VendorData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCDataResponseValidationError{
				field:  "VendorData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUserData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetKYCDataResponseValidationError{
					field:  "UserData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetKYCDataResponseValidationError{
					field:  "UserData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetKYCDataResponseValidationError{
				field:  "UserData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetKYCDataResponseMultiError(errors)
	}

	return nil
}

// GetKYCDataResponseMultiError is an error wrapping multiple validation errors
// returned by GetKYCDataResponse.ValidateAll() if the designated constraints
// aren't met.
type GetKYCDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCDataResponseMultiError) AllErrors() []error { return m }

// GetKYCDataResponseValidationError is the validation error returned by
// GetKYCDataResponse.Validate if the designated constraints aren't met.
type GetKYCDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCDataResponseValidationError) ErrorName() string {
	return "GetKYCDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCDataResponseValidationError{}

// Validate checks the field values on GetKYCAttemptRequest_ClientReqIdAndType
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetKYCAttemptRequest_ClientReqIdAndType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetKYCAttemptRequest_ClientReqIdAndType with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetKYCAttemptRequest_ClientReqIdAndTypeMultiError, or nil if none found.
func (m *GetKYCAttemptRequest_ClientReqIdAndType) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKYCAttemptRequest_ClientReqIdAndType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqId

	// no validation rules for KycType

	if len(errors) > 0 {
		return GetKYCAttemptRequest_ClientReqIdAndTypeMultiError(errors)
	}

	return nil
}

// GetKYCAttemptRequest_ClientReqIdAndTypeMultiError is an error wrapping
// multiple validation errors returned by
// GetKYCAttemptRequest_ClientReqIdAndType.ValidateAll() if the designated
// constraints aren't met.
type GetKYCAttemptRequest_ClientReqIdAndTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKYCAttemptRequest_ClientReqIdAndTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKYCAttemptRequest_ClientReqIdAndTypeMultiError) AllErrors() []error { return m }

// GetKYCAttemptRequest_ClientReqIdAndTypeValidationError is the validation
// error returned by GetKYCAttemptRequest_ClientReqIdAndType.Validate if the
// designated constraints aren't met.
type GetKYCAttemptRequest_ClientReqIdAndTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKYCAttemptRequest_ClientReqIdAndTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKYCAttemptRequest_ClientReqIdAndTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKYCAttemptRequest_ClientReqIdAndTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKYCAttemptRequest_ClientReqIdAndTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKYCAttemptRequest_ClientReqIdAndTypeValidationError) ErrorName() string {
	return "GetKYCAttemptRequest_ClientReqIdAndTypeValidationError"
}

// Error satisfies the builtin error interface
func (e GetKYCAttemptRequest_ClientReqIdAndTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKYCAttemptRequest_ClientReqIdAndType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKYCAttemptRequest_ClientReqIdAndTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKYCAttemptRequest_ClientReqIdAndTypeValidationError{}

// Validate checks the field values on UploadBKYCRecordRequest_PanDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UploadBKYCRecordRequest_PanDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadBKYCRecordRequest_PanDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UploadBKYCRecordRequest_PanDetailsMultiError, or nil if none found.
func (m *UploadBKYCRecordRequest_PanDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadBKYCRecordRequest_PanDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PanType

	// no validation rules for PanImageS3Url

	if len(errors) > 0 {
		return UploadBKYCRecordRequest_PanDetailsMultiError(errors)
	}

	return nil
}

// UploadBKYCRecordRequest_PanDetailsMultiError is an error wrapping multiple
// validation errors returned by
// UploadBKYCRecordRequest_PanDetails.ValidateAll() if the designated
// constraints aren't met.
type UploadBKYCRecordRequest_PanDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadBKYCRecordRequest_PanDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadBKYCRecordRequest_PanDetailsMultiError) AllErrors() []error { return m }

// UploadBKYCRecordRequest_PanDetailsValidationError is the validation error
// returned by UploadBKYCRecordRequest_PanDetails.Validate if the designated
// constraints aren't met.
type UploadBKYCRecordRequest_PanDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadBKYCRecordRequest_PanDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadBKYCRecordRequest_PanDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadBKYCRecordRequest_PanDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadBKYCRecordRequest_PanDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadBKYCRecordRequest_PanDetailsValidationError) ErrorName() string {
	return "UploadBKYCRecordRequest_PanDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e UploadBKYCRecordRequest_PanDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadBKYCRecordRequest_PanDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadBKYCRecordRequest_PanDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadBKYCRecordRequest_PanDetailsValidationError{}

// Validate checks the field values on
// ConfirmBKYCDetailsRequest_CommunicationAddressInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ConfirmBKYCDetailsRequest_CommunicationAddressInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ConfirmBKYCDetailsRequest_CommunicationAddressInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ConfirmBKYCDetailsRequest_CommunicationAddressInfoMultiError, or nil if
// none found.
func (m *ConfirmBKYCDetailsRequest_CommunicationAddressInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfirmBKYCDetailsRequest_CommunicationAddressInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressUpdateNeeded

	if all {
		switch v := interface{}(m.GetCommunicationAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError{
					field:  "CommunicationAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError{
					field:  "CommunicationAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommunicationAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError{
				field:  "CommunicationAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConfirmBKYCDetailsRequest_CommunicationAddressInfoMultiError(errors)
	}

	return nil
}

// ConfirmBKYCDetailsRequest_CommunicationAddressInfoMultiError is an error
// wrapping multiple validation errors returned by
// ConfirmBKYCDetailsRequest_CommunicationAddressInfo.ValidateAll() if the
// designated constraints aren't met.
type ConfirmBKYCDetailsRequest_CommunicationAddressInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfirmBKYCDetailsRequest_CommunicationAddressInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfirmBKYCDetailsRequest_CommunicationAddressInfoMultiError) AllErrors() []error { return m }

// ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError is the
// validation error returned by
// ConfirmBKYCDetailsRequest_CommunicationAddressInfo.Validate if the
// designated constraints aren't met.
type ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError) ErrorName() string {
	return "ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfirmBKYCDetailsRequest_CommunicationAddressInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfirmBKYCDetailsRequest_CommunicationAddressInfoValidationError{}
