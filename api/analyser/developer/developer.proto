// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package analyser.developer;

option go_package = "github.com/epifi/gamma/api/analyser/developer";
option java_package = "com.github.epifi.gamma.api.analyser.developer";

enum AnalyserEntity {
  ANALYSER_ENTITY_UNSPECIFIED = 0;

  ANALYSER_TRANSACTIONS = 1;

  ANALYSER_AA_TRANSACTIONS = 2;

  ANALYSER_TRANSACTIONS_UPDATE = 3;

  ANALYSER_AA_TRANSACTIONS_UPDATE = 4;

  ANALYSER_ANALYSIS_TASK = 5;

  MF_PORTFOLIO_HISTORY = 6;
}
