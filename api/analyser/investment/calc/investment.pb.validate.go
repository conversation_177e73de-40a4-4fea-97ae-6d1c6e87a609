// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/analyser/investment/calc/investment.proto

package calc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	investment "github.com/epifi/gamma/api/investment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = investment.InvestmentInstrumentType(0)
)

// Validate checks the field values on InvestmentTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentTransaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentTransactionMultiError, or nil if none found.
func (m *InvestmentTransaction) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentTransaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	switch v := m.Transaction.(type) {
	case *InvestmentTransaction_MutualFundExternalOrder:
		if v == nil {
			err := InvestmentTransactionValidationError{
				field:  "Transaction",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMutualFundExternalOrder()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InvestmentTransactionValidationError{
						field:  "MutualFundExternalOrder",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InvestmentTransactionValidationError{
						field:  "MutualFundExternalOrder",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMutualFundExternalOrder()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InvestmentTransactionValidationError{
					field:  "MutualFundExternalOrder",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return InvestmentTransactionMultiError(errors)
	}

	return nil
}

// InvestmentTransactionMultiError is an error wrapping multiple validation
// errors returned by InvestmentTransaction.ValidateAll() if the designated
// constraints aren't met.
type InvestmentTransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentTransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentTransactionMultiError) AllErrors() []error { return m }

// InvestmentTransactionValidationError is the validation error returned by
// InvestmentTransaction.Validate if the designated constraints aren't met.
type InvestmentTransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentTransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentTransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentTransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentTransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentTransactionValidationError) ErrorName() string {
	return "InvestmentTransactionValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentTransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentTransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentTransactionValidationError{}

// Validate checks the field values on HistoryAggregate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HistoryAggregate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HistoryAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HistoryAggregateMultiError, or nil if none found.
func (m *HistoryAggregate) ValidateAll() error {
	return m.validate(true)
}

func (m *HistoryAggregate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHistoryDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HistoryAggregateValidationError{
					field:  "HistoryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HistoryAggregateValidationError{
					field:  "HistoryDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHistoryDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HistoryAggregateValidationError{
				field:  "HistoryDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountTillDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HistoryAggregateValidationError{
					field:  "AmountTillDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HistoryAggregateValidationError{
					field:  "AmountTillDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountTillDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HistoryAggregateValidationError{
				field:  "AmountTillDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HistoryAggregateMultiError(errors)
	}

	return nil
}

// HistoryAggregateMultiError is an error wrapping multiple validation errors
// returned by HistoryAggregate.ValidateAll() if the designated constraints
// aren't met.
type HistoryAggregateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HistoryAggregateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HistoryAggregateMultiError) AllErrors() []error { return m }

// HistoryAggregateValidationError is the validation error returned by
// HistoryAggregate.Validate if the designated constraints aren't met.
type HistoryAggregateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HistoryAggregateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HistoryAggregateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HistoryAggregateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HistoryAggregateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HistoryAggregateValidationError) ErrorName() string { return "HistoryAggregateValidationError" }

// Error satisfies the builtin error interface
func (e HistoryAggregateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHistoryAggregate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HistoryAggregateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HistoryAggregateValidationError{}
