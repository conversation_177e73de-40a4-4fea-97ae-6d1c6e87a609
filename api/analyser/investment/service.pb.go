// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/analyser/investment/service.proto

package investment

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	enums "github.com/epifi/gamma/api/analyser/enums"
	model "github.com/epifi/gamma/api/analyser/investment/model"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetTaskStatusResponse_Status int32

const (
	GetTaskStatusResponse_OK GetTaskStatusResponse_Status = 0
	// in case task is not found
	GetTaskStatusResponse_NOT_FOUND GetTaskStatusResponse_Status = 5
	// internal error while fetching the task status
	GetTaskStatusResponse_INTERNAL GetTaskStatusResponse_Status = 13
)

// Enum value maps for GetTaskStatusResponse_Status.
var (
	GetTaskStatusResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetTaskStatusResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetTaskStatusResponse_Status) Enum() *GetTaskStatusResponse_Status {
	p := new(GetTaskStatusResponse_Status)
	*p = x
	return p
}

func (x GetTaskStatusResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetTaskStatusResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_investment_service_proto_enumTypes[0].Descriptor()
}

func (GetTaskStatusResponse_Status) Type() protoreflect.EnumType {
	return &file_api_analyser_investment_service_proto_enumTypes[0]
}

func (x GetTaskStatusResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetTaskStatusResponse_Status.Descriptor instead.
func (GetTaskStatusResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{1, 0}
}

type InitiateTasksRequest_Priority int32

const (
	InitiateTasksRequest_PRIORITY_UNSPECIFIED InitiateTasksRequest_Priority = 0
	InitiateTasksRequest_PRIORITY_LOW         InitiateTasksRequest_Priority = 1
	InitiateTasksRequest_PRIORITY_HIGH        InitiateTasksRequest_Priority = 2
)

// Enum value maps for InitiateTasksRequest_Priority.
var (
	InitiateTasksRequest_Priority_name = map[int32]string{
		0: "PRIORITY_UNSPECIFIED",
		1: "PRIORITY_LOW",
		2: "PRIORITY_HIGH",
	}
	InitiateTasksRequest_Priority_value = map[string]int32{
		"PRIORITY_UNSPECIFIED": 0,
		"PRIORITY_LOW":         1,
		"PRIORITY_HIGH":        2,
	}
)

func (x InitiateTasksRequest_Priority) Enum() *InitiateTasksRequest_Priority {
	p := new(InitiateTasksRequest_Priority)
	*p = x
	return p
}

func (x InitiateTasksRequest_Priority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateTasksRequest_Priority) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_investment_service_proto_enumTypes[1].Descriptor()
}

func (InitiateTasksRequest_Priority) Type() protoreflect.EnumType {
	return &file_api_analyser_investment_service_proto_enumTypes[1]
}

func (x InitiateTasksRequest_Priority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateTasksRequest_Priority.Descriptor instead.
func (InitiateTasksRequest_Priority) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{2, 0}
}

type InitiateTasksResponse_Status int32

const (
	InitiateTasksResponse_OK InitiateTasksResponse_Status = 0
	// internal error while initiating the task
	InitiateTasksResponse_INTERNAL InitiateTasksResponse_Status = 13
)

// Enum value maps for InitiateTasksResponse_Status.
var (
	InitiateTasksResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	InitiateTasksResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x InitiateTasksResponse_Status) Enum() *InitiateTasksResponse_Status {
	p := new(InitiateTasksResponse_Status)
	*p = x
	return p
}

func (x InitiateTasksResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateTasksResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_investment_service_proto_enumTypes[2].Descriptor()
}

func (InitiateTasksResponse_Status) Type() protoreflect.EnumType {
	return &file_api_analyser_investment_service_proto_enumTypes[2]
}

func (x InitiateTasksResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateTasksResponse_Status.Descriptor instead.
func (InitiateTasksResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetMFPortfolioHistoryResponse_Status int32

const (
	GetMFPortfolioHistoryResponse_OK GetMFPortfolioHistoryResponse_Status = 0
	// in case task is not found
	GetMFPortfolioHistoryResponse_NOT_FOUND GetMFPortfolioHistoryResponse_Status = 5
	// internal error while fetching the task status
	GetMFPortfolioHistoryResponse_INTERNAL GetMFPortfolioHistoryResponse_Status = 13
)

// Enum value maps for GetMFPortfolioHistoryResponse_Status.
var (
	GetMFPortfolioHistoryResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetMFPortfolioHistoryResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetMFPortfolioHistoryResponse_Status) Enum() *GetMFPortfolioHistoryResponse_Status {
	p := new(GetMFPortfolioHistoryResponse_Status)
	*p = x
	return p
}

func (x GetMFPortfolioHistoryResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMFPortfolioHistoryResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_investment_service_proto_enumTypes[3].Descriptor()
}

func (GetMFPortfolioHistoryResponse_Status) Type() protoreflect.EnumType {
	return &file_api_analyser_investment_service_proto_enumTypes[3]
}

func (x GetMFPortfolioHistoryResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMFPortfolioHistoryResponse_Status.Descriptor instead.
func (GetMFPortfolioHistoryResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{6, 0}
}

type GetMFSchemeAnalyticsResponse_Status int32

const (
	GetMFSchemeAnalyticsResponse_OK GetMFSchemeAnalyticsResponse_Status = 0
	// in case task is not found
	GetMFSchemeAnalyticsResponse_NOT_FOUND GetMFSchemeAnalyticsResponse_Status = 5
	// internal error while fetching the task status
	GetMFSchemeAnalyticsResponse_INTERNAL GetMFSchemeAnalyticsResponse_Status = 13
)

// Enum value maps for GetMFSchemeAnalyticsResponse_Status.
var (
	GetMFSchemeAnalyticsResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "NOT_FOUND",
		13: "INTERNAL",
	}
	GetMFSchemeAnalyticsResponse_Status_value = map[string]int32{
		"OK":        0,
		"NOT_FOUND": 5,
		"INTERNAL":  13,
	}
)

func (x GetMFSchemeAnalyticsResponse_Status) Enum() *GetMFSchemeAnalyticsResponse_Status {
	p := new(GetMFSchemeAnalyticsResponse_Status)
	*p = x
	return p
}

func (x GetMFSchemeAnalyticsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetMFSchemeAnalyticsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_investment_service_proto_enumTypes[4].Descriptor()
}

func (GetMFSchemeAnalyticsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_analyser_investment_service_proto_enumTypes[4]
}

func (x GetMFSchemeAnalyticsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetMFSchemeAnalyticsResponse_Status.Descriptor instead.
func (GetMFSchemeAnalyticsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{10, 0}
}

type DeleteAllMfPortfolioHistoryResponse_Status int32

const (
	DeleteAllMfPortfolioHistoryResponse_OK       DeleteAllMfPortfolioHistoryResponse_Status = 0
	DeleteAllMfPortfolioHistoryResponse_INTERNAL DeleteAllMfPortfolioHistoryResponse_Status = 13
)

// Enum value maps for DeleteAllMfPortfolioHistoryResponse_Status.
var (
	DeleteAllMfPortfolioHistoryResponse_Status_name = map[int32]string{
		0:  "OK",
		13: "INTERNAL",
	}
	DeleteAllMfPortfolioHistoryResponse_Status_value = map[string]int32{
		"OK":       0,
		"INTERNAL": 13,
	}
)

func (x DeleteAllMfPortfolioHistoryResponse_Status) Enum() *DeleteAllMfPortfolioHistoryResponse_Status {
	p := new(DeleteAllMfPortfolioHistoryResponse_Status)
	*p = x
	return p
}

func (x DeleteAllMfPortfolioHistoryResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeleteAllMfPortfolioHistoryResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_analyser_investment_service_proto_enumTypes[5].Descriptor()
}

func (DeleteAllMfPortfolioHistoryResponse_Status) Type() protoreflect.EnumType {
	return &file_api_analyser_investment_service_proto_enumTypes[5]
}

func (x DeleteAllMfPortfolioHistoryResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeleteAllMfPortfolioHistoryResponse_Status.Descriptor instead.
func (DeleteAllMfPortfolioHistoryResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{14, 0}
}

type GetTaskStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actorId of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Types that are assignable to GetBy:
	//
	//	*GetTaskStatusRequest_TaskId
	//	*GetTaskStatusRequest_TaskName
	GetBy isGetTaskStatusRequest_GetBy `protobuf_oneof:"get_by"`
}

func (x *GetTaskStatusRequest) Reset() {
	*x = GetTaskStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskStatusRequest) ProtoMessage() {}

func (x *GetTaskStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskStatusRequest.ProtoReflect.Descriptor instead.
func (*GetTaskStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetTaskStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (m *GetTaskStatusRequest) GetGetBy() isGetTaskStatusRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetTaskStatusRequest) GetTaskId() string {
	if x, ok := x.GetGetBy().(*GetTaskStatusRequest_TaskId); ok {
		return x.TaskId
	}
	return ""
}

func (x *GetTaskStatusRequest) GetTaskName() model.TaskName {
	if x, ok := x.GetGetBy().(*GetTaskStatusRequest_TaskName); ok {
		return x.TaskName
	}
	return model.TaskName(0)
}

type isGetTaskStatusRequest_GetBy interface {
	isGetTaskStatusRequest_GetBy()
}

type GetTaskStatusRequest_TaskId struct {
	// Status of the particular task is returned
	TaskId string `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3,oneof"`
}

type GetTaskStatusRequest_TaskName struct {
	// Status of latest task (by updated_at) is returned by the rpc in last 1 day.
	// If no task exists, then no record found status is returned
	TaskName model.TaskName `protobuf:"varint,3,opt,name=task_name,json=taskName,proto3,enum=analyser.investment.model.TaskName,oneof"`
}

func (*GetTaskStatusRequest_TaskId) isGetTaskStatusRequest_GetBy() {}

func (*GetTaskStatusRequest_TaskName) isGetTaskStatusRequest_GetBy() {}

type GetTaskStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Status of the task
	TaskStatus model.TaskStatus `protobuf:"varint,2,opt,name=task_status,json=taskStatus,proto3,enum=analyser.investment.model.TaskStatus" json:"task_status,omitempty"`
}

func (x *GetTaskStatusResponse) Reset() {
	*x = GetTaskStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskStatusResponse) ProtoMessage() {}

func (x *GetTaskStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskStatusResponse.ProtoReflect.Descriptor instead.
func (*GetTaskStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetTaskStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTaskStatusResponse) GetTaskStatus() model.TaskStatus {
	if x != nil {
		return x.TaskStatus
	}
	return model.TaskStatus(0)
}

type InitiateTasksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId  string                        `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Tasks    []*TaskDetails                `protobuf:"bytes,2,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Priority InitiateTasksRequest_Priority `protobuf:"varint,3,opt,name=priority,proto3,enum=analyser.investment.InitiateTasksRequest_Priority" json:"priority,omitempty"`
}

func (x *InitiateTasksRequest) Reset() {
	*x = InitiateTasksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateTasksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateTasksRequest) ProtoMessage() {}

func (x *InitiateTasksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateTasksRequest.ProtoReflect.Descriptor instead.
func (*InitiateTasksRequest) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{2}
}

func (x *InitiateTasksRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiateTasksRequest) GetTasks() []*TaskDetails {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *InitiateTasksRequest) GetPriority() InitiateTasksRequest_Priority {
	if x != nil {
		return x.Priority
	}
	return InitiateTasksRequest_PRIORITY_UNSPECIFIED
}

type InitiateTasksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *InitiateTasksResponse) Reset() {
	*x = InitiateTasksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateTasksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateTasksResponse) ProtoMessage() {}

func (x *InitiateTasksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateTasksResponse.ProtoReflect.Descriptor instead.
func (*InitiateTasksResponse) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{3}
}

func (x *InitiateTasksResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type TaskDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The task which needs to be scheduled
	TaskName model.TaskName `protobuf:"varint,1,opt,name=task_name,json=taskName,proto3,enum=analyser.investment.model.TaskName" json:"task_name,omitempty"`
	// The params specific to the task
	TaskParams *model.TaskParams `protobuf:"bytes,2,opt,name=task_params,json=taskParams,proto3" json:"task_params,omitempty"`
}

func (x *TaskDetails) Reset() {
	*x = TaskDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDetails) ProtoMessage() {}

func (x *TaskDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDetails.ProtoReflect.Descriptor instead.
func (*TaskDetails) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{4}
}

func (x *TaskDetails) GetTaskName() model.TaskName {
	if x != nil {
		return x.TaskName
	}
	return model.TaskName(0)
}

func (x *TaskDetails) GetTaskParams() *model.TaskParams {
	if x != nil {
		return x.TaskParams
	}
	return nil
}

// The portfolio history will be fetched for the given actor with the given given TimeDuration in the specified date range
type GetMFPortfolioHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId      string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	FromDate     *date.Date             `protobuf:"bytes,2,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	ToDate       *date.Date             `protobuf:"bytes,3,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	TimeDuration enums.TimeDurationType `protobuf:"varint,4,opt,name=time_duration,json=timeDuration,proto3,enum=analyser.enums.TimeDurationType" json:"time_duration,omitempty"`
}

func (x *GetMFPortfolioHistoryRequest) Reset() {
	*x = GetMFPortfolioHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMFPortfolioHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMFPortfolioHistoryRequest) ProtoMessage() {}

func (x *GetMFPortfolioHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMFPortfolioHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetMFPortfolioHistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetMFPortfolioHistoryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetMFPortfolioHistoryRequest) GetFromDate() *date.Date {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetMFPortfolioHistoryRequest) GetToDate() *date.Date {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *GetMFPortfolioHistoryRequest) GetTimeDuration() enums.TimeDurationType {
	if x != nil {
		return x.TimeDuration
	}
	return enums.TimeDurationType(0)
}

type GetMFPortfolioHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                   *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Details                  []*MFPortfolioDetails `protobuf:"bytes,2,rep,name=details,proto3" json:"details,omitempty"`
	EnrichedPortfolioDetails *PortfolioChanges     `protobuf:"bytes,3,opt,name=enriched_portfolio_details,json=enrichedPortfolioDetails,proto3" json:"enriched_portfolio_details,omitempty"`
}

func (x *GetMFPortfolioHistoryResponse) Reset() {
	*x = GetMFPortfolioHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMFPortfolioHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMFPortfolioHistoryResponse) ProtoMessage() {}

func (x *GetMFPortfolioHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMFPortfolioHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetMFPortfolioHistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetMFPortfolioHistoryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMFPortfolioHistoryResponse) GetDetails() []*MFPortfolioDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *GetMFPortfolioHistoryResponse) GetEnrichedPortfolioDetails() *PortfolioChanges {
	if x != nil {
		return x.EnrichedPortfolioDetails
	}
	return nil
}

type MFPortfolioDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	History           *model.MFPortFolioHistory `protobuf:"bytes,1,opt,name=history,proto3" json:"history,omitempty"`
	ChangesInDuration *PortfolioChanges         `protobuf:"bytes,2,opt,name=changes_in_duration,json=changesInDuration,proto3" json:"changes_in_duration,omitempty"`
}

func (x *MFPortfolioDetails) Reset() {
	*x = MFPortfolioDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MFPortfolioDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MFPortfolioDetails) ProtoMessage() {}

func (x *MFPortfolioDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MFPortfolioDetails.ProtoReflect.Descriptor instead.
func (*MFPortfolioDetails) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{7}
}

func (x *MFPortfolioDetails) GetHistory() *model.MFPortFolioHistory {
	if x != nil {
		return x.History
	}
	return nil
}

func (x *MFPortfolioDetails) GetChangesInDuration() *PortfolioChanges {
	if x != nil {
		return x.ChangesInDuration
	}
	return nil
}

type PortfolioChanges struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// invested_amount is sum of all buy transactions in the given duration
	InvestedAmount *money.Money `protobuf:"bytes,1,opt,name=invested_amount,json=investedAmount,proto3" json:"invested_amount,omitempty"`
	// withdrawn_amount is sum of all sell transactions in the given duration
	WithdrawnAmount *money.Money `protobuf:"bytes,2,opt,name=withdrawn_amount,json=withdrawnAmount,proto3" json:"withdrawn_amount,omitempty"`
	// portfolio_change is the change in portfolio value from the last history date with the given duration
	PortfolioChange *money.Money `protobuf:"bytes,3,opt,name=portfolio_change,json=portfolioChange,proto3" json:"portfolio_change,omitempty"`
	// portfolio_growth_absolute is portfolio absolute growth in the given month/year (based on the TimeDuration).
	PortfolioGrowthAbsolute *money.Money `protobuf:"bytes,4,opt,name=portfolio_growth_absolute,json=portfolioGrowthAbsolute,proto3" json:"portfolio_growth_absolute,omitempty"`
	// portfolio_growth_percentage is portfolio growth percentage for the given month/year (based on the TimeDuration).
	PortfolioGrowthPercentage float64 `protobuf:"fixed64,5,opt,name=portfolio_growth_percentage,json=portfolioGrowthPercentage,proto3" json:"portfolio_growth_percentage,omitempty"`
}

func (x *PortfolioChanges) Reset() {
	*x = PortfolioChanges{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioChanges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioChanges) ProtoMessage() {}

func (x *PortfolioChanges) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioChanges.ProtoReflect.Descriptor instead.
func (*PortfolioChanges) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{8}
}

func (x *PortfolioChanges) GetInvestedAmount() *money.Money {
	if x != nil {
		return x.InvestedAmount
	}
	return nil
}

func (x *PortfolioChanges) GetWithdrawnAmount() *money.Money {
	if x != nil {
		return x.WithdrawnAmount
	}
	return nil
}

func (x *PortfolioChanges) GetPortfolioChange() *money.Money {
	if x != nil {
		return x.PortfolioChange
	}
	return nil
}

func (x *PortfolioChanges) GetPortfolioGrowthAbsolute() *money.Money {
	if x != nil {
		return x.PortfolioGrowthAbsolute
	}
	return nil
}

func (x *PortfolioChanges) GetPortfolioGrowthPercentage() float64 {
	if x != nil {
		return x.PortfolioGrowthPercentage
	}
	return 0
}

type GetMFSchemeAnalyticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetMFSchemeAnalyticsRequest) Reset() {
	*x = GetMFSchemeAnalyticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMFSchemeAnalyticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMFSchemeAnalyticsRequest) ProtoMessage() {}

func (x *GetMFSchemeAnalyticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMFSchemeAnalyticsRequest.ProtoReflect.Descriptor instead.
func (*GetMFSchemeAnalyticsRequest) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetMFSchemeAnalyticsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetMFSchemeAnalyticsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// analytics for individual schemes across all folios
	Schemes []*EnrichedMfSchemeAnalytics `protobuf:"bytes,2,rep,name=schemes,proto3" json:"schemes,omitempty"`
	// analytics over entire portfolio
	Portfolio *EnrichedMFPortfolioAnalytics `protobuf:"bytes,3,opt,name=portfolio,proto3" json:"portfolio,omitempty"`
}

func (x *GetMFSchemeAnalyticsResponse) Reset() {
	*x = GetMFSchemeAnalyticsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMFSchemeAnalyticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMFSchemeAnalyticsResponse) ProtoMessage() {}

func (x *GetMFSchemeAnalyticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMFSchemeAnalyticsResponse.ProtoReflect.Descriptor instead.
func (*GetMFSchemeAnalyticsResponse) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetMFSchemeAnalyticsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMFSchemeAnalyticsResponse) GetSchemes() []*EnrichedMfSchemeAnalytics {
	if x != nil {
		return x.Schemes
	}
	return nil
}

func (x *GetMFSchemeAnalyticsResponse) GetPortfolio() *EnrichedMFPortfolioAnalytics {
	if x != nil {
		return x.Portfolio
	}
	return nil
}

// EnrichedMfSchemeAnalytics wraps persisted analytics and on the fly computed analytics for a scheme
type EnrichedMfSchemeAnalytics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Analytics *model.MfScheme `protobuf:"bytes,1,opt,name=analytics,proto3" json:"analytics,omitempty"`
}

func (x *EnrichedMfSchemeAnalytics) Reset() {
	*x = EnrichedMfSchemeAnalytics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrichedMfSchemeAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrichedMfSchemeAnalytics) ProtoMessage() {}

func (x *EnrichedMfSchemeAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrichedMfSchemeAnalytics.ProtoReflect.Descriptor instead.
func (*EnrichedMfSchemeAnalytics) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{11}
}

func (x *EnrichedMfSchemeAnalytics) GetAnalytics() *model.MfScheme {
	if x != nil {
		return x.Analytics
	}
	return nil
}

// EnrichedMFPortfolioAnalytics wraps persisted analytics and on the fly computed analytics for entire portfolio
type EnrichedMFPortfolioAnalytics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Portfolio *model.MFPortFolio `protobuf:"bytes,1,opt,name=portfolio,proto3" json:"portfolio,omitempty"`
}

func (x *EnrichedMFPortfolioAnalytics) Reset() {
	*x = EnrichedMFPortfolioAnalytics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnrichedMFPortfolioAnalytics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnrichedMFPortfolioAnalytics) ProtoMessage() {}

func (x *EnrichedMFPortfolioAnalytics) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnrichedMFPortfolioAnalytics.ProtoReflect.Descriptor instead.
func (*EnrichedMFPortfolioAnalytics) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{12}
}

func (x *EnrichedMFPortfolioAnalytics) GetPortfolio() *model.MFPortFolio {
	if x != nil {
		return x.Portfolio
	}
	return nil
}

type DeleteAllMfPortfolioHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *DeleteAllMfPortfolioHistoryRequest) Reset() {
	*x = DeleteAllMfPortfolioHistoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAllMfPortfolioHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAllMfPortfolioHistoryRequest) ProtoMessage() {}

func (x *DeleteAllMfPortfolioHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAllMfPortfolioHistoryRequest.ProtoReflect.Descriptor instead.
func (*DeleteAllMfPortfolioHistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteAllMfPortfolioHistoryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type DeleteAllMfPortfolioHistoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteAllMfPortfolioHistoryResponse) Reset() {
	*x = DeleteAllMfPortfolioHistoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_analyser_investment_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAllMfPortfolioHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAllMfPortfolioHistoryResponse) ProtoMessage() {}

func (x *DeleteAllMfPortfolioHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_analyser_investment_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAllMfPortfolioHistoryResponse.ProtoReflect.Descriptor instead.
func (*DeleteAllMfPortfolioHistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_analyser_investment_service_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteAllMfPortfolioHistoryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_analyser_investment_service_proto protoreflect.FileDescriptor

var file_api_analyser_investment_service_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65,
	0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x1e, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x66, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6d, 0x66,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x42, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e,
	0x61, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x79, 0x22, 0xb3, 0x01,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x0b,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0x8d, 0x02, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x36, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x4e, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08,
	0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x49, 0x0a, 0x08, 0x50, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10,
	0x0a, 0x0c, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x4f, 0x57, 0x10, 0x01,
	0x12, 0x11, 0x0a, 0x0d, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x48, 0x49, 0x47,
	0x48, 0x10, 0x02, 0x22, 0x5c, 0x0a, 0x15, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f,
	0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x0d, 0x22, 0xab, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x4a, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x50, 0x0a,
	0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0x83, 0x02, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34,
	0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x74, 0x6f,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9b, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x50,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x07,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x63, 0x0a, 0x1a, 0x65, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x18, 0x65, 0x6e, 0x72, 0x69,
	0x63, 0x68, 0x65, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x2d, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x0d, 0x22, 0xb4, 0x01, 0x0a, 0x12, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x07, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x46, 0x6f,
	0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x07, 0x68, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x12, 0x55, 0x0a, 0x13, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x5f, 0x69,
	0x6e, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x49, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xdd, 0x02, 0x0a, 0x10, 0x50,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12,
	0x3b, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x10,
	0x77, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x77, 0x69, 0x74, 0x68,
	0x64, 0x72, 0x61, 0x77, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x10, 0x70,
	0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4e, 0x0a, 0x19, 0x70, 0x6f,
	0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x61,
	0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x47, 0x72, 0x6f, 0x77,
	0x74, 0x68, 0x41, 0x62, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x1b, 0x70, 0x6f,
	0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x19, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68,
	0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x41, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x4d, 0x46, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x8d, 0x02,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6e, 0x72, 0x69, 0x63,
	0x68, 0x65, 0x64, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x4f, 0x0a,
	0x09, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x4d,
	0x46, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74,
	0x69, 0x63, 0x73, 0x52, 0x09, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x22, 0x2d,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0x5e, 0x0a,
	0x19, 0x45, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x41, 0x0a, 0x09, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x66, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x52, 0x09, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x22, 0x64, 0x0a,
	0x1c, 0x45, 0x6e, 0x72, 0x69, 0x63, 0x68, 0x65, 0x64, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x44, 0x0a,
	0x09, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x46, 0x50,
	0x6f, 0x72, 0x74, 0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x52, 0x09, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x22, 0x48, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c,
	0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x6a, 0x0a,
	0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1e, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x32, 0xf5, 0x04, 0x0a, 0x13, 0x49, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x12, 0x66, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66, 0x0a, 0x0d, 0x49, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72,
	0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7e, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x31, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x46, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x46, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90,
	0x01, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4d, 0x66, 0x50, 0x6f,
	0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x37,
	0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4d, 0x66,
	0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x4d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c,
	0x69, 0x6f, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x60, 0x0a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_analyser_investment_service_proto_rawDescOnce sync.Once
	file_api_analyser_investment_service_proto_rawDescData = file_api_analyser_investment_service_proto_rawDesc
)

func file_api_analyser_investment_service_proto_rawDescGZIP() []byte {
	file_api_analyser_investment_service_proto_rawDescOnce.Do(func() {
		file_api_analyser_investment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_analyser_investment_service_proto_rawDescData)
	})
	return file_api_analyser_investment_service_proto_rawDescData
}

var file_api_analyser_investment_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_analyser_investment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_api_analyser_investment_service_proto_goTypes = []interface{}{
	(GetTaskStatusResponse_Status)(0),               // 0: analyser.investment.GetTaskStatusResponse.Status
	(InitiateTasksRequest_Priority)(0),              // 1: analyser.investment.InitiateTasksRequest.Priority
	(InitiateTasksResponse_Status)(0),               // 2: analyser.investment.InitiateTasksResponse.Status
	(GetMFPortfolioHistoryResponse_Status)(0),       // 3: analyser.investment.GetMFPortfolioHistoryResponse.Status
	(GetMFSchemeAnalyticsResponse_Status)(0),        // 4: analyser.investment.GetMFSchemeAnalyticsResponse.Status
	(DeleteAllMfPortfolioHistoryResponse_Status)(0), // 5: analyser.investment.DeleteAllMfPortfolioHistoryResponse.Status
	(*GetTaskStatusRequest)(nil),                    // 6: analyser.investment.GetTaskStatusRequest
	(*GetTaskStatusResponse)(nil),                   // 7: analyser.investment.GetTaskStatusResponse
	(*InitiateTasksRequest)(nil),                    // 8: analyser.investment.InitiateTasksRequest
	(*InitiateTasksResponse)(nil),                   // 9: analyser.investment.InitiateTasksResponse
	(*TaskDetails)(nil),                             // 10: analyser.investment.TaskDetails
	(*GetMFPortfolioHistoryRequest)(nil),            // 11: analyser.investment.GetMFPortfolioHistoryRequest
	(*GetMFPortfolioHistoryResponse)(nil),           // 12: analyser.investment.GetMFPortfolioHistoryResponse
	(*MFPortfolioDetails)(nil),                      // 13: analyser.investment.MFPortfolioDetails
	(*PortfolioChanges)(nil),                        // 14: analyser.investment.PortfolioChanges
	(*GetMFSchemeAnalyticsRequest)(nil),             // 15: analyser.investment.GetMFSchemeAnalyticsRequest
	(*GetMFSchemeAnalyticsResponse)(nil),            // 16: analyser.investment.GetMFSchemeAnalyticsResponse
	(*EnrichedMfSchemeAnalytics)(nil),               // 17: analyser.investment.EnrichedMfSchemeAnalytics
	(*EnrichedMFPortfolioAnalytics)(nil),            // 18: analyser.investment.EnrichedMFPortfolioAnalytics
	(*DeleteAllMfPortfolioHistoryRequest)(nil),      // 19: analyser.investment.DeleteAllMfPortfolioHistoryRequest
	(*DeleteAllMfPortfolioHistoryResponse)(nil),     // 20: analyser.investment.DeleteAllMfPortfolioHistoryResponse
	(model.TaskName)(0),                             // 21: analyser.investment.model.TaskName
	(*rpc.Status)(nil),                              // 22: rpc.Status
	(model.TaskStatus)(0),                           // 23: analyser.investment.model.TaskStatus
	(*model.TaskParams)(nil),                        // 24: analyser.investment.model.TaskParams
	(*date.Date)(nil),                               // 25: google.type.Date
	(enums.TimeDurationType)(0),                     // 26: analyser.enums.TimeDurationType
	(*model.MFPortFolioHistory)(nil),                // 27: analyser.investment.model.MFPortFolioHistory
	(*money.Money)(nil),                             // 28: google.type.Money
	(*model.MfScheme)(nil),                          // 29: analyser.investment.model.MfScheme
	(*model.MFPortFolio)(nil),                       // 30: analyser.investment.model.MFPortFolio
}
var file_api_analyser_investment_service_proto_depIdxs = []int32{
	21, // 0: analyser.investment.GetTaskStatusRequest.task_name:type_name -> analyser.investment.model.TaskName
	22, // 1: analyser.investment.GetTaskStatusResponse.status:type_name -> rpc.Status
	23, // 2: analyser.investment.GetTaskStatusResponse.task_status:type_name -> analyser.investment.model.TaskStatus
	10, // 3: analyser.investment.InitiateTasksRequest.tasks:type_name -> analyser.investment.TaskDetails
	1,  // 4: analyser.investment.InitiateTasksRequest.priority:type_name -> analyser.investment.InitiateTasksRequest.Priority
	22, // 5: analyser.investment.InitiateTasksResponse.status:type_name -> rpc.Status
	21, // 6: analyser.investment.TaskDetails.task_name:type_name -> analyser.investment.model.TaskName
	24, // 7: analyser.investment.TaskDetails.task_params:type_name -> analyser.investment.model.TaskParams
	25, // 8: analyser.investment.GetMFPortfolioHistoryRequest.from_date:type_name -> google.type.Date
	25, // 9: analyser.investment.GetMFPortfolioHistoryRequest.to_date:type_name -> google.type.Date
	26, // 10: analyser.investment.GetMFPortfolioHistoryRequest.time_duration:type_name -> analyser.enums.TimeDurationType
	22, // 11: analyser.investment.GetMFPortfolioHistoryResponse.status:type_name -> rpc.Status
	13, // 12: analyser.investment.GetMFPortfolioHistoryResponse.details:type_name -> analyser.investment.MFPortfolioDetails
	14, // 13: analyser.investment.GetMFPortfolioHistoryResponse.enriched_portfolio_details:type_name -> analyser.investment.PortfolioChanges
	27, // 14: analyser.investment.MFPortfolioDetails.history:type_name -> analyser.investment.model.MFPortFolioHistory
	14, // 15: analyser.investment.MFPortfolioDetails.changes_in_duration:type_name -> analyser.investment.PortfolioChanges
	28, // 16: analyser.investment.PortfolioChanges.invested_amount:type_name -> google.type.Money
	28, // 17: analyser.investment.PortfolioChanges.withdrawn_amount:type_name -> google.type.Money
	28, // 18: analyser.investment.PortfolioChanges.portfolio_change:type_name -> google.type.Money
	28, // 19: analyser.investment.PortfolioChanges.portfolio_growth_absolute:type_name -> google.type.Money
	22, // 20: analyser.investment.GetMFSchemeAnalyticsResponse.status:type_name -> rpc.Status
	17, // 21: analyser.investment.GetMFSchemeAnalyticsResponse.schemes:type_name -> analyser.investment.EnrichedMfSchemeAnalytics
	18, // 22: analyser.investment.GetMFSchemeAnalyticsResponse.portfolio:type_name -> analyser.investment.EnrichedMFPortfolioAnalytics
	29, // 23: analyser.investment.EnrichedMfSchemeAnalytics.analytics:type_name -> analyser.investment.model.MfScheme
	30, // 24: analyser.investment.EnrichedMFPortfolioAnalytics.portfolio:type_name -> analyser.investment.model.MFPortFolio
	22, // 25: analyser.investment.DeleteAllMfPortfolioHistoryResponse.status:type_name -> rpc.Status
	6,  // 26: analyser.investment.InvestmentAnalytics.GetTaskStatus:input_type -> analyser.investment.GetTaskStatusRequest
	8,  // 27: analyser.investment.InvestmentAnalytics.InitiateTasks:input_type -> analyser.investment.InitiateTasksRequest
	11, // 28: analyser.investment.InvestmentAnalytics.GetMFPortfolioHistory:input_type -> analyser.investment.GetMFPortfolioHistoryRequest
	15, // 29: analyser.investment.InvestmentAnalytics.GetMFSchemeAnalytics:input_type -> analyser.investment.GetMFSchemeAnalyticsRequest
	19, // 30: analyser.investment.InvestmentAnalytics.DeleteAllMfPortfolioHistory:input_type -> analyser.investment.DeleteAllMfPortfolioHistoryRequest
	7,  // 31: analyser.investment.InvestmentAnalytics.GetTaskStatus:output_type -> analyser.investment.GetTaskStatusResponse
	9,  // 32: analyser.investment.InvestmentAnalytics.InitiateTasks:output_type -> analyser.investment.InitiateTasksResponse
	12, // 33: analyser.investment.InvestmentAnalytics.GetMFPortfolioHistory:output_type -> analyser.investment.GetMFPortfolioHistoryResponse
	16, // 34: analyser.investment.InvestmentAnalytics.GetMFSchemeAnalytics:output_type -> analyser.investment.GetMFSchemeAnalyticsResponse
	20, // 35: analyser.investment.InvestmentAnalytics.DeleteAllMfPortfolioHistory:output_type -> analyser.investment.DeleteAllMfPortfolioHistoryResponse
	31, // [31:36] is the sub-list for method output_type
	26, // [26:31] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_analyser_investment_service_proto_init() }
func file_api_analyser_investment_service_proto_init() {
	if File_api_analyser_investment_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_analyser_investment_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateTasksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateTasksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMFPortfolioHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMFPortfolioHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MFPortfolioDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioChanges); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMFSchemeAnalyticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMFSchemeAnalyticsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrichedMfSchemeAnalytics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnrichedMFPortfolioAnalytics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAllMfPortfolioHistoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_analyser_investment_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAllMfPortfolioHistoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_analyser_investment_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*GetTaskStatusRequest_TaskId)(nil),
		(*GetTaskStatusRequest_TaskName)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_analyser_investment_service_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_analyser_investment_service_proto_goTypes,
		DependencyIndexes: file_api_analyser_investment_service_proto_depIdxs,
		EnumInfos:         file_api_analyser_investment_service_proto_enumTypes,
		MessageInfos:      file_api_analyser_investment_service_proto_msgTypes,
	}.Build()
	File_api_analyser_investment_service_proto = out.File
	file_api_analyser_investment_service_proto_rawDesc = nil
	file_api_analyser_investment_service_proto_goTypes = nil
	file_api_analyser_investment_service_proto_depIdxs = nil
}
