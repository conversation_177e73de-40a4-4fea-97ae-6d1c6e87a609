syntax = "proto3";

package frontend.tiering.benefits.external;

import "api/typesv2/common/device.proto";

option go_package = "github.com/epifi/gamma/api/frontend/tiering/benefits/external";
option java_package = "com.github.epifi.gamma.api.frontend.tiering.benefits.external";

message Benefit {
  // Heading of the benefit
  // eg: Free debit card
  string heading = 1;
  // Sub heading of the benefit
  // eg: Worth ₹499
  string sub_heading = 2;
  // Url of the benefit image
  string image_url = 3;
  // Approximate cash equivalent of the benefit
  // eg: If benefit is "Free debit card" worth 499 then populate this field with 499
  uint32 cash_equivalent = 4;
  // NOTE: populate these bottom sheets only if bottom sheet needs to shown for a benefit
  // heading when benefit is opened as bottom sheet
  // eg: Get a free debit card
  string bottom_sheet_heading = 5;
  // sub heading when benefit is opened as bottom sheet
  // eg: When you upgrade to Fi Plus, you will get a slick, new VISA Platinum debit card worth ₹499
  string bottom_sheet_sub_heading = 6;
}

// Request that will come from tiering
message GetBenefitsRequest {
  // ActorId to fetch benefits for
  string actor_id = 1;
  // Current app version of the user
  uint32 app_version = 2;
  // Current app platform of the user
  api.typesv2.common.Platform app_platform = 3;
}

// Response that needs to be returned by child services
message GetBenefitsResponse {
  // Map of benefits with key as string of frontend.tiering.enum.Tier
  map<string, BenefitsList> benefits_map = 1;
}

message BenefitsList {
  repeated benefits.external.Benefit benefits_list = 1;
}

// Enum to represent different sections in all plans page
enum BenefitsSection {
  BENEFITS_SECTION_UNSPECIFIED = 0;
  BENEFITS_SECTION_DEFAULT = 1;
  BENEFITS_SECTION_UNLOCKED = 2;
  BENEFITS_SECTION_LOCKED = 3;
  BENEFITS_SECTION_HERO = 4;
  BENEFITS_SECTION_HERO_V2 = 5;
  BENEFITS_SECTION_HERO_V3 = 6;
}
