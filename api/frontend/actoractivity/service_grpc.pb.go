// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/actoractivity/service.proto

package actoractivity

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ActorActivity_GetActivities_FullMethodName        = "/frontend.actoractivity.ActorActivity/GetActivities"
	ActorActivity_GetSimilarActivities_FullMethodName = "/frontend.actoractivity.ActorActivity/GetSimilarActivities"
)

// ActorActivityClient is the client API for ActorActivity service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ActorActivityClient interface {
	// A paginated rpc to fetch the activities belonging to a combination of actor and
	// accountIds present in the request
	// If page size is not passed in the request, the default page size is 30
	//
	// Along with activity response contains
	// before_token, after_token representing the location of page fetched.
	//
	// # Currently all the activity details will be fetched from order
	//
	// NOTE - one of the account filter or the card ids should be present
	// if both account filters and card ids are present, all the activities associated with the account
	// and the card will be returned.
	GetActivities(ctx context.Context, in *GetActivitiesRequest, opts ...grpc.CallOption) (*GetActivitiesResponse, error)
	// rpc to return similar transactions count with text to display
	GetSimilarActivities(ctx context.Context, in *GetSimilarActivitiesRequest, opts ...grpc.CallOption) (*GetSimilarActivitiesResponse, error)
}

type actorActivityClient struct {
	cc grpc.ClientConnInterface
}

func NewActorActivityClient(cc grpc.ClientConnInterface) ActorActivityClient {
	return &actorActivityClient{cc}
}

func (c *actorActivityClient) GetActivities(ctx context.Context, in *GetActivitiesRequest, opts ...grpc.CallOption) (*GetActivitiesResponse, error) {
	out := new(GetActivitiesResponse)
	err := c.cc.Invoke(ctx, ActorActivity_GetActivities_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *actorActivityClient) GetSimilarActivities(ctx context.Context, in *GetSimilarActivitiesRequest, opts ...grpc.CallOption) (*GetSimilarActivitiesResponse, error) {
	out := new(GetSimilarActivitiesResponse)
	err := c.cc.Invoke(ctx, ActorActivity_GetSimilarActivities_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ActorActivityServer is the server API for ActorActivity service.
// All implementations should embed UnimplementedActorActivityServer
// for forward compatibility
type ActorActivityServer interface {
	// A paginated rpc to fetch the activities belonging to a combination of actor and
	// accountIds present in the request
	// If page size is not passed in the request, the default page size is 30
	//
	// Along with activity response contains
	// before_token, after_token representing the location of page fetched.
	//
	// # Currently all the activity details will be fetched from order
	//
	// NOTE - one of the account filter or the card ids should be present
	// if both account filters and card ids are present, all the activities associated with the account
	// and the card will be returned.
	GetActivities(context.Context, *GetActivitiesRequest) (*GetActivitiesResponse, error)
	// rpc to return similar transactions count with text to display
	GetSimilarActivities(context.Context, *GetSimilarActivitiesRequest) (*GetSimilarActivitiesResponse, error)
}

// UnimplementedActorActivityServer should be embedded to have forward compatible implementations.
type UnimplementedActorActivityServer struct {
}

func (UnimplementedActorActivityServer) GetActivities(context.Context, *GetActivitiesRequest) (*GetActivitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActivities not implemented")
}
func (UnimplementedActorActivityServer) GetSimilarActivities(context.Context, *GetSimilarActivitiesRequest) (*GetSimilarActivitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimilarActivities not implemented")
}

// UnsafeActorActivityServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ActorActivityServer will
// result in compilation errors.
type UnsafeActorActivityServer interface {
	mustEmbedUnimplementedActorActivityServer()
}

func RegisterActorActivityServer(s grpc.ServiceRegistrar, srv ActorActivityServer) {
	s.RegisterService(&ActorActivity_ServiceDesc, srv)
}

func _ActorActivity_GetActivities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActorActivityServer).GetActivities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActorActivity_GetActivities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActorActivityServer).GetActivities(ctx, req.(*GetActivitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ActorActivity_GetSimilarActivities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimilarActivitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ActorActivityServer).GetSimilarActivities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ActorActivity_GetSimilarActivities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ActorActivityServer).GetSimilarActivities(ctx, req.(*GetSimilarActivitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ActorActivity_ServiceDesc is the grpc.ServiceDesc for ActorActivity service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ActorActivity_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.actoractivity.ActorActivity",
	HandlerType: (*ActorActivityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetActivities",
			Handler:    _ActorActivity_GetActivities_Handler,
		},
		{
			MethodName: "GetSimilarActivities",
			Handler:    _ActorActivity_GetSimilarActivities_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/actoractivity/service.proto",
}
