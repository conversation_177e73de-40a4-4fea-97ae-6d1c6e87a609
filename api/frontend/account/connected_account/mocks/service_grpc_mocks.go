// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/account/connected_account/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	connected_account "github.com/epifi/gamma/api/frontend/account/connected_account"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConnectedAccountClient is a mock of ConnectedAccountClient interface.
type MockConnectedAccountClient struct {
	ctrl     *gomock.Controller
	recorder *MockConnectedAccountClientMockRecorder
}

// MockConnectedAccountClientMockRecorder is the mock recorder for MockConnectedAccountClient.
type MockConnectedAccountClientMockRecorder struct {
	mock *MockConnectedAccountClient
}

// NewMockConnectedAccountClient creates a new mock instance.
func NewMockConnectedAccountClient(ctrl *gomock.Controller) *MockConnectedAccountClient {
	mock := &MockConnectedAccountClient{ctrl: ctrl}
	mock.recorder = &MockConnectedAccountClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConnectedAccountClient) EXPECT() *MockConnectedAccountClientMockRecorder {
	return m.recorder
}

// FetchAccounts mocks base method.
func (m *MockConnectedAccountClient) FetchAccounts(ctx context.Context, in *connected_account.FetchAccountsRequest, opts ...grpc.CallOption) (*connected_account.FetchAccountsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchAccounts", varargs...)
	ret0, _ := ret[0].(*connected_account.FetchAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAccounts indicates an expected call of FetchAccounts.
func (mr *MockConnectedAccountClientMockRecorder) FetchAccounts(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAccounts", reflect.TypeOf((*MockConnectedAccountClient)(nil).FetchAccounts), varargs...)
}

// InitiateAccountLinking mocks base method.
func (m *MockConnectedAccountClient) InitiateAccountLinking(ctx context.Context, in *connected_account.InitiateAccountLinkingRequest, opts ...grpc.CallOption) (*connected_account.InitiateAccountLinkingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateAccountLinking", varargs...)
	ret0, _ := ret[0].(*connected_account.InitiateAccountLinkingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateAccountLinking indicates an expected call of InitiateAccountLinking.
func (mr *MockConnectedAccountClientMockRecorder) InitiateAccountLinking(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAccountLinking", reflect.TypeOf((*MockConnectedAccountClient)(nil).InitiateAccountLinking), varargs...)
}

// InitiateConsent mocks base method.
func (m *MockConnectedAccountClient) InitiateConsent(ctx context.Context, in *connected_account.InitiateConsentRequest, opts ...grpc.CallOption) (*connected_account.InitiateConsentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateConsent", varargs...)
	ret0, _ := ret[0].(*connected_account.InitiateConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateConsent indicates an expected call of InitiateConsent.
func (mr *MockConnectedAccountClientMockRecorder) InitiateConsent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateConsent", reflect.TypeOf((*MockConnectedAccountClient)(nil).InitiateConsent), varargs...)
}

// SyncAccount mocks base method.
func (m *MockConnectedAccountClient) SyncAccount(ctx context.Context, in *connected_account.SyncAccountRequest, opts ...grpc.CallOption) (*connected_account.SyncAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SyncAccount", varargs...)
	ret0, _ := ret[0].(*connected_account.SyncAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncAccount indicates an expected call of SyncAccount.
func (mr *MockConnectedAccountClientMockRecorder) SyncAccount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncAccount", reflect.TypeOf((*MockConnectedAccountClient)(nil).SyncAccount), varargs...)
}

// VerifyAccountLinkingOTP mocks base method.
func (m *MockConnectedAccountClient) VerifyAccountLinkingOTP(ctx context.Context, in *connected_account.VerifyAccountLinkingOTPRequest, opts ...grpc.CallOption) (*connected_account.VerifyAccountLinkingOTPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyAccountLinkingOTP", varargs...)
	ret0, _ := ret[0].(*connected_account.VerifyAccountLinkingOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAccountLinkingOTP indicates an expected call of VerifyAccountLinkingOTP.
func (mr *MockConnectedAccountClientMockRecorder) VerifyAccountLinkingOTP(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAccountLinkingOTP", reflect.TypeOf((*MockConnectedAccountClient)(nil).VerifyAccountLinkingOTP), varargs...)
}

// MockConnectedAccountServer is a mock of ConnectedAccountServer interface.
type MockConnectedAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockConnectedAccountServerMockRecorder
}

// MockConnectedAccountServerMockRecorder is the mock recorder for MockConnectedAccountServer.
type MockConnectedAccountServerMockRecorder struct {
	mock *MockConnectedAccountServer
}

// NewMockConnectedAccountServer creates a new mock instance.
func NewMockConnectedAccountServer(ctrl *gomock.Controller) *MockConnectedAccountServer {
	mock := &MockConnectedAccountServer{ctrl: ctrl}
	mock.recorder = &MockConnectedAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConnectedAccountServer) EXPECT() *MockConnectedAccountServerMockRecorder {
	return m.recorder
}

// FetchAccounts mocks base method.
func (m *MockConnectedAccountServer) FetchAccounts(arg0 context.Context, arg1 *connected_account.FetchAccountsRequest) (*connected_account.FetchAccountsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAccounts", arg0, arg1)
	ret0, _ := ret[0].(*connected_account.FetchAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAccounts indicates an expected call of FetchAccounts.
func (mr *MockConnectedAccountServerMockRecorder) FetchAccounts(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAccounts", reflect.TypeOf((*MockConnectedAccountServer)(nil).FetchAccounts), arg0, arg1)
}

// InitiateAccountLinking mocks base method.
func (m *MockConnectedAccountServer) InitiateAccountLinking(arg0 context.Context, arg1 *connected_account.InitiateAccountLinkingRequest) (*connected_account.InitiateAccountLinkingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateAccountLinking", arg0, arg1)
	ret0, _ := ret[0].(*connected_account.InitiateAccountLinkingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateAccountLinking indicates an expected call of InitiateAccountLinking.
func (mr *MockConnectedAccountServerMockRecorder) InitiateAccountLinking(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateAccountLinking", reflect.TypeOf((*MockConnectedAccountServer)(nil).InitiateAccountLinking), arg0, arg1)
}

// InitiateConsent mocks base method.
func (m *MockConnectedAccountServer) InitiateConsent(arg0 context.Context, arg1 *connected_account.InitiateConsentRequest) (*connected_account.InitiateConsentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateConsent", arg0, arg1)
	ret0, _ := ret[0].(*connected_account.InitiateConsentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateConsent indicates an expected call of InitiateConsent.
func (mr *MockConnectedAccountServerMockRecorder) InitiateConsent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateConsent", reflect.TypeOf((*MockConnectedAccountServer)(nil).InitiateConsent), arg0, arg1)
}

// SyncAccount mocks base method.
func (m *MockConnectedAccountServer) SyncAccount(arg0 context.Context, arg1 *connected_account.SyncAccountRequest) (*connected_account.SyncAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncAccount", arg0, arg1)
	ret0, _ := ret[0].(*connected_account.SyncAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncAccount indicates an expected call of SyncAccount.
func (mr *MockConnectedAccountServerMockRecorder) SyncAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncAccount", reflect.TypeOf((*MockConnectedAccountServer)(nil).SyncAccount), arg0, arg1)
}

// VerifyAccountLinkingOTP mocks base method.
func (m *MockConnectedAccountServer) VerifyAccountLinkingOTP(arg0 context.Context, arg1 *connected_account.VerifyAccountLinkingOTPRequest) (*connected_account.VerifyAccountLinkingOTPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyAccountLinkingOTP", arg0, arg1)
	ret0, _ := ret[0].(*connected_account.VerifyAccountLinkingOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAccountLinkingOTP indicates an expected call of VerifyAccountLinkingOTP.
func (mr *MockConnectedAccountServerMockRecorder) VerifyAccountLinkingOTP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAccountLinkingOTP", reflect.TypeOf((*MockConnectedAccountServer)(nil).VerifyAccountLinkingOTP), arg0, arg1)
}

// MockUnsafeConnectedAccountServer is a mock of UnsafeConnectedAccountServer interface.
type MockUnsafeConnectedAccountServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConnectedAccountServerMockRecorder
}

// MockUnsafeConnectedAccountServerMockRecorder is the mock recorder for MockUnsafeConnectedAccountServer.
type MockUnsafeConnectedAccountServerMockRecorder struct {
	mock *MockUnsafeConnectedAccountServer
}

// NewMockUnsafeConnectedAccountServer creates a new mock instance.
func NewMockUnsafeConnectedAccountServer(ctrl *gomock.Controller) *MockUnsafeConnectedAccountServer {
	mock := &MockUnsafeConnectedAccountServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConnectedAccountServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConnectedAccountServer) EXPECT() *MockUnsafeConnectedAccountServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConnectedAccountServer mocks base method.
func (m *MockUnsafeConnectedAccountServer) mustEmbedUnimplementedConnectedAccountServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConnectedAccountServer")
}

// mustEmbedUnimplementedConnectedAccountServer indicates an expected call of mustEmbedUnimplementedConnectedAccountServer.
func (mr *MockUnsafeConnectedAccountServerMockRecorder) mustEmbedUnimplementedConnectedAccountServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConnectedAccountServer", reflect.TypeOf((*MockUnsafeConnectedAccountServer)(nil).mustEmbedUnimplementedConnectedAccountServer))
}
