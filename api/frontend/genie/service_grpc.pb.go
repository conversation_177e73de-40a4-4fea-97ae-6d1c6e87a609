// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/genie/service.proto

package genie

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Genie_GenerateOTP_FullMethodName        = "/frontend.genie.Genie/GenerateOTP"
	Genie_VerifyOTP_FullMethodName          = "/frontend.genie.Genie/VerifyOTP"
	Genie_VerifyMPIN_FullMethodName         = "/frontend.genie.Genie/VerifyMPIN"
	Genie_ResetMPIN_FullMethodName          = "/frontend.genie.Genie/ResetMPIN"
	Genie_VerifyHandshakeOTP_FullMethodName = "/frontend.genie.Genie/VerifyHandshakeOTP"
	Genie_InitiateHandshake_FullMethodName  = "/frontend.genie.Genie/InitiateHandshake"
	Genie_GetUserDetails_FullMethodName     = "/frontend.genie.Genie/GetUserDetails"
	Genie_InitiateBKYC_FullMethodName       = "/frontend.genie.Genie/InitiateBKYC"
	Genie_UploadBKYCRecord_FullMethodName   = "/frontend.genie.Genie/UploadBKYCRecord"
	Genie_GetAgentProfile_FullMethodName    = "/frontend.genie.Genie/GetAgentProfile"
	Genie_SignOutAgent_FullMethodName       = "/frontend.genie.Genie/SignOutAgent"
	Genie_CaptureSignature_FullMethodName   = "/frontend.genie.Genie/CaptureSignature"
)

// GenieClient is the client API for Genie service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GenieClient interface {
	// GenerateOTP takes the agent id as request and sends an Email OTP to agent's registered email
	GenerateOTP(ctx context.Context, in *GenerateOTPRequest, opts ...grpc.CallOption) (*GenerateOTPResponse, error)
	// VerifyOTP takes otp string as request and verifies the agent entered OTP and returns a refresh token
	VerifyOTP(ctx context.Context, in *VerifyOTPRequest, opts ...grpc.CallOption) (*VerifyOTPResponse, error)
	// VerifyMPIN takes MPIN w/ refresh token and returns access token after verifying MPIN
	// If reset MPIN is required, it prescribes the client to go to reset credentials page
	VerifyMPIN(ctx context.Context, in *VerifyMPINRequest, opts ...grpc.CallOption) (*VerifyMPINResponse, error)
	// ResetMPIN takes old MPIN + New MPIN w/ refresh token and returns access token after updating MPIN
	ResetMPIN(ctx context.Context, in *ResetMPINRequest, opts ...grpc.CallOption) (*ResetMPINResponse, error)
	// VerifyHandshakeOTP is called by the client to verify the handshake OTP.
	// Current use-case: 1. Biometric KYC.
	VerifyHandshakeOTP(ctx context.Context, in *VerifyHandshakeOTPRequest, opts ...grpc.CallOption) (*VerifyHandshakeOTPResponse, error)
	// InitiateHandshake starts handshake process for a particular flow.
	InitiateHandshake(ctx context.Context, in *InitiateHandshakeRequest, opts ...grpc.CallOption) (*InitiateHandshakeResponse, error)
	// GetUserDetails is called by the agent to fetch basic info about the client
	GetUserDetails(ctx context.Context, in *GetUserDetailsRequest, opts ...grpc.CallOption) (*GetUserDetailsResponse, error)
	// Before starting BKYC process using SDK, client should call this method to
	// inform server. It return secure token to client also.
	// This method validates if BKYC should be done and
	// stores the BKYC state on backend.
	// RPC Status OK in response represents a go ahead for BKYC.
	InitiateBKYC(ctx context.Context, in *InitiateBKYCRequest, opts ...grpc.CallOption) (*InitiateBKYCResponse, error)
	// UploadBKYCRecord uploads user KYC data after success of BKYC. This is encrypted
	// and is expected to be the same payload received from vendor by the client.
	// Failure returned in case of failure to decrypt the request body or
	// the body doesn't match the expected schema. This data get's purged after expire duration
	UploadBKYCRecord(ctx context.Context, in *UploadBKYCRecordRequest, opts ...grpc.CallOption) (*UploadBKYCRecordResponse, error)
	GetAgentProfile(ctx context.Context, in *GetAgentProfileRequest, opts ...grpc.CallOption) (*GetAgentProfileResponse, error)
	SignOutAgent(ctx context.Context, in *SignOutAgentRequest, opts ...grpc.CallOption) (*SignOutAgentResponse, error)
	// Deprecated: Do not use.
	// User signature is captured after BKYC is completed
	// Agent app calls this RPC to upload the signature
	CaptureSignature(ctx context.Context, in *CaptureSignatureRequest, opts ...grpc.CallOption) (*CaptureSignatureResponse, error)
}

type genieClient struct {
	cc grpc.ClientConnInterface
}

func NewGenieClient(cc grpc.ClientConnInterface) GenieClient {
	return &genieClient{cc}
}

func (c *genieClient) GenerateOTP(ctx context.Context, in *GenerateOTPRequest, opts ...grpc.CallOption) (*GenerateOTPResponse, error) {
	out := new(GenerateOTPResponse)
	err := c.cc.Invoke(ctx, Genie_GenerateOTP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) VerifyOTP(ctx context.Context, in *VerifyOTPRequest, opts ...grpc.CallOption) (*VerifyOTPResponse, error) {
	out := new(VerifyOTPResponse)
	err := c.cc.Invoke(ctx, Genie_VerifyOTP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) VerifyMPIN(ctx context.Context, in *VerifyMPINRequest, opts ...grpc.CallOption) (*VerifyMPINResponse, error) {
	out := new(VerifyMPINResponse)
	err := c.cc.Invoke(ctx, Genie_VerifyMPIN_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) ResetMPIN(ctx context.Context, in *ResetMPINRequest, opts ...grpc.CallOption) (*ResetMPINResponse, error) {
	out := new(ResetMPINResponse)
	err := c.cc.Invoke(ctx, Genie_ResetMPIN_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) VerifyHandshakeOTP(ctx context.Context, in *VerifyHandshakeOTPRequest, opts ...grpc.CallOption) (*VerifyHandshakeOTPResponse, error) {
	out := new(VerifyHandshakeOTPResponse)
	err := c.cc.Invoke(ctx, Genie_VerifyHandshakeOTP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) InitiateHandshake(ctx context.Context, in *InitiateHandshakeRequest, opts ...grpc.CallOption) (*InitiateHandshakeResponse, error) {
	out := new(InitiateHandshakeResponse)
	err := c.cc.Invoke(ctx, Genie_InitiateHandshake_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) GetUserDetails(ctx context.Context, in *GetUserDetailsRequest, opts ...grpc.CallOption) (*GetUserDetailsResponse, error) {
	out := new(GetUserDetailsResponse)
	err := c.cc.Invoke(ctx, Genie_GetUserDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) InitiateBKYC(ctx context.Context, in *InitiateBKYCRequest, opts ...grpc.CallOption) (*InitiateBKYCResponse, error) {
	out := new(InitiateBKYCResponse)
	err := c.cc.Invoke(ctx, Genie_InitiateBKYC_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) UploadBKYCRecord(ctx context.Context, in *UploadBKYCRecordRequest, opts ...grpc.CallOption) (*UploadBKYCRecordResponse, error) {
	out := new(UploadBKYCRecordResponse)
	err := c.cc.Invoke(ctx, Genie_UploadBKYCRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) GetAgentProfile(ctx context.Context, in *GetAgentProfileRequest, opts ...grpc.CallOption) (*GetAgentProfileResponse, error) {
	out := new(GetAgentProfileResponse)
	err := c.cc.Invoke(ctx, Genie_GetAgentProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *genieClient) SignOutAgent(ctx context.Context, in *SignOutAgentRequest, opts ...grpc.CallOption) (*SignOutAgentResponse, error) {
	out := new(SignOutAgentResponse)
	err := c.cc.Invoke(ctx, Genie_SignOutAgent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *genieClient) CaptureSignature(ctx context.Context, in *CaptureSignatureRequest, opts ...grpc.CallOption) (*CaptureSignatureResponse, error) {
	out := new(CaptureSignatureResponse)
	err := c.cc.Invoke(ctx, Genie_CaptureSignature_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GenieServer is the server API for Genie service.
// All implementations should embed UnimplementedGenieServer
// for forward compatibility
type GenieServer interface {
	// GenerateOTP takes the agent id as request and sends an Email OTP to agent's registered email
	GenerateOTP(context.Context, *GenerateOTPRequest) (*GenerateOTPResponse, error)
	// VerifyOTP takes otp string as request and verifies the agent entered OTP and returns a refresh token
	VerifyOTP(context.Context, *VerifyOTPRequest) (*VerifyOTPResponse, error)
	// VerifyMPIN takes MPIN w/ refresh token and returns access token after verifying MPIN
	// If reset MPIN is required, it prescribes the client to go to reset credentials page
	VerifyMPIN(context.Context, *VerifyMPINRequest) (*VerifyMPINResponse, error)
	// ResetMPIN takes old MPIN + New MPIN w/ refresh token and returns access token after updating MPIN
	ResetMPIN(context.Context, *ResetMPINRequest) (*ResetMPINResponse, error)
	// VerifyHandshakeOTP is called by the client to verify the handshake OTP.
	// Current use-case: 1. Biometric KYC.
	VerifyHandshakeOTP(context.Context, *VerifyHandshakeOTPRequest) (*VerifyHandshakeOTPResponse, error)
	// InitiateHandshake starts handshake process for a particular flow.
	InitiateHandshake(context.Context, *InitiateHandshakeRequest) (*InitiateHandshakeResponse, error)
	// GetUserDetails is called by the agent to fetch basic info about the client
	GetUserDetails(context.Context, *GetUserDetailsRequest) (*GetUserDetailsResponse, error)
	// Before starting BKYC process using SDK, client should call this method to
	// inform server. It return secure token to client also.
	// This method validates if BKYC should be done and
	// stores the BKYC state on backend.
	// RPC Status OK in response represents a go ahead for BKYC.
	InitiateBKYC(context.Context, *InitiateBKYCRequest) (*InitiateBKYCResponse, error)
	// UploadBKYCRecord uploads user KYC data after success of BKYC. This is encrypted
	// and is expected to be the same payload received from vendor by the client.
	// Failure returned in case of failure to decrypt the request body or
	// the body doesn't match the expected schema. This data get's purged after expire duration
	UploadBKYCRecord(context.Context, *UploadBKYCRecordRequest) (*UploadBKYCRecordResponse, error)
	GetAgentProfile(context.Context, *GetAgentProfileRequest) (*GetAgentProfileResponse, error)
	SignOutAgent(context.Context, *SignOutAgentRequest) (*SignOutAgentResponse, error)
	// Deprecated: Do not use.
	// User signature is captured after BKYC is completed
	// Agent app calls this RPC to upload the signature
	CaptureSignature(context.Context, *CaptureSignatureRequest) (*CaptureSignatureResponse, error)
}

// UnimplementedGenieServer should be embedded to have forward compatible implementations.
type UnimplementedGenieServer struct {
}

func (UnimplementedGenieServer) GenerateOTP(context.Context, *GenerateOTPRequest) (*GenerateOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateOTP not implemented")
}
func (UnimplementedGenieServer) VerifyOTP(context.Context, *VerifyOTPRequest) (*VerifyOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyOTP not implemented")
}
func (UnimplementedGenieServer) VerifyMPIN(context.Context, *VerifyMPINRequest) (*VerifyMPINResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyMPIN not implemented")
}
func (UnimplementedGenieServer) ResetMPIN(context.Context, *ResetMPINRequest) (*ResetMPINResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetMPIN not implemented")
}
func (UnimplementedGenieServer) VerifyHandshakeOTP(context.Context, *VerifyHandshakeOTPRequest) (*VerifyHandshakeOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyHandshakeOTP not implemented")
}
func (UnimplementedGenieServer) InitiateHandshake(context.Context, *InitiateHandshakeRequest) (*InitiateHandshakeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateHandshake not implemented")
}
func (UnimplementedGenieServer) GetUserDetails(context.Context, *GetUserDetailsRequest) (*GetUserDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDetails not implemented")
}
func (UnimplementedGenieServer) InitiateBKYC(context.Context, *InitiateBKYCRequest) (*InitiateBKYCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateBKYC not implemented")
}
func (UnimplementedGenieServer) UploadBKYCRecord(context.Context, *UploadBKYCRecordRequest) (*UploadBKYCRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadBKYCRecord not implemented")
}
func (UnimplementedGenieServer) GetAgentProfile(context.Context, *GetAgentProfileRequest) (*GetAgentProfileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgentProfile not implemented")
}
func (UnimplementedGenieServer) SignOutAgent(context.Context, *SignOutAgentRequest) (*SignOutAgentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignOutAgent not implemented")
}
func (UnimplementedGenieServer) CaptureSignature(context.Context, *CaptureSignatureRequest) (*CaptureSignatureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CaptureSignature not implemented")
}

// UnsafeGenieServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GenieServer will
// result in compilation errors.
type UnsafeGenieServer interface {
	mustEmbedUnimplementedGenieServer()
}

func RegisterGenieServer(s grpc.ServiceRegistrar, srv GenieServer) {
	s.RegisterService(&Genie_ServiceDesc, srv)
}

func _Genie_GenerateOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).GenerateOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_GenerateOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).GenerateOTP(ctx, req.(*GenerateOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_VerifyOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).VerifyOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_VerifyOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).VerifyOTP(ctx, req.(*VerifyOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_VerifyMPIN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyMPINRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).VerifyMPIN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_VerifyMPIN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).VerifyMPIN(ctx, req.(*VerifyMPINRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_ResetMPIN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetMPINRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).ResetMPIN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_ResetMPIN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).ResetMPIN(ctx, req.(*ResetMPINRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_VerifyHandshakeOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyHandshakeOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).VerifyHandshakeOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_VerifyHandshakeOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).VerifyHandshakeOTP(ctx, req.(*VerifyHandshakeOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_InitiateHandshake_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateHandshakeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).InitiateHandshake(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_InitiateHandshake_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).InitiateHandshake(ctx, req.(*InitiateHandshakeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_GetUserDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).GetUserDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_GetUserDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).GetUserDetails(ctx, req.(*GetUserDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_InitiateBKYC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateBKYCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).InitiateBKYC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_InitiateBKYC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).InitiateBKYC(ctx, req.(*InitiateBKYCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_UploadBKYCRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadBKYCRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).UploadBKYCRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_UploadBKYCRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).UploadBKYCRecord(ctx, req.(*UploadBKYCRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_GetAgentProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).GetAgentProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_GetAgentProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).GetAgentProfile(ctx, req.(*GetAgentProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_SignOutAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignOutAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).SignOutAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_SignOutAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).SignOutAgent(ctx, req.(*SignOutAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Genie_CaptureSignature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CaptureSignatureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GenieServer).CaptureSignature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Genie_CaptureSignature_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GenieServer).CaptureSignature(ctx, req.(*CaptureSignatureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Genie_ServiceDesc is the grpc.ServiceDesc for Genie service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Genie_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.genie.Genie",
	HandlerType: (*GenieServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateOTP",
			Handler:    _Genie_GenerateOTP_Handler,
		},
		{
			MethodName: "VerifyOTP",
			Handler:    _Genie_VerifyOTP_Handler,
		},
		{
			MethodName: "VerifyMPIN",
			Handler:    _Genie_VerifyMPIN_Handler,
		},
		{
			MethodName: "ResetMPIN",
			Handler:    _Genie_ResetMPIN_Handler,
		},
		{
			MethodName: "VerifyHandshakeOTP",
			Handler:    _Genie_VerifyHandshakeOTP_Handler,
		},
		{
			MethodName: "InitiateHandshake",
			Handler:    _Genie_InitiateHandshake_Handler,
		},
		{
			MethodName: "GetUserDetails",
			Handler:    _Genie_GetUserDetails_Handler,
		},
		{
			MethodName: "InitiateBKYC",
			Handler:    _Genie_InitiateBKYC_Handler,
		},
		{
			MethodName: "UploadBKYCRecord",
			Handler:    _Genie_UploadBKYCRecord_Handler,
		},
		{
			MethodName: "GetAgentProfile",
			Handler:    _Genie_GetAgentProfile_Handler,
		},
		{
			MethodName: "SignOutAgent",
			Handler:    _Genie_SignOutAgent_Handler,
		},
		{
			MethodName: "CaptureSignature",
			Handler:    _Genie_CaptureSignature_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/genie/service.proto",
}
