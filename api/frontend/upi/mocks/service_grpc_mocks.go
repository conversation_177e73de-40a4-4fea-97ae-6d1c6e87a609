// Code generated by MockGen. DO NOT EDIT.
// Source: api/./frontend/upi/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	upi "github.com/epifi/gamma/api/frontend/upi"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockUpiClient is a mock of UpiClient interface.
type MockUpiClient struct {
	ctrl     *gomock.Controller
	recorder *MockUpiClientMockRecorder
}

// MockUpiClientMockRecorder is the mock recorder for MockUpiClient.
type MockUpiClientMockRecorder struct {
	mock *MockUpiClient
}

// NewMockUpiClient creates a new mock instance.
func NewMockUpiClient(ctrl *gomock.Controller) *MockUpiClient {
	mock := &MockUpiClient{ctrl: ctrl}
	mock.recorder = &MockUpiClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUpiClient) EXPECT() *MockUpiClientMockRecorder {
	return m.recorder
}

// GetNPCICLParametersV1 mocks base method.
func (m *MockUpiClient) GetNPCICLParametersV1(ctx context.Context, in *upi.GetNPCICLParametersV1Request, opts ...grpc.CallOption) (*upi.GetNPCICLParametersV1Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNPCICLParametersV1", varargs...)
	ret0, _ := ret[0].(*upi.GetNPCICLParametersV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNPCICLParametersV1 indicates an expected call of GetNPCICLParametersV1.
func (mr *MockUpiClientMockRecorder) GetNPCICLParametersV1(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNPCICLParametersV1", reflect.TypeOf((*MockUpiClient)(nil).GetNPCICLParametersV1), varargs...)
}

// MockUpiServer is a mock of UpiServer interface.
type MockUpiServer struct {
	ctrl     *gomock.Controller
	recorder *MockUpiServerMockRecorder
}

// MockUpiServerMockRecorder is the mock recorder for MockUpiServer.
type MockUpiServerMockRecorder struct {
	mock *MockUpiServer
}

// NewMockUpiServer creates a new mock instance.
func NewMockUpiServer(ctrl *gomock.Controller) *MockUpiServer {
	mock := &MockUpiServer{ctrl: ctrl}
	mock.recorder = &MockUpiServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUpiServer) EXPECT() *MockUpiServerMockRecorder {
	return m.recorder
}

// GetNPCICLParametersV1 mocks base method.
func (m *MockUpiServer) GetNPCICLParametersV1(arg0 context.Context, arg1 *upi.GetNPCICLParametersV1Request) (*upi.GetNPCICLParametersV1Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNPCICLParametersV1", arg0, arg1)
	ret0, _ := ret[0].(*upi.GetNPCICLParametersV1Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNPCICLParametersV1 indicates an expected call of GetNPCICLParametersV1.
func (mr *MockUpiServerMockRecorder) GetNPCICLParametersV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNPCICLParametersV1", reflect.TypeOf((*MockUpiServer)(nil).GetNPCICLParametersV1), arg0, arg1)
}

// MockUnsafeUpiServer is a mock of UnsafeUpiServer interface.
type MockUnsafeUpiServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeUpiServerMockRecorder
}

// MockUnsafeUpiServerMockRecorder is the mock recorder for MockUnsafeUpiServer.
type MockUnsafeUpiServerMockRecorder struct {
	mock *MockUnsafeUpiServer
}

// NewMockUnsafeUpiServer creates a new mock instance.
func NewMockUnsafeUpiServer(ctrl *gomock.Controller) *MockUnsafeUpiServer {
	mock := &MockUnsafeUpiServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeUpiServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeUpiServer) EXPECT() *MockUnsafeUpiServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedUpiServer mocks base method.
func (m *MockUnsafeUpiServer) mustEmbedUnimplementedUpiServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedUpiServer")
}

// mustEmbedUnimplementedUpiServer indicates an expected call of mustEmbedUnimplementedUpiServer.
func (mr *MockUnsafeUpiServerMockRecorder) mustEmbedUnimplementedUpiServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedUpiServer", reflect.TypeOf((*MockUnsafeUpiServer)(nil).mustEmbedUnimplementedUpiServer))
}
