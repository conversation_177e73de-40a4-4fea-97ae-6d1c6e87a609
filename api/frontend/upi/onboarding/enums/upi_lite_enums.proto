syntax = "proto3";

package frontend.upi.onboarding.enums;

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding/enums";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding.enums";

// UpiLiteAccountStatus - status of upi lite account
enum UpiLiteAccountStatus {
  UPI_LITE_ACCOUNT_STATUS_UNSPECIFIED = 0;
  UPI_LITE_ACCOUNT_STATUS_ACTIVE = 1;
  UPI_LITE_ACCOUNT_STATUS_INACTIVE = 2;
}

// TopUpAmountOptionType - represents the type of option
enum TopUpAmountOptionType {
  OPTION_TYPE_UNSPECIFIED = 0;
  // option of type ADD can be used any number of times.
  // i.e. clicking on this option always adds the
  // corresponding value to currently entered amount
  OPTION_TYPE_ADD = 1;
  // option of type SET basically sets entered amount to
  // the corresponding value irrespective of the earlier
  // operations applied on it.
  OPTION_TYPE_SET = 2;
}
