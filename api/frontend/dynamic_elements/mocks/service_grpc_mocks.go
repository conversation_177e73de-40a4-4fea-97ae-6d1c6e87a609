// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/dynamic_elements/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	dynamic_elements "github.com/epifi/gamma/api/frontend/dynamic_elements"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDynamicElementsClient is a mock of DynamicElementsClient interface.
type MockDynamicElementsClient struct {
	ctrl     *gomock.Controller
	recorder *MockDynamicElementsClientMockRecorder
}

// MockDynamicElementsClientMockRecorder is the mock recorder for MockDynamicElementsClient.
type MockDynamicElementsClientMockRecorder struct {
	mock *MockDynamicElementsClient
}

// NewMockDynamicElementsClient creates a new mock instance.
func NewMockDynamicElementsClient(ctrl *gomock.Controller) *MockDynamicElementsClient {
	mock := &MockDynamicElementsClient{ctrl: ctrl}
	mock.recorder = &MockDynamicElementsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDynamicElementsClient) EXPECT() *MockDynamicElementsClientMockRecorder {
	return m.recorder
}

// DynamicElementCallback mocks base method.
func (m *MockDynamicElementsClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DynamicElementCallback", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockDynamicElementsClientMockRecorder) DynamicElementCallback(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockDynamicElementsClient)(nil).DynamicElementCallback), varargs...)
}

// FetchDynamicElements mocks base method.
func (m *MockDynamicElementsClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchDynamicElements", varargs...)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockDynamicElementsClientMockRecorder) FetchDynamicElements(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockDynamicElementsClient)(nil).FetchDynamicElements), varargs...)
}

// MockDynamicElementsServer is a mock of DynamicElementsServer interface.
type MockDynamicElementsServer struct {
	ctrl     *gomock.Controller
	recorder *MockDynamicElementsServerMockRecorder
}

// MockDynamicElementsServerMockRecorder is the mock recorder for MockDynamicElementsServer.
type MockDynamicElementsServerMockRecorder struct {
	mock *MockDynamicElementsServer
}

// NewMockDynamicElementsServer creates a new mock instance.
func NewMockDynamicElementsServer(ctrl *gomock.Controller) *MockDynamicElementsServer {
	mock := &MockDynamicElementsServer{ctrl: ctrl}
	mock.recorder = &MockDynamicElementsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDynamicElementsServer) EXPECT() *MockDynamicElementsServerMockRecorder {
	return m.recorder
}

// DynamicElementCallback mocks base method.
func (m *MockDynamicElementsServer) DynamicElementCallback(arg0 context.Context, arg1 *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DynamicElementCallback", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.DynamicElementCallbackResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DynamicElementCallback indicates an expected call of DynamicElementCallback.
func (mr *MockDynamicElementsServerMockRecorder) DynamicElementCallback(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DynamicElementCallback", reflect.TypeOf((*MockDynamicElementsServer)(nil).DynamicElementCallback), arg0, arg1)
}

// FetchDynamicElements mocks base method.
func (m *MockDynamicElementsServer) FetchDynamicElements(arg0 context.Context, arg1 *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchDynamicElements", arg0, arg1)
	ret0, _ := ret[0].(*dynamic_elements.FetchDynamicElementsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchDynamicElements indicates an expected call of FetchDynamicElements.
func (mr *MockDynamicElementsServerMockRecorder) FetchDynamicElements(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchDynamicElements", reflect.TypeOf((*MockDynamicElementsServer)(nil).FetchDynamicElements), arg0, arg1)
}

// MockUnsafeDynamicElementsServer is a mock of UnsafeDynamicElementsServer interface.
type MockUnsafeDynamicElementsServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDynamicElementsServerMockRecorder
}

// MockUnsafeDynamicElementsServerMockRecorder is the mock recorder for MockUnsafeDynamicElementsServer.
type MockUnsafeDynamicElementsServerMockRecorder struct {
	mock *MockUnsafeDynamicElementsServer
}

// NewMockUnsafeDynamicElementsServer creates a new mock instance.
func NewMockUnsafeDynamicElementsServer(ctrl *gomock.Controller) *MockUnsafeDynamicElementsServer {
	mock := &MockUnsafeDynamicElementsServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDynamicElementsServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDynamicElementsServer) EXPECT() *MockUnsafeDynamicElementsServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDynamicElementsServer mocks base method.
func (m *MockUnsafeDynamicElementsServer) mustEmbedUnimplementedDynamicElementsServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDynamicElementsServer")
}

// mustEmbedUnimplementedDynamicElementsServer indicates an expected call of mustEmbedUnimplementedDynamicElementsServer.
func (mr *MockUnsafeDynamicElementsServerMockRecorder) mustEmbedUnimplementedDynamicElementsServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDynamicElementsServer", reflect.TypeOf((*MockUnsafeDynamicElementsServer)(nil).mustEmbedUnimplementedDynamicElementsServer))
}
