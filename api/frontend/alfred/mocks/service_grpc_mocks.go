// Code generated by MockGen. DO NOT EDIT.
// Source: api/./frontend/alfred/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	alfred "github.com/epifi/gamma/api/frontend/alfred"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAlfredClient is a mock of AlfredClient interface.
type MockAlfredClient struct {
	ctrl     *gomock.Controller
	recorder *MockAlfredClientMockRecorder
}

// MockAlfredClientMockRecorder is the mock recorder for MockAlfredClient.
type MockAlfredClientMockRecorder struct {
	mock *MockAlfredClient
}

// NewMockAlfredClient creates a new mock instance.
func NewMockAlfredClient(ctrl *gomock.Controller) *MockAlfredClient {
	mock := &MockAlfredClient{ctrl: ctrl}
	mock.recorder = &MockAlfredClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlfredClient) EXPECT() *MockAlfredClientMockRecorder {
	return m.recorder
}

// GetFilteredRequestSummaries mocks base method.
func (m *MockAlfredClient) GetFilteredRequestSummaries(ctx context.Context, in *alfred.GetFilteredRequestSummariesRequest, opts ...grpc.CallOption) (*alfred.GetFilteredRequestSummariesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFilteredRequestSummaries", varargs...)
	ret0, _ := ret[0].(*alfred.GetFilteredRequestSummariesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilteredRequestSummaries indicates an expected call of GetFilteredRequestSummaries.
func (mr *MockAlfredClientMockRecorder) GetFilteredRequestSummaries(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilteredRequestSummaries", reflect.TypeOf((*MockAlfredClient)(nil).GetFilteredRequestSummaries), varargs...)
}

// GetRequestChoices mocks base method.
func (m *MockAlfredClient) GetRequestChoices(ctx context.Context, in *alfred.GetRequestChoicesRequest, opts ...grpc.CallOption) (*alfred.GetRequestChoicesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRequestChoices", varargs...)
	ret0, _ := ret[0].(*alfred.GetRequestChoicesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestChoices indicates an expected call of GetRequestChoices.
func (mr *MockAlfredClientMockRecorder) GetRequestChoices(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestChoices", reflect.TypeOf((*MockAlfredClient)(nil).GetRequestChoices), varargs...)
}

// PollRequestStatus mocks base method.
func (m *MockAlfredClient) PollRequestStatus(ctx context.Context, in *alfred.PollRequestStatusRequest, opts ...grpc.CallOption) (*alfred.PollRequestStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PollRequestStatus", varargs...)
	ret0, _ := ret[0].(*alfred.PollRequestStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PollRequestStatus indicates an expected call of PollRequestStatus.
func (mr *MockAlfredClientMockRecorder) PollRequestStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PollRequestStatus", reflect.TypeOf((*MockAlfredClient)(nil).PollRequestStatus), varargs...)
}

// ProvisionNewRequest mocks base method.
func (m *MockAlfredClient) ProvisionNewRequest(ctx context.Context, in *alfred.ProvisionNewReqRequest, opts ...grpc.CallOption) (*alfred.ProvisionNewReqResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProvisionNewRequest", varargs...)
	ret0, _ := ret[0].(*alfred.ProvisionNewReqResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProvisionNewRequest indicates an expected call of ProvisionNewRequest.
func (mr *MockAlfredClientMockRecorder) ProvisionNewRequest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvisionNewRequest", reflect.TypeOf((*MockAlfredClient)(nil).ProvisionNewRequest), varargs...)
}

// MockAlfredServer is a mock of AlfredServer interface.
type MockAlfredServer struct {
	ctrl     *gomock.Controller
	recorder *MockAlfredServerMockRecorder
}

// MockAlfredServerMockRecorder is the mock recorder for MockAlfredServer.
type MockAlfredServerMockRecorder struct {
	mock *MockAlfredServer
}

// NewMockAlfredServer creates a new mock instance.
func NewMockAlfredServer(ctrl *gomock.Controller) *MockAlfredServer {
	mock := &MockAlfredServer{ctrl: ctrl}
	mock.recorder = &MockAlfredServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlfredServer) EXPECT() *MockAlfredServerMockRecorder {
	return m.recorder
}

// GetFilteredRequestSummaries mocks base method.
func (m *MockAlfredServer) GetFilteredRequestSummaries(arg0 context.Context, arg1 *alfred.GetFilteredRequestSummariesRequest) (*alfred.GetFilteredRequestSummariesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFilteredRequestSummaries", arg0, arg1)
	ret0, _ := ret[0].(*alfred.GetFilteredRequestSummariesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFilteredRequestSummaries indicates an expected call of GetFilteredRequestSummaries.
func (mr *MockAlfredServerMockRecorder) GetFilteredRequestSummaries(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFilteredRequestSummaries", reflect.TypeOf((*MockAlfredServer)(nil).GetFilteredRequestSummaries), arg0, arg1)
}

// GetRequestChoices mocks base method.
func (m *MockAlfredServer) GetRequestChoices(arg0 context.Context, arg1 *alfred.GetRequestChoicesRequest) (*alfred.GetRequestChoicesResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRequestChoices", arg0, arg1)
	ret0, _ := ret[0].(*alfred.GetRequestChoicesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestChoices indicates an expected call of GetRequestChoices.
func (mr *MockAlfredServerMockRecorder) GetRequestChoices(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestChoices", reflect.TypeOf((*MockAlfredServer)(nil).GetRequestChoices), arg0, arg1)
}

// PollRequestStatus mocks base method.
func (m *MockAlfredServer) PollRequestStatus(arg0 context.Context, arg1 *alfred.PollRequestStatusRequest) (*alfred.PollRequestStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PollRequestStatus", arg0, arg1)
	ret0, _ := ret[0].(*alfred.PollRequestStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PollRequestStatus indicates an expected call of PollRequestStatus.
func (mr *MockAlfredServerMockRecorder) PollRequestStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PollRequestStatus", reflect.TypeOf((*MockAlfredServer)(nil).PollRequestStatus), arg0, arg1)
}

// ProvisionNewRequest mocks base method.
func (m *MockAlfredServer) ProvisionNewRequest(arg0 context.Context, arg1 *alfred.ProvisionNewReqRequest) (*alfred.ProvisionNewReqResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProvisionNewRequest", arg0, arg1)
	ret0, _ := ret[0].(*alfred.ProvisionNewReqResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProvisionNewRequest indicates an expected call of ProvisionNewRequest.
func (mr *MockAlfredServerMockRecorder) ProvisionNewRequest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvisionNewRequest", reflect.TypeOf((*MockAlfredServer)(nil).ProvisionNewRequest), arg0, arg1)
}

// MockUnsafeAlfredServer is a mock of UnsafeAlfredServer interface.
type MockUnsafeAlfredServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAlfredServerMockRecorder
}

// MockUnsafeAlfredServerMockRecorder is the mock recorder for MockUnsafeAlfredServer.
type MockUnsafeAlfredServerMockRecorder struct {
	mock *MockUnsafeAlfredServer
}

// NewMockUnsafeAlfredServer creates a new mock instance.
func NewMockUnsafeAlfredServer(ctrl *gomock.Controller) *MockUnsafeAlfredServer {
	mock := &MockUnsafeAlfredServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAlfredServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAlfredServer) EXPECT() *MockUnsafeAlfredServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAlfredServer mocks base method.
func (m *MockUnsafeAlfredServer) mustEmbedUnimplementedAlfredServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAlfredServer")
}

// mustEmbedUnimplementedAlfredServer indicates an expected call of mustEmbedUnimplementedAlfredServer.
func (mr *MockUnsafeAlfredServerMockRecorder) mustEmbedUnimplementedAlfredServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAlfredServer", reflect.TypeOf((*MockUnsafeAlfredServer)(nil).mustEmbedUnimplementedAlfredServer))
}
