// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/inapphelp/contact_us/service.proto

package contact_us

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ContactUs_GetContactUsLandingScreen_FullMethodName   = "/frontend.inapphelp.contact_us.ContactUs/GetContactUsLandingScreen"
	ContactUs_GetIssuesBasedOnPrompt_FullMethodName      = "/frontend.inapphelp.contact_us.ContactUs/GetIssuesBasedOnPrompt"
	ContactUs_GetContactUsTerminalScreen_FullMethodName  = "/frontend.inapphelp.contact_us.ContactUs/GetContactUsTerminalScreen"
	ContactUs_GetHelpLandingScreen_FullMethodName        = "/frontend.inapphelp.contact_us.ContactUs/GetHelpLandingScreen"
	ContactUs_GetCategorySelectionScreen_FullMethodName  = "/frontend.inapphelp.contact_us.ContactUs/GetCategorySelectionScreen"
	ContactUs_GetNextActionForIssue_FullMethodName       = "/frontend.inapphelp.contact_us.ContactUs/GetNextActionForIssue"
	ContactUs_SelectContactChannel_FullMethodName        = "/frontend.inapphelp.contact_us.ContactUs/SelectContactChannel"
	ContactUs_GetContactUsLandingScreenV2_FullMethodName = "/frontend.inapphelp.contact_us.ContactUs/GetContactUsLandingScreenV2"
	ContactUs_GetOpenIncidentsScreen_FullMethodName      = "/frontend.inapphelp.contact_us.ContactUs/GetOpenIncidentsScreen"
)

// ContactUsClient is the client API for ContactUs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ContactUsClient interface {
	// This rpc is to be called when the user reaches the contact us landing page. This will fetch the issue lists (Trending Issues and Recent Issues) that will be shown to the user in the case the search bar is empty
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6729-10107&t=7CnImuJganWDyCbD-0
	GetContactUsLandingScreen(ctx context.Context, in *GetContactUsLandingScreenRequest, opts ...grpc.CallOption) (*GetContactUsLandingScreenResponse, error)
	// This rpc is to be called when the user edits the text in search bar on the contact us landing screen
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6729-10314&t=wM2LnsgwmDT1mLz9-0
	// TODO(smit) https://monorail.pointz.in/p/fi-app/issues/detail?id=82259
	// The rpc is to be called by the user when the user edits the search query, making sure a gap of 500ms between making the rpc call and the moment the user edits the search query
	GetIssuesBasedOnPrompt(ctx context.Context, in *GetIssuesBasedOnPromptRequest, opts ...grpc.CallOption) (*GetIssuesBasedOnPromptResponse, error)
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10511&t=nt4zIX1M3XYpriMW-0
	// This rpc fetches the terminal screen details of the contact us flow
	GetContactUsTerminalScreen(ctx context.Context, in *GetContactUsTerminalScreenRequest, opts ...grpc.CallOption) (*GetContactUsTerminalScreenResponse, error)
	// GetHelpLandingScreen returns the details to be shown on Help landing screen
	// for now this RPC only supports the navbar ctas
	GetHelpLandingScreen(ctx context.Context, in *GetHelpLandingScreenRequest, opts ...grpc.CallOption) (*GetHelpLandingScreenResponse, error)
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=9qzkLma7WCLdgcSg-0
	// This RPC fetches the category selection screen in contact us flow
	// Request takes UserIssueIdentifier and analytics screen name as request both are mandatory
	// Response status codes:
	// Internal server error: if the server fails to serve the request
	// InvalidArgument: if the request object is invalidly
	// OK: success
	GetCategorySelectionScreen(ctx context.Context, in *GetCategorySelectionScreenRequest, opts ...grpc.CallOption) (*GetCategorySelectionScreenResponse, error)
	// GetNextActionForIssue RPC makes the decision whether to land user to terminal screen, or category selection screen
	// Request takes UserIssueIdentifier, which is a required field
	// Response contains a deeplink to which user should be navigated
	// in-case this RPC fails for unknown reason, the client should navigate user to category selection screen
	GetNextActionForIssue(ctx context.Context, in *GetNextActionForIssueRequest, opts ...grpc.CallOption) (*GetNextActionForIssueResponse, error)
	// This RPC is called when the user selects a contact channel to contact customer care.
	// The objective of this RPC is to pass all the relevant context of user issue to the agent when they reach out.
	// Request takes the selected contact option (mandatory), available contact options, user issue identifier (mandatory) and meta data
	// if this RPC fails, the client should ignore the failure and continue with the flow, this should happen on best-effort basis
	SelectContactChannel(ctx context.Context, in *SelectContactChannelRequest, opts ...grpc.CallOption) (*SelectContactChannelResponse, error)
	// This RPC fetches the content for the new Contact Us Landing Screen (V2).
	// It provides all the necessary sections and elements to render the screen.
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748&t=4SzjTwXgOKZ9sTE9-4
	GetContactUsLandingScreenV2(ctx context.Context, in *GetContactUsLandingScreenV2Request, opts ...grpc.CallOption) (*GetContactUsLandingScreenV2Response, error)
	// This RPC fetches the content for the Open Incidents Screen.
	// It provides a list of active incidents or service disruptions.
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35931&t=4SzjTwXgOKZ9sTE9-4
	GetOpenIncidentsScreen(ctx context.Context, in *GetOpenIncidentsScreenRequest, opts ...grpc.CallOption) (*GetOpenIncidentsScreenResponse, error)
}

type contactUsClient struct {
	cc grpc.ClientConnInterface
}

func NewContactUsClient(cc grpc.ClientConnInterface) ContactUsClient {
	return &contactUsClient{cc}
}

func (c *contactUsClient) GetContactUsLandingScreen(ctx context.Context, in *GetContactUsLandingScreenRequest, opts ...grpc.CallOption) (*GetContactUsLandingScreenResponse, error) {
	out := new(GetContactUsLandingScreenResponse)
	err := c.cc.Invoke(ctx, ContactUs_GetContactUsLandingScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) GetIssuesBasedOnPrompt(ctx context.Context, in *GetIssuesBasedOnPromptRequest, opts ...grpc.CallOption) (*GetIssuesBasedOnPromptResponse, error) {
	out := new(GetIssuesBasedOnPromptResponse)
	err := c.cc.Invoke(ctx, ContactUs_GetIssuesBasedOnPrompt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) GetContactUsTerminalScreen(ctx context.Context, in *GetContactUsTerminalScreenRequest, opts ...grpc.CallOption) (*GetContactUsTerminalScreenResponse, error) {
	out := new(GetContactUsTerminalScreenResponse)
	err := c.cc.Invoke(ctx, ContactUs_GetContactUsTerminalScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) GetHelpLandingScreen(ctx context.Context, in *GetHelpLandingScreenRequest, opts ...grpc.CallOption) (*GetHelpLandingScreenResponse, error) {
	out := new(GetHelpLandingScreenResponse)
	err := c.cc.Invoke(ctx, ContactUs_GetHelpLandingScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) GetCategorySelectionScreen(ctx context.Context, in *GetCategorySelectionScreenRequest, opts ...grpc.CallOption) (*GetCategorySelectionScreenResponse, error) {
	out := new(GetCategorySelectionScreenResponse)
	err := c.cc.Invoke(ctx, ContactUs_GetCategorySelectionScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) GetNextActionForIssue(ctx context.Context, in *GetNextActionForIssueRequest, opts ...grpc.CallOption) (*GetNextActionForIssueResponse, error) {
	out := new(GetNextActionForIssueResponse)
	err := c.cc.Invoke(ctx, ContactUs_GetNextActionForIssue_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) SelectContactChannel(ctx context.Context, in *SelectContactChannelRequest, opts ...grpc.CallOption) (*SelectContactChannelResponse, error) {
	out := new(SelectContactChannelResponse)
	err := c.cc.Invoke(ctx, ContactUs_SelectContactChannel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) GetContactUsLandingScreenV2(ctx context.Context, in *GetContactUsLandingScreenV2Request, opts ...grpc.CallOption) (*GetContactUsLandingScreenV2Response, error) {
	out := new(GetContactUsLandingScreenV2Response)
	err := c.cc.Invoke(ctx, ContactUs_GetContactUsLandingScreenV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contactUsClient) GetOpenIncidentsScreen(ctx context.Context, in *GetOpenIncidentsScreenRequest, opts ...grpc.CallOption) (*GetOpenIncidentsScreenResponse, error) {
	out := new(GetOpenIncidentsScreenResponse)
	err := c.cc.Invoke(ctx, ContactUs_GetOpenIncidentsScreen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ContactUsServer is the server API for ContactUs service.
// All implementations should embed UnimplementedContactUsServer
// for forward compatibility
type ContactUsServer interface {
	// This rpc is to be called when the user reaches the contact us landing page. This will fetch the issue lists (Trending Issues and Recent Issues) that will be shown to the user in the case the search bar is empty
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6729-10107&t=7CnImuJganWDyCbD-0
	GetContactUsLandingScreen(context.Context, *GetContactUsLandingScreenRequest) (*GetContactUsLandingScreenResponse, error)
	// This rpc is to be called when the user edits the text in search bar on the contact us landing screen
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6729-10314&t=wM2LnsgwmDT1mLz9-0
	// TODO(smit) https://monorail.pointz.in/p/fi-app/issues/detail?id=82259
	// The rpc is to be called by the user when the user edits the search query, making sure a gap of 500ms between making the rpc call and the moment the user edits the search query
	GetIssuesBasedOnPrompt(context.Context, *GetIssuesBasedOnPromptRequest) (*GetIssuesBasedOnPromptResponse, error)
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10511&t=nt4zIX1M3XYpriMW-0
	// This rpc fetches the terminal screen details of the contact us flow
	GetContactUsTerminalScreen(context.Context, *GetContactUsTerminalScreenRequest) (*GetContactUsTerminalScreenResponse, error)
	// GetHelpLandingScreen returns the details to be shown on Help landing screen
	// for now this RPC only supports the navbar ctas
	GetHelpLandingScreen(context.Context, *GetHelpLandingScreenRequest) (*GetHelpLandingScreenResponse, error)
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=9qzkLma7WCLdgcSg-0
	// This RPC fetches the category selection screen in contact us flow
	// Request takes UserIssueIdentifier and analytics screen name as request both are mandatory
	// Response status codes:
	// Internal server error: if the server fails to serve the request
	// InvalidArgument: if the request object is invalidly
	// OK: success
	GetCategorySelectionScreen(context.Context, *GetCategorySelectionScreenRequest) (*GetCategorySelectionScreenResponse, error)
	// GetNextActionForIssue RPC makes the decision whether to land user to terminal screen, or category selection screen
	// Request takes UserIssueIdentifier, which is a required field
	// Response contains a deeplink to which user should be navigated
	// in-case this RPC fails for unknown reason, the client should navigate user to category selection screen
	GetNextActionForIssue(context.Context, *GetNextActionForIssueRequest) (*GetNextActionForIssueResponse, error)
	// This RPC is called when the user selects a contact channel to contact customer care.
	// The objective of this RPC is to pass all the relevant context of user issue to the agent when they reach out.
	// Request takes the selected contact option (mandatory), available contact options, user issue identifier (mandatory) and meta data
	// if this RPC fails, the client should ignore the failure and continue with the flow, this should happen on best-effort basis
	SelectContactChannel(context.Context, *SelectContactChannelRequest) (*SelectContactChannelResponse, error)
	// This RPC fetches the content for the new Contact Us Landing Screen (V2).
	// It provides all the necessary sections and elements to render the screen.
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748&t=4SzjTwXgOKZ9sTE9-4
	GetContactUsLandingScreenV2(context.Context, *GetContactUsLandingScreenV2Request) (*GetContactUsLandingScreenV2Response, error)
	// This RPC fetches the content for the Open Incidents Screen.
	// It provides a list of active incidents or service disruptions.
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35931&t=4SzjTwXgOKZ9sTE9-4
	GetOpenIncidentsScreen(context.Context, *GetOpenIncidentsScreenRequest) (*GetOpenIncidentsScreenResponse, error)
}

// UnimplementedContactUsServer should be embedded to have forward compatible implementations.
type UnimplementedContactUsServer struct {
}

func (UnimplementedContactUsServer) GetContactUsLandingScreen(context.Context, *GetContactUsLandingScreenRequest) (*GetContactUsLandingScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContactUsLandingScreen not implemented")
}
func (UnimplementedContactUsServer) GetIssuesBasedOnPrompt(context.Context, *GetIssuesBasedOnPromptRequest) (*GetIssuesBasedOnPromptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIssuesBasedOnPrompt not implemented")
}
func (UnimplementedContactUsServer) GetContactUsTerminalScreen(context.Context, *GetContactUsTerminalScreenRequest) (*GetContactUsTerminalScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContactUsTerminalScreen not implemented")
}
func (UnimplementedContactUsServer) GetHelpLandingScreen(context.Context, *GetHelpLandingScreenRequest) (*GetHelpLandingScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHelpLandingScreen not implemented")
}
func (UnimplementedContactUsServer) GetCategorySelectionScreen(context.Context, *GetCategorySelectionScreenRequest) (*GetCategorySelectionScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCategorySelectionScreen not implemented")
}
func (UnimplementedContactUsServer) GetNextActionForIssue(context.Context, *GetNextActionForIssueRequest) (*GetNextActionForIssueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextActionForIssue not implemented")
}
func (UnimplementedContactUsServer) SelectContactChannel(context.Context, *SelectContactChannelRequest) (*SelectContactChannelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectContactChannel not implemented")
}
func (UnimplementedContactUsServer) GetContactUsLandingScreenV2(context.Context, *GetContactUsLandingScreenV2Request) (*GetContactUsLandingScreenV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContactUsLandingScreenV2 not implemented")
}
func (UnimplementedContactUsServer) GetOpenIncidentsScreen(context.Context, *GetOpenIncidentsScreenRequest) (*GetOpenIncidentsScreenResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpenIncidentsScreen not implemented")
}

// UnsafeContactUsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ContactUsServer will
// result in compilation errors.
type UnsafeContactUsServer interface {
	mustEmbedUnimplementedContactUsServer()
}

func RegisterContactUsServer(s grpc.ServiceRegistrar, srv ContactUsServer) {
	s.RegisterService(&ContactUs_ServiceDesc, srv)
}

func _ContactUs_GetContactUsLandingScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactUsLandingScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetContactUsLandingScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetContactUsLandingScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetContactUsLandingScreen(ctx, req.(*GetContactUsLandingScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_GetIssuesBasedOnPrompt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIssuesBasedOnPromptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetIssuesBasedOnPrompt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetIssuesBasedOnPrompt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetIssuesBasedOnPrompt(ctx, req.(*GetIssuesBasedOnPromptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_GetContactUsTerminalScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactUsTerminalScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetContactUsTerminalScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetContactUsTerminalScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetContactUsTerminalScreen(ctx, req.(*GetContactUsTerminalScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_GetHelpLandingScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHelpLandingScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetHelpLandingScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetHelpLandingScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetHelpLandingScreen(ctx, req.(*GetHelpLandingScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_GetCategorySelectionScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCategorySelectionScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetCategorySelectionScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetCategorySelectionScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetCategorySelectionScreen(ctx, req.(*GetCategorySelectionScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_GetNextActionForIssue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextActionForIssueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetNextActionForIssue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetNextActionForIssue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetNextActionForIssue(ctx, req.(*GetNextActionForIssueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_SelectContactChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SelectContactChannelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).SelectContactChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_SelectContactChannel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).SelectContactChannel(ctx, req.(*SelectContactChannelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_GetContactUsLandingScreenV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContactUsLandingScreenV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetContactUsLandingScreenV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetContactUsLandingScreenV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetContactUsLandingScreenV2(ctx, req.(*GetContactUsLandingScreenV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContactUs_GetOpenIncidentsScreen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpenIncidentsScreenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContactUsServer).GetOpenIncidentsScreen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContactUs_GetOpenIncidentsScreen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContactUsServer).GetOpenIncidentsScreen(ctx, req.(*GetOpenIncidentsScreenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ContactUs_ServiceDesc is the grpc.ServiceDesc for ContactUs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ContactUs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.inapphelp.contact_us.ContactUs",
	HandlerType: (*ContactUsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetContactUsLandingScreen",
			Handler:    _ContactUs_GetContactUsLandingScreen_Handler,
		},
		{
			MethodName: "GetIssuesBasedOnPrompt",
			Handler:    _ContactUs_GetIssuesBasedOnPrompt_Handler,
		},
		{
			MethodName: "GetContactUsTerminalScreen",
			Handler:    _ContactUs_GetContactUsTerminalScreen_Handler,
		},
		{
			MethodName: "GetHelpLandingScreen",
			Handler:    _ContactUs_GetHelpLandingScreen_Handler,
		},
		{
			MethodName: "GetCategorySelectionScreen",
			Handler:    _ContactUs_GetCategorySelectionScreen_Handler,
		},
		{
			MethodName: "GetNextActionForIssue",
			Handler:    _ContactUs_GetNextActionForIssue_Handler,
		},
		{
			MethodName: "SelectContactChannel",
			Handler:    _ContactUs_SelectContactChannel_Handler,
		},
		{
			MethodName: "GetContactUsLandingScreenV2",
			Handler:    _ContactUs_GetContactUsLandingScreenV2_Handler,
		},
		{
			MethodName: "GetOpenIncidentsScreen",
			Handler:    _ContactUs_GetOpenIncidentsScreen_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/inapphelp/contact_us/service.proto",
}
