// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/investment/aggregator/collective_investment_idea_component.proto

package aggregator

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Figma: https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=7920-16589&mode=dev
// Shows list of investment ideas as collections
type CollectiveInvestmentIdeaComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentIdeaComponents []*InvestmentIdeaComponent `protobuf:"bytes,1,rep,name=investment_idea_components,json=investmentIdeaComponents,proto3" json:"investment_idea_components,omitempty"`
	// background color for component
	BgColor string `protobuf:"bytes,6,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// separator color
	SeparatorColor string `protobuf:"bytes,7,opt,name=separator_color,json=separatorColor,proto3" json:"separator_color,omitempty"`
	// shadow for component
	Shadows []*ui.Shadow `protobuf:"bytes,8,rep,name=shadows,proto3" json:"shadows,omitempty"`
}

func (x *CollectiveInvestmentIdeaComponent) Reset() {
	*x = CollectiveInvestmentIdeaComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectiveInvestmentIdeaComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectiveInvestmentIdeaComponent) ProtoMessage() {}

func (x *CollectiveInvestmentIdeaComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectiveInvestmentIdeaComponent.ProtoReflect.Descriptor instead.
func (*CollectiveInvestmentIdeaComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescGZIP(), []int{0}
}

func (x *CollectiveInvestmentIdeaComponent) GetInvestmentIdeaComponents() []*InvestmentIdeaComponent {
	if x != nil {
		return x.InvestmentIdeaComponents
	}
	return nil
}

func (x *CollectiveInvestmentIdeaComponent) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *CollectiveInvestmentIdeaComponent) GetSeparatorColor() string {
	if x != nil {
		return x.SeparatorColor
	}
	return ""
}

func (x *CollectiveInvestmentIdeaComponent) GetShadows() []*ui.Shadow {
	if x != nil {
		return x.Shadows
	}
	return nil
}

type InvestmentIdeaComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// eg
	// row 1 - Invest in top tech stocks
	// row 2 - US markets are up again from last week
	VerticalTexts []*common.Text `protobuf:"bytes,1,rep,name=vertical_texts,json=verticalTexts,proto3" json:"vertical_texts,omitempty"`
	// Aligned to bottom right of the component
	Icon     *common.VisualElement `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Deeplink *deeplink.Deeplink    `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *InvestmentIdeaComponent) Reset() {
	*x = InvestmentIdeaComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvestmentIdeaComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvestmentIdeaComponent) ProtoMessage() {}

func (x *InvestmentIdeaComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvestmentIdeaComponent.ProtoReflect.Descriptor instead.
func (*InvestmentIdeaComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescGZIP(), []int{1}
}

func (x *InvestmentIdeaComponent) GetVerticalTexts() []*common.Text {
	if x != nil {
		return x.VerticalTexts
	}
	return nil
}

func (x *InvestmentIdeaComponent) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *InvestmentIdeaComponent) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

var File_api_frontend_investment_aggregator_collective_investment_idea_component_proto protoreflect.FileDescriptor

var file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDesc = []byte{
	0x0a, 0x4d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x61, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x1a,
	0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x90, 0x02, 0x0a, 0x21, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x61, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x75, 0x0a, 0x1a, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x49, 0x6e, 0x76,
	0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x18, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x70,
	0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x70, 0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x30, 0x0a, 0x07, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x52, 0x07, 0x73, 0x68, 0x61,
	0x64, 0x6f, 0x77, 0x73, 0x22, 0xca, 0x01, 0x0a, 0x17, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x61, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x3f, 0x0a, 0x0e, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x0d, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x54, 0x65, 0x78, 0x74,
	0x73, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x42, 0x76, 0x0a, 0x39, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x5a, 0x39,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescOnce sync.Once
	file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescData = file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDesc
)

func file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescGZIP() []byte {
	file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescOnce.Do(func() {
		file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescData)
	})
	return file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDescData
}

var file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_goTypes = []interface{}{
	(*CollectiveInvestmentIdeaComponent)(nil), // 0: frontend.investment.aggregator.CollectiveInvestmentIdeaComponent
	(*InvestmentIdeaComponent)(nil),           // 1: frontend.investment.aggregator.InvestmentIdeaComponent
	(*ui.Shadow)(nil),                         // 2: api.typesv2.ui.Shadow
	(*common.Text)(nil),                       // 3: api.typesv2.common.Text
	(*common.VisualElement)(nil),              // 4: api.typesv2.common.VisualElement
	(*deeplink.Deeplink)(nil),                 // 5: frontend.deeplink.Deeplink
}
var file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_depIdxs = []int32{
	1, // 0: frontend.investment.aggregator.CollectiveInvestmentIdeaComponent.investment_idea_components:type_name -> frontend.investment.aggregator.InvestmentIdeaComponent
	2, // 1: frontend.investment.aggregator.CollectiveInvestmentIdeaComponent.shadows:type_name -> api.typesv2.ui.Shadow
	3, // 2: frontend.investment.aggregator.InvestmentIdeaComponent.vertical_texts:type_name -> api.typesv2.common.Text
	4, // 3: frontend.investment.aggregator.InvestmentIdeaComponent.icon:type_name -> api.typesv2.common.VisualElement
	5, // 4: frontend.investment.aggregator.InvestmentIdeaComponent.deeplink:type_name -> frontend.deeplink.Deeplink
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() {
	file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_init()
}
func file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_init() {
	if File_api_frontend_investment_aggregator_collective_investment_idea_component_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectiveInvestmentIdeaComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvestmentIdeaComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_goTypes,
		DependencyIndexes: file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_depIdxs,
		MessageInfos:      file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_msgTypes,
	}.Build()
	File_api_frontend_investment_aggregator_collective_investment_idea_component_proto = out.File
	file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_rawDesc = nil
	file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_goTypes = nil
	file_api_frontend_investment_aggregator_collective_investment_idea_component_proto_depIdxs = nil
}
