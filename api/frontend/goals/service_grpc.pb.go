// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/goals/service.proto

package goals

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Goals_GetGoalDiscovery_FullMethodName                               = "/frontend.goals.Goals/GetGoalDiscovery"
	Goals_GetGoalDiscoveryInExistingInvestmentInstrument_FullMethodName = "/frontend.goals.Goals/GetGoalDiscoveryInExistingInvestmentInstrument"
	Goals_GetGoalAlterOptions_FullMethodName                            = "/frontend.goals.Goals/GetGoalAlterOptions"
	Goals_CreateGoal_FullMethodName                                     = "/frontend.goals.Goals/CreateGoal"
	Goals_UpdateGoal_FullMethodName                                     = "/frontend.goals.Goals/UpdateGoal"
)

// GoalsClient is the client API for Goals service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GoalsClient interface {
	// options for goal discovery during investment instrument creation
	GetGoalDiscovery(ctx context.Context, in *GetGoalDiscoveryRequest, opts ...grpc.CallOption) (*GetGoalDiscoveryResponse, error)
	// options for goal discovery in existing investment instrument which were already created
	// this rpc is usually called in the investment instrument details screen
	GetGoalDiscoveryInExistingInvestmentInstrument(ctx context.Context, in *GetGoalDiscoveryInExistingInvestmentInstrumentRequest, opts ...grpc.CallOption) (*GetGoalDiscoveryInExistingInvestmentInstrumentResponse, error)
	// GetGoalAlterOptions provides options for editing a goal or deleting a goal. only amount is editable for now.
	// this rpc is usually called in the investment instrument details screen
	GetGoalAlterOptions(ctx context.Context, in *GetGoalAlterOptionsRequest, opts ...grpc.CallOption) (*GetGoalAlterOptionsResponse, error)
	// CreateGoal creates a goal defined by the user for a given target amount and target date.
	// this rpc calls BE Goal's CreateGoal rpc
	CreateGoal(ctx context.Context, in *CreateGoalRequest, opts ...grpc.CallOption) (*CreateGoalResponse, error)
	// UpdateGoal updates a goal.
	// Note: not all fields are mutable from the UI. Currently, only amount can be updated for now.
	UpdateGoal(ctx context.Context, in *UpdateGoalRequest, opts ...grpc.CallOption) (*UpdateGoalResponse, error)
}

type goalsClient struct {
	cc grpc.ClientConnInterface
}

func NewGoalsClient(cc grpc.ClientConnInterface) GoalsClient {
	return &goalsClient{cc}
}

func (c *goalsClient) GetGoalDiscovery(ctx context.Context, in *GetGoalDiscoveryRequest, opts ...grpc.CallOption) (*GetGoalDiscoveryResponse, error) {
	out := new(GetGoalDiscoveryResponse)
	err := c.cc.Invoke(ctx, Goals_GetGoalDiscovery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goalsClient) GetGoalDiscoveryInExistingInvestmentInstrument(ctx context.Context, in *GetGoalDiscoveryInExistingInvestmentInstrumentRequest, opts ...grpc.CallOption) (*GetGoalDiscoveryInExistingInvestmentInstrumentResponse, error) {
	out := new(GetGoalDiscoveryInExistingInvestmentInstrumentResponse)
	err := c.cc.Invoke(ctx, Goals_GetGoalDiscoveryInExistingInvestmentInstrument_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goalsClient) GetGoalAlterOptions(ctx context.Context, in *GetGoalAlterOptionsRequest, opts ...grpc.CallOption) (*GetGoalAlterOptionsResponse, error) {
	out := new(GetGoalAlterOptionsResponse)
	err := c.cc.Invoke(ctx, Goals_GetGoalAlterOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goalsClient) CreateGoal(ctx context.Context, in *CreateGoalRequest, opts ...grpc.CallOption) (*CreateGoalResponse, error) {
	out := new(CreateGoalResponse)
	err := c.cc.Invoke(ctx, Goals_CreateGoal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goalsClient) UpdateGoal(ctx context.Context, in *UpdateGoalRequest, opts ...grpc.CallOption) (*UpdateGoalResponse, error) {
	out := new(UpdateGoalResponse)
	err := c.cc.Invoke(ctx, Goals_UpdateGoal_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GoalsServer is the server API for Goals service.
// All implementations should embed UnimplementedGoalsServer
// for forward compatibility
type GoalsServer interface {
	// options for goal discovery during investment instrument creation
	GetGoalDiscovery(context.Context, *GetGoalDiscoveryRequest) (*GetGoalDiscoveryResponse, error)
	// options for goal discovery in existing investment instrument which were already created
	// this rpc is usually called in the investment instrument details screen
	GetGoalDiscoveryInExistingInvestmentInstrument(context.Context, *GetGoalDiscoveryInExistingInvestmentInstrumentRequest) (*GetGoalDiscoveryInExistingInvestmentInstrumentResponse, error)
	// GetGoalAlterOptions provides options for editing a goal or deleting a goal. only amount is editable for now.
	// this rpc is usually called in the investment instrument details screen
	GetGoalAlterOptions(context.Context, *GetGoalAlterOptionsRequest) (*GetGoalAlterOptionsResponse, error)
	// CreateGoal creates a goal defined by the user for a given target amount and target date.
	// this rpc calls BE Goal's CreateGoal rpc
	CreateGoal(context.Context, *CreateGoalRequest) (*CreateGoalResponse, error)
	// UpdateGoal updates a goal.
	// Note: not all fields are mutable from the UI. Currently, only amount can be updated for now.
	UpdateGoal(context.Context, *UpdateGoalRequest) (*UpdateGoalResponse, error)
}

// UnimplementedGoalsServer should be embedded to have forward compatible implementations.
type UnimplementedGoalsServer struct {
}

func (UnimplementedGoalsServer) GetGoalDiscovery(context.Context, *GetGoalDiscoveryRequest) (*GetGoalDiscoveryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoalDiscovery not implemented")
}
func (UnimplementedGoalsServer) GetGoalDiscoveryInExistingInvestmentInstrument(context.Context, *GetGoalDiscoveryInExistingInvestmentInstrumentRequest) (*GetGoalDiscoveryInExistingInvestmentInstrumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoalDiscoveryInExistingInvestmentInstrument not implemented")
}
func (UnimplementedGoalsServer) GetGoalAlterOptions(context.Context, *GetGoalAlterOptionsRequest) (*GetGoalAlterOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoalAlterOptions not implemented")
}
func (UnimplementedGoalsServer) CreateGoal(context.Context, *CreateGoalRequest) (*CreateGoalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGoal not implemented")
}
func (UnimplementedGoalsServer) UpdateGoal(context.Context, *UpdateGoalRequest) (*UpdateGoalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGoal not implemented")
}

// UnsafeGoalsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GoalsServer will
// result in compilation errors.
type UnsafeGoalsServer interface {
	mustEmbedUnimplementedGoalsServer()
}

func RegisterGoalsServer(s grpc.ServiceRegistrar, srv GoalsServer) {
	s.RegisterService(&Goals_ServiceDesc, srv)
}

func _Goals_GetGoalDiscovery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoalDiscoveryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoalsServer).GetGoalDiscovery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Goals_GetGoalDiscovery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoalsServer).GetGoalDiscovery(ctx, req.(*GetGoalDiscoveryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Goals_GetGoalDiscoveryInExistingInvestmentInstrument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoalDiscoveryInExistingInvestmentInstrumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoalsServer).GetGoalDiscoveryInExistingInvestmentInstrument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Goals_GetGoalDiscoveryInExistingInvestmentInstrument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoalsServer).GetGoalDiscoveryInExistingInvestmentInstrument(ctx, req.(*GetGoalDiscoveryInExistingInvestmentInstrumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Goals_GetGoalAlterOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGoalAlterOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoalsServer).GetGoalAlterOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Goals_GetGoalAlterOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoalsServer).GetGoalAlterOptions(ctx, req.(*GetGoalAlterOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Goals_CreateGoal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGoalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoalsServer).CreateGoal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Goals_CreateGoal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoalsServer).CreateGoal(ctx, req.(*CreateGoalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Goals_UpdateGoal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGoalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoalsServer).UpdateGoal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Goals_UpdateGoal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoalsServer).UpdateGoal(ctx, req.(*UpdateGoalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Goals_ServiceDesc is the grpc.ServiceDesc for Goals service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Goals_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.goals.Goals",
	HandlerType: (*GoalsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGoalDiscovery",
			Handler:    _Goals_GetGoalDiscovery_Handler,
		},
		{
			MethodName: "GetGoalDiscoveryInExistingInvestmentInstrument",
			Handler:    _Goals_GetGoalDiscoveryInExistingInvestmentInstrument_Handler,
		},
		{
			MethodName: "GetGoalAlterOptions",
			Handler:    _Goals_GetGoalAlterOptions_Handler,
		},
		{
			MethodName: "CreateGoal",
			Handler:    _Goals_CreateGoal_Handler,
		},
		{
			MethodName: "UpdateGoal",
			Handler:    _Goals_UpdateGoal_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/goals/service.proto",
}
