package goals

import (
	beGoalsPb "github.com/epifi/gamma/api/goals"
)

// ToBeProto converts FE goal state to BE goal state
func (x GoalState) ToBeProto() beGoalsPb.GoalState {
	val, ok := goalStateFeToBeMap[x]
	if !ok {
		return beGoalsPb.GoalState_GOAL_STATE_UNSPECIFIED
	}
	return val
}

// FromBeToFeGoalState converts BE goal state to FE goal state
func FromBeToFeGoalState(gp beGoalsPb.GoalState) GoalState {
	val, ok := goalStateBeToFeMap[gp]
	if !ok {
		return GoalState_GOAL_STATE_UNSPECIFIED
	}
	return val
}

// map of goal state from FE to BE
var goalStateFeToBeMap = map[GoalState]beGoalsPb.GoalState{
	GoalState_GOAL_STATE_UNSPECIFIED: beGoalsPb.GoalState_GOAL_STATE_UNSPECIFIED,
	GoalState_GOAL_STATE_CREATED:     beGoalsPb.GoalState_GOAL_STATE_CREATED,
	GoalState_GOAL_STATE_ACTIVE:      beGoalsPb.GoalState_GOAL_STATE_ACTIVE,
	GoalState_GOAL_STATE_COMPLETED:   beGoalsPb.GoalState_GOAL_STATE_COMPLETED,
}

// map of goal state from BE to FE
var goalStateBeToFeMap = map[beGoalsPb.GoalState]GoalState{
	beGoalsPb.GoalState_GOAL_STATE_UNSPECIFIED: GoalState_GOAL_STATE_UNSPECIFIED,
	beGoalsPb.GoalState_GOAL_STATE_CREATED:     GoalState_GOAL_STATE_CREATED,
	beGoalsPb.GoalState_GOAL_STATE_ACTIVE:      GoalState_GOAL_STATE_ACTIVE,
	beGoalsPb.GoalState_GOAL_STATE_COMPLETED:   GoalState_GOAL_STATE_COMPLETED,
}
