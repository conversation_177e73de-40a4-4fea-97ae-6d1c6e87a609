// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/goals/goal.proto

package goals

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Goal with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Goal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Goal with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in GoalMultiError, or nil if none found.
func (m *Goal) ValidateAll() error {
	return m.validate(true)
}

func (m *Goal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetTargetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GoalValidationError{
					field:  "TargetAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GoalValidationError{
					field:  "TargetAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GoalValidationError{
				field:  "TargetAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTargetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GoalValidationError{
					field:  "TargetDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GoalValidationError{
					field:  "TargetDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GoalValidationError{
				field:  "TargetDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Name

	// no validation rules for State

	// no validation rules for Provenance

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GoalValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GoalValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GoalValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GoalMultiError(errors)
	}

	return nil
}

// GoalMultiError is an error wrapping multiple validation errors returned by
// Goal.ValidateAll() if the designated constraints aren't met.
type GoalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GoalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GoalMultiError) AllErrors() []error { return m }

// GoalValidationError is the validation error returned by Goal.Validate if the
// designated constraints aren't met.
type GoalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GoalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GoalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GoalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GoalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GoalValidationError) ErrorName() string { return "GoalValidationError" }

// Error satisfies the builtin error interface
func (e GoalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGoal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GoalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GoalValidationError{}
