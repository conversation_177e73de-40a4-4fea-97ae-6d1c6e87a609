syntax = "proto3";
package frontend.analyser;

import "api/frontend/analyser/text.proto";
import "api/frontend/analyser/filter.proto";

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

enum TimeFilterQuickSelectOptionType {
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_UNSPECIFIED = 0;
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_CURRENT_MONTH = 1;
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_LAST_MONTH = 2;
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_THIS_YEAR = 3;
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_LAST_YEAR = 4;
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_LAST_12_MONTHS = 5;
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_LAST_5_YEARS = 6;
  TIME_FILTER_QUICK_SELECT_OPTION_TYPE_LAST_10_YEARS = 7;
}

message TimeFilterQuickSelectOption {
  TimeFilterQuickSelectOptionType type = 1;
  TextElement label = 2;
  DateRange date_range = 3;
  bool is_selected = 4;
}

// Data required by client to show time filter
message TimeFilterV2Widget {
  // list of common time ranges selection options, at max one option is selected
  // if no option is selected the custom_time_selection is in use
  repeated TimeFilterQuickSelectOption quick_select_options = 1;
  // custom time range selection
    TimeRangeSelection custom_time_selection = 2;
}

// Selection allowed in custom time selection
enum TimeRangeSelectionType {
  TIME_RANGE_SELECTION_TYPE_UNSPECIFIED = 0;
  // selection of only from date
  TIME_RANGE_SELECTION_TYPE_FROM_DATE = 1;
  // selection of from and to date
  TIME_RANGE_SELECTION_TYPE_FROM_TO_DATE  = 2;
}

// Enums used to decide the type of options to be shown to user
enum TimeFilterSelectionGranularity {
  TIME_FILTER_SELECTION_GRANULARITY_UNSPECIFIED = 0;
  // option to select only year
  TIME_FILTER_SELECTION_GRANULARITY_YEAR = 1;
  // option to select month and year
  TIME_FILTER_SELECTION_GRANULARITY_MONTH = 2;
  // option to select day, month and year
  TIME_FILTER_SELECTION_GRANULARITY_DAY = 3;
}


enum TimeRangeValidationType {
  TIME_RANGE_VALIDATION_UNSPECIFIED = 0;
  TIME_RANGE_VALIDATION_MAX_MONTHS = 1;
}

// TimeRangeValidation defines type and params to be used for time range validation
message TimeRangeValidation {
  TimeRangeValidationType type = 1;
  oneof Params {
    TimeRangeValidationMaxMonth max_month_validation = 2;
  }
}

// TimeRangeValidationMaxMonth contains validation params for max number of month allowed in date range selection
message TimeRangeValidationMaxMonth {
  // max number of months allowed in date range selection
  int32 max_months = 1;
}

// TimeRangeSelection defines selection of a custom time range
message TimeRangeSelection {
  // TimeRangeSelectionType defines the user interaction allowed in the filter
  // TIME_FILTER_TYPE_FROM_DATE : user can select from time. to time selection is restricted
  // TIME_FILTER_TYPE_FROM_TO_DATE : user can select from and to time
  TimeRangeSelectionType type = 1;

  // defines the time granularity supported by the filter
  TimeFilterSelectionGranularity granularity = 2;

  // date range selected on the filter
  DateRange date_range = 3;

  // validations to be applied to the time range selection
  // client and backend maintain validation logic on the basis of TimeRangeValidationType
  // client embed the message to be displayed on validation failure
  repeated TimeRangeValidation validations = 4;

  // indicates if custom option should be highlighted as selected
  bool is_selected = 5;
}

// TimeFilterSelectionType define type of time filter value selected by the user
enum TimeFilterSelectionType {
  TIME_FILTER_SELECTION_TYPE_UNSPECIFIED = 0;
  // user selected one of the options from quick select
  TIME_FILTER_SELECTION_TYPE_QUICK_SELECT = 1;
  // user selected a custom time range
  TIME_FILTER_SELECTION_TYPE_CUSTOM = 2;
}

// TimeFilterValue to be enriched by client on the basis of user selection
message TimeFilterV2Value {
  // selection_type defines what type of filter value was selected
  TimeFilterSelectionType selection_type = 1;
  oneof value {
    TimeFilterQuickSelectOptionType quick_select_option = 2;
    DateRange selected_date_range = 3;
  }
}
