syntax = "proto3";

package api.frontend.vkyccall;

option go_package = "github.com/epifi/gamma/api/frontend/vkyccall";
option java_package = "com.github.epifi.gamma.api.frontend.vkyccall";

enum CallActionType {
  CALL_ACTION_TYPE_UNSPECIFIED = 0;
  CALL_ACTION_TYPE_JOIN_ROOM = 1;
  CALL_ACTION_TYPE_END_CALL = 2;
  CALL_ACTION_TYPE_FLIP_CAMERA = 3;
  CALL_ACTION_TYPE_CAPTURE_SCREENSHOT = 4;
  CALL_ACTION_TYPE_UI_PROMPT = 5;
  CALL_ACTION_TYPE_PING = 6;
}

enum CallEndReason {
  CALL_END_REASON_UNSPECIFIED = 0;
  CALL_END_REASON_AGENT_END = 1;
  CALL_END_REASON_TIMED_OUT = 2;
}
