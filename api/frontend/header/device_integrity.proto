syntax = "proto3";

package frontend.header;

option go_package = "github.com/epifi/gamma/api/frontend/header";
option java_package = "com.github.epifi.gamma.api.frontend.header";

// different types of consents needed from user in order to allow them
// to use the app on an unsafe device.
// Using UnsafeDeviceConsentType instead of frontend.consent.ConsentType
// because of circular dependency error.
enum UnsafeDeviceConsentType {
  // deprecated as no longer required with new async device integrity check (V2)
  // flow where consent is recorded only from FE rpc VerifyDeviceIntegrity

  UNSAFE_DEVCE_CONSENT_TYPE_UNSPECIFIED = 0 [deprecated = true] ;
  // needed for cases when device fails CTS profile match test
  HIGH_RISK_DEVICE = 1 [deprecated = true];
}

// contains info related to device integrity that is passed by the client in request header
message DeviceIntegrity {
  // deprecated as no longer required with new async device integrity check (V2)
  // flow where consent is recorded only from FE rpc VerifyDeviceIntegrity
  //
  // For some specific failures during device verification,
  // we allow app access to the user provided that they give
  // consent to using the app on an unsafe device.
  // This field can be used by client to inform the backend
  // that user has given their consent.
  UnsafeDeviceConsentType unsafe_device_consent_type = 1 [deprecated = true];
  // deprecated as no longer required with new async device integrity check (V2)
  // flow where consent is recorded only from FE rpc VerifyDeviceIntegrity
  //
  // duration(in secs) for which the unsafe device user consent is valid.
  uint32 unsafe_device_consent_duration = 2 [deprecated = true];

  // contains identifier for device integrity results, which can used to check integrity status and its validity
  string device_integrity_token = 3;
}
