// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/fittt/rule/rule_params.proto

package rule

import (
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RuleParamEditState int32

const (
	RuleParamEditState_RULE_PARAM_EDIT_STATE_UNSPECIFIED RuleParamEditState = 0
	RuleParamEditState_SELECT_FOR_EDIT                   RuleParamEditState = 1 // Param with this state should be allowed to edit first
	RuleParamEditState_EDITABLE                          RuleParamEditState = 2 // can be edited
	RuleParamEditState_NOT_EDITABLE                      RuleParamEditState = 3 // shouldn't allowed to edit. Ex., unique params are not allowed to edit till M40
	// the param will be merged with the rule description text and will not be clickable
	// example usage: "SD name would be not clickable in deposit inline auto-save flow"
	// e.g: "Vacay fund" in https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?node-id=10401-39305&t=Md6ebKFVVSqpHlY9-4
	RuleParamEditState_NOT_EDITABLE_WITH_HIDDEN_OPTIONS RuleParamEditState = 4
)

// Enum value maps for RuleParamEditState.
var (
	RuleParamEditState_name = map[int32]string{
		0: "RULE_PARAM_EDIT_STATE_UNSPECIFIED",
		1: "SELECT_FOR_EDIT",
		2: "EDITABLE",
		3: "NOT_EDITABLE",
		4: "NOT_EDITABLE_WITH_HIDDEN_OPTIONS",
	}
	RuleParamEditState_value = map[string]int32{
		"RULE_PARAM_EDIT_STATE_UNSPECIFIED": 0,
		"SELECT_FOR_EDIT":                   1,
		"EDITABLE":                          2,
		"NOT_EDITABLE":                      3,
		"NOT_EDITABLE_WITH_HIDDEN_OPTIONS":  4,
	}
)

func (x RuleParamEditState) Enum() *RuleParamEditState {
	p := new(RuleParamEditState)
	*p = x
	return p
}

func (x RuleParamEditState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RuleParamEditState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_fittt_rule_rule_params_proto_enumTypes[0].Descriptor()
}

func (RuleParamEditState) Type() protoreflect.EnumType {
	return &file_api_frontend_fittt_rule_rule_params_proto_enumTypes[0]
}

func (x RuleParamEditState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RuleParamEditState.Descriptor instead.
func (RuleParamEditState) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP(), []int{0}
}

type RuleParamValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// considering above example, {"confMerchantName":"Swiggy", "minAmount":500, "percentageVal":5, "sdName":"Test"} are the rule_param_values
	RuleParamValues map[string]*Value `protobuf:"bytes,1,rep,name=rule_param_values,json=ruleParamValues,proto3" json:"rule_param_values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RuleParamValues) Reset() {
	*x = RuleParamValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuleParamValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleParamValues) ProtoMessage() {}

func (x *RuleParamValues) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleParamValues.ProtoReflect.Descriptor instead.
func (*RuleParamValues) Descriptor() ([]byte, []int) {
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP(), []int{0}
}

func (x *RuleParamValues) GetRuleParamValues() map[string]*Value {
	if x != nil {
		return x.RuleParamValues
	}
	return nil
}

// Value for the user provided inputs on rule subscription
type Value struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*Value_SdValue
	//	*Value_MoneyVal
	//	*Value_DayOfWeekVal
	//	*Value_DateOfMonthVal
	//	*Value_UsStockValue
	Value isValue_Value `protobuf_oneof:"value"`
	// Id to identify value uniquely on client
	Id string `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"`
	// required for multiple subscription support, applicable for unique params only
	// if the subscription exists with this param, is_selected flag is true
	IsSelected bool `protobuf:"varint,17,opt,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty"`
	// tells the behaviour of the param if its editable, not editable or not editable with no options
	EditState RuleParamEditState `protobuf:"varint,24,opt,name=edit_state,json=editState,proto3,enum=frontend.fittt.rule.RuleParamEditState" json:"edit_state,omitempty"`
}

func (x *Value) Reset() {
	*x = Value{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Value) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Value) ProtoMessage() {}

func (x *Value) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Value.ProtoReflect.Descriptor instead.
func (*Value) Descriptor() ([]byte, []int) {
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP(), []int{1}
}

func (m *Value) GetValue() isValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *Value) GetSdValue() *SdParamValue {
	if x, ok := x.GetValue().(*Value_SdValue); ok {
		return x.SdValue
	}
	return nil
}

func (x *Value) GetMoneyVal() *typesv2.Money {
	if x, ok := x.GetValue().(*Value_MoneyVal); ok {
		return x.MoneyVal
	}
	return nil
}

func (x *Value) GetDayOfWeekVal() *DayOfWeekVal {
	if x, ok := x.GetValue().(*Value_DayOfWeekVal); ok {
		return x.DayOfWeekVal
	}
	return nil
}

func (x *Value) GetDateOfMonthVal() *DateOfMonthVal {
	if x, ok := x.GetValue().(*Value_DateOfMonthVal); ok {
		return x.DateOfMonthVal
	}
	return nil
}

func (x *Value) GetUsStockValue() *USStockValue {
	if x, ok := x.GetValue().(*Value_UsStockValue); ok {
		return x.UsStockValue
	}
	return nil
}

func (x *Value) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Value) GetIsSelected() bool {
	if x != nil {
		return x.IsSelected
	}
	return false
}

func (x *Value) GetEditState() RuleParamEditState {
	if x != nil {
		return x.EditState
	}
	return RuleParamEditState_RULE_PARAM_EDIT_STATE_UNSPECIFIED
}

type isValue_Value interface {
	isValue_Value()
}

type Value_SdValue struct {
	SdValue *SdParamValue `protobuf:"bytes,4,opt,name=sd_value,json=sdValue,proto3,oneof"`
}

type Value_MoneyVal struct {
	MoneyVal *typesv2.Money `protobuf:"bytes,5,opt,name=money_val,json=moneyVal,proto3,oneof"`
}

type Value_DayOfWeekVal struct {
	// day of the week
	DayOfWeekVal *DayOfWeekVal `protobuf:"bytes,22,opt,name=day_of_week_val,json=dayOfWeekVal,proto3,oneof"`
}

type Value_DateOfMonthVal struct {
	// date of a month
	// valid entries can be from 1 to 28
	DateOfMonthVal *DateOfMonthVal `protobuf:"bytes,23,opt,name=date_of_month_val,json=dateOfMonthVal,proto3,oneof"`
}

type Value_UsStockValue struct {
	// Value of stock for US stocks SIP specific rules and subscriptions
	UsStockValue *USStockValue `protobuf:"bytes,1,opt,name=us_stock_value,json=usStockValue,proto3,oneof"`
}

func (*Value_SdValue) isValue_Value() {}

func (*Value_MoneyVal) isValue_Value() {}

func (*Value_DayOfWeekVal) isValue_Value() {}

func (*Value_DateOfMonthVal) isValue_Value() {}

func (*Value_UsStockValue) isValue_Value() {}

type SdParamValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the SD
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// account identifier
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// masked account number
	MaskedAccountNumber string `protobuf:"bytes,3,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
}

func (x *SdParamValue) Reset() {
	*x = SdParamValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SdParamValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SdParamValue) ProtoMessage() {}

func (x *SdParamValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SdParamValue.ProtoReflect.Descriptor instead.
func (*SdParamValue) Descriptor() ([]byte, []int) {
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP(), []int{2}
}

func (x *SdParamValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SdParamValue) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *SdParamValue) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

type DayOfWeekVal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// eg: Monday, Tuesday etc - parsed from google/type/dayofweek.proto and converted to Titlecase
	Weekday string `protobuf:"bytes,1,opt,name=weekday,proto3" json:"weekday,omitempty"`
	// eg: Mon, Tue, Thu etc
	AbbrName string `protobuf:"bytes,2,opt,name=abbr_name,json=abbrName,proto3" json:"abbr_name,omitempty"`
	// bool to check if the day is enabled to select by user
	Enabled bool `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *DayOfWeekVal) Reset() {
	*x = DayOfWeekVal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DayOfWeekVal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DayOfWeekVal) ProtoMessage() {}

func (x *DayOfWeekVal) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DayOfWeekVal.ProtoReflect.Descriptor instead.
func (*DayOfWeekVal) Descriptor() ([]byte, []int) {
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP(), []int{3}
}

func (x *DayOfWeekVal) GetWeekday() string {
	if x != nil {
		return x.Weekday
	}
	return ""
}

func (x *DayOfWeekVal) GetAbbrName() string {
	if x != nil {
		return x.AbbrName
	}
	return ""
}

func (x *DayOfWeekVal) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type DateOfMonthVal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// eg: 1, 2, 3 etc
	Date int32 `protobuf:"varint,1,opt,name=date,proto3" json:"date,omitempty"`
	// eg: 1st, 2nd, 3rd etc
	DisplayString string `protobuf:"bytes,2,opt,name=display_string,json=displayString,proto3" json:"display_string,omitempty"`
	// bool to check if the date is enabled to select by user
	Enabled bool `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *DateOfMonthVal) Reset() {
	*x = DateOfMonthVal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateOfMonthVal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateOfMonthVal) ProtoMessage() {}

func (x *DateOfMonthVal) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateOfMonthVal.ProtoReflect.Descriptor instead.
func (*DateOfMonthVal) Descriptor() ([]byte, []int) {
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP(), []int{4}
}

func (x *DateOfMonthVal) GetDate() int32 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *DateOfMonthVal) GetDisplayString() string {
	if x != nil {
		return x.DisplayString
	}
	return ""
}

func (x *DateOfMonthVal) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type USStockValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of a stock / ETF listed in US stock exchanges
	StockId string `protobuf:"bytes,1,opt,name=stock_id,json=stockId,proto3" json:"stock_id,omitempty"`
}

func (x *USStockValue) Reset() {
	*x = USStockValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *USStockValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*USStockValue) ProtoMessage() {}

func (x *USStockValue) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_fittt_rule_rule_params_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use USStockValue.ProtoReflect.Descriptor instead.
func (*USStockValue) Descriptor() ([]byte, []int) {
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP(), []int{5}
}

func (x *USStockValue) GetStockId() string {
	if x != nil {
		return x.StockId
	}
	return ""
}

var File_api_frontend_fittt_rule_rule_params_proto protoreflect.FileDescriptor

var file_api_frontend_fittt_rule_rule_params_proto_rawDesc = []byte{
	0x0a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x66,
	0x69, 0x74, 0x74, 0x74, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x2f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x72, 0x75, 0x6c, 0x65,
	0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd8, 0x01, 0x0a, 0x0f, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x65, 0x0a,
	0x11, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52,
	0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x2e, 0x52,
	0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x72, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x1a, 0x5e, 0x0a, 0x14, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x72,
	0x75, 0x6c, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xe5, 0x03, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3e,
	0x0a, 0x08, 0x73, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74,
	0x74, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x53, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x07, 0x73, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x31,
	0x0a, 0x09, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x12, 0x4a, 0x0a, 0x0f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b,
	0x5f, 0x76, 0x61, 0x6c, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x72, 0x75, 0x6c, 0x65,
	0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x56, 0x61, 0x6c, 0x48, 0x00, 0x52,
	0x0c, 0x64, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x56, 0x61, 0x6c, 0x12, 0x50, 0x0a,
	0x11, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x76,
	0x61, 0x6c, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x48, 0x00, 0x52,
	0x0e, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x12,
	0x49, 0x0a, 0x0e, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x55, 0x53,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x75, 0x73,
	0x53, 0x74, 0x6f, 0x63, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73,
	0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x46, 0x0a, 0x0a, 0x65,
	0x64, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74,
	0x2e, 0x72, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x45,
	0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x65, 0x64, 0x69, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x75, 0x0a, 0x0c,
	0x53, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x32, 0x0a, 0x15, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x5f, 0x0a, 0x0c, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b,
	0x56, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x65, 0x6b, 0x64, 0x61, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x65, 0x6b, 0x64, 0x61, 0x79, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x62, 0x62, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x61, 0x62, 0x62, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x22, 0x65, 0x0a, 0x0e, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x56, 0x61, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x29, 0x0a, 0x0c, 0x55,
	0x53, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x49, 0x64, 0x2a, 0x96, 0x01, 0x0a, 0x12, 0x52, 0x75, 0x6c, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a,
	0x21, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x5f, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x44, 0x49,
	0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x45,
	0x44, 0x49, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x4f, 0x54,
	0x5f, 0x45, 0x44, 0x49, 0x54, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x48,
	0x49, 0x44, 0x44, 0x45, 0x4e, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x04, 0x42,
	0x60, 0x0a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2e, 0x72, 0x75, 0x6c,
	0x65, 0x5a, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x69, 0x74, 0x74, 0x74, 0x2f, 0x72, 0x75, 0x6c,
	0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_fittt_rule_rule_params_proto_rawDescOnce sync.Once
	file_api_frontend_fittt_rule_rule_params_proto_rawDescData = file_api_frontend_fittt_rule_rule_params_proto_rawDesc
)

func file_api_frontend_fittt_rule_rule_params_proto_rawDescGZIP() []byte {
	file_api_frontend_fittt_rule_rule_params_proto_rawDescOnce.Do(func() {
		file_api_frontend_fittt_rule_rule_params_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_fittt_rule_rule_params_proto_rawDescData)
	})
	return file_api_frontend_fittt_rule_rule_params_proto_rawDescData
}

var file_api_frontend_fittt_rule_rule_params_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_frontend_fittt_rule_rule_params_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_frontend_fittt_rule_rule_params_proto_goTypes = []interface{}{
	(RuleParamEditState)(0), // 0: frontend.fittt.rule.RuleParamEditState
	(*RuleParamValues)(nil), // 1: frontend.fittt.rule.RuleParamValues
	(*Value)(nil),           // 2: frontend.fittt.rule.Value
	(*SdParamValue)(nil),    // 3: frontend.fittt.rule.SdParamValue
	(*DayOfWeekVal)(nil),    // 4: frontend.fittt.rule.DayOfWeekVal
	(*DateOfMonthVal)(nil),  // 5: frontend.fittt.rule.DateOfMonthVal
	(*USStockValue)(nil),    // 6: frontend.fittt.rule.USStockValue
	nil,                     // 7: frontend.fittt.rule.RuleParamValues.RuleParamValuesEntry
	(*typesv2.Money)(nil),   // 8: api.typesv2.Money
}
var file_api_frontend_fittt_rule_rule_params_proto_depIdxs = []int32{
	7, // 0: frontend.fittt.rule.RuleParamValues.rule_param_values:type_name -> frontend.fittt.rule.RuleParamValues.RuleParamValuesEntry
	3, // 1: frontend.fittt.rule.Value.sd_value:type_name -> frontend.fittt.rule.SdParamValue
	8, // 2: frontend.fittt.rule.Value.money_val:type_name -> api.typesv2.Money
	4, // 3: frontend.fittt.rule.Value.day_of_week_val:type_name -> frontend.fittt.rule.DayOfWeekVal
	5, // 4: frontend.fittt.rule.Value.date_of_month_val:type_name -> frontend.fittt.rule.DateOfMonthVal
	6, // 5: frontend.fittt.rule.Value.us_stock_value:type_name -> frontend.fittt.rule.USStockValue
	0, // 6: frontend.fittt.rule.Value.edit_state:type_name -> frontend.fittt.rule.RuleParamEditState
	2, // 7: frontend.fittt.rule.RuleParamValues.RuleParamValuesEntry.value:type_name -> frontend.fittt.rule.Value
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_frontend_fittt_rule_rule_params_proto_init() }
func file_api_frontend_fittt_rule_rule_params_proto_init() {
	if File_api_frontend_fittt_rule_rule_params_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_fittt_rule_rule_params_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuleParamValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_fittt_rule_rule_params_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Value); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_fittt_rule_rule_params_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SdParamValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_fittt_rule_rule_params_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DayOfWeekVal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_fittt_rule_rule_params_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateOfMonthVal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_fittt_rule_rule_params_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*USStockValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_fittt_rule_rule_params_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Value_SdValue)(nil),
		(*Value_MoneyVal)(nil),
		(*Value_DayOfWeekVal)(nil),
		(*Value_DateOfMonthVal)(nil),
		(*Value_UsStockValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_fittt_rule_rule_params_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_fittt_rule_rule_params_proto_goTypes,
		DependencyIndexes: file_api_frontend_fittt_rule_rule_params_proto_depIdxs,
		EnumInfos:         file_api_frontend_fittt_rule_rule_params_proto_enumTypes,
		MessageInfos:      file_api_frontend_fittt_rule_rule_params_proto_msgTypes,
	}.Build()
	File_api_frontend_fittt_rule_rule_params_proto = out.File
	file_api_frontend_fittt_rule_rule_params_proto_rawDesc = nil
	file_api_frontend_fittt_rule_rule_params_proto_goTypes = nil
	file_api_frontend_fittt_rule_rule_params_proto_depIdxs = nil
}
