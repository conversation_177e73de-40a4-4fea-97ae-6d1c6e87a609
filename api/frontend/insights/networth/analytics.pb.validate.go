// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/networth/internal/analytics.proto

package networth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on WidgetAnalyticsPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WidgetAnalyticsPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WidgetAnalyticsPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WidgetAnalyticsPayloadMultiError, or nil if none found.
func (m *WidgetAnalyticsPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *WidgetAnalyticsPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WidgetName

	// no validation rules for Status

	if len(errors) > 0 {
		return WidgetAnalyticsPayloadMultiError(errors)
	}

	return nil
}

// WidgetAnalyticsPayloadMultiError is an error wrapping multiple validation
// errors returned by WidgetAnalyticsPayload.ValidateAll() if the designated
// constraints aren't met.
type WidgetAnalyticsPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WidgetAnalyticsPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WidgetAnalyticsPayloadMultiError) AllErrors() []error { return m }

// WidgetAnalyticsPayloadValidationError is the validation error returned by
// WidgetAnalyticsPayload.Validate if the designated constraints aren't met.
type WidgetAnalyticsPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WidgetAnalyticsPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WidgetAnalyticsPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WidgetAnalyticsPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WidgetAnalyticsPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WidgetAnalyticsPayloadValidationError) ErrorName() string {
	return "WidgetAnalyticsPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e WidgetAnalyticsPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWidgetAnalyticsPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WidgetAnalyticsPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WidgetAnalyticsPayloadValidationError{}
