// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/insights/epf/service.proto

package epf

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	insights "github.com/epifi/gamma/api/frontend/insights"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = insights.UanListScreenSource(0)
)

// Validate checks the field values on GetEpfLandingPageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfLandingPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfLandingPageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfLandingPageRequestMultiError, or nil if none found.
func (m *GetEpfLandingPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfLandingPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfLandingPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfLandingPageRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfLandingPageRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEpfLandingPageRequestMultiError(errors)
	}

	return nil
}

// GetEpfLandingPageRequestMultiError is an error wrapping multiple validation
// errors returned by GetEpfLandingPageRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEpfLandingPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfLandingPageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfLandingPageRequestMultiError) AllErrors() []error { return m }

// GetEpfLandingPageRequestValidationError is the validation error returned by
// GetEpfLandingPageRequest.Validate if the designated constraints aren't met.
type GetEpfLandingPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfLandingPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfLandingPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfLandingPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfLandingPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfLandingPageRequestValidationError) ErrorName() string {
	return "GetEpfLandingPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfLandingPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfLandingPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfLandingPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfLandingPageRequestValidationError{}

// Validate checks the field values on GetEpfLandingPageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEpfLandingPageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEpfLandingPageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEpfLandingPageResponseMultiError, or nil if none found.
func (m *GetEpfLandingPageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEpfLandingPageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfLandingPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfLandingPageResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfLandingPageResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLandingPageComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEpfLandingPageResponseValidationError{
						field:  fmt.Sprintf("LandingPageComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEpfLandingPageResponseValidationError{
						field:  fmt.Sprintf("LandingPageComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEpfLandingPageResponseValidationError{
					field:  fmt.Sprintf("LandingPageComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetEpfSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfLandingPageResponseValidationError{
					field:  "EpfSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfLandingPageResponseValidationError{
					field:  "EpfSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEpfSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfLandingPageResponseValidationError{
				field:  "EpfSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedirectionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEpfLandingPageResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEpfLandingPageResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEpfLandingPageResponseValidationError{
				field:  "RedirectionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEpfLandingPageResponseMultiError(errors)
	}

	return nil
}

// GetEpfLandingPageResponseMultiError is an error wrapping multiple validation
// errors returned by GetEpfLandingPageResponse.ValidateAll() if the
// designated constraints aren't met.
type GetEpfLandingPageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEpfLandingPageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEpfLandingPageResponseMultiError) AllErrors() []error { return m }

// GetEpfLandingPageResponseValidationError is the validation error returned by
// GetEpfLandingPageResponse.Validate if the designated constraints aren't met.
type GetEpfLandingPageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEpfLandingPageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEpfLandingPageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEpfLandingPageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEpfLandingPageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEpfLandingPageResponseValidationError) ErrorName() string {
	return "GetEpfLandingPageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEpfLandingPageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEpfLandingPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEpfLandingPageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEpfLandingPageResponseValidationError{}

// Validate checks the field values on DiscoverUANsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DiscoverUANsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DiscoverUANsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DiscoverUANsRequestMultiError, or nil if none found.
func (m *DiscoverUANsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DiscoverUANsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DiscoverUANsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DiscoverUANsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DiscoverUANsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EpfImportSessionId

	switch v := m.GetBy.(type) {
	case *DiscoverUANsRequest_PhoneNumber:
		if v == nil {
			err := DiscoverUANsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhoneNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DiscoverUANsRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DiscoverUANsRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DiscoverUANsRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DiscoverUANsRequestMultiError(errors)
	}

	return nil
}

// DiscoverUANsRequestMultiError is an error wrapping multiple validation
// errors returned by DiscoverUANsRequest.ValidateAll() if the designated
// constraints aren't met.
type DiscoverUANsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DiscoverUANsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DiscoverUANsRequestMultiError) AllErrors() []error { return m }

// DiscoverUANsRequestValidationError is the validation error returned by
// DiscoverUANsRequest.Validate if the designated constraints aren't met.
type DiscoverUANsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DiscoverUANsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DiscoverUANsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DiscoverUANsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DiscoverUANsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DiscoverUANsRequestValidationError) ErrorName() string {
	return "DiscoverUANsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DiscoverUANsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDiscoverUANsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DiscoverUANsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DiscoverUANsRequestValidationError{}

// Validate checks the field values on DiscoverUANsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DiscoverUANsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DiscoverUANsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DiscoverUANsResponseMultiError, or nil if none found.
func (m *DiscoverUANsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DiscoverUANsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DiscoverUANsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DiscoverUANsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DiscoverUANsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedirectionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DiscoverUANsResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DiscoverUANsResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DiscoverUANsResponseValidationError{
				field:  "RedirectionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DiscoverUANsResponseMultiError(errors)
	}

	return nil
}

// DiscoverUANsResponseMultiError is an error wrapping multiple validation
// errors returned by DiscoverUANsResponse.ValidateAll() if the designated
// constraints aren't met.
type DiscoverUANsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DiscoverUANsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DiscoverUANsResponseMultiError) AllErrors() []error { return m }

// DiscoverUANsResponseValidationError is the validation error returned by
// DiscoverUANsResponse.Validate if the designated constraints aren't met.
type DiscoverUANsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DiscoverUANsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DiscoverUANsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DiscoverUANsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DiscoverUANsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DiscoverUANsResponseValidationError) ErrorName() string {
	return "DiscoverUANsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DiscoverUANsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDiscoverUANsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DiscoverUANsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DiscoverUANsResponseValidationError{}

// Validate checks the field values on GetConnectUANsDashboardRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConnectUANsDashboardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConnectUANsDashboardRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetConnectUANsDashboardRequestMultiError, or nil if none found.
func (m *GetConnectUANsDashboardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectUANsDashboardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectUANsDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectUANsDashboardRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectUANsDashboardRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Source

	// no validation rules for EpfImportSessionId

	switch v := m.GetBy.(type) {
	case *GetConnectUANsDashboardRequest_PhoneNumber:
		if v == nil {
			err := GetConnectUANsDashboardRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPhoneNumber()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetConnectUANsDashboardRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetConnectUANsDashboardRequestValidationError{
						field:  "PhoneNumber",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetConnectUANsDashboardRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetConnectUANsDashboardRequestMultiError(errors)
	}

	return nil
}

// GetConnectUANsDashboardRequestMultiError is an error wrapping multiple
// validation errors returned by GetConnectUANsDashboardRequest.ValidateAll()
// if the designated constraints aren't met.
type GetConnectUANsDashboardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectUANsDashboardRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectUANsDashboardRequestMultiError) AllErrors() []error { return m }

// GetConnectUANsDashboardRequestValidationError is the validation error
// returned by GetConnectUANsDashboardRequest.Validate if the designated
// constraints aren't met.
type GetConnectUANsDashboardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectUANsDashboardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectUANsDashboardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectUANsDashboardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectUANsDashboardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectUANsDashboardRequestValidationError) ErrorName() string {
	return "GetConnectUANsDashboardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectUANsDashboardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectUANsDashboardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectUANsDashboardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectUANsDashboardRequestValidationError{}

// Validate checks the field values on GetConnectUANsDashboardResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetConnectUANsDashboardResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConnectUANsDashboardResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetConnectUANsDashboardResponseMultiError, or nil if none found.
func (m *GetConnectUANsDashboardResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConnectUANsDashboardResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectUANsDashboardResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectUANsDashboardResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectUANsDashboardResponseValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetConnectUanWidgets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetConnectUANsDashboardResponseValidationError{
						field:  fmt.Sprintf("ConnectUanWidgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetConnectUANsDashboardResponseValidationError{
						field:  fmt.Sprintf("ConnectUanWidgets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetConnectUANsDashboardResponseValidationError{
					field:  fmt.Sprintf("ConnectUanWidgets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBottomButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "BottomButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "BottomButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectUANsDashboardResponseValidationError{
				field:  "BottomButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFooter() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetConnectUANsDashboardResponseValidationError{
						field:  fmt.Sprintf("Footer[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetConnectUANsDashboardResponseValidationError{
						field:  fmt.Sprintf("Footer[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetConnectUANsDashboardResponseValidationError{
					field:  fmt.Sprintf("Footer[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConnectMoreDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "ConnectMoreDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetConnectUANsDashboardResponseValidationError{
					field:  "ConnectMoreDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectMoreDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetConnectUANsDashboardResponseValidationError{
				field:  "ConnectMoreDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetConnectUANsDashboardResponseMultiError(errors)
	}

	return nil
}

// GetConnectUANsDashboardResponseMultiError is an error wrapping multiple
// validation errors returned by GetConnectUANsDashboardResponse.ValidateAll()
// if the designated constraints aren't met.
type GetConnectUANsDashboardResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConnectUANsDashboardResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConnectUANsDashboardResponseMultiError) AllErrors() []error { return m }

// GetConnectUANsDashboardResponseValidationError is the validation error
// returned by GetConnectUANsDashboardResponse.Validate if the designated
// constraints aren't met.
type GetConnectUANsDashboardResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConnectUANsDashboardResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConnectUANsDashboardResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConnectUANsDashboardResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConnectUANsDashboardResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConnectUANsDashboardResponseValidationError) ErrorName() string {
	return "GetConnectUANsDashboardResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetConnectUANsDashboardResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConnectUANsDashboardResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConnectUANsDashboardResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConnectUANsDashboardResponseValidationError{}

// Validate checks the field values on Button with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Button) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Button with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ButtonMultiError, or nil if none found.
func (m *Button) ValidateAll() error {
	return m.validate(true)
}

func (m *Button) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ButtonValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ButtonValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ButtonValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedirection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ButtonValidationError{
					field:  "Redirection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ButtonValidationError{
					field:  "Redirection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ButtonValidationError{
				field:  "Redirection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ButtonMultiError(errors)
	}

	return nil
}

// ButtonMultiError is an error wrapping multiple validation errors returned by
// Button.ValidateAll() if the designated constraints aren't met.
type ButtonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ButtonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ButtonMultiError) AllErrors() []error { return m }

// ButtonValidationError is the validation error returned by Button.Validate if
// the designated constraints aren't met.
type ButtonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ButtonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ButtonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ButtonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ButtonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ButtonValidationError) ErrorName() string { return "ButtonValidationError" }

// Error satisfies the builtin error interface
func (e ButtonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sButton.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ButtonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ButtonValidationError{}

// Validate checks the field values on ConnectUANWidget with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ConnectUANWidget) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectUANWidget with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConnectUANWidgetMultiError, or nil if none found.
func (m *ConnectUANWidget) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectUANWidget) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectUANWidgetValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUanNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "UanNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "UanNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUanNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectUANWidgetValidationError{
				field:  "UanNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatusTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "StatusTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "StatusTag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatusTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectUANWidgetValidationError{
				field:  "StatusTag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConnectInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "ConnectInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "ConnectInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectUANWidgetValidationError{
				field:  "ConnectInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectUANWidgetValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectUANWidgetValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConnectUANWidgetMultiError(errors)
	}

	return nil
}

// ConnectUANWidgetMultiError is an error wrapping multiple validation errors
// returned by ConnectUANWidget.ValidateAll() if the designated constraints
// aren't met.
type ConnectUANWidgetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectUANWidgetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectUANWidgetMultiError) AllErrors() []error { return m }

// ConnectUANWidgetValidationError is the validation error returned by
// ConnectUANWidget.Validate if the designated constraints aren't met.
type ConnectUANWidgetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectUANWidgetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectUANWidgetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectUANWidgetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectUANWidgetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectUANWidgetValidationError) ErrorName() string { return "ConnectUANWidgetValidationError" }

// Error satisfies the builtin error interface
func (e ConnectUANWidgetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectUANWidget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectUANWidgetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectUANWidgetValidationError{}

// Validate checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpRequestMultiError, or nil if none found.
func (m *GenerateOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetUanNumber()) < 1 {
		err := GenerateOtpRequestValidationError{
			field:  "UanNumber",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetClientReqId()); l < 1 || l > 50 {
		err := GenerateOtpRequestValidationError{
			field:  "ClientReqId",
			reason: "value length must be between 1 and 50 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsConsentTaken

	// no validation rules for EpfImportSessionId

	if len(errors) > 0 {
		return GenerateOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateOtpRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpRequestMultiError) AllErrors() []error { return m }

// GenerateOtpRequestValidationError is the validation error returned by
// GenerateOtpRequest.Validate if the designated constraints aren't met.
type GenerateOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpRequestValidationError) ErrorName() string {
	return "GenerateOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpRequestValidationError{}

// Validate checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpResponseMultiError, or nil if none found.
func (m *GenerateOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpResponseValidationError{
				field:  "InfoText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFailureFooterText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "FailureFooterText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "FailureFooterText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFailureFooterText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpResponseValidationError{
				field:  "FailureFooterText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpResponseMultiError) AllErrors() []error { return m }

// GenerateOtpResponseValidationError is the validation error returned by
// GenerateOtpResponse.Validate if the designated constraints aren't met.
type GenerateOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpResponseValidationError) ErrorName() string {
	return "GenerateOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpResponseValidationError{}

// Validate checks the field values on VerifyOtpRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpRequestMultiError, or nil if none found.
func (m *VerifyOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Otp

	// no validation rules for ClientReqId

	// no validation rules for EpfImportSessionId

	if len(errors) > 0 {
		return VerifyOtpRequestMultiError(errors)
	}

	return nil
}

// VerifyOtpRequestMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpRequestMultiError) AllErrors() []error { return m }

// VerifyOtpRequestValidationError is the validation error returned by
// VerifyOtpRequest.Validate if the designated constraints aren't met.
type VerifyOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpRequestValidationError) ErrorName() string { return "VerifyOtpRequestValidationError" }

// Error satisfies the builtin error interface
func (e VerifyOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpRequestValidationError{}

// Validate checks the field values on VerifyOtpResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpResponseMultiError, or nil if none found.
func (m *VerifyOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedirectionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "RedirectionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyOtpResponseMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpResponse.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpResponseMultiError) AllErrors() []error { return m }

// VerifyOtpResponseValidationError is the validation error returned by
// VerifyOtpResponse.Validate if the designated constraints aren't met.
type VerifyOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpResponseValidationError) ErrorName() string {
	return "VerifyOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpResponseValidationError{}

// Validate checks the field values on InitiateEpfImportSessionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateEpfImportSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateEpfImportSessionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InitiateEpfImportSessionRequestMultiError, or nil if none found.
func (m *InitiateEpfImportSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateEpfImportSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateEpfImportSessionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateEpfImportSessionRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateEpfImportSessionRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateEpfImportSessionRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateEpfImportSessionRequestValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateEpfImportSessionRequestValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateEpfImportSessionRequestMultiError(errors)
	}

	return nil
}

// InitiateEpfImportSessionRequestMultiError is an error wrapping multiple
// validation errors returned by InitiateEpfImportSessionRequest.ValidateAll()
// if the designated constraints aren't met.
type InitiateEpfImportSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateEpfImportSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateEpfImportSessionRequestMultiError) AllErrors() []error { return m }

// InitiateEpfImportSessionRequestValidationError is the validation error
// returned by InitiateEpfImportSessionRequest.Validate if the designated
// constraints aren't met.
type InitiateEpfImportSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateEpfImportSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateEpfImportSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateEpfImportSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateEpfImportSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateEpfImportSessionRequestValidationError) ErrorName() string {
	return "InitiateEpfImportSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateEpfImportSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateEpfImportSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateEpfImportSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateEpfImportSessionRequestValidationError{}

// Validate checks the field values on InitiateEpfImportSessionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InitiateEpfImportSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateEpfImportSessionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InitiateEpfImportSessionResponseMultiError, or nil if none found.
func (m *InitiateEpfImportSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateEpfImportSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateEpfImportSessionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateEpfImportSessionResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateEpfImportSessionResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedirectionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateEpfImportSessionResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateEpfImportSessionResponseValidationError{
					field:  "RedirectionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateEpfImportSessionResponseValidationError{
				field:  "RedirectionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InitiateEpfImportSessionResponseMultiError(errors)
	}

	return nil
}

// InitiateEpfImportSessionResponseMultiError is an error wrapping multiple
// validation errors returned by
// InitiateEpfImportSessionResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateEpfImportSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateEpfImportSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateEpfImportSessionResponseMultiError) AllErrors() []error { return m }

// InitiateEpfImportSessionResponseValidationError is the validation error
// returned by InitiateEpfImportSessionResponse.Validate if the designated
// constraints aren't met.
type InitiateEpfImportSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateEpfImportSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateEpfImportSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateEpfImportSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateEpfImportSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateEpfImportSessionResponseValidationError) ErrorName() string {
	return "InitiateEpfImportSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateEpfImportSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateEpfImportSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateEpfImportSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateEpfImportSessionResponseValidationError{}
