// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package queue;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/be-common/api/queue";
option java_package = "com.github.epifi.be-common.api.queue";

// Message representing information regarding the state of queue retry
message QueueRetryInfo {

  // unique id of queue msg for retry debugging
  string queue_msg_id = 1;

  // epifi generated id, for vendor bank's async calls
  string req_id = 2;

  // number of attempts that has been made to process a queue message
  int32 attempts = 3;

  // timestamp of the first attempt to process a queue message.
  // some retry strategies may decide next retry time based on this parameter.
  google.protobuf.Timestamp first_attempt = 4;
}
