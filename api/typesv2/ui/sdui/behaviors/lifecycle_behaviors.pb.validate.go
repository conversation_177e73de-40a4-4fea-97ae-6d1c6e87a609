// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/ui/sdui/behaviors/lifecycle_behaviors.proto

package behaviors

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LifecycleBehavior with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LifecycleBehavior) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LifecycleBehavior with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LifecycleBehaviorMultiError, or nil if none found.
func (m *LifecycleBehavior) ValidateAll() error {
	return m.validate(true)
}

func (m *LifecycleBehavior) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAnalyticsEvent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LifecycleBehaviorValidationError{
					field:  "AnalyticsEvent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LifecycleBehaviorValidationError{
					field:  "AnalyticsEvent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalyticsEvent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LifecycleBehaviorValidationError{
				field:  "AnalyticsEvent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Behavior.(type) {
	case *LifecycleBehavior_LoadBehavior:
		if v == nil {
			err := LifecycleBehaviorValidationError{
				field:  "Behavior",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoadBehavior()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LifecycleBehaviorValidationError{
						field:  "LoadBehavior",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LifecycleBehaviorValidationError{
						field:  "LoadBehavior",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoadBehavior()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LifecycleBehaviorValidationError{
					field:  "LoadBehavior",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LifecycleBehavior_ViewedBehavior:
		if v == nil {
			err := LifecycleBehaviorValidationError{
				field:  "Behavior",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetViewedBehavior()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LifecycleBehaviorValidationError{
						field:  "ViewedBehavior",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LifecycleBehaviorValidationError{
						field:  "ViewedBehavior",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetViewedBehavior()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LifecycleBehaviorValidationError{
					field:  "ViewedBehavior",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LifecycleBehavior_HiddenBehavior:
		if v == nil {
			err := LifecycleBehaviorValidationError{
				field:  "Behavior",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHiddenBehavior()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LifecycleBehaviorValidationError{
						field:  "HiddenBehavior",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LifecycleBehaviorValidationError{
						field:  "HiddenBehavior",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHiddenBehavior()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LifecycleBehaviorValidationError{
					field:  "HiddenBehavior",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LifecycleBehaviorMultiError(errors)
	}

	return nil
}

// LifecycleBehaviorMultiError is an error wrapping multiple validation errors
// returned by LifecycleBehavior.ValidateAll() if the designated constraints
// aren't met.
type LifecycleBehaviorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LifecycleBehaviorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LifecycleBehaviorMultiError) AllErrors() []error { return m }

// LifecycleBehaviorValidationError is the validation error returned by
// LifecycleBehavior.Validate if the designated constraints aren't met.
type LifecycleBehaviorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LifecycleBehaviorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LifecycleBehaviorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LifecycleBehaviorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LifecycleBehaviorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LifecycleBehaviorValidationError) ErrorName() string {
	return "LifecycleBehaviorValidationError"
}

// Error satisfies the builtin error interface
func (e LifecycleBehaviorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLifecycleBehavior.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LifecycleBehaviorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LifecycleBehaviorValidationError{}

// Validate checks the field values on OnLoadBehavior with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OnLoadBehavior) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnLoadBehavior with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OnLoadBehaviorMultiError,
// or nil if none found.
func (m *OnLoadBehavior) ValidateAll() error {
	return m.validate(true)
}

func (m *OnLoadBehavior) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return OnLoadBehaviorMultiError(errors)
	}

	return nil
}

// OnLoadBehaviorMultiError is an error wrapping multiple validation errors
// returned by OnLoadBehavior.ValidateAll() if the designated constraints
// aren't met.
type OnLoadBehaviorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnLoadBehaviorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnLoadBehaviorMultiError) AllErrors() []error { return m }

// OnLoadBehaviorValidationError is the validation error returned by
// OnLoadBehavior.Validate if the designated constraints aren't met.
type OnLoadBehaviorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnLoadBehaviorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnLoadBehaviorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnLoadBehaviorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnLoadBehaviorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnLoadBehaviorValidationError) ErrorName() string { return "OnLoadBehaviorValidationError" }

// Error satisfies the builtin error interface
func (e OnLoadBehaviorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnLoadBehavior.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnLoadBehaviorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnLoadBehaviorValidationError{}

// Validate checks the field values on OnViewedBehavior with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnViewedBehavior) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnViewedBehavior with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnViewedBehaviorMultiError, or nil if none found.
func (m *OnViewedBehavior) ValidateAll() error {
	return m.validate(true)
}

func (m *OnViewedBehavior) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return OnViewedBehaviorMultiError(errors)
	}

	return nil
}

// OnViewedBehaviorMultiError is an error wrapping multiple validation errors
// returned by OnViewedBehavior.ValidateAll() if the designated constraints
// aren't met.
type OnViewedBehaviorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnViewedBehaviorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnViewedBehaviorMultiError) AllErrors() []error { return m }

// OnViewedBehaviorValidationError is the validation error returned by
// OnViewedBehavior.Validate if the designated constraints aren't met.
type OnViewedBehaviorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnViewedBehaviorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnViewedBehaviorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnViewedBehaviorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnViewedBehaviorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnViewedBehaviorValidationError) ErrorName() string { return "OnViewedBehaviorValidationError" }

// Error satisfies the builtin error interface
func (e OnViewedBehaviorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnViewedBehavior.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnViewedBehaviorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnViewedBehaviorValidationError{}

// Validate checks the field values on OnHiddenBehavior with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnHiddenBehavior) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnHiddenBehavior with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnHiddenBehaviorMultiError, or nil if none found.
func (m *OnHiddenBehavior) ValidateAll() error {
	return m.validate(true)
}

func (m *OnHiddenBehavior) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return OnHiddenBehaviorMultiError(errors)
	}

	return nil
}

// OnHiddenBehaviorMultiError is an error wrapping multiple validation errors
// returned by OnHiddenBehavior.ValidateAll() if the designated constraints
// aren't met.
type OnHiddenBehaviorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnHiddenBehaviorMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnHiddenBehaviorMultiError) AllErrors() []error { return m }

// OnHiddenBehaviorValidationError is the validation error returned by
// OnHiddenBehavior.Validate if the designated constraints aren't met.
type OnHiddenBehaviorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnHiddenBehaviorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnHiddenBehaviorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnHiddenBehaviorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnHiddenBehaviorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnHiddenBehaviorValidationError) ErrorName() string { return "OnHiddenBehaviorValidationError" }

// Error satisfies the builtin error interface
func (e OnHiddenBehaviorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnHiddenBehavior.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnHiddenBehaviorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnHiddenBehaviorValidationError{}
