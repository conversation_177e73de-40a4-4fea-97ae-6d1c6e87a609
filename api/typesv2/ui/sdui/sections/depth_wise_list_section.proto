syntax = "proto3";

package api.typesv2.ui.sdui.sections;

import "api/typesv2/ui/sdui/behaviors/lifecycle_behaviors.proto";
import "api/typesv2/ui/sdui/components/component.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";
import "api/typesv2/ui/sdui/behaviors/interaction_behaviors.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/sections";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.sections";

// Figma screen link: https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=6499-49605&mode=design&t=p2Wx6HkA94C0gIbg-4
// Structure link: https://drive.google.com/file/d/1GMFVTia4wbNRJy8lAbkuhAf5d7axYgx3/view?usp=sharing
// Usage example link 1:  https://drive.google.com/file/d/1OSsSx9rB_ItTs8PmbSN_K_FC8z2K0hUf/view?usp=sharing,
// Usage example link 2:  https://drive.google.com/file/d/1zvGExmzncwEO3bN5G7pddCVjw0Ign0mY/view?usp=sharing
// DepthWiseListSection is a section that renders a list of components in a depthwise manner.
// This section internally maps to a ZStack view in iOS and a Box compose view in Android.
message DepthWiseListSection {
  // Whether the list is scrollable or not
  bool is_scrollable = 1;
  // The components to be rendered in the list
  repeated components.Component components = 2;
  // The visual properties of the list, like background color, etc.
  repeated properties.VisualProperty visual_properties = 3;
  // Depth wise alignment of this list section.
  DepthWiseAlignment alignment = 4;
  // Click behavior of this list section.
  repeated behaviors.InteractionBehavior interaction_behaviors = 5;
  // (Optional) Behaviour (and optional Analytics metadata) to be used by clients when this section loads on the screen
  // (may or may not be visible)
  behaviors.LifecycleBehavior load_behavior = 6;
  // (Optional) Behaviours (and optional Analytics metadata) to be used by clients when this section is visible on the
  // client device's screen
  behaviors.LifecycleBehavior visible_behavior = 7;
  // DepthWiseAlignment is the alignment of the element in the depth wise list section.
  enum DepthWiseAlignment {
    UNSPECIFIED = 0;
    TOP_LEFT = 1;
    TOP_CENTER = 2;
    TOP_RIGHT = 3;
    CENTER_LEFT = 4;
    CENTER_CENTER = 5;
    CENTER_RIGHT = 6;
    BOTTOM_LEFT = 7;
    BOTTOM_CENTER = 8;
    BOTTOM_RIGHT = 9;
  }
}
