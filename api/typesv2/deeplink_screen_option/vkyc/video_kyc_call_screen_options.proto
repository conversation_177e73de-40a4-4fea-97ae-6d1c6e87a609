syntax = "proto3";

package api.typesv2.deeplink_screen_option.vkyc;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/visual_element.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.vkyc";
// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message VideoKycCallScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Image/Lottie to be shown on screen
  message LoaderInfo {
    typesv2.common.VisualElement image = 1;
    typesv2.common.Text title = 2;
    typesv2.common.Text subtitle = 3;
    int32 expected_wait_time_in_sec = 4;
  }
  LoaderInfo loader_info = 2;
  // ApplicationId is used to fetch applicant details
  string application_id = 3;
  // CallId is used to check the status of call
  string call_id = 4;
  // ExitDeeplink to redirect user once call is completed
  frontend.deeplink.Deeplink exit_deeplink = 5;
  // enum map to api/kyc/vkyc/internal/vkyc_karza_call_info.proto entry point
  string entry_point = 6;
  frontend.deeplink.BackAction back_action = 7;
}
