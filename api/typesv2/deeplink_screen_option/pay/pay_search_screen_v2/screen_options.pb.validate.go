// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/pay/pay_search_screen_v2/screen_options.proto

package pay_search_screen_v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PaySearchV2ScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PaySearchV2ScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaySearchV2ScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PaySearchV2ScreenOptionsMultiError, or nil if none found.
func (m *PaySearchV2ScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *PaySearchV2ScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaySearchV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaySearchV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaySearchV2ScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaySearchScreenType

	// no validation rules for FocusedSection

	if len(errors) > 0 {
		return PaySearchV2ScreenOptionsMultiError(errors)
	}

	return nil
}

// PaySearchV2ScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by PaySearchV2ScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type PaySearchV2ScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaySearchV2ScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaySearchV2ScreenOptionsMultiError) AllErrors() []error { return m }

// PaySearchV2ScreenOptionsValidationError is the validation error returned by
// PaySearchV2ScreenOptions.Validate if the designated constraints aren't met.
type PaySearchV2ScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaySearchV2ScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaySearchV2ScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaySearchV2ScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaySearchV2ScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaySearchV2ScreenOptionsValidationError) ErrorName() string {
	return "PaySearchV2ScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e PaySearchV2ScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaySearchV2ScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaySearchV2ScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaySearchV2ScreenOptionsValidationError{}
