// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/pay/upi_mapper/screen_options.proto

package upi_mapper

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpiNumberLinkingSuccessScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpiNumberLinkingSuccessScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpiNumberLinkingSuccessScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpiNumberLinkingSuccessScreenOptionsMultiError, or nil if none found.
func (m *UpiNumberLinkingSuccessScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberLinkingSuccessScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptionsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCloseIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloseIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptionsValidationError{
				field:  "CloseIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptionsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLinkingDetailsCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "LinkingDetailsCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "LinkingDetailsCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLinkingDetailsCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptionsValidationError{
				field:  "LinkingDetailsCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrimaryCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrimaryCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptionsValidationError{
				field:  "PrimaryCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberLinkingSuccessScreenOptionsMultiError(errors)
	}

	return nil
}

// UpiNumberLinkingSuccessScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// UpiNumberLinkingSuccessScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type UpiNumberLinkingSuccessScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberLinkingSuccessScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberLinkingSuccessScreenOptionsMultiError) AllErrors() []error { return m }

// UpiNumberLinkingSuccessScreenOptionsValidationError is the validation error
// returned by UpiNumberLinkingSuccessScreenOptions.Validate if the designated
// constraints aren't met.
type UpiNumberLinkingSuccessScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberLinkingSuccessScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpiNumberLinkingSuccessScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpiNumberLinkingSuccessScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpiNumberLinkingSuccessScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiNumberLinkingSuccessScreenOptionsValidationError) ErrorName() string {
	return "UpiNumberLinkingSuccessScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberLinkingSuccessScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberLinkingSuccessScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberLinkingSuccessScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberLinkingSuccessScreenOptionsValidationError{}

// Validate checks the field values on
// UpiNumberAccountSelectionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpiNumberAccountSelectionBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpiNumberAccountSelectionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// UpiNumberAccountSelectionBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *UpiNumberAccountSelectionBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberAccountSelectionBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCloseIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "CloseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloseIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
				field:  "CloseIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "Image",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
				field:  "Image",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPrimaryCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrimaryCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{
				field:  "PrimaryCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberAccountSelectionBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// UpiNumberAccountSelectionBottomSheetScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// UpiNumberAccountSelectionBottomSheetScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type UpiNumberAccountSelectionBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberAccountSelectionBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberAccountSelectionBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError is the
// validation error returned by
// UpiNumberAccountSelectionBottomSheetScreenOptions.Validate if the
// designated constraints aren't met.
type UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberAccountSelectionBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberAccountSelectionBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardMultiError, or nil
// if none found.
func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBankInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
					field:  "BankInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
					field:  "BankInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
				field:  "BankInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUpiInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
						field:  fmt.Sprintf("UpiInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
						field:  fmt.Sprintf("UpiInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
					field:  fmt.Sprintf("UpiInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
					field:  "ContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{
				field:  "ContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardMultiError(errors)
	}

	return nil
}

// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardMultiError is an
// error wrapping multiple validation errors returned by
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard.ValidateAll() if
// the designated constraints aren't met.
type UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardMultiError) AllErrors() []error {
	return m
}

// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError is
// the validation error returned by
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard.Validate if the
// designated constraints aren't met.
type UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError) ErrorName() string {
	return "UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCardValidationError{}

// Validate checks the field values on
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoMultiError,
// or nil if none found.
func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBankIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
					field:  "BankIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
					field:  "BankIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
				field:  "BankIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBankAccountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
					field:  "BankAccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
					field:  "BankAccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
				field:  "BankAccountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoMultiError(errors)
	}

	return nil
}

// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoMultiError
// is an error wrapping multiple validation errors returned by
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo.ValidateAll()
// if the designated constraints aren't met.
type UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoMultiError) AllErrors() []error {
	return m
}

// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError
// is the validation error returned by
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo.Validate
// if the designated constraints aren't met.
type UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError) ErrorName() string {
	return "UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_BankInfoValidationError{}

// Validate checks the field values on
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoMultiError,
// or nil if none found.
func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoMultiError(errors)
	}

	return nil
}

// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoMultiError is
// an error wrapping multiple validation errors returned by
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo.ValidateAll()
// if the designated constraints aren't met.
type UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoMultiError) AllErrors() []error {
	return m
}

// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError
// is the validation error returned by
// UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo.Validate if
// the designated constraints aren't met.
type UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError) ErrorName() string {
	return "UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberLinkingSuccessScreenOptions_LinkingDetailsCard_UpiInfoValidationError{}

// Validate checks the field values on
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpiNumberAccountSelectionBottomSheetScreenOptions_Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpiNumberAccountSelectionBottomSheetScreenOptions_AccountMultiError, or nil
// if none found.
func (m *UpiNumberAccountSelectionBottomSheetScreenOptions_Account) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberAccountSelectionBottomSheetScreenOptions_Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DerivedAccountId

	// no validation rules for Vpa

	if all {
		switch v := interface{}(m.GetDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError{
				field:  "DisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberAccountSelectionBottomSheetScreenOptions_AccountMultiError(errors)
	}

	return nil
}

// UpiNumberAccountSelectionBottomSheetScreenOptions_AccountMultiError is an
// error wrapping multiple validation errors returned by
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account.ValidateAll() if
// the designated constraints aren't met.
type UpiNumberAccountSelectionBottomSheetScreenOptions_AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberAccountSelectionBottomSheetScreenOptions_AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberAccountSelectionBottomSheetScreenOptions_AccountMultiError) AllErrors() []error {
	return m
}

// UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError is
// the validation error returned by
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account.Validate if the
// designated constraints aren't met.
type UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError) ErrorName() string {
	return "UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberAccountSelectionBottomSheetScreenOptions_Account.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberAccountSelectionBottomSheetScreenOptions_AccountValidationError{}

// Validate checks the field values on
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoMultiError,
// or nil if none found.
func (m *UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBankIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
					field:  "BankIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
					field:  "BankIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
				field:  "BankIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBankAccountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
					field:  "BankAccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
					field:  "BankAccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
				field:  "BankAccountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoMultiError(errors)
	}

	return nil
}

// UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoMultiError
// is an error wrapping multiple validation errors returned by
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo.ValidateAll()
// if the designated constraints aren't met.
type UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoMultiError) AllErrors() []error {
	return m
}

// UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError
// is the validation error returned by
// UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo.Validate
// if the designated constraints aren't met.
type UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError) ErrorName() string {
	return "UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpiNumberAccountSelectionBottomSheetScreenOptions_Account_DisplayInfoValidationError{}
