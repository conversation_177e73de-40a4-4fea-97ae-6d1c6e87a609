syntax = "proto3";

package api.typesv2.deeplink_screen_option.usstocks.metadata;

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/metadata";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.usstocks.metadata";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// USStocksScreenMeta propagates generic info in a us stocks screen
message USStocksScreenMetadata {
  // optional id to get drop off bottom sheet using api/frontend/usstocks/service.proto GetDropOffBottomSheet rpc
  // client should not cast this string to any enum to avoid coupling it with specific typesv2
  string drop_off_bottom_sheet_id = 1;
}
