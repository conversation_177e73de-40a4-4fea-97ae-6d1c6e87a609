// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/pkg/rpc_based_redirection_screen_options.proto

package pkg

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RpcBasedRedirectionScreenOption with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RpcBasedRedirectionScreenOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RpcBasedRedirectionScreenOption with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RpcBasedRedirectionScreenOptionMultiError, or nil if none found.
func (m *RpcBasedRedirectionScreenOption) ValidateAll() error {
	return m.validate(true)
}

func (m *RpcBasedRedirectionScreenOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RpcBasedRedirectionScreenOptionValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RpcToCall

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RpcBasedRedirectionScreenOptionValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RpcBasedRedirectionScreenOptionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RpcBasedRedirectionScreenOptionValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Payload.(type) {
	case *RpcBasedRedirectionScreenOption_ResetOnboardingStageRequest:
		if v == nil {
			err := RpcBasedRedirectionScreenOptionValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResetOnboardingStageRequest()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
						field:  "ResetOnboardingStageRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RpcBasedRedirectionScreenOptionValidationError{
						field:  "ResetOnboardingStageRequest",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResetOnboardingStageRequest()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RpcBasedRedirectionScreenOptionValidationError{
					field:  "ResetOnboardingStageRequest",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RpcBasedRedirectionScreenOptionMultiError(errors)
	}

	return nil
}

// RpcBasedRedirectionScreenOptionMultiError is an error wrapping multiple
// validation errors returned by RpcBasedRedirectionScreenOption.ValidateAll()
// if the designated constraints aren't met.
type RpcBasedRedirectionScreenOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RpcBasedRedirectionScreenOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RpcBasedRedirectionScreenOptionMultiError) AllErrors() []error { return m }

// RpcBasedRedirectionScreenOptionValidationError is the validation error
// returned by RpcBasedRedirectionScreenOption.Validate if the designated
// constraints aren't met.
type RpcBasedRedirectionScreenOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RpcBasedRedirectionScreenOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RpcBasedRedirectionScreenOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RpcBasedRedirectionScreenOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RpcBasedRedirectionScreenOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RpcBasedRedirectionScreenOptionValidationError) ErrorName() string {
	return "RpcBasedRedirectionScreenOptionValidationError"
}

// Error satisfies the builtin error interface
func (e RpcBasedRedirectionScreenOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRpcBasedRedirectionScreenOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RpcBasedRedirectionScreenOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RpcBasedRedirectionScreenOptionValidationError{}
