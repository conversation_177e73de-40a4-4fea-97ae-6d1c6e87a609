syntax = "proto3";

package api.typesv2.deeplink_screen_option.sa_closure;

import "api/frontend/account/enums/sa_closure.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/info/info_view.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/swipe_button.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/ui/key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/sa_closure";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.sa_closure";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

//  https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=233-8188&mode=design&t=d6fSyshwKwwbCWJ8-4
message SaClosureUserFeedbackScreenOptions {
  ScreenOptionHeader header = 1;
  common.Text page_title = 2;
  // feedback option the user can select
  repeated UserFeedbackOption user_feedback_options = 3;
  // cta to submit the feedback
  frontend.deeplink.Cta cta = 4;
}

message UserFeedbackOption {
  // text for the feedback option
  common.Text text = 1;
  // Optional: deeplink to take user when clicked on feedback option
  frontend.deeplink.Deeplink action = 2;
  // flag to load text box for custom feedback
  bool is_custom_feedback = 3;
  // Optional: placeholder field to show in text box for getting custom feedback from user
  // applicable when IsCustomFeedback is true
  string custom_feedback_placeholder = 4;
}

// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=218-3163&mode=design&t=dL7LJ7PujX6uecF3-4
message PanDobInputScreenOptions {
  ScreenOptionHeader header = 1;
  common.Text heading = 2;
  common.Text subheading = 3;
  string pan_number_input_placeholder = 4;
  string dob_input_placeholder = 5;
  frontend.deeplink.Cta submit_cta = 6;
  string background_color = 7;
}

// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=576-20238&mode=design&t=dL7LJ7PujX6uecF3-4
message SaClosureSubmitRequestSwipeActionScreenOptions {
  ScreenOptionHeader header = 1;
  // element for Image, Title and subtitle to be shown in the Swipe action screen
  common.ui.widget.VisualElementTitleSubtitleElement image_title_subtitle = 2;
  ui.SwipeButton swipe_button = 3;
  frontend.deeplink.Cta cancel_cta = 4;
}

message SaClosureRequestSubmittedScreenOptions {
  ScreenOptionHeader header = 1;
  info.FullScreenInfoView full_screen_info_view = 2;
  // counter to show the number of days remaining before cancelling colosure request
  ItcTitleSubtitleElement counter_element = 3;
}

message ItcTitleSubtitleElement {
  ui.IconTextComponent counter = 1;
  typesv2.common.Text title = 2;
  typesv2.common.Text subtitle = 3;
  string background_color = 4;
}

message SaClosureResolveIssuesScreenOptions {
  ScreenOptionHeader header = 1;
  info.FullScreenInfoView full_screen_info_view = 2;
  // feedback selected by the user
  string user_feedback_text = 3;
  // additional info to be shown to the user
  typesv2.ui.KeyValuePair additional_info = 4;
  // text to be shown on top of cta
  typesv2.common.Text bottom_text = 5;
}

message SaClosureBenefitScreenOptions {
  ScreenOptionHeader header = 1;
  frontend.account.enums.SAClosureRequestEntryPoint entry_point = 2;
}
