// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/insights/networth_refresh/net_worth_refresh.proto

package networth_refresh

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on NetWorthGetNextActionRequestParams with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *NetWorthGetNextActionRequestParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetWorthGetNextActionRequestParams
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// NetWorthGetNextActionRequestParamsMultiError, or nil if none found.
func (m *NetWorthGetNextActionRequestParams) ValidateAll() error {
	return m.validate(true)
}

func (m *NetWorthGetNextActionRequestParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NetWorthRefreshId

	// no validation rules for Provenance

	for idx, item := range m.GetAssetRefreshInfoV2() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetWorthGetNextActionRequestParamsValidationError{
						field:  fmt.Sprintf("AssetRefreshInfoV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetWorthGetNextActionRequestParamsValidationError{
						field:  fmt.Sprintf("AssetRefreshInfoV2[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetWorthGetNextActionRequestParamsValidationError{
					field:  fmt.Sprintf("AssetRefreshInfoV2[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NetWorthGetNextActionRequestParamsMultiError(errors)
	}

	return nil
}

// NetWorthGetNextActionRequestParamsMultiError is an error wrapping multiple
// validation errors returned by
// NetWorthGetNextActionRequestParams.ValidateAll() if the designated
// constraints aren't met.
type NetWorthGetNextActionRequestParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetWorthGetNextActionRequestParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetWorthGetNextActionRequestParamsMultiError) AllErrors() []error { return m }

// NetWorthGetNextActionRequestParamsValidationError is the validation error
// returned by NetWorthGetNextActionRequestParams.Validate if the designated
// constraints aren't met.
type NetWorthGetNextActionRequestParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetWorthGetNextActionRequestParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetWorthGetNextActionRequestParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetWorthGetNextActionRequestParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetWorthGetNextActionRequestParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetWorthGetNextActionRequestParamsValidationError) ErrorName() string {
	return "NetWorthGetNextActionRequestParamsValidationError"
}

// Error satisfies the builtin error interface
func (e NetWorthGetNextActionRequestParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetWorthGetNextActionRequestParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetWorthGetNextActionRequestParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetWorthGetNextActionRequestParamsValidationError{}

// Validate checks the field values on NetWorthRefreshHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetWorthRefreshHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetWorthRefreshHeader with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetWorthRefreshHeaderMultiError, or nil if none found.
func (m *NetWorthRefreshHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *NetWorthRefreshHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProgressDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetWorthRefreshHeaderValidationError{
					field:  "ProgressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetWorthRefreshHeaderValidationError{
					field:  "ProgressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProgressDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetWorthRefreshHeaderValidationError{
				field:  "ProgressDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetAssetDisplayInfo()))
		i := 0
		for key := range m.GetAssetDisplayInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAssetDisplayInfo()[key]
			_ = val

			// no validation rules for AssetDisplayInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, NetWorthRefreshHeaderValidationError{
							field:  fmt.Sprintf("AssetDisplayInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, NetWorthRefreshHeaderValidationError{
							field:  fmt.Sprintf("AssetDisplayInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return NetWorthRefreshHeaderValidationError{
						field:  fmt.Sprintf("AssetDisplayInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for CurrentAssetName

	// no validation rules for NetWorthRefreshId

	if len(errors) > 0 {
		return NetWorthRefreshHeaderMultiError(errors)
	}

	return nil
}

// NetWorthRefreshHeaderMultiError is an error wrapping multiple validation
// errors returned by NetWorthRefreshHeader.ValidateAll() if the designated
// constraints aren't met.
type NetWorthRefreshHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetWorthRefreshHeaderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetWorthRefreshHeaderMultiError) AllErrors() []error { return m }

// NetWorthRefreshHeaderValidationError is the validation error returned by
// NetWorthRefreshHeader.Validate if the designated constraints aren't met.
type NetWorthRefreshHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetWorthRefreshHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetWorthRefreshHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetWorthRefreshHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetWorthRefreshHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetWorthRefreshHeaderValidationError) ErrorName() string {
	return "NetWorthRefreshHeaderValidationError"
}

// Error satisfies the builtin error interface
func (e NetWorthRefreshHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetWorthRefreshHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetWorthRefreshHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetWorthRefreshHeaderValidationError{}

// Validate checks the field values on RefreshAssetProgressDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefreshAssetProgressDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshAssetProgressDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshAssetProgressDetailsMultiError, or nil if none found.
func (m *RefreshAssetProgressDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshAssetProgressDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalAssetsToRefresh

	// no validation rules for AssetsRefreshed

	if len(errors) > 0 {
		return RefreshAssetProgressDetailsMultiError(errors)
	}

	return nil
}

// RefreshAssetProgressDetailsMultiError is an error wrapping multiple
// validation errors returned by RefreshAssetProgressDetails.ValidateAll() if
// the designated constraints aren't met.
type RefreshAssetProgressDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshAssetProgressDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshAssetProgressDetailsMultiError) AllErrors() []error { return m }

// RefreshAssetProgressDetailsValidationError is the validation error returned
// by RefreshAssetProgressDetails.Validate if the designated constraints
// aren't met.
type RefreshAssetProgressDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshAssetProgressDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshAssetProgressDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshAssetProgressDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshAssetProgressDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshAssetProgressDetailsValidationError) ErrorName() string {
	return "RefreshAssetProgressDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshAssetProgressDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshAssetProgressDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshAssetProgressDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshAssetProgressDetailsValidationError{}

// Validate checks the field values on RefreshAssetDisplayInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefreshAssetDisplayInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshAssetDisplayInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshAssetDisplayInfoMultiError, or nil if none found.
func (m *RefreshAssetDisplayInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshAssetDisplayInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshAssetDisplayInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshAssetDisplayInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshAssetDisplayInfoValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSkipDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshAssetDisplayInfoValidationError{
					field:  "SkipDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshAssetDisplayInfoValidationError{
					field:  "SkipDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkipDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshAssetDisplayInfoValidationError{
				field:  "SkipDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefreshAssetDisplayInfoMultiError(errors)
	}

	return nil
}

// RefreshAssetDisplayInfoMultiError is an error wrapping multiple validation
// errors returned by RefreshAssetDisplayInfo.ValidateAll() if the designated
// constraints aren't met.
type RefreshAssetDisplayInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshAssetDisplayInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshAssetDisplayInfoMultiError) AllErrors() []error { return m }

// RefreshAssetDisplayInfoValidationError is the validation error returned by
// RefreshAssetDisplayInfo.Validate if the designated constraints aren't met.
type RefreshAssetDisplayInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshAssetDisplayInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshAssetDisplayInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshAssetDisplayInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshAssetDisplayInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshAssetDisplayInfoValidationError) ErrorName() string {
	return "RefreshAssetDisplayInfoValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshAssetDisplayInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshAssetDisplayInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshAssetDisplayInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshAssetDisplayInfoValidationError{}

// Validate checks the field values on AssetRefreshInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AssetRefreshInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AssetRefreshInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AssetRefreshInfoMultiError, or nil if none found.
func (m *AssetRefreshInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AssetRefreshInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetName

	// no validation rules for RefreshId

	// no validation rules for RefreshStatus

	if len(errors) > 0 {
		return AssetRefreshInfoMultiError(errors)
	}

	return nil
}

// AssetRefreshInfoMultiError is an error wrapping multiple validation errors
// returned by AssetRefreshInfo.ValidateAll() if the designated constraints
// aren't met.
type AssetRefreshInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AssetRefreshInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AssetRefreshInfoMultiError) AllErrors() []error { return m }

// AssetRefreshInfoValidationError is the validation error returned by
// AssetRefreshInfo.Validate if the designated constraints aren't met.
type AssetRefreshInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AssetRefreshInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AssetRefreshInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AssetRefreshInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AssetRefreshInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AssetRefreshInfoValidationError) ErrorName() string { return "AssetRefreshInfoValidationError" }

// Error satisfies the builtin error interface
func (e AssetRefreshInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAssetRefreshInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AssetRefreshInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AssetRefreshInfoValidationError{}
