// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/form/map.proto

package form

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Map with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Map) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Map with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MapMultiError, or nil if none found.
func (m *Map) ValidateAll() error {
	return m.validate(true)
}

func (m *Map) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetMap()))
		i := 0
		for key := range m.GetMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMap()[key]
			_ = val

			// no validation rules for Map[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, MapValidationError{
							field:  fmt.Sprintf("Map[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, MapValidationError{
							field:  fmt.Sprintf("Map[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return MapValidationError{
						field:  fmt.Sprintf("Map[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return MapMultiError(errors)
	}

	return nil
}

// MapMultiError is an error wrapping multiple validation errors returned by
// Map.ValidateAll() if the designated constraints aren't met.
type MapMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MapMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MapMultiError) AllErrors() []error { return m }

// MapValidationError is the validation error returned by Map.Validate if the
// designated constraints aren't met.
type MapValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MapValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MapValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MapValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MapValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MapValidationError) ErrorName() string { return "MapValidationError" }

// Error satisfies the builtin error interface
func (e MapValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMap.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MapValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MapValidationError{}
