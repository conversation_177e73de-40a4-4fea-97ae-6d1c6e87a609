// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/webui/detail_grid.proto

package webui

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on KeyValueList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KeyValueList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KeyValueList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KeyValueListMultiError, or
// nil if none found.
func (m *KeyValueList) ValidateAll() error {
	return m.validate(true)
}

func (m *KeyValueList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetKeyValueList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KeyValueListValidationError{
						field:  fmt.Sprintf("KeyValueList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KeyValueListValidationError{
						field:  fmt.Sprintf("KeyValueList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KeyValueListValidationError{
					field:  fmt.Sprintf("KeyValueList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return KeyValueListMultiError(errors)
	}

	return nil
}

// KeyValueListMultiError is an error wrapping multiple validation errors
// returned by KeyValueList.ValidateAll() if the designated constraints aren't met.
type KeyValueListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KeyValueListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KeyValueListMultiError) AllErrors() []error { return m }

// KeyValueListValidationError is the validation error returned by
// KeyValueList.Validate if the designated constraints aren't met.
type KeyValueListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KeyValueListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KeyValueListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KeyValueListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KeyValueListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KeyValueListValidationError) ErrorName() string { return "KeyValueListValidationError" }

// Error satisfies the builtin error interface
func (e KeyValueListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKeyValueList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KeyValueListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KeyValueListValidationError{}

// Validate checks the field values on Cell with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Cell) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cell with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CellMultiError, or nil if none found.
func (m *Cell) ValidateAll() error {
	return m.validate(true)
}

func (m *Cell) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetStyle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CellValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CellValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStyle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CellValidationError{
				field:  "Style",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Value.(type) {
	case *Cell_TextValue:
		if v == nil {
			err := CellValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TextValue
	case *Cell_KeyValuePairs:
		if v == nil {
			err := CellValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetKeyValuePairs()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CellValidationError{
						field:  "KeyValuePairs",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CellValidationError{
						field:  "KeyValuePairs",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetKeyValuePairs()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CellValidationError{
					field:  "KeyValuePairs",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CellMultiError(errors)
	}

	return nil
}

// CellMultiError is an error wrapping multiple validation errors returned by
// Cell.ValidateAll() if the designated constraints aren't met.
type CellMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CellMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CellMultiError) AllErrors() []error { return m }

// CellValidationError is the validation error returned by Cell.Validate if the
// designated constraints aren't met.
type CellValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CellValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CellValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CellValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CellValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CellValidationError) ErrorName() string { return "CellValidationError" }

// Error satisfies the builtin error interface
func (e CellValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCell.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CellValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CellValidationError{}

// Validate checks the field values on GridRow with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GridRow) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GridRow with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in GridRowMultiError, or nil if none found.
func (m *GridRow) ValidateAll() error {
	return m.validate(true)
}

func (m *GridRow) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Label

	if all {
		switch v := interface{}(m.GetDetailValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GridRowValidationError{
					field:  "DetailValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GridRowValidationError{
					field:  "DetailValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GridRowValidationError{
				field:  "DetailValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GridRowMultiError(errors)
	}

	return nil
}

// GridRowMultiError is an error wrapping multiple validation errors returned
// by GridRow.ValidateAll() if the designated constraints aren't met.
type GridRowMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GridRowMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GridRowMultiError) AllErrors() []error { return m }

// GridRowValidationError is the validation error returned by GridRow.Validate
// if the designated constraints aren't met.
type GridRowValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GridRowValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GridRowValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GridRowValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GridRowValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GridRowValidationError) ErrorName() string { return "GridRowValidationError" }

// Error satisfies the builtin error interface
func (e GridRowValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGridRow.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GridRowValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GridRowValidationError{}

// Validate checks the field values on DetailView with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DetailView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetailView with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DetailViewMultiError, or
// nil if none found.
func (m *DetailView) ValidateAll() error {
	return m.validate(true)
}

func (m *DetailView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	for idx, item := range m.GetRows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetailViewValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetailViewValidationError{
						field:  fmt.Sprintf("Rows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetailViewValidationError{
					field:  fmt.Sprintf("Rows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DetailViewMultiError(errors)
	}

	return nil
}

// DetailViewMultiError is an error wrapping multiple validation errors
// returned by DetailView.ValidateAll() if the designated constraints aren't met.
type DetailViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailViewMultiError) AllErrors() []error { return m }

// DetailViewValidationError is the validation error returned by
// DetailView.Validate if the designated constraints aren't met.
type DetailViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailViewValidationError) ErrorName() string { return "DetailViewValidationError" }

// Error satisfies the builtin error interface
func (e DetailViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetailView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailViewValidationError{}

// Validate checks the field values on KeyValueList_KeyValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KeyValueList_KeyValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KeyValueList_KeyValue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KeyValueList_KeyValueMultiError, or nil if none found.
func (m *KeyValueList_KeyValue) ValidateAll() error {
	return m.validate(true)
}

func (m *KeyValueList_KeyValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Value

	if all {
		switch v := interface{}(m.GetStyle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KeyValueList_KeyValueValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KeyValueList_KeyValueValidationError{
					field:  "Style",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStyle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KeyValueList_KeyValueValidationError{
				field:  "Style",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KeyValueList_KeyValueMultiError(errors)
	}

	return nil
}

// KeyValueList_KeyValueMultiError is an error wrapping multiple validation
// errors returned by KeyValueList_KeyValue.ValidateAll() if the designated
// constraints aren't met.
type KeyValueList_KeyValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KeyValueList_KeyValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KeyValueList_KeyValueMultiError) AllErrors() []error { return m }

// KeyValueList_KeyValueValidationError is the validation error returned by
// KeyValueList_KeyValue.Validate if the designated constraints aren't met.
type KeyValueList_KeyValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KeyValueList_KeyValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KeyValueList_KeyValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KeyValueList_KeyValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KeyValueList_KeyValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KeyValueList_KeyValueValidationError) ErrorName() string {
	return "KeyValueList_KeyValueValidationError"
}

// Error satisfies the builtin error interface
func (e KeyValueList_KeyValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKeyValueList_KeyValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KeyValueList_KeyValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KeyValueList_KeyValueValidationError{}
