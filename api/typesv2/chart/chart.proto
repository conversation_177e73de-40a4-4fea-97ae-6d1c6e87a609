syntax = "proto3";

package api.typesv2.chart;

import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/chart";
option java_package = "com.github.epifi.gamma.api.typesv2.chart";

message ChartForTimePeriod {
  // The text to be show in tab
  // e.g. 1D, 6M, etc.
  // this is tag to be shown if user has not selected the tab
  typesv2.ui.IconTextComponent period_text = 1;

  // Represent chart properties of given graph
  // Eg: +1.3%, 3M (it is gain then text color is green)
  // Eg: -1.3%, 6M (it is loss then text color is red)
  repeated typesv2.ui.IconTextComponent chart_properties = 2;

  // represent the command to render chart with given data points
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3795%3A19129&t=KVVe7EsjitFElgwD-4
  ChartComponent chart_component = 3;

  // Represent chart description and interpretation of graph
  // Eg: ₹1,000 per month could have grown to ₹1.2 Lakh
  // figma: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=18157-7074&mode=design&t=8PhGWds5O8xzgLsz-4
  typesv2.ui.IconTextComponent chart_description = 4;

  // represent if the chart is selected and should be shows as default
  bool is_selected = 5;

  // represent a web URL in order to render the chart.

  // If there is a need to display a chart at the top, it is advised to use the FetchConfig rpc
  // otherwise the client won't be able to display any UI
  // until chart data is requested via FE rpc, which will impact the user experience.

  // for any other chart use case, following url can be used to render the view since it wont effect user experience
  string chart_web_url = 6;

  // The text to be show in tab when user select the tab
  // e.g. 1D, 6M, etc.
  // for is_selected == true, this field should used to render UI
  // other wise period_text should be used to render UI
  typesv2.ui.IconTextComponent selected_period_text = 7;
}


message ChartComponent {
  // indicate the command that needs to be run in order to render the chart,
  // For instance, we submit two JS functions to usstocks.
  // The first JS function helps initialize the attributes of the chart, such as the line color, start and end times, etc.
  // The second JS function helps rendering chart with given data points
  // The client that receives this instruction will call a web client or service to render the graph/chart.
  string chart_js_command = 1;
}
