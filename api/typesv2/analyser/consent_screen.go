package analyser

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	typesUiWidgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/be-common/pkg/colors"
)

func NewDefaultBenefitsLineItem(iconUrl, text string) *BenefitsSection_LineItem {
	return &BenefitsSection_LineItem{
		Icon: &BenefitsSection_LineItem_Icon{
			BackgroundColor: colors.ColorDarkLayer2,
			Image:           commontypes.GetVisualElementImageFromUrl(iconUrl),
		},
		Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(
			text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_S),
	}
}

func NewDefaultConsentScreen() *ConsentScreen {
	return &ConsentScreen{BackgroundColor: colors.ColorDarkBase}
}

func (c *ConsentScreen) WithDefaultTitle(text string) *ConsentScreen {
	c.Title = typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
		text, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M))
	return c
}

func (c *ConsentScreen) WithPrimaryImage(image *commontypes.VisualElement) *ConsentScreen {
	c.PrimaryImage = image
	return c
}

func (c *ConsentScreen) WithDefaultDescription(text string) *ConsentScreen {
	c.Description = typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
		text, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER))
	return c
}

func (c *ConsentScreen) WithDefaultBenefitsSection(footer *typesUiPb.IconTextComponent) *ConsentScreen {
	c.BenefitsSection = &BenefitsSection{
		BackgroundColor: colors.ColorDarkLayer1,
		Footer:          footer,
	}
	return c
}

func (c *ConsentScreen) WithDefaultBenefitsLineItem(iconUrl, text string) *ConsentScreen {
	if c.BenefitsSection == nil {
		c.WithDefaultBenefitsSection(nil)
	}
	c.BenefitsSection.Benefits = append(c.BenefitsSection.Benefits, &BenefitsSection_LineItem{
		Icon: &BenefitsSection_LineItem_Icon{
			BackgroundColor: colors.ColorDarkLayer2,
			Image:           commontypes.GetVisualElementImageFromUrl(iconUrl),
		},
		Text: commontypes.GetTextFromHtmlStringFontColourFontStyle(
			text, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_S),
	})

	return c
}

func (c *ConsentScreen) WithParagraphTnC(text, consentId string) *ConsentScreen {
	c.TncContent = &TnC{
		TncType: TnCType_TNC_TYPE_PARAGRAPH,
		Content: &TnC_Paragraph{
			Paragraph: &ConsentParagraph{
				ConsentId: consentId,
				Text:      commontypes.GetTextFromHtmlStringFontColourFontStyle(text, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_XS),
			},
		},
	}
	return c
}

func (c *ConsentScreen) WithCheckBoxTnC(text, consentId string) *ConsentScreen {
	if c.GetTncContent().GetCheckBoxList().GetCheckBoxes() == nil {
		c.TncContent = &TnC{
			TncType: TnCType_TNC_TYPE_CHECKBOX_LIST,
			Content: &TnC_CheckBoxList{
				CheckBoxList: &CheckBoxList{
					CheckBoxes: []*typesUiWidgetPb.CheckboxItem{},
				},
			},
		}
	}
	c.GetTncContent().GetCheckBoxList().CheckBoxes = append(c.GetTncContent().GetCheckBoxList().CheckBoxes, &typesUiWidgetPb.CheckboxItem{
		Id: consentId,
		DisplayText: commontypes.GetTextFromHtmlStringFontColourFontStyle(
			text, colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_XS),
	})
	return c
}

func (c *ConsentScreen) WithButton(text string, dl *deeplinkPb.Deeplink) *ConsentScreen {
	c.Cta = &CTA{
		CtaType: CTAType_CTA_TYPE_BUTTON,
		Content: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
			text, colors.ColorSnow, commontypes.FontStyle_BUTTON_M)).WithContainer(0, 0, 0, colors.ColorLightPrimaryAction).WithDeeplink(dl),
		BottomShadowColor: "#00866F",
	}
	return c
}

func (c *ConsentScreen) WithSlider(text string, dl *deeplinkPb.Deeplink) *ConsentScreen {
	c.Cta = &CTA{
		CtaType: CTAType_CTA_TYPE_SLIDER,
		Content: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
			text, colors.ColorSnow, commontypes.FontStyle_BUTTON_M)).WithContainer(0, 0, 0, colors.ColorLightPrimaryAction).WithDeeplink(dl),
	}
	return c
}
