syntax = "proto3";

package api.typesv2.common;

import "api/typesv2/common/account/account_type.proto";

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

message BankAccountDetails {
  string account_name = 1;
  string account_number = 2;
  account.AccountType account_type = 4;
  string ifsc = 5;
  string bank_name = 6;
}
