package common

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPhoneNumber_ToStringNationalNumber(t *testing.T) {
	ph := PhoneNumber{
		NationalNumber: 9999999999,
	}
	phStr := ph.ToStringNationalNumber()
	assert.Equal(t, "9999999999", phStr)
}

func TestParsePhoneNumber(t *testing.T) {
	tests := []struct {
		name     string
		phoneStr string
		want     *PhoneNumber
		wantErr  string
	}{
		{
			name:     "12 digit phone number with country code",
			phoneStr: "919999999999",
			want: &PhoneNumber{
				CountryCode:    91,
				NationalNumber: 9999999999,
			},
		},
		{
			name:     "10 digit phone number",
			phoneStr: "9999999999",
			want: &PhoneNumber{
				CountryCode:    0,
				NationalNumber: 9999999999,
			},
		},
		{
			name:     "wrong phone number length",
			phoneStr: "9199999999999999",
			want:     nil,
			wantErr:  "invalid phone number length",
		},
		{
			name:     "empty string",
			phoneStr: "",
			want:     nil,
			wantErr:  "invalid argument in parsing phone number",
		},
		{
			name:     "12 digit phone number with sign",
			phoneStr: "+************",
			want: &PhoneNumber{
				CountryCode:    91,
				NationalNumber: 9971488189,
			},
			wantErr: "",
		},
		{
			name:     "12 digit phone number with 3 digit country code",
			phoneStr: "971507148818",
			want: &PhoneNumber{
				CountryCode:    971,
				NationalNumber: 507148818,
			},
			wantErr: "",
		},
		{
			name:     "9 digit phone number with leading 0",
			phoneStr: "0507148818",
			want: &PhoneNumber{
				NationalNumber: 507148818,
			},
			wantErr: "",
		},
		{
			name:     "9 digit phone number with leading +0",
			phoneStr: "+000507148818",
			want: &PhoneNumber{
				NationalNumber: 507148818,
			},
			wantErr: "",
		},
		{
			name:     "12 digit phone number with 3 digit country code and plus",
			phoneStr: "+************",
			want: &PhoneNumber{
				CountryCode:    971,
				NationalNumber: 527148818,
			},
			wantErr: "",
		},
		{
			name:     "UAE number regex match failed",
			phoneStr: "+9715171488188",
			want:     nil,
			wantErr:  "regex match failed in parsing ph num",
		},
		{
			name:     "India number regex match failed",
			phoneStr: "+9144444444444",
			want:     nil,
			wantErr:  "regex match failed in parsing ph num",
		},
		{
			name:     "phone number with sign not starting with +",
			phoneStr: "#************",
			want:     nil,
			wantErr:  "error in parse ph num: strconv.Atoi: parsing \"#************\": invalid syntax",
		},
		{
			name:     "valid US number",
			phoneStr: "12002001230",
			want: &PhoneNumber{
				CountryCode:    1,
				NationalNumber: 2002001230,
			},
			wantErr: "",
		},
		{
			name:     "valid Qatar number",
			phoneStr: "97434561234",
			want: &PhoneNumber{
				CountryCode:    974,
				NationalNumber: 34561234,
			},
			wantErr: "",
		},
		{
			name:     "invalid Qatar number",
			phoneStr: "************",
			want:     nil,
			wantErr:  "regex match failed in parsing ph num",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParsePhoneNumber(tt.phoneStr)
			if (err != nil) != (tt.wantErr != "") {
				t.Errorf("got error: '%v', want: '%v'", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr {
					t.Errorf("got error: %v, want error: %v", err, tt.wantErr)
				}
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParsePhoneNumber() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPhoneNumber_ToString1(t *testing.T) {
	tests := []struct {
		name string
		ph   *PhoneNumber
		want string
	}{
		{
			name: "nil phone number",
			ph:   nil,
			want: "",
		},
		{
			name: "with country code phone number",
			ph: &PhoneNumber{
				CountryCode:    91,
				NationalNumber: 9999999999,
			},
			want: "919999999999",
		},
		{
			name: "without country code phone number",
			ph: &PhoneNumber{
				CountryCode:    0,
				NationalNumber: 9999999999,
			},
			want: "9999999999",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := tt.ph
			if got := p.ToString(); got != tt.want {
				t.Errorf("ToString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestPhoneNumber_ToStringInNpciFormat(t *testing.T) {
	tests := []struct {
		name string
		ph   *PhoneNumber
		want string
	}{
		{
			name: "nil phone number",
			ph:   nil,
			want: "",
		},
		{
			name: "with country code phone number",
			ph: &PhoneNumber{
				CountryCode:    91,
				NationalNumber: 9999999999,
			},
			want: "919999999999",
		},
		{
			name: "international phone number with country code",
			ph: &PhoneNumber{
				CountryCode:    971,
				NationalNumber: 9999999999,
			},
			want: "00009719999999999",
		},
		{
			name: "international phone number with large country code",
			ph: &PhoneNumber{
				CountryCode:    97121211,
				NationalNumber: 9999999999,
			},
			want: "971212119999999999",
		},
		{
			name: "without country code phone number",
			ph: &PhoneNumber{
				CountryCode:    0,
				NationalNumber: 9999999999,
			},
			want: "9999999999",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := tt.ph
			if got := p.ToStringInNpciFormat(); got != tt.want {
				t.Errorf("ToStringInNpciFormat() = %v, want %v", got, tt.want)
			}
		})
	}
}
