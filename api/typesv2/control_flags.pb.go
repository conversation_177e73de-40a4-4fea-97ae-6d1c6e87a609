// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/control_flags.proto

package typesv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents control flag to enable and disable any control.
type ControlFlag int32

const (
	ControlFlag_CONTROL_FLAG_UNSPECIFIED ControlFlag = 0
	ControlFlag_CONTROL_FLAG_ENABLE      ControlFlag = 1
	ControlFlag_CONTROL_FLAG_DISABLE     ControlFlag = 2
)

// Enum value maps for ControlFlag.
var (
	ControlFlag_name = map[int32]string{
		0: "CONTROL_FLAG_UNSPECIFIED",
		1: "CONTROL_FLAG_ENABLE",
		2: "CONTROL_FLAG_DISABLE",
	}
	ControlFlag_value = map[string]int32{
		"CONTROL_FLAG_UNSPECIFIED": 0,
		"CONTROL_FLAG_ENABLE":      1,
		"CONTROL_FLAG_DISABLE":     2,
	}
)

func (x ControlFlag) Enum() *ControlFlag {
	p := new(ControlFlag)
	*p = x
	return p
}

func (x ControlFlag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ControlFlag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_control_flags_proto_enumTypes[0].Descriptor()
}

func (ControlFlag) Type() protoreflect.EnumType {
	return &file_api_typesv2_control_flags_proto_enumTypes[0]
}

func (x ControlFlag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ControlFlag.Descriptor instead.
func (ControlFlag) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_control_flags_proto_rawDescGZIP(), []int{0}
}

var File_api_typesv2_control_flags_proto protoreflect.FileDescriptor

var file_api_typesv2_control_flags_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2a, 0x5e,
	0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1c, 0x0a,
	0x18, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x43,
	0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f,
	0x46, 0x4c, 0x41, 0x47, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x42, 0x48,
	0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_control_flags_proto_rawDescOnce sync.Once
	file_api_typesv2_control_flags_proto_rawDescData = file_api_typesv2_control_flags_proto_rawDesc
)

func file_api_typesv2_control_flags_proto_rawDescGZIP() []byte {
	file_api_typesv2_control_flags_proto_rawDescOnce.Do(func() {
		file_api_typesv2_control_flags_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_control_flags_proto_rawDescData)
	})
	return file_api_typesv2_control_flags_proto_rawDescData
}

var file_api_typesv2_control_flags_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_control_flags_proto_goTypes = []interface{}{
	(ControlFlag)(0), // 0: api.typesv2.ControlFlag
}
var file_api_typesv2_control_flags_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_control_flags_proto_init() }
func file_api_typesv2_control_flags_proto_init() {
	if File_api_typesv2_control_flags_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_control_flags_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_control_flags_proto_goTypes,
		DependencyIndexes: file_api_typesv2_control_flags_proto_depIdxs,
		EnumInfos:         file_api_typesv2_control_flags_proto_enumTypes,
	}.Build()
	File_api_typesv2_control_flags_proto = out.File
	file_api_typesv2_control_flags_proto_rawDesc = nil
	file_api_typesv2_control_flags_proto_goTypes = nil
	file_api_typesv2_control_flags_proto_depIdxs = nil
}
