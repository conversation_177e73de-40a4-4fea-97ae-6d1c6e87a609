// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/rms/orchestrator/consumer/orchestrator_consumer.proto

package consumer

import (
	context "context"
	action "github.com/epifi/gamma/api/fittt/action"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	OrchestratorConsumer_ProcessRMSEvent_FullMethodName                   = "/api.rms.orchestrator.consumer.OrchestratorConsumer/ProcessRMSEvent"
	OrchestratorConsumer_ProcessActionExecutionUpdateEvent_FullMethodName = "/api.rms.orchestrator.consumer.OrchestratorConsumer/ProcessActionExecutionUpdateEvent"
)

// OrchestratorConsumerClient is the client API for OrchestratorConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrchestratorConsumerClient interface {
	// ProcessRMSEvent processes the incoming order event,
	// 1. The incoming event type is matched with the predefined rules
	// 2. user subscriptions for matched rules are fetched
	// 3. A fact is prepared for every subscription
	// 4. condition is evaluated
	// 5. if condition returns true, rule is processed and delegated to client service
	ProcessRMSEvent(ctx context.Context, in *ProcessRMSEventRequest, opts ...grpc.CallOption) (*ProcessRMSEventResponse, error)
	ProcessActionExecutionUpdateEvent(ctx context.Context, in *action.ActionExecutionUpdate, opts ...grpc.CallOption) (*ProcessActionExecutionUpdateEventResponse, error)
}

type orchestratorConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewOrchestratorConsumerClient(cc grpc.ClientConnInterface) OrchestratorConsumerClient {
	return &orchestratorConsumerClient{cc}
}

func (c *orchestratorConsumerClient) ProcessRMSEvent(ctx context.Context, in *ProcessRMSEventRequest, opts ...grpc.CallOption) (*ProcessRMSEventResponse, error) {
	out := new(ProcessRMSEventResponse)
	err := c.cc.Invoke(ctx, OrchestratorConsumer_ProcessRMSEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orchestratorConsumerClient) ProcessActionExecutionUpdateEvent(ctx context.Context, in *action.ActionExecutionUpdate, opts ...grpc.CallOption) (*ProcessActionExecutionUpdateEventResponse, error) {
	out := new(ProcessActionExecutionUpdateEventResponse)
	err := c.cc.Invoke(ctx, OrchestratorConsumer_ProcessActionExecutionUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrchestratorConsumerServer is the server API for OrchestratorConsumer service.
// All implementations should embed UnimplementedOrchestratorConsumerServer
// for forward compatibility
type OrchestratorConsumerServer interface {
	// ProcessRMSEvent processes the incoming order event,
	// 1. The incoming event type is matched with the predefined rules
	// 2. user subscriptions for matched rules are fetched
	// 3. A fact is prepared for every subscription
	// 4. condition is evaluated
	// 5. if condition returns true, rule is processed and delegated to client service
	ProcessRMSEvent(context.Context, *ProcessRMSEventRequest) (*ProcessRMSEventResponse, error)
	ProcessActionExecutionUpdateEvent(context.Context, *action.ActionExecutionUpdate) (*ProcessActionExecutionUpdateEventResponse, error)
}

// UnimplementedOrchestratorConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedOrchestratorConsumerServer struct {
}

func (UnimplementedOrchestratorConsumerServer) ProcessRMSEvent(context.Context, *ProcessRMSEventRequest) (*ProcessRMSEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessRMSEvent not implemented")
}
func (UnimplementedOrchestratorConsumerServer) ProcessActionExecutionUpdateEvent(context.Context, *action.ActionExecutionUpdate) (*ProcessActionExecutionUpdateEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessActionExecutionUpdateEvent not implemented")
}

// UnsafeOrchestratorConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrchestratorConsumerServer will
// result in compilation errors.
type UnsafeOrchestratorConsumerServer interface {
	mustEmbedUnimplementedOrchestratorConsumerServer()
}

func RegisterOrchestratorConsumerServer(s grpc.ServiceRegistrar, srv OrchestratorConsumerServer) {
	s.RegisterService(&OrchestratorConsumer_ServiceDesc, srv)
}

func _OrchestratorConsumer_ProcessRMSEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessRMSEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorConsumerServer).ProcessRMSEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrchestratorConsumer_ProcessRMSEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorConsumerServer).ProcessRMSEvent(ctx, req.(*ProcessRMSEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrchestratorConsumer_ProcessActionExecutionUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(action.ActionExecutionUpdate)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrchestratorConsumerServer).ProcessActionExecutionUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrchestratorConsumer_ProcessActionExecutionUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrchestratorConsumerServer).ProcessActionExecutionUpdateEvent(ctx, req.(*action.ActionExecutionUpdate))
	}
	return interceptor(ctx, in, info, handler)
}

// OrchestratorConsumer_ServiceDesc is the grpc.ServiceDesc for OrchestratorConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrchestratorConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.rms.orchestrator.consumer.OrchestratorConsumer",
	HandlerType: (*OrchestratorConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessRMSEvent",
			Handler:    _OrchestratorConsumer_ProcessRMSEvent_Handler,
		},
		{
			MethodName: "ProcessActionExecutionUpdateEvent",
			Handler:    _OrchestratorConsumer_ProcessActionExecutionUpdateEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/rms/orchestrator/consumer/orchestrator_consumer.proto",
}
