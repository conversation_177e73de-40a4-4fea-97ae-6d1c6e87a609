// nolint:dupl
package comms

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/epifi/be-common/pkg/datetime"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	TransactionTimeLayout = "Jan 02,2006 at 15:04:05"
	DateFormat            = "02 Jan, 2006"
)

// CARD CONTROL EMAIL TEMPLATES

func (o *EmailOption_CreditCardControlsEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardControlsEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardControlsEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardControlsEmailOption.GetOption().(type) {
	case *CreditCardControlsEmailOption_CreditCardControlsEmailOptionV1:
		return o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV1().GetTemplateVersion()
	case *CreditCardControlsEmailOption_CreditCardControlsEmailOptionV2:
		return o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_CreditCardControlsEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("card controls email option is nil")
	}
	switch o.CreditCardControlsEmailOption.GetOption().(type) {
	case *CreditCardControlsEmailOption_CreditCardControlsEmailOptionV1:
		msg := strings.Replace(templateBody, "{#icon_url#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV1().GetIconUrl(), 1)
		msg = strings.Replace(msg, "{#heading#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV1().GetHeading(), 1)
		msg = strings.Replace(msg, "{#description#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV1().GetDescription(), 1)
		return msg, nil
	case *CreditCardControlsEmailOption_CreditCardControlsEmailOptionV2:
		msg := strings.Replace(templateBody, "{#icon_url#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetIconUrl(), 1)
		msg = strings.Replace(msg, "{#heading#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetHeading(), 1)
		msg = strings.Replace(msg, "{#description#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetDescription(), 1)
		msg = strings.Replace(msg, "{#box_title#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetBoxTitle(), 1)
		msg = strings.Replace(msg, "{#box_description#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetBoxDescription(), 1)
		msg = strings.Replace(msg, "{#bottom_description#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetBottomDescription(), 1)
		msg = strings.Replace(msg, "{#collapse_box_description#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetCollapseBoxDescription(), 1)
		msg = strings.Replace(msg, "{#box_color_class#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetBoxColorClass(), 1)
		msg = strings.Replace(msg, "{#additional_description#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetAdditionalDescription(), 1)

		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for card controls email template")
	}
}

// nolint: dupl
func (o *EmailOption_CreditCardControlsEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("card controls email option is nil")
	}
	switch o.CreditCardControlsEmailOption.GetOption().(type) {
	case *CreditCardControlsEmailOption_CreditCardControlsEmailOptionV1:
		sub := strings.Replace(subject, "{#card_control_event_type#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV1().GetCardControlEventType(), 1)
		sub = strings.Replace(sub, "{#last_four_digits#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV1().GetLastFourDigits(), 1)
		return sub, nil
	case *CreditCardControlsEmailOption_CreditCardControlsEmailOptionV2:
		sub := strings.Replace(subject, "{#card_control_event_type#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetCardControlEventType(), 1)
		sub = strings.Replace(sub, "{#last_four_digits#}", o.CreditCardControlsEmailOption.GetCreditCardControlsEmailOptionV2().GetLastFourDigits(), 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for credit card controls email template")
	}
}

// CARD TXN EMAIL TEMPLATE
func (o *EmailOption_CreditCardTransactionStatusEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardTransactionStatusEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardTransactionStatusEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardTransactionStatusEmailOption.GetOption().(type) {
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV1:
		return o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetTemplateVersion()
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV2:
		return o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetTemplateVersion()
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV3:
		return o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV3().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_CreditCardTransactionStatusEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("card txns email option is nil")
	}
	switch o.CreditCardTransactionStatusEmailOption.GetOption().(type) {
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV1:
		msg := strings.Replace(templateBody, "{#icon_url#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetIconUrl(), 1)
		msg = strings.Replace(msg, "{#background_color#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetBackgroundColor(), 1)
		msg = strings.Replace(msg, "{#heading#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetHeading(), 1)
		msg = strings.Replace(msg, "{#description#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetDescription(), 1)
		msg = strings.Replace(msg, "{#box_title#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetBoxTitle(), 1)
		msg = strings.Replace(msg, "{#box_description#}", moneyPkg.ToDisplayString(o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetTxnAmount()), 1)
		switch o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetTxnType().(type) {
		case *CreditCardTransactionStatusEmailOptionV1_Merchant:
			msg = strings.Replace(msg, "{#item_1#}", "Merchant", 1)
			msg = strings.Replace(msg, "{#value_1#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetMerchant(), 1)
		case *CreditCardTransactionStatusEmailOptionV1_AtmOutlet:
			msg = strings.Replace(msg, "{#item_1#}", "Atm Outlet", 1)
			msg = strings.Replace(msg, "{#value_1#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetAtmOutlet(), 1)
		}
		msg = strings.Replace(msg, "{#item_2#}", "Time", 1)
		msg = strings.Replace(msg, "{#value_2#}", datetime.TimestampToString(o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetTxnTimestamp(), TransactionTimeLayout, datetime.IST), 1)
		msg = strings.Replace(msg, "{#item_3#}", "Card Number", 1)
		msg = strings.Replace(msg, "{#value_3#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetMaskedCardNumber(), 1)
		return msg, nil
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV2:
		msg := strings.Replace(templateBody, "{#icon_url#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetIconUrl(), 1)
		msg = strings.Replace(msg, "{#background_color#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetBackgroundColor(), 1)
		msg = strings.Replace(msg, "{#heading#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetHeading(), 1)
		msg = strings.Replace(msg, "{#description#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetDescription(), 1)
		msg = strings.Replace(msg, "{#box_title#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetBoxTitle(), 1)
		msg = strings.Replace(msg, "{#box_description#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetBoxDescription(), 1)
		msg = strings.Replace(msg, "{#additional_description#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetAdditionalDescription(), 1)
		msg = strings.Replace(msg, "{#box_color_class#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetBoxColorClass(), 1)
		msg = strings.Replace(msg, "{#collapse_box_description#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetCollapseBoxDescription(), 1)
		msg = strings.Replace(msg, "{#collapse_item_description#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetCollapseItemDescription(), 1)
		msg = strings.Replace(msg, "{#table_row_details#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetTableRowDetails(), 1)
		return msg, nil
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV3:
		msg := strings.Replace(templateBody, "{#user_name#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV3().GetUserName(), 1)
		msg = strings.Replace(msg, "{#txn_failure_reason#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV3().GetTxnFailureReason(), 1)
		msg = strings.Replace(msg, "{#txn_failure_fix#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV3().GetTxnFailureFix(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for card txns email template")
	}
}

// nolint: dupl
func (o *EmailOption_CreditCardTransactionStatusEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card txns email option is nil")
	}
	switch o.CreditCardTransactionStatusEmailOption.GetOption().(type) {
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV1:
		sub := strings.Replace(subject, "{#card_transaction_event_type#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetCardTransactionEventType(), 1)
		sub = strings.Replace(sub, "{#last_four_digits#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV1().GetLastFourDigits(), 1)
		return sub, nil
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV2:
		sub := strings.Replace(subject, "{#card_transaction_event_type#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetCardTransactionEventType(), 1)
		sub = strings.Replace(sub, "{#last_four_digits#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV2().GetLastFourDigits(), 1)
		return sub, nil
	case *CreditCardTransactionStatusEmailOption_CreditCardTransactionStatusEmailOptionV3:
		sub := strings.Replace(subject, "{#email_subject#}", o.CreditCardTransactionStatusEmailOption.GetCreditCardTransactionStatusEmailOptionV3().GetEmailSubject(), 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for credit card txns email template")
	}
}

// CARD STATEMENT EMAIL TEMPLATE
func (o *EmailOption_CreditCardStatementEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardStatementEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardStatementEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardStatementEmailOption.GetOption().(type) {
	case *CreditCardStatementEmailOption_CreditCardStatementEmailOptionV1:
		return o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_CreditCardStatementEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card statement email option is nil")
	}
	switch o.CreditCardStatementEmailOption.GetOption().(type) {
	case *CreditCardStatementEmailOption_CreditCardStatementEmailOptionV1:
		totalAmountDueString := moneyPkg.ToDisplayStringWithoutSymbol(o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetTotalDue())
		minAmountDueString := moneyPkg.ToDisplayStringWithoutSymbol(o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetMinimumDue())
		statementToDate := datetime.DateToString(o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetStatementToDate(), DateFormat, datetime.IST)
		paymentDueDate := datetime.DateToString(o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetPaymentDueDate(), DateFormat, datetime.IST)
		msg := strings.Replace(templateBody, "{#credit_card_pay_bill_link#}", o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetCreditCardPayBillUrl(), 1)
		msg = strings.Replace(msg, "{#fi_coins_earned#}", strconv.Itoa(int(o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetTotalFiCoinsEarned())), 1)
		msg = strings.Replace(msg, "{#tad#}", totalAmountDueString, 1)
		msg = strings.Replace(msg, "{#mad#}", minAmountDueString, 1)
		msg = strings.Replace(msg, "{#statement_ending_date#}", statementToDate, 1)
		msg = strings.Replace(msg, "{#credit_card_last_four_digits#}", o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetCreditCardLastFourDigits(), 1)
		msg = strings.Replace(msg, "{#payment_due_date#}", paymentDueDate, 1)
		msg = strings.Replace(msg, "{#card_program_header_image#}", o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetCreditCardHeaderImage(), 1)
		msg = strings.Replace(msg, "{#card_program_credit_card_image#}", o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetCreditCardImage(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for credit card statement email template")
	}
}

func (o *EmailOption_CreditCardStatementEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card statement email option is nil")
	}
	switch o.CreditCardStatementEmailOption.GetOption().(type) {
	case *CreditCardStatementEmailOption_CreditCardStatementEmailOptionV1:
		statementFromDate := datetime.DateToString(o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetStatementFromDate(), DateFormat, datetime.IST)
		statementToDate := datetime.DateToString(o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetStatementToDate(), DateFormat, datetime.IST)
		statementDuration := fmt.Sprintf("%s - %s", statementFromDate, statementToDate)
		sub := strings.Replace(subject, "{#credit_card_last_four_digits#}", o.CreditCardStatementEmailOption.GetCreditCardStatementEmailOptionV1().GetCreditCardLastFourDigits(), 1)
		sub = strings.Replace(sub, "{#statement_duration#}", statementDuration, 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for credit card statement email template")
	}
}

// CREDIT CARD NOT ACTIVATED EMAIL
func (o *EmailOption_CreditCardNotActivatedEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardNotActivatedEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardNotActivatedEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardNotActivatedEmailOption.GetOption().(type) {
	case *CreditCardNotActivatedEmailOption_CreditCardNotActivatedEmailOptionV1:
		return o.CreditCardNotActivatedEmailOption.GetCreditCardNotActivatedEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_CreditCardNotActivatedEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card not activated email option is nil")
	}
	switch o.CreditCardNotActivatedEmailOption.GetOption().(type) {
	case *CreditCardNotActivatedEmailOption_CreditCardNotActivatedEmailOptionV1:
		msg := strings.Replace(templateBody, "{#first_name#}", o.CreditCardNotActivatedEmailOption.GetCreditCardNotActivatedEmailOptionV1().GetCustomerName().GetFirstName(), 1)
		msg = strings.Replace(msg, "{#days_left_to_activate_card#}", strconv.Itoa(int(o.CreditCardNotActivatedEmailOption.GetCreditCardNotActivatedEmailOptionV1().GetDaysLeftForActivation())), 1)
		msg = strings.Replace(msg, "{#activate_cc_url#}", o.CreditCardNotActivatedEmailOption.GetCreditCardNotActivatedEmailOptionV1().GetActivateCcUrl(), 2)
		msg = strings.Replace(msg, "{#open_fi_url#}", o.CreditCardNotActivatedEmailOption.GetCreditCardNotActivatedEmailOptionV1().GetOpenFiUrl(), 2)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for credit card not activated email template")
	}
}

func (o *EmailOption_CreditCardNotActivatedEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("credit card not activated email option is nil")
	}
	switch o.CreditCardNotActivatedEmailOption.GetOption().(type) {
	case *CreditCardNotActivatedEmailOption_CreditCardNotActivatedEmailOptionV1:
		sub := strings.Replace(subject, "{#credit_card_last_four_digits#}", o.CreditCardNotActivatedEmailOption.GetCreditCardNotActivatedEmailOptionV1().GetCcLastFourDigits(), 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for credit card not activated email template")
	}
}

// CREDIT_CARD_WELCOME_EMAIL is common welcome email
func (o *EmailOption_CreditCardWelcomeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardWelcomeEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardWelcomeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardWelcomeEmailOption.GetOption().(type) {
	case *CreditCardWelcomeEmailOption_CreditCardWelcomeEmailOptionV1:
		return o.CreditCardWelcomeEmailOption.GetCreditCardWelcomeEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_CreditCardWelcomeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("welcome email option is nil")
	}
	switch o.CreditCardWelcomeEmailOption.GetOption().(type) {
	case *CreditCardWelcomeEmailOption_CreditCardWelcomeEmailOptionV1:
		msg := strings.Replace(templateBody, "{#card_details#}", o.CreditCardWelcomeEmailOption.GetCreditCardWelcomeEmailOptionV1().GetCardImageUrl(), 1)
		msg = strings.Replace(msg, "{#common_tncs#}", o.CreditCardWelcomeEmailOption.GetCreditCardWelcomeEmailOptionV1().GetCommonTncs(), 1)
		msg = strings.Replace(msg, "{#mitc#}", o.CreditCardWelcomeEmailOption.GetCreditCardWelcomeEmailOptionV1().GetMostImportantTncs(), 1)
		msg = strings.Replace(msg, "{#kfs#}", o.CreditCardWelcomeEmailOption.GetCreditCardWelcomeEmailOptionV1().GetKeyFactStatement(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for welcome email template")
	}
}

func (o *EmailOption_CreditCardWelcomeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("welcome email option is nil")
	}
	switch o.CreditCardWelcomeEmailOption.GetOption().(type) {
	case *CreditCardWelcomeEmailOption_CreditCardWelcomeEmailOptionV1:
		sub := strings.Replace(subject, "{#card_name#}", o.CreditCardWelcomeEmailOption.GetCreditCardWelcomeEmailOptionV1().GetCardName(), 1)
		return sub, nil
	default:
		return "", fmt.Errorf("no valid version found for welcome email template")
	}
}

// MAGNIFI_WELCOME_EMAIL
func (o *EmailOption_MagnifiWelcomeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.MagnifiWelcomeEmailOption.GetEmailType()
}

func (o *EmailOption_MagnifiWelcomeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.MagnifiWelcomeEmailOption.GetOption().(type) {
	case *MagnifiWelcomeEmailOption_MagnifiWelcomeEmailOptionV1:
		return o.MagnifiWelcomeEmailOption.GetMagnifiWelcomeEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_MagnifiWelcomeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("magnifi welcome email option is nil")
	}
	switch o.MagnifiWelcomeEmailOption.GetOption().(type) {
	case *MagnifiWelcomeEmailOption_MagnifiWelcomeEmailOptionV1:
		return templateBody, nil
	default:
		return "", fmt.Errorf("no valid version found for magnifi welcome email template")
	}
}

func (o *EmailOption_MagnifiWelcomeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("magnifi welcome email option is nil")
	}
	switch o.MagnifiWelcomeEmailOption.GetOption().(type) {
	case *MagnifiWelcomeEmailOption_MagnifiWelcomeEmailOptionV1:
		return subject, nil
	default:
		return "", fmt.Errorf("no valid version found for magnifi welcome email template")
	}
}

// Credit card EMI creation email
func (o *EmailOption_CreditCardEmiCreationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardEmiCreationEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardEmiCreationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEmiCreationEmailOption.GetOption().(type) {
	case *CreditCardEmiCreationEmailOption_CreditCardEmiCreationEmailOptionV1:
		return o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_CreditCardEmiCreationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi creation email option is nil")
	}
	switch o.CreditCardEmiCreationEmailOption.GetOption().(type) {
	case *CreditCardEmiCreationEmailOption_CreditCardEmiCreationEmailOptionV1:
		msg := strings.Replace(templateBody, "{#merchant_name#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetMerchantName(), 2)
		msg = strings.Replace(msg, "{#transaction_amount#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetTransactionAmount(), 3)
		msg = strings.Replace(msg, "{#emi_amount#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetEmiAmount(), 1)
		msg = strings.Replace(msg, "{#cancellation_date#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetCancellationDate(), 1)
		msg = strings.Replace(msg, "{#creation_date#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetCreationDate(), 1)
		msg = strings.Replace(msg, "{#transaction_date#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetTransactionDate(), 1)
		msg = strings.Replace(msg, "{#tenure#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetTenure(), 1)
		msg = strings.Replace(msg, "{#interest_rate#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetInterestRate(), 1)
		msg = strings.Replace(msg, "{#processing_fees#}", o.CreditCardEmiCreationEmailOption.GetCreditCardEmiCreationEmailOptionV1().GetProcessingFees(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for emi creation email template")
	}
}

func (o *EmailOption_CreditCardEmiCreationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi creation email option is nil")
	}
	switch o.CreditCardEmiCreationEmailOption.GetOption().(type) {
	case *CreditCardEmiCreationEmailOption_CreditCardEmiCreationEmailOptionV1:
		return subject, nil
	default:
		return "", fmt.Errorf("no valid version found for emi creation email template")
	}
}

// Credit card EMI cancellation email
func (o *EmailOption_CreditCardEmiCancellationEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardEmiCancellationEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardEmiCancellationEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEmiCancellationEmailOption.GetOption().(type) {
	case *CreditCardEmiCancellationEmailOption_CreditCardEmiCancellationEmailOptionV1:
		return o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_CreditCardEmiCancellationEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi creation email option is nil")
	}
	switch o.CreditCardEmiCancellationEmailOption.GetOption().(type) {
	case *CreditCardEmiCancellationEmailOption_CreditCardEmiCancellationEmailOptionV1:
		msg := strings.Replace(templateBody, "{#merchant_name#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetMerchantName(), 2)
		msg = strings.Replace(msg, "{#transaction_amount#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetTransactionAmount(), 1)
		msg = strings.Replace(msg, "{#emi_amount#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetEmiAmount(), 2)
		msg = strings.Replace(msg, "{#cancellation_date#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetCancellationDate(), 2)
		msg = strings.Replace(msg, "{#creation_date#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetCreationDate(), 1)
		msg = strings.Replace(msg, "{#next_bill_gen_date#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetNextBillGenDate(), 1)
		msg = strings.Replace(msg, "{#card_last_four_digits#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetCardLastFourDigits(), 1)
		msg = strings.Replace(msg, "{#pre_closure_charges#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetPreClosureCharges(), 1)
		msg = strings.Replace(msg, "{#interest_charges#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetInterestCharges(), 1)
		msg = strings.Replace(msg, "{#amount_added_to_next_statement#}", o.CreditCardEmiCancellationEmailOption.GetCreditCardEmiCancellationEmailOptionV1().GetAmountAddedToNextStatement(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for emi creation email template")
	}
}

func (o *EmailOption_CreditCardEmiCancellationEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi creation email option is nil")
	}
	switch o.CreditCardEmiCancellationEmailOption.GetOption().(type) {
	case *CreditCardEmiCancellationEmailOption_CreditCardEmiCancellationEmailOptionV1:
		return subject, nil
	default:
		return "", fmt.Errorf("no valid version found for emi creation email template")
	}
}

// Credit card EMI preclosure email
func (o *EmailOption_CreditCardEmiPreClosureEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}
	return o.CreditCardEmiPreClosureEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardEmiPreClosureEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
	switch o.CreditCardEmiPreClosureEmailOption.GetOption().(type) {
	case *CreditCardEmiPreClosureEmailOption_CreditCardEmiPreClosureEmailOptionV1:
		return o.CreditCardEmiPreClosureEmailOption.GetCreditCardEmiPreClosureEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

// nolint:dupl
func (o *EmailOption_CreditCardEmiPreClosureEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi creation email option is nil")
	}
	switch o.CreditCardEmiPreClosureEmailOption.GetOption().(type) {
	case *CreditCardEmiPreClosureEmailOption_CreditCardEmiPreClosureEmailOptionV1:
		msg := strings.Replace(templateBody, "{#merchant_name#}", o.CreditCardEmiPreClosureEmailOption.GetCreditCardEmiPreClosureEmailOptionV1().GetMerchantName(), 2)
		msg = strings.Replace(msg, "{#pre_closure_fees#}", o.CreditCardEmiPreClosureEmailOption.GetCreditCardEmiPreClosureEmailOptionV1().GetPreClosureFees(), 3)
		msg = strings.Replace(msg, "{#emi_amount#}", o.CreditCardEmiPreClosureEmailOption.GetCreditCardEmiPreClosureEmailOptionV1().GetEmiAmount(), 1)
		msg = strings.Replace(msg, "{#due_amount#}", o.CreditCardEmiPreClosureEmailOption.GetCreditCardEmiPreClosureEmailOptionV1().GetDueAmount(), 2)
		msg = strings.Replace(msg, "{#creation_date#}", o.CreditCardEmiPreClosureEmailOption.GetCreditCardEmiPreClosureEmailOptionV1().GetCreationDate(), 1)
		msg = strings.Replace(msg, "{#total_charges#}", o.CreditCardEmiPreClosureEmailOption.GetCreditCardEmiPreClosureEmailOptionV1().GetTotalCharges(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for emi creation email template")
	}
}

func (o *EmailOption_CreditCardEmiPreClosureEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi creation email option is nil")
	}
	switch o.CreditCardEmiPreClosureEmailOption.GetOption().(type) {
	case *CreditCardEmiPreClosureEmailOption_CreditCardEmiPreClosureEmailOptionV1:
		return subject, nil
	default:
		return "", fmt.Errorf("no valid version found for emi creation email template")
	}
}

func (o *EmailOption_CreditCardEmiClosureEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi closure email option is nil")
	}

	switch o.CreditCardEmiClosureEmailOption.GetOption().(type) {
	case *CreditCardEmiClosureEmailOption_CreditCardEmiClosureEmailOptionV1:
		msg := strings.Replace(templateBody, "{#merchant_name#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetMerchantName(), 2)
		msg = strings.Replace(msg, "{#card_number#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetCardNumber(), 1)
		msg = strings.Replace(msg, "{#creation_date#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetCreationDate(), 1)
		msg = strings.Replace(msg, "{#completion_date#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetCompletionDate(), 1)
		msg = strings.Replace(msg, "{#total_amount_paid#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetTotalAmountPaid(), 1)
		msg = strings.Replace(msg, "{#principal_amount#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetPrincipalAmount(), 1)
		msg = strings.Replace(msg, "{#processing_fees#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetProcessingFees(), 1)
		msg = strings.Replace(msg, "{#interest_paid#}", o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetInterestPaid(), 1)
		return msg, nil
	default:
		return "", fmt.Errorf("no valid version found for emi closure email template")
	}
}

func (o *EmailOption_CreditCardEmiClosureEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("emi closure email option is nil")
	}

	switch o.CreditCardEmiClosureEmailOption.GetOption().(type) {
	case *CreditCardEmiClosureEmailOption_CreditCardEmiClosureEmailOptionV1:
		return subject, nil
	default:
		return "", fmt.Errorf("no valid version found for emi closure email template")
	}
}

func (o *EmailOption_CreditCardEmiClosureEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}

	return o.CreditCardEmiClosureEmailOption.GetEmailType()
}

func (o *EmailOption_CreditCardEmiClosureEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch o.CreditCardEmiClosureEmailOption.GetOption().(type) {
	case *CreditCardEmiClosureEmailOption_CreditCardEmiClosureEmailOptionV1:
		return o.CreditCardEmiClosureEmailOption.GetCreditCardEmiClosureEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}

func (o *EmailOption_CcKycComplianceStatusChangeEmailOption) GetActualEmailBody(templateBody string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("email option nil for CcKycComplianceStatusChangeEmailOption GetActualEmailBody")
	}

	switch o.CcKycComplianceStatusChangeEmailOption.GetOption().(type) {
	case *CcKycComplianceStatusChangeEmailOption_CcKycComplianceStatusChangeEmailOptionV1:
		templateBody = strings.Replace(templateBody, "{#background_color#}", o.CcKycComplianceStatusChangeEmailOption.GetCcKycComplianceStatusChangeEmailOptionV1().GetBackgroundColor(), 1)
		templateBody = strings.Replace(templateBody, "{#icon_url#}", o.CcKycComplianceStatusChangeEmailOption.GetCcKycComplianceStatusChangeEmailOptionV1().GetIconUrl(), 1)
		templateBody = strings.Replace(templateBody, "{#heading#}", o.CcKycComplianceStatusChangeEmailOption.GetCcKycComplianceStatusChangeEmailOptionV1().GetHeading(), 1)
		templateBody = strings.Replace(templateBody, "{#description#}", o.CcKycComplianceStatusChangeEmailOption.GetCcKycComplianceStatusChangeEmailOptionV1().GetDescription(), 1)
		templateBody = strings.Replace(templateBody, "{#table_row_details#}", o.CcKycComplianceStatusChangeEmailOption.GetCcKycComplianceStatusChangeEmailOptionV1().GetTableRowDetails(), 1)
		templateBody = strings.Replace(templateBody, "{#collapse_item_description#}", o.CcKycComplianceStatusChangeEmailOption.GetCcKycComplianceStatusChangeEmailOptionV1().GetCollapseItemDescription(), 1)
		return templateBody, nil
	default:
		return "", fmt.Errorf("invalid option for CcKycComplianceStatusChangeEmailOption GetActualEmailBody")
	}
}

func (o *EmailOption_CcKycComplianceStatusChangeEmailOption) GetEmailSubject(subject string) (string, error) {
	if o == nil {
		return "", fmt.Errorf("email option nil for CcKycComplianceStatusChangeEmailOption subject")
	}

	switch o.CcKycComplianceStatusChangeEmailOption.GetOption().(type) {
	case *CcKycComplianceStatusChangeEmailOption_CcKycComplianceStatusChangeEmailOptionV1:
		timeNowString := datetime.TimestampToString(timestamppb.Now(), "02 January 2006", datetime.IST)
		subject = strings.Replace(subject, "{#file_gen_date#}", timeNowString, 1)
		return subject, nil
	default:
		return "", fmt.Errorf("invalid version for CcKycComplianceStatusChangeEmailOption GetEmailSubject")
	}
}

func (o *EmailOption_CcKycComplianceStatusChangeEmailOption) GetType() EmailType {
	if o == nil {
		return EmailType_EMAIL_TYPE_UNSPECIFIED
	}

	return o.CcKycComplianceStatusChangeEmailOption.GetEmailType()
}

func (o *EmailOption_CcKycComplianceStatusChangeEmailOption) GetTemplateVersion() TemplateVersion {
	if o == nil {
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}

	switch o.CcKycComplianceStatusChangeEmailOption.GetOption().(type) {
	case *CcKycComplianceStatusChangeEmailOption_CcKycComplianceStatusChangeEmailOptionV1:
		return o.CcKycComplianceStatusChangeEmailOption.GetCcKycComplianceStatusChangeEmailOptionV1().GetTemplateVersion()
	default:
		return TemplateVersion_TEMPLATE_VERSION_UNSPECIFIED
	}
}
