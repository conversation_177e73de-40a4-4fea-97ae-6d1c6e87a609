syntax = "proto3";

package webfe.story;

option go_package = "github.com/epifi/gamma/api/webfe/story";
option java_package = "com.github.epifi.gamma.api.webfe.story";

enum StoryTemplate {
  STORY_TEMPLATE_UNSPECIFIED = 0;
  STORY_TEMPLATE_1 = 1;
  STORY_TEMPLATE_2A = 2;
  STORY_TEMPLATE_2B = 3;
  STORY_TEMPLATE_2C = 4;
  STORY_TEMPLATE_2D = 5;
  STORY_TEMPLATE_3 = 6;
  STORY_TEMPLATE_4A = 7;
  STORY_TEMPLATE_4B = 8;
  STORY_TEMPLATE_5 = 9;
  // 6A figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3926%3A10446&t=NF6McT3ePyrG6DkP-4
  // Value card on top and a bottom sheet containing line items below it
  STORY_TEMPLATE_6A = 10;
  // 6B figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3926%3A10191&t=NF6McT3ePyrG6DkP-4
  // Image card on top and a bottom sheet containing line items below it
  STORY_TEMPLATE_6B = 11;
  // 6C figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3926%3A9887&t=NF6McT3ePyrG6DkP-4
  // top title text with a bottom sheet containing line items below it
  STORY_TEMPLATE_6C = 12;
  // 7 figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3926%3A10271&t=NF6McT3ePyrG6DkP-4
  // Top tile and subtitle followed by upto 4 Image Card Components
  STORY_TEMPLATE_7 = 13;
  // 6D figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=4092%3A11814&t=NF6McT3ePyrG6DkP-4
  // Top value card with a bottom sheet containing center image, title and subtitle
  STORY_TEMPLATE_6D = 14;

  // 8 figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=42964-33201&t=bZPekPkfe8te8JBC-4
  // top title, subtitle and (image or lottie) with linear gradient background
  STORY_TEMPLATE_8 = 15;
}

enum Template1Var {
  TEMPLATE_1_VAR_UNSPECIFIED = 0;
  TEMPLATE_1_VAR_TEXT_1 = 1;
  TEMPLATE_1_VAR_TEXT_2 = 2;
  TEMPLATE_1_VAR_TEXT_3 = 3;
  TEMPLATE_1_VAR_TEXT_4 = 4;
  TEMPLATE_1_VAR_IMAGE_1 = 5;
  TEMPLATE_1_VAR_BG_LOTTIE = 6;
  TEMPLATE_1_VAR_IMAGE_2 = 7;
  TEMPLATE_1_VAR_ACTION_ID = 8;
}

enum Template2AVar {
  TEMPLATE_2A_VAR_UNSPECIFIED = 0;
  TEMPLATE_2A_VAR_TEXT_1 = 1;
  TEMPLATE_2A_VAR_IMAGE_1 = 2;
  TEMPLATE_2A_VAR_TEXT_2 = 3;
  TEMPLATE_2A_VAR_TEXT_3 = 4;
  TEMPLATE_2A_VAR_BG_LOTTIE = 5;
}

enum Template2BVar {
  TEMPLATE_2B_VAR_UNSPECIFIED = 0;
  TEMPLATE_2B_VAR_TEXT_1 = 1;
  TEMPLATE_2B_VAR_TEXT_2 = 2;
  TEMPLATE_2B_VAR_TEXT_3 = 3;
  TEMPLATE_2B_VAR_SHARE_IMAGE = 4;
  TEMPLATE_2B_VAR_BG_LOTTIE = 5;
}

enum Template2CVar {
  TEMPLATE_2C_VAR_UNSPECIFIED = 0;
  TEMPLATE_2C_VAR_TEXT_1 = 1;
  TEMPLATE_2C_VAR_TEXT_2 = 2;
  TEMPLATE_2C_VAR_TEXT_3 = 3;
  TEMPLATE_2C_VAR_ICON_IMAGE = 4;
  TEMPLATE_2C_VAR_TEXT_4 = 5;
  TEMPLATE_2C_VAR_SHARE_IMAGE = 6;
  TEMPLATE_2C_VAR_BG_LOTTIE = 7;
}

enum Template2DVar {
  TEMPLATE_2D_VAR_UNSPECIFIED = 0;
  TEMPLATE_2D_VAR_TEXT_1 = 1;
  TEMPLATE_2D_VAR_TEXT_2 = 2;
  TEMPLATE_2D_VAR_TEXT_3 = 3;
  TEMPLATE_2D_VAR_TEXT_4 = 4;
  TEMPLATE_2D_VAR_BG_LOTTIE = 5;
}

enum Template3Var {
  TEMPLATE_3_VAR_UNSPECIFIED = 0;
  TEMPLATE_3_VAR_SHARE_IMAGE = 1;
  TEMPLATE_3_VAR_BG_LOTTIE = 2;
}

enum Template4AVar {
  TEMPLATE_4A_VAR_UNSPECIFIED = 0;
  TEMPLATE_4A_VAR_TEXT_1 = 1;
  TEMPLATE_4A_VAR_ITEMS = 2;
  TEMPLATE_4A_VAR_SHARE_IMAGE = 3;
  TEMPLATE_4A_VAR_BG_LOTTIE = 4;
}

enum Template4BVar {
  TEMPLATE_4B_VAR_UNSPECIFIED = 0;
  TEMPLATE_4B_VAR_TEXT_1 = 1;
  TEMPLATE_4B_VAR_ITEMS = 2;
  TEMPLATE_4B_VAR_SHARE_IMAGE = 3;
  TEMPLATE_4B_VAR_BG_LOTTIE = 4;
}

enum Template5Var {
  TEMPLATE_5_VAR_UNSPECIFIED = 0;
  TEMPLATE_5_VAR_TEXT_1 = 1;
  TEMPLATE_5_VAR_CENTRE_IMAGE = 2;
  TEMPLATE_5_VAR_BOX_IMAGE = 3;
  TEMPLATE_5_VAR_TEXT_2 = 4;
  TEMPLATE_5_VAR_TEXT_3 = 5;
  TEMPLATE_5_VAR_SHARE_IMAGE = 6;
  TEMPLATE_5_VAR_BG_LOTTIE = 7;
}

enum Template6AVar {
  TEMPLATE_6A_VAR_UNSPECIFIED = 0;
  TEMPLATE_6A_VAR_VALUE_CARD = 1;
  TEMPLATE_6A_VAR_BOTTOM_SHEET_LIST_VIEW = 2;
}

enum Template6BVar {
  TEMPLATE_6B_VAR_UNSPECIFIED = 0;
  TEMPLATE_6B_VAR_IMAGE_CARD = 1;
  TEMPLATE_6B_VAR_BOTTOM_SHEET_LIST_VIEW = 2;
}

enum Template6CVar {
  TEMPLATE_6C_VAR_UNSPECIFIED = 0;
  TEMPLATE_6C_VAR_TEXT_1 = 1;
  TEMPLATE_6C_VAR_BOTTOM_SHEET_LIST_VIEW = 2;
  // Background in linear gradient; proto defines as
  // LinearGradient in api/types/ui/widget/widget_themes.proto
  // example: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43053-34736&t=bZPekPkfe8te8JBC-4
  TEMPLATE_6C_VAR_BOTTOM_SHEET_BG_LINEAR_GRADIENT = 3;
}


enum Template7Var {
  TEMPLATE_7_VAR_UNSPECIFIED = 0;
  TEMPLATE_7_VAR_TEXT_1 = 1;
  TEMPLATE_7_VAR_TEXT_2 = 2;
  TEMPLATE_7_VAR_IMAGE_CARD_LIST = 3;
  // Background in linear gradient; proto defines as
  // LinearGradient in api/types/ui/widget/widget_themes.proto
  // example: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=42979-34436&t=bZPekPkfe8te8JBC-4
  TEMPLATE_7_VAR_BG_LINEAR_GRADIENT = 4;
}

enum Template6DVar {
  TEMPLATE_6D_VAR_UNSPECIFIED = 0;
  TEMPLATE_6D_VAR_VALUE_CARD = 1;
  TEMPLATE_6D_VAR_BOTTOM_SHEET_CENTER_IMAGE_VIEW = 2;
}

enum Template8Var {
  TEMPLATE_8_VAR_UNSPECIFIED = 0;
  TEMPLATE_8_VAR_TITLE = 1;
  TEMPLATE_8_VAR_SUBTITLE = 2;
  // either image or lottie needs to be sent
  // both are not mandatory
  TEMPLATE_8_VAR_IMAGE = 3;
  TEMPLATE_8_VAR_LOTTIE = 4;
  // Background in linear gradient; proto defines as
  // LinearGradient in api/types/ui/widget/widget_themes.proto
  TEMPLATE_8_VAR_BG_LINEAR_GRADIENT = 5;
}
