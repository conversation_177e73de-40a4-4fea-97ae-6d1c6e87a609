syntax = "proto3";

package search.aggs;

option go_package = "github.com/epifi/gamma/api/search/aggs";
option java_package = "com.github.epifi.gamma.api.search.aggs";

enum TxnCategoryL1{
  TxnCategoryL1_UNSPECIFIED=0;
  L1_INCOME = 1;
  TRANSFER = 2;
  L1_INVESTMENT = 3;
  REPAYMENT = 4;
  WITHDRAWLS = 5;
  SPENDS = 6;
  TAX = 7;
  FEES = 8;
  UNCATEGORISED = 9;
}

enum TxnCategoryL2 {
  TxnCategoryL2_UNSPECIFIED=0;
  L2_INCOME = 1;
  L2_INVESTMENT = 2;
  ENTERTAINMENT = 3;
  FOOD_DRINK_GROCERIES = 4;
  HOME_AND_FAMILY = 5;
  HOUSING = 6;
  LOAN_REPAYMENTS = 7;
  MISC = 8;
  PERSONAL_CAR_HEALTH_MEDICAL = 9;
  SHOPPING = 10;
  TRANSPORT = 11;
}

enum TxnCategoryL3 {
  TxnCategoryL3_UNSPECIFIED=0;
  INCOME_FROM_EMPLOYMENT = 1;
  INCOME_FROM_RENT = 2;
  OTHER = 3;
  REFUNDS = 4;
  FIXED_DEPOSITS = 5;
  MUTUAL_FUNDS = 6;
  PENSION_POLICY = 7;
  PPF = 8;
  RECURRING_DEPOSITS = 9;
  SIDS = 10;
}
