// Code generated by MockGen. DO NOT EDIT.
// Source: api/./search/indexer/consumer_service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	categorizer "github.com/epifi/gamma/api/bigdata/categorizer"
	categorizer0 "github.com/epifi/gamma/api/categorizer"
	event "github.com/epifi/gamma/api/connected_account/event"
	event0 "github.com/epifi/gamma/api/cx/sherlock_scripts/event"
	event1 "github.com/epifi/gamma/api/cx/sherlock_sop/event"
	consumer "github.com/epifi/gamma/api/firefly/accounting/consumer"
	event2 "github.com/epifi/gamma/api/inapphelp/faq/event"
	notification "github.com/epifi/gamma/api/insights/notification"
	order "github.com/epifi/gamma/api/order"
	aa "github.com/epifi/gamma/api/order/aa"
	publisher "github.com/epifi/gamma/api/parser/publisher"
	account_pi "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savings "github.com/epifi/gamma/api/savings"
	indexer "github.com/epifi/gamma/api/search/indexer"
	timeline "github.com/epifi/gamma/api/timeline"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockIndexerClient is a mock of IndexerClient interface.
type MockIndexerClient struct {
	ctrl     *gomock.Controller
	recorder *MockIndexerClientMockRecorder
}

// MockIndexerClientMockRecorder is the mock recorder for MockIndexerClient.
type MockIndexerClientMockRecorder struct {
	mock *MockIndexerClient
}

// NewMockIndexerClient creates a new mock instance.
func NewMockIndexerClient(ctrl *gomock.Controller) *MockIndexerClient {
	mock := &MockIndexerClient{ctrl: ctrl}
	mock.recorder = &MockIndexerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIndexerClient) EXPECT() *MockIndexerClientMockRecorder {
	return m.recorder
}

// GetActorIdDocCount mocks base method.
func (m *MockIndexerClient) GetActorIdDocCount(ctx context.Context, in *indexer.GetActorIdDocCountRequest, opts ...grpc.CallOption) (*indexer.GetActorIdDocCountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActorIdDocCount", varargs...)
	ret0, _ := ret[0].(*indexer.GetActorIdDocCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorIdDocCount indicates an expected call of GetActorIdDocCount.
func (mr *MockIndexerClientMockRecorder) GetActorIdDocCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorIdDocCount", reflect.TypeOf((*MockIndexerClient)(nil).GetActorIdDocCount), varargs...)
}

// GetIndexDetail mocks base method.
func (m *MockIndexerClient) GetIndexDetail(ctx context.Context, in *indexer.GetIndexDetailRequest, opts ...grpc.CallOption) (*indexer.GetIndexDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIndexDetail", varargs...)
	ret0, _ := ret[0].(*indexer.GetIndexDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexDetail indicates an expected call of GetIndexDetail.
func (mr *MockIndexerClientMockRecorder) GetIndexDetail(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexDetail", reflect.TypeOf((*MockIndexerClient)(nil).GetIndexDetail), varargs...)
}

// GetInstrumentIndexActorIds mocks base method.
func (m *MockIndexerClient) GetInstrumentIndexActorIds(ctx context.Context, in *indexer.GetInstrumentIndexActorIdsRequest, opts ...grpc.CallOption) (*indexer.GetInstrumentIndexActorIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetInstrumentIndexActorIds", varargs...)
	ret0, _ := ret[0].(*indexer.GetInstrumentIndexActorIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstrumentIndexActorIds indicates an expected call of GetInstrumentIndexActorIds.
func (mr *MockIndexerClientMockRecorder) GetInstrumentIndexActorIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstrumentIndexActorIds", reflect.TypeOf((*MockIndexerClient)(nil).GetInstrumentIndexActorIds), varargs...)
}

// IndexOrder mocks base method.
func (m *MockIndexerClient) IndexOrder(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*indexer.IndexOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IndexOrder", varargs...)
	ret0, _ := ret[0].(*indexer.IndexOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IndexOrder indicates an expected call of IndexOrder.
func (mr *MockIndexerClientMockRecorder) IndexOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexOrder", reflect.TypeOf((*MockIndexerClient)(nil).IndexOrder), varargs...)
}

// IndexPis mocks base method.
func (m *MockIndexerClient) IndexPis(ctx context.Context, in *indexer.IndexPiRequest, opts ...grpc.CallOption) (*indexer.IndexPiResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IndexPis", varargs...)
	ret0, _ := ret[0].(*indexer.IndexPiResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IndexPis indicates an expected call of IndexPis.
func (mr *MockIndexerClientMockRecorder) IndexPis(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexPis", reflect.TypeOf((*MockIndexerClient)(nil).IndexPis), varargs...)
}

// ProcessAATxnEvent mocks base method.
func (m *MockIndexerClient) ProcessAATxnEvent(ctx context.Context, in *aa.AATxnUpdate, opts ...grpc.CallOption) (*indexer.ProcessAaTxnEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessAATxnEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessAaTxnEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAATxnEvent indicates an expected call of ProcessAATxnEvent.
func (mr *MockIndexerClientMockRecorder) ProcessAATxnEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAATxnEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessAATxnEvent), varargs...)
}

// ProcessBulkAATransactionEvent mocks base method.
func (m *MockIndexerClient) ProcessBulkAATransactionEvent(ctx context.Context, in *event.BatchAATxnUpdate, opts ...grpc.CallOption) (*indexer.ProcessBulkAATransactionEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessBulkAATransactionEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessBulkAATransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkAATransactionEvent indicates an expected call of ProcessBulkAATransactionEvent.
func (mr *MockIndexerClientMockRecorder) ProcessBulkAATransactionEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkAATransactionEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessBulkAATransactionEvent), varargs...)
}

// ProcessBulkOrder mocks base method.
func (m *MockIndexerClient) ProcessBulkOrder(ctx context.Context, in *order.BatchOrderUpdate, opts ...grpc.CallOption) (*indexer.ProcessTxnEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessBulkOrder", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessTxnEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkOrder indicates an expected call of ProcessBulkOrder.
func (mr *MockIndexerClientMockRecorder) ProcessBulkOrder(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkOrder", reflect.TypeOf((*MockIndexerClient)(nil).ProcessBulkOrder), varargs...)
}

// ProcessBulkTimelineEvent mocks base method.
func (m *MockIndexerClient) ProcessBulkTimelineEvent(ctx context.Context, in *timeline.BatchTimelineUpdate, opts ...grpc.CallOption) (*indexer.ProcessTimeLineEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessBulkTimelineEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessTimeLineEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkTimelineEvent indicates an expected call of ProcessBulkTimelineEvent.
func (mr *MockIndexerClientMockRecorder) ProcessBulkTimelineEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkTimelineEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessBulkTimelineEvent), varargs...)
}

// ProcessBulkTransactionEvent mocks base method.
func (m *MockIndexerClient) ProcessBulkTransactionEvent(ctx context.Context, in *publisher.PayTxnBackfillPublisher, opts ...grpc.CallOption) (*indexer.ProcessBulkTransactionEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessBulkTransactionEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessBulkTransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkTransactionEvent indicates an expected call of ProcessBulkTransactionEvent.
func (mr *MockIndexerClientMockRecorder) ProcessBulkTransactionEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkTransactionEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessBulkTransactionEvent), varargs...)
}

// ProcessCategorizerBatchEvent mocks base method.
func (m *MockIndexerClient) ProcessCategorizerBatchEvent(ctx context.Context, in *categorizer0.BatchTransactionCategoryUpdateEvent, opts ...grpc.CallOption) (*indexer.ProcessCategorizerBatchEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCategorizerBatchEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessCategorizerBatchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategorizerBatchEvent indicates an expected call of ProcessCategorizerBatchEvent.
func (mr *MockIndexerClientMockRecorder) ProcessCategorizerBatchEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategorizerBatchEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessCategorizerBatchEvent), varargs...)
}

// ProcessCategorizerEvent mocks base method.
func (m *MockIndexerClient) ProcessCategorizerEvent(ctx context.Context, in *categorizer0.TransactionCategoryUpdateEvent, opts ...grpc.CallOption) (*indexer.ProcessCategorizerEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCategorizerEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessCategorizerEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategorizerEvent indicates an expected call of ProcessCategorizerEvent.
func (mr *MockIndexerClientMockRecorder) ProcessCategorizerEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategorizerEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessCategorizerEvent), varargs...)
}

// ProcessCategorizerOntologyUpdateEvent mocks base method.
func (m *MockIndexerClient) ProcessCategorizerOntologyUpdateEvent(ctx context.Context, in *categorizer0.OntologyUpdateEvent, opts ...grpc.CallOption) (*indexer.ProcessCategorizerOntologyUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCategorizerOntologyUpdateEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessCategorizerOntologyUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategorizerOntologyUpdateEvent indicates an expected call of ProcessCategorizerOntologyUpdateEvent.
func (mr *MockIndexerClientMockRecorder) ProcessCategorizerOntologyUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategorizerOntologyUpdateEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessCategorizerOntologyUpdateEvent), varargs...)
}

// ProcessCategoryBatchEventPerOrderEvent mocks base method.
func (m *MockIndexerClient) ProcessCategoryBatchEventPerOrderEvent(ctx context.Context, in *categorizer.BatchOrderCategoryDetailsEvent, opts ...grpc.CallOption) (*indexer.ProcessCategoryBatchEventPerOrderEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCategoryBatchEventPerOrderEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessCategoryBatchEventPerOrderEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategoryBatchEventPerOrderEvent indicates an expected call of ProcessCategoryBatchEventPerOrderEvent.
func (mr *MockIndexerClientMockRecorder) ProcessCategoryBatchEventPerOrderEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategoryBatchEventPerOrderEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessCategoryBatchEventPerOrderEvent), varargs...)
}

// ProcessCreditCardTransactionEvent mocks base method.
func (m *MockIndexerClient) ProcessCreditCardTransactionEvent(ctx context.Context, in *consumer.CreditCardTransactionEvent, opts ...grpc.CallOption) (*indexer.ProcessCreditCardTransactionEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessCreditCardTransactionEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessCreditCardTransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCreditCardTransactionEvent indicates an expected call of ProcessCreditCardTransactionEvent.
func (mr *MockIndexerClientMockRecorder) ProcessCreditCardTransactionEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCreditCardTransactionEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessCreditCardTransactionEvent), varargs...)
}

// ProcessFaqEvent mocks base method.
func (m *MockIndexerClient) ProcessFaqEvent(ctx context.Context, in *event2.FaqDocumentEvent, opts ...grpc.CallOption) (*indexer.ProcessFaqEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessFaqEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessFaqEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFaqEvent indicates an expected call of ProcessFaqEvent.
func (mr *MockIndexerClientMockRecorder) ProcessFaqEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFaqEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessFaqEvent), varargs...)
}

// ProcessGmailEvent mocks base method.
func (m *MockIndexerClient) ProcessGmailEvent(ctx context.Context, in *notification.GmailUserSpendsNotification, opts ...grpc.CallOption) (*indexer.ProcessGmailEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessGmailEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessGmailEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessGmailEvent indicates an expected call of ProcessGmailEvent.
func (mr *MockIndexerClientMockRecorder) ProcessGmailEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessGmailEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessGmailEvent), varargs...)
}

// ProcessPiEvent mocks base method.
func (m *MockIndexerClient) ProcessPiEvent(ctx context.Context, in *account_pi.AccountPiCreateOrUpdateEvent, opts ...grpc.CallOption) (*indexer.ProcessPiEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessPiEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessPiEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPiEvent indicates an expected call of ProcessPiEvent.
func (mr *MockIndexerClientMockRecorder) ProcessPiEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPiEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessPiEvent), varargs...)
}

// ProcessSavingsAccountUpdateEvent mocks base method.
func (m *MockIndexerClient) ProcessSavingsAccountUpdateEvent(ctx context.Context, in *savings.AccountStateUpdateEvent, opts ...grpc.CallOption) (*indexer.ProcessSavingsAccountUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessSavingsAccountUpdateEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessSavingsAccountUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSavingsAccountUpdateEvent indicates an expected call of ProcessSavingsAccountUpdateEvent.
func (mr *MockIndexerClientMockRecorder) ProcessSavingsAccountUpdateEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSavingsAccountUpdateEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessSavingsAccountUpdateEvent), varargs...)
}

// ProcessSherlockScriptsEvent mocks base method.
func (m *MockIndexerClient) ProcessSherlockScriptsEvent(ctx context.Context, in *event0.UpdateSherlockScriptsBatch, opts ...grpc.CallOption) (*indexer.ProcessSherlockScriptsUpdateBatchEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessSherlockScriptsEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessSherlockScriptsUpdateBatchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSherlockScriptsEvent indicates an expected call of ProcessSherlockScriptsEvent.
func (mr *MockIndexerClientMockRecorder) ProcessSherlockScriptsEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSherlockScriptsEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessSherlockScriptsEvent), varargs...)
}

// ProcessSherlockSopEvent mocks base method.
func (m *MockIndexerClient) ProcessSherlockSopEvent(ctx context.Context, in *event1.UpdateSherlockSopBatch, opts ...grpc.CallOption) (*indexer.ProcessSherlockSopsUpdateBatchEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessSherlockSopEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessSherlockSopsUpdateBatchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSherlockSopEvent indicates an expected call of ProcessSherlockSopEvent.
func (mr *MockIndexerClientMockRecorder) ProcessSherlockSopEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSherlockSopEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessSherlockSopEvent), varargs...)
}

// ProcessTimeLineEvent mocks base method.
func (m *MockIndexerClient) ProcessTimeLineEvent(ctx context.Context, in *timeline.TimeLineCreateOrUpdateEvent, opts ...grpc.CallOption) (*indexer.ProcessTimeLineEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessTimeLineEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessTimeLineEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessTimeLineEvent indicates an expected call of ProcessTimeLineEvent.
func (mr *MockIndexerClientMockRecorder) ProcessTimeLineEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessTimeLineEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessTimeLineEvent), varargs...)
}

// ProcessTxnEvent mocks base method.
func (m *MockIndexerClient) ProcessTxnEvent(ctx context.Context, in *order.OrderUpdate, opts ...grpc.CallOption) (*indexer.ProcessTxnEventResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessTxnEvent", varargs...)
	ret0, _ := ret[0].(*indexer.ProcessTxnEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessTxnEvent indicates an expected call of ProcessTxnEvent.
func (mr *MockIndexerClientMockRecorder) ProcessTxnEvent(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessTxnEvent", reflect.TypeOf((*MockIndexerClient)(nil).ProcessTxnEvent), varargs...)
}

// UpdateTransactionCategory mocks base method.
func (m *MockIndexerClient) UpdateTransactionCategory(ctx context.Context, in *indexer.UpdateTransactionCategoryRequest, opts ...grpc.CallOption) (*indexer.UpdateTransactionCategoryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTransactionCategory", varargs...)
	ret0, _ := ret[0].(*indexer.UpdateTransactionCategoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransactionCategory indicates an expected call of UpdateTransactionCategory.
func (mr *MockIndexerClientMockRecorder) UpdateTransactionCategory(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransactionCategory", reflect.TypeOf((*MockIndexerClient)(nil).UpdateTransactionCategory), varargs...)
}

// MockIndexerServer is a mock of IndexerServer interface.
type MockIndexerServer struct {
	ctrl     *gomock.Controller
	recorder *MockIndexerServerMockRecorder
}

// MockIndexerServerMockRecorder is the mock recorder for MockIndexerServer.
type MockIndexerServerMockRecorder struct {
	mock *MockIndexerServer
}

// NewMockIndexerServer creates a new mock instance.
func NewMockIndexerServer(ctrl *gomock.Controller) *MockIndexerServer {
	mock := &MockIndexerServer{ctrl: ctrl}
	mock.recorder = &MockIndexerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIndexerServer) EXPECT() *MockIndexerServerMockRecorder {
	return m.recorder
}

// GetActorIdDocCount mocks base method.
func (m *MockIndexerServer) GetActorIdDocCount(arg0 context.Context, arg1 *indexer.GetActorIdDocCountRequest) (*indexer.GetActorIdDocCountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActorIdDocCount", arg0, arg1)
	ret0, _ := ret[0].(*indexer.GetActorIdDocCountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorIdDocCount indicates an expected call of GetActorIdDocCount.
func (mr *MockIndexerServerMockRecorder) GetActorIdDocCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorIdDocCount", reflect.TypeOf((*MockIndexerServer)(nil).GetActorIdDocCount), arg0, arg1)
}

// GetIndexDetail mocks base method.
func (m *MockIndexerServer) GetIndexDetail(arg0 context.Context, arg1 *indexer.GetIndexDetailRequest) (*indexer.GetIndexDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexDetail", arg0, arg1)
	ret0, _ := ret[0].(*indexer.GetIndexDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexDetail indicates an expected call of GetIndexDetail.
func (mr *MockIndexerServerMockRecorder) GetIndexDetail(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexDetail", reflect.TypeOf((*MockIndexerServer)(nil).GetIndexDetail), arg0, arg1)
}

// GetInstrumentIndexActorIds mocks base method.
func (m *MockIndexerServer) GetInstrumentIndexActorIds(arg0 context.Context, arg1 *indexer.GetInstrumentIndexActorIdsRequest) (*indexer.GetInstrumentIndexActorIdsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstrumentIndexActorIds", arg0, arg1)
	ret0, _ := ret[0].(*indexer.GetInstrumentIndexActorIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstrumentIndexActorIds indicates an expected call of GetInstrumentIndexActorIds.
func (mr *MockIndexerServerMockRecorder) GetInstrumentIndexActorIds(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstrumentIndexActorIds", reflect.TypeOf((*MockIndexerServer)(nil).GetInstrumentIndexActorIds), arg0, arg1)
}

// IndexOrder mocks base method.
func (m *MockIndexerServer) IndexOrder(arg0 context.Context, arg1 *order.OrderUpdate) (*indexer.IndexOrderResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IndexOrder", arg0, arg1)
	ret0, _ := ret[0].(*indexer.IndexOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IndexOrder indicates an expected call of IndexOrder.
func (mr *MockIndexerServerMockRecorder) IndexOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexOrder", reflect.TypeOf((*MockIndexerServer)(nil).IndexOrder), arg0, arg1)
}

// IndexPis mocks base method.
func (m *MockIndexerServer) IndexPis(arg0 context.Context, arg1 *indexer.IndexPiRequest) (*indexer.IndexPiResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IndexPis", arg0, arg1)
	ret0, _ := ret[0].(*indexer.IndexPiResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IndexPis indicates an expected call of IndexPis.
func (mr *MockIndexerServerMockRecorder) IndexPis(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IndexPis", reflect.TypeOf((*MockIndexerServer)(nil).IndexPis), arg0, arg1)
}

// ProcessAATxnEvent mocks base method.
func (m *MockIndexerServer) ProcessAATxnEvent(arg0 context.Context, arg1 *aa.AATxnUpdate) (*indexer.ProcessAaTxnEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessAATxnEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessAaTxnEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessAATxnEvent indicates an expected call of ProcessAATxnEvent.
func (mr *MockIndexerServerMockRecorder) ProcessAATxnEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessAATxnEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessAATxnEvent), arg0, arg1)
}

// ProcessBulkAATransactionEvent mocks base method.
func (m *MockIndexerServer) ProcessBulkAATransactionEvent(arg0 context.Context, arg1 *event.BatchAATxnUpdate) (*indexer.ProcessBulkAATransactionEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessBulkAATransactionEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessBulkAATransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkAATransactionEvent indicates an expected call of ProcessBulkAATransactionEvent.
func (mr *MockIndexerServerMockRecorder) ProcessBulkAATransactionEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkAATransactionEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessBulkAATransactionEvent), arg0, arg1)
}

// ProcessBulkOrder mocks base method.
func (m *MockIndexerServer) ProcessBulkOrder(arg0 context.Context, arg1 *order.BatchOrderUpdate) (*indexer.ProcessTxnEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessBulkOrder", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessTxnEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkOrder indicates an expected call of ProcessBulkOrder.
func (mr *MockIndexerServerMockRecorder) ProcessBulkOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkOrder", reflect.TypeOf((*MockIndexerServer)(nil).ProcessBulkOrder), arg0, arg1)
}

// ProcessBulkTimelineEvent mocks base method.
func (m *MockIndexerServer) ProcessBulkTimelineEvent(arg0 context.Context, arg1 *timeline.BatchTimelineUpdate) (*indexer.ProcessTimeLineEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessBulkTimelineEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessTimeLineEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkTimelineEvent indicates an expected call of ProcessBulkTimelineEvent.
func (mr *MockIndexerServerMockRecorder) ProcessBulkTimelineEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkTimelineEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessBulkTimelineEvent), arg0, arg1)
}

// ProcessBulkTransactionEvent mocks base method.
func (m *MockIndexerServer) ProcessBulkTransactionEvent(arg0 context.Context, arg1 *publisher.PayTxnBackfillPublisher) (*indexer.ProcessBulkTransactionEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessBulkTransactionEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessBulkTransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessBulkTransactionEvent indicates an expected call of ProcessBulkTransactionEvent.
func (mr *MockIndexerServerMockRecorder) ProcessBulkTransactionEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessBulkTransactionEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessBulkTransactionEvent), arg0, arg1)
}

// ProcessCategorizerBatchEvent mocks base method.
func (m *MockIndexerServer) ProcessCategorizerBatchEvent(arg0 context.Context, arg1 *categorizer0.BatchTransactionCategoryUpdateEvent) (*indexer.ProcessCategorizerBatchEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCategorizerBatchEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessCategorizerBatchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategorizerBatchEvent indicates an expected call of ProcessCategorizerBatchEvent.
func (mr *MockIndexerServerMockRecorder) ProcessCategorizerBatchEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategorizerBatchEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessCategorizerBatchEvent), arg0, arg1)
}

// ProcessCategorizerEvent mocks base method.
func (m *MockIndexerServer) ProcessCategorizerEvent(arg0 context.Context, arg1 *categorizer0.TransactionCategoryUpdateEvent) (*indexer.ProcessCategorizerEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCategorizerEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessCategorizerEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategorizerEvent indicates an expected call of ProcessCategorizerEvent.
func (mr *MockIndexerServerMockRecorder) ProcessCategorizerEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategorizerEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessCategorizerEvent), arg0, arg1)
}

// ProcessCategorizerOntologyUpdateEvent mocks base method.
func (m *MockIndexerServer) ProcessCategorizerOntologyUpdateEvent(arg0 context.Context, arg1 *categorizer0.OntologyUpdateEvent) (*indexer.ProcessCategorizerOntologyUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCategorizerOntologyUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessCategorizerOntologyUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategorizerOntologyUpdateEvent indicates an expected call of ProcessCategorizerOntologyUpdateEvent.
func (mr *MockIndexerServerMockRecorder) ProcessCategorizerOntologyUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategorizerOntologyUpdateEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessCategorizerOntologyUpdateEvent), arg0, arg1)
}

// ProcessCategoryBatchEventPerOrderEvent mocks base method.
func (m *MockIndexerServer) ProcessCategoryBatchEventPerOrderEvent(arg0 context.Context, arg1 *categorizer.BatchOrderCategoryDetailsEvent) (*indexer.ProcessCategoryBatchEventPerOrderEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCategoryBatchEventPerOrderEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessCategoryBatchEventPerOrderEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCategoryBatchEventPerOrderEvent indicates an expected call of ProcessCategoryBatchEventPerOrderEvent.
func (mr *MockIndexerServerMockRecorder) ProcessCategoryBatchEventPerOrderEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCategoryBatchEventPerOrderEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessCategoryBatchEventPerOrderEvent), arg0, arg1)
}

// ProcessCreditCardTransactionEvent mocks base method.
func (m *MockIndexerServer) ProcessCreditCardTransactionEvent(arg0 context.Context, arg1 *consumer.CreditCardTransactionEvent) (*indexer.ProcessCreditCardTransactionEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessCreditCardTransactionEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessCreditCardTransactionEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessCreditCardTransactionEvent indicates an expected call of ProcessCreditCardTransactionEvent.
func (mr *MockIndexerServerMockRecorder) ProcessCreditCardTransactionEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessCreditCardTransactionEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessCreditCardTransactionEvent), arg0, arg1)
}

// ProcessFaqEvent mocks base method.
func (m *MockIndexerServer) ProcessFaqEvent(arg0 context.Context, arg1 *event2.FaqDocumentEvent) (*indexer.ProcessFaqEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessFaqEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessFaqEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessFaqEvent indicates an expected call of ProcessFaqEvent.
func (mr *MockIndexerServerMockRecorder) ProcessFaqEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFaqEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessFaqEvent), arg0, arg1)
}

// ProcessGmailEvent mocks base method.
func (m *MockIndexerServer) ProcessGmailEvent(arg0 context.Context, arg1 *notification.GmailUserSpendsNotification) (*indexer.ProcessGmailEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessGmailEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessGmailEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessGmailEvent indicates an expected call of ProcessGmailEvent.
func (mr *MockIndexerServerMockRecorder) ProcessGmailEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessGmailEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessGmailEvent), arg0, arg1)
}

// ProcessPiEvent mocks base method.
func (m *MockIndexerServer) ProcessPiEvent(arg0 context.Context, arg1 *account_pi.AccountPiCreateOrUpdateEvent) (*indexer.ProcessPiEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessPiEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessPiEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessPiEvent indicates an expected call of ProcessPiEvent.
func (mr *MockIndexerServerMockRecorder) ProcessPiEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPiEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessPiEvent), arg0, arg1)
}

// ProcessSavingsAccountUpdateEvent mocks base method.
func (m *MockIndexerServer) ProcessSavingsAccountUpdateEvent(arg0 context.Context, arg1 *savings.AccountStateUpdateEvent) (*indexer.ProcessSavingsAccountUpdateEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessSavingsAccountUpdateEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessSavingsAccountUpdateEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSavingsAccountUpdateEvent indicates an expected call of ProcessSavingsAccountUpdateEvent.
func (mr *MockIndexerServerMockRecorder) ProcessSavingsAccountUpdateEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSavingsAccountUpdateEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessSavingsAccountUpdateEvent), arg0, arg1)
}

// ProcessSherlockScriptsEvent mocks base method.
func (m *MockIndexerServer) ProcessSherlockScriptsEvent(arg0 context.Context, arg1 *event0.UpdateSherlockScriptsBatch) (*indexer.ProcessSherlockScriptsUpdateBatchEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessSherlockScriptsEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessSherlockScriptsUpdateBatchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSherlockScriptsEvent indicates an expected call of ProcessSherlockScriptsEvent.
func (mr *MockIndexerServerMockRecorder) ProcessSherlockScriptsEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSherlockScriptsEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessSherlockScriptsEvent), arg0, arg1)
}

// ProcessSherlockSopEvent mocks base method.
func (m *MockIndexerServer) ProcessSherlockSopEvent(arg0 context.Context, arg1 *event1.UpdateSherlockSopBatch) (*indexer.ProcessSherlockSopsUpdateBatchEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessSherlockSopEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessSherlockSopsUpdateBatchEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessSherlockSopEvent indicates an expected call of ProcessSherlockSopEvent.
func (mr *MockIndexerServerMockRecorder) ProcessSherlockSopEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessSherlockSopEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessSherlockSopEvent), arg0, arg1)
}

// ProcessTimeLineEvent mocks base method.
func (m *MockIndexerServer) ProcessTimeLineEvent(arg0 context.Context, arg1 *timeline.TimeLineCreateOrUpdateEvent) (*indexer.ProcessTimeLineEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessTimeLineEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessTimeLineEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessTimeLineEvent indicates an expected call of ProcessTimeLineEvent.
func (mr *MockIndexerServerMockRecorder) ProcessTimeLineEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessTimeLineEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessTimeLineEvent), arg0, arg1)
}

// ProcessTxnEvent mocks base method.
func (m *MockIndexerServer) ProcessTxnEvent(arg0 context.Context, arg1 *order.OrderUpdate) (*indexer.ProcessTxnEventResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessTxnEvent", arg0, arg1)
	ret0, _ := ret[0].(*indexer.ProcessTxnEventResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessTxnEvent indicates an expected call of ProcessTxnEvent.
func (mr *MockIndexerServerMockRecorder) ProcessTxnEvent(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessTxnEvent", reflect.TypeOf((*MockIndexerServer)(nil).ProcessTxnEvent), arg0, arg1)
}

// UpdateTransactionCategory mocks base method.
func (m *MockIndexerServer) UpdateTransactionCategory(arg0 context.Context, arg1 *indexer.UpdateTransactionCategoryRequest) (*indexer.UpdateTransactionCategoryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTransactionCategory", arg0, arg1)
	ret0, _ := ret[0].(*indexer.UpdateTransactionCategoryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransactionCategory indicates an expected call of UpdateTransactionCategory.
func (mr *MockIndexerServerMockRecorder) UpdateTransactionCategory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransactionCategory", reflect.TypeOf((*MockIndexerServer)(nil).UpdateTransactionCategory), arg0, arg1)
}

// MockUnsafeIndexerServer is a mock of UnsafeIndexerServer interface.
type MockUnsafeIndexerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeIndexerServerMockRecorder
}

// MockUnsafeIndexerServerMockRecorder is the mock recorder for MockUnsafeIndexerServer.
type MockUnsafeIndexerServerMockRecorder struct {
	mock *MockUnsafeIndexerServer
}

// NewMockUnsafeIndexerServer creates a new mock instance.
func NewMockUnsafeIndexerServer(ctrl *gomock.Controller) *MockUnsafeIndexerServer {
	mock := &MockUnsafeIndexerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeIndexerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeIndexerServer) EXPECT() *MockUnsafeIndexerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedIndexerServer mocks base method.
func (m *MockUnsafeIndexerServer) mustEmbedUnimplementedIndexerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedIndexerServer")
}

// mustEmbedUnimplementedIndexerServer indicates an expected call of mustEmbedUnimplementedIndexerServer.
func (mr *MockUnsafeIndexerServerMockRecorder) mustEmbedUnimplementedIndexerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedIndexerServer", reflect.TypeOf((*MockUnsafeIndexerServer)(nil).mustEmbedUnimplementedIndexerServer))
}
