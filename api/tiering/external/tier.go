package external

func (t Tier) IsSalaryOrSalaryLiteTier() bool {
	return t == Tier_TIER_FI_SALARY_LITE || t == Tier_TIER_FI_SALARY || t == Tier_TIER_FI_SALARY_BASIC
}

func (t Tier) IsSalaryTier() bool {
	return t == Tier_TIER_FI_SALARY || t == Tier_TIER_FI_SALARY_BASIC
}

// Deprecated: FI_AA_SALARY_BAND_3 is the only Prime tier - BAND_1, and BAND_2 are deprecated tiers - having them only for backward compatability
func (t Tier) IsSalaryRelatedTier() bool {
	return t == Tier_TIER_FI_SALARY_LITE ||
		t == Tier_TIER_FI_SALARY ||
		t == Tier_TIER_FI_AA_SALARY_BAND_1 ||
		t == Tier_TIER_FI_AA_SALARY_BAND_2 ||
		t == Tier_TIER_FI_AA_SALARY_BAND_3 ||
		t == Tier_TIER_FI_SALARY_BASIC
}

func (t Tier) IsAaSalaryTier() bool {
	return t == Tier_TIER_FI_AA_SALARY_BAND_1 ||
		t == Tier_TIER_FI_AA_SALARY_BAND_2 ||
		t == Tier_TIER_FI_AA_SALARY_BAND_3
}

func (t Tier) IsBaseTier() bool {
	return t == Tier_TIER_FI_BASIC || t == Tier_TIER_FI_REGULAR
}

var AaSalaryTiers = func() []Tier {
	return []Tier{
		Tier_TIER_FI_AA_SALARY_BAND_1,
		Tier_TIER_FI_AA_SALARY_BAND_2,
		Tier_TIER_FI_AA_SALARY_BAND_3,
	}
}

func (t Tier) IsCashbackEligibleTier() bool {
	return t.IsAaSalaryTier() || t == Tier_TIER_FI_INFINITE || t == Tier_TIER_FI_SALARY || t == Tier_TIER_FI_PLUS || t == Tier_TIER_FI_SALARY_BASIC
}
