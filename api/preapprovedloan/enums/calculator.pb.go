// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/enums/calculator.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CalculatorAccuracy is an enum to specify the required accuracy for the loan amount calculations
// example usage: S1/S2 screens
type CalculatorAccuracy int32

const (
	CalculatorAccuracy_CALCULATOR_ACCURACY_UNSPECIFIED CalculatorAccuracy = 0
	// recommended to be used when high level calculations are required for presentation and not for the final calculations
	// e.g.: S1 screen - as the user is seeing estimated values based on his/her inputs
	CalculatorAccuracy_CALCULATOR_ACCURACY_NEAR_ACCURATE CalculatorAccuracy = 1
	// would be using the most accurate calculation possible (say lms)
	// values from this calculator will be the final values committed to the lender and the user
	// this could incur extra cost in terms of network calls or computation compared to near accurate
	// e.g.: S2 screen - for Liquiloans Early Salary, we would use Finflux LMS to do the calculations
	CalculatorAccuracy_CALCULATOR_ACCURACY_ACCURATE CalculatorAccuracy = 2
)

// Enum value maps for CalculatorAccuracy.
var (
	CalculatorAccuracy_name = map[int32]string{
		0: "CALCULATOR_ACCURACY_UNSPECIFIED",
		1: "CALCULATOR_ACCURACY_NEAR_ACCURATE",
		2: "CALCULATOR_ACCURACY_ACCURATE",
	}
	CalculatorAccuracy_value = map[string]int32{
		"CALCULATOR_ACCURACY_UNSPECIFIED":   0,
		"CALCULATOR_ACCURACY_NEAR_ACCURATE": 1,
		"CALCULATOR_ACCURACY_ACCURATE":      2,
	}
)

func (x CalculatorAccuracy) Enum() *CalculatorAccuracy {
	p := new(CalculatorAccuracy)
	*p = x
	return p
}

func (x CalculatorAccuracy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CalculatorAccuracy) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_enums_calculator_proto_enumTypes[0].Descriptor()
}

func (CalculatorAccuracy) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_enums_calculator_proto_enumTypes[0]
}

func (x CalculatorAccuracy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CalculatorAccuracy.Descriptor instead.
func (CalculatorAccuracy) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_enums_calculator_proto_rawDescGZIP(), []int{0}
}

var File_api_preapprovedloan_enums_calculator_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_enums_calculator_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2a, 0x82, 0x01, 0x0a, 0x12, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x41, 0x63, 0x63, 0x75, 0x72, 0x61, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x41,
	0x4c, 0x43, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x55, 0x52, 0x41, 0x43,
	0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x25, 0x0a, 0x21, 0x43, 0x41, 0x4c, 0x43, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x43,
	0x43, 0x55, 0x52, 0x41, 0x43, 0x59, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x55,
	0x52, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x4c, 0x43, 0x55, 0x4c,
	0x41, 0x54, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x55, 0x52, 0x41, 0x43, 0x59, 0x5f, 0x41, 0x43,
	0x43, 0x55, 0x52, 0x41, 0x54, 0x45, 0x10, 0x02, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x30, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_enums_calculator_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_enums_calculator_proto_rawDescData = file_api_preapprovedloan_enums_calculator_proto_rawDesc
)

func file_api_preapprovedloan_enums_calculator_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_enums_calculator_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_enums_calculator_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_enums_calculator_proto_rawDescData)
	})
	return file_api_preapprovedloan_enums_calculator_proto_rawDescData
}

var file_api_preapprovedloan_enums_calculator_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_preapprovedloan_enums_calculator_proto_goTypes = []interface{}{
	(CalculatorAccuracy)(0), // 0: preapprovedloan.enums.CalculatorAccuracy
}
var file_api_preapprovedloan_enums_calculator_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_enums_calculator_proto_init() }
func file_api_preapprovedloan_enums_calculator_proto_init() {
	if File_api_preapprovedloan_enums_calculator_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_enums_calculator_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_enums_calculator_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_enums_calculator_proto_depIdxs,
		EnumInfos:         file_api_preapprovedloan_enums_calculator_proto_enumTypes,
	}.Build()
	File_api_preapprovedloan_enums_calculator_proto = out.File
	file_api_preapprovedloan_enums_calculator_proto_rawDesc = nil
	file_api_preapprovedloan_enums_calculator_proto_goTypes = nil
	file_api_preapprovedloan_enums_calculator_proto_depIdxs = nil
}
