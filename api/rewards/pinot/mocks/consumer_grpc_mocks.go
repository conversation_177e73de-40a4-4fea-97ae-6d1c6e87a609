// Code generated by MockGen. DO NOT EDIT.
// Source: api/rewards/pinot/consumer_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	events "github.com/epifi/gamma/api/rewards/events"
	pinot "github.com/epifi/gamma/api/rewards/pinot"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockConsumerClient is a mock of ConsumerClient interface.
type MockConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerClientMockRecorder
}

// MockConsumerClientMockRecorder is the mock recorder for MockConsumerClient.
type MockConsumerClientMockRecorder struct {
	mock *MockConsumerClient
}

// NewMockConsumerClient creates a new mock instance.
func NewMockConsumerClient(ctrl *gomock.Controller) *MockConsumerClient {
	mock := &MockConsumerClient{ctrl: ctrl}
	mock.recorder = &MockConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerClient) EXPECT() *MockConsumerClientMockRecorder {
	return m.recorder
}

// ProcessProjectionEventForPinot mocks base method.
func (m *MockConsumerClient) ProcessProjectionEventForPinot(ctx context.Context, in *events.ProjectionEvent, opts ...grpc.CallOption) (*pinot.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessProjectionEventForPinot", varargs...)
	ret0, _ := ret[0].(*pinot.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessProjectionEventForPinot indicates an expected call of ProcessProjectionEventForPinot.
func (mr *MockConsumerClientMockRecorder) ProcessProjectionEventForPinot(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessProjectionEventForPinot", reflect.TypeOf((*MockConsumerClient)(nil).ProcessProjectionEventForPinot), varargs...)
}

// ProcessTerminalRewardEventsForPinot mocks base method.
func (m *MockConsumerClient) ProcessTerminalRewardEventsForPinot(ctx context.Context, in *events.RewardStatusUpdateEvent, opts ...grpc.CallOption) (*pinot.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessTerminalRewardEventsForPinot", varargs...)
	ret0, _ := ret[0].(*pinot.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessTerminalRewardEventsForPinot indicates an expected call of ProcessTerminalRewardEventsForPinot.
func (mr *MockConsumerClientMockRecorder) ProcessTerminalRewardEventsForPinot(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessTerminalRewardEventsForPinot", reflect.TypeOf((*MockConsumerClient)(nil).ProcessTerminalRewardEventsForPinot), varargs...)
}

// MockConsumerServer is a mock of ConsumerServer interface.
type MockConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockConsumerServerMockRecorder
}

// MockConsumerServerMockRecorder is the mock recorder for MockConsumerServer.
type MockConsumerServerMockRecorder struct {
	mock *MockConsumerServer
}

// NewMockConsumerServer creates a new mock instance.
func NewMockConsumerServer(ctrl *gomock.Controller) *MockConsumerServer {
	mock := &MockConsumerServer{ctrl: ctrl}
	mock.recorder = &MockConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConsumerServer) EXPECT() *MockConsumerServerMockRecorder {
	return m.recorder
}

// ProcessProjectionEventForPinot mocks base method.
func (m *MockConsumerServer) ProcessProjectionEventForPinot(arg0 context.Context, arg1 *events.ProjectionEvent) (*pinot.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessProjectionEventForPinot", arg0, arg1)
	ret0, _ := ret[0].(*pinot.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessProjectionEventForPinot indicates an expected call of ProcessProjectionEventForPinot.
func (mr *MockConsumerServerMockRecorder) ProcessProjectionEventForPinot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessProjectionEventForPinot", reflect.TypeOf((*MockConsumerServer)(nil).ProcessProjectionEventForPinot), arg0, arg1)
}

// ProcessTerminalRewardEventsForPinot mocks base method.
func (m *MockConsumerServer) ProcessTerminalRewardEventsForPinot(arg0 context.Context, arg1 *events.RewardStatusUpdateEvent) (*pinot.ConsumerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessTerminalRewardEventsForPinot", arg0, arg1)
	ret0, _ := ret[0].(*pinot.ConsumerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessTerminalRewardEventsForPinot indicates an expected call of ProcessTerminalRewardEventsForPinot.
func (mr *MockConsumerServerMockRecorder) ProcessTerminalRewardEventsForPinot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessTerminalRewardEventsForPinot", reflect.TypeOf((*MockConsumerServer)(nil).ProcessTerminalRewardEventsForPinot), arg0, arg1)
}

// MockUnsafeConsumerServer is a mock of UnsafeConsumerServer interface.
type MockUnsafeConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeConsumerServerMockRecorder
}

// MockUnsafeConsumerServerMockRecorder is the mock recorder for MockUnsafeConsumerServer.
type MockUnsafeConsumerServerMockRecorder struct {
	mock *MockUnsafeConsumerServer
}

// NewMockUnsafeConsumerServer creates a new mock instance.
func NewMockUnsafeConsumerServer(ctrl *gomock.Controller) *MockUnsafeConsumerServer {
	mock := &MockUnsafeConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeConsumerServer) EXPECT() *MockUnsafeConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedConsumerServer mocks base method.
func (m *MockUnsafeConsumerServer) mustEmbedUnimplementedConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedConsumerServer")
}

// mustEmbedUnimplementedConsumerServer indicates an expected call of mustEmbedUnimplementedConsumerServer.
func (mr *MockUnsafeConsumerServerMockRecorder) mustEmbedUnimplementedConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedConsumerServer", reflect.TypeOf((*MockUnsafeConsumerServer)(nil).mustEmbedUnimplementedConsumerServer))
}
