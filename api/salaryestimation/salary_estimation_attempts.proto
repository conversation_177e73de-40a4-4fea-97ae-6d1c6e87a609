//go:generate gen_sql -types=Source,AttemptStatus
syntax = "proto3";

package salaryestimation;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/salaryestimation";
option java_package = "com.github.epifi.gamma.api.salaryestimation";

enum Source {
  SOURCE_UNSPECIFIED = 0;

  SOURCE_AA = 1;
}

// Status enum for salary estimation attempts
enum AttemptStatus {
  ATTEMPT_STATUS_UNSPECIFIED = 0;
  ATTEMPT_STATUS_INITIATED = 1;
  ATTEMPT_STATUS_DATA_SHARING_IN_PROGRESS = 2;
  ATTEMPT_STATUS_DATA_SHARING_COMPLETED = 3;
  ATTEMPT_STATUS_DATA_SHARING_FAILED = 4;
  ATTEMPT_STATUS_ANALYSIS_IN_PROGRESS = 5;
  ATTEMPT_STATUS_ANALYSIS_COMPLETED = 6;
  ATTEMPT_STATUS_ANALYSIS_FAILED = 7;
  ATTEMPT_STATUS_COMPLETED = 8;
  ATTEMPT_STATUS_FAILED = 9;
  ATTEMPT_STATUS_SKIPPED = 10;
}

// SalaryEstimationAttempt represents a salary estimation attempt record
message SalaryEstimationAttempt {
  // Unique identifier for the salary estimation attempt
  string id = 1;

  // Client request ID - unique identifier provided by the client
  string client_req_id = 2 [(validate.rules).string.min_len = 1];

  // Source of the salary estimation attempt
  Source source = 3;

  // Actor ID associated with the attempt
  string actor_id = 4 [(validate.rules).string.min_len = 1];

  // Current status of the attempt
  AttemptStatus status = 5;

  // Timestamp when the record was created
  google.protobuf.Timestamp created_at = 6;

  // Timestamp when the record was last updated
  google.protobuf.Timestamp updated_at = 7;

  // Timestamp when the record was deleted (soft delete)
  google.protobuf.Timestamp deleted_at = 8;
}

// Field mask for SalaryEstimationAttempt updates
enum SalaryEstimationAttemptFieldMask {
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED = 0;
  SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS = 1;
}
