// Code generated by MockGen. DO NOT EDIT.
// Source: api/salaryestimation/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	salaryestimation "github.com/epifi/gamma/api/salaryestimation"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockSalaryEstimationClient is a mock of SalaryEstimationClient interface.
type MockSalaryEstimationClient struct {
	ctrl     *gomock.Controller
	recorder *MockSalaryEstimationClientMockRecorder
}

// MockSalaryEstimationClientMockRecorder is the mock recorder for MockSalaryEstimationClient.
type MockSalaryEstimationClientMockRecorder struct {
	mock *MockSalaryEstimationClient
}

// NewMockSalaryEstimationClient creates a new mock instance.
func NewMockSalaryEstimationClient(ctrl *gomock.Controller) *MockSalaryEstimationClient {
	mock := &MockSalaryEstimationClient{ctrl: ctrl}
	mock.recorder = &MockSalaryEstimationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSalaryEstimationClient) EXPECT() *MockSalaryEstimationClientMockRecorder {
	return m.recorder
}

// ComputeSalary mocks base method.
func (m *MockSalaryEstimationClient) ComputeSalary(ctx context.Context, in *salaryestimation.ComputeSalaryRequest, opts ...grpc.CallOption) (*salaryestimation.ComputeSalaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ComputeSalary", varargs...)
	ret0, _ := ret[0].(*salaryestimation.ComputeSalaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ComputeSalary indicates an expected call of ComputeSalary.
func (mr *MockSalaryEstimationClientMockRecorder) ComputeSalary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ComputeSalary", reflect.TypeOf((*MockSalaryEstimationClient)(nil).ComputeSalary), varargs...)
}

// GetSalary mocks base method.
func (m *MockSalaryEstimationClient) GetSalary(ctx context.Context, in *salaryestimation.GetSalaryRequest, opts ...grpc.CallOption) (*salaryestimation.GetSalaryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSalary", varargs...)
	ret0, _ := ret[0].(*salaryestimation.GetSalaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSalary indicates an expected call of GetSalary.
func (mr *MockSalaryEstimationClientMockRecorder) GetSalary(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSalary", reflect.TypeOf((*MockSalaryEstimationClient)(nil).GetSalary), varargs...)
}

// MockSalaryEstimationServer is a mock of SalaryEstimationServer interface.
type MockSalaryEstimationServer struct {
	ctrl     *gomock.Controller
	recorder *MockSalaryEstimationServerMockRecorder
}

// MockSalaryEstimationServerMockRecorder is the mock recorder for MockSalaryEstimationServer.
type MockSalaryEstimationServerMockRecorder struct {
	mock *MockSalaryEstimationServer
}

// NewMockSalaryEstimationServer creates a new mock instance.
func NewMockSalaryEstimationServer(ctrl *gomock.Controller) *MockSalaryEstimationServer {
	mock := &MockSalaryEstimationServer{ctrl: ctrl}
	mock.recorder = &MockSalaryEstimationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSalaryEstimationServer) EXPECT() *MockSalaryEstimationServerMockRecorder {
	return m.recorder
}

// ComputeSalary mocks base method.
func (m *MockSalaryEstimationServer) ComputeSalary(arg0 context.Context, arg1 *salaryestimation.ComputeSalaryRequest) (*salaryestimation.ComputeSalaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ComputeSalary", arg0, arg1)
	ret0, _ := ret[0].(*salaryestimation.ComputeSalaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ComputeSalary indicates an expected call of ComputeSalary.
func (mr *MockSalaryEstimationServerMockRecorder) ComputeSalary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ComputeSalary", reflect.TypeOf((*MockSalaryEstimationServer)(nil).ComputeSalary), arg0, arg1)
}

// GetSalary mocks base method.
func (m *MockSalaryEstimationServer) GetSalary(arg0 context.Context, arg1 *salaryestimation.GetSalaryRequest) (*salaryestimation.GetSalaryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSalary", arg0, arg1)
	ret0, _ := ret[0].(*salaryestimation.GetSalaryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSalary indicates an expected call of GetSalary.
func (mr *MockSalaryEstimationServerMockRecorder) GetSalary(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSalary", reflect.TypeOf((*MockSalaryEstimationServer)(nil).GetSalary), arg0, arg1)
}

// MockUnsafeSalaryEstimationServer is a mock of UnsafeSalaryEstimationServer interface.
type MockUnsafeSalaryEstimationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeSalaryEstimationServerMockRecorder
}

// MockUnsafeSalaryEstimationServerMockRecorder is the mock recorder for MockUnsafeSalaryEstimationServer.
type MockUnsafeSalaryEstimationServerMockRecorder struct {
	mock *MockUnsafeSalaryEstimationServer
}

// NewMockUnsafeSalaryEstimationServer creates a new mock instance.
func NewMockUnsafeSalaryEstimationServer(ctrl *gomock.Controller) *MockUnsafeSalaryEstimationServer {
	mock := &MockUnsafeSalaryEstimationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeSalaryEstimationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeSalaryEstimationServer) EXPECT() *MockUnsafeSalaryEstimationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedSalaryEstimationServer mocks base method.
func (m *MockUnsafeSalaryEstimationServer) mustEmbedUnimplementedSalaryEstimationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedSalaryEstimationServer")
}

// mustEmbedUnimplementedSalaryEstimationServer indicates an expected call of mustEmbedUnimplementedSalaryEstimationServer.
func (mr *MockUnsafeSalaryEstimationServerMockRecorder) mustEmbedUnimplementedSalaryEstimationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedSalaryEstimationServer", reflect.TypeOf((*MockUnsafeSalaryEstimationServer)(nil).mustEmbedUnimplementedSalaryEstimationServer))
}
