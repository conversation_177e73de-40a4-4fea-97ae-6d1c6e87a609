syntax = "proto3";

package salaryprogram;

import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "api/salaryprogram/referrals/seasons.proto";

option go_package = "github.com/epifi/gamma/api/salaryprogram/referrals";
option java_package = "com.github.epifi.gamma.api.salaryprogram.referrals";

service Referrals {
  // rpc to create a season.
  rpc CreateSeason(CreateSeasonsRequest)  returns (CreateSeasonsResponse);
  // rpc to fetch a season with filters.
  rpc GetSeasons(GetSeasonsRequest) returns (GetSeasonsResponse);
  // rpc to update a season
  rpc UpdateSeason(UpdateSeasonRequest) returns (UpdateSeasonResponse);
}

message CreateSeasonsRequest {
  // display details for the season.
  SeasonDisplayMeta display_meta = 1 [(validate.rules).message.required = true];
  // milestones of the season.
  SeasonMilestones milestones = 2;
  // timestamp from which season should be displayed.
  google.protobuf.Timestamp display_since = 3 [(validate.rules).timestamp.required = true];
  // timestamp till which season should be displayed.
  google.protobuf.Timestamp display_till = 4 [(validate.rules).timestamp.required = true];
  // timestamp from which season is active.
  google.protobuf.Timestamp active_since = 5 [(validate.rules).timestamp.required = true];
  // timestamp till which season is active.
  google.protobuf.Timestamp active_till = 6 [(validate.rules).timestamp.required = true];
}

message CreateSeasonsResponse {
  rpc.Status status = 1;
  // season which was just created
  ReferralsSeason season = 2;
}

message GetSeasonsRequest {
  // season id
  string id = 1;
  google.protobuf.Timestamp display_since = 2;
  google.protobuf.Timestamp display_till = 3;
  google.protobuf.Timestamp active_since = 4;
  google.protobuf.Timestamp active_till = 5;
}

message GetSeasonsResponse {
  rpc.Status status = 1;
  // list of seasons
  repeated ReferralsSeason seasons = 2;
}

message UpdateSeasonRequest {
  // referral season details that need to be updated
  // entry with passed ReferralsSeason.Id will be updated
  ReferralsSeason referrals_season= 1;
  // list of fields which need to be updated
  repeated ReferralsSeasonFieldMask update_referrals_season_field_masks = 2;
}

message UpdateSeasonResponse {
  rpc.Status status = 1;
  // updated referral season
  ReferralsSeason updated_referrals_season = 2;
}

// enum fields corresponding to columns allowed to update in ReferralsSeason model
enum ReferralsSeasonFieldMask {
  REFERRALS_SEASON_FIELD_MASK_UNSPECIFIED = 0;
  SEASON_DISPLAY_META = 1;
  SEASON_MILESTONES = 2;
  SEASON_DISPLAY_SINCE = 3;
  SEASON_DISPLAY_TILL = 4;
  SEASON_ACTIVE_SINCE = 5;
  SEASON_ACTIVE_TILL = 6;
}
