// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/salaryprogram/referrals/service.proto

package referrals

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Referrals_CreateSeason_FullMethodName = "/salaryprogram.Referrals/CreateSeason"
	Referrals_GetSeasons_FullMethodName   = "/salaryprogram.Referrals/GetSeasons"
	Referrals_UpdateSeason_FullMethodName = "/salaryprogram.Referrals/UpdateSeason"
)

// ReferralsClient is the client API for Referrals service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ReferralsClient interface {
	// rpc to create a season.
	CreateSeason(ctx context.Context, in *CreateSeasonsRequest, opts ...grpc.CallOption) (*CreateSeasonsResponse, error)
	// rpc to fetch a season with filters.
	GetSeasons(ctx context.Context, in *GetSeasonsRequest, opts ...grpc.CallOption) (*GetSeasonsResponse, error)
	// rpc to update a season
	UpdateSeason(ctx context.Context, in *UpdateSeasonRequest, opts ...grpc.CallOption) (*UpdateSeasonResponse, error)
}

type referralsClient struct {
	cc grpc.ClientConnInterface
}

func NewReferralsClient(cc grpc.ClientConnInterface) ReferralsClient {
	return &referralsClient{cc}
}

func (c *referralsClient) CreateSeason(ctx context.Context, in *CreateSeasonsRequest, opts ...grpc.CallOption) (*CreateSeasonsResponse, error) {
	out := new(CreateSeasonsResponse)
	err := c.cc.Invoke(ctx, Referrals_CreateSeason_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *referralsClient) GetSeasons(ctx context.Context, in *GetSeasonsRequest, opts ...grpc.CallOption) (*GetSeasonsResponse, error) {
	out := new(GetSeasonsResponse)
	err := c.cc.Invoke(ctx, Referrals_GetSeasons_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *referralsClient) UpdateSeason(ctx context.Context, in *UpdateSeasonRequest, opts ...grpc.CallOption) (*UpdateSeasonResponse, error) {
	out := new(UpdateSeasonResponse)
	err := c.cc.Invoke(ctx, Referrals_UpdateSeason_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReferralsServer is the server API for Referrals service.
// All implementations should embed UnimplementedReferralsServer
// for forward compatibility
type ReferralsServer interface {
	// rpc to create a season.
	CreateSeason(context.Context, *CreateSeasonsRequest) (*CreateSeasonsResponse, error)
	// rpc to fetch a season with filters.
	GetSeasons(context.Context, *GetSeasonsRequest) (*GetSeasonsResponse, error)
	// rpc to update a season
	UpdateSeason(context.Context, *UpdateSeasonRequest) (*UpdateSeasonResponse, error)
}

// UnimplementedReferralsServer should be embedded to have forward compatible implementations.
type UnimplementedReferralsServer struct {
}

func (UnimplementedReferralsServer) CreateSeason(context.Context, *CreateSeasonsRequest) (*CreateSeasonsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSeason not implemented")
}
func (UnimplementedReferralsServer) GetSeasons(context.Context, *GetSeasonsRequest) (*GetSeasonsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSeasons not implemented")
}
func (UnimplementedReferralsServer) UpdateSeason(context.Context, *UpdateSeasonRequest) (*UpdateSeasonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSeason not implemented")
}

// UnsafeReferralsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ReferralsServer will
// result in compilation errors.
type UnsafeReferralsServer interface {
	mustEmbedUnimplementedReferralsServer()
}

func RegisterReferralsServer(s grpc.ServiceRegistrar, srv ReferralsServer) {
	s.RegisterService(&Referrals_ServiceDesc, srv)
}

func _Referrals_CreateSeason_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSeasonsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReferralsServer).CreateSeason(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Referrals_CreateSeason_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReferralsServer).CreateSeason(ctx, req.(*CreateSeasonsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Referrals_GetSeasons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeasonsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReferralsServer).GetSeasons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Referrals_GetSeasons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReferralsServer).GetSeasons(ctx, req.(*GetSeasonsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Referrals_UpdateSeason_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSeasonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReferralsServer).UpdateSeason(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Referrals_UpdateSeason_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReferralsServer).UpdateSeason(ctx, req.(*UpdateSeasonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Referrals_ServiceDesc is the grpc.ServiceDesc for Referrals service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Referrals_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "salaryprogram.Referrals",
	HandlerType: (*ReferralsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSeason",
			Handler:    _Referrals_CreateSeason_Handler,
		},
		{
			MethodName: "GetSeasons",
			Handler:    _Referrals_GetSeasons_Handler,
		},
		{
			MethodName: "UpdateSeason",
			Handler:    _Referrals_UpdateSeason_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/salaryprogram/referrals/service.proto",
}
