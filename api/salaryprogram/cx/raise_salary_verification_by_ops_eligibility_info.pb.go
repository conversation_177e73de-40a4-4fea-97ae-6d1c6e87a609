// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/salaryprogram/cx/raise_salary_verification_by_ops_eligibility_info.proto

package cx

import (
	salaryprogram "github.com/epifi/gamma/api/salaryprogram"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RaiseSalaryVerificationByOpsEligibilityInfo stores the info required for checking the eligibility of actor for raising salary verification request by ops.
type RaiseSalaryVerificationByOpsEligibilityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// denotes the actor for which eligibility details are stored
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// denotes status given to users based on the eligibility for raising manual salary verification.
	EligibilityStatus UserSalaryVerificationEligibilityStatus `protobuf:"varint,3,opt,name=eligibility_status,json=eligibilityStatus,proto3,enum=salaryprogram.cx.UserSalaryVerificationEligibilityStatus" json:"eligibility_status,omitempty"`
	// denotes the time from when the user is salary program active due to last activation entry
	LastActivationFromTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=last_activation_from_time,json=lastActivationFromTime,proto3" json:"last_activation_from_time,omitempty"`
	// denotes the time till when the user is salary program active due to last activation entry
	LastActivationTillTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=last_activation_till_time,json=lastActivationTillTime,proto3" json:"last_activation_till_time,omitempty"`
	// denotes the last verified salary txn timestamp
	LastVerifiedSalaryTxnTimestamp *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=last_verified_salary_txn_timestamp,json=lastVerifiedSalaryTxnTimestamp,proto3" json:"last_verified_salary_txn_timestamp,omitempty"`
	// denotes the source by which last salary txn was verified
	LastVerifiedSalaryTxnVerifiedBy salaryprogram.SalaryTxnVerificationRequestVerifiedBy `protobuf:"varint,7,opt,name=last_verified_salary_txn_verified_by,json=lastVerifiedSalaryTxnVerifiedBy,proto3,enum=salaryprogram.SalaryTxnVerificationRequestVerifiedBy" json:"last_verified_salary_txn_verified_by,omitempty"`
	// time at which user completed salary program registration
	RegistrationCompletionTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=registration_completion_time,json=registrationCompletionTime,proto3" json:"registration_completion_time,omitempty"`
	CreatedAt                  *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt                  *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) Reset() {
	*x = RaiseSalaryVerificationByOpsEligibilityInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RaiseSalaryVerificationByOpsEligibilityInfo) ProtoMessage() {}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RaiseSalaryVerificationByOpsEligibilityInfo.ProtoReflect.Descriptor instead.
func (*RaiseSalaryVerificationByOpsEligibilityInfo) Descriptor() ([]byte, []int) {
	return file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescGZIP(), []int{0}
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetEligibilityStatus() UserSalaryVerificationEligibilityStatus {
	if x != nil {
		return x.EligibilityStatus
	}
	return UserSalaryVerificationEligibilityStatus_ELIGIBILITY_STATUS_UNSPECIFIED
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetLastActivationFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActivationFromTime
	}
	return nil
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetLastActivationTillTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastActivationTillTime
	}
	return nil
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetLastVerifiedSalaryTxnTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastVerifiedSalaryTxnTimestamp
	}
	return nil
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetLastVerifiedSalaryTxnVerifiedBy() salaryprogram.SalaryTxnVerificationRequestVerifiedBy {
	if x != nil {
		return x.LastVerifiedSalaryTxnVerifiedBy
	}
	return salaryprogram.SalaryTxnVerificationRequestVerifiedBy(0)
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetRegistrationCompletionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RegistrationCompletionTime
	}
	return nil
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RaiseSalaryVerificationByOpsEligibilityInfo) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto protoreflect.FileDescriptor

var file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDesc = []byte{
	0x0a, 0x4c, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x2f, 0x63, 0x78, 0x2f, 0x72, 0x61, 0x69, 0x73, 0x65, 0x5f, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x62, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x5f, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10,
	0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x63, 0x78,
	0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x2f, 0x63, 0x78, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x78, 0x6e,
	0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb3, 0x06, 0x0a,
	0x2b, 0x52, 0x61, 0x69, 0x73, 0x65, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x4f, 0x70, 0x73, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x68, 0x0a, 0x12, 0x65, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x2e, 0x63, 0x78, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11,
	0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x55, 0x0a, 0x19, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x19, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6c, 0x6c,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16, 0x6c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x66, 0x0a, 0x22, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x1e, 0x6c, 0x61, 0x73, 0x74, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x54, 0x78, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x84, 0x01, 0x0a, 0x24, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79,
	0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x54, 0x78, 0x6e,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x42, 0x79, 0x52, 0x1f, 0x6c,
	0x61, 0x73, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x53, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x54, 0x78, 0x6e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x42, 0x79, 0x12, 0x5c,
	0x0a, 0x1c, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x1a, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x63,
	0x78, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61,
	0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x63, 0x78, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescOnce sync.Once
	file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescData = file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDesc
)

func file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescGZIP() []byte {
	file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescOnce.Do(func() {
		file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescData)
	})
	return file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDescData
}

var file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_goTypes = []interface{}{
	(*RaiseSalaryVerificationByOpsEligibilityInfo)(nil),       // 0: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo
	(UserSalaryVerificationEligibilityStatus)(0),              // 1: salaryprogram.cx.UserSalaryVerificationEligibilityStatus
	(*timestamppb.Timestamp)(nil),                             // 2: google.protobuf.Timestamp
	(salaryprogram.SalaryTxnVerificationRequestVerifiedBy)(0), // 3: salaryprogram.SalaryTxnVerificationRequestVerifiedBy
}
var file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_depIdxs = []int32{
	1, // 0: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.eligibility_status:type_name -> salaryprogram.cx.UserSalaryVerificationEligibilityStatus
	2, // 1: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.last_activation_from_time:type_name -> google.protobuf.Timestamp
	2, // 2: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.last_activation_till_time:type_name -> google.protobuf.Timestamp
	2, // 3: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.last_verified_salary_txn_timestamp:type_name -> google.protobuf.Timestamp
	3, // 4: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.last_verified_salary_txn_verified_by:type_name -> salaryprogram.SalaryTxnVerificationRequestVerifiedBy
	2, // 5: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.registration_completion_time:type_name -> google.protobuf.Timestamp
	2, // 6: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.created_at:type_name -> google.protobuf.Timestamp
	2, // 7: salaryprogram.cx.RaiseSalaryVerificationByOpsEligibilityInfo.updated_at:type_name -> google.protobuf.Timestamp
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_init() }
func file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_init() {
	if File_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto != nil {
		return
	}
	file_api_salaryprogram_cx_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RaiseSalaryVerificationByOpsEligibilityInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_goTypes,
		DependencyIndexes: file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_depIdxs,
		MessageInfos:      file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_msgTypes,
	}.Build()
	File_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto = out.File
	file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_rawDesc = nil
	file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_goTypes = nil
	file_api_salaryprogram_cx_raise_salary_verification_by_ops_eligibility_info_proto_depIdxs = nil
}
