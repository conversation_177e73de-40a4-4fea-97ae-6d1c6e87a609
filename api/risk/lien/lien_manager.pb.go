// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/lien/lien_manager.proto

package lien

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	comms "github.com/epifi/gamma/api/comms"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AddLienRequest contains parameters required for adding a lien
type AddLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Account number on which to add lien
	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// Amount to be marked as lien
	Amount float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// Currency code (e.g., INR)
	CurrencyCode string `protobuf:"bytes,3,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// Reason code for the lien
	ReasonCode string `protobuf:"bytes,4,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	// Additional remarks
	Remarks string `protobuf:"bytes,5,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Time from which the lien should start
	StartDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// Time until when the lien should last
	EndDate *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// Channel's request identifier
	ChannelRequestId string `protobuf:"bytes,8,opt,name=channel_request_id,json=channelRequestId,proto3" json:"channel_request_id,omitempty"`
}

func (x *AddLienRequest) Reset() {
	*x = AddLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLienRequest) ProtoMessage() {}

func (x *AddLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLienRequest.ProtoReflect.Descriptor instead.
func (*AddLienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_manager_proto_rawDescGZIP(), []int{0}
}

func (x *AddLienRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *AddLienRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AddLienRequest) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *AddLienRequest) GetReasonCode() string {
	if x != nil {
		return x.ReasonCode
	}
	return ""
}

func (x *AddLienRequest) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *AddLienRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *AddLienRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *AddLienRequest) GetChannelRequestId() string {
	if x != nil {
		return x.ChannelRequestId
	}
	return ""
}

// AddLienResponse contains the response from the add lien operation
type AddLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// CBS status returned by bank API
	CbsStatus string `protobuf:"bytes,3,opt,name=cbs_status,json=cbsStatus,proto3" json:"cbs_status,omitempty"`
	// CBS response returned by bank API
	CbsResponse string `protobuf:"bytes,4,opt,name=cbs_response,json=cbsResponse,proto3" json:"cbs_response,omitempty"`
	// Message returned by the API
	ApiMessage string `protobuf:"bytes,5,opt,name=api_message,json=apiMessage,proto3" json:"api_message,omitempty"`
}

func (x *AddLienResponse) Reset() {
	*x = AddLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLienResponse) ProtoMessage() {}

func (x *AddLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLienResponse.ProtoReflect.Descriptor instead.
func (*AddLienResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_manager_proto_rawDescGZIP(), []int{1}
}

func (x *AddLienResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AddLienResponse) GetCbsStatus() string {
	if x != nil {
		return x.CbsStatus
	}
	return ""
}

func (x *AddLienResponse) GetCbsResponse() string {
	if x != nil {
		return x.CbsResponse
	}
	return ""
}

func (x *AddLienResponse) GetApiMessage() string {
	if x != nil {
		return x.ApiMessage
	}
	return ""
}

// EnquireLienRequest contains parameters required for enquiring a lien
type EnquireLienRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Account number for which to enquire lien
	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	RequestId     string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *EnquireLienRequest) Reset() {
	*x = EnquireLienRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireLienRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireLienRequest) ProtoMessage() {}

func (x *EnquireLienRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireLienRequest.ProtoReflect.Descriptor instead.
func (*EnquireLienRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_manager_proto_rawDescGZIP(), []int{2}
}

func (x *EnquireLienRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *EnquireLienRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// EnquireLienResponse contains the response from the enquire lien operation
type EnquireLienResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the lien request
	LienId string `protobuf:"bytes,1,opt,name=lien_id,json=lienId,proto3" json:"lien_id,omitempty"`
	// Message returned by the API
	ApiMessage string `protobuf:"bytes,2,opt,name=api_message,json=apiMessage,proto3" json:"api_message,omitempty"`
	// Details of the lien
	LienDetails *LienDetails `protobuf:"bytes,3,opt,name=lien_details,json=lienDetails,proto3" json:"lien_details,omitempty"`
}

func (x *EnquireLienResponse) Reset() {
	*x = EnquireLienResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnquireLienResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnquireLienResponse) ProtoMessage() {}

func (x *EnquireLienResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnquireLienResponse.ProtoReflect.Descriptor instead.
func (*EnquireLienResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_manager_proto_rawDescGZIP(), []int{3}
}

func (x *EnquireLienResponse) GetLienId() string {
	if x != nil {
		return x.LienId
	}
	return ""
}

func (x *EnquireLienResponse) GetApiMessage() string {
	if x != nil {
		return x.ApiMessage
	}
	return ""
}

func (x *EnquireLienResponse) GetLienDetails() *LienDetails {
	if x != nil {
		return x.LienDetails
	}
	return nil
}

// LienDetails contains the details of a lien
type LienDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Amount marked as lien
	Amount float64 `protobuf:"fixed64,1,opt,name=amount,proto3" json:"amount,omitempty"`
	// Currency code (e.g., INR)
	CurrencyCode string `protobuf:"bytes,2,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// Reason code for the lien
	ReasonCode string `protobuf:"bytes,3,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	// Additional remarks
	Remarks string `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// Time from which the lien started
	StartDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// Time until when the lien lasts
	EndDate *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *LienDetails) Reset() {
	*x = LienDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LienDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LienDetails) ProtoMessage() {}

func (x *LienDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LienDetails.ProtoReflect.Descriptor instead.
func (*LienDetails) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_manager_proto_rawDescGZIP(), []int{4}
}

func (x *LienDetails) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *LienDetails) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *LienDetails) GetReasonCode() string {
	if x != nil {
		return x.ReasonCode
	}
	return ""
}

func (x *LienDetails) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *LienDetails) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *LienDetails) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

type GetCommsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Account number for which to get communications
	AccountNumber string `protobuf:"bytes,1,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// time from which the lien will be applied
	StartDate *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// time until which the lien will be applied
	EndDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// amount that is marked as lien for the account
	Amount *money.Money `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *GetCommsRequest) Reset() {
	*x = GetCommsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommsRequest) ProtoMessage() {}

func (x *GetCommsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommsRequest.ProtoReflect.Descriptor instead.
func (*GetCommsRequest) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_manager_proto_rawDescGZIP(), []int{5}
}

func (x *GetCommsRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *GetCommsRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *GetCommsRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *GetCommsRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type GetCommsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Communications []*comms.Communication `protobuf:"bytes,1,rep,name=communications,proto3" json:"communications,omitempty"`
}

func (x *GetCommsResponse) Reset() {
	*x = GetCommsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_lien_lien_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommsResponse) ProtoMessage() {}

func (x *GetCommsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_lien_lien_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommsResponse.ProtoReflect.Descriptor instead.
func (*GetCommsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_lien_lien_manager_proto_rawDescGZIP(), []int{6}
}

func (x *GetCommsResponse) GetCommunications() []*comms.Communication {
	if x != nil {
		return x.Communications
	}
	return nil
}

var File_api_risk_lien_lien_manager_proto protoreflect.FileDescriptor

var file_api_risk_lien_lien_manager_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x69, 0x65, 0x6e, 0x2f,
	0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c, 0x69, 0x65, 0x6e, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd8, 0x02, 0x0a, 0x0e, 0x41, 0x64,
	0x64, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x22, 0x84, 0x01, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x4c, 0x69, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x62, 0x73, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x62,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x62, 0x73, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x69, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x69, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x6c, 0x0a, 0x12, 0x45,
	0x6e, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x26, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x13, 0x45, 0x6e,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x4c, 0x69, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x6c, 0x69, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70,
	0x69, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x70, 0x69, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0c, 0x6c,
	0x69, 0x65, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c, 0x69, 0x65, 0x6e, 0x2e, 0x4c, 0x69,
	0x65, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x6c, 0x69, 0x65, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xf7, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x65, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65,
	0x22, 0xd6, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x50, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a,
	0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x4c, 0x0a, 0x24, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x6c,
	0x69, 0x65, 0x6e, 0x5a, 0x24, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x69, 0x73, 0x6b, 0x2f, 0x6c, 0x69, 0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_risk_lien_lien_manager_proto_rawDescOnce sync.Once
	file_api_risk_lien_lien_manager_proto_rawDescData = file_api_risk_lien_lien_manager_proto_rawDesc
)

func file_api_risk_lien_lien_manager_proto_rawDescGZIP() []byte {
	file_api_risk_lien_lien_manager_proto_rawDescOnce.Do(func() {
		file_api_risk_lien_lien_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_lien_lien_manager_proto_rawDescData)
	})
	return file_api_risk_lien_lien_manager_proto_rawDescData
}

var file_api_risk_lien_lien_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_risk_lien_lien_manager_proto_goTypes = []interface{}{
	(*AddLienRequest)(nil),        // 0: risk.lien.AddLienRequest
	(*AddLienResponse)(nil),       // 1: risk.lien.AddLienResponse
	(*EnquireLienRequest)(nil),    // 2: risk.lien.EnquireLienRequest
	(*EnquireLienResponse)(nil),   // 3: risk.lien.EnquireLienResponse
	(*LienDetails)(nil),           // 4: risk.lien.LienDetails
	(*GetCommsRequest)(nil),       // 5: risk.lien.GetCommsRequest
	(*GetCommsResponse)(nil),      // 6: risk.lien.GetCommsResponse
	(*timestamppb.Timestamp)(nil), // 7: google.protobuf.Timestamp
	(*money.Money)(nil),           // 8: google.type.Money
	(*comms.Communication)(nil),   // 9: comms.Communication
}
var file_api_risk_lien_lien_manager_proto_depIdxs = []int32{
	7, // 0: risk.lien.AddLienRequest.start_date:type_name -> google.protobuf.Timestamp
	7, // 1: risk.lien.AddLienRequest.end_date:type_name -> google.protobuf.Timestamp
	4, // 2: risk.lien.EnquireLienResponse.lien_details:type_name -> risk.lien.LienDetails
	7, // 3: risk.lien.LienDetails.start_date:type_name -> google.protobuf.Timestamp
	7, // 4: risk.lien.LienDetails.end_date:type_name -> google.protobuf.Timestamp
	7, // 5: risk.lien.GetCommsRequest.start_date:type_name -> google.protobuf.Timestamp
	7, // 6: risk.lien.GetCommsRequest.end_date:type_name -> google.protobuf.Timestamp
	8, // 7: risk.lien.GetCommsRequest.amount:type_name -> google.type.Money
	9, // 8: risk.lien.GetCommsResponse.communications:type_name -> comms.Communication
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_api_risk_lien_lien_manager_proto_init() }
func file_api_risk_lien_lien_manager_proto_init() {
	if File_api_risk_lien_lien_manager_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_lien_lien_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lien_lien_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lien_lien_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireLienRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lien_lien_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnquireLienResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lien_lien_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LienDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lien_lien_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_lien_lien_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_lien_lien_manager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_lien_lien_manager_proto_goTypes,
		DependencyIndexes: file_api_risk_lien_lien_manager_proto_depIdxs,
		MessageInfos:      file_api_risk_lien_lien_manager_proto_msgTypes,
	}.Build()
	File_api_risk_lien_lien_manager_proto = out.File
	file_api_risk_lien_lien_manager_proto_rawDesc = nil
	file_api_risk_lien_lien_manager_proto_goTypes = nil
	file_api_risk_lien_lien_manager_proto_depIdxs = nil
}
