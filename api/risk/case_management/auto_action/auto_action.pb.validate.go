// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/auto_action/auto_action.proto

package auto_action

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	case_management "github.com/epifi/gamma/api/risk/case_management"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = case_management.SuggestedActionType(0)
)

// Validate checks the field values on AutoAction with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AutoAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AutoAction with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AutoActionMultiError, or
// nil if none found.
func (m *AutoAction) ValidateAll() error {
	return m.validate(true)
}

func (m *AutoAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuggestedActionType

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoActionValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoActionValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoActionValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetActionParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoActionValidationError{
					field:  "ActionParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoActionValidationError{
					field:  "ActionParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActionParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoActionValidationError{
				field:  "ActionParameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AutoActionMultiError(errors)
	}

	return nil
}

// AutoActionMultiError is an error wrapping multiple validation errors
// returned by AutoAction.ValidateAll() if the designated constraints aren't met.
type AutoActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AutoActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AutoActionMultiError) AllErrors() []error { return m }

// AutoActionValidationError is the validation error returned by
// AutoAction.Validate if the designated constraints aren't met.
type AutoActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AutoActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AutoActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AutoActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AutoActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AutoActionValidationError) ErrorName() string { return "AutoActionValidationError" }

// Error satisfies the builtin error interface
func (e AutoActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAutoAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AutoActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AutoActionValidationError{}

// Validate checks the field values on DeduplicatedReviewAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeduplicatedReviewAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeduplicatedReviewAction with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeduplicatedReviewActionMultiError, or nil if none found.
func (m *DeduplicatedReviewAction) ValidateAll() error {
	return m.validate(true)
}

func (m *DeduplicatedReviewAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPerformReviewActionRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "PerformReviewActionRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "PerformReviewActionRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPerformReviewActionRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeduplicatedReviewActionValidationError{
				field:  "PerformReviewActionRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDedupeComment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "DedupeComment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "DedupeComment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedupeComment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeduplicatedReviewActionValidationError{
				field:  "DedupeComment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotifications()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "Notifications",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "Notifications",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotifications()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeduplicatedReviewActionValidationError{
				field:  "Notifications",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExclusionComment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "ExclusionComment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeduplicatedReviewActionValidationError{
					field:  "ExclusionComment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExclusionComment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeduplicatedReviewActionValidationError{
				field:  "ExclusionComment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldExclude

	if len(errors) > 0 {
		return DeduplicatedReviewActionMultiError(errors)
	}

	return nil
}

// DeduplicatedReviewActionMultiError is an error wrapping multiple validation
// errors returned by DeduplicatedReviewAction.ValidateAll() if the designated
// constraints aren't met.
type DeduplicatedReviewActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeduplicatedReviewActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeduplicatedReviewActionMultiError) AllErrors() []error { return m }

// DeduplicatedReviewActionValidationError is the validation error returned by
// DeduplicatedReviewAction.Validate if the designated constraints aren't met.
type DeduplicatedReviewActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeduplicatedReviewActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeduplicatedReviewActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeduplicatedReviewActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeduplicatedReviewActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeduplicatedReviewActionValidationError) ErrorName() string {
	return "DeduplicatedReviewActionValidationError"
}

// Error satisfies the builtin error interface
func (e DeduplicatedReviewActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeduplicatedReviewAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeduplicatedReviewActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeduplicatedReviewActionValidationError{}
