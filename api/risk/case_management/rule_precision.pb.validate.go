// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/rule_precision.proto

package case_management

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RulePrecision with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RulePrecision) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RulePrecision with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RulePrecisionMultiError, or
// nil if none found.
func (m *RulePrecision) ValidateAll() error {
	return m.validate(true)
}

func (m *RulePrecision) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetRuleId()) < 1 {
		err := RulePrecisionValidationError{
			field:  "RuleId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Version

	if val := m.GetPrecision(); val < 0 || val > 1 {
		err := RulePrecisionValidationError{
			field:  "Precision",
			reason: "value must be inside range [0, 1]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RulePrecisionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RulePrecisionValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RulePrecisionValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RulePrecisionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RulePrecisionValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RulePrecisionValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	if len(errors) > 0 {
		return RulePrecisionMultiError(errors)
	}

	return nil
}

// RulePrecisionMultiError is an error wrapping multiple validation errors
// returned by RulePrecision.ValidateAll() if the designated constraints
// aren't met.
type RulePrecisionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RulePrecisionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RulePrecisionMultiError) AllErrors() []error { return m }

// RulePrecisionValidationError is the validation error returned by
// RulePrecision.Validate if the designated constraints aren't met.
type RulePrecisionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RulePrecisionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RulePrecisionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RulePrecisionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RulePrecisionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RulePrecisionValidationError) ErrorName() string { return "RulePrecisionValidationError" }

// Error satisfies the builtin error interface
func (e RulePrecisionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRulePrecision.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RulePrecisionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RulePrecisionValidationError{}
