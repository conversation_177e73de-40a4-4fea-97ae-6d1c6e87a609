package review

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.com/epifi/gamma/api/risk/case_management/enums"
	vgCrmPb "github.com/epifi/gamma/api/vendorgateway/crm"
	vgRiskPb "github.com/epifi/gamma/api/vendorgateway/crm/risk"
)

var (
	caseFieldToTicketFieldMap = map[CaseFieldMask]vgRiskPb.RiskTicketFieldMask{
		CaseFieldMask_CASE_FIELD_MASK_CREATED_AT: vgRiskPb.RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_CREATED_AT,
		CaseFieldMask_CASE_FIELD_MASK_UPDATED_AT: vgRiskPb.RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_UPDATED_AT,
	}

	priorityMap = map[Priority]vgRiskPb.Priority{
		Priority_PRIORITY_UNSPECIFIED: vgRiskPb.Priority_PRIORITY_UNSPECIFIED,
		Priority_PRIORITY_CRITICAL:    vgRiskPb.Priority_PRIORITY_CRITICAL,
		Priority_PRIORITY_HIGH:        vgRiskPb.Priority_PRIORITY_HIGH,
		Priority_PRIORITY_MEDIUM:      vgRiskPb.Priority_PRIORITY_MEDIUM,
		Priority_PRIORITY_LOW:         vgRiskPb.Priority_PRIORITY_LOW,
	}
	priorityReverseMap = lo.Invert(priorityMap)

	statusMap = map[Status]vgRiskPb.Status{
		Status_STATUS_UNSPECIFIED:               vgRiskPb.Status_STATUS_UNSPECIFIED,
		Status_STATUS_CREATED:                   vgRiskPb.Status_STATUS_CREATED,
		Status_STATUS_ASSIGNED:                  vgRiskPb.Status_STATUS_ASSIGNED,
		Status_STATUS_CLOSED:                    vgRiskPb.Status_STATUS_WONT_REVIEW,
		Status_STATUS_RESOLVED:                  vgRiskPb.Status_STATUS_DONE,
		Status_STATUS_IN_QA_REVIEW:              vgRiskPb.Status_STATUS_IN_QA_REVIEW,
		Status_STATUS_IN_REVIEW:                 vgRiskPb.Status_STATUS_IN_REVIEW,
		Status_STATUS_MANUAL_INTERVENTION:       vgRiskPb.Status_STATUS_MANUAL_INTERVENTION,
		Status_STATUS_PENDING_USER_INFO:         vgRiskPb.Status_STATUS_PENDING_USER_INFO,
		Status_STATUS_REVIEW_ACTION_IN_PROGRESS: vgRiskPb.Status_STATUS_REVIEW_ACTION_IN_PROGRESS,
		Status_STATUS_MARKED_FOR_AUTO_ACTION:    vgRiskPb.Status_STATUS_MARKED_FOR_AUTO_ACTION,
		Status_STATUS_PENDING_ON_USER:           vgRiskPb.Status_STATUS_PENDING_ON_USER,
	}
	statusReverseMap = lo.Invert(statusMap)

	reviewTypeMap = map[ReviewType]vgRiskPb.ReviewType{
		ReviewType_REVIEW_TYPE_UNSPECIFIED:          vgRiskPb.ReviewType_REVIEW_TYPE_UNSPECIFIED,
		ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW:   vgRiskPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
		ReviewType_REVIEW_TYPE_USER_REVIEW:          vgRiskPb.ReviewType_REVIEW_TYPE_USER_REVIEW,
		ReviewType_REVIEW_TYPE_AFU_REVIEW:           vgRiskPb.ReviewType_REVIEW_TYPE_AFU_REVIEW,
		ReviewType_REVIEW_TYPE_LEA_COMPLAINT_REVIEW: vgRiskPb.ReviewType_REVIEW_TYPE_LEA_COMPLAINT_REVIEW,
		ReviewType_REVIEW_TYPE_ESCALATION_REVIEW:    vgRiskPb.ReviewType_REVIEW_TYPE_ESCALATION_REVIEW,
	}
	reviewTypeReverseMap = lo.Invert(reviewTypeMap)

	ErrNilObject     = errors.New("case object is nil")
	ErrInvalidCaseId = errors.New("case id is invalid")
)

// ToVGProto will convert Case proto to CRM ticket proto
// crmUserId parameter will need to be passed separately to abstract what user identifier
// should be used in CRM from scope of this method
func (c *Case) ToVGProto(crmUserId string, agentId uint64) (*vgCrmPb.Ticket, error) {
	if c == nil {
		return nil, ErrNilObject
	}
	var (
		caseId int64
		err    error
	)
	// parse case id to uint64 if present
	if c.GetId() != "" {
		caseId, err = strconv.ParseInt(c.GetId(), 10, 64)
		if err != nil {
			return nil, errors.Wrap(err, ErrInvalidCaseId.Error())
		}
	}
	return &vgCrmPb.Ticket{
		Ticket: &vgCrmPb.Ticket_RiskTicket{
			RiskTicket: &vgRiskPb.RiskTicket{
				Id:                       uint64(caseId),
				UniqueExternalUserId:     crmUserId,
				IsSample:                 c.GetIsSample(),
				Priority:                 c.GetPriority().toCRMProto(),
				Status:                   c.GetStatus().toCRMProto(),
				ReviewType:               c.GetReviewType().toCRMProto(),
				Verdict:                  c.GetVerdict().ToCRMProto(),
				Tags:                     c.GetTags(),
				SnoozedTill:              c.GetSnoozedTill(),
				ConfidenceScore:          c.GetConfidenceScore(),
				AssignedAgentId:          agentId,
				LastAssignedAnalystEmail: c.GetLastAssignedAnalystEmail(),
				AgentGroup:               analystGroupMap[c.GetAnalystGroup()],
			},
		},
	}, nil
}

// getBECasePbFromVGPb will convert CRM ticket proto to Case proto
// actorID parameter will need to be passed separately to abstract conversion from crm user id to actor id from scope of this method
// similarly assignedToEmail also needs to be passed since ticket object only contains agent id and not email
func GetBECasePbFromVGPb(crmCase *vgCrmPb.Ticket, actorId string, assignedToEmail string) (*Case, error) {
	riskCase := crmCase.GetRiskTicket()
	if riskCase == nil {
		return nil, ErrNilObject
	}
	return &Case{
		Id:                       strconv.FormatInt(int64(riskCase.GetId()), 10),
		Priority:                 priorityReverseMap[riskCase.GetPriority()],
		Status:                   statusReverseMap[riskCase.GetStatus()],
		AssignedTo:               assignedToEmail,
		IsSample:                 riskCase.GetIsSample(),
		ActorId:                  actorId,
		ReviewType:               reviewTypeReverseMap[riskCase.GetReviewType()],
		CreatedAt:                riskCase.GetCreatedAt(),
		UpdatedAt:                riskCase.GetUpdatedAt(),
		Verdict:                  enums.VerdictReverseMap[riskCase.GetVerdict()],
		Tags:                     riskCase.GetTags(),
		SnoozedTill:              riskCase.GetSnoozedTill(),
		ConfidenceScore:          riskCase.GetConfidenceScore(),
		LastAssignedAnalystEmail: riskCase.GetLastAssignedAnalystEmail(),
		AnalystGroup:             analystGroupReverseMap[riskCase.GetAgentGroup()],
	}, nil
}

// TODO(38949): Add support for case filters in ticket list CRM rpc
func (c *CaseFilters) ToCRMFilters() (*vgCrmPb.GetTicketListRequest_RiskFilters, error) {
	if c == nil {
		return nil, nil
	}
	return &vgCrmPb.GetTicketListRequest_RiskFilters{
		RiskFilters: &vgRiskPb.RiskFilters{
			AssignedToEmail: c.GetAssignedToEmail(),
			Statuses:        toCRMStatusListProto(c.GetStatuses()),
			Priorities:      toCRMPriorityListProto(c.GetPriorities()),
			ReviewTypes:     toCRMReviewTypeListProto(c.GetReviewTypes()),
			Snoozed:         c.GetSnoozed(),
			Tags:            c.GetTags(),
		},
	}, nil
}

func (c *SortableCaseFilters) ToCRMFilters(crmUserId string, sortBy *CaseSortBy) (*vgCrmPb.GetSortedTicketsRequest_RiskFilters, error) {
	if c == nil {
		return nil, nil
	}

	var (
		vgRiskFilters = &vgCrmPb.GetSortedTicketsRequest_RiskFilters{
			RiskFilters: &vgRiskPb.SortableRiskFilters{},
		}
	)
	if len(c.GetActorId()) != 0 {
		if len(crmUserId) == 0 {
			return nil, fmt.Errorf("crm user id is empty for actor id filter")
		}
		vgRiskFilters.RiskFilters.UniqueExternalUserId = crmUserId
		vgRiskFilters.RiskFilters.FilterOptions = append(vgRiskFilters.RiskFilters.FilterOptions, vgRiskPb.FilterOption_FILTER_OPTION_UNIQUE_EXTERNAL_ID)
	}

	switch {
	case sortBy == nil:
		vgRiskFilters.RiskFilters.Order = commontypes.SortOrder_SORT_ORDER_DESCENDING
		vgRiskFilters.RiskFilters.SortByTicketField = vgRiskPb.RiskTicketFieldMask_RISK_TICKET_FIELD_MASK_CREATED_AT
	default:
		vgRiskFilters.RiskFilters.Order = sortBy.GetSortOrder()
		vgRiskFilters.RiskFilters.SortByTicketField = caseFieldToTicketFieldMap[sortBy.GetFieldMask()]
	}

	return vgRiskFilters, nil
}

func (p Priority) toCRMProto() vgRiskPb.Priority {
	return priorityMap[p]
}

func (s Status) toCRMProto() vgRiskPb.Status {
	return statusMap[s]
}

func (r ReviewType) toCRMProto() vgRiskPb.ReviewType {
	return reviewTypeMap[r]
}

func toCRMStatusListProto(statuses []Status) []vgRiskPb.Status {
	var vgStatusList []vgRiskPb.Status
	for _, status := range statuses {
		vgStatusList = append(vgStatusList, status.toCRMProto())
	}
	return vgStatusList
}

func toCRMPriorityListProto(priorities []Priority) []vgRiskPb.Priority {
	var vgPriorityList []vgRiskPb.Priority
	for _, priority := range priorities {
		vgPriorityList = append(vgPriorityList, priority.toCRMProto())
	}
	return vgPriorityList
}

func toCRMReviewTypeListProto(reviewTypes []ReviewType) []vgRiskPb.ReviewType {
	var vgReviewTypeList []vgRiskPb.ReviewType
	for _, reviewType := range reviewTypes {
		vgReviewTypeList = append(vgReviewTypeList, reviewType.toCRMProto())
	}
	return vgReviewTypeList
}
