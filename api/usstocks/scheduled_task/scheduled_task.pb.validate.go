// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/usstocks/scheduled_task/scheduled_task.proto

package scheduled_task

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ScheduledTask with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScheduledTask) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScheduledTask with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScheduledTaskMultiError, or
// nil if none found.
func (m *ScheduledTask) ValidateAll() error {
	return m.validate(true)
}

func (m *ScheduledTask) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Task.(type) {
	case *ScheduledTask_TopTradedAmongHighMarketCapTask:
		if v == nil {
			err := ScheduledTaskValidationError{
				field:  "Task",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTopTradedAmongHighMarketCapTask()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScheduledTaskValidationError{
						field:  "TopTradedAmongHighMarketCapTask",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScheduledTaskValidationError{
						field:  "TopTradedAmongHighMarketCapTask",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTopTradedAmongHighMarketCapTask()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScheduledTaskValidationError{
					field:  "TopTradedAmongHighMarketCapTask",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ScheduledTask_TopGainersAcrossAllIndex:
		if v == nil {
			err := ScheduledTaskValidationError{
				field:  "Task",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTopGainersAcrossAllIndex()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScheduledTaskValidationError{
						field:  "TopGainersAcrossAllIndex",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScheduledTaskValidationError{
						field:  "TopGainersAcrossAllIndex",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTopGainersAcrossAllIndex()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScheduledTaskValidationError{
					field:  "TopGainersAcrossAllIndex",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ScheduledTask_TopLosersAcrossAllIndex:
		if v == nil {
			err := ScheduledTaskValidationError{
				field:  "Task",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTopLosersAcrossAllIndex()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ScheduledTaskValidationError{
						field:  "TopLosersAcrossAllIndex",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ScheduledTaskValidationError{
						field:  "TopLosersAcrossAllIndex",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTopLosersAcrossAllIndex()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ScheduledTaskValidationError{
					field:  "TopLosersAcrossAllIndex",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ScheduledTaskMultiError(errors)
	}

	return nil
}

// ScheduledTaskMultiError is an error wrapping multiple validation errors
// returned by ScheduledTask.ValidateAll() if the designated constraints
// aren't met.
type ScheduledTaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScheduledTaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScheduledTaskMultiError) AllErrors() []error { return m }

// ScheduledTaskValidationError is the validation error returned by
// ScheduledTask.Validate if the designated constraints aren't met.
type ScheduledTaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScheduledTaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScheduledTaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScheduledTaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScheduledTaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScheduledTaskValidationError) ErrorName() string { return "ScheduledTaskValidationError" }

// Error satisfies the builtin error interface
func (e ScheduledTaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScheduledTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScheduledTaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScheduledTaskValidationError{}

// Validate checks the field values on TopTradedAmongHighMarketCapTask with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TopTradedAmongHighMarketCapTask) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopTradedAmongHighMarketCapTask with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TopTradedAmongHighMarketCapTaskMultiError, or nil if none found.
func (m *TopTradedAmongHighMarketCapTask) ValidateAll() error {
	return m.validate(true)
}

func (m *TopTradedAmongHighMarketCapTask) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TopTradedAmongHighMarketCapTaskMultiError(errors)
	}

	return nil
}

// TopTradedAmongHighMarketCapTaskMultiError is an error wrapping multiple
// validation errors returned by TopTradedAmongHighMarketCapTask.ValidateAll()
// if the designated constraints aren't met.
type TopTradedAmongHighMarketCapTaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopTradedAmongHighMarketCapTaskMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopTradedAmongHighMarketCapTaskMultiError) AllErrors() []error { return m }

// TopTradedAmongHighMarketCapTaskValidationError is the validation error
// returned by TopTradedAmongHighMarketCapTask.Validate if the designated
// constraints aren't met.
type TopTradedAmongHighMarketCapTaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopTradedAmongHighMarketCapTaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopTradedAmongHighMarketCapTaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopTradedAmongHighMarketCapTaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopTradedAmongHighMarketCapTaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopTradedAmongHighMarketCapTaskValidationError) ErrorName() string {
	return "TopTradedAmongHighMarketCapTaskValidationError"
}

// Error satisfies the builtin error interface
func (e TopTradedAmongHighMarketCapTaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopTradedAmongHighMarketCapTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopTradedAmongHighMarketCapTaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopTradedAmongHighMarketCapTaskValidationError{}

// Validate checks the field values on TopGainersAcrossAllIndex with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TopGainersAcrossAllIndex) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopGainersAcrossAllIndex with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TopGainersAcrossAllIndexMultiError, or nil if none found.
func (m *TopGainersAcrossAllIndex) ValidateAll() error {
	return m.validate(true)
}

func (m *TopGainersAcrossAllIndex) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TopGainersAcrossAllIndexMultiError(errors)
	}

	return nil
}

// TopGainersAcrossAllIndexMultiError is an error wrapping multiple validation
// errors returned by TopGainersAcrossAllIndex.ValidateAll() if the designated
// constraints aren't met.
type TopGainersAcrossAllIndexMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopGainersAcrossAllIndexMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopGainersAcrossAllIndexMultiError) AllErrors() []error { return m }

// TopGainersAcrossAllIndexValidationError is the validation error returned by
// TopGainersAcrossAllIndex.Validate if the designated constraints aren't met.
type TopGainersAcrossAllIndexValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopGainersAcrossAllIndexValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopGainersAcrossAllIndexValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopGainersAcrossAllIndexValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopGainersAcrossAllIndexValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopGainersAcrossAllIndexValidationError) ErrorName() string {
	return "TopGainersAcrossAllIndexValidationError"
}

// Error satisfies the builtin error interface
func (e TopGainersAcrossAllIndexValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopGainersAcrossAllIndex.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopGainersAcrossAllIndexValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopGainersAcrossAllIndexValidationError{}

// Validate checks the field values on TopLosersAcrossAllIndex with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TopLosersAcrossAllIndex) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopLosersAcrossAllIndex with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TopLosersAcrossAllIndexMultiError, or nil if none found.
func (m *TopLosersAcrossAllIndex) ValidateAll() error {
	return m.validate(true)
}

func (m *TopLosersAcrossAllIndex) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TopLosersAcrossAllIndexMultiError(errors)
	}

	return nil
}

// TopLosersAcrossAllIndexMultiError is an error wrapping multiple validation
// errors returned by TopLosersAcrossAllIndex.ValidateAll() if the designated
// constraints aren't met.
type TopLosersAcrossAllIndexMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopLosersAcrossAllIndexMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopLosersAcrossAllIndexMultiError) AllErrors() []error { return m }

// TopLosersAcrossAllIndexValidationError is the validation error returned by
// TopLosersAcrossAllIndex.Validate if the designated constraints aren't met.
type TopLosersAcrossAllIndexValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopLosersAcrossAllIndexValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopLosersAcrossAllIndexValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopLosersAcrossAllIndexValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopLosersAcrossAllIndexValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopLosersAcrossAllIndexValidationError) ErrorName() string {
	return "TopLosersAcrossAllIndexValidationError"
}

// Error satisfies the builtin error interface
func (e TopLosersAcrossAllIndexValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopLosersAcrossAllIndex.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopLosersAcrossAllIndexValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopLosersAcrossAllIndexValidationError{}
