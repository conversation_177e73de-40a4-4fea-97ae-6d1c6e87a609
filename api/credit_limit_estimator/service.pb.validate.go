// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/credit_limit_estimator/service.proto

package credit_limit_estimator

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/firefly/enums"

	preapprovedloan "github.com/epifi/gamma/api/preapprovedloan"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.Vendor(0)

	_ = preapprovedloan.Vendor(0)
)

// Validate checks the field values on GetCreditCardConservativeLimitRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditCardConservativeLimitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditCardConservativeLimitRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditCardConservativeLimitRequestMultiError, or nil if none found.
func (m *GetCreditCardConservativeLimitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardConservativeLimitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	// no validation rules for ShouldCallVendor

	if all {
		switch v := interface{}(m.GetCardProgram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitRequestValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitRequestValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardProgram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardConservativeLimitRequestValidationError{
				field:  "CardProgram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditCardConservativeLimitRequestMultiError(errors)
	}

	return nil
}

// GetCreditCardConservativeLimitRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetCreditCardConservativeLimitRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardConservativeLimitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardConservativeLimitRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardConservativeLimitRequestMultiError) AllErrors() []error { return m }

// GetCreditCardConservativeLimitRequestValidationError is the validation error
// returned by GetCreditCardConservativeLimitRequest.Validate if the
// designated constraints aren't met.
type GetCreditCardConservativeLimitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardConservativeLimitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardConservativeLimitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardConservativeLimitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardConservativeLimitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardConservativeLimitRequestValidationError) ErrorName() string {
	return "GetCreditCardConservativeLimitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardConservativeLimitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardConservativeLimitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardConservativeLimitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardConservativeLimitRequestValidationError{}

// Validate checks the field values on GetCreditCardConservativeLimitResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditCardConservativeLimitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditCardConservativeLimitResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCreditCardConservativeLimitResponseMultiError, or nil if none found.
func (m *GetCreditCardConservativeLimitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditCardConservativeLimitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardConservativeLimitResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConservativeLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitResponseValidationError{
					field:  "ConservativeLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitResponseValidationError{
					field:  "ConservativeLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConservativeLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardConservativeLimitResponseValidationError{
				field:  "ConservativeLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferId

	if all {
		switch v := interface{}(m.GetCardProgram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitResponseValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditCardConservativeLimitResponseValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardProgram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditCardConservativeLimitResponseValidationError{
				field:  "CardProgram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditCardConservativeLimitResponseMultiError(errors)
	}

	return nil
}

// GetCreditCardConservativeLimitResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCreditCardConservativeLimitResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditCardConservativeLimitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditCardConservativeLimitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditCardConservativeLimitResponseMultiError) AllErrors() []error { return m }

// GetCreditCardConservativeLimitResponseValidationError is the validation
// error returned by GetCreditCardConservativeLimitResponse.Validate if the
// designated constraints aren't met.
type GetCreditCardConservativeLimitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditCardConservativeLimitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditCardConservativeLimitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditCardConservativeLimitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditCardConservativeLimitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditCardConservativeLimitResponseValidationError) ErrorName() string {
	return "GetCreditCardConservativeLimitResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditCardConservativeLimitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditCardConservativeLimitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditCardConservativeLimitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditCardConservativeLimitResponseValidationError{}

// Validate checks the field values on GetLoanConservativeLimitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanConservativeLimitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanConservativeLimitRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanConservativeLimitRequestMultiError, or nil if none found.
func (m *GetLoanConservativeLimitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanConservativeLimitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Vendor

	// no validation rules for OfferId

	// no validation rules for ShouldCallVendor

	if len(errors) > 0 {
		return GetLoanConservativeLimitRequestMultiError(errors)
	}

	return nil
}

// GetLoanConservativeLimitRequestMultiError is an error wrapping multiple
// validation errors returned by GetLoanConservativeLimitRequest.ValidateAll()
// if the designated constraints aren't met.
type GetLoanConservativeLimitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanConservativeLimitRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanConservativeLimitRequestMultiError) AllErrors() []error { return m }

// GetLoanConservativeLimitRequestValidationError is the validation error
// returned by GetLoanConservativeLimitRequest.Validate if the designated
// constraints aren't met.
type GetLoanConservativeLimitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanConservativeLimitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanConservativeLimitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanConservativeLimitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanConservativeLimitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanConservativeLimitRequestValidationError) ErrorName() string {
	return "GetLoanConservativeLimitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanConservativeLimitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanConservativeLimitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanConservativeLimitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanConservativeLimitRequestValidationError{}

// Validate checks the field values on GetLoanConservativeLimitResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanConservativeLimitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanConservativeLimitResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanConservativeLimitResponseMultiError, or nil if none found.
func (m *GetLoanConservativeLimitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanConservativeLimitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanConservativeLimitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanConservativeLimitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanConservativeLimitResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConservativeLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanConservativeLimitResponseValidationError{
					field:  "ConservativeLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanConservativeLimitResponseValidationError{
					field:  "ConservativeLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConservativeLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanConservativeLimitResponseValidationError{
				field:  "ConservativeLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanConservativeLimitResponseMultiError(errors)
	}

	return nil
}

// GetLoanConservativeLimitResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanConservativeLimitResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanConservativeLimitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanConservativeLimitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanConservativeLimitResponseMultiError) AllErrors() []error { return m }

// GetLoanConservativeLimitResponseValidationError is the validation error
// returned by GetLoanConservativeLimitResponse.Validate if the designated
// constraints aren't met.
type GetLoanConservativeLimitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanConservativeLimitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanConservativeLimitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanConservativeLimitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanConservativeLimitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanConservativeLimitResponseValidationError) ErrorName() string {
	return "GetLoanConservativeLimitResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanConservativeLimitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanConservativeLimitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanConservativeLimitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanConservativeLimitResponseValidationError{}
