syntax = "proto3";

package vendordata;

option go_package = "github.com/epifi/gamma/api/vendordata/ip";
option java_package = "com.github.epifi.gamma.api.vendordata.ip";

// https://dev.maxmind.com/geoip/docs/databases/city-and-country?lang=en#blocks-files
message GeoIpNetwork {
  string network = 1;
  int32 geoname_id = 2;
  int32 registered_country_geoname_id = 3;
  int32 represented_country_geoname_id = 4;
  bool is_anonymous_proxy = 5;
  bool is_satellite_provider = 6;
  string postal_code = 7;
  float latitude = 8;
  float longitude = 9;
  int32 accuracy_radius = 10;
}
