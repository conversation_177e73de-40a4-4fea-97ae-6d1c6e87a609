// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/creditreportv2/internal/credit_report_download_attempts.proto

package creditreportv2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on CreditReportDownloadAttempt with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditReportDownloadAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditReportDownloadAttempt with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditReportDownloadAttemptMultiError, or nil if none found.
func (m *CreditReportDownloadAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditReportDownloadAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreditReportDownloadId

	// no validation rules for ActorId

	// no validation rules for OrchId

	// no validation rules for Vendor

	// no validation rules for Status

	// no validation rules for SubStatus

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadAttemptValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadAttemptValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreditReportDownloadAttemptMultiError(errors)
	}

	return nil
}

// CreditReportDownloadAttemptMultiError is an error wrapping multiple
// validation errors returned by CreditReportDownloadAttempt.ValidateAll() if
// the designated constraints aren't met.
type CreditReportDownloadAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditReportDownloadAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditReportDownloadAttemptMultiError) AllErrors() []error { return m }

// CreditReportDownloadAttemptValidationError is the validation error returned
// by CreditReportDownloadAttempt.Validate if the designated constraints
// aren't met.
type CreditReportDownloadAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditReportDownloadAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditReportDownloadAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditReportDownloadAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditReportDownloadAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditReportDownloadAttemptValidationError) ErrorName() string {
	return "CreditReportDownloadAttemptValidationError"
}

// Error satisfies the builtin error interface
func (e CreditReportDownloadAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditReportDownloadAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditReportDownloadAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditReportDownloadAttemptValidationError{}
