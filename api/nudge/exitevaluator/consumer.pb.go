//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/nudge/exitevaluator/consumer.proto

package exitevaluator

import (
	queue "github.com/epifi/be-common/api/queue"
	datacollector "github.com/epifi/gamma/api/nudge/datacollector"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConsumerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ConsumerResponse) Reset() {
	*x = ConsumerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_nudge_exitevaluator_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsumerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumerResponse) ProtoMessage() {}

func (x *ConsumerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_nudge_exitevaluator_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumerResponse.ProtoReflect.Descriptor instead.
func (*ConsumerResponse) Descriptor() ([]byte, []int) {
	return file_api_nudge_exitevaluator_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ConsumerResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_nudge_exitevaluator_consumer_proto protoreflect.FileDescriptor

var file_api_nudge_exitevaluator_consumer_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x65, 0x78, 0x69, 0x74,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e,
	0x65, 0x78, 0x69, 0x74, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x28, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5a, 0x0a, 0x10, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0x72, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x12, 0x66, 0x0a, 0x19, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x22,
	0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x1a, 0x25, 0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x65, 0x78, 0x69, 0x74, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x60, 0x0a, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2e, 0x65, 0x78,
	0x69, 0x74, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x5a, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x75, 0x64, 0x67, 0x65, 0x2f, 0x65, 0x78,
	0x69, 0x74, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_nudge_exitevaluator_consumer_proto_rawDescOnce sync.Once
	file_api_nudge_exitevaluator_consumer_proto_rawDescData = file_api_nudge_exitevaluator_consumer_proto_rawDesc
)

func file_api_nudge_exitevaluator_consumer_proto_rawDescGZIP() []byte {
	file_api_nudge_exitevaluator_consumer_proto_rawDescOnce.Do(func() {
		file_api_nudge_exitevaluator_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_nudge_exitevaluator_consumer_proto_rawDescData)
	})
	return file_api_nudge_exitevaluator_consumer_proto_rawDescData
}

var file_api_nudge_exitevaluator_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_nudge_exitevaluator_consumer_proto_goTypes = []interface{}{
	(*ConsumerResponse)(nil),             // 0: nudge.exitevaluator.ConsumerResponse
	(*queue.ConsumerResponseHeader)(nil), // 1: queue.ConsumerResponseHeader
	(*datacollector.CollectedData)(nil),  // 2: nudge.datacollector.CollectedData
}
var file_api_nudge_exitevaluator_consumer_proto_depIdxs = []int32{
	1, // 0: nudge.exitevaluator.ConsumerResponse.response_header:type_name -> queue.ConsumerResponseHeader
	2, // 1: nudge.exitevaluator.Consumer.ProcessDataCollectorEvent:input_type -> nudge.datacollector.CollectedData
	0, // 2: nudge.exitevaluator.Consumer.ProcessDataCollectorEvent:output_type -> nudge.exitevaluator.ConsumerResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_nudge_exitevaluator_consumer_proto_init() }
func file_api_nudge_exitevaluator_consumer_proto_init() {
	if File_api_nudge_exitevaluator_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_nudge_exitevaluator_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsumerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_nudge_exitevaluator_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_nudge_exitevaluator_consumer_proto_goTypes,
		DependencyIndexes: file_api_nudge_exitevaluator_consumer_proto_depIdxs,
		MessageInfos:      file_api_nudge_exitevaluator_consumer_proto_msgTypes,
	}.Build()
	File_api_nudge_exitevaluator_consumer_proto = out.File
	file_api_nudge_exitevaluator_consumer_proto_rawDesc = nil
	file_api_nudge_exitevaluator_consumer_proto_goTypes = nil
	file_api_nudge_exitevaluator_consumer_proto_depIdxs = nil
}
