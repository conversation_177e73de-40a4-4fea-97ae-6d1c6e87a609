// RPCs related to Partner SDK's frontend service

syntax = "proto3";

package auth.partnersdk;

import "api/auth/partnersdk/platform.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/device.proto";
import "api/vendorgateway/vendor.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/auth/partnersdk";
option java_package = "com.github.epifi.gamma.api.auth.partnersdk";

// Partner SDK service for Epifi users
service PartnerSDK {

  // GetSessionParams registers the client details at partner bank
  // and responds with session parameters. Client can make use of
  // these parameters to securely interact with SDK and partner bank
  rpc GetSessionParams(GetSessionParamsRequest) returns (GetSessionParamsResponse);
}

message GetSessionParamsRequest {

  // Identifier of the actor that initiated the request
  string actor_id = 1;

  // Device ID from where the request has originated
  string device_id = 2;
  // Determines if the client is refreshing the session params
  // (or) if this is fresh session registration
  //
  // A session parameter is active for 30 days from the time
  // it is created and client is expected to refresh the
  // parameters post this duration
  //
  // Client can make use of this parameter to refresh the parameters
  bool refresh = 3;

  // List of session parameters that helps the partner bank
  // to authenticate the request
  message SessionParams {
    // Unique identifier of the partner bank's key
    // that is used in the process of generating
    // the session attributes
    string key_id = 1;
    // Initial session keys to register the client app
    // with the partner bank
    // Base 64 encoded
    string encrypted_keys = 2;
    // HMAC of keys
    // If HMAC verification fails, the request will be rejected
    // Base 64 encoded
    string hmac = 3;
    // Ephemeral public key that is used in
    // Elliptic curve's Diffie hellman(ECDH) key agreement protocol
    // to generate the keys to encrypt the payload i.e., keys
    // Base 64 encoded
    string ecdh_pk = 4;
  }

  SessionParams session_params = 4;

  // Partner bank with whom the client is to be registered with
  vendorgateway.Vendor vendor = 5 [(validate.rules).enum = {not_in: [0]}];

  //app platform - Android, iOS etc.
  //deprecated in favour of api.typesv2.common.Platform, use app_platform instead
  auth.partnersdk.Platform platform = 7 [(validate.rules).enum = {not_in: [0]}, deprecated = true];

  //app platform - Android, iOS etc.
  api.typesv2.common.Platform app_platform = 8 [(validate.rules).enum = {not_in: [0]}];;
}

message GetSessionParamsResponse {
  enum Status{
    // Request has been processed successfully
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // Bank account not found with the given parameters
    RECORD_NOT_FOUND = 5;
    // Internal server error
    INTERNAL = 13;
    // Public key of the partner bank is compromised - use backup key
    // Upgrade SDK if backup key is also compromised
    COMPROMISED_PARTNER_PUB_KEY = 100;
    // Public key used in the request is invalid
    // Use the right key
    // e.g., If backup key is used when the primary key is active
    // (or) if key ID is invalid
    INVALID_PARTNER_PUB_KEY = 101;
    // HMac verification failed
    INVALID_HMAC = 102;
    // Previous session not found in case of Refresh
    INVALID_REFRESH_ATTEMPT = 103;
  }
  rpc.Status status = 1;

  // HMAC key that is to be used by the app
  // to generate HMAC for all the request payloads
  // between app and SDK
  // Base 64 encoded
  string app_hmac_key = 2;

  // ECDH's Public key to be registered with the SDK
  // Encrypted value is to be passed to SDK as it is
  // Base 64 encoded
  string encrypted_ecdh_pk = 3;

  // Signature of the payload
  // Signature is to be passed to SDK as it is
  // Base 64 encoded
  string signature = 4;

  // There are multiple keys using which a partner bank
  // can sign a payload. Key ID identified the key
  // using which a payload has been signed
  // Signature's Key ID is to be passed to SDK as it is
  // Base 64 encoded
  string signature_key_id = 5;

  // Bank config to render the pin page for sdk.
  auth.partnersdk.BankConfig bank_config = 6;
}

message BankConfig {
  // Bank logo url.
  // It will be used to show logo of bank at pin page.
  string bank_logo_url = 1;

  // Bank name. It will be used to show bank name.
  string bank_name = 2;

  // Card pin length supported by bank.
  // Based on the value number of input field will be rendered to capture card PIN.
  uint32 card_pin_length = 3;

  // UPI pin length supported by bank.
  // Based on the value number of input field will be rendered to capture UPI PIN.
  uint32 secure_pin_length = 4;

  // Otp length supported by bank for banking APIs.
  // Based on the value number of input field will be rendered to capture otp by cred block.
  uint32 vendor_otp_length = 5;
}
