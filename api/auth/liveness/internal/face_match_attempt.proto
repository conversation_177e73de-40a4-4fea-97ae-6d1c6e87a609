// protolint:disable MAX_LINE_LENGTH

/*
Protos relating to the Liveness that are internal to the domain such as data models
*/


syntax = "proto3";

package auth.liveness;

import "api/auth/liveness/internal/face_match_annotation.proto";
import "api/auth/liveness/types.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/auth/liveness";
option java_package = "com.github.epifi.gamma.api.auth.liveness";

// FaceMatchAttempt stores the information related to the
// face match attempt.
message FaceMatchAttempt {
  string actor_id = 1;
  // unique identifier auto generated by DB.
  string attempt_id = 2;
  // RequestID is the ID with which the caller calls FM to identify and track the status of the request
  string request_id = 3;
  // Request ID to track vendor request
  string vendor_request_id = 4;
  // Vendor
  vendorgateway.Vendor vendor = 8;

  // location of video which was used to do the FM. Example for S3: s3://demo-bucket/try/p3_2.mp4.
  string video_location = 5;

  string image_location = 6;
  // Status of the FM attempt
  FaceMatchStatus status = 7;

  float face_match_score = 10;
  // Id of the liveness summary this attempt corresponds to
  string summary_id = 11;

  // annotation to update extra information
  FaceMatchAnnotation annotation = 12;

  // Threshold used to pass the facematch attempt
  float threshold = 13;

  // store strictness logic applicable for user
  StrictnessLogic strictness_logic = 14 [deprecated = true];

  // store inhouse status
  FaceMatchStatus inhouse_status = 15;

  // Request id of the liveness attempt this facematch was done for
  string liveness_request_id = 16;

  google.protobuf.Timestamp created_at = 17;

  google.protobuf.Timestamp updated_at = 18;
}

// FaceMatchAttemptFieldMask is used to mask columns to update in DB Update call
enum FaceMatchAttemptFieldMask {
  FM_ATTEMPT_FIELD_MASK_FM_ATTEMPT_FIELD_NONE = 0;
  FM_ATTEMPT_FIELD_MASK_VENDOR = 1;
  FM_ATTEMPT_FIELD_MASK_VENDOR_REQUEST_ID = 2;
  FM_ATTEMPT_FIELD_MASK_VIDEO_LOCATION = 4;
  FM_ATTEMPT_FIELD_MASK_IMAGE_LOCATION = 5;
  FM_ATTEMPT_FIELD_MASK_STATUS = 6;
  FM_ATTEMPT_FIELD_MASK_FACE_MATCH_SCORE = 7;
  FM_ATTEMPT_FIELD_MASK_SUMMARY_ID = 8;
  FM_ATTEMPT_FIELD_MASK_ANNOTATION = 9;
  FM_ATTEMPT_FIELD_MASK_THRESHOLD = 10;
  FM_ATTEMPT_FIELD_MASK_INHOUSE_STATUS = 11;
}


