syntax = "proto3";

package token;

option go_package = "github.com/epifi/gamma/api/auth/token";
option java_package = "com.github.epifi.gamma.api.auth.token";

// JWT tokens have a set of registered claims and custom claims
// This message defines the custom claims passed while creating token
message CustomTokenClaims {
  // The subject field in jwt registered claims can have values of various types - actor id, ph number, email, ...
  // It depends on the type of token and also when the token is created
  // To identify the type of value being passed, this field needs to be specified
  // string type of the enum SubjectType (api/auth/token/enums.proto)
  string subject_type = 1;
}
