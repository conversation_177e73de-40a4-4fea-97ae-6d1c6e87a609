syntax = "proto3";
package vendornotification.aa;

import "api/vendors/aa/notification.proto";
import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/aa";

// This event will be published from VN server when received from AA for consent status
message ConsentEvent {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader request_header = 1;
  vendors.aa.ConsentNotification consent_notification = 2;
  google.protobuf.Timestamp event_timestamp = 3;
}

// This event will be published from VN server when received from AA for FI status
message FIEvent {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader request_header = 1;
  vendors.aa.FINotification fi_notification = 2;
  google.protobuf.Timestamp event_timestamp = 3;
}

message AccountLinkEvent {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader request_header = 1;
  vendors.aa.AccountLinkNotification account_link_notification = 2;
  google.protobuf.Timestamp event_timestamp = 3;
}
