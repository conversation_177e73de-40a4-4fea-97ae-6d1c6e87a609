// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendornotification/openbanking/kyctypechange/federal/event.proto

package federal

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on KYCStateChangeEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KYCStateChangeEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KYCStateChangeEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KYCStateChangeEventMultiError, or nil if none found.
func (m *KYCStateChangeEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *KYCStateChangeEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConsumerRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCStateChangeEventValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCStateChangeEventValidationError{
					field:  "ConsumerRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsumerRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCStateChangeEventValidationError{
				field:  "ConsumerRequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UniqueUidaiRrn

	// no validation rules for KycStatus

	// no validation rules for KycDate

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetEventTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCStateChangeEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCStateChangeEventValidationError{
					field:  "EventTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEventTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCStateChangeEventValidationError{
				field:  "EventTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionId

	// no validation rules for EventId

	if len(errors) > 0 {
		return KYCStateChangeEventMultiError(errors)
	}

	return nil
}

// KYCStateChangeEventMultiError is an error wrapping multiple validation
// errors returned by KYCStateChangeEvent.ValidateAll() if the designated
// constraints aren't met.
type KYCStateChangeEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KYCStateChangeEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KYCStateChangeEventMultiError) AllErrors() []error { return m }

// KYCStateChangeEventValidationError is the validation error returned by
// KYCStateChangeEvent.Validate if the designated constraints aren't met.
type KYCStateChangeEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KYCStateChangeEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KYCStateChangeEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KYCStateChangeEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KYCStateChangeEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KYCStateChangeEventValidationError) ErrorName() string {
	return "KYCStateChangeEventValidationError"
}

// Error satisfies the builtin error interface
func (e KYCStateChangeEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKYCStateChangeEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KYCStateChangeEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KYCStateChangeEventValidationError{}
