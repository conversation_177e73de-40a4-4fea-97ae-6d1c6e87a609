// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendornotification/openbanking/payment/federal/service.proto

package federal

import (
	_ "github.com/epifi/be-common/api/rpc"
	federal "github.com/epifi/gamma/api/vendors/federal"
	addFunds "github.com/epifi/gamma/api/vendors/federal/payment/addFunds"
	b2c "github.com/epifi/gamma/api/vendors/federal/payment/b2c"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Epifi's credentials provided by Federal bank
	// Credentials will be different for UAT and PROD
	// Credentials will be provided by Federal Bank's API support team
	// Credentials include user_id, password and sender code
	SenderCode string `protobuf:"bytes,1,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	// Request ID of the transaction for which the call back is initiated
	// Request ID helps uniquely identifying a transaction
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	// UTR number stands for Unique Transaction Reference number
	// UTR No (Unique transaction reference number) is generally used for
	// reference of a particular NEFT /RTGS transaction.
	//
	// IMPS reference number in case of IMPS
	// Bank reference number in case of Intra fund transfer
	//
	// Deprecated: Marked as deprecated in api/vendornotification/openbanking/payment/federal/service.proto.
	Utr string `protobuf:"bytes,3,opt,name=utr,json=Utr,proto3" json:"utr,omitempty"`
	// UTR22 is the new field in which Federal is sending us the new-format of UTRs
	// UTR number stands for Unique Transaction Reference number
	// UTR No (Unique transaction reference number) is generally used for
	// reference of a particular NEFT /RTGS transaction.
	//
	// IMPS reference number in case of IMPS
	// Bank reference number in case of Intra fund transfer
	UtrTwentyTwo string `protobuf:"bytes,13,opt,name=utr_twenty_two,json=Utr22,proto3" json:"utr_twenty_two,omitempty"`
	// Account number of the Remitter
	RemitterAccountNumber string `protobuf:"bytes,4,opt,name=remitter_account_number,json=RemitterAccountNumber,proto3" json:"remitter_account_number,omitempty"`
	// Account number of the Beneficiary
	BeneficiaryAccountNumber string `protobuf:"bytes,5,opt,name=beneficiary_account_number,json=BeneficiaryAccountNumber,proto3" json:"beneficiary_account_number,omitempty"`
	// Registered name of Account holder with the beneficiary bank
	// Populated only in the case of IMPS transaction
	CreditedAccountName string `protobuf:"bytes,12,opt,name=credited_account_name,json=CreditedAccountName,proto3" json:"credited_account_name,omitempty"`
	// Amount to be Transferred in xxx.yy Format E.g 123.45 INR
	Amount string `protobuf:"bytes,6,opt,name=amount,json=TranAmount,proto3" json:"amount,omitempty"`
	// Transaction timestamp in "2019-01-10 14:36:09.404068" format
	TransTimestamp string `protobuf:"bytes,7,opt,name=trans_timestamp,json=TranTimeStamp,proto3" json:"trans_timestamp,omitempty"`
	// Response Code for the Transaction
	ResponseCode string `protobuf:"bytes,8,opt,name=response_code,json=ResponseCode,proto3" json:"response_code,omitempty"`
	// Response Description for the Transaction
	ResponseDesc string `protobuf:"bytes,9,opt,name=response_desc,json=ResponseReason,proto3" json:"response_desc,omitempty"`
	// High level response codes depicting the stage of the transaction
	// PROCESSED, SUCCESS, FAILURE, SUSPECT
	ResponseAction string `protobuf:"bytes,10,opt,name=response_action,json=ResponseAction,proto3" json:"response_action,omitempty"`
	// Device token that is issued by the federal bank at the time of client's device registration
	// TODO(pruthvi): Do we really need this?
	DeviceToken string `protobuf:"bytes,11,opt,name=device_token,json=DeviceToken,proto3" json:"device_token,omitempty"`
}

func (x *UpdateTransactionRequest) Reset() {
	*x = UpdateTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTransactionRequest) ProtoMessage() {}

func (x *UpdateTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTransactionRequest.ProtoReflect.Descriptor instead.
func (*UpdateTransactionRequest) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateTransactionRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *UpdateTransactionRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendornotification/openbanking/payment/federal/service.proto.
func (x *UpdateTransactionRequest) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *UpdateTransactionRequest) GetUtrTwentyTwo() string {
	if x != nil {
		return x.UtrTwentyTwo
	}
	return ""
}

func (x *UpdateTransactionRequest) GetRemitterAccountNumber() string {
	if x != nil {
		return x.RemitterAccountNumber
	}
	return ""
}

func (x *UpdateTransactionRequest) GetBeneficiaryAccountNumber() string {
	if x != nil {
		return x.BeneficiaryAccountNumber
	}
	return ""
}

func (x *UpdateTransactionRequest) GetCreditedAccountName() string {
	if x != nil {
		return x.CreditedAccountName
	}
	return ""
}

func (x *UpdateTransactionRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *UpdateTransactionRequest) GetTransTimestamp() string {
	if x != nil {
		return x.TransTimestamp
	}
	return ""
}

func (x *UpdateTransactionRequest) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *UpdateTransactionRequest) GetResponseDesc() string {
	if x != nil {
		return x.ResponseDesc
	}
	return ""
}

func (x *UpdateTransactionRequest) GetResponseAction() string {
	if x != nil {
		return x.ResponseAction
	}
	return ""
}

func (x *UpdateTransactionRequest) GetDeviceToken() string {
	if x != nil {
		return x.DeviceToken
	}
	return ""
}

type ProcessUpiEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Represents raw XML dump of the UPI Event
	RawData []byte `protobuf:"bytes,1,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
}

func (x *ProcessUpiEventRequest) Reset() {
	*x = ProcessUpiEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessUpiEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessUpiEventRequest) ProtoMessage() {}

func (x *ProcessUpiEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessUpiEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessUpiEventRequest) Descriptor() ([]byte, []int) {
	return file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessUpiEventRequest) GetRawData() []byte {
	if x != nil {
		return x.RawData
	}
	return nil
}

var File_api_vendornotification_openbanking_payment_federal_service_proto protoreflect.FileDescriptor

var file_api_vendornotification_openbanking_payment_federal_service_proto_rawDesc = []byte{
	0x0a, 0x40, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x65, 0x64,
	0x65, 0x72, 0x61, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x3d, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x61, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x2f, 0x61, 0x64, 0x64, 0x5f, 0x66, 0x75,
	0x6e, 0x64, 0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x62, 0x32, 0x63, 0x2f, 0x62, 0x32, 0x63, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x95, 0x04, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x03,
	0x75, 0x74, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x55,
	0x74, 0x72, 0x12, 0x1d, 0x0a, 0x0e, 0x75, 0x74, 0x72, 0x5f, 0x74, 0x77, 0x65, 0x6e, 0x74, 0x79,
	0x5f, 0x74, 0x77, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x55, 0x74, 0x72, 0x32,
	0x32, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x52, 0x65, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x1a, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x63, 0x69, 0x61, 0x72, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x65, 0x64,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x54, 0x72, 0x61,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x33, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x55, 0x70, 0x69, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x32, 0xb4, 0x07, 0x0a,
	0x07, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x9e, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22, 0x1c, 0x2f, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x12, 0x95, 0x01, 0x0a, 0x14, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x32, 0x43, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x38, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64,
	0x65, 0x72, 0x61, 0x6c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x62, 0x32, 0x63,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x32, 0x43, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22,
	0x20, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x62, 0x32, 0x63, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x12, 0x8f, 0x01, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x62,
	0x6f, 0x75, 0x6e, 0x64, 0x54, 0x78, 0x6e, 0x12, 0x29, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2e, 0x3a, 0x01, 0x2a, 0x22, 0x29, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x02, 0x01, 0x12, 0x91, 0x01, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x54, 0x78, 0x6e, 0x56, 0x31, 0x12, 0x29, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x54, 0x78, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x37,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x3a, 0x01, 0x2a, 0x22, 0x2c, 0x2f, 0x6f, 0x70, 0x65, 0x6e,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x12, 0x96, 0x01, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x55, 0x70, 0x69, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x55, 0x70, 0x69, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x23, 0x90, 0x9e, 0xd7,
	0x0a, 0x00, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x0c, 0x2f, 0x75, 0x70, 0x69, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c,
	0x12, 0xb0, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x64, 0x64, 0x46,
	0x75, 0x6e, 0x64, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x64, 0x64, 0x46,
	0x75, 0x6e, 0x64, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x3d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x37, 0x3a, 0x01, 0x2a, 0x22,
	0x32, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x64, 0x64, 0x66, 0x75,
	0x6e, 0x64, 0x73, 0x42, 0x96, 0x01, 0x0a, 0x49, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x5a, 0x49, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescOnce sync.Once
	file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescData = file_api_vendornotification_openbanking_payment_federal_service_proto_rawDesc
)

func file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescGZIP() []byte {
	file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescOnce.Do(func() {
		file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescData)
	})
	return file_api_vendornotification_openbanking_payment_federal_service_proto_rawDescData
}

var file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendornotification_openbanking_payment_federal_service_proto_goTypes = []interface{}{
	(*UpdateTransactionRequest)(nil),         // 0: vendornotification.openbanking.payment.federal.UpdateTransactionRequest
	(*ProcessUpiEventRequest)(nil),           // 1: vendornotification.openbanking.payment.federal.ProcessUpiEventRequest
	(*b2c.UpdateB2CTransactionRequest)(nil),  // 2: vendors.federal.payment.b2c.UpdateB2CTransactionRequest
	(*federal.ProcessInboundTxnRequest)(nil), // 3: vendors.federal.ProcessInboundTxnRequest
	(*addFunds.AddFundsCallbackRequest)(nil), // 4: vendors.federal.payment.addFunds.AddFundsCallbackRequest
	(*emptypb.Empty)(nil),                    // 5: google.protobuf.Empty
}
var file_api_vendornotification_openbanking_payment_federal_service_proto_depIdxs = []int32{
	0, // 0: vendornotification.openbanking.payment.federal.Payment.UpdateTransaction:input_type -> vendornotification.openbanking.payment.federal.UpdateTransactionRequest
	2, // 1: vendornotification.openbanking.payment.federal.Payment.UpdateB2CTransaction:input_type -> vendors.federal.payment.b2c.UpdateB2CTransactionRequest
	3, // 2: vendornotification.openbanking.payment.federal.Payment.ProcessInboundTxn:input_type -> vendors.federal.ProcessInboundTxnRequest
	3, // 3: vendornotification.openbanking.payment.federal.Payment.ProcessInboundTxnV1:input_type -> vendors.federal.ProcessInboundTxnRequest
	1, // 4: vendornotification.openbanking.payment.federal.Payment.ProcessUpiEvent:input_type -> vendornotification.openbanking.payment.federal.ProcessUpiEventRequest
	4, // 5: vendornotification.openbanking.payment.federal.Payment.ProcessAddFundsCallbackEvent:input_type -> vendors.federal.payment.addFunds.AddFundsCallbackRequest
	5, // 6: vendornotification.openbanking.payment.federal.Payment.UpdateTransaction:output_type -> google.protobuf.Empty
	5, // 7: vendornotification.openbanking.payment.federal.Payment.UpdateB2CTransaction:output_type -> google.protobuf.Empty
	5, // 8: vendornotification.openbanking.payment.federal.Payment.ProcessInboundTxn:output_type -> google.protobuf.Empty
	5, // 9: vendornotification.openbanking.payment.federal.Payment.ProcessInboundTxnV1:output_type -> google.protobuf.Empty
	5, // 10: vendornotification.openbanking.payment.federal.Payment.ProcessUpiEvent:output_type -> google.protobuf.Empty
	5, // 11: vendornotification.openbanking.payment.federal.Payment.ProcessAddFundsCallbackEvent:output_type -> google.protobuf.Empty
	6, // [6:12] is the sub-list for method output_type
	0, // [0:6] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendornotification_openbanking_payment_federal_service_proto_init() }
func file_api_vendornotification_openbanking_payment_federal_service_proto_init() {
	if File_api_vendornotification_openbanking_payment_federal_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessUpiEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendornotification_openbanking_payment_federal_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendornotification_openbanking_payment_federal_service_proto_goTypes,
		DependencyIndexes: file_api_vendornotification_openbanking_payment_federal_service_proto_depIdxs,
		MessageInfos:      file_api_vendornotification_openbanking_payment_federal_service_proto_msgTypes,
	}.Build()
	File_api_vendornotification_openbanking_payment_federal_service_proto = out.File
	file_api_vendornotification_openbanking_payment_federal_service_proto_rawDesc = nil
	file_api_vendornotification_openbanking_payment_federal_service_proto_goTypes = nil
	file_api_vendornotification_openbanking_payment_federal_service_proto_depIdxs = nil
}
