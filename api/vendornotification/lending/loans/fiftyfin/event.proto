syntax = "proto3";

package vendornotification.lending.loans.fiftyfin;

option go_package = "github.com/epifi/gamma/api/vendornotification/lending/loans/fiftyfin";
option java_package = "com.github.epifi.gamma.api.vendornotification.lending.loans.fiftyfin";

import "api/queue/consumer_headers.proto";
import "api/vendors/fiftyfin/notifications.proto";

// Message that will be pushed to sqs for processing based on the notification of fiftyfin.
message LoansFiftyfinCallbackEvent {
  queue.ConsumerRequestHeader request_header = 1;
  vendors.fiftyfin.LoansNotification notification = 2;
}
