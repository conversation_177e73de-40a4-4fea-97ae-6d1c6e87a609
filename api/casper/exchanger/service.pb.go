// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/casper/exchanger/service.proto

package exchanger

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	casper "github.com/epifi/gamma/api/casper"
	money "google.golang.org/genproto/googleapis/type/money"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RedeemExchangerOfferResponse_Status int32

const (
	RedeemExchangerOfferResponse_OK RedeemExchangerOfferResponse_Status = 0
	// this status means that user's account is under credit freeze, and redemption is not possible.
	RedeemExchangerOfferResponse_CREDIT_FROZEN            RedeemExchangerOfferResponse_Status = 100
	RedeemExchangerOfferResponse_SAVING_ACCOUNT_NOT_FOUND RedeemExchangerOfferResponse_Status = 101
)

// Enum value maps for RedeemExchangerOfferResponse_Status.
var (
	RedeemExchangerOfferResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "CREDIT_FROZEN",
		101: "SAVING_ACCOUNT_NOT_FOUND",
	}
	RedeemExchangerOfferResponse_Status_value = map[string]int32{
		"OK":                       0,
		"CREDIT_FROZEN":            100,
		"SAVING_ACCOUNT_NOT_FOUND": 101,
	}
)

func (x RedeemExchangerOfferResponse_Status) Enum() *RedeemExchangerOfferResponse_Status {
	p := new(RedeemExchangerOfferResponse_Status)
	*p = x
	return p
}

func (x RedeemExchangerOfferResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedeemExchangerOfferResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_casper_exchanger_service_proto_enumTypes[0].Descriptor()
}

func (RedeemExchangerOfferResponse_Status) Type() protoreflect.EnumType {
	return &file_api_casper_exchanger_service_proto_enumTypes[0]
}

func (x RedeemExchangerOfferResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedeemExchangerOfferResponse_Status.Descriptor instead.
func (RedeemExchangerOfferResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{27, 0}
}

type SubmitUserInputForChosenOptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom the input is being submitted
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// corresponding id of the order created for the exchanger_offer after redeeming
	ExchangerOrderId string `protobuf:"bytes,2,opt,name=exchanger_order_id,json=exchangerOrderId,proto3" json:"exchanger_order_id,omitempty"`
	// shipping address for physical merchandise
	ShippingAddress *postaladdress.PostalAddress `protobuf:"bytes,3,opt,name=shipping_address,json=shippingAddress,proto3" json:"shipping_address,omitempty"` // todo(rohanchougule): add more fields if different types of user-inputs come in
}

func (x *SubmitUserInputForChosenOptionRequest) Reset() {
	*x = SubmitUserInputForChosenOptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitUserInputForChosenOptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitUserInputForChosenOptionRequest) ProtoMessage() {}

func (x *SubmitUserInputForChosenOptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitUserInputForChosenOptionRequest.ProtoReflect.Descriptor instead.
func (*SubmitUserInputForChosenOptionRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{0}
}

func (x *SubmitUserInputForChosenOptionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SubmitUserInputForChosenOptionRequest) GetExchangerOrderId() string {
	if x != nil {
		return x.ExchangerOrderId
	}
	return ""
}

func (x *SubmitUserInputForChosenOptionRequest) GetShippingAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.ShippingAddress
	}
	return nil
}

type SubmitUserInputForChosenOptionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// updated exchanger-offer-order after submitting the user-input
	ExchangerOfferOrder *ExchangerOfferOrder `protobuf:"bytes,2,opt,name=exchanger_offer_order,json=exchangerOfferOrder,proto3" json:"exchanger_offer_order,omitempty"`
}

func (x *SubmitUserInputForChosenOptionResponse) Reset() {
	*x = SubmitUserInputForChosenOptionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitUserInputForChosenOptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitUserInputForChosenOptionResponse) ProtoMessage() {}

func (x *SubmitUserInputForChosenOptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitUserInputForChosenOptionResponse.ProtoReflect.Descriptor instead.
func (*SubmitUserInputForChosenOptionResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{1}
}

func (x *SubmitUserInputForChosenOptionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SubmitUserInputForChosenOptionResponse) GetExchangerOfferOrder() *ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrder
	}
	return nil
}

type CreateExchangerOfferInventoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// description of the inventory
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	// denotes the type of reward being managed by the inventory
	RewardType RewardType `protobuf:"varint,2,opt,name=reward_type,json=rewardType,proto3,enum=casper.exchanger.RewardType" json:"reward_type,omitempty"`
	// initialized count of the inventory
	TotalCount int32 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// max number of inventory items that can be given to a single user
	PerUserLimit int32 `protobuf:"varint,4,opt,name=per_user_limit,json=perUserLimit,proto3" json:"per_user_limit,omitempty"`
}

func (x *CreateExchangerOfferInventoryRequest) Reset() {
	*x = CreateExchangerOfferInventoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferInventoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferInventoryRequest) ProtoMessage() {}

func (x *CreateExchangerOfferInventoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferInventoryRequest.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferInventoryRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateExchangerOfferInventoryRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateExchangerOfferInventoryRequest) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_REWARD_TYPE_UNSPECIFIED
}

func (x *CreateExchangerOfferInventoryRequest) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *CreateExchangerOfferInventoryRequest) GetPerUserLimit() int32 {
	if x != nil {
		return x.PerUserLimit
	}
	return 0
}

type CreateExchangerOfferInventoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger-offer-inventory
	ExchangerOfferInventory *ExchangerOfferInventory `protobuf:"bytes,2,opt,name=exchanger_offer_inventory,json=exchangerOfferInventory,proto3" json:"exchanger_offer_inventory,omitempty"`
}

func (x *CreateExchangerOfferInventoryResponse) Reset() {
	*x = CreateExchangerOfferInventoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferInventoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferInventoryResponse) ProtoMessage() {}

func (x *CreateExchangerOfferInventoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferInventoryResponse.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferInventoryResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{3}
}

func (x *CreateExchangerOfferInventoryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateExchangerOfferInventoryResponse) GetExchangerOfferInventory() *ExchangerOfferInventory {
	if x != nil {
		return x.ExchangerOfferInventory
	}
	return nil
}

type GetExchangerOfferGroupsByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupIds []string `protobuf:"bytes,1,rep,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
}

func (x *GetExchangerOfferGroupsByIdsRequest) Reset() {
	*x = GetExchangerOfferGroupsByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferGroupsByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferGroupsByIdsRequest) ProtoMessage() {}

func (x *GetExchangerOfferGroupsByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferGroupsByIdsRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferGroupsByIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetExchangerOfferGroupsByIdsRequest) GetGroupIds() []string {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

type GetExchangerOfferGroupsByIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// mappinng of exchanger-offer group_id to group
	GroupIdToGroupMap map[string]*ExchangerOfferGroup `protobuf:"bytes,2,rep,name=group_id_to_group_map,json=groupIdToGroupMap,proto3" json:"group_id_to_group_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetExchangerOfferGroupsByIdsResponse) Reset() {
	*x = GetExchangerOfferGroupsByIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferGroupsByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferGroupsByIdsResponse) ProtoMessage() {}

func (x *GetExchangerOfferGroupsByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferGroupsByIdsResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferGroupsByIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetExchangerOfferGroupsByIdsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOfferGroupsByIdsResponse) GetGroupIdToGroupMap() map[string]*ExchangerOfferGroup {
	if x != nil {
		return x.GroupIdToGroupMap
	}
	return nil
}

type GetEOGroupsRewardUnitsActorUtilisationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId  string   `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	GroupIds []string `protobuf:"bytes,2,rep,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`
}

func (x *GetEOGroupsRewardUnitsActorUtilisationRequest) Reset() {
	*x = GetEOGroupsRewardUnitsActorUtilisationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEOGroupsRewardUnitsActorUtilisationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEOGroupsRewardUnitsActorUtilisationRequest) ProtoMessage() {}

func (x *GetEOGroupsRewardUnitsActorUtilisationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEOGroupsRewardUnitsActorUtilisationRequest.ProtoReflect.Descriptor instead.
func (*GetEOGroupsRewardUnitsActorUtilisationRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetEOGroupsRewardUnitsActorUtilisationRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetEOGroupsRewardUnitsActorUtilisationRequest) GetGroupIds() []string {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

type GetEOGroupsRewardUnitsActorUtilisationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// mapping of exchanger-offer group_id and reward-units utilisation by the actor
	GroupIdToUtilisationMap map[string]*ExchangerOfferGroupRewardUnitsActorUtilisation `protobuf:"bytes,2,rep,name=group_id_to_utilisation_map,json=groupIdToUtilisationMap,proto3" json:"group_id_to_utilisation_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetEOGroupsRewardUnitsActorUtilisationResponse) Reset() {
	*x = GetEOGroupsRewardUnitsActorUtilisationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEOGroupsRewardUnitsActorUtilisationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEOGroupsRewardUnitsActorUtilisationResponse) ProtoMessage() {}

func (x *GetEOGroupsRewardUnitsActorUtilisationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEOGroupsRewardUnitsActorUtilisationResponse.ProtoReflect.Descriptor instead.
func (*GetEOGroupsRewardUnitsActorUtilisationResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetEOGroupsRewardUnitsActorUtilisationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetEOGroupsRewardUnitsActorUtilisationResponse) GetGroupIdToUtilisationMap() map[string]*ExchangerOfferGroupRewardUnitsActorUtilisation {
	if x != nil {
		return x.GroupIdToUtilisationMap
	}
	return nil
}

type GetExchangerOffersOrdersSummaryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string                                          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Filters *GetExchangerOffersOrdersSummaryRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetExchangerOffersOrdersSummaryRequest) Reset() {
	*x = GetExchangerOffersOrdersSummaryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersOrdersSummaryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersOrdersSummaryRequest) ProtoMessage() {}

func (x *GetExchangerOffersOrdersSummaryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersOrdersSummaryRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersOrdersSummaryRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetExchangerOffersOrdersSummaryRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetExchangerOffersOrdersSummaryRequest) GetFilters() *GetExchangerOffersOrdersSummaryRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetExchangerOffersOrdersSummaryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// total cash reward amount earned.
	CashEarned *money.Money `protobuf:"bytes,2,opt,name=cash_earned,json=cashEarned,proto3" json:"cash_earned,omitempty"`
	// total ficoins  earned
	FiCoinsEarned int32 `protobuf:"varint,3,opt,name=fi_coins_earned,json=fiCoinsEarned,proto3" json:"fi_coins_earned,omitempty"`
	// total cash amount that is in_processing state.
	// i.e claimed but haven't been credited yet.
	InProcessCashRewardAmount *money.Money `protobuf:"bytes,4,opt,name=in_process_cash_reward_amount,json=inProcessCashRewardAmount,proto3" json:"in_process_cash_reward_amount,omitempty"`
	// total fi coins that are in_processing
	// i.e claimed but haven't been credited yet.
	InProcessFiCoins int32 `protobuf:"varint,5,opt,name=in_process_fi_coins,json=inProcessFiCoins,proto3" json:"in_process_fi_coins,omitempty"`
	// total count of cash rewards.
	CashRewardCount int32 `protobuf:"varint,6,opt,name=cash_reward_count,json=cashRewardCount,proto3" json:"cash_reward_count,omitempty"`
	// total count of fi-coins rewards.
	FiCoinsRewardCount int32 `protobuf:"varint,7,opt,name=fi_coins_reward_count,json=fiCoinsRewardCount,proto3" json:"fi_coins_reward_count,omitempty"`
}

func (x *GetExchangerOffersOrdersSummaryResponse) Reset() {
	*x = GetExchangerOffersOrdersSummaryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersOrdersSummaryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersOrdersSummaryResponse) ProtoMessage() {}

func (x *GetExchangerOffersOrdersSummaryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersOrdersSummaryResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersOrdersSummaryResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetExchangerOffersOrdersSummaryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOffersOrdersSummaryResponse) GetCashEarned() *money.Money {
	if x != nil {
		return x.CashEarned
	}
	return nil
}

func (x *GetExchangerOffersOrdersSummaryResponse) GetFiCoinsEarned() int32 {
	if x != nil {
		return x.FiCoinsEarned
	}
	return 0
}

func (x *GetExchangerOffersOrdersSummaryResponse) GetInProcessCashRewardAmount() *money.Money {
	if x != nil {
		return x.InProcessCashRewardAmount
	}
	return nil
}

func (x *GetExchangerOffersOrdersSummaryResponse) GetInProcessFiCoins() int32 {
	if x != nil {
		return x.InProcessFiCoins
	}
	return 0
}

func (x *GetExchangerOffersOrdersSummaryResponse) GetCashRewardCount() int32 {
	if x != nil {
		return x.CashRewardCount
	}
	return 0
}

func (x *GetExchangerOffersOrdersSummaryResponse) GetFiCoinsRewardCount() int32 {
	if x != nil {
		return x.FiCoinsRewardCount
	}
	return 0
}

type CreateExchangerOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// currency through which given ExchangerOffer can be redeemed like FI_COINS.
	RedemptionCurrency ExchangerOfferRedemptionCurrency `protobuf:"varint,1,opt,name=redemption_currency,json=redemptionCurrency,proto3,enum=casper.exchanger.ExchangerOfferRedemptionCurrency" json:"redemption_currency,omitempty"`
	// price of redeeming the ExchangerOffer.
	RedemptionPrice float32 `protobuf:"fixed32,2,opt,name=redemption_price,json=redemptionPrice,proto3" json:"redemption_price,omitempty"`
	// display details of the offer
	OfferDisplayDetails *ExchangerOfferDisplayDetails `protobuf:"bytes,3,opt,name=offer_display_details,json=offerDisplayDetails,proto3" json:"offer_display_details,omitempty"`
	// config to generate reward options on redeeming the ExchangerOffer.
	OfferOptionsConfig *ExchangerOfferOptionsConfig `protobuf:"bytes,4,opt,name=offer_options_config,json=offerOptionsConfig,proto3" json:"offer_options_config,omitempty"`
	// stores aggregates related config for an ExchangerOffer.
	OfferAggregatesConfig *ExchangerOfferAggregatesConfig `protobuf:"bytes,5,opt,name=offer_aggregates_config,json=offerAggregatesConfig,proto3" json:"offer_aggregates_config,omitempty"`
	// exchanger-offer group id
	GroupId string `protobuf:"bytes,6,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	// additional details related to offer
	AdditionalDetails *ExchangerOfferAdditionalDetails `protobuf:"bytes,7,opt,name=additional_details,json=additionalDetails,proto3" json:"additional_details,omitempty"`
	// manually applied tags
	ManualTags []casper.TagName `protobuf:"varint,16,rep,packed,name=manual_tags,json=manualTags,proto3,enum=casper.TagName" json:"manual_tags,omitempty"`
	// category tag of the offer
	CategoryTag casper.CategoryTag `protobuf:"varint,17,opt,name=category_tag,json=categoryTag,proto3,enum=casper.CategoryTag" json:"category_tag,omitempty"`
	// sub category tag of the offer
	SubCategoryTag casper.SubCategoryTag `protobuf:"varint,18,opt,name=sub_category_tag,json=subCategoryTag,proto3,enum=casper.SubCategoryTag" json:"sub_category_tag,omitempty"`
}

func (x *CreateExchangerOfferRequest) Reset() {
	*x = CreateExchangerOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferRequest) ProtoMessage() {}

func (x *CreateExchangerOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferRequest.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateExchangerOfferRequest) GetRedemptionCurrency() ExchangerOfferRedemptionCurrency {
	if x != nil {
		return x.RedemptionCurrency
	}
	return ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED
}

func (x *CreateExchangerOfferRequest) GetRedemptionPrice() float32 {
	if x != nil {
		return x.RedemptionPrice
	}
	return 0
}

func (x *CreateExchangerOfferRequest) GetOfferDisplayDetails() *ExchangerOfferDisplayDetails {
	if x != nil {
		return x.OfferDisplayDetails
	}
	return nil
}

func (x *CreateExchangerOfferRequest) GetOfferOptionsConfig() *ExchangerOfferOptionsConfig {
	if x != nil {
		return x.OfferOptionsConfig
	}
	return nil
}

func (x *CreateExchangerOfferRequest) GetOfferAggregatesConfig() *ExchangerOfferAggregatesConfig {
	if x != nil {
		return x.OfferAggregatesConfig
	}
	return nil
}

func (x *CreateExchangerOfferRequest) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *CreateExchangerOfferRequest) GetAdditionalDetails() *ExchangerOfferAdditionalDetails {
	if x != nil {
		return x.AdditionalDetails
	}
	return nil
}

func (x *CreateExchangerOfferRequest) GetManualTags() []casper.TagName {
	if x != nil {
		return x.ManualTags
	}
	return nil
}

func (x *CreateExchangerOfferRequest) GetCategoryTag() casper.CategoryTag {
	if x != nil {
		return x.CategoryTag
	}
	return casper.CategoryTag(0)
}

func (x *CreateExchangerOfferRequest) GetSubCategoryTag() casper.SubCategoryTag {
	if x != nil {
		return x.SubCategoryTag
	}
	return casper.SubCategoryTag(0)
}

type CreateExchangerOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger offer
	ExchangerOffer *ExchangerOffer `protobuf:"bytes,2,opt,name=exchanger_offer,json=exchangerOffer,proto3" json:"exchanger_offer,omitempty"`
}

func (x *CreateExchangerOfferResponse) Reset() {
	*x = CreateExchangerOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferResponse) ProtoMessage() {}

func (x *CreateExchangerOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferResponse.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateExchangerOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateExchangerOfferResponse) GetExchangerOffer() *ExchangerOffer {
	if x != nil {
		return x.ExchangerOffer
	}
	return nil
}

type CreateExchangerOfferListingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exchanger_offer for which listing is created
	ExchangerOfferId string `protobuf:"bytes,1,opt,name=exchanger_offer_id,json=exchangerOfferId,proto3" json:"exchanger_offer_id,omitempty"`
	// since when listing is available for redemption
	ActiveSince string `protobuf:"bytes,2,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// till when listing is available for redemption
	ActiveTill string `protobuf:"bytes,3,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// till when listing is available for display
	DisplaySince string `protobuf:"bytes,4,opt,name=display_since,json=displaySince,proto3" json:"display_since,omitempty"`
	// till when listing is available for display
	DisplayTill string `protobuf:"bytes,5,opt,name=display_till,json=displayTill,proto3" json:"display_till,omitempty"`
}

func (x *CreateExchangerOfferListingRequest) Reset() {
	*x = CreateExchangerOfferListingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferListingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferListingRequest) ProtoMessage() {}

func (x *CreateExchangerOfferListingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferListingRequest.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferListingRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{12}
}

func (x *CreateExchangerOfferListingRequest) GetExchangerOfferId() string {
	if x != nil {
		return x.ExchangerOfferId
	}
	return ""
}

func (x *CreateExchangerOfferListingRequest) GetActiveSince() string {
	if x != nil {
		return x.ActiveSince
	}
	return ""
}

func (x *CreateExchangerOfferListingRequest) GetActiveTill() string {
	if x != nil {
		return x.ActiveTill
	}
	return ""
}

func (x *CreateExchangerOfferListingRequest) GetDisplaySince() string {
	if x != nil {
		return x.DisplaySince
	}
	return ""
}

func (x *CreateExchangerOfferListingRequest) GetDisplayTill() string {
	if x != nil {
		return x.DisplayTill
	}
	return ""
}

type CreateExchangerOfferListingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger offer listing
	ExchangerOfferListing *ExchangerOfferListing `protobuf:"bytes,2,opt,name=exchanger_offer_listing,json=exchangerOfferListing,proto3" json:"exchanger_offer_listing,omitempty"`
}

func (x *CreateExchangerOfferListingResponse) Reset() {
	*x = CreateExchangerOfferListingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferListingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferListingResponse) ProtoMessage() {}

func (x *CreateExchangerOfferListingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferListingResponse.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferListingResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{13}
}

func (x *CreateExchangerOfferListingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateExchangerOfferListingResponse) GetExchangerOfferListing() *ExchangerOfferListing {
	if x != nil {
		return x.ExchangerOfferListing
	}
	return nil
}

type UpdateExchangerOfferDisplayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the offer which needs to be updated
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// display details of the offer
	NewDisplayDetails *ExchangerOfferDisplayDetails `protobuf:"bytes,2,opt,name=new_display_details,json=newDisplayDetails,proto3" json:"new_display_details,omitempty"`
	// additional details of the offer
	NewAdditionalDetails *ExchangerOfferAdditionalDetails `protobuf:"bytes,3,opt,name=new_additional_details,json=newAdditionalDetails,proto3" json:"new_additional_details,omitempty"`
	// updated list of tags applied to the offer
	NewManualTags []casper.TagName `protobuf:"varint,4,rep,packed,name=new_manual_tags,json=newManualTags,proto3,enum=casper.TagName" json:"new_manual_tags,omitempty"`
	// updated category tag of the offer
	CategoryTag casper.CategoryTag `protobuf:"varint,5,opt,name=category_tag,json=categoryTag,proto3,enum=casper.CategoryTag" json:"category_tag,omitempty"`
	// updated sub category tag of the offer
	SubCategoryTag casper.SubCategoryTag `protobuf:"varint,6,opt,name=sub_category_tag,json=subCategoryTag,proto3,enum=casper.SubCategoryTag" json:"sub_category_tag,omitempty"`
}

func (x *UpdateExchangerOfferDisplayRequest) Reset() {
	*x = UpdateExchangerOfferDisplayRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExchangerOfferDisplayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExchangerOfferDisplayRequest) ProtoMessage() {}

func (x *UpdateExchangerOfferDisplayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExchangerOfferDisplayRequest.ProtoReflect.Descriptor instead.
func (*UpdateExchangerOfferDisplayRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateExchangerOfferDisplayRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *UpdateExchangerOfferDisplayRequest) GetNewDisplayDetails() *ExchangerOfferDisplayDetails {
	if x != nil {
		return x.NewDisplayDetails
	}
	return nil
}

func (x *UpdateExchangerOfferDisplayRequest) GetNewAdditionalDetails() *ExchangerOfferAdditionalDetails {
	if x != nil {
		return x.NewAdditionalDetails
	}
	return nil
}

func (x *UpdateExchangerOfferDisplayRequest) GetNewManualTags() []casper.TagName {
	if x != nil {
		return x.NewManualTags
	}
	return nil
}

func (x *UpdateExchangerOfferDisplayRequest) GetCategoryTag() casper.CategoryTag {
	if x != nil {
		return x.CategoryTag
	}
	return casper.CategoryTag(0)
}

func (x *UpdateExchangerOfferDisplayRequest) GetSubCategoryTag() casper.SubCategoryTag {
	if x != nil {
		return x.SubCategoryTag
	}
	return casper.SubCategoryTag(0)
}

type UpdateExchangerOfferDisplayResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// updated exchanger offer
	ExchangerOffer *ExchangerOffer `protobuf:"bytes,2,opt,name=exchanger_offer,json=exchangerOffer,proto3" json:"exchanger_offer,omitempty"`
}

func (x *UpdateExchangerOfferDisplayResponse) Reset() {
	*x = UpdateExchangerOfferDisplayResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExchangerOfferDisplayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExchangerOfferDisplayResponse) ProtoMessage() {}

func (x *UpdateExchangerOfferDisplayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExchangerOfferDisplayResponse.ProtoReflect.Descriptor instead.
func (*UpdateExchangerOfferDisplayResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateExchangerOfferDisplayResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateExchangerOfferDisplayResponse) GetExchangerOffer() *ExchangerOffer {
	if x != nil {
		return x.ExchangerOffer
	}
	return nil
}

type UpdateExchangerOfferStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exchanger offer id
	OfferId string `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	// new status to update to
	NewStatus ExchangerOfferStatus `protobuf:"varint,2,opt,name=new_status,json=newStatus,proto3,enum=casper.exchanger.ExchangerOfferStatus" json:"new_status,omitempty"`
}

func (x *UpdateExchangerOfferStatusRequest) Reset() {
	*x = UpdateExchangerOfferStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExchangerOfferStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExchangerOfferStatusRequest) ProtoMessage() {}

func (x *UpdateExchangerOfferStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExchangerOfferStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateExchangerOfferStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateExchangerOfferStatusRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *UpdateExchangerOfferStatusRequest) GetNewStatus() ExchangerOfferStatus {
	if x != nil {
		return x.NewStatus
	}
	return ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_UNSPECIFIED
}

type UpdateExchangerOfferStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// updated exchanger offer
	ExchangerOffer *ExchangerOffer `protobuf:"bytes,2,opt,name=exchanger_offer,json=exchangerOffer,proto3" json:"exchanger_offer,omitempty"`
}

func (x *UpdateExchangerOfferStatusResponse) Reset() {
	*x = UpdateExchangerOfferStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExchangerOfferStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExchangerOfferStatusResponse) ProtoMessage() {}

func (x *UpdateExchangerOfferStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExchangerOfferStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateExchangerOfferStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateExchangerOfferStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateExchangerOfferStatusResponse) GetExchangerOffer() *ExchangerOffer {
	if x != nil {
		return x.ExchangerOffer
	}
	return nil
}

type UpdateExchangerOfferListingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// listing id of the offer which needs to be updated
	OfferListingId string `protobuf:"bytes,1,opt,name=offer_listing_id,json=offerListingId,proto3" json:"offer_listing_id,omitempty"`
	// since when listing is available for redemption
	ActiveSince string `protobuf:"bytes,2,opt,name=active_since,json=activeSince,proto3" json:"active_since,omitempty"`
	// till when listing is available for redemption
	ActiveTill string `protobuf:"bytes,3,opt,name=active_till,json=activeTill,proto3" json:"active_till,omitempty"`
	// till when listing is available for display
	DisplaySince string `protobuf:"bytes,4,opt,name=display_since,json=displaySince,proto3" json:"display_since,omitempty"`
	// till when listing is available for display
	DisplayTill string `protobuf:"bytes,5,opt,name=display_till,json=displayTill,proto3" json:"display_till,omitempty"`
}

func (x *UpdateExchangerOfferListingRequest) Reset() {
	*x = UpdateExchangerOfferListingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExchangerOfferListingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExchangerOfferListingRequest) ProtoMessage() {}

func (x *UpdateExchangerOfferListingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExchangerOfferListingRequest.ProtoReflect.Descriptor instead.
func (*UpdateExchangerOfferListingRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateExchangerOfferListingRequest) GetOfferListingId() string {
	if x != nil {
		return x.OfferListingId
	}
	return ""
}

func (x *UpdateExchangerOfferListingRequest) GetActiveSince() string {
	if x != nil {
		return x.ActiveSince
	}
	return ""
}

func (x *UpdateExchangerOfferListingRequest) GetActiveTill() string {
	if x != nil {
		return x.ActiveTill
	}
	return ""
}

func (x *UpdateExchangerOfferListingRequest) GetDisplaySince() string {
	if x != nil {
		return x.DisplaySince
	}
	return ""
}

func (x *UpdateExchangerOfferListingRequest) GetDisplayTill() string {
	if x != nil {
		return x.DisplayTill
	}
	return ""
}

type UpdateExchangerOfferListingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// updated exchanger offer listing
	OfferListing *ExchangerOfferListing `protobuf:"bytes,2,opt,name=offer_listing,json=offerListing,proto3" json:"offer_listing,omitempty"`
}

func (x *UpdateExchangerOfferListingResponse) Reset() {
	*x = UpdateExchangerOfferListingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExchangerOfferListingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExchangerOfferListingResponse) ProtoMessage() {}

func (x *UpdateExchangerOfferListingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExchangerOfferListingResponse.ProtoReflect.Descriptor instead.
func (*UpdateExchangerOfferListingResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateExchangerOfferListingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *UpdateExchangerOfferListingResponse) GetOfferListing() *ExchangerOfferListing {
	if x != nil {
		return x.OfferListing
	}
	return nil
}

type DeleteExchangerOfferListingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// listing id of the offer which needs to be deleted
	OfferListingId string `protobuf:"bytes,1,opt,name=offer_listing_id,json=offerListingId,proto3" json:"offer_listing_id,omitempty"`
}

func (x *DeleteExchangerOfferListingRequest) Reset() {
	*x = DeleteExchangerOfferListingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExchangerOfferListingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExchangerOfferListingRequest) ProtoMessage() {}

func (x *DeleteExchangerOfferListingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExchangerOfferListingRequest.ProtoReflect.Descriptor instead.
func (*DeleteExchangerOfferListingRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteExchangerOfferListingRequest) GetOfferListingId() string {
	if x != nil {
		return x.OfferListingId
	}
	return ""
}

type DeleteExchangerOfferListingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteExchangerOfferListingResponse) Reset() {
	*x = DeleteExchangerOfferListingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExchangerOfferListingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExchangerOfferListingResponse) ProtoMessage() {}

func (x *DeleteExchangerOfferListingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExchangerOfferListingResponse.ProtoReflect.Descriptor instead.
func (*DeleteExchangerOfferListingResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteExchangerOfferListingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetExchangerOffersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom the offers need to be fetched.
	// optional
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// filters to decide which offers to return in response
	// deprecated: Use FiltersV2
	//
	// Deprecated: Marked as deprecated in api/casper/exchanger/service.proto.
	Filters *casper.CatalogFilters `protobuf:"bytes,4,opt,name=filters,proto3" json:"filters,omitempty"`
	// filters to decide which offers to return in response
	FiltersV2 *GetExchangerOffersRequest_FiltersV2 `protobuf:"bytes,2,opt,name=filters_v2,json=filtersV2,proto3" json:"filters_v2,omitempty"`
}

func (x *GetExchangerOffersRequest) Reset() {
	*x = GetExchangerOffersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersRequest) ProtoMessage() {}

func (x *GetExchangerOffersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetExchangerOffersRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/casper/exchanger/service.proto.
func (x *GetExchangerOffersRequest) GetFilters() *casper.CatalogFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetExchangerOffersRequest) GetFiltersV2() *GetExchangerOffersRequest_FiltersV2 {
	if x != nil {
		return x.FiltersV2
	}
	return nil
}

type GetExchangerOffersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of active exchanger offers
	ExchangerOffers []*ExchangerOffer `protobuf:"bytes,2,rep,name=exchanger_offers,json=exchangerOffers,proto3" json:"exchanger_offers,omitempty"`
	// exchanger offer id to offer listing mapping
	OfferIdToListingMap map[string]*ExchangerOfferListing `protobuf:"bytes,3,rep,name=offer_id_to_listing_map,json=offerIdToListingMap,proto3" json:"offer_id_to_listing_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetExchangerOffersResponse) Reset() {
	*x = GetExchangerOffersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersResponse) ProtoMessage() {}

func (x *GetExchangerOffersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{23}
}

func (x *GetExchangerOffersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOffersResponse) GetExchangerOffers() []*ExchangerOffer {
	if x != nil {
		return x.ExchangerOffers
	}
	return nil
}

func (x *GetExchangerOffersResponse) GetOfferIdToListingMap() map[string]*ExchangerOfferListing {
	if x != nil {
		return x.OfferIdToListingMap
	}
	return nil
}

type GetExchangerOffersByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	// filters to decide which offers to return in response
	Filters *GetExchangerOffersByIdsRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetExchangerOffersByIdsRequest) Reset() {
	*x = GetExchangerOffersByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersByIdsRequest) ProtoMessage() {}

func (x *GetExchangerOffersByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersByIdsRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersByIdsRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{24}
}

func (x *GetExchangerOffersByIdsRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *GetExchangerOffersByIdsRequest) GetFilters() *GetExchangerOffersByIdsRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetExchangerOffersByIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// requested exchanger-offers
	ExchangerOffers []*ExchangerOffer `protobuf:"bytes,2,rep,name=exchanger_offers,json=exchangerOffers,proto3" json:"exchanger_offers,omitempty"`
}

func (x *GetExchangerOffersByIdsResponse) Reset() {
	*x = GetExchangerOffersByIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersByIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersByIdsResponse) ProtoMessage() {}

func (x *GetExchangerOffersByIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersByIdsResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersByIdsResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetExchangerOffersByIdsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOffersByIdsResponse) GetExchangerOffers() []*ExchangerOffer {
	if x != nil {
		return x.ExchangerOffers
	}
	return nil
}

type RedeemExchangerOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id of an exchanger offer that is to be redeemed.
	ExchangerOfferId string `protobuf:"bytes,1,opt,name=exchanger_offer_id,json=exchangerOfferId,proto3" json:"exchanger_offer_id,omitempty"`
	// actor who is redeeming the exchanger offer
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// (client) request id for idempotency check
	RequestId string `protobuf:"bytes,3,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"` // todo (utkarsh) : add any other necessary details needed for redeeming an exchanger offer
}

func (x *RedeemExchangerOfferRequest) Reset() {
	*x = RedeemExchangerOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemExchangerOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemExchangerOfferRequest) ProtoMessage() {}

func (x *RedeemExchangerOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemExchangerOfferRequest.ProtoReflect.Descriptor instead.
func (*RedeemExchangerOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{26}
}

func (x *RedeemExchangerOfferRequest) GetExchangerOfferId() string {
	if x != nil {
		return x.ExchangerOfferId
	}
	return ""
}

func (x *RedeemExchangerOfferRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RedeemExchangerOfferRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type RedeemExchangerOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// order created for exchanger offer for the actor
	ExchangerOfferOrder *ExchangerOfferOrder `protobuf:"bytes,2,opt,name=exchanger_offer_order,json=exchangerOfferOrder,proto3" json:"exchanger_offer_order,omitempty"`
}

func (x *RedeemExchangerOfferResponse) Reset() {
	*x = RedeemExchangerOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedeemExchangerOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemExchangerOfferResponse) ProtoMessage() {}

func (x *RedeemExchangerOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemExchangerOfferResponse.ProtoReflect.Descriptor instead.
func (*RedeemExchangerOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{27}
}

func (x *RedeemExchangerOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *RedeemExchangerOfferResponse) GetExchangerOfferOrder() *ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrder
	}
	return nil
}

type GetExchangerOfferOrdersForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for who exchanger offer orders need to be fetched
	ActorId string                                          `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Filters *GetExchangerOfferOrdersForActorRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
	// page context to help server fetch the page
	PageContext *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetExchangerOfferOrdersForActorRequest) Reset() {
	*x = GetExchangerOfferOrdersForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersForActorRequest) ProtoMessage() {}

func (x *GetExchangerOfferOrdersForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersForActorRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetExchangerOfferOrdersForActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetExchangerOfferOrdersForActorRequest) GetFilters() *GetExchangerOfferOrdersForActorRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetExchangerOfferOrdersForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger offer orders list
	ExchangerOfferOrders []*ExchangerOfferOrder `protobuf:"bytes,2,rep,name=exchanger_offer_orders,json=exchangerOfferOrders,proto3" json:"exchanger_offer_orders,omitempty"`
	// page context to help client fetch the next page
	PageContext *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetExchangerOfferOrdersForActorResponse) Reset() {
	*x = GetExchangerOfferOrdersForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersForActorResponse) ProtoMessage() {}

func (x *GetExchangerOfferOrdersForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersForActorResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetExchangerOfferOrdersForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorResponse) GetExchangerOfferOrders() []*ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrders
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetExchangerOfferOrdersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for who exchanger offer orders need to be fetched
	// optional
	ActorId string                                  `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Filters *GetExchangerOfferOrdersRequest_Filters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
	// page context to help server fetch the page
	PageContext *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetExchangerOfferOrdersRequest) Reset() {
	*x = GetExchangerOfferOrdersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersRequest) ProtoMessage() {}

func (x *GetExchangerOfferOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetExchangerOfferOrdersRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetExchangerOfferOrdersRequest) GetFilters() *GetExchangerOfferOrdersRequest_Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetExchangerOfferOrdersRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetExchangerOfferOrdersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger offer orders list
	ExchangerOfferOrders []*ExchangerOfferOrder `protobuf:"bytes,2,rep,name=exchanger_offer_orders,json=exchangerOfferOrders,proto3" json:"exchanger_offer_orders,omitempty"`
	// page context to help client fetch the next page
	PageContext *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetExchangerOfferOrdersResponse) Reset() {
	*x = GetExchangerOfferOrdersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersResponse) ProtoMessage() {}

func (x *GetExchangerOfferOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{31}
}

func (x *GetExchangerOfferOrdersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOfferOrdersResponse) GetExchangerOfferOrders() []*ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrders
	}
	return nil
}

func (x *GetExchangerOfferOrdersResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type GetExchangerOrderByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExchangerOrderId string `protobuf:"bytes,1,opt,name=exchanger_order_id,json=exchangerOrderId,proto3" json:"exchanger_order_id,omitempty"`
}

func (x *GetExchangerOrderByIdRequest) Reset() {
	*x = GetExchangerOrderByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOrderByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOrderByIdRequest) ProtoMessage() {}

func (x *GetExchangerOrderByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOrderByIdRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOrderByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetExchangerOrderByIdRequest) GetExchangerOrderId() string {
	if x != nil {
		return x.ExchangerOrderId
	}
	return ""
}

type GetExchangerOrderByIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status         *rpc.Status          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ExchangerOrder *ExchangerOfferOrder `protobuf:"bytes,2,opt,name=exchanger_order,json=exchangerOrder,proto3" json:"exchanger_order,omitempty"`
}

func (x *GetExchangerOrderByIdResponse) Reset() {
	*x = GetExchangerOrderByIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOrderByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOrderByIdResponse) ProtoMessage() {}

func (x *GetExchangerOrderByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOrderByIdResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOrderByIdResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetExchangerOrderByIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOrderByIdResponse) GetExchangerOrder() *ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOrder
	}
	return nil
}

type GetExchangerOfferActorAttemptsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Exchanger offer which was redeemed
	ExchangerOfferId string `protobuf:"bytes,2,opt,name=exchanger_offer_id,json=exchangerOfferId,proto3" json:"exchanger_offer_id,omitempty"`
	// From & To timestamp between which redemptions needs to be fetched,
	// including fromAttemptedTime but excluding toAttemptedTime,
	// which is [fromAttemptedTime, toAttemptedTime)
	FromAttemptedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=fromAttemptedTime,proto3" json:"fromAttemptedTime,omitempty"`
	ToAttemptedTime   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=toAttemptedTime,proto3" json:"toAttemptedTime,omitempty"`
}

func (x *GetExchangerOfferActorAttemptsRequest) Reset() {
	*x = GetExchangerOfferActorAttemptsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferActorAttemptsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferActorAttemptsRequest) ProtoMessage() {}

func (x *GetExchangerOfferActorAttemptsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferActorAttemptsRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferActorAttemptsRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetExchangerOfferActorAttemptsRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetExchangerOfferActorAttemptsRequest) GetExchangerOfferId() string {
	if x != nil {
		return x.ExchangerOfferId
	}
	return ""
}

func (x *GetExchangerOfferActorAttemptsRequest) GetFromAttemptedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromAttemptedTime
	}
	return nil
}

func (x *GetExchangerOfferActorAttemptsRequest) GetToAttemptedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToAttemptedTime
	}
	return nil
}

type GetExchangerOfferActorAttemptsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger offer actor attempts list
	ExchangerOfferActorAttempts []*ExchangerOfferActorAttempt `protobuf:"bytes,2,rep,name=exchanger_offer_actor_attempts,json=exchangerOfferActorAttempts,proto3" json:"exchanger_offer_actor_attempts,omitempty"`
}

func (x *GetExchangerOfferActorAttemptsResponse) Reset() {
	*x = GetExchangerOfferActorAttemptsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferActorAttemptsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferActorAttemptsResponse) ProtoMessage() {}

func (x *GetExchangerOfferActorAttemptsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferActorAttemptsResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferActorAttemptsResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetExchangerOfferActorAttemptsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOfferActorAttemptsResponse) GetExchangerOfferActorAttempts() []*ExchangerOfferActorAttempt {
	if x != nil {
		return x.ExchangerOfferActorAttempts
	}
	return nil
}

type GetExchangerOffersActorAttemptsCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// list of exchanger-offers for which the attempts count is required
	ExchangerOfferIds []string `protobuf:"bytes,2,rep,name=exchanger_offer_ids,json=exchangerOfferIds,proto3" json:"exchanger_offer_ids,omitempty"`
	// From & To timestamp between which redemptions/attempts needs to be fetched,
	// including fromAttemptedTime but excluding toAttemptedTime,
	// which is [fromAttemptedTime, toAttemptedTime)
	FromAttemptedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=fromAttemptedTime,proto3" json:"fromAttemptedTime,omitempty"`
	ToAttemptedTime   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=toAttemptedTime,proto3" json:"toAttemptedTime,omitempty"`
}

func (x *GetExchangerOffersActorAttemptsCountRequest) Reset() {
	*x = GetExchangerOffersActorAttemptsCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersActorAttemptsCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersActorAttemptsCountRequest) ProtoMessage() {}

func (x *GetExchangerOffersActorAttemptsCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersActorAttemptsCountRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersActorAttemptsCountRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetExchangerOffersActorAttemptsCountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetExchangerOffersActorAttemptsCountRequest) GetExchangerOfferIds() []string {
	if x != nil {
		return x.ExchangerOfferIds
	}
	return nil
}

func (x *GetExchangerOffersActorAttemptsCountRequest) GetFromAttemptedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromAttemptedTime
	}
	return nil
}

func (x *GetExchangerOffersActorAttemptsCountRequest) GetToAttemptedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToAttemptedTime
	}
	return nil
}

type GetExchangerOffersActorAttemptsCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// mapping of exchanger-offer-ids and the attempts count
	OfferIdToAttemptsCountMap map[string]int32 `protobuf:"bytes,2,rep,name=offer_id_to_attempts_count_map,json=offerIdToAttemptsCountMap,proto3" json:"offer_id_to_attempts_count_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetExchangerOffersActorAttemptsCountResponse) Reset() {
	*x = GetExchangerOffersActorAttemptsCountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersActorAttemptsCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersActorAttemptsCountResponse) ProtoMessage() {}

func (x *GetExchangerOffersActorAttemptsCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersActorAttemptsCountResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersActorAttemptsCountResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetExchangerOffersActorAttemptsCountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOffersActorAttemptsCountResponse) GetOfferIdToAttemptsCountMap() map[string]int32 {
	if x != nil {
		return x.OfferIdToAttemptsCountMap
	}
	return nil
}

type ChooseExchangerOrderOptionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor who is choosing the option
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// corresponding id of the order created for the exchanger_offer after redeeming
	ExchangerOrderId string `protobuf:"bytes,3,opt,name=exchanger_order_id,json=exchangerOrderId,proto3" json:"exchanger_order_id,omitempty"`
	// id of the option selected under the exchanger_order
	OptionId string `protobuf:"bytes,4,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
}

func (x *ChooseExchangerOrderOptionRequest) Reset() {
	*x = ChooseExchangerOrderOptionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChooseExchangerOrderOptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChooseExchangerOrderOptionRequest) ProtoMessage() {}

func (x *ChooseExchangerOrderOptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChooseExchangerOrderOptionRequest.ProtoReflect.Descriptor instead.
func (*ChooseExchangerOrderOptionRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{38}
}

func (x *ChooseExchangerOrderOptionRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ChooseExchangerOrderOptionRequest) GetExchangerOrderId() string {
	if x != nil {
		return x.ExchangerOrderId
	}
	return ""
}

func (x *ChooseExchangerOrderOptionRequest) GetOptionId() string {
	if x != nil {
		return x.OptionId
	}
	return ""
}

type ChooseExchangerOrderOptionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger-offer-order whose option was chosen
	ExchangerOfferOrder *ExchangerOfferOrder `protobuf:"bytes,2,opt,name=exchanger_offer_order,json=exchangerOfferOrder,proto3" json:"exchanger_offer_order,omitempty"`
}

func (x *ChooseExchangerOrderOptionResponse) Reset() {
	*x = ChooseExchangerOrderOptionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChooseExchangerOrderOptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChooseExchangerOrderOptionResponse) ProtoMessage() {}

func (x *ChooseExchangerOrderOptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChooseExchangerOrderOptionResponse.ProtoReflect.Descriptor instead.
func (*ChooseExchangerOrderOptionResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{39}
}

func (x *ChooseExchangerOrderOptionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ChooseExchangerOrderOptionResponse) GetExchangerOfferOrder() *ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrder
	}
	return nil
}

type CreateExchangerOfferGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// description of group
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	// rewardType units caps config for an actor across all exchanger offers in a group
	EOGroupRewardUnitsCapUserAggregate *RewardUnitsCapAggregate `protobuf:"bytes,2,opt,name=EO_group_reward_units_cap_user_aggregate,json=EOGroupRewardUnitsCapUserAggregate,proto3" json:"EO_group_reward_units_cap_user_aggregate,omitempty"`
}

func (x *CreateExchangerOfferGroupRequest) Reset() {
	*x = CreateExchangerOfferGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferGroupRequest) ProtoMessage() {}

func (x *CreateExchangerOfferGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferGroupRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{40}
}

func (x *CreateExchangerOfferGroupRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateExchangerOfferGroupRequest) GetEOGroupRewardUnitsCapUserAggregate() *RewardUnitsCapAggregate {
	if x != nil {
		return x.EOGroupRewardUnitsCapUserAggregate
	}
	return nil
}

type CreateExchangerOfferGroupResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchangerOffer group
	ExchangerOfferGroup *ExchangerOfferGroup `protobuf:"bytes,2,opt,name=exchanger_offer_group,json=exchangerOfferGroup,proto3" json:"exchanger_offer_group,omitempty"`
}

func (x *CreateExchangerOfferGroupResponse) Reset() {
	*x = CreateExchangerOfferGroupResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExchangerOfferGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangerOfferGroupResponse) ProtoMessage() {}

func (x *CreateExchangerOfferGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangerOfferGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateExchangerOfferGroupResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{41}
}

func (x *CreateExchangerOfferGroupResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateExchangerOfferGroupResponse) GetExchangerOfferGroup() *ExchangerOfferGroup {
	if x != nil {
		return x.ExchangerOfferGroup
	}
	return nil
}

type IncrementExchangerOfferInventoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exchanger offer inventory id
	ExchangerOfferInventoryId string `protobuf:"bytes,1,opt,name=exchanger_offer_inventory_id,json=exchangerOfferInventoryId,proto3" json:"exchanger_offer_inventory_id,omitempty"`
	// count by which inventory should be increased
	IncrementCount int32 `protobuf:"varint,2,opt,name=increment_count,json=incrementCount,proto3" json:"increment_count,omitempty"`
}

func (x *IncrementExchangerOfferInventoryRequest) Reset() {
	*x = IncrementExchangerOfferInventoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncrementExchangerOfferInventoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncrementExchangerOfferInventoryRequest) ProtoMessage() {}

func (x *IncrementExchangerOfferInventoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncrementExchangerOfferInventoryRequest.ProtoReflect.Descriptor instead.
func (*IncrementExchangerOfferInventoryRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{42}
}

func (x *IncrementExchangerOfferInventoryRequest) GetExchangerOfferInventoryId() string {
	if x != nil {
		return x.ExchangerOfferInventoryId
	}
	return ""
}

func (x *IncrementExchangerOfferInventoryRequest) GetIncrementCount() int32 {
	if x != nil {
		return x.IncrementCount
	}
	return 0
}

type IncrementExchangerOfferInventoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// updated exchanger offer inventory
	ExchangerOfferInventory *ExchangerOfferInventory `protobuf:"bytes,2,opt,name=exchanger_offer_inventory,json=exchangerOfferInventory,proto3" json:"exchanger_offer_inventory,omitempty"`
}

func (x *IncrementExchangerOfferInventoryResponse) Reset() {
	*x = IncrementExchangerOfferInventoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncrementExchangerOfferInventoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncrementExchangerOfferInventoryResponse) ProtoMessage() {}

func (x *IncrementExchangerOfferInventoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncrementExchangerOfferInventoryResponse.ProtoReflect.Descriptor instead.
func (*IncrementExchangerOfferInventoryResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{43}
}

func (x *IncrementExchangerOfferInventoryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IncrementExchangerOfferInventoryResponse) GetExchangerOfferInventory() *ExchangerOfferInventory {
	if x != nil {
		return x.ExchangerOfferInventory
	}
	return nil
}

type DecryptExchangerOfferOrdersDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exchanger offer orders with encrypted offer details
	ExchangerOfferOrders []*ExchangerOfferOrder `protobuf:"bytes,1,rep,name=exchanger_offer_orders,json=exchangerOfferOrders,proto3" json:"exchanger_offer_orders,omitempty"`
}

func (x *DecryptExchangerOfferOrdersDetailsRequest) Reset() {
	*x = DecryptExchangerOfferOrdersDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DecryptExchangerOfferOrdersDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptExchangerOfferOrdersDetailsRequest) ProtoMessage() {}

func (x *DecryptExchangerOfferOrdersDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptExchangerOfferOrdersDetailsRequest.ProtoReflect.Descriptor instead.
func (*DecryptExchangerOfferOrdersDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{44}
}

func (x *DecryptExchangerOfferOrdersDetailsRequest) GetExchangerOfferOrders() []*ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrders
	}
	return nil
}

type DecryptExchangerOfferOrdersDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger offer orders with decrypted offer details
	ExchangerOfferOrders []*ExchangerOfferOrder `protobuf:"bytes,2,rep,name=exchanger_offer_orders,json=exchangerOfferOrders,proto3" json:"exchanger_offer_orders,omitempty"`
}

func (x *DecryptExchangerOfferOrdersDetailsResponse) Reset() {
	*x = DecryptExchangerOfferOrdersDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DecryptExchangerOfferOrdersDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DecryptExchangerOfferOrdersDetailsResponse) ProtoMessage() {}

func (x *DecryptExchangerOfferOrdersDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DecryptExchangerOfferOrdersDetailsResponse.ProtoReflect.Descriptor instead.
func (*DecryptExchangerOfferOrdersDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{45}
}

func (x *DecryptExchangerOfferOrdersDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *DecryptExchangerOfferOrdersDetailsResponse) GetExchangerOfferOrders() []*ExchangerOfferOrder {
	if x != nil {
		return x.ExchangerOfferOrders
	}
	return nil
}

type GetRedemptionCountsForActorOfferIdsInMonthRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId  string   `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	OfferIds []string `protobuf:"bytes,2,rep,name=offer_ids,json=offerIds,proto3" json:"offer_ids,omitempty"`
	// timestamp from the month for which we want to fetch count of redemptions.
	// only the month/year will be used from the given timestamp.
	MonthTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=month_timestamp,json=monthTimestamp,proto3" json:"month_timestamp,omitempty"`
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthRequest) Reset() {
	*x = GetRedemptionCountsForActorOfferIdsInMonthRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRedemptionCountsForActorOfferIdsInMonthRequest) ProtoMessage() {}

func (x *GetRedemptionCountsForActorOfferIdsInMonthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRedemptionCountsForActorOfferIdsInMonthRequest.ProtoReflect.Descriptor instead.
func (*GetRedemptionCountsForActorOfferIdsInMonthRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{46}
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthRequest) GetOfferIds() []string {
	if x != nil {
		return x.OfferIds
	}
	return nil
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthRequest) GetMonthTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.MonthTimestamp
	}
	return nil
}

type GetRedemptionCountsForActorOfferIdsInMonthResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// exchanger offer orders with decrypted offer details
	OfferIdToRedemptionsCountInMonthMap map[string]uint32 `protobuf:"bytes,2,rep,name=offer_id_to_redemptions_count_in_month_map,json=offerIdToRedemptionsCountInMonthMap,proto3" json:"offer_id_to_redemptions_count_in_month_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthResponse) Reset() {
	*x = GetRedemptionCountsForActorOfferIdsInMonthResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRedemptionCountsForActorOfferIdsInMonthResponse) ProtoMessage() {}

func (x *GetRedemptionCountsForActorOfferIdsInMonthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRedemptionCountsForActorOfferIdsInMonthResponse.ProtoReflect.Descriptor instead.
func (*GetRedemptionCountsForActorOfferIdsInMonthResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{47}
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRedemptionCountsForActorOfferIdsInMonthResponse) GetOfferIdToRedemptionsCountInMonthMap() map[string]uint32 {
	if x != nil {
		return x.OfferIdToRedemptionsCountInMonthMap
	}
	return nil
}

type GetExchangerOffersByFiltersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// actor for whom the offers need to be fetched.
	// optional
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// filters to decide which offers to return in response
	Filters *ExchangerOfferFilters `protobuf:"bytes,2,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetExchangerOffersByFiltersRequest) Reset() {
	*x = GetExchangerOffersByFiltersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersByFiltersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersByFiltersRequest) ProtoMessage() {}

func (x *GetExchangerOffersByFiltersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersByFiltersRequest.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersByFiltersRequest) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{48}
}

func (x *GetExchangerOffersByFiltersRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetExchangerOffersByFiltersRequest) GetFilters() *ExchangerOfferFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type ExchangerOfferFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filters specific to the catalog screen (and can also include common filters across offers for the catalog screen)
	CatalogFilters *casper.CatalogFilters `protobuf:"bytes,1,opt,name=catalog_filters,json=catalogFilters,proto3" json:"catalog_filters,omitempty"`
	// status of exchanger offer
	Status ExchangerOfferStatus `protobuf:"varint,2,opt,name=status,proto3,enum=casper.exchanger.ExchangerOfferStatus" json:"status,omitempty"`
	// time window in which we want to fetch exchanger offers
	FromTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	TillTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=till_time,json=tillTime,proto3" json:"till_time,omitempty"`
}

func (x *ExchangerOfferFilters) Reset() {
	*x = ExchangerOfferFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExchangerOfferFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExchangerOfferFilters) ProtoMessage() {}

func (x *ExchangerOfferFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExchangerOfferFilters.ProtoReflect.Descriptor instead.
func (*ExchangerOfferFilters) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{49}
}

func (x *ExchangerOfferFilters) GetCatalogFilters() *casper.CatalogFilters {
	if x != nil {
		return x.CatalogFilters
	}
	return nil
}

func (x *ExchangerOfferFilters) GetStatus() ExchangerOfferStatus {
	if x != nil {
		return x.Status
	}
	return ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_UNSPECIFIED
}

func (x *ExchangerOfferFilters) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *ExchangerOfferFilters) GetTillTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TillTime
	}
	return nil
}

type GetExchangerOffersByFiltersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of active exchanger offers
	ExchangerOffers []*ExchangerOffer `protobuf:"bytes,2,rep,name=exchanger_offers,json=exchangerOffers,proto3" json:"exchanger_offers,omitempty"`
	// exchanger offer id to offer listing mapping
	OfferIdToListingMap map[string]*ExchangerOfferListing `protobuf:"bytes,3,rep,name=offer_id_to_listing_map,json=offerIdToListingMap,proto3" json:"offer_id_to_listing_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetExchangerOffersByFiltersResponse) Reset() {
	*x = GetExchangerOffersByFiltersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersByFiltersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersByFiltersResponse) ProtoMessage() {}

func (x *GetExchangerOffersByFiltersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersByFiltersResponse.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersByFiltersResponse) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetExchangerOffersByFiltersResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetExchangerOffersByFiltersResponse) GetExchangerOffers() []*ExchangerOffer {
	if x != nil {
		return x.ExchangerOffers
	}
	return nil
}

func (x *GetExchangerOffersByFiltersResponse) GetOfferIdToListingMap() map[string]*ExchangerOfferListing {
	if x != nil {
		return x.OfferIdToListingMap
	}
	return nil
}

type GetExchangerOffersOrdersSummaryRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferId     string                     `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	States      []ExchangerOfferOrderState `protobuf:"varint,2,rep,packed,name=states,proto3,enum=casper.exchanger.ExchangerOfferOrderState" json:"states,omitempty"`
	RewardTypes []RewardType               `protobuf:"varint,3,rep,packed,name=reward_types,json=rewardTypes,proto3,enum=casper.exchanger.RewardType" json:"reward_types,omitempty"`
}

func (x *GetExchangerOffersOrdersSummaryRequest_Filters) Reset() {
	*x = GetExchangerOffersOrdersSummaryRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersOrdersSummaryRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersOrdersSummaryRequest_Filters) ProtoMessage() {}

func (x *GetExchangerOffersOrdersSummaryRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersOrdersSummaryRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersOrdersSummaryRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetExchangerOffersOrdersSummaryRequest_Filters) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetExchangerOffersOrdersSummaryRequest_Filters) GetStates() []ExchangerOfferOrderState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *GetExchangerOffersOrdersSummaryRequest_Filters) GetRewardTypes() []RewardType {
	if x != nil {
		return x.RewardTypes
	}
	return nil
}

type GetExchangerOffersRequest_FiltersV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filters specific to the catalog screen (and can also include common filters across offers for the catalog screen)
	CatalogFilters *casper.CatalogFilters `protobuf:"bytes,1,opt,name=catalog_filters,json=catalogFilters,proto3" json:"catalog_filters,omitempty"`
	// status of exchanger offer
	Status ExchangerOfferStatus `protobuf:"varint,2,opt,name=status,proto3,enum=casper.exchanger.ExchangerOfferStatus" json:"status,omitempty"`
}

func (x *GetExchangerOffersRequest_FiltersV2) Reset() {
	*x = GetExchangerOffersRequest_FiltersV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersRequest_FiltersV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersRequest_FiltersV2) ProtoMessage() {}

func (x *GetExchangerOffersRequest_FiltersV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersRequest_FiltersV2.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersRequest_FiltersV2) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{22, 0}
}

func (x *GetExchangerOffersRequest_FiltersV2) GetCatalogFilters() *casper.CatalogFilters {
	if x != nil {
		return x.CatalogFilters
	}
	return nil
}

func (x *GetExchangerOffersRequest_FiltersV2) GetStatus() ExchangerOfferStatus {
	if x != nil {
		return x.Status
	}
	return ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_UNSPECIFIED
}

type GetExchangerOffersByIdsRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// status of exchanger offer
	Status ExchangerOfferStatus `protobuf:"varint,1,opt,name=status,proto3,enum=casper.exchanger.ExchangerOfferStatus" json:"status,omitempty"`
}

func (x *GetExchangerOffersByIdsRequest_Filters) Reset() {
	*x = GetExchangerOffersByIdsRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOffersByIdsRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOffersByIdsRequest_Filters) ProtoMessage() {}

func (x *GetExchangerOffersByIdsRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOffersByIdsRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetExchangerOffersByIdsRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{24, 0}
}

func (x *GetExchangerOffersByIdsRequest_Filters) GetStatus() ExchangerOfferStatus {
	if x != nil {
		return x.Status
	}
	return ExchangerOfferStatus_EXCHANGER_OFFER_STATUS_UNSPECIFIED
}

type GetExchangerOfferOrdersForActorRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferId     string                     `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	States      []ExchangerOfferOrderState `protobuf:"varint,2,rep,packed,name=states,proto3,enum=casper.exchanger.ExchangerOfferOrderState" json:"states,omitempty"`
	FromDate    *timestamppb.Timestamp     `protobuf:"bytes,3,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	UptoDate    *timestamppb.Timestamp     `protobuf:"bytes,4,opt,name=upto_date,json=uptoDate,proto3" json:"upto_date,omitempty"`
	RewardTypes []RewardType               `protobuf:"varint,5,rep,packed,name=reward_types,json=rewardTypes,proto3,enum=casper.exchanger.RewardType" json:"reward_types,omitempty"`
	// filter based on expiry status of the reward
	// REWARD_EXPIRY_STATUS_EXPIRED is currently only supported for EGV reward type and this filter will return only EGV type (chosen only) expired rewards
	// REWARD_EXPIRY_STATUS_NOT_EXPIRED returns all exchanger rewards which are not EGV type or EGV exchanger rewards which are not expired
	RewardExpiryStatus RewardExpiryStatus `protobuf:"varint,7,opt,name=reward_expiry_status,json=rewardExpiryStatus,proto3,enum=casper.exchanger.RewardExpiryStatus" json:"reward_expiry_status,omitempty"`
	// filters present in WithinOrFilters are ORed together but ANDed with other filters present in Filters
	WithinOrFilters *GetExchangerOfferOrdersForActorRequest_OrFilters `protobuf:"bytes,8,opt,name=within_or_filters,json=withinOrFilters,proto3" json:"within_or_filters,omitempty"`
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) Reset() {
	*x = GetExchangerOfferOrdersForActorRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersForActorRequest_Filters) ProtoMessage() {}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersForActorRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersForActorRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{28, 0}
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) GetStates() []ExchangerOfferOrderState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) GetUptoDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UptoDate
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) GetRewardTypes() []RewardType {
	if x != nil {
		return x.RewardTypes
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) GetRewardExpiryStatus() RewardExpiryStatus {
	if x != nil {
		return x.RewardExpiryStatus
	}
	return RewardExpiryStatus_REWARD_EXPIRY_STATUS_UNSPECIFIED
}

func (x *GetExchangerOfferOrdersForActorRequest_Filters) GetWithinOrFilters() *GetExchangerOfferOrdersForActorRequest_OrFilters {
	if x != nil {
		return x.WithinOrFilters
	}
	return nil
}

type GetExchangerOfferOrdersForActorRequest_OrFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardTypes []RewardType `protobuf:"varint,1,rep,packed,name=reward_types,json=rewardTypes,proto3,enum=casper.exchanger.RewardType" json:"reward_types,omitempty"`
	// filter based on tags present in the ExchangerOffer
	// if ANY of the passed tags is present in the ExchangerOffer, corresponding ExchangerOfferOrder will be returned
	OrOfferTags []casper.TagName `protobuf:"varint,2,rep,packed,name=or_offer_tags,json=orOfferTags,proto3,enum=casper.TagName" json:"or_offer_tags,omitempty"`
}

func (x *GetExchangerOfferOrdersForActorRequest_OrFilters) Reset() {
	*x = GetExchangerOfferOrdersForActorRequest_OrFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersForActorRequest_OrFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersForActorRequest_OrFilters) ProtoMessage() {}

func (x *GetExchangerOfferOrdersForActorRequest_OrFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersForActorRequest_OrFilters.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersForActorRequest_OrFilters) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{28, 1}
}

func (x *GetExchangerOfferOrdersForActorRequest_OrFilters) GetRewardTypes() []RewardType {
	if x != nil {
		return x.RewardTypes
	}
	return nil
}

func (x *GetExchangerOfferOrdersForActorRequest_OrFilters) GetOrOfferTags() []casper.TagName {
	if x != nil {
		return x.OrOfferTags
	}
	return nil
}

type GetExchangerOfferOrdersRequest_Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferId     string                     `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	States      []ExchangerOfferOrderState `protobuf:"varint,2,rep,packed,name=states,proto3,enum=casper.exchanger.ExchangerOfferOrderState" json:"states,omitempty"`
	FromDate    *timestamppb.Timestamp     `protobuf:"bytes,3,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	UptoDate    *timestamppb.Timestamp     `protobuf:"bytes,4,opt,name=upto_date,json=uptoDate,proto3" json:"upto_date,omitempty"`
	RewardTypes []RewardType               `protobuf:"varint,5,rep,packed,name=reward_types,json=rewardTypes,proto3,enum=casper.exchanger.RewardType" json:"reward_types,omitempty"`
}

func (x *GetExchangerOfferOrdersRequest_Filters) Reset() {
	*x = GetExchangerOfferOrdersRequest_Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_exchanger_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExchangerOfferOrdersRequest_Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExchangerOfferOrdersRequest_Filters) ProtoMessage() {}

func (x *GetExchangerOfferOrdersRequest_Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_exchanger_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExchangerOfferOrdersRequest_Filters.ProtoReflect.Descriptor instead.
func (*GetExchangerOfferOrdersRequest_Filters) Descriptor() ([]byte, []int) {
	return file_api_casper_exchanger_service_proto_rawDescGZIP(), []int{30, 0}
}

func (x *GetExchangerOfferOrdersRequest_Filters) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *GetExchangerOfferOrdersRequest_Filters) GetStates() []ExchangerOfferOrderState {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *GetExchangerOfferOrdersRequest_Filters) GetFromDate() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetExchangerOfferOrdersRequest_Filters) GetUptoDate() *timestamppb.Timestamp {
	if x != nil {
		return x.UptoDate
	}
	return nil
}

func (x *GetExchangerOfferOrdersRequest_Filters) GetRewardTypes() []RewardType {
	if x != nil {
		return x.RewardTypes
	}
	return nil
}

var File_api_casper_exchanger_service_proto protoreflect.FileDescriptor

var file_api_casper_exchanger_service_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2f, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xcd, 0x01, 0x0a, 0x25, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x37, 0x0a, 0x12, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x68, 0x69,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x0f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x22, 0xa8, 0x01, 0x0a, 0x26, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x59, 0x0a, 0x15, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x13, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xce, 0x01, 0x0a, 0x24,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x70, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xb3, 0x01, 0x0a,
	0x25, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x65, 0x0a, 0x19, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x17, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f,
	0x72, 0x79, 0x22, 0x42, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x79, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x22, 0xb9, 0x02, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x7f, 0x0a, 0x15, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x5f, 0x74, 0x6f, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x79,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x54, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x11, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x54, 0x6f, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4d, 0x61, 0x70, 0x1a, 0x6b, 0x0a, 0x16, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x54, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x3b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x67, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x45, 0x4f, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x22, 0x82, 0x03, 0x0a, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x4f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x74, 0x69, 0x6c, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x9b, 0x01, 0x0a, 0x1b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5d, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x4f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e,
	0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x54, 0x6f, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x17, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x54, 0x6f, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61,
	0x70, 0x1a, 0x8c, 0x01, 0x0a, 0x1c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x54, 0x6f, 0x55,
	0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x56, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0xd6, 0x02, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x5a, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x40, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0xa9, 0x01,
	0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x8f, 0x03, 0x0a, 0x27, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x63, 0x61,
	0x73, 0x68, 0x5f, 0x65, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x73, 0x68, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x12,
	0x26, 0x0a, 0x0f, 0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x65, 0x61, 0x72, 0x6e,
	0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e,
	0x73, 0x45, 0x61, 0x72, 0x6e, 0x65, 0x64, 0x12, 0x54, 0x0a, 0x1d, 0x69, 0x6e, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x19, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x73,
	0x68, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a,
	0x13, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x69, 0x5f, 0x63,
	0x6f, 0x69, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x69, 0x6e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x2a, 0x0a, 0x11,
	0x63, 0x61, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x73, 0x68, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x66, 0x69, 0x5f, 0x63,
	0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x66, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xad, 0x06, 0x0a, 0x1b,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6d, 0x0a, 0x13, 0x72,
	0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x12, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65,
	0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x6c, 0x0a, 0x15, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x13,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x69, 0x0a, 0x14, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x72,
	0x0a, 0x17, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x15, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x60, 0x0a,
	0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x30, 0x0a, 0x0b, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x10,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x54, 0x61,
	0x67, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x54, 0x61, 0x67,
	0x73, 0x12, 0x36, 0x0a, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x52, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x12, 0x40, 0x0a, 0x10, 0x73, 0x75, 0x62,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x52, 0x0e, 0x73, 0x75, 0x62,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x22, 0x8e, 0x01, 0x0a, 0x1c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x49, 0x0a, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x22, 0x95, 0x02, 0x0a,
	0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x12, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x10, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0c,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0b, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x2e, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x54, 0x69, 0x6c, 0x6c, 0x22, 0xab, 0x01, 0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x5f, 0x0a, 0x17, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x15, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x22, 0xc6, 0x03, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x5e, 0x0a, 0x13, 0x6e, 0x65, 0x77, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x6e, 0x65,
	0x77, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x67, 0x0a, 0x16, 0x6e, 0x65, 0x77, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x31, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x14, 0x6e, 0x65, 0x77, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x37, 0x0a, 0x0f, 0x6e, 0x65, 0x77, 0x5f,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x54, 0x61, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x54, 0x61, 0x67,
	0x73, 0x12, 0x36, 0x0a, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x52, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x12, 0x40, 0x0a, 0x10, 0x73, 0x75, 0x62,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x52, 0x0e, 0x73, 0x75, 0x62,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x67, 0x22, 0x95, 0x01, 0x0a, 0x23,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x0e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x22, 0x85, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x09, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x94, 0x01, 0x0a, 0x22,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x0e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x22, 0xe5, 0x01, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x10, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x0e,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x69, 0x6e, 0x63,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6c, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54, 0x69,
	0x6c, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x69,
	0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6c, 0x6c, 0x22, 0x98, 0x01, 0x0a, 0x23, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x22, 0x59, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x10, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64,
	0x52, 0x0e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64,
	0x22, 0x4a, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd1, 0x02, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x54, 0x0a, 0x0a, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x76, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x56, 0x32, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x56,
	0x32, 0x1a, 0x8c, 0x01, 0x0a, 0x09, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x56, 0x32, 0x12,
	0x3f, 0x0a, 0x0f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x0e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xfc, 0x02, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x52, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x12, 0x7b, 0x0a, 0x17, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x67, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x70, 0x1a, 0x6f,
	0x0a, 0x18, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xd1, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x12, 0x52, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x49, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x10,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x1b, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x12, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64,
	0x52, 0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x22, 0xe1, 0x01, 0x0a, 0x1c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x15, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x13,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x22, 0x41, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f,
	0x46, 0x52, 0x4f, 0x5a, 0x45, 0x4e, 0x10, 0x64, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x41, 0x56, 0x49,
	0x4e, 0x47, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x65, 0x22, 0xd6, 0x06, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x1a,
	0xe9, 0x03, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44,
	0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09, 0x75, 0x70, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x08, 0x75, 0x70, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0c,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x56, 0x0a,
	0x14, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x12, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6e, 0x0a, 0x11, 0x77, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x5f,
	0x6f, 0x72, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x42, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x52, 0x0f, 0x77, 0x69, 0x74, 0x68, 0x69, 0x6e, 0x4f, 0x72, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x07, 0x1a, 0x81, 0x01, 0x0a, 0x09,
	0x4f, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3f, 0x0a, 0x0c, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x1c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0d, 0x6f, 0x72,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x54, 0x61, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x0b, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x61, 0x67, 0x73, 0x22,
	0xe8, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x5b, 0x0a, 0x16, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x14, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a,
	0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0xe9, 0x03, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3a, 0x0a, 0x0c,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x9b, 0x02, 0x0a, 0x07, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x42, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x09,
	0x75, 0x70, 0x74, 0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x75, 0x70, 0x74,
	0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xe0, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x5b, 0x0a, 0x16, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x14, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a, 0x0c,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22, 0x4c, 0x0a, 0x1c, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4e,
	0x0a, 0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xaa,
	0x02, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x37,
	0x0a, 0x12, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x11, 0x66, 0x72, 0x6f, 0x6d, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x11, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x74,
	0x6f, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0f, 0x74, 0x6f, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc0, 0x01, 0x0a, 0x26,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x71, 0x0a, 0x1e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x52, 0x1b, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x22, 0xb7,
	0x02, 0x0a, 0x2b, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x13, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x22, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18,
	0x64, 0x52, 0x11, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x52, 0x0a, 0x11, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x11, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4e, 0x0a, 0x0f, 0x74, 0x6f, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0f, 0x74, 0x6f, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc4, 0x02, 0x0a, 0x2c, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xa0,
	0x01, 0x0a, 0x1e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x61,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54,
	0x6f, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61,
	0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x19, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54,
	0x6f, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61,
	0x70, 0x1a, 0x4c, 0x0a, 0x1e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xaa, 0x01, 0x0a, 0x21, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04,
	0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x12, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04,
	0x18, 0x64, 0x52, 0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x09, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x04,
	0x18, 0x64, 0x52, 0x08, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xa4, 0x01, 0x0a,
	0x22, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x15, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x13,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x22, 0xc5, 0x01, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x7f, 0x0a, 0x28, 0x45, 0x4f,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e,
	0x69, 0x74, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x22, 0x45, 0x4f, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x43, 0x61, 0x70, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x21,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x15, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x13, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x22, 0x9c, 0x01, 0x0a, 0x27, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a,
	0x1c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x19, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x30,
	0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x0e, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0xb6, 0x01, 0x0a, 0x28, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65,
	0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x65, 0x0a, 0x19, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79,
	0x52, 0x17, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x22, 0x88, 0x01, 0x0a, 0x29, 0x44, 0x65,
	0x63, 0x72, 0x79, 0x70, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x16, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x14,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x22, 0xae, 0x01, 0x0a, 0x2a, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x16, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x14, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0xcf, 0x01, 0x0a, 0x31, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64,
	0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72,
	0x41, 0x63, 0x74, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x49, 0x6e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x04, 0x18, 0x64, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x4d, 0x0a, 0x0f, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xfa, 0x02, 0x0a, 0x32, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x49,
	0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0xc6, 0x01, 0x0a, 0x2a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x5f, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x6d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x49,
	0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x23, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64,
	0x54, 0x6f, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x1a, 0x56, 0x0a, 0x28,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x82, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x8a, 0x02, 0x0a, 0x15, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x12, 0x3f, 0x0a, 0x0f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x52, 0x0e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a,
	0x09, 0x74, 0x69, 0x6c, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x74, 0x69,
	0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8f, 0x03, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x10, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52,
	0x0f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x12, 0x84, 0x01, 0x0a, 0x17, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x13, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x70, 0x1a, 0x6f, 0x0a, 0x18, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x64, 0x54, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x9d, 0x1c, 0x0a, 0x15, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x75, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x1b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x35, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49,
	0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x36, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x37, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x1b, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12, 0x34, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x35, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x8a, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x12, 0x34, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01,
	0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x2e,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6f, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73,
	0x12, 0x2b, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e,
	0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x30, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x14, 0x52,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x12, 0x2d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x39, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x17, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x30, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x42, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x37, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65,
	0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x24,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x1a, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x33, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x68, 0x6f, 0x6f, 0x73,
	0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01,
	0x0a, 0x1e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x37, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x43, 0x68, 0x6f, 0x73, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70,
	0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x46, 0x6f, 0x72, 0x43,
	0x68, 0x6f, 0x73, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x38, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72,
	0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x39, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a,
	0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x32, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x8d, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42,
	0x79, 0x49, 0x64, 0x73, 0x12, 0x35, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42,
	0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xab, 0x01, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x45, 0x4f, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x4f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x74, 0x69,
	0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x40, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x4f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x55, 0x74,
	0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x99, 0x01, 0x0a, 0x20, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76,
	0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x39, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x49, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x65,
	0x6e, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9f, 0x01,
	0x0a, 0x22, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x63, 0x72, 0x79, 0x70, 0x74, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xb9, 0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x43,
	0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x73, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8a, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x73, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x72, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x65, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_casper_exchanger_service_proto_rawDescOnce sync.Once
	file_api_casper_exchanger_service_proto_rawDescData = file_api_casper_exchanger_service_proto_rawDesc
)

func file_api_casper_exchanger_service_proto_rawDescGZIP() []byte {
	file_api_casper_exchanger_service_proto_rawDescOnce.Do(func() {
		file_api_casper_exchanger_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_casper_exchanger_service_proto_rawDescData)
	})
	return file_api_casper_exchanger_service_proto_rawDescData
}

var file_api_casper_exchanger_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_casper_exchanger_service_proto_msgTypes = make([]protoimpl.MessageInfo, 63)
var file_api_casper_exchanger_service_proto_goTypes = []interface{}{
	(RedeemExchangerOfferResponse_Status)(0),                   // 0: casper.exchanger.RedeemExchangerOfferResponse.Status
	(*SubmitUserInputForChosenOptionRequest)(nil),              // 1: casper.exchanger.SubmitUserInputForChosenOptionRequest
	(*SubmitUserInputForChosenOptionResponse)(nil),             // 2: casper.exchanger.SubmitUserInputForChosenOptionResponse
	(*CreateExchangerOfferInventoryRequest)(nil),               // 3: casper.exchanger.CreateExchangerOfferInventoryRequest
	(*CreateExchangerOfferInventoryResponse)(nil),              // 4: casper.exchanger.CreateExchangerOfferInventoryResponse
	(*GetExchangerOfferGroupsByIdsRequest)(nil),                // 5: casper.exchanger.GetExchangerOfferGroupsByIdsRequest
	(*GetExchangerOfferGroupsByIdsResponse)(nil),               // 6: casper.exchanger.GetExchangerOfferGroupsByIdsResponse
	(*GetEOGroupsRewardUnitsActorUtilisationRequest)(nil),      // 7: casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationRequest
	(*GetEOGroupsRewardUnitsActorUtilisationResponse)(nil),     // 8: casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse
	(*GetExchangerOffersOrdersSummaryRequest)(nil),             // 9: casper.exchanger.GetExchangerOffersOrdersSummaryRequest
	(*GetExchangerOffersOrdersSummaryResponse)(nil),            // 10: casper.exchanger.GetExchangerOffersOrdersSummaryResponse
	(*CreateExchangerOfferRequest)(nil),                        // 11: casper.exchanger.CreateExchangerOfferRequest
	(*CreateExchangerOfferResponse)(nil),                       // 12: casper.exchanger.CreateExchangerOfferResponse
	(*CreateExchangerOfferListingRequest)(nil),                 // 13: casper.exchanger.CreateExchangerOfferListingRequest
	(*CreateExchangerOfferListingResponse)(nil),                // 14: casper.exchanger.CreateExchangerOfferListingResponse
	(*UpdateExchangerOfferDisplayRequest)(nil),                 // 15: casper.exchanger.UpdateExchangerOfferDisplayRequest
	(*UpdateExchangerOfferDisplayResponse)(nil),                // 16: casper.exchanger.UpdateExchangerOfferDisplayResponse
	(*UpdateExchangerOfferStatusRequest)(nil),                  // 17: casper.exchanger.UpdateExchangerOfferStatusRequest
	(*UpdateExchangerOfferStatusResponse)(nil),                 // 18: casper.exchanger.UpdateExchangerOfferStatusResponse
	(*UpdateExchangerOfferListingRequest)(nil),                 // 19: casper.exchanger.UpdateExchangerOfferListingRequest
	(*UpdateExchangerOfferListingResponse)(nil),                // 20: casper.exchanger.UpdateExchangerOfferListingResponse
	(*DeleteExchangerOfferListingRequest)(nil),                 // 21: casper.exchanger.DeleteExchangerOfferListingRequest
	(*DeleteExchangerOfferListingResponse)(nil),                // 22: casper.exchanger.DeleteExchangerOfferListingResponse
	(*GetExchangerOffersRequest)(nil),                          // 23: casper.exchanger.GetExchangerOffersRequest
	(*GetExchangerOffersResponse)(nil),                         // 24: casper.exchanger.GetExchangerOffersResponse
	(*GetExchangerOffersByIdsRequest)(nil),                     // 25: casper.exchanger.GetExchangerOffersByIdsRequest
	(*GetExchangerOffersByIdsResponse)(nil),                    // 26: casper.exchanger.GetExchangerOffersByIdsResponse
	(*RedeemExchangerOfferRequest)(nil),                        // 27: casper.exchanger.RedeemExchangerOfferRequest
	(*RedeemExchangerOfferResponse)(nil),                       // 28: casper.exchanger.RedeemExchangerOfferResponse
	(*GetExchangerOfferOrdersForActorRequest)(nil),             // 29: casper.exchanger.GetExchangerOfferOrdersForActorRequest
	(*GetExchangerOfferOrdersForActorResponse)(nil),            // 30: casper.exchanger.GetExchangerOfferOrdersForActorResponse
	(*GetExchangerOfferOrdersRequest)(nil),                     // 31: casper.exchanger.GetExchangerOfferOrdersRequest
	(*GetExchangerOfferOrdersResponse)(nil),                    // 32: casper.exchanger.GetExchangerOfferOrdersResponse
	(*GetExchangerOrderByIdRequest)(nil),                       // 33: casper.exchanger.GetExchangerOrderByIdRequest
	(*GetExchangerOrderByIdResponse)(nil),                      // 34: casper.exchanger.GetExchangerOrderByIdResponse
	(*GetExchangerOfferActorAttemptsRequest)(nil),              // 35: casper.exchanger.GetExchangerOfferActorAttemptsRequest
	(*GetExchangerOfferActorAttemptsResponse)(nil),             // 36: casper.exchanger.GetExchangerOfferActorAttemptsResponse
	(*GetExchangerOffersActorAttemptsCountRequest)(nil),        // 37: casper.exchanger.GetExchangerOffersActorAttemptsCountRequest
	(*GetExchangerOffersActorAttemptsCountResponse)(nil),       // 38: casper.exchanger.GetExchangerOffersActorAttemptsCountResponse
	(*ChooseExchangerOrderOptionRequest)(nil),                  // 39: casper.exchanger.ChooseExchangerOrderOptionRequest
	(*ChooseExchangerOrderOptionResponse)(nil),                 // 40: casper.exchanger.ChooseExchangerOrderOptionResponse
	(*CreateExchangerOfferGroupRequest)(nil),                   // 41: casper.exchanger.CreateExchangerOfferGroupRequest
	(*CreateExchangerOfferGroupResponse)(nil),                  // 42: casper.exchanger.CreateExchangerOfferGroupResponse
	(*IncrementExchangerOfferInventoryRequest)(nil),            // 43: casper.exchanger.IncrementExchangerOfferInventoryRequest
	(*IncrementExchangerOfferInventoryResponse)(nil),           // 44: casper.exchanger.IncrementExchangerOfferInventoryResponse
	(*DecryptExchangerOfferOrdersDetailsRequest)(nil),          // 45: casper.exchanger.DecryptExchangerOfferOrdersDetailsRequest
	(*DecryptExchangerOfferOrdersDetailsResponse)(nil),         // 46: casper.exchanger.DecryptExchangerOfferOrdersDetailsResponse
	(*GetRedemptionCountsForActorOfferIdsInMonthRequest)(nil),  // 47: casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthRequest
	(*GetRedemptionCountsForActorOfferIdsInMonthResponse)(nil), // 48: casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse
	(*GetExchangerOffersByFiltersRequest)(nil),                 // 49: casper.exchanger.GetExchangerOffersByFiltersRequest
	(*ExchangerOfferFilters)(nil),                              // 50: casper.exchanger.ExchangerOfferFilters
	(*GetExchangerOffersByFiltersResponse)(nil),                // 51: casper.exchanger.GetExchangerOffersByFiltersResponse
	nil, // 52: casper.exchanger.GetExchangerOfferGroupsByIdsResponse.GroupIdToGroupMapEntry
	nil, // 53: casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse.GroupIdToUtilisationMapEntry
	(*GetExchangerOffersOrdersSummaryRequest_Filters)(nil), // 54: casper.exchanger.GetExchangerOffersOrdersSummaryRequest.Filters
	(*GetExchangerOffersRequest_FiltersV2)(nil),            // 55: casper.exchanger.GetExchangerOffersRequest.FiltersV2
	nil, // 56: casper.exchanger.GetExchangerOffersResponse.OfferIdToListingMapEntry
	(*GetExchangerOffersByIdsRequest_Filters)(nil),           // 57: casper.exchanger.GetExchangerOffersByIdsRequest.Filters
	(*GetExchangerOfferOrdersForActorRequest_Filters)(nil),   // 58: casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters
	(*GetExchangerOfferOrdersForActorRequest_OrFilters)(nil), // 59: casper.exchanger.GetExchangerOfferOrdersForActorRequest.OrFilters
	(*GetExchangerOfferOrdersRequest_Filters)(nil),           // 60: casper.exchanger.GetExchangerOfferOrdersRequest.Filters
	nil,                                                    // 61: casper.exchanger.GetExchangerOffersActorAttemptsCountResponse.OfferIdToAttemptsCountMapEntry
	nil,                                                    // 62: casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse.OfferIdToRedemptionsCountInMonthMapEntry
	nil,                                                    // 63: casper.exchanger.GetExchangerOffersByFiltersResponse.OfferIdToListingMapEntry
	(*postaladdress.PostalAddress)(nil),                    // 64: google.type.PostalAddress
	(*rpc.Status)(nil),                                     // 65: rpc.Status
	(*ExchangerOfferOrder)(nil),                            // 66: casper.exchanger.ExchangerOfferOrder
	(RewardType)(0),                                        // 67: casper.exchanger.RewardType
	(*ExchangerOfferInventory)(nil),                        // 68: casper.exchanger.ExchangerOfferInventory
	(*money.Money)(nil),                                    // 69: google.type.Money
	(ExchangerOfferRedemptionCurrency)(0),                  // 70: casper.exchanger.ExchangerOfferRedemptionCurrency
	(*ExchangerOfferDisplayDetails)(nil),                   // 71: casper.exchanger.ExchangerOfferDisplayDetails
	(*ExchangerOfferOptionsConfig)(nil),                    // 72: casper.exchanger.ExchangerOfferOptionsConfig
	(*ExchangerOfferAggregatesConfig)(nil),                 // 73: casper.exchanger.ExchangerOfferAggregatesConfig
	(*ExchangerOfferAdditionalDetails)(nil),                // 74: casper.exchanger.ExchangerOfferAdditionalDetails
	(casper.TagName)(0),                                    // 75: casper.TagName
	(casper.CategoryTag)(0),                                // 76: casper.CategoryTag
	(casper.SubCategoryTag)(0),                             // 77: casper.SubCategoryTag
	(*ExchangerOffer)(nil),                                 // 78: casper.exchanger.ExchangerOffer
	(*ExchangerOfferListing)(nil),                          // 79: casper.exchanger.ExchangerOfferListing
	(ExchangerOfferStatus)(0),                              // 80: casper.exchanger.ExchangerOfferStatus
	(*casper.CatalogFilters)(nil),                          // 81: casper.CatalogFilters
	(*rpc.PageContextRequest)(nil),                         // 82: rpc.PageContextRequest
	(*rpc.PageContextResponse)(nil),                        // 83: rpc.PageContextResponse
	(*timestamppb.Timestamp)(nil),                          // 84: google.protobuf.Timestamp
	(*ExchangerOfferActorAttempt)(nil),                     // 85: casper.exchanger.ExchangerOfferActorAttempt
	(*RewardUnitsCapAggregate)(nil),                        // 86: casper.exchanger.RewardUnitsCapAggregate
	(*ExchangerOfferGroup)(nil),                            // 87: casper.exchanger.ExchangerOfferGroup
	(*ExchangerOfferGroupRewardUnitsActorUtilisation)(nil), // 88: casper.exchanger.ExchangerOfferGroupRewardUnitsActorUtilisation
	(ExchangerOfferOrderState)(0),                          // 89: casper.exchanger.ExchangerOfferOrderState
	(RewardExpiryStatus)(0),                                // 90: casper.exchanger.RewardExpiryStatus
}
var file_api_casper_exchanger_service_proto_depIdxs = []int32{
	64,  // 0: casper.exchanger.SubmitUserInputForChosenOptionRequest.shipping_address:type_name -> google.type.PostalAddress
	65,  // 1: casper.exchanger.SubmitUserInputForChosenOptionResponse.status:type_name -> rpc.Status
	66,  // 2: casper.exchanger.SubmitUserInputForChosenOptionResponse.exchanger_offer_order:type_name -> casper.exchanger.ExchangerOfferOrder
	67,  // 3: casper.exchanger.CreateExchangerOfferInventoryRequest.reward_type:type_name -> casper.exchanger.RewardType
	65,  // 4: casper.exchanger.CreateExchangerOfferInventoryResponse.status:type_name -> rpc.Status
	68,  // 5: casper.exchanger.CreateExchangerOfferInventoryResponse.exchanger_offer_inventory:type_name -> casper.exchanger.ExchangerOfferInventory
	65,  // 6: casper.exchanger.GetExchangerOfferGroupsByIdsResponse.status:type_name -> rpc.Status
	52,  // 7: casper.exchanger.GetExchangerOfferGroupsByIdsResponse.group_id_to_group_map:type_name -> casper.exchanger.GetExchangerOfferGroupsByIdsResponse.GroupIdToGroupMapEntry
	65,  // 8: casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse.status:type_name -> rpc.Status
	53,  // 9: casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse.group_id_to_utilisation_map:type_name -> casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse.GroupIdToUtilisationMapEntry
	54,  // 10: casper.exchanger.GetExchangerOffersOrdersSummaryRequest.filters:type_name -> casper.exchanger.GetExchangerOffersOrdersSummaryRequest.Filters
	65,  // 11: casper.exchanger.GetExchangerOffersOrdersSummaryResponse.status:type_name -> rpc.Status
	69,  // 12: casper.exchanger.GetExchangerOffersOrdersSummaryResponse.cash_earned:type_name -> google.type.Money
	69,  // 13: casper.exchanger.GetExchangerOffersOrdersSummaryResponse.in_process_cash_reward_amount:type_name -> google.type.Money
	70,  // 14: casper.exchanger.CreateExchangerOfferRequest.redemption_currency:type_name -> casper.exchanger.ExchangerOfferRedemptionCurrency
	71,  // 15: casper.exchanger.CreateExchangerOfferRequest.offer_display_details:type_name -> casper.exchanger.ExchangerOfferDisplayDetails
	72,  // 16: casper.exchanger.CreateExchangerOfferRequest.offer_options_config:type_name -> casper.exchanger.ExchangerOfferOptionsConfig
	73,  // 17: casper.exchanger.CreateExchangerOfferRequest.offer_aggregates_config:type_name -> casper.exchanger.ExchangerOfferAggregatesConfig
	74,  // 18: casper.exchanger.CreateExchangerOfferRequest.additional_details:type_name -> casper.exchanger.ExchangerOfferAdditionalDetails
	75,  // 19: casper.exchanger.CreateExchangerOfferRequest.manual_tags:type_name -> casper.TagName
	76,  // 20: casper.exchanger.CreateExchangerOfferRequest.category_tag:type_name -> casper.CategoryTag
	77,  // 21: casper.exchanger.CreateExchangerOfferRequest.sub_category_tag:type_name -> casper.SubCategoryTag
	65,  // 22: casper.exchanger.CreateExchangerOfferResponse.status:type_name -> rpc.Status
	78,  // 23: casper.exchanger.CreateExchangerOfferResponse.exchanger_offer:type_name -> casper.exchanger.ExchangerOffer
	65,  // 24: casper.exchanger.CreateExchangerOfferListingResponse.status:type_name -> rpc.Status
	79,  // 25: casper.exchanger.CreateExchangerOfferListingResponse.exchanger_offer_listing:type_name -> casper.exchanger.ExchangerOfferListing
	71,  // 26: casper.exchanger.UpdateExchangerOfferDisplayRequest.new_display_details:type_name -> casper.exchanger.ExchangerOfferDisplayDetails
	74,  // 27: casper.exchanger.UpdateExchangerOfferDisplayRequest.new_additional_details:type_name -> casper.exchanger.ExchangerOfferAdditionalDetails
	75,  // 28: casper.exchanger.UpdateExchangerOfferDisplayRequest.new_manual_tags:type_name -> casper.TagName
	76,  // 29: casper.exchanger.UpdateExchangerOfferDisplayRequest.category_tag:type_name -> casper.CategoryTag
	77,  // 30: casper.exchanger.UpdateExchangerOfferDisplayRequest.sub_category_tag:type_name -> casper.SubCategoryTag
	65,  // 31: casper.exchanger.UpdateExchangerOfferDisplayResponse.status:type_name -> rpc.Status
	78,  // 32: casper.exchanger.UpdateExchangerOfferDisplayResponse.exchanger_offer:type_name -> casper.exchanger.ExchangerOffer
	80,  // 33: casper.exchanger.UpdateExchangerOfferStatusRequest.new_status:type_name -> casper.exchanger.ExchangerOfferStatus
	65,  // 34: casper.exchanger.UpdateExchangerOfferStatusResponse.status:type_name -> rpc.Status
	78,  // 35: casper.exchanger.UpdateExchangerOfferStatusResponse.exchanger_offer:type_name -> casper.exchanger.ExchangerOffer
	65,  // 36: casper.exchanger.UpdateExchangerOfferListingResponse.status:type_name -> rpc.Status
	79,  // 37: casper.exchanger.UpdateExchangerOfferListingResponse.offer_listing:type_name -> casper.exchanger.ExchangerOfferListing
	65,  // 38: casper.exchanger.DeleteExchangerOfferListingResponse.status:type_name -> rpc.Status
	81,  // 39: casper.exchanger.GetExchangerOffersRequest.filters:type_name -> casper.CatalogFilters
	55,  // 40: casper.exchanger.GetExchangerOffersRequest.filters_v2:type_name -> casper.exchanger.GetExchangerOffersRequest.FiltersV2
	65,  // 41: casper.exchanger.GetExchangerOffersResponse.status:type_name -> rpc.Status
	78,  // 42: casper.exchanger.GetExchangerOffersResponse.exchanger_offers:type_name -> casper.exchanger.ExchangerOffer
	56,  // 43: casper.exchanger.GetExchangerOffersResponse.offer_id_to_listing_map:type_name -> casper.exchanger.GetExchangerOffersResponse.OfferIdToListingMapEntry
	57,  // 44: casper.exchanger.GetExchangerOffersByIdsRequest.filters:type_name -> casper.exchanger.GetExchangerOffersByIdsRequest.Filters
	65,  // 45: casper.exchanger.GetExchangerOffersByIdsResponse.status:type_name -> rpc.Status
	78,  // 46: casper.exchanger.GetExchangerOffersByIdsResponse.exchanger_offers:type_name -> casper.exchanger.ExchangerOffer
	65,  // 47: casper.exchanger.RedeemExchangerOfferResponse.status:type_name -> rpc.Status
	66,  // 48: casper.exchanger.RedeemExchangerOfferResponse.exchanger_offer_order:type_name -> casper.exchanger.ExchangerOfferOrder
	58,  // 49: casper.exchanger.GetExchangerOfferOrdersForActorRequest.filters:type_name -> casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters
	82,  // 50: casper.exchanger.GetExchangerOfferOrdersForActorRequest.page_context:type_name -> rpc.PageContextRequest
	65,  // 51: casper.exchanger.GetExchangerOfferOrdersForActorResponse.status:type_name -> rpc.Status
	66,  // 52: casper.exchanger.GetExchangerOfferOrdersForActorResponse.exchanger_offer_orders:type_name -> casper.exchanger.ExchangerOfferOrder
	83,  // 53: casper.exchanger.GetExchangerOfferOrdersForActorResponse.page_context:type_name -> rpc.PageContextResponse
	60,  // 54: casper.exchanger.GetExchangerOfferOrdersRequest.filters:type_name -> casper.exchanger.GetExchangerOfferOrdersRequest.Filters
	82,  // 55: casper.exchanger.GetExchangerOfferOrdersRequest.page_context:type_name -> rpc.PageContextRequest
	65,  // 56: casper.exchanger.GetExchangerOfferOrdersResponse.status:type_name -> rpc.Status
	66,  // 57: casper.exchanger.GetExchangerOfferOrdersResponse.exchanger_offer_orders:type_name -> casper.exchanger.ExchangerOfferOrder
	83,  // 58: casper.exchanger.GetExchangerOfferOrdersResponse.page_context:type_name -> rpc.PageContextResponse
	65,  // 59: casper.exchanger.GetExchangerOrderByIdResponse.status:type_name -> rpc.Status
	66,  // 60: casper.exchanger.GetExchangerOrderByIdResponse.exchanger_order:type_name -> casper.exchanger.ExchangerOfferOrder
	84,  // 61: casper.exchanger.GetExchangerOfferActorAttemptsRequest.fromAttemptedTime:type_name -> google.protobuf.Timestamp
	84,  // 62: casper.exchanger.GetExchangerOfferActorAttemptsRequest.toAttemptedTime:type_name -> google.protobuf.Timestamp
	65,  // 63: casper.exchanger.GetExchangerOfferActorAttemptsResponse.status:type_name -> rpc.Status
	85,  // 64: casper.exchanger.GetExchangerOfferActorAttemptsResponse.exchanger_offer_actor_attempts:type_name -> casper.exchanger.ExchangerOfferActorAttempt
	84,  // 65: casper.exchanger.GetExchangerOffersActorAttemptsCountRequest.fromAttemptedTime:type_name -> google.protobuf.Timestamp
	84,  // 66: casper.exchanger.GetExchangerOffersActorAttemptsCountRequest.toAttemptedTime:type_name -> google.protobuf.Timestamp
	65,  // 67: casper.exchanger.GetExchangerOffersActorAttemptsCountResponse.status:type_name -> rpc.Status
	61,  // 68: casper.exchanger.GetExchangerOffersActorAttemptsCountResponse.offer_id_to_attempts_count_map:type_name -> casper.exchanger.GetExchangerOffersActorAttemptsCountResponse.OfferIdToAttemptsCountMapEntry
	65,  // 69: casper.exchanger.ChooseExchangerOrderOptionResponse.status:type_name -> rpc.Status
	66,  // 70: casper.exchanger.ChooseExchangerOrderOptionResponse.exchanger_offer_order:type_name -> casper.exchanger.ExchangerOfferOrder
	86,  // 71: casper.exchanger.CreateExchangerOfferGroupRequest.EO_group_reward_units_cap_user_aggregate:type_name -> casper.exchanger.RewardUnitsCapAggregate
	65,  // 72: casper.exchanger.CreateExchangerOfferGroupResponse.status:type_name -> rpc.Status
	87,  // 73: casper.exchanger.CreateExchangerOfferGroupResponse.exchanger_offer_group:type_name -> casper.exchanger.ExchangerOfferGroup
	65,  // 74: casper.exchanger.IncrementExchangerOfferInventoryResponse.status:type_name -> rpc.Status
	68,  // 75: casper.exchanger.IncrementExchangerOfferInventoryResponse.exchanger_offer_inventory:type_name -> casper.exchanger.ExchangerOfferInventory
	66,  // 76: casper.exchanger.DecryptExchangerOfferOrdersDetailsRequest.exchanger_offer_orders:type_name -> casper.exchanger.ExchangerOfferOrder
	65,  // 77: casper.exchanger.DecryptExchangerOfferOrdersDetailsResponse.status:type_name -> rpc.Status
	66,  // 78: casper.exchanger.DecryptExchangerOfferOrdersDetailsResponse.exchanger_offer_orders:type_name -> casper.exchanger.ExchangerOfferOrder
	84,  // 79: casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthRequest.month_timestamp:type_name -> google.protobuf.Timestamp
	65,  // 80: casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse.status:type_name -> rpc.Status
	62,  // 81: casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse.offer_id_to_redemptions_count_in_month_map:type_name -> casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse.OfferIdToRedemptionsCountInMonthMapEntry
	50,  // 82: casper.exchanger.GetExchangerOffersByFiltersRequest.filters:type_name -> casper.exchanger.ExchangerOfferFilters
	81,  // 83: casper.exchanger.ExchangerOfferFilters.catalog_filters:type_name -> casper.CatalogFilters
	80,  // 84: casper.exchanger.ExchangerOfferFilters.status:type_name -> casper.exchanger.ExchangerOfferStatus
	84,  // 85: casper.exchanger.ExchangerOfferFilters.from_time:type_name -> google.protobuf.Timestamp
	84,  // 86: casper.exchanger.ExchangerOfferFilters.till_time:type_name -> google.protobuf.Timestamp
	65,  // 87: casper.exchanger.GetExchangerOffersByFiltersResponse.status:type_name -> rpc.Status
	78,  // 88: casper.exchanger.GetExchangerOffersByFiltersResponse.exchanger_offers:type_name -> casper.exchanger.ExchangerOffer
	63,  // 89: casper.exchanger.GetExchangerOffersByFiltersResponse.offer_id_to_listing_map:type_name -> casper.exchanger.GetExchangerOffersByFiltersResponse.OfferIdToListingMapEntry
	87,  // 90: casper.exchanger.GetExchangerOfferGroupsByIdsResponse.GroupIdToGroupMapEntry.value:type_name -> casper.exchanger.ExchangerOfferGroup
	88,  // 91: casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse.GroupIdToUtilisationMapEntry.value:type_name -> casper.exchanger.ExchangerOfferGroupRewardUnitsActorUtilisation
	89,  // 92: casper.exchanger.GetExchangerOffersOrdersSummaryRequest.Filters.states:type_name -> casper.exchanger.ExchangerOfferOrderState
	67,  // 93: casper.exchanger.GetExchangerOffersOrdersSummaryRequest.Filters.reward_types:type_name -> casper.exchanger.RewardType
	81,  // 94: casper.exchanger.GetExchangerOffersRequest.FiltersV2.catalog_filters:type_name -> casper.CatalogFilters
	80,  // 95: casper.exchanger.GetExchangerOffersRequest.FiltersV2.status:type_name -> casper.exchanger.ExchangerOfferStatus
	79,  // 96: casper.exchanger.GetExchangerOffersResponse.OfferIdToListingMapEntry.value:type_name -> casper.exchanger.ExchangerOfferListing
	80,  // 97: casper.exchanger.GetExchangerOffersByIdsRequest.Filters.status:type_name -> casper.exchanger.ExchangerOfferStatus
	89,  // 98: casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters.states:type_name -> casper.exchanger.ExchangerOfferOrderState
	84,  // 99: casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters.from_date:type_name -> google.protobuf.Timestamp
	84,  // 100: casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters.upto_date:type_name -> google.protobuf.Timestamp
	67,  // 101: casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters.reward_types:type_name -> casper.exchanger.RewardType
	90,  // 102: casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters.reward_expiry_status:type_name -> casper.exchanger.RewardExpiryStatus
	59,  // 103: casper.exchanger.GetExchangerOfferOrdersForActorRequest.Filters.within_or_filters:type_name -> casper.exchanger.GetExchangerOfferOrdersForActorRequest.OrFilters
	67,  // 104: casper.exchanger.GetExchangerOfferOrdersForActorRequest.OrFilters.reward_types:type_name -> casper.exchanger.RewardType
	75,  // 105: casper.exchanger.GetExchangerOfferOrdersForActorRequest.OrFilters.or_offer_tags:type_name -> casper.TagName
	89,  // 106: casper.exchanger.GetExchangerOfferOrdersRequest.Filters.states:type_name -> casper.exchanger.ExchangerOfferOrderState
	84,  // 107: casper.exchanger.GetExchangerOfferOrdersRequest.Filters.from_date:type_name -> google.protobuf.Timestamp
	84,  // 108: casper.exchanger.GetExchangerOfferOrdersRequest.Filters.upto_date:type_name -> google.protobuf.Timestamp
	67,  // 109: casper.exchanger.GetExchangerOfferOrdersRequest.Filters.reward_types:type_name -> casper.exchanger.RewardType
	79,  // 110: casper.exchanger.GetExchangerOffersByFiltersResponse.OfferIdToListingMapEntry.value:type_name -> casper.exchanger.ExchangerOfferListing
	11,  // 111: casper.exchanger.ExchangerOfferService.CreateExchangerOffer:input_type -> casper.exchanger.CreateExchangerOfferRequest
	13,  // 112: casper.exchanger.ExchangerOfferService.CreateExchangerOfferListing:input_type -> casper.exchanger.CreateExchangerOfferListingRequest
	3,   // 113: casper.exchanger.ExchangerOfferService.CreateExchangerOfferInventory:input_type -> casper.exchanger.CreateExchangerOfferInventoryRequest
	15,  // 114: casper.exchanger.ExchangerOfferService.UpdateExchangerOfferDisplay:input_type -> casper.exchanger.UpdateExchangerOfferDisplayRequest
	17,  // 115: casper.exchanger.ExchangerOfferService.UpdateExchangerOfferStatus:input_type -> casper.exchanger.UpdateExchangerOfferStatusRequest
	19,  // 116: casper.exchanger.ExchangerOfferService.UpdateExchangerOfferListing:input_type -> casper.exchanger.UpdateExchangerOfferListingRequest
	21,  // 117: casper.exchanger.ExchangerOfferService.DeleteExchangerOfferListing:input_type -> casper.exchanger.DeleteExchangerOfferListingRequest
	23,  // 118: casper.exchanger.ExchangerOfferService.GetExchangerOffers:input_type -> casper.exchanger.GetExchangerOffersRequest
	25,  // 119: casper.exchanger.ExchangerOfferService.GetExchangerOffersByIds:input_type -> casper.exchanger.GetExchangerOffersByIdsRequest
	27,  // 120: casper.exchanger.ExchangerOfferService.RedeemExchangerOffer:input_type -> casper.exchanger.RedeemExchangerOfferRequest
	29,  // 121: casper.exchanger.ExchangerOfferService.GetExchangerOfferOrdersForActor:input_type -> casper.exchanger.GetExchangerOfferOrdersForActorRequest
	31,  // 122: casper.exchanger.ExchangerOfferService.GetExchangerOfferOrders:input_type -> casper.exchanger.GetExchangerOfferOrdersRequest
	33,  // 123: casper.exchanger.ExchangerOfferService.GetExchangerOrderById:input_type -> casper.exchanger.GetExchangerOrderByIdRequest
	35,  // 124: casper.exchanger.ExchangerOfferService.GetExchangerOfferActorAttempts:input_type -> casper.exchanger.GetExchangerOfferActorAttemptsRequest
	37,  // 125: casper.exchanger.ExchangerOfferService.GetExchangerOffersActorAttemptsCount:input_type -> casper.exchanger.GetExchangerOffersActorAttemptsCountRequest
	39,  // 126: casper.exchanger.ExchangerOfferService.ChooseExchangerOrderOption:input_type -> casper.exchanger.ChooseExchangerOrderOptionRequest
	1,   // 127: casper.exchanger.ExchangerOfferService.SubmitUserInputForChosenOption:input_type -> casper.exchanger.SubmitUserInputForChosenOptionRequest
	9,   // 128: casper.exchanger.ExchangerOfferService.GetExchangerOffersOrdersSummary:input_type -> casper.exchanger.GetExchangerOffersOrdersSummaryRequest
	41,  // 129: casper.exchanger.ExchangerOfferService.CreateExchangerOfferGroup:input_type -> casper.exchanger.CreateExchangerOfferGroupRequest
	5,   // 130: casper.exchanger.ExchangerOfferService.GetExchangerOfferGroupsByIds:input_type -> casper.exchanger.GetExchangerOfferGroupsByIdsRequest
	7,   // 131: casper.exchanger.ExchangerOfferService.GetEOGroupsRewardUnitsActorUtilisation:input_type -> casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationRequest
	43,  // 132: casper.exchanger.ExchangerOfferService.IncrementExchangerOfferInventory:input_type -> casper.exchanger.IncrementExchangerOfferInventoryRequest
	45,  // 133: casper.exchanger.ExchangerOfferService.DecryptExchangerOfferOrdersDetails:input_type -> casper.exchanger.DecryptExchangerOfferOrdersDetailsRequest
	47,  // 134: casper.exchanger.ExchangerOfferService.GetRedemptionCountsForActorOfferIdsInMonth:input_type -> casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthRequest
	49,  // 135: casper.exchanger.ExchangerOfferService.GetExchangerOffersByFilters:input_type -> casper.exchanger.GetExchangerOffersByFiltersRequest
	12,  // 136: casper.exchanger.ExchangerOfferService.CreateExchangerOffer:output_type -> casper.exchanger.CreateExchangerOfferResponse
	14,  // 137: casper.exchanger.ExchangerOfferService.CreateExchangerOfferListing:output_type -> casper.exchanger.CreateExchangerOfferListingResponse
	4,   // 138: casper.exchanger.ExchangerOfferService.CreateExchangerOfferInventory:output_type -> casper.exchanger.CreateExchangerOfferInventoryResponse
	16,  // 139: casper.exchanger.ExchangerOfferService.UpdateExchangerOfferDisplay:output_type -> casper.exchanger.UpdateExchangerOfferDisplayResponse
	18,  // 140: casper.exchanger.ExchangerOfferService.UpdateExchangerOfferStatus:output_type -> casper.exchanger.UpdateExchangerOfferStatusResponse
	20,  // 141: casper.exchanger.ExchangerOfferService.UpdateExchangerOfferListing:output_type -> casper.exchanger.UpdateExchangerOfferListingResponse
	22,  // 142: casper.exchanger.ExchangerOfferService.DeleteExchangerOfferListing:output_type -> casper.exchanger.DeleteExchangerOfferListingResponse
	24,  // 143: casper.exchanger.ExchangerOfferService.GetExchangerOffers:output_type -> casper.exchanger.GetExchangerOffersResponse
	26,  // 144: casper.exchanger.ExchangerOfferService.GetExchangerOffersByIds:output_type -> casper.exchanger.GetExchangerOffersByIdsResponse
	28,  // 145: casper.exchanger.ExchangerOfferService.RedeemExchangerOffer:output_type -> casper.exchanger.RedeemExchangerOfferResponse
	30,  // 146: casper.exchanger.ExchangerOfferService.GetExchangerOfferOrdersForActor:output_type -> casper.exchanger.GetExchangerOfferOrdersForActorResponse
	32,  // 147: casper.exchanger.ExchangerOfferService.GetExchangerOfferOrders:output_type -> casper.exchanger.GetExchangerOfferOrdersResponse
	34,  // 148: casper.exchanger.ExchangerOfferService.GetExchangerOrderById:output_type -> casper.exchanger.GetExchangerOrderByIdResponse
	36,  // 149: casper.exchanger.ExchangerOfferService.GetExchangerOfferActorAttempts:output_type -> casper.exchanger.GetExchangerOfferActorAttemptsResponse
	38,  // 150: casper.exchanger.ExchangerOfferService.GetExchangerOffersActorAttemptsCount:output_type -> casper.exchanger.GetExchangerOffersActorAttemptsCountResponse
	40,  // 151: casper.exchanger.ExchangerOfferService.ChooseExchangerOrderOption:output_type -> casper.exchanger.ChooseExchangerOrderOptionResponse
	2,   // 152: casper.exchanger.ExchangerOfferService.SubmitUserInputForChosenOption:output_type -> casper.exchanger.SubmitUserInputForChosenOptionResponse
	10,  // 153: casper.exchanger.ExchangerOfferService.GetExchangerOffersOrdersSummary:output_type -> casper.exchanger.GetExchangerOffersOrdersSummaryResponse
	42,  // 154: casper.exchanger.ExchangerOfferService.CreateExchangerOfferGroup:output_type -> casper.exchanger.CreateExchangerOfferGroupResponse
	6,   // 155: casper.exchanger.ExchangerOfferService.GetExchangerOfferGroupsByIds:output_type -> casper.exchanger.GetExchangerOfferGroupsByIdsResponse
	8,   // 156: casper.exchanger.ExchangerOfferService.GetEOGroupsRewardUnitsActorUtilisation:output_type -> casper.exchanger.GetEOGroupsRewardUnitsActorUtilisationResponse
	44,  // 157: casper.exchanger.ExchangerOfferService.IncrementExchangerOfferInventory:output_type -> casper.exchanger.IncrementExchangerOfferInventoryResponse
	46,  // 158: casper.exchanger.ExchangerOfferService.DecryptExchangerOfferOrdersDetails:output_type -> casper.exchanger.DecryptExchangerOfferOrdersDetailsResponse
	48,  // 159: casper.exchanger.ExchangerOfferService.GetRedemptionCountsForActorOfferIdsInMonth:output_type -> casper.exchanger.GetRedemptionCountsForActorOfferIdsInMonthResponse
	51,  // 160: casper.exchanger.ExchangerOfferService.GetExchangerOffersByFilters:output_type -> casper.exchanger.GetExchangerOffersByFiltersResponse
	136, // [136:161] is the sub-list for method output_type
	111, // [111:136] is the sub-list for method input_type
	111, // [111:111] is the sub-list for extension type_name
	111, // [111:111] is the sub-list for extension extendee
	0,   // [0:111] is the sub-list for field type_name
}

func init() { file_api_casper_exchanger_service_proto_init() }
func file_api_casper_exchanger_service_proto_init() {
	if File_api_casper_exchanger_service_proto != nil {
		return
	}
	file_api_casper_exchanger_exchanger_offer_proto_init()
	file_api_casper_exchanger_exchanger_offer_actor_attempt_proto_init()
	file_api_casper_exchanger_exchanger_offer_group_proto_init()
	file_api_casper_exchanger_exchanger_offer_inventory_proto_init()
	file_api_casper_exchanger_exchanger_offer_listing_proto_init()
	file_api_casper_exchanger_exchanger_offer_order_proto_init()
	file_api_casper_exchanger_exchanger_offer_reward_units_actor_utilisations_proto_init()
	file_api_casper_exchanger_reward_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_casper_exchanger_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitUserInputForChosenOptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitUserInputForChosenOptionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferInventoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferInventoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferGroupsByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferGroupsByIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEOGroupsRewardUnitsActorUtilisationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEOGroupsRewardUnitsActorUtilisationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersOrdersSummaryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersOrdersSummaryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferListingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferListingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExchangerOfferDisplayRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExchangerOfferDisplayResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExchangerOfferStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExchangerOfferStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExchangerOfferListingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExchangerOfferListingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExchangerOfferListingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExchangerOfferListingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersByIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedeemExchangerOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedeemExchangerOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOrderByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOrderByIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferActorAttemptsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferActorAttemptsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersActorAttemptsCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersActorAttemptsCountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChooseExchangerOrderOptionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChooseExchangerOrderOptionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExchangerOfferGroupResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncrementExchangerOfferInventoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncrementExchangerOfferInventoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DecryptExchangerOfferOrdersDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DecryptExchangerOfferOrdersDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRedemptionCountsForActorOfferIdsInMonthRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRedemptionCountsForActorOfferIdsInMonthResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersByFiltersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExchangerOfferFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersByFiltersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersOrdersSummaryRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersRequest_FiltersV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOffersByIdsRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersForActorRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersForActorRequest_OrFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_exchanger_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExchangerOfferOrdersRequest_Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_casper_exchanger_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   63,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_casper_exchanger_service_proto_goTypes,
		DependencyIndexes: file_api_casper_exchanger_service_proto_depIdxs,
		EnumInfos:         file_api_casper_exchanger_service_proto_enumTypes,
		MessageInfos:      file_api_casper_exchanger_service_proto_msgTypes,
	}.Build()
	File_api_casper_exchanger_service_proto = out.File
	file_api_casper_exchanger_service_proto_rawDesc = nil
	file_api_casper_exchanger_service_proto_goTypes = nil
	file_api_casper_exchanger_service_proto_depIdxs = nil
}
