// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/acquisition/crossattach/service.proto

package crossattach

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CrossAttach_GetCrossSellInfo_FullMethodName = "/acquisition.crossattach.CrossAttach/GetCrossSellInfo"
)

// CrossAttachClient is the client API for CrossAttach service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CrossAttachClient interface {
	GetCrossSellInfo(ctx context.Context, in *GetCrossSellInfoRequest, opts ...grpc.CallOption) (*GetCrossSellInfoResponse, error)
}

type crossAttachClient struct {
	cc grpc.ClientConnInterface
}

func NewCrossAttachClient(cc grpc.ClientConnInterface) CrossAttachClient {
	return &crossAttachClient{cc}
}

func (c *crossAttachClient) GetCrossSellInfo(ctx context.Context, in *GetCrossSellInfoRequest, opts ...grpc.CallOption) (*GetCrossSellInfoResponse, error) {
	out := new(GetCrossSellInfoResponse)
	err := c.cc.Invoke(ctx, CrossAttach_GetCrossSellInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CrossAttachServer is the server API for CrossAttach service.
// All implementations should embed UnimplementedCrossAttachServer
// for forward compatibility
type CrossAttachServer interface {
	GetCrossSellInfo(context.Context, *GetCrossSellInfoRequest) (*GetCrossSellInfoResponse, error)
}

// UnimplementedCrossAttachServer should be embedded to have forward compatible implementations.
type UnimplementedCrossAttachServer struct {
}

func (UnimplementedCrossAttachServer) GetCrossSellInfo(context.Context, *GetCrossSellInfoRequest) (*GetCrossSellInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCrossSellInfo not implemented")
}

// UnsafeCrossAttachServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CrossAttachServer will
// result in compilation errors.
type UnsafeCrossAttachServer interface {
	mustEmbedUnimplementedCrossAttachServer()
}

func RegisterCrossAttachServer(s grpc.ServiceRegistrar, srv CrossAttachServer) {
	s.RegisterService(&CrossAttach_ServiceDesc, srv)
}

func _CrossAttach_GetCrossSellInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCrossSellInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrossAttachServer).GetCrossSellInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrossAttach_GetCrossSellInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrossAttachServer).GetCrossSellInfo(ctx, req.(*GetCrossSellInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CrossAttach_ServiceDesc is the grpc.ServiceDesc for CrossAttach service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CrossAttach_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "acquisition.crossattach.CrossAttach",
	HandlerType: (*CrossAttachServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCrossSellInfo",
			Handler:    _CrossAttach_GetCrossSellInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/acquisition/crossattach/service.proto",
}
