// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/p2pinvestment/enums.pb.go

package p2pinvestment

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the InvestorStatus in string format in DB
func (p InvestorStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing InvestorStatus while reading from DB
func (p *InvestorStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := InvestorStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected InvestorStatus value: %s", val)
	}
	*p = InvestorStatus(valInt)
	return nil
}

// Marshaler interface implementation for InvestorStatus
func (x InvestorStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for InvestorStatus
func (x *InvestorStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = InvestorStatus(InvestorStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the InvestorSubStatus in string format in DB
func (p InvestorSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing InvestorSubStatus while reading from DB
func (p *InvestorSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := InvestorSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected InvestorSubStatus value: %s", val)
	}
	*p = InvestorSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for InvestorSubStatus
func (x InvestorSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for InvestorSubStatus
func (x *InvestorSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = InvestorSubStatus(InvestorSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the Vendor in string format in DB
func (p Vendor) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Vendor while reading from DB
func (p *Vendor) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Vendor_value[val]
	if !ok {
		return fmt.Errorf("unexpected Vendor value: %s", val)
	}
	*p = Vendor(valInt)
	return nil
}

// Marshaler interface implementation for Vendor
func (x Vendor) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Vendor
func (x *Vendor) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Vendor(Vendor_value[val])
	return nil
}

// Valuer interface implementation for storing the InvestmentTransactionType in string format in DB
func (p InvestmentTransactionType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing InvestmentTransactionType while reading from DB
func (p *InvestmentTransactionType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := InvestmentTransactionType_value[val]
	if !ok {
		return fmt.Errorf("unexpected InvestmentTransactionType value: %s", val)
	}
	*p = InvestmentTransactionType(valInt)
	return nil
}

// Marshaler interface implementation for InvestmentTransactionType
func (x InvestmentTransactionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for InvestmentTransactionType
func (x *InvestmentTransactionType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = InvestmentTransactionType(InvestmentTransactionType_value[val])
	return nil
}

// Valuer interface implementation for storing the InvestmentTransactionStatus in string format in DB
func (p InvestmentTransactionStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing InvestmentTransactionStatus while reading from DB
func (p *InvestmentTransactionStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := InvestmentTransactionStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected InvestmentTransactionStatus value: %s", val)
	}
	*p = InvestmentTransactionStatus(valInt)
	return nil
}

// Marshaler interface implementation for InvestmentTransactionStatus
func (x InvestmentTransactionStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for InvestmentTransactionStatus
func (x *InvestmentTransactionStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = InvestmentTransactionStatus(InvestmentTransactionStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the InvestmentTransactionSubStatus in string format in DB
func (p InvestmentTransactionSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing InvestmentTransactionSubStatus while reading from DB
func (p *InvestmentTransactionSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := InvestmentTransactionSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected InvestmentTransactionSubStatus value: %s", val)
	}
	*p = InvestmentTransactionSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for InvestmentTransactionSubStatus
func (x InvestmentTransactionSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for InvestmentTransactionSubStatus
func (x *InvestmentTransactionSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = InvestmentTransactionSubStatus(InvestmentTransactionSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the ActivityType in string format in DB
func (p ActivityType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ActivityType while reading from DB
func (p *ActivityType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ActivityType_value[val]
	if !ok {
		return fmt.Errorf("unexpected ActivityType value: %s", val)
	}
	*p = ActivityType(valInt)
	return nil
}

// Marshaler interface implementation for ActivityType
func (x ActivityType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ActivityType
func (x *ActivityType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ActivityType(ActivityType_value[val])
	return nil
}

// Valuer interface implementation for storing the ActivityStatus in string format in DB
func (p ActivityStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ActivityStatus while reading from DB
func (p *ActivityStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ActivityStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ActivityStatus value: %s", val)
	}
	*p = ActivityStatus(valInt)
	return nil
}

// Marshaler interface implementation for ActivityStatus
func (x ActivityStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ActivityStatus
func (x *ActivityStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ActivityStatus(ActivityStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the ActivityStageStatus in string format in DB
func (p ActivityStageStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ActivityStageStatus while reading from DB
func (p *ActivityStageStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ActivityStageStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected ActivityStageStatus value: %s", val)
	}
	*p = ActivityStageStatus(valInt)
	return nil
}

// Marshaler interface implementation for ActivityStageStatus
func (x ActivityStageStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ActivityStageStatus
func (x *ActivityStageStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ActivityStageStatus(ActivityStageStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the ActivityStage in string format in DB
func (p ActivityStage) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing ActivityStage while reading from DB
func (p *ActivityStage) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := ActivityStage_value[val]
	if !ok {
		return fmt.Errorf("unexpected ActivityStage value: %s", val)
	}
	*p = ActivityStage(valInt)
	return nil
}

// Marshaler interface implementation for ActivityStage
func (x ActivityStage) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for ActivityStage
func (x *ActivityStage) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = ActivityStage(ActivityStage_value[val])
	return nil
}

// Valuer interface implementation for storing the IneligibilityReason in string format in DB
func (p IneligibilityReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing IneligibilityReason while reading from DB
func (p *IneligibilityReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := IneligibilityReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected IneligibilityReason value: %s", val)
	}
	*p = IneligibilityReason(valInt)
	return nil
}

// Marshaler interface implementation for IneligibilityReason
func (x IneligibilityReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for IneligibilityReason
func (x *IneligibilityReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = IneligibilityReason(IneligibilityReason_value[val])
	return nil
}

// Valuer interface implementation for storing the SchemeIneligibilityReason in string format in DB
func (p SchemeIneligibilityReason) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing SchemeIneligibilityReason while reading from DB
func (p *SchemeIneligibilityReason) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SchemeIneligibilityReason_value[val]
	if !ok {
		return fmt.Errorf("unexpected SchemeIneligibilityReason value: %s", val)
	}
	*p = SchemeIneligibilityReason(valInt)
	return nil
}

// Marshaler interface implementation for SchemeIneligibilityReason
func (x SchemeIneligibilityReason) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for SchemeIneligibilityReason
func (x *SchemeIneligibilityReason) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = SchemeIneligibilityReason(SchemeIneligibilityReason_value[val])
	return nil
}

// Valuer interface implementation for storing the MaturityActionStatus in string format in DB
func (p MaturityActionStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing MaturityActionStatus while reading from DB
func (p *MaturityActionStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := MaturityActionStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected MaturityActionStatus value: %s", val)
	}
	*p = MaturityActionStatus(valInt)
	return nil
}

// Marshaler interface implementation for MaturityActionStatus
func (x MaturityActionStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for MaturityActionStatus
func (x *MaturityActionStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = MaturityActionStatus(MaturityActionStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the MaturityActionSubStatus in string format in DB
func (p MaturityActionSubStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing MaturityActionSubStatus while reading from DB
func (p *MaturityActionSubStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := MaturityActionSubStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected MaturityActionSubStatus value: %s", val)
	}
	*p = MaturityActionSubStatus(valInt)
	return nil
}

// Marshaler interface implementation for MaturityActionSubStatus
func (x MaturityActionSubStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for MaturityActionSubStatus
func (x *MaturityActionSubStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = MaturityActionSubStatus(MaturityActionSubStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the MaturityActionType in string format in DB
func (p MaturityActionType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing MaturityActionType while reading from DB
func (p *MaturityActionType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := MaturityActionType_value[val]
	if !ok {
		return fmt.Errorf("unexpected MaturityActionType value: %s", val)
	}
	*p = MaturityActionType(valInt)
	return nil
}

// Marshaler interface implementation for MaturityActionType
func (x MaturityActionType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for MaturityActionType
func (x *MaturityActionType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = MaturityActionType(MaturityActionType_value[val])
	return nil
}

// Valuer interface implementation for storing the SchemeStatus in string format in DB
func (p SchemeStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing SchemeStatus while reading from DB
func (p *SchemeStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := SchemeStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected SchemeStatus value: %s", val)
	}
	*p = SchemeStatus(valInt)
	return nil
}

// Marshaler interface implementation for SchemeStatus
func (x SchemeStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for SchemeStatus
func (x *SchemeStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = SchemeStatus(SchemeStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the DocumentUploadStatus in string format in DB
func (p DocumentUploadStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing DocumentUploadStatus while reading from DB
func (p *DocumentUploadStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := DocumentUploadStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected DocumentUploadStatus value: %s", val)
	}
	*p = DocumentUploadStatus(valInt)
	return nil
}

// Marshaler interface implementation for DocumentUploadStatus
func (x DocumentUploadStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for DocumentUploadStatus
func (x *DocumentUploadStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = DocumentUploadStatus(DocumentUploadStatus_value[val])
	return nil
}

// Valuer interface implementation for storing the MaturityConsentType in string format in DB
func (p MaturityConsentType) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing MaturityConsentType while reading from DB
func (p *MaturityConsentType) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := MaturityConsentType_value[val]
	if !ok {
		return fmt.Errorf("unexpected MaturityConsentType value: %s", val)
	}
	*p = MaturityConsentType(valInt)
	return nil
}

// Marshaler interface implementation for MaturityConsentType
func (x MaturityConsentType) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for MaturityConsentType
func (x *MaturityConsentType) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = MaturityConsentType(MaturityConsentType_value[val])
	return nil
}
