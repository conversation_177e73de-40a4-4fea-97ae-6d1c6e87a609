// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/bankcust/bankcust_consumer.proto

package bankcust

import (
	context "context"
	notification "github.com/epifi/gamma/api/auth/notification"
	kyc "github.com/epifi/gamma/api/kyc"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	FederalBankCustomerConsumer_ProcessCustomerCreation_FullMethodName                = "/bankcust.FederalBankCustomerConsumer/ProcessCustomerCreation"
	FederalBankCustomerConsumer_ProcessCustomerCreationCallback_FullMethodName        = "/bankcust.FederalBankCustomerConsumer/ProcessCustomerCreationCallback"
	FederalBankCustomerConsumer_ProcessBKYCUpdateEvent_FullMethodName                 = "/bankcust.FederalBankCustomerConsumer/ProcessBKYCUpdateEvent"
	FederalBankCustomerConsumer_ProcessKYCStateChangeEvent_FullMethodName             = "/bankcust.FederalBankCustomerConsumer/ProcessKYCStateChangeEvent"
	FederalBankCustomerConsumer_ProcessResidentialStatusUpdateCallback_FullMethodName = "/bankcust.FederalBankCustomerConsumer/ProcessResidentialStatusUpdateCallback"
	FederalBankCustomerConsumer_ProcessMobileNumberUpdateCallback_FullMethodName      = "/bankcust.FederalBankCustomerConsumer/ProcessMobileNumberUpdateCallback"
	FederalBankCustomerConsumer_ProcessAuthFactorUpdateEvent_FullMethodName           = "/bankcust.FederalBankCustomerConsumer/ProcessAuthFactorUpdateEvent"
)

// FederalBankCustomerConsumerClient is the client API for FederalBankCustomerConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FederalBankCustomerConsumerClient interface {
	// ProcessCustomerCreation is a consumer rpc which processes customer creation
	// this rpc calls vendor to initiate customer creation and polls for the customer creation status async
	ProcessCustomerCreation(ctx context.Context, in *ProcessCustomerCreationRequest, opts ...grpc.CallOption) (*ProcessCustomerCreationResponse, error)
	// ProcessCustomerCreationCallback is a consumer rpc which consumes packet published at vendornotification
	// this rpc processes the callback posted by vendor
	ProcessCustomerCreationCallback(ctx context.Context, in *ProcessCustomerCreationCallbackRequest, opts ...grpc.CallOption) (*ProcessCustomerCreationCallbackResponse, error)
	// ProcessBKYCUpdateEvent is a consumer rpc which consumes the bkyc update event and triggers kyc upgrade for the customer
	// It then proceeds to poll vendor api until kyc level of the customer has been successfully updated at their end
	ProcessBKYCUpdateEvent(ctx context.Context, in *kyc.BKYCUpdateEvent, opts ...grpc.CallOption) (*ProcessBKYCUpdateResponse, error)
	// ProcessKYCStateChangeEvent is a consumer RPC responsible for consuming kyc state change event
	// It then proceeds to update the kyc status of the user
	ProcessKYCStateChangeEvent(ctx context.Context, in *ProcessKYCStateChangeEventRequest, opts ...grpc.CallOption) (*ProcessKYCStateChangeEventResponse, error)
	// ProcessResidentialStatusUpdateCallback is a consumer RPC responsible for consuming residential status update event packet
	// residential status update happens when user migrates to another country and account changes to NRE/NRO type
	// It then update the bank customer status to inactive/active
	ProcessResidentialStatusUpdateCallback(ctx context.Context, in *ProcessResidentialStatusUpdateCallbackRequest, opts ...grpc.CallOption) (*ProcessResidentialStatusUpdateCallbackResponse, error)
	// bank temporarily deactivates the device and sends us a notification when user updates the number outside FI
	// It updates the bank customer status to inactive
	ProcessMobileNumberUpdateCallback(ctx context.Context, in *ProcessMobileNumberUpdateCallbackRequest, opts ...grpc.CallOption) (*ProcessMobileNumberUpdateCallbackResponse, error)
	// ProcessAuthFactorUpdateEvent will consume the AFU event and update bank customer status to active
	ProcessAuthFactorUpdateEvent(ctx context.Context, in *notification.AuthFactorUpdateEvent, opts ...grpc.CallOption) (*ProcessAuthFactorUpdateEventResponse, error)
}

type federalBankCustomerConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewFederalBankCustomerConsumerClient(cc grpc.ClientConnInterface) FederalBankCustomerConsumerClient {
	return &federalBankCustomerConsumerClient{cc}
}

func (c *federalBankCustomerConsumerClient) ProcessCustomerCreation(ctx context.Context, in *ProcessCustomerCreationRequest, opts ...grpc.CallOption) (*ProcessCustomerCreationResponse, error) {
	out := new(ProcessCustomerCreationResponse)
	err := c.cc.Invoke(ctx, FederalBankCustomerConsumer_ProcessCustomerCreation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *federalBankCustomerConsumerClient) ProcessCustomerCreationCallback(ctx context.Context, in *ProcessCustomerCreationCallbackRequest, opts ...grpc.CallOption) (*ProcessCustomerCreationCallbackResponse, error) {
	out := new(ProcessCustomerCreationCallbackResponse)
	err := c.cc.Invoke(ctx, FederalBankCustomerConsumer_ProcessCustomerCreationCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *federalBankCustomerConsumerClient) ProcessBKYCUpdateEvent(ctx context.Context, in *kyc.BKYCUpdateEvent, opts ...grpc.CallOption) (*ProcessBKYCUpdateResponse, error) {
	out := new(ProcessBKYCUpdateResponse)
	err := c.cc.Invoke(ctx, FederalBankCustomerConsumer_ProcessBKYCUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *federalBankCustomerConsumerClient) ProcessKYCStateChangeEvent(ctx context.Context, in *ProcessKYCStateChangeEventRequest, opts ...grpc.CallOption) (*ProcessKYCStateChangeEventResponse, error) {
	out := new(ProcessKYCStateChangeEventResponse)
	err := c.cc.Invoke(ctx, FederalBankCustomerConsumer_ProcessKYCStateChangeEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *federalBankCustomerConsumerClient) ProcessResidentialStatusUpdateCallback(ctx context.Context, in *ProcessResidentialStatusUpdateCallbackRequest, opts ...grpc.CallOption) (*ProcessResidentialStatusUpdateCallbackResponse, error) {
	out := new(ProcessResidentialStatusUpdateCallbackResponse)
	err := c.cc.Invoke(ctx, FederalBankCustomerConsumer_ProcessResidentialStatusUpdateCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *federalBankCustomerConsumerClient) ProcessMobileNumberUpdateCallback(ctx context.Context, in *ProcessMobileNumberUpdateCallbackRequest, opts ...grpc.CallOption) (*ProcessMobileNumberUpdateCallbackResponse, error) {
	out := new(ProcessMobileNumberUpdateCallbackResponse)
	err := c.cc.Invoke(ctx, FederalBankCustomerConsumer_ProcessMobileNumberUpdateCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *federalBankCustomerConsumerClient) ProcessAuthFactorUpdateEvent(ctx context.Context, in *notification.AuthFactorUpdateEvent, opts ...grpc.CallOption) (*ProcessAuthFactorUpdateEventResponse, error) {
	out := new(ProcessAuthFactorUpdateEventResponse)
	err := c.cc.Invoke(ctx, FederalBankCustomerConsumer_ProcessAuthFactorUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FederalBankCustomerConsumerServer is the server API for FederalBankCustomerConsumer service.
// All implementations should embed UnimplementedFederalBankCustomerConsumerServer
// for forward compatibility
type FederalBankCustomerConsumerServer interface {
	// ProcessCustomerCreation is a consumer rpc which processes customer creation
	// this rpc calls vendor to initiate customer creation and polls for the customer creation status async
	ProcessCustomerCreation(context.Context, *ProcessCustomerCreationRequest) (*ProcessCustomerCreationResponse, error)
	// ProcessCustomerCreationCallback is a consumer rpc which consumes packet published at vendornotification
	// this rpc processes the callback posted by vendor
	ProcessCustomerCreationCallback(context.Context, *ProcessCustomerCreationCallbackRequest) (*ProcessCustomerCreationCallbackResponse, error)
	// ProcessBKYCUpdateEvent is a consumer rpc which consumes the bkyc update event and triggers kyc upgrade for the customer
	// It then proceeds to poll vendor api until kyc level of the customer has been successfully updated at their end
	ProcessBKYCUpdateEvent(context.Context, *kyc.BKYCUpdateEvent) (*ProcessBKYCUpdateResponse, error)
	// ProcessKYCStateChangeEvent is a consumer RPC responsible for consuming kyc state change event
	// It then proceeds to update the kyc status of the user
	ProcessKYCStateChangeEvent(context.Context, *ProcessKYCStateChangeEventRequest) (*ProcessKYCStateChangeEventResponse, error)
	// ProcessResidentialStatusUpdateCallback is a consumer RPC responsible for consuming residential status update event packet
	// residential status update happens when user migrates to another country and account changes to NRE/NRO type
	// It then update the bank customer status to inactive/active
	ProcessResidentialStatusUpdateCallback(context.Context, *ProcessResidentialStatusUpdateCallbackRequest) (*ProcessResidentialStatusUpdateCallbackResponse, error)
	// bank temporarily deactivates the device and sends us a notification when user updates the number outside FI
	// It updates the bank customer status to inactive
	ProcessMobileNumberUpdateCallback(context.Context, *ProcessMobileNumberUpdateCallbackRequest) (*ProcessMobileNumberUpdateCallbackResponse, error)
	// ProcessAuthFactorUpdateEvent will consume the AFU event and update bank customer status to active
	ProcessAuthFactorUpdateEvent(context.Context, *notification.AuthFactorUpdateEvent) (*ProcessAuthFactorUpdateEventResponse, error)
}

// UnimplementedFederalBankCustomerConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedFederalBankCustomerConsumerServer struct {
}

func (UnimplementedFederalBankCustomerConsumerServer) ProcessCustomerCreation(context.Context, *ProcessCustomerCreationRequest) (*ProcessCustomerCreationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCustomerCreation not implemented")
}
func (UnimplementedFederalBankCustomerConsumerServer) ProcessCustomerCreationCallback(context.Context, *ProcessCustomerCreationCallbackRequest) (*ProcessCustomerCreationCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCustomerCreationCallback not implemented")
}
func (UnimplementedFederalBankCustomerConsumerServer) ProcessBKYCUpdateEvent(context.Context, *kyc.BKYCUpdateEvent) (*ProcessBKYCUpdateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessBKYCUpdateEvent not implemented")
}
func (UnimplementedFederalBankCustomerConsumerServer) ProcessKYCStateChangeEvent(context.Context, *ProcessKYCStateChangeEventRequest) (*ProcessKYCStateChangeEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessKYCStateChangeEvent not implemented")
}
func (UnimplementedFederalBankCustomerConsumerServer) ProcessResidentialStatusUpdateCallback(context.Context, *ProcessResidentialStatusUpdateCallbackRequest) (*ProcessResidentialStatusUpdateCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessResidentialStatusUpdateCallback not implemented")
}
func (UnimplementedFederalBankCustomerConsumerServer) ProcessMobileNumberUpdateCallback(context.Context, *ProcessMobileNumberUpdateCallbackRequest) (*ProcessMobileNumberUpdateCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessMobileNumberUpdateCallback not implemented")
}
func (UnimplementedFederalBankCustomerConsumerServer) ProcessAuthFactorUpdateEvent(context.Context, *notification.AuthFactorUpdateEvent) (*ProcessAuthFactorUpdateEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessAuthFactorUpdateEvent not implemented")
}

// UnsafeFederalBankCustomerConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FederalBankCustomerConsumerServer will
// result in compilation errors.
type UnsafeFederalBankCustomerConsumerServer interface {
	mustEmbedUnimplementedFederalBankCustomerConsumerServer()
}

func RegisterFederalBankCustomerConsumerServer(s grpc.ServiceRegistrar, srv FederalBankCustomerConsumerServer) {
	s.RegisterService(&FederalBankCustomerConsumer_ServiceDesc, srv)
}

func _FederalBankCustomerConsumer_ProcessCustomerCreation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCustomerCreationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FederalBankCustomerConsumerServer).ProcessCustomerCreation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FederalBankCustomerConsumer_ProcessCustomerCreation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FederalBankCustomerConsumerServer).ProcessCustomerCreation(ctx, req.(*ProcessCustomerCreationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FederalBankCustomerConsumer_ProcessCustomerCreationCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCustomerCreationCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FederalBankCustomerConsumerServer).ProcessCustomerCreationCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FederalBankCustomerConsumer_ProcessCustomerCreationCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FederalBankCustomerConsumerServer).ProcessCustomerCreationCallback(ctx, req.(*ProcessCustomerCreationCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FederalBankCustomerConsumer_ProcessBKYCUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(kyc.BKYCUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FederalBankCustomerConsumerServer).ProcessBKYCUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FederalBankCustomerConsumer_ProcessBKYCUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FederalBankCustomerConsumerServer).ProcessBKYCUpdateEvent(ctx, req.(*kyc.BKYCUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _FederalBankCustomerConsumer_ProcessKYCStateChangeEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessKYCStateChangeEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FederalBankCustomerConsumerServer).ProcessKYCStateChangeEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FederalBankCustomerConsumer_ProcessKYCStateChangeEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FederalBankCustomerConsumerServer).ProcessKYCStateChangeEvent(ctx, req.(*ProcessKYCStateChangeEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FederalBankCustomerConsumer_ProcessResidentialStatusUpdateCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessResidentialStatusUpdateCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FederalBankCustomerConsumerServer).ProcessResidentialStatusUpdateCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FederalBankCustomerConsumer_ProcessResidentialStatusUpdateCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FederalBankCustomerConsumerServer).ProcessResidentialStatusUpdateCallback(ctx, req.(*ProcessResidentialStatusUpdateCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FederalBankCustomerConsumer_ProcessMobileNumberUpdateCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessMobileNumberUpdateCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FederalBankCustomerConsumerServer).ProcessMobileNumberUpdateCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FederalBankCustomerConsumer_ProcessMobileNumberUpdateCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FederalBankCustomerConsumerServer).ProcessMobileNumberUpdateCallback(ctx, req.(*ProcessMobileNumberUpdateCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FederalBankCustomerConsumer_ProcessAuthFactorUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(notification.AuthFactorUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FederalBankCustomerConsumerServer).ProcessAuthFactorUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FederalBankCustomerConsumer_ProcessAuthFactorUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FederalBankCustomerConsumerServer).ProcessAuthFactorUpdateEvent(ctx, req.(*notification.AuthFactorUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// FederalBankCustomerConsumer_ServiceDesc is the grpc.ServiceDesc for FederalBankCustomerConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FederalBankCustomerConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "bankcust.FederalBankCustomerConsumer",
	HandlerType: (*FederalBankCustomerConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessCustomerCreation",
			Handler:    _FederalBankCustomerConsumer_ProcessCustomerCreation_Handler,
		},
		{
			MethodName: "ProcessCustomerCreationCallback",
			Handler:    _FederalBankCustomerConsumer_ProcessCustomerCreationCallback_Handler,
		},
		{
			MethodName: "ProcessBKYCUpdateEvent",
			Handler:    _FederalBankCustomerConsumer_ProcessBKYCUpdateEvent_Handler,
		},
		{
			MethodName: "ProcessKYCStateChangeEvent",
			Handler:    _FederalBankCustomerConsumer_ProcessKYCStateChangeEvent_Handler,
		},
		{
			MethodName: "ProcessResidentialStatusUpdateCallback",
			Handler:    _FederalBankCustomerConsumer_ProcessResidentialStatusUpdateCallback_Handler,
		},
		{
			MethodName: "ProcessMobileNumberUpdateCallback",
			Handler:    _FederalBankCustomerConsumer_ProcessMobileNumberUpdateCallback_Handler,
		},
		{
			MethodName: "ProcessAuthFactorUpdateEvent",
			Handler:    _FederalBankCustomerConsumer_ProcessAuthFactorUpdateEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/bankcust/bankcust_consumer.proto",
}
