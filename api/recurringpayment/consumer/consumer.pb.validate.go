// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/recurringpayment/consumer/consumer.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	recurringpayment "github.com/epifi/gamma/api/recurringpayment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = recurringpayment.RecurringPaymentType(0)
)

// Validate checks the field values on
// RecurringPaymentCreationAuthorizationVendorCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecurringPaymentCreationAuthorizationVendorCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RecurringPaymentCreationAuthorizationVendorCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecurringPaymentCreationAuthorizationVendorCallbackRequestMultiError, or
// nil if none found.
func (m *RecurringPaymentCreationAuthorizationVendorCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentCreationAuthorizationVendorCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetVendorRequestId()) < 1 {
		err := RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
			field:  "VendorRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _RecurringPaymentCreationAuthorizationVendorCallbackRequest_RecurringPaymentType_NotInLookup[m.GetRecurringPaymentType()]; ok {
		err := RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
			field:  "RecurringPaymentType",
			reason: "value must not be in list [RECURRING_PAYMENT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.RecurringPaymentTypeSpecificPayload.(type) {
	case *RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload_:
		if v == nil {
			err := RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
				field:  "RecurringPaymentTypeSpecificPayload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnachPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
						field:  "EnachPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
						field:  "EnachPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnachPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{
					field:  "EnachPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RecurringPaymentCreationAuthorizationVendorCallbackRequestMultiError(errors)
	}

	return nil
}

// RecurringPaymentCreationAuthorizationVendorCallbackRequestMultiError is an
// error wrapping multiple validation errors returned by
// RecurringPaymentCreationAuthorizationVendorCallbackRequest.ValidateAll() if
// the designated constraints aren't met.
type RecurringPaymentCreationAuthorizationVendorCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentCreationAuthorizationVendorCallbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentCreationAuthorizationVendorCallbackRequestMultiError) AllErrors() []error {
	return m
}

// RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError is
// the validation error returned by
// RecurringPaymentCreationAuthorizationVendorCallbackRequest.Validate if the
// designated constraints aren't met.
type RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError) ErrorName() string {
	return "RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentCreationAuthorizationVendorCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentCreationAuthorizationVendorCallbackRequestValidationError{}

var _RecurringPaymentCreationAuthorizationVendorCallbackRequest_RecurringPaymentType_NotInLookup = map[recurringpayment.RecurringPaymentType]struct{}{
	0: {},
}

// Validate checks the field values on ConsumerResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ConsumerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumerResponseMultiError, or nil if none found.
func (m *ConsumerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumerResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumerResponseMultiError(errors)
	}

	return nil
}

// ConsumerResponseMultiError is an error wrapping multiple validation errors
// returned by ConsumerResponse.ValidateAll() if the designated constraints
// aren't met.
type ConsumerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumerResponseMultiError) AllErrors() []error { return m }

// ConsumerResponseValidationError is the validation error returned by
// ConsumerResponse.Validate if the designated constraints aren't met.
type ConsumerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumerResponseValidationError) ErrorName() string { return "ConsumerResponseValidationError" }

// Error satisfies the builtin error interface
func (e ConsumerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumerResponseValidationError{}

// Validate checks the field values on CreateOffAppRecurringPaymentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOffAppRecurringPaymentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOffAppRecurringPaymentRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOffAppRecurringPaymentRequestMultiError, or nil if none found.
func (m *CreateOffAppRecurringPaymentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOffAppRecurringPaymentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOffAppRecurringPaymentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOffAppRecurringPaymentRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOffAppRecurringPaymentRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetRecurringPayment() == nil {
		err := CreateOffAppRecurringPaymentRequestValidationError{
			field:  "RecurringPayment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRecurringPayment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOffAppRecurringPaymentRequestValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOffAppRecurringPaymentRequestValidationError{
					field:  "RecurringPayment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecurringPayment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOffAppRecurringPaymentRequestValidationError{
				field:  "RecurringPayment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.RecurringPaymentTypeSpecificPayload.(type) {
	case *CreateOffAppRecurringPaymentRequest_EnachPayload_:
		if v == nil {
			err := CreateOffAppRecurringPaymentRequestValidationError{
				field:  "RecurringPaymentTypeSpecificPayload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnachPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateOffAppRecurringPaymentRequestValidationError{
						field:  "EnachPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateOffAppRecurringPaymentRequestValidationError{
						field:  "EnachPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnachPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateOffAppRecurringPaymentRequestValidationError{
					field:  "EnachPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CreateOffAppRecurringPaymentRequestMultiError(errors)
	}

	return nil
}

// CreateOffAppRecurringPaymentRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateOffAppRecurringPaymentRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOffAppRecurringPaymentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOffAppRecurringPaymentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOffAppRecurringPaymentRequestMultiError) AllErrors() []error { return m }

// CreateOffAppRecurringPaymentRequestValidationError is the validation error
// returned by CreateOffAppRecurringPaymentRequest.Validate if the designated
// constraints aren't met.
type CreateOffAppRecurringPaymentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOffAppRecurringPaymentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOffAppRecurringPaymentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOffAppRecurringPaymentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOffAppRecurringPaymentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOffAppRecurringPaymentRequestValidationError) ErrorName() string {
	return "CreateOffAppRecurringPaymentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOffAppRecurringPaymentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOffAppRecurringPaymentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOffAppRecurringPaymentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOffAppRecurringPaymentRequestValidationError{}

// Validate checks the field values on
// FetchAndCreateOffAppRecurringPaymentsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FetchAndCreateOffAppRecurringPaymentsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchAndCreateOffAppRecurringPaymentsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchAndCreateOffAppRecurringPaymentsRequestMultiError, or nil if none found.
func (m *FetchAndCreateOffAppRecurringPaymentsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAndCreateOffAppRecurringPaymentsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAndCreateOffAppRecurringPaymentsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAndCreateOffAppRecurringPaymentsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAndCreateOffAppRecurringPaymentsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := FetchAndCreateOffAppRecurringPaymentsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return FetchAndCreateOffAppRecurringPaymentsRequestMultiError(errors)
	}

	return nil
}

// FetchAndCreateOffAppRecurringPaymentsRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchAndCreateOffAppRecurringPaymentsRequest.ValidateAll() if the
// designated constraints aren't met.
type FetchAndCreateOffAppRecurringPaymentsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAndCreateOffAppRecurringPaymentsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAndCreateOffAppRecurringPaymentsRequestMultiError) AllErrors() []error { return m }

// FetchAndCreateOffAppRecurringPaymentsRequestValidationError is the
// validation error returned by
// FetchAndCreateOffAppRecurringPaymentsRequest.Validate if the designated
// constraints aren't met.
type FetchAndCreateOffAppRecurringPaymentsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAndCreateOffAppRecurringPaymentsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAndCreateOffAppRecurringPaymentsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAndCreateOffAppRecurringPaymentsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAndCreateOffAppRecurringPaymentsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAndCreateOffAppRecurringPaymentsRequestValidationError) ErrorName() string {
	return "FetchAndCreateOffAppRecurringPaymentsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAndCreateOffAppRecurringPaymentsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAndCreateOffAppRecurringPaymentsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAndCreateOffAppRecurringPaymentsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAndCreateOffAppRecurringPaymentsRequestValidationError{}

// Validate checks the field values on
// FetchAndCreateFailedEnachTransactionsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FetchAndCreateFailedEnachTransactionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchAndCreateFailedEnachTransactionsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FetchAndCreateFailedEnachTransactionsRequestMultiError, or nil if none found.
func (m *FetchAndCreateFailedEnachTransactionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAndCreateFailedEnachTransactionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAndCreateFailedEnachTransactionsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAndCreateFailedEnachTransactionsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAndCreateFailedEnachTransactionsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := FetchAndCreateFailedEnachTransactionsRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetFromDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAndCreateFailedEnachTransactionsRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAndCreateFailedEnachTransactionsRequestValidationError{
					field:  "FromDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAndCreateFailedEnachTransactionsRequestValidationError{
				field:  "FromDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAndCreateFailedEnachTransactionsRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAndCreateFailedEnachTransactionsRequestValidationError{
					field:  "ToDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAndCreateFailedEnachTransactionsRequestValidationError{
				field:  "ToDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchAndCreateFailedEnachTransactionsRequestMultiError(errors)
	}

	return nil
}

// FetchAndCreateFailedEnachTransactionsRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchAndCreateFailedEnachTransactionsRequest.ValidateAll() if the
// designated constraints aren't met.
type FetchAndCreateFailedEnachTransactionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAndCreateFailedEnachTransactionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAndCreateFailedEnachTransactionsRequestMultiError) AllErrors() []error { return m }

// FetchAndCreateFailedEnachTransactionsRequestValidationError is the
// validation error returned by
// FetchAndCreateFailedEnachTransactionsRequest.Validate if the designated
// constraints aren't met.
type FetchAndCreateFailedEnachTransactionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAndCreateFailedEnachTransactionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAndCreateFailedEnachTransactionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAndCreateFailedEnachTransactionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAndCreateFailedEnachTransactionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAndCreateFailedEnachTransactionsRequestValidationError) ErrorName() string {
	return "FetchAndCreateFailedEnachTransactionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAndCreateFailedEnachTransactionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAndCreateFailedEnachTransactionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAndCreateFailedEnachTransactionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAndCreateFailedEnachTransactionsRequestValidationError{}

// Validate checks the field values on
// RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadMultiError,
// or nil if none found.
func (m *RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Umrn

	// no validation rules for NpciRefId

	// no validation rules for DestBankReferenceNumber

	// no validation rules for MerchantReferenceMessageId

	if len(errors) > 0 {
		return RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadMultiError(errors)
	}

	return nil
}

// RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadMultiError
// is an error wrapping multiple validation errors returned by
// RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload.ValidateAll()
// if the designated constraints aren't met.
type RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadMultiError) AllErrors() []error {
	return m
}

// RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError
// is the validation error returned by
// RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload.Validate
// if the designated constraints aren't met.
type RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError) ErrorName() string {
	return "RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringPaymentCreationAuthorizationVendorCallbackRequest_EnachPayloadValidationError{}

// Validate checks the field values on
// CreateOffAppRecurringPaymentRequest_EnachPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateOffAppRecurringPaymentRequest_EnachPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateOffAppRecurringPaymentRequest_EnachPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CreateOffAppRecurringPaymentRequest_EnachPayloadMultiError, or nil if none found.
func (m *CreateOffAppRecurringPaymentRequest_EnachPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOffAppRecurringPaymentRequest_EnachPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetUmrn()) < 1 {
		err := CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError{
			field:  "Umrn",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateOffAppRecurringPaymentRequest_EnachPayloadMultiError(errors)
	}

	return nil
}

// CreateOffAppRecurringPaymentRequest_EnachPayloadMultiError is an error
// wrapping multiple validation errors returned by
// CreateOffAppRecurringPaymentRequest_EnachPayload.ValidateAll() if the
// designated constraints aren't met.
type CreateOffAppRecurringPaymentRequest_EnachPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOffAppRecurringPaymentRequest_EnachPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOffAppRecurringPaymentRequest_EnachPayloadMultiError) AllErrors() []error { return m }

// CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError is the
// validation error returned by
// CreateOffAppRecurringPaymentRequest_EnachPayload.Validate if the designated
// constraints aren't met.
type CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError) ErrorName() string {
	return "CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOffAppRecurringPaymentRequest_EnachPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOffAppRecurringPaymentRequest_EnachPayloadValidationError{}
