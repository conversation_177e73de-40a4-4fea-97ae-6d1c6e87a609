syntax = "proto3";

package accounts.balance;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/accounts/balance";
option java_package = "com.github.epifi.gamma.api.accounts.balance";

message SavingsAccountBalances {
  string account_id = 1;

  accounts.balance.OpeningBalanceInfo opening_balance_info_from_partner  = 2;

  google.type.Money available_balance_from_partner = 3;

  google.type.Money ledger_balance_from_partner = 4;

  // Timestamp of the moment when balance from partner was last updated
  google.protobuf.Timestamp balance_from_partner_updated_at = 5;

  // Timestamp of the moment account was created
  google.protobuf.Timestamp created_at = 6;

  // Timestamp of the moment account was last updated
  google.protobuf.Timestamp updated_at = 7;

  // Timestamp of the moment account was deleted
  google.protobuf.Timestamp deleted_at = 8;
}

message OpeningBalanceInfo {
  // opening balance is the balance that is brought forward at the beginning of an accounting period from the end
  // of a previous accounting period or when starting out.
  // In an operating account, the ending balance at the end of one month or year becomes the opening balance for the
  // beginning of the next month or accounting year.
  google.type.Money opening_balance = 1;

  // timestamp on which opening balance for the account was last updated at
  google.protobuf.Timestamp updated_at = 2;
}

enum SavingsAccountBalancesFieldMask {
  SAVINGS_ACCOUNT_BALANCES_UNSPECIFIED = 0;
  OPENING_BALANCE_FROM_PARTNER = 1;
  AVAILABLE_BALANCE_FROM_PARTNER = 2;
  LEDGER_BALANCE_FROM_PARTNER = 3;
  BALANCE_FROM_PARTNER_UPDATED_AT = 4;
  ACCOUNT_ID = 5;
  CREATED_AT = 6;
  UPDATE_AT = 7;
}
