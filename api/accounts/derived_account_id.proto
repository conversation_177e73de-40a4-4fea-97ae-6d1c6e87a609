syntax = "proto3";

package accounts;

option go_package = "github.com/epifi/gamma/api/accounts";
option java_package = "com.github.epifi.gamma.api.accounts";

// unique id reference to uniquely identify a account. It stores the combination of internal_account_id,connected_account_id,and tpap_account_id
// It will stores the all the various types of ids such as tpap_account_id,internal_account_id,connected_account_id
message DerivedAccountId {
  // internal_account_id denotes the fi account id
  string internal_account_id = 1;
  // connected_account_id denotes the connected account id
  string connected_account_id = 2;
  // tpap_account_id denotes the connected account id
  string tpap_account_id = 3;
  // deposit_account_id denotes the deposit account id
  string deposit_account_id = 4;
  // credit_card_account_id denotes the credit card account id
  string credit_card_account_id = 5;
}
