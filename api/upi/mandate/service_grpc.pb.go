// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/upi/mandate/service.proto

package mandate

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MandateService_CreateMandate_FullMethodName               = "/upi.mandate.MandateService/CreateMandate"
	MandateService_GetMandate_FullMethodName                  = "/upi.mandate.MandateService/GetMandate"
	MandateService_AuthoriseMandateAction_FullMethodName      = "/upi.mandate.MandateService/AuthoriseMandateAction"
	MandateService_FetchAndUpdateRequestStatus_FullMethodName = "/upi.mandate.MandateService/FetchAndUpdateRequestStatus"
	MandateService_DeclineMandateAction_FullMethodName        = "/upi.mandate.MandateService/DeclineMandateAction"
	MandateService_GetMandateRequestParameters_FullMethodName = "/upi.mandate.MandateService/GetMandateRequestParameters"
	MandateService_GetMandateDetails_FullMethodName           = "/upi.mandate.MandateService/GetMandateDetails"
	MandateService_ModifyMandate_FullMethodName               = "/upi.mandate.MandateService/ModifyMandate"
	MandateService_RevokeMandate_FullMethodName               = "/upi.mandate.MandateService/RevokeMandate"
	MandateService_PauseUnpauseMandate_FullMethodName         = "/upi.mandate.MandateService/PauseUnpauseMandate"
	MandateService_InitiateMandateExecution_FullMethodName    = "/upi.mandate.MandateService/InitiateMandateExecution"
)

// MandateServiceClient is the client API for MandateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MandateServiceClient interface {
	// Creates the mandate entity and mandate request
	CreateMandate(ctx context.Context, in *CreateMandateRequest, opts ...grpc.CallOption) (*CreateMandateResponse, error)
	// GetMandate fetches the mandate for the given request params
	// current supported methods to fetch the mandate
	// 1) req_id
	// 2) recurring_payment_id
	// 3) umn
	GetMandate(ctx context.Context, in *GetMandateRequest, opts ...grpc.CallOption) (*GetMandateResponse, error)
	// AuthoriseMandateAction authorises the mandate action.
	// Calls the vendor to authorises mandate action and updates the mandate request's state/stage
	AuthoriseMandateAction(ctx context.Context, in *AuthoriseMandateActionRequest, opts ...grpc.CallOption) (*AuthoriseMandateActionResponse, error)
	// FetchAndUpdateRequestStatus fetches the mandate request status and updates the mandate request status
	FetchAndUpdateRequestStatus(ctx context.Context, in *FetchAndUpdateRequestStatusRequest, opts ...grpc.CallOption) (*FetchAndUpdateRequestStatusResponse, error)
	// DeclineMandateAction declines the mandate action.
	// It checks if the request is in a state that can be declined and move the mandate request to declined status
	// NOTE - We are not sending a response to NCPI as of now. Will be taking this up later
	DeclineMandateAction(ctx context.Context, in *DeclineMandateActionRequest, opts ...grpc.CallOption) (*DeclineMandateActionResponse, error)
	// GetMandateRequestParameters fetches the mandate request parameters for given req id
	GetMandateRequestParameters(ctx context.Context, in *GetMandateRequestParametersRequest, opts ...grpc.CallOption) (*GetMandateRequestParametersResponse, error)
	// GetMandateDetails fetches the mandate details for given recurring payment id
	GetMandateDetails(ctx context.Context, in *GetMandateDetailsRequest, opts ...grpc.CallOption) (*GetMandateDetailsResponse, error)
	// ModifyMandate creates a mandate request with action as modify
	ModifyMandate(ctx context.Context, in *ModifyMandateRequest, opts ...grpc.CallOption) (*ModifyMandateResponse, error)
	// RevokeMandate creates a mandate request with action as revoke
	RevokeMandate(ctx context.Context, in *RevokeMandateRequest, opts ...grpc.CallOption) (*RevokeMandateResponse, error)
	// PauseUnpause creates a mandate request with action as pause
	PauseUnpauseMandate(ctx context.Context, in *PauseUnpauseMandateRequest, opts ...grpc.CallOption) (*PauseUnpauseMandateResponse, error)
	// InitiateMandateExecution initiates mandate execution by making call to vendor
	InitiateMandateExecution(ctx context.Context, in *InitiateMandateExecutionRequest, opts ...grpc.CallOption) (*InitiateMandateExecutionResponse, error)
}

type mandateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMandateServiceClient(cc grpc.ClientConnInterface) MandateServiceClient {
	return &mandateServiceClient{cc}
}

func (c *mandateServiceClient) CreateMandate(ctx context.Context, in *CreateMandateRequest, opts ...grpc.CallOption) (*CreateMandateResponse, error) {
	out := new(CreateMandateResponse)
	err := c.cc.Invoke(ctx, MandateService_CreateMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) GetMandate(ctx context.Context, in *GetMandateRequest, opts ...grpc.CallOption) (*GetMandateResponse, error) {
	out := new(GetMandateResponse)
	err := c.cc.Invoke(ctx, MandateService_GetMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) AuthoriseMandateAction(ctx context.Context, in *AuthoriseMandateActionRequest, opts ...grpc.CallOption) (*AuthoriseMandateActionResponse, error) {
	out := new(AuthoriseMandateActionResponse)
	err := c.cc.Invoke(ctx, MandateService_AuthoriseMandateAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) FetchAndUpdateRequestStatus(ctx context.Context, in *FetchAndUpdateRequestStatusRequest, opts ...grpc.CallOption) (*FetchAndUpdateRequestStatusResponse, error) {
	out := new(FetchAndUpdateRequestStatusResponse)
	err := c.cc.Invoke(ctx, MandateService_FetchAndUpdateRequestStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) DeclineMandateAction(ctx context.Context, in *DeclineMandateActionRequest, opts ...grpc.CallOption) (*DeclineMandateActionResponse, error) {
	out := new(DeclineMandateActionResponse)
	err := c.cc.Invoke(ctx, MandateService_DeclineMandateAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) GetMandateRequestParameters(ctx context.Context, in *GetMandateRequestParametersRequest, opts ...grpc.CallOption) (*GetMandateRequestParametersResponse, error) {
	out := new(GetMandateRequestParametersResponse)
	err := c.cc.Invoke(ctx, MandateService_GetMandateRequestParameters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) GetMandateDetails(ctx context.Context, in *GetMandateDetailsRequest, opts ...grpc.CallOption) (*GetMandateDetailsResponse, error) {
	out := new(GetMandateDetailsResponse)
	err := c.cc.Invoke(ctx, MandateService_GetMandateDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) ModifyMandate(ctx context.Context, in *ModifyMandateRequest, opts ...grpc.CallOption) (*ModifyMandateResponse, error) {
	out := new(ModifyMandateResponse)
	err := c.cc.Invoke(ctx, MandateService_ModifyMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) RevokeMandate(ctx context.Context, in *RevokeMandateRequest, opts ...grpc.CallOption) (*RevokeMandateResponse, error) {
	out := new(RevokeMandateResponse)
	err := c.cc.Invoke(ctx, MandateService_RevokeMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) PauseUnpauseMandate(ctx context.Context, in *PauseUnpauseMandateRequest, opts ...grpc.CallOption) (*PauseUnpauseMandateResponse, error) {
	out := new(PauseUnpauseMandateResponse)
	err := c.cc.Invoke(ctx, MandateService_PauseUnpauseMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mandateServiceClient) InitiateMandateExecution(ctx context.Context, in *InitiateMandateExecutionRequest, opts ...grpc.CallOption) (*InitiateMandateExecutionResponse, error) {
	out := new(InitiateMandateExecutionResponse)
	err := c.cc.Invoke(ctx, MandateService_InitiateMandateExecution_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MandateServiceServer is the server API for MandateService service.
// All implementations should embed UnimplementedMandateServiceServer
// for forward compatibility
type MandateServiceServer interface {
	// Creates the mandate entity and mandate request
	CreateMandate(context.Context, *CreateMandateRequest) (*CreateMandateResponse, error)
	// GetMandate fetches the mandate for the given request params
	// current supported methods to fetch the mandate
	// 1) req_id
	// 2) recurring_payment_id
	// 3) umn
	GetMandate(context.Context, *GetMandateRequest) (*GetMandateResponse, error)
	// AuthoriseMandateAction authorises the mandate action.
	// Calls the vendor to authorises mandate action and updates the mandate request's state/stage
	AuthoriseMandateAction(context.Context, *AuthoriseMandateActionRequest) (*AuthoriseMandateActionResponse, error)
	// FetchAndUpdateRequestStatus fetches the mandate request status and updates the mandate request status
	FetchAndUpdateRequestStatus(context.Context, *FetchAndUpdateRequestStatusRequest) (*FetchAndUpdateRequestStatusResponse, error)
	// DeclineMandateAction declines the mandate action.
	// It checks if the request is in a state that can be declined and move the mandate request to declined status
	// NOTE - We are not sending a response to NCPI as of now. Will be taking this up later
	DeclineMandateAction(context.Context, *DeclineMandateActionRequest) (*DeclineMandateActionResponse, error)
	// GetMandateRequestParameters fetches the mandate request parameters for given req id
	GetMandateRequestParameters(context.Context, *GetMandateRequestParametersRequest) (*GetMandateRequestParametersResponse, error)
	// GetMandateDetails fetches the mandate details for given recurring payment id
	GetMandateDetails(context.Context, *GetMandateDetailsRequest) (*GetMandateDetailsResponse, error)
	// ModifyMandate creates a mandate request with action as modify
	ModifyMandate(context.Context, *ModifyMandateRequest) (*ModifyMandateResponse, error)
	// RevokeMandate creates a mandate request with action as revoke
	RevokeMandate(context.Context, *RevokeMandateRequest) (*RevokeMandateResponse, error)
	// PauseUnpause creates a mandate request with action as pause
	PauseUnpauseMandate(context.Context, *PauseUnpauseMandateRequest) (*PauseUnpauseMandateResponse, error)
	// InitiateMandateExecution initiates mandate execution by making call to vendor
	InitiateMandateExecution(context.Context, *InitiateMandateExecutionRequest) (*InitiateMandateExecutionResponse, error)
}

// UnimplementedMandateServiceServer should be embedded to have forward compatible implementations.
type UnimplementedMandateServiceServer struct {
}

func (UnimplementedMandateServiceServer) CreateMandate(context.Context, *CreateMandateRequest) (*CreateMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMandate not implemented")
}
func (UnimplementedMandateServiceServer) GetMandate(context.Context, *GetMandateRequest) (*GetMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMandate not implemented")
}
func (UnimplementedMandateServiceServer) AuthoriseMandateAction(context.Context, *AuthoriseMandateActionRequest) (*AuthoriseMandateActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthoriseMandateAction not implemented")
}
func (UnimplementedMandateServiceServer) FetchAndUpdateRequestStatus(context.Context, *FetchAndUpdateRequestStatusRequest) (*FetchAndUpdateRequestStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchAndUpdateRequestStatus not implemented")
}
func (UnimplementedMandateServiceServer) DeclineMandateAction(context.Context, *DeclineMandateActionRequest) (*DeclineMandateActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeclineMandateAction not implemented")
}
func (UnimplementedMandateServiceServer) GetMandateRequestParameters(context.Context, *GetMandateRequestParametersRequest) (*GetMandateRequestParametersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMandateRequestParameters not implemented")
}
func (UnimplementedMandateServiceServer) GetMandateDetails(context.Context, *GetMandateDetailsRequest) (*GetMandateDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMandateDetails not implemented")
}
func (UnimplementedMandateServiceServer) ModifyMandate(context.Context, *ModifyMandateRequest) (*ModifyMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyMandate not implemented")
}
func (UnimplementedMandateServiceServer) RevokeMandate(context.Context, *RevokeMandateRequest) (*RevokeMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RevokeMandate not implemented")
}
func (UnimplementedMandateServiceServer) PauseUnpauseMandate(context.Context, *PauseUnpauseMandateRequest) (*PauseUnpauseMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseUnpauseMandate not implemented")
}
func (UnimplementedMandateServiceServer) InitiateMandateExecution(context.Context, *InitiateMandateExecutionRequest) (*InitiateMandateExecutionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateMandateExecution not implemented")
}

// UnsafeMandateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MandateServiceServer will
// result in compilation errors.
type UnsafeMandateServiceServer interface {
	mustEmbedUnimplementedMandateServiceServer()
}

func RegisterMandateServiceServer(s grpc.ServiceRegistrar, srv MandateServiceServer) {
	s.RegisterService(&MandateService_ServiceDesc, srv)
}

func _MandateService_CreateMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).CreateMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_CreateMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).CreateMandate(ctx, req.(*CreateMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_GetMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).GetMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_GetMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).GetMandate(ctx, req.(*GetMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_AuthoriseMandateAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthoriseMandateActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).AuthoriseMandateAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_AuthoriseMandateAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).AuthoriseMandateAction(ctx, req.(*AuthoriseMandateActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_FetchAndUpdateRequestStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAndUpdateRequestStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).FetchAndUpdateRequestStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_FetchAndUpdateRequestStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).FetchAndUpdateRequestStatus(ctx, req.(*FetchAndUpdateRequestStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_DeclineMandateAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeclineMandateActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).DeclineMandateAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_DeclineMandateAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).DeclineMandateAction(ctx, req.(*DeclineMandateActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_GetMandateRequestParameters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMandateRequestParametersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).GetMandateRequestParameters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_GetMandateRequestParameters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).GetMandateRequestParameters(ctx, req.(*GetMandateRequestParametersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_GetMandateDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMandateDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).GetMandateDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_GetMandateDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).GetMandateDetails(ctx, req.(*GetMandateDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_ModifyMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).ModifyMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_ModifyMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).ModifyMandate(ctx, req.(*ModifyMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_RevokeMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RevokeMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).RevokeMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_RevokeMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).RevokeMandate(ctx, req.(*RevokeMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_PauseUnpauseMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PauseUnpauseMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).PauseUnpauseMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_PauseUnpauseMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).PauseUnpauseMandate(ctx, req.(*PauseUnpauseMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MandateService_InitiateMandateExecution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateMandateExecutionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MandateServiceServer).InitiateMandateExecution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MandateService_InitiateMandateExecution_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MandateServiceServer).InitiateMandateExecution(ctx, req.(*InitiateMandateExecutionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MandateService_ServiceDesc is the grpc.ServiceDesc for MandateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MandateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "upi.mandate.MandateService",
	HandlerType: (*MandateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMandate",
			Handler:    _MandateService_CreateMandate_Handler,
		},
		{
			MethodName: "GetMandate",
			Handler:    _MandateService_GetMandate_Handler,
		},
		{
			MethodName: "AuthoriseMandateAction",
			Handler:    _MandateService_AuthoriseMandateAction_Handler,
		},
		{
			MethodName: "FetchAndUpdateRequestStatus",
			Handler:    _MandateService_FetchAndUpdateRequestStatus_Handler,
		},
		{
			MethodName: "DeclineMandateAction",
			Handler:    _MandateService_DeclineMandateAction_Handler,
		},
		{
			MethodName: "GetMandateRequestParameters",
			Handler:    _MandateService_GetMandateRequestParameters_Handler,
		},
		{
			MethodName: "GetMandateDetails",
			Handler:    _MandateService_GetMandateDetails_Handler,
		},
		{
			MethodName: "ModifyMandate",
			Handler:    _MandateService_ModifyMandate_Handler,
		},
		{
			MethodName: "RevokeMandate",
			Handler:    _MandateService_RevokeMandate_Handler,
		},
		{
			MethodName: "PauseUnpauseMandate",
			Handler:    _MandateService_PauseUnpauseMandate_Handler,
		},
		{
			MethodName: "InitiateMandateExecution",
			Handler:    _MandateService_InitiateMandateExecution_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/upi/mandate/service.proto",
}
