// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/acl/whatsapp.proto

package acl

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SendWhatsAppMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendWhatsAppMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendWhatsAppMessageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendWhatsAppMessageRequestMultiError, or nil if none found.
func (m *SendWhatsAppMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendWhatsAppMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResponseType

	for idx, item := range m.GetMessages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SendWhatsAppMessageRequestValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SendWhatsAppMessageRequestValidationError{
						field:  fmt.Sprintf("Messages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SendWhatsAppMessageRequestValidationError{
					field:  fmt.Sprintf("Messages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SendWhatsAppMessageRequestMultiError(errors)
	}

	return nil
}

// SendWhatsAppMessageRequestMultiError is an error wrapping multiple
// validation errors returned by SendWhatsAppMessageRequest.ValidateAll() if
// the designated constraints aren't met.
type SendWhatsAppMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendWhatsAppMessageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendWhatsAppMessageRequestMultiError) AllErrors() []error { return m }

// SendWhatsAppMessageRequestValidationError is the validation error returned
// by SendWhatsAppMessageRequest.Validate if the designated constraints aren't met.
type SendWhatsAppMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendWhatsAppMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendWhatsAppMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendWhatsAppMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendWhatsAppMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendWhatsAppMessageRequestValidationError) ErrorName() string {
	return "SendWhatsAppMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendWhatsAppMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendWhatsAppMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendWhatsAppMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendWhatsAppMessageRequestValidationError{}

// Validate checks the field values on WhatsAppMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WhatsAppMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WhatsAppMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WhatsAppMessageMultiError, or nil if none found.
func (m *WhatsAppMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *WhatsAppMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sender

	// no validation rules for To

	// no validation rules for Channel

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhatsAppMessageValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMedia()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "Media",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "Media",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMedia()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhatsAppMessageValidationError{
				field:  "Media",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "Template",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "Template",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhatsAppMessageValidationError{
				field:  "Template",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMediaTemplate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "MediaTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhatsAppMessageValidationError{
					field:  "MediaTemplate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMediaTemplate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhatsAppMessageValidationError{
				field:  "MediaTemplate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WhatsAppMessageMultiError(errors)
	}

	return nil
}

// WhatsAppMessageMultiError is an error wrapping multiple validation errors
// returned by WhatsAppMessage.ValidateAll() if the designated constraints
// aren't met.
type WhatsAppMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WhatsAppMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WhatsAppMessageMultiError) AllErrors() []error { return m }

// WhatsAppMessageValidationError is the validation error returned by
// WhatsAppMessage.Validate if the designated constraints aren't met.
type WhatsAppMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WhatsAppMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WhatsAppMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WhatsAppMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WhatsAppMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WhatsAppMessageValidationError) ErrorName() string { return "WhatsAppMessageValidationError" }

// Error satisfies the builtin error interface
func (e WhatsAppMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWhatsAppMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WhatsAppMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WhatsAppMessageValidationError{}

// Validate checks the field values on TextMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TextMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TextMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TextMessageMultiError, or
// nil if none found.
func (m *TextMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *TextMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	// no validation rules for PreviewFirstUrl

	if len(errors) > 0 {
		return TextMessageMultiError(errors)
	}

	return nil
}

// TextMessageMultiError is an error wrapping multiple validation errors
// returned by TextMessage.ValidateAll() if the designated constraints aren't met.
type TextMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TextMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TextMessageMultiError) AllErrors() []error { return m }

// TextMessageValidationError is the validation error returned by
// TextMessage.Validate if the designated constraints aren't met.
type TextMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TextMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TextMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TextMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TextMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TextMessageValidationError) ErrorName() string { return "TextMessageValidationError" }

// Error satisfies the builtin error interface
func (e TextMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTextMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TextMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TextMessageValidationError{}

// Validate checks the field values on MediaMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MediaMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MediaMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MediaMessageMultiError, or
// nil if none found.
func (m *MediaMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *MediaMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ContentType

	// no validation rules for MediaUrl

	// no validation rules for Caption

	if len(errors) > 0 {
		return MediaMessageMultiError(errors)
	}

	return nil
}

// MediaMessageMultiError is an error wrapping multiple validation errors
// returned by MediaMessage.ValidateAll() if the designated constraints aren't met.
type MediaMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MediaMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MediaMessageMultiError) AllErrors() []error { return m }

// MediaMessageValidationError is the validation error returned by
// MediaMessage.Validate if the designated constraints aren't met.
type MediaMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MediaMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MediaMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MediaMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MediaMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MediaMessageValidationError) ErrorName() string { return "MediaMessageValidationError" }

// Error satisfies the builtin error interface
func (e MediaMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMediaMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MediaMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MediaMessageValidationError{}

// Validate checks the field values on TemplateMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TemplateMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TemplateMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TemplateMessageMultiError, or nil if none found.
func (m *TemplateMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *TemplateMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LangCode

	// no validation rules for Template

	for idx, item := range m.GetBody() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TemplateMessageValidationError{
						field:  fmt.Sprintf("Body[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TemplateMessageValidationError{
						field:  fmt.Sprintf("Body[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TemplateMessageValidationError{
					field:  fmt.Sprintf("Body[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TemplateMessageMultiError(errors)
	}

	return nil
}

// TemplateMessageMultiError is an error wrapping multiple validation errors
// returned by TemplateMessage.ValidateAll() if the designated constraints
// aren't met.
type TemplateMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TemplateMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TemplateMessageMultiError) AllErrors() []error { return m }

// TemplateMessageValidationError is the validation error returned by
// TemplateMessage.Validate if the designated constraints aren't met.
type TemplateMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TemplateMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TemplateMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TemplateMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TemplateMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TemplateMessageValidationError) ErrorName() string { return "TemplateMessageValidationError" }

// Error satisfies the builtin error interface
func (e TemplateMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTemplateMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TemplateMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TemplateMessageValidationError{}

// Validate checks the field values on DynamicVariable with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DynamicVariable) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DynamicVariable with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DynamicVariableMultiError, or nil if none found.
func (m *DynamicVariable) ValidateAll() error {
	return m.validate(true)
}

func (m *DynamicVariable) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Text

	if len(errors) > 0 {
		return DynamicVariableMultiError(errors)
	}

	return nil
}

// DynamicVariableMultiError is an error wrapping multiple validation errors
// returned by DynamicVariable.ValidateAll() if the designated constraints
// aren't met.
type DynamicVariableMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DynamicVariableMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DynamicVariableMultiError) AllErrors() []error { return m }

// DynamicVariableValidationError is the validation error returned by
// DynamicVariable.Validate if the designated constraints aren't met.
type DynamicVariableValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DynamicVariableValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DynamicVariableValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DynamicVariableValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DynamicVariableValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DynamicVariableValidationError) ErrorName() string { return "DynamicVariableValidationError" }

// Error satisfies the builtin error interface
func (e DynamicVariableValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDynamicVariable.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DynamicVariableValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DynamicVariableValidationError{}

// Validate checks the field values on MediaTemplateMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MediaTemplateMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MediaTemplateMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MediaTemplateMessageMultiError, or nil if none found.
func (m *MediaTemplateMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *MediaTemplateMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LangCode

	// no validation rules for MediaUrl

	// no validation rules for ContentType

	// no validation rules for Template

	// no validation rules for Parameters

	// no validation rules for MediaFileName

	if len(errors) > 0 {
		return MediaTemplateMessageMultiError(errors)
	}

	return nil
}

// MediaTemplateMessageMultiError is an error wrapping multiple validation
// errors returned by MediaTemplateMessage.ValidateAll() if the designated
// constraints aren't met.
type MediaTemplateMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MediaTemplateMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MediaTemplateMessageMultiError) AllErrors() []error { return m }

// MediaTemplateMessageValidationError is the validation error returned by
// MediaTemplateMessage.Validate if the designated constraints aren't met.
type MediaTemplateMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MediaTemplateMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MediaTemplateMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MediaTemplateMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MediaTemplateMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MediaTemplateMessageValidationError) ErrorName() string {
	return "MediaTemplateMessageValidationError"
}

// Error satisfies the builtin error interface
func (e MediaTemplateMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMediaTemplateMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MediaTemplateMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MediaTemplateMessageValidationError{}

// Validate checks the field values on SendWhatsAppMessageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendWhatsAppMessageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendWhatsAppMessageResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendWhatsAppMessageResponseMultiError, or nil if none found.
func (m *SendWhatsAppMessageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendWhatsAppMessageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	// no validation rules for ResponseId

	for idx, item := range m.GetDescription() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SendWhatsAppMessageResponseValidationError{
						field:  fmt.Sprintf("Description[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SendWhatsAppMessageResponseValidationError{
						field:  fmt.Sprintf("Description[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SendWhatsAppMessageResponseValidationError{
					field:  fmt.Sprintf("Description[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SendWhatsAppMessageResponseMultiError(errors)
	}

	return nil
}

// SendWhatsAppMessageResponseMultiError is an error wrapping multiple
// validation errors returned by SendWhatsAppMessageResponse.ValidateAll() if
// the designated constraints aren't met.
type SendWhatsAppMessageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendWhatsAppMessageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendWhatsAppMessageResponseMultiError) AllErrors() []error { return m }

// SendWhatsAppMessageResponseValidationError is the validation error returned
// by SendWhatsAppMessageResponse.Validate if the designated constraints
// aren't met.
type SendWhatsAppMessageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendWhatsAppMessageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendWhatsAppMessageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendWhatsAppMessageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendWhatsAppMessageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendWhatsAppMessageResponseValidationError) ErrorName() string {
	return "SendWhatsAppMessageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendWhatsAppMessageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendWhatsAppMessageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendWhatsAppMessageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendWhatsAppMessageResponseValidationError{}

// Validate checks the field values on Description with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Description) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Description with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DescriptionMultiError, or
// nil if none found.
func (m *Description) ValidateAll() error {
	return m.validate(true)
}

func (m *Description) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrorCode

	// no validation rules for ErrorDescription

	if len(errors) > 0 {
		return DescriptionMultiError(errors)
	}

	return nil
}

// DescriptionMultiError is an error wrapping multiple validation errors
// returned by Description.ValidateAll() if the designated constraints aren't met.
type DescriptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DescriptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DescriptionMultiError) AllErrors() []error { return m }

// DescriptionValidationError is the validation error returned by
// Description.Validate if the designated constraints aren't met.
type DescriptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DescriptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DescriptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DescriptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DescriptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DescriptionValidationError) ErrorName() string { return "DescriptionValidationError" }

// Error satisfies the builtin error interface
func (e DescriptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDescription.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DescriptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DescriptionValidationError{}

// Validate checks the field values on OptInUserVendorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OptInUserVendorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OptInUserVendorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OptInUserVendorRequestMultiError, or nil if none found.
func (m *OptInUserVendorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *OptInUserVendorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EnterpriseId

	// no validation rules for PhoneNumber

	// no validation rules for Token

	if len(errors) > 0 {
		return OptInUserVendorRequestMultiError(errors)
	}

	return nil
}

// OptInUserVendorRequestMultiError is an error wrapping multiple validation
// errors returned by OptInUserVendorRequest.ValidateAll() if the designated
// constraints aren't met.
type OptInUserVendorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OptInUserVendorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OptInUserVendorRequestMultiError) AllErrors() []error { return m }

// OptInUserVendorRequestValidationError is the validation error returned by
// OptInUserVendorRequest.Validate if the designated constraints aren't met.
type OptInUserVendorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OptInUserVendorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OptInUserVendorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OptInUserVendorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OptInUserVendorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OptInUserVendorRequestValidationError) ErrorName() string {
	return "OptInUserVendorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e OptInUserVendorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOptInUserVendorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OptInUserVendorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OptInUserVendorRequestValidationError{}

// Validate checks the field values on OptInUserVendorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OptInUserVendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OptInUserVendorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OptInUserVendorResponseMultiError, or nil if none found.
func (m *OptInUserVendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *OptInUserVendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	// no validation rules for Status

	// no validation rules for Timestamp

	// no validation rules for Data

	// no validation rules for Message

	if len(errors) > 0 {
		return OptInUserVendorResponseMultiError(errors)
	}

	return nil
}

// OptInUserVendorResponseMultiError is an error wrapping multiple validation
// errors returned by OptInUserVendorResponse.ValidateAll() if the designated
// constraints aren't met.
type OptInUserVendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OptInUserVendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OptInUserVendorResponseMultiError) AllErrors() []error { return m }

// OptInUserVendorResponseValidationError is the validation error returned by
// OptInUserVendorResponse.Validate if the designated constraints aren't met.
type OptInUserVendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OptInUserVendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OptInUserVendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OptInUserVendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OptInUserVendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OptInUserVendorResponseValidationError) ErrorName() string {
	return "OptInUserVendorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e OptInUserVendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOptInUserVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OptInUserVendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OptInUserVendorResponseValidationError{}
