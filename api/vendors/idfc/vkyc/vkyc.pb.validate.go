// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/idfc/vkyc/vkyc.proto

package vkyc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateVkycProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateVkycProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVkycProfileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVkycProfileRequestMultiError, or nil if none found.
func (m *CreateVkycProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReqId

	// no validation rules for ReferenceId

	if len(errors) > 0 {
		return CreateVkycProfileRequestMultiError(errors)
	}

	return nil
}

// CreateVkycProfileRequestMultiError is an error wrapping multiple validation
// errors returned by CreateVkycProfileRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateVkycProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileRequestMultiError) AllErrors() []error { return m }

// CreateVkycProfileRequestValidationError is the validation error returned by
// CreateVkycProfileRequest.Validate if the designated constraints aren't met.
type CreateVkycProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVkycProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVkycProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVkycProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVkycProfileRequestValidationError) ErrorName() string {
	return "CreateVkycProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileRequestValidationError{}

// Validate checks the field values on CreateVkycProfileResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateVkycProfileResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVkycProfileResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVkycProfileResponseMultiError, or nil if none found.
func (m *CreateVkycProfileResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReqId

	if all {
		switch v := interface{}(m.GetCreateProfileResp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVkycProfileResponseValidationError{
					field:  "CreateProfileResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVkycProfileResponseValidationError{
					field:  "CreateProfileResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateProfileResp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVkycProfileResponseValidationError{
				field:  "CreateProfileResp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVkycProfileResponseMultiError(errors)
	}

	return nil
}

// CreateVkycProfileResponseMultiError is an error wrapping multiple validation
// errors returned by CreateVkycProfileResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateVkycProfileResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileResponseMultiError) AllErrors() []error { return m }

// CreateVkycProfileResponseValidationError is the validation error returned by
// CreateVkycProfileResponse.Validate if the designated constraints aren't met.
type CreateVkycProfileResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVkycProfileResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVkycProfileResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVkycProfileResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVkycProfileResponseValidationError) ErrorName() string {
	return "CreateVkycProfileResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileResponseValidationError{}

// Validate checks the field values on CreateVkycProfileBusinessErrorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreateVkycProfileBusinessErrorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateVkycProfileBusinessErrorResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CreateVkycProfileBusinessErrorResponseMultiError, or nil if none found.
func (m *CreateVkycProfileBusinessErrorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileBusinessErrorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCreateProfileResp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVkycProfileBusinessErrorResponseValidationError{
					field:  "CreateProfileResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVkycProfileBusinessErrorResponseValidationError{
					field:  "CreateProfileResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateProfileResp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVkycProfileBusinessErrorResponseValidationError{
				field:  "CreateProfileResp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVkycProfileBusinessErrorResponseMultiError(errors)
	}

	return nil
}

// CreateVkycProfileBusinessErrorResponseMultiError is an error wrapping
// multiple validation errors returned by
// CreateVkycProfileBusinessErrorResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateVkycProfileBusinessErrorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileBusinessErrorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileBusinessErrorResponseMultiError) AllErrors() []error { return m }

// CreateVkycProfileBusinessErrorResponseValidationError is the validation
// error returned by CreateVkycProfileBusinessErrorResponse.Validate if the
// designated constraints aren't met.
type CreateVkycProfileBusinessErrorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileBusinessErrorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVkycProfileBusinessErrorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVkycProfileBusinessErrorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVkycProfileBusinessErrorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVkycProfileBusinessErrorResponseValidationError) ErrorName() string {
	return "CreateVkycProfileBusinessErrorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileBusinessErrorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileBusinessErrorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileBusinessErrorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileBusinessErrorResponseValidationError{}

// Validate checks the field values on VkycQueueMetricsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VkycQueueMetricsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VkycQueueMetricsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VkycQueueMetricsRequestMultiError, or nil if none found.
func (m *VkycQueueMetricsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycQueueMetricsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReqId

	// no validation rules for Source

	if len(errors) > 0 {
		return VkycQueueMetricsRequestMultiError(errors)
	}

	return nil
}

// VkycQueueMetricsRequestMultiError is an error wrapping multiple validation
// errors returned by VkycQueueMetricsRequest.ValidateAll() if the designated
// constraints aren't met.
type VkycQueueMetricsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycQueueMetricsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycQueueMetricsRequestMultiError) AllErrors() []error { return m }

// VkycQueueMetricsRequestValidationError is the validation error returned by
// VkycQueueMetricsRequest.Validate if the designated constraints aren't met.
type VkycQueueMetricsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycQueueMetricsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VkycQueueMetricsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VkycQueueMetricsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VkycQueueMetricsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VkycQueueMetricsRequestValidationError) ErrorName() string {
	return "VkycQueueMetricsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VkycQueueMetricsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycQueueMetricsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycQueueMetricsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycQueueMetricsRequestValidationError{}

// Validate checks the field values on VkycQueueMetricsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VkycQueueMetricsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VkycQueueMetricsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VkycQueueMetricsResponseMultiError, or nil if none found.
func (m *VkycQueueMetricsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycQueueMetricsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQueueMetricsResp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VkycQueueMetricsResponseValidationError{
					field:  "QueueMetricsResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VkycQueueMetricsResponseValidationError{
					field:  "QueueMetricsResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQueueMetricsResp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VkycQueueMetricsResponseValidationError{
				field:  "QueueMetricsResp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VkycQueueMetricsResponseMultiError(errors)
	}

	return nil
}

// VkycQueueMetricsResponseMultiError is an error wrapping multiple validation
// errors returned by VkycQueueMetricsResponse.ValidateAll() if the designated
// constraints aren't met.
type VkycQueueMetricsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycQueueMetricsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycQueueMetricsResponseMultiError) AllErrors() []error { return m }

// VkycQueueMetricsResponseValidationError is the validation error returned by
// VkycQueueMetricsResponse.Validate if the designated constraints aren't met.
type VkycQueueMetricsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycQueueMetricsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VkycQueueMetricsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VkycQueueMetricsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VkycQueueMetricsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VkycQueueMetricsResponseValidationError) ErrorName() string {
	return "VkycQueueMetricsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VkycQueueMetricsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycQueueMetricsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycQueueMetricsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycQueueMetricsResponseValidationError{}

// Validate checks the field values on VkycQueueMetricsBusinessErrorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *VkycQueueMetricsBusinessErrorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VkycQueueMetricsBusinessErrorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VkycQueueMetricsBusinessErrorResponseMultiError, or nil if none found.
func (m *VkycQueueMetricsBusinessErrorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycQueueMetricsBusinessErrorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQueueMetricsResp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VkycQueueMetricsBusinessErrorResponseValidationError{
					field:  "QueueMetricsResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VkycQueueMetricsBusinessErrorResponseValidationError{
					field:  "QueueMetricsResp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQueueMetricsResp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VkycQueueMetricsBusinessErrorResponseValidationError{
				field:  "QueueMetricsResp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VkycQueueMetricsBusinessErrorResponseMultiError(errors)
	}

	return nil
}

// VkycQueueMetricsBusinessErrorResponseMultiError is an error wrapping
// multiple validation errors returned by
// VkycQueueMetricsBusinessErrorResponse.ValidateAll() if the designated
// constraints aren't met.
type VkycQueueMetricsBusinessErrorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycQueueMetricsBusinessErrorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycQueueMetricsBusinessErrorResponseMultiError) AllErrors() []error { return m }

// VkycQueueMetricsBusinessErrorResponseValidationError is the validation error
// returned by VkycQueueMetricsBusinessErrorResponse.Validate if the
// designated constraints aren't met.
type VkycQueueMetricsBusinessErrorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycQueueMetricsBusinessErrorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VkycQueueMetricsBusinessErrorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VkycQueueMetricsBusinessErrorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VkycQueueMetricsBusinessErrorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VkycQueueMetricsBusinessErrorResponseValidationError) ErrorName() string {
	return "VkycQueueMetricsBusinessErrorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VkycQueueMetricsBusinessErrorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycQueueMetricsBusinessErrorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycQueueMetricsBusinessErrorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycQueueMetricsBusinessErrorResponseValidationError{}

// Validate checks the field values on
// CreateVkycProfileResponse_CreateProfileResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateVkycProfileResponse_CreateProfileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateVkycProfileResponse_CreateProfileResp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreateVkycProfileResponse_CreateProfileRespMultiError, or nil if none found.
func (m *CreateVkycProfileResponse_CreateProfileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileResponse_CreateProfileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVkycProfileResponse_CreateProfileRespValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVkycProfileResponse_CreateProfileRespValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVkycProfileResponse_CreateProfileRespValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResourceData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVkycProfileResponse_CreateProfileRespValidationError{
					field:  "ResourceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVkycProfileResponse_CreateProfileRespValidationError{
					field:  "ResourceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResourceData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVkycProfileResponse_CreateProfileRespValidationError{
				field:  "ResourceData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVkycProfileResponse_CreateProfileRespMultiError(errors)
	}

	return nil
}

// CreateVkycProfileResponse_CreateProfileRespMultiError is an error wrapping
// multiple validation errors returned by
// CreateVkycProfileResponse_CreateProfileResp.ValidateAll() if the designated
// constraints aren't met.
type CreateVkycProfileResponse_CreateProfileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileResponse_CreateProfileRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileResponse_CreateProfileRespMultiError) AllErrors() []error { return m }

// CreateVkycProfileResponse_CreateProfileRespValidationError is the validation
// error returned by CreateVkycProfileResponse_CreateProfileResp.Validate if
// the designated constraints aren't met.
type CreateVkycProfileResponse_CreateProfileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileResponse_CreateProfileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVkycProfileResponse_CreateProfileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVkycProfileResponse_CreateProfileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVkycProfileResponse_CreateProfileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVkycProfileResponse_CreateProfileRespValidationError) ErrorName() string {
	return "CreateVkycProfileResponse_CreateProfileRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileResponse_CreateProfileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileResponse_CreateProfileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileResponse_CreateProfileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileResponse_CreateProfileRespValidationError{}

// Validate checks the field values on
// CreateVkycProfileResponse_CreateProfileResp_MetaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVkycProfileResponse_CreateProfileResp_MetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateVkycProfileResponse_CreateProfileResp_MetaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CreateVkycProfileResponse_CreateProfileResp_MetaDataMultiError, or nil if
// none found.
func (m *CreateVkycProfileResponse_CreateProfileResp_MetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileResponse_CreateProfileResp_MetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	// no validation rules for Status

	// no validation rules for Version

	// no validation rules for Time

	if len(errors) > 0 {
		return CreateVkycProfileResponse_CreateProfileResp_MetaDataMultiError(errors)
	}

	return nil
}

// CreateVkycProfileResponse_CreateProfileResp_MetaDataMultiError is an error
// wrapping multiple validation errors returned by
// CreateVkycProfileResponse_CreateProfileResp_MetaData.ValidateAll() if the
// designated constraints aren't met.
type CreateVkycProfileResponse_CreateProfileResp_MetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileResponse_CreateProfileResp_MetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileResponse_CreateProfileResp_MetaDataMultiError) AllErrors() []error { return m }

// CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError is the
// validation error returned by
// CreateVkycProfileResponse_CreateProfileResp_MetaData.Validate if the
// designated constraints aren't met.
type CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError) ErrorName() string {
	return "CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileResponse_CreateProfileResp_MetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileResponse_CreateProfileResp_MetaDataValidationError{}

// Validate checks the field values on
// CreateVkycProfileResponse_CreateProfileResp_ResourceData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateVkycProfileResponse_CreateProfileResp_ResourceData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateVkycProfileResponse_CreateProfileResp_ResourceData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVkycProfileResponse_CreateProfileResp_ResourceDataMultiError, or nil
// if none found.
func (m *CreateVkycProfileResponse_CreateProfileResp_ResourceData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileResponse_CreateProfileResp_ResourceData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProfileId

	// no validation rules for CaptureLink

	// no validation rules for CaptureExpiresAt

	if len(errors) > 0 {
		return CreateVkycProfileResponse_CreateProfileResp_ResourceDataMultiError(errors)
	}

	return nil
}

// CreateVkycProfileResponse_CreateProfileResp_ResourceDataMultiError is an
// error wrapping multiple validation errors returned by
// CreateVkycProfileResponse_CreateProfileResp_ResourceData.ValidateAll() if
// the designated constraints aren't met.
type CreateVkycProfileResponse_CreateProfileResp_ResourceDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileResponse_CreateProfileResp_ResourceDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileResponse_CreateProfileResp_ResourceDataMultiError) AllErrors() []error {
	return m
}

// CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError is
// the validation error returned by
// CreateVkycProfileResponse_CreateProfileResp_ResourceData.Validate if the
// designated constraints aren't met.
type CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError) ErrorName() string {
	return "CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileResponse_CreateProfileResp_ResourceData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileResponse_CreateProfileResp_ResourceDataValidationError{}

// Validate checks the field values on
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVkycProfileBusinessErrorResponse_CreateProfileRespMultiError, or nil
// if none found.
func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMetaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{
					field:  "MetaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{
				field:  "MetaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResourceData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{
					field:  "ResourceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{
					field:  "ResourceData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResourceData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{
				field:  "ResourceData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVkycProfileBusinessErrorResponse_CreateProfileRespMultiError(errors)
	}

	return nil
}

// CreateVkycProfileBusinessErrorResponse_CreateProfileRespMultiError is an
// error wrapping multiple validation errors returned by
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp.ValidateAll() if
// the designated constraints aren't met.
type CreateVkycProfileBusinessErrorResponse_CreateProfileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileBusinessErrorResponse_CreateProfileRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileBusinessErrorResponse_CreateProfileRespMultiError) AllErrors() []error {
	return m
}

// CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError is
// the validation error returned by
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp.Validate if the
// designated constraints aren't met.
type CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError) ErrorName() string {
	return "CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileBusinessErrorResponse_CreateProfileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileBusinessErrorResponse_CreateProfileRespValidationError{}

// Validate checks the field values on
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataMultiError,
// or nil if none found.
func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	// no validation rules for Status

	if len(errors) > 0 {
		return CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataMultiError(errors)
	}

	return nil
}

// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataMultiError
// is an error wrapping multiple validation errors returned by
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData.ValidateAll()
// if the designated constraints aren't met.
type CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataMultiError) AllErrors() []error {
	return m
}

// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError
// is the validation error returned by
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData.Validate
// if the designated constraints aren't met.
type CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError) ErrorName() string {
	return "CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileBusinessErrorResponse_CreateProfileResp_MetaDataValidationError{}

// Validate checks the field values on
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataMultiError,
// or nil if none found.
func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataMultiError(errors)
	}

	return nil
}

// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataMultiError
// is an error wrapping multiple validation errors returned by
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData.ValidateAll()
// if the designated constraints aren't met.
type CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataMultiError) AllErrors() []error {
	return m
}

// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError
// is the validation error returned by
// CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData.Validate
// if the designated constraints aren't met.
type CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError) ErrorName() string {
	return "CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVkycProfileBusinessErrorResponse_CreateProfileResp_ResourceDataValidationError{}

// Validate checks the field values on
// VkycQueueMetricsResponse_QueueMetricsResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VkycQueueMetricsResponse_QueueMetricsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VkycQueueMetricsResponse_QueueMetricsResp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// VkycQueueMetricsResponse_QueueMetricsRespMultiError, or nil if none found.
func (m *VkycQueueMetricsResponse_QueueMetricsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycQueueMetricsResponse_QueueMetricsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for ReqId

	for idx, item := range m.GetQueueMetrics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VkycQueueMetricsResponse_QueueMetricsRespValidationError{
						field:  fmt.Sprintf("QueueMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VkycQueueMetricsResponse_QueueMetricsRespValidationError{
						field:  fmt.Sprintf("QueueMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VkycQueueMetricsResponse_QueueMetricsRespValidationError{
					field:  fmt.Sprintf("QueueMetrics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return VkycQueueMetricsResponse_QueueMetricsRespMultiError(errors)
	}

	return nil
}

// VkycQueueMetricsResponse_QueueMetricsRespMultiError is an error wrapping
// multiple validation errors returned by
// VkycQueueMetricsResponse_QueueMetricsResp.ValidateAll() if the designated
// constraints aren't met.
type VkycQueueMetricsResponse_QueueMetricsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycQueueMetricsResponse_QueueMetricsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycQueueMetricsResponse_QueueMetricsRespMultiError) AllErrors() []error { return m }

// VkycQueueMetricsResponse_QueueMetricsRespValidationError is the validation
// error returned by VkycQueueMetricsResponse_QueueMetricsResp.Validate if the
// designated constraints aren't met.
type VkycQueueMetricsResponse_QueueMetricsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycQueueMetricsResponse_QueueMetricsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VkycQueueMetricsResponse_QueueMetricsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VkycQueueMetricsResponse_QueueMetricsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VkycQueueMetricsResponse_QueueMetricsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VkycQueueMetricsResponse_QueueMetricsRespValidationError) ErrorName() string {
	return "VkycQueueMetricsResponse_QueueMetricsRespValidationError"
}

// Error satisfies the builtin error interface
func (e VkycQueueMetricsResponse_QueueMetricsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycQueueMetricsResponse_QueueMetricsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycQueueMetricsResponse_QueueMetricsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycQueueMetricsResponse_QueueMetricsRespValidationError{}

// Validate checks the field values on
// VkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsMultiError, or nil if
// none found.
func (m *VkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VideoKycPriority

	// no validation rules for Label

	// no validation rules for OperatorsBusy

	// no validation rules for OperatorsAvailable

	// no validation rules for UsersQueued

	// no validation rules for AvgWaitTime

	if len(errors) > 0 {
		return VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsMultiError(errors)
	}

	return nil
}

// VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsMultiError is an error
// wrapping multiple validation errors returned by
// VkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics.ValidateAll() if the
// designated constraints aren't met.
type VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsMultiError) AllErrors() []error {
	return m
}

// VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError is the
// validation error returned by
// VkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics.Validate if the
// designated constraints aren't met.
type VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError) ErrorName() string {
	return "VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError"
}

// Error satisfies the builtin error interface
func (e VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycQueueMetricsResponse_QueueMetricsResp_QueueMetrics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycQueueMetricsResponse_QueueMetricsResp_QueueMetricsValidationError{}

// Validate checks the field values on
// VkycQueueMetricsBusinessErrorResponse_QueueMetricsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VkycQueueMetricsBusinessErrorResponse_QueueMetricsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// VkycQueueMetricsBusinessErrorResponse_QueueMetricsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespMultiError, or nil if
// none found.
func (m *VkycQueueMetricsBusinessErrorResponse_QueueMetricsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *VkycQueueMetricsBusinessErrorResponse_QueueMetricsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Message

	// no validation rules for ReqId

	if len(errors) > 0 {
		return VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespMultiError(errors)
	}

	return nil
}

// VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespMultiError is an error
// wrapping multiple validation errors returned by
// VkycQueueMetricsBusinessErrorResponse_QueueMetricsResp.ValidateAll() if the
// designated constraints aren't met.
type VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespMultiError) AllErrors() []error {
	return m
}

// VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError is the
// validation error returned by
// VkycQueueMetricsBusinessErrorResponse_QueueMetricsResp.Validate if the
// designated constraints aren't met.
type VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError) ErrorName() string {
	return "VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError"
}

// Error satisfies the builtin error interface
func (e VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVkycQueueMetricsBusinessErrorResponse_QueueMetricsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VkycQueueMetricsBusinessErrorResponse_QueueMetricsRespValidationError{}
