// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/payu/billpayments/billfetch.proto

package billpayments

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// {
// "agentId": "<AgentID>",
// "billerId": "<BillerID>",
// "customerName": "<CustomerName>",
// "customerPhoneNumber": "<phone number>",
// "customerParams": {
// "<paramName>": "<paramValue>"
// },
// "deviceDetails": {
// "INITIATING_CHANNEL": "INT/MOB",
// "IP": "xx.xx.xx.xx",
// "MAC": "xx.xxx.xxx.xx"
// },
// "timeStamp": "<yyyy-MM-dd HH:mm:ss>",
// "refId": "<Reference Id>"
// }
type BillFetchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// AgentId-> provided by PayU
	AgentId string `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	// billerId-> biller identification number provided by payu.
	// (It will be same as received from get biller API)
	BillerId string `protobuf:"bytes,2,opt,name=biller_id,json=billerId,proto3" json:"biller_id,omitempty"`
	// customerName-> customer name of payment account.
	// E.g. in case of wallet, wallet holder name
	CustomerName string `protobuf:"bytes,3,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// customerPhoneNumber-> Registered mobile number of customer
	CustomerPhoneNumber string `protobuf:"bytes,4,opt,name=customer_phone_number,json=customerPhoneNumber,proto3" json:"customer_phone_number,omitempty"`
	// customerParams-> It will contain all fields which are provided by payu in get billers
	// API. These parameters are mandatory for fetching bills.
	CustomerParams map[string]string `protobuf:"bytes,5,rep,name=customer_params,json=customerParams,proto3" json:"customer_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// deviceDetails->
	// Contains device related details.
	// e.g.
	//
	//	{
	//	  "INITIATING_CHANNEL":"INT",
	//	  "IP":"127.0.0.1",
	//	  "MAC":"00-14-22-01-23-45"
	//	}
	DeviceDetails *DeviceDetails `protobuf:"bytes,6,opt,name=device_details,json=deviceDetails,proto3" json:"device_details,omitempty"`
	// timeStamp-> Date on which the bill has been fetched. Yes
	TimeStamp string `protobuf:"bytes,7,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp,omitempty"`
	// refId-> Reference ID to identify the transaction as unique transaction.
	// Length should be between 24 to 35.
	RefId string `protobuf:"bytes,8,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
}

func (x *BillFetchRequest) Reset() {
	*x = BillFetchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillFetchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillFetchRequest) ProtoMessage() {}

func (x *BillFetchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillFetchRequest.ProtoReflect.Descriptor instead.
func (*BillFetchRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_payu_billpayments_billfetch_proto_rawDescGZIP(), []int{0}
}

func (x *BillFetchRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *BillFetchRequest) GetBillerId() string {
	if x != nil {
		return x.BillerId
	}
	return ""
}

func (x *BillFetchRequest) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *BillFetchRequest) GetCustomerPhoneNumber() string {
	if x != nil {
		return x.CustomerPhoneNumber
	}
	return ""
}

func (x *BillFetchRequest) GetCustomerParams() map[string]string {
	if x != nil {
		return x.CustomerParams
	}
	return nil
}

func (x *BillFetchRequest) GetDeviceDetails() *DeviceDetails {
	if x != nil {
		return x.DeviceDetails
	}
	return nil
}

func (x *BillFetchRequest) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *BillFetchRequest) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

// Success:
// {
// "code": 200,
// "status": "SUCCESS",
// "payload": {
// "refId": "<reference ID>",
// "timeStamp": "<yyyy-MM-dd hh:mm:ss>",
// "amount": 200.00,
// "accountHolderName": "<account customer name>",
// "dueDate": "<yyyy-MM-dd>",
// "billDate": "<yyyy-MM-dd>",
// "billerId": "<Biller-Id>",
// "amountDetails": [
// {
// "<paramName>": "<paramNameValue>"
// }
// ],
// "additionalParams": {
// "Key1": "value1",
// "Key2": "value2",
// "Key3": "value3"
// },
// "billNumber": "<billNumber>",
// "billPeriod": "<Period of bill>",
// "approvalRefNum": "<bbps biller approval Ref Number>"
// }
// }
//
// Failure:
// {
// "code": 600,
// "status": "failure",
// "payload": {
// "errors": [
// {
// "reason": "<Error Message>",
// "errorCode": "<Error Code>"
// }
// ],
// "refId": "<referenceID>",
// "type": "fetch",
// "message": "fetch_request_failed",
// "additionalParams": {
// "Key1": "value1",
// "Key2": "value2",
// "Key3": "value3"
// }
// }
// }
type BillFetchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Global response code
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// SUCCESS/FAILURE
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// It will contain the bill fetch transaction data
	Payload *BillFetchResponse_Payload `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *BillFetchResponse) Reset() {
	*x = BillFetchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillFetchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillFetchResponse) ProtoMessage() {}

func (x *BillFetchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillFetchResponse.ProtoReflect.Descriptor instead.
func (*BillFetchResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_payu_billpayments_billfetch_proto_rawDescGZIP(), []int{1}
}

func (x *BillFetchResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BillFetchResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *BillFetchResponse) GetPayload() *BillFetchResponse_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type BillFetchResponse_Payload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Reference Identification Number.
	// The length of refID should be between 34 to 35.
	RefId string `protobuf:"bytes,1,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	// Current time stamp of the server.
	// <yyyy-MM-dd hh:mm:ss>
	//
	// Deprecated: Marked as deprecated in api/vendors/payu/billpayments/billfetch.proto.
	TimeStamp_1 string `protobuf:"bytes,2,opt,name=time_stamp_1,json=requestTimeStamp,proto3" json:"time_stamp_1,omitempty"`
	// Deprecated: Marked as deprecated in api/vendors/payu/billpayments/billfetch.proto.
	TimeStamp_2 string `protobuf:"bytes,3,opt,name=time_stamp_2,json=timeStamp,proto3" json:"time_stamp_2,omitempty"`
	// timestamp can either come as requestTimeStamp or timeStamp
	TimeStamp string `protobuf:"bytes,4,opt,name=time_stamp,json=timeStampConflict,proto3" json:"time_stamp,omitempty"`
	// Amount to be paid
	Amount float64 `protobuf:"fixed64,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// Account holder name on bill.
	AccountHolderName string `protobuf:"bytes,6,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	// Due date of bill
	DueDate string `protobuf:"bytes,7,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	// Billing date of bill
	BillDate string `protobuf:"bytes,8,opt,name=bill_date,json=billDate,proto3" json:"bill_date,omitempty"`
	// Biller Identification ID
	BillerId string `protobuf:"bytes,9,opt,name=biller_id,json=billerId,proto3" json:"biller_id,omitempty"`
	// Total Amount Breakup (It may be available or may not be available).
	// It will contain breakup details of total payable amount.
	// E.g. for education category :-
	// {
	// "Bus fee": "100",
	// "education fees": "100"
	// }
	AmountDetails []*structpb.Struct `protobuf:"bytes,10,rep,name=amount_details,json=amountDetails,proto3" json:"amount_details,omitempty"`
	// It will contain additional information if any available from the biller side for both BBPS and Non
	// BBPS billers.
	// Example:-
	// In case of BBPS biller if there is any additional information like early payment fee,late
	// payment fee,early payment date,late payment due date,dtc code etc.
	// has been mentioned in any biller MDM than additional params will contain those values as in key
	// value pairs.
	// "additionalParams": {
	// "Early Pay Date": "<date1>",
	// "Early Payment Fee": "<fee1>",
	// "Late Payment Date": "<date2>",
	// "Late Payment Fee": "<fee2>",
	// "DTC code": "<code>",
	// "Base Bill Amount": "<amount>"
	// }
	// Similarly in case of Non BBPS biller wants to share any additional information than it will be sent to
	// agent in additional params itself.
	AdditionalParams map[string]string `protobuf:"bytes,11,rep,name=additional_params,json=additionalParams,proto3" json:"additional_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Unique identifier of the bill.
	BillNumber string `protobuf:"bytes,12,opt,name=bill_number,json=billNumber,proto3" json:"bill_number,omitempty"`
	// Billing period of the bill. Most possible values are as mentioned below.
	// 1. ONETIME
	// 2. DAILY
	// 3. WEEKLY
	// 4. BIMONTHLY
	// 5. MONTHLY
	// 6. QUARTERLY
	// 7. HALFYEARLY
	// 8. YEARLY
	// 9. ASPRESENTED
	BillPeriod string `protobuf:"bytes,13,opt,name=bill_period,json=billPeriod,proto3" json:"bill_period,omitempty"`
	// bbps biller approval Ref Number
	ApprovalRefNum string `protobuf:"bytes,14,opt,name=approval_ref_num,json=approvalRefNum,proto3" json:"approval_ref_num,omitempty"`
	// List of errors encountered
	Errors []*Error `protobuf:"bytes,15,rep,name=errors,proto3" json:"errors,omitempty"`
	// Type of Error
	Type string `protobuf:"bytes,16,opt,name=type,proto3" json:"type,omitempty"`
	// Message of error type
	Message string `protobuf:"bytes,17,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *BillFetchResponse_Payload) Reset() {
	*x = BillFetchResponse_Payload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillFetchResponse_Payload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillFetchResponse_Payload) ProtoMessage() {}

func (x *BillFetchResponse_Payload) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillFetchResponse_Payload.ProtoReflect.Descriptor instead.
func (*BillFetchResponse_Payload) Descriptor() ([]byte, []int) {
	return file_api_vendors_payu_billpayments_billfetch_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BillFetchResponse_Payload) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendors/payu/billpayments/billfetch.proto.
func (x *BillFetchResponse_Payload) GetTimeStamp_1() string {
	if x != nil {
		return x.TimeStamp_1
	}
	return ""
}

// Deprecated: Marked as deprecated in api/vendors/payu/billpayments/billfetch.proto.
func (x *BillFetchResponse_Payload) GetTimeStamp_2() string {
	if x != nil {
		return x.TimeStamp_2
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BillFetchResponse_Payload) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetDueDate() string {
	if x != nil {
		return x.DueDate
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetBillDate() string {
	if x != nil {
		return x.BillDate
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetBillerId() string {
	if x != nil {
		return x.BillerId
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetAmountDetails() []*structpb.Struct {
	if x != nil {
		return x.AmountDetails
	}
	return nil
}

func (x *BillFetchResponse_Payload) GetAdditionalParams() map[string]string {
	if x != nil {
		return x.AdditionalParams
	}
	return nil
}

func (x *BillFetchResponse_Payload) GetBillNumber() string {
	if x != nil {
		return x.BillNumber
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetBillPeriod() string {
	if x != nil {
		return x.BillPeriod
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetApprovalRefNum() string {
	if x != nil {
		return x.ApprovalRefNum
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetErrors() []*Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *BillFetchResponse_Payload) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *BillFetchResponse_Payload) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_api_vendors_payu_billpayments_billfetch_proto protoreflect.FileDescriptor

var file_api_vendors_payu_billpayments_billfetch_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x75, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f,
	0x62, 0x69, 0x6c, 0x6c, 0x66, 0x65, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x19, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x75, 0x2e, 0x62, 0x69,
	0x6c, 0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x75, 0x2f, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x03, 0x0a, 0x10, 0x42, 0x69, 0x6c, 0x6c, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x68, 0x0a, 0x0f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x75, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x4f, 0x0a, 0x0e, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x75, 0x2e, 0x62, 0x69, 0x6c, 0x6c,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x1a, 0x41, 0x0a, 0x13, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x99,
	0x07, 0x0a, 0x11, 0x42, 0x69, 0x6c, 0x6c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x4e, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x75,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x42, 0x69,
	0x6c, 0x6c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x1a, 0x87, 0x06, 0x0a, 0x07, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65,
	0x66, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x5f, 0x31, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x23, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x32, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x25, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74,
	0x61, 0x6d, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x62, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x69, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x62, 0x69, 0x6c, 0x6c, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0e, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x0d, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x77, 0x0a, 0x11, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x75, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e,
	0x42, 0x69, 0x6c, 0x6c, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x10, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x69, 0x6c, 0x6c, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x52, 0x65, 0x66, 0x4e, 0x75, 0x6d, 0x12, 0x38, 0x0a,
	0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x75, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52,
	0x06, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x43, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x6c, 0x0a, 0x34, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x75, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x75, 0x2f, 0x62, 0x69, 0x6c, 0x6c,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_payu_billpayments_billfetch_proto_rawDescOnce sync.Once
	file_api_vendors_payu_billpayments_billfetch_proto_rawDescData = file_api_vendors_payu_billpayments_billfetch_proto_rawDesc
)

func file_api_vendors_payu_billpayments_billfetch_proto_rawDescGZIP() []byte {
	file_api_vendors_payu_billpayments_billfetch_proto_rawDescOnce.Do(func() {
		file_api_vendors_payu_billpayments_billfetch_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_payu_billpayments_billfetch_proto_rawDescData)
	})
	return file_api_vendors_payu_billpayments_billfetch_proto_rawDescData
}

var file_api_vendors_payu_billpayments_billfetch_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_vendors_payu_billpayments_billfetch_proto_goTypes = []interface{}{
	(*BillFetchRequest)(nil),          // 0: vendors.payu.billpayments.BillFetchRequest
	(*BillFetchResponse)(nil),         // 1: vendors.payu.billpayments.BillFetchResponse
	nil,                               // 2: vendors.payu.billpayments.BillFetchRequest.CustomerParamsEntry
	(*BillFetchResponse_Payload)(nil), // 3: vendors.payu.billpayments.BillFetchResponse.Payload
	nil,                               // 4: vendors.payu.billpayments.BillFetchResponse.Payload.AdditionalParamsEntry
	(*DeviceDetails)(nil),             // 5: vendors.payu.billpayments.DeviceDetails
	(*structpb.Struct)(nil),           // 6: google.protobuf.Struct
	(*Error)(nil),                     // 7: vendors.payu.billpayments.Error
}
var file_api_vendors_payu_billpayments_billfetch_proto_depIdxs = []int32{
	2, // 0: vendors.payu.billpayments.BillFetchRequest.customer_params:type_name -> vendors.payu.billpayments.BillFetchRequest.CustomerParamsEntry
	5, // 1: vendors.payu.billpayments.BillFetchRequest.device_details:type_name -> vendors.payu.billpayments.DeviceDetails
	3, // 2: vendors.payu.billpayments.BillFetchResponse.payload:type_name -> vendors.payu.billpayments.BillFetchResponse.Payload
	6, // 3: vendors.payu.billpayments.BillFetchResponse.Payload.amount_details:type_name -> google.protobuf.Struct
	4, // 4: vendors.payu.billpayments.BillFetchResponse.Payload.additional_params:type_name -> vendors.payu.billpayments.BillFetchResponse.Payload.AdditionalParamsEntry
	7, // 5: vendors.payu.billpayments.BillFetchResponse.Payload.errors:type_name -> vendors.payu.billpayments.Error
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_vendors_payu_billpayments_billfetch_proto_init() }
func file_api_vendors_payu_billpayments_billfetch_proto_init() {
	if File_api_vendors_payu_billpayments_billfetch_proto != nil {
		return
	}
	file_api_vendors_payu_billpayments_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillFetchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillFetchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_payu_billpayments_billfetch_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillFetchResponse_Payload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_payu_billpayments_billfetch_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_payu_billpayments_billfetch_proto_goTypes,
		DependencyIndexes: file_api_vendors_payu_billpayments_billfetch_proto_depIdxs,
		MessageInfos:      file_api_vendors_payu_billpayments_billfetch_proto_msgTypes,
	}.Build()
	File_api_vendors_payu_billpayments_billfetch_proto = out.File
	file_api_vendors_payu_billpayments_billfetch_proto_rawDesc = nil
	file_api_vendors_payu_billpayments_billfetch_proto_goTypes = nil
	file_api_vendors_payu_billpayments_billfetch_proto_depIdxs = nil
}
