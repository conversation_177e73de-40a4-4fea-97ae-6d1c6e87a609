//go:generate gen_sql -types=Dependent,NomineeDetails,EmployeeDetails,EmployerPurchaseDetail,HealthPlan

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/onsurity/member_invitation_vendor.proto

package onsurity

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MemberInvitationAPIRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Employee                *EmployeeDetails          `protobuf:"bytes,1,opt,name=employee,json=employeeDetails,proto3" json:"employee,omitempty"`
	Dependents              []*Dependent              `protobuf:"bytes,2,rep,name=dependents,proto3" json:"dependents,omitempty"`
	HealthPlan              *HealthPlan               `protobuf:"bytes,3,opt,name=health_plan,json=healthPlan,proto3" json:"health_plan,omitempty"`
	LifePlan                *LifePlan                 `protobuf:"bytes,4,opt,name=life_plan,json=lifePlan,proto3" json:"life_plan,omitempty"`
	EmployerPurchaseDetails []*EmployerPurchaseDetail `protobuf:"bytes,5,rep,name=employer_purchase_details,json=employerPurchaseDetails,proto3" json:"employer_purchase_details,omitempty"`
}

func (x *MemberInvitationAPIRequest) Reset() {
	*x = MemberInvitationAPIRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberInvitationAPIRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberInvitationAPIRequest) ProtoMessage() {}

func (x *MemberInvitationAPIRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberInvitationAPIRequest.ProtoReflect.Descriptor instead.
func (*MemberInvitationAPIRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{0}
}

func (x *MemberInvitationAPIRequest) GetEmployee() *EmployeeDetails {
	if x != nil {
		return x.Employee
	}
	return nil
}

func (x *MemberInvitationAPIRequest) GetDependents() []*Dependent {
	if x != nil {
		return x.Dependents
	}
	return nil
}

func (x *MemberInvitationAPIRequest) GetHealthPlan() *HealthPlan {
	if x != nil {
		return x.HealthPlan
	}
	return nil
}

func (x *MemberInvitationAPIRequest) GetLifePlan() *LifePlan {
	if x != nil {
		return x.LifePlan
	}
	return nil
}

func (x *MemberInvitationAPIRequest) GetEmployerPurchaseDetails() []*EmployerPurchaseDetail {
	if x != nil {
		return x.EmployerPurchaseDetails
	}
	return nil
}

type MemberInvitationApiResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *MemberInvitationApiResponse_Data `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Meta *MemberInvitationApiResponse_Meta `protobuf:"bytes,2,opt,name=meta,proto3" json:"meta,omitempty"`
}

func (x *MemberInvitationApiResponse) Reset() {
	*x = MemberInvitationApiResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberInvitationApiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberInvitationApiResponse) ProtoMessage() {}

func (x *MemberInvitationApiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberInvitationApiResponse.ProtoReflect.Descriptor instead.
func (*MemberInvitationApiResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{1}
}

func (x *MemberInvitationApiResponse) GetData() *MemberInvitationApiResponse_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MemberInvitationApiResponse) GetMeta() *MemberInvitationApiResponse_Meta {
	if x != nil {
		return x.Meta
	}
	return nil
}

type EmployeeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Member’s phone number
	PhoneNumber string `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Member’s email address
	EmailId string `protobuf:"bytes,2,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// Member’s name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Member’s date of birth(DD/MM/YYYY)
	Dob string `protobuf:"bytes,4,opt,name=dob,proto3" json:"dob,omitempty"`
	// Member’s gender
	Gender string `protobuf:"bytes,5,opt,name=gender,proto3" json:"gender,omitempty"`
	// Unique user id for the client provided by Onsurity
	PartnerUserId string `protobuf:"bytes,6,opt,name=partner_user_id,proto3" json:"partner_user_id,omitempty"`
	// Unique employee number or id stored by the client
	EmployeeNo     string          `protobuf:"bytes,7,opt,name=employee_no,proto3" json:"employee_no,omitempty"`
	NomineeDetails *NomineeDetails `protobuf:"bytes,8,opt,name=nominee_details,json=nomineeDetails,proto3" json:"nominee_details,omitempty"`
	// Any government ID
	GovtIdName string `protobuf:"bytes,9,opt,name=govt_id_name,json=govtIdName,proto3" json:"govt_id_name,omitempty"`
	// Government id value
	GovtIdValue string `protobuf:"bytes,10,opt,name=govt_id_value,json=govtIdValue,proto3" json:"govt_id_value,omitempty"`
	// Member’s city
	City string `protobuf:"bytes,11,opt,name=city,proto3" json:"city,omitempty"`
	// Member’s state
	State string `protobuf:"bytes,12,opt,name=state,proto3" json:"state,omitempty"`
	// Member’s address
	AddrLine string `protobuf:"bytes,13,opt,name=addr_line,json=AddrLine,proto3" json:"addr_line,omitempty"`
	// Member’s personal mobile number
	PersonalMobileNumber string `protobuf:"bytes,14,opt,name=personal_mobile_number,json=personalMobileNumber,proto3" json:"personal_mobile_number,omitempty"`
	// Member’s personal email address
	PersonalEmail string `protobuf:"bytes,15,opt,name=personal_email,json=personalEmail,proto3" json:"personal_email,omitempty"`
	// Member’s marital status
	MaritalStatus string `protobuf:"bytes,16,opt,name=marital_status,json=maritalStatus,proto3" json:"marital_status,omitempty"`
	// Member’s employment type
	EmploymentType string `protobuf:"bytes,17,opt,name=employment_type,json=employmentType,proto3" json:"employment_type,omitempty"`
	// Member’s employment status
	EmployeeStatus string `protobuf:"bytes,18,opt,name=employee_status,json=employeeStatus,proto3" json:"employee_status,omitempty"`
	// Member’s joining date
	JoiningDate string `protobuf:"bytes,19,opt,name=joining_date,json=joiningDate,proto3" json:"joining_date,omitempty"`
	// Member’s esic
	Esic string `protobuf:"bytes,21,opt,name=esic,proto3" json:"esic,omitempty"`
	// Member’s department
	Department string `protobuf:"bytes,22,opt,name=department,proto3" json:"department,omitempty"`
	// Member’s designation
	Designation string `protobuf:"bytes,23,opt,name=designation,proto3" json:"designation,omitempty"`
	// Member’s division
	Division string `protobuf:"bytes,24,opt,name=division,proto3" json:"division,omitempty"`
	// Member’s branch
	Branch string `protobuf:"bytes,25,opt,name=branch,proto3" json:"branch,omitempty"`
	// Member’s location
	Location string `protobuf:"bytes,26,opt,name=location,proto3" json:"location,omitempty"`
	// Member’s paygrade
	PayGrade string `protobuf:"bytes,27,opt,name=pay_grade,json=payGrade,proto3" json:"pay_grade,omitempty"`
	// Member’s salary
	AnnualIncome string `protobuf:"bytes,28,opt,name=annual_income,json=annualIncome,proto3" json:"annual_income,omitempty"`
}

func (x *EmployeeDetails) Reset() {
	*x = EmployeeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmployeeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmployeeDetails) ProtoMessage() {}

func (x *EmployeeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmployeeDetails.ProtoReflect.Descriptor instead.
func (*EmployeeDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{2}
}

func (x *EmployeeDetails) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *EmployeeDetails) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *EmployeeDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EmployeeDetails) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *EmployeeDetails) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *EmployeeDetails) GetPartnerUserId() string {
	if x != nil {
		return x.PartnerUserId
	}
	return ""
}

func (x *EmployeeDetails) GetEmployeeNo() string {
	if x != nil {
		return x.EmployeeNo
	}
	return ""
}

func (x *EmployeeDetails) GetNomineeDetails() *NomineeDetails {
	if x != nil {
		return x.NomineeDetails
	}
	return nil
}

func (x *EmployeeDetails) GetGovtIdName() string {
	if x != nil {
		return x.GovtIdName
	}
	return ""
}

func (x *EmployeeDetails) GetGovtIdValue() string {
	if x != nil {
		return x.GovtIdValue
	}
	return ""
}

func (x *EmployeeDetails) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *EmployeeDetails) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *EmployeeDetails) GetAddrLine() string {
	if x != nil {
		return x.AddrLine
	}
	return ""
}

func (x *EmployeeDetails) GetPersonalMobileNumber() string {
	if x != nil {
		return x.PersonalMobileNumber
	}
	return ""
}

func (x *EmployeeDetails) GetPersonalEmail() string {
	if x != nil {
		return x.PersonalEmail
	}
	return ""
}

func (x *EmployeeDetails) GetMaritalStatus() string {
	if x != nil {
		return x.MaritalStatus
	}
	return ""
}

func (x *EmployeeDetails) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *EmployeeDetails) GetEmployeeStatus() string {
	if x != nil {
		return x.EmployeeStatus
	}
	return ""
}

func (x *EmployeeDetails) GetJoiningDate() string {
	if x != nil {
		return x.JoiningDate
	}
	return ""
}

func (x *EmployeeDetails) GetEsic() string {
	if x != nil {
		return x.Esic
	}
	return ""
}

func (x *EmployeeDetails) GetDepartment() string {
	if x != nil {
		return x.Department
	}
	return ""
}

func (x *EmployeeDetails) GetDesignation() string {
	if x != nil {
		return x.Designation
	}
	return ""
}

func (x *EmployeeDetails) GetDivision() string {
	if x != nil {
		return x.Division
	}
	return ""
}

func (x *EmployeeDetails) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

func (x *EmployeeDetails) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *EmployeeDetails) GetPayGrade() string {
	if x != nil {
		return x.PayGrade
	}
	return ""
}

func (x *EmployeeDetails) GetAnnualIncome() string {
	if x != nil {
		return x.AnnualIncome
	}
	return ""
}

type NomineeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Nominee’s name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Nominee’s date of birth (DD/MM/YYYY)
	Dob      string `protobuf:"bytes,2,opt,name=dob,proto3" json:"dob,omitempty"`
	Relation string `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *NomineeDetails) Reset() {
	*x = NomineeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NomineeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NomineeDetails) ProtoMessage() {}

func (x *NomineeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NomineeDetails.ProtoReflect.Descriptor instead.
func (*NomineeDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{3}
}

func (x *NomineeDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NomineeDetails) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *NomineeDetails) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type Dependent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Dependent’s name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Dependent’s gender
	Gender string `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	// Dependent’s date of birth (DD/MM/YYYY)
	Dob string `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
	// Dependent’s relation with primary memeber
	Relation string `protobuf:"bytes,4,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *Dependent) Reset() {
	*x = Dependent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Dependent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dependent) ProtoMessage() {}

func (x *Dependent) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dependent.ProtoReflect.Descriptor instead.
func (*Dependent) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{4}
}

func (x *Dependent) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Dependent) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Dependent) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *Dependent) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type HealthPlan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Healthcare plan name on which we want to add the member
	PlanName string `protobuf:"bytes,1,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`
	// Healthcare policy type on which we want to add the member
	PolicyType string `protobuf:"bytes,2,opt,name=policy_type,json=policyType,proto3" json:"policy_type,omitempty"`
}

func (x *HealthPlan) Reset() {
	*x = HealthPlan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthPlan) ProtoMessage() {}

func (x *HealthPlan) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthPlan.ProtoReflect.Descriptor instead.
func (*HealthPlan) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{5}
}

func (x *HealthPlan) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *HealthPlan) GetPolicyType() string {
	if x != nil {
		return x.PolicyType
	}
	return ""
}

type LifePlan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Lifecare plan name on which we want to add the member
	PlanName string `protobuf:"bytes,1,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`
	// Lifecare policy type on which we want to add the member
	PolicyType string `protobuf:"bytes,2,opt,name=policy_type,json=policyType,proto3" json:"policy_type,omitempty"`
}

func (x *LifePlan) Reset() {
	*x = LifePlan{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LifePlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LifePlan) ProtoMessage() {}

func (x *LifePlan) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LifePlan.ProtoReflect.Descriptor instead.
func (*LifePlan) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{6}
}

func (x *LifePlan) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *LifePlan) GetPolicyType() string {
	if x != nil {
		return x.PolicyType
	}
	return ""
}

type EmployerPurchaseDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Dependent’s name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Dependent’s date of birth (DD/MM/YYYY)
	Dob string `protobuf:"bytes,2,opt,name=dob,proto3" json:"dob,omitempty"`
	// Dependent’s gender
	Gender string `protobuf:"bytes,3,opt,name=gender,proto3" json:"gender,omitempty"`
	// Dependent’s relation with primary member
	Relation string `protobuf:"bytes,4,opt,name=relation,proto3" json:"relation,omitempty"`
	// Healthcare plan name on which we want to add the member
	PlanName string `protobuf:"bytes,5,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`
	// Healthcare policy type on which we want to add the member
	PolicyType string `protobuf:"bytes,6,opt,name=policy_type,json=policyType,proto3" json:"policy_type,omitempty"`
}

func (x *EmployerPurchaseDetail) Reset() {
	*x = EmployerPurchaseDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmployerPurchaseDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmployerPurchaseDetail) ProtoMessage() {}

func (x *EmployerPurchaseDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmployerPurchaseDetail.ProtoReflect.Descriptor instead.
func (*EmployerPurchaseDetail) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{7}
}

func (x *EmployerPurchaseDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EmployerPurchaseDetail) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *EmployerPurchaseDetail) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *EmployerPurchaseDetail) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *EmployerPurchaseDetail) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *EmployerPurchaseDetail) GetPolicyType() string {
	if x != nil {
		return x.PolicyType
	}
	return ""
}

type MemberInvitationApiResponse_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *MemberInvitationApiResponse_Data) Reset() {
	*x = MemberInvitationApiResponse_Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberInvitationApiResponse_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberInvitationApiResponse_Data) ProtoMessage() {}

func (x *MemberInvitationApiResponse_Data) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberInvitationApiResponse_Data.ProtoReflect.Descriptor instead.
func (*MemberInvitationApiResponse_Data) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{1, 0}
}

func (x *MemberInvitationApiResponse_Data) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type MemberInvitationApiResponse_Meta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess       bool                                 `protobuf:"varint,1,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	StatusCode      string                               `protobuf:"bytes,2,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	ResponseMessage string                               `protobuf:"bytes,3,opt,name=response_message,json=responseMessage,proto3" json:"response_message,omitempty"`
	DisplayMessage  string                               `protobuf:"bytes,4,opt,name=display_message,json=displayMessage,proto3" json:"display_message,omitempty"`
	Errors          []*MemberInvitationApiResponse_Error `protobuf:"bytes,5,rep,name=errors,json=errors_Deprecated,proto3" json:"errors,omitempty"` //Deprecated
	ErrorsList      *structpb.Value                      `protobuf:"bytes,6,opt,name=errors_list,json=errors,proto3" json:"errors_list,omitempty"`
}

func (x *MemberInvitationApiResponse_Meta) Reset() {
	*x = MemberInvitationApiResponse_Meta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberInvitationApiResponse_Meta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberInvitationApiResponse_Meta) ProtoMessage() {}

func (x *MemberInvitationApiResponse_Meta) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberInvitationApiResponse_Meta.ProtoReflect.Descriptor instead.
func (*MemberInvitationApiResponse_Meta) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{1, 1}
}

func (x *MemberInvitationApiResponse_Meta) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *MemberInvitationApiResponse_Meta) GetStatusCode() string {
	if x != nil {
		return x.StatusCode
	}
	return ""
}

func (x *MemberInvitationApiResponse_Meta) GetResponseMessage() string {
	if x != nil {
		return x.ResponseMessage
	}
	return ""
}

func (x *MemberInvitationApiResponse_Meta) GetDisplayMessage() string {
	if x != nil {
		return x.DisplayMessage
	}
	return ""
}

func (x *MemberInvitationApiResponse_Meta) GetErrors() []*MemberInvitationApiResponse_Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *MemberInvitationApiResponse_Meta) GetErrorsList() *structpb.Value {
	if x != nil {
		return x.ErrorsList
	}
	return nil
}

type MemberInvitationApiResponse_Error struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action  string `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Row     string `protobuf:"bytes,3,opt,name=row,proto3" json:"row,omitempty"`
	Value   string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *MemberInvitationApiResponse_Error) Reset() {
	*x = MemberInvitationApiResponse_Error{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MemberInvitationApiResponse_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemberInvitationApiResponse_Error) ProtoMessage() {}

func (x *MemberInvitationApiResponse_Error) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemberInvitationApiResponse_Error.ProtoReflect.Descriptor instead.
func (*MemberInvitationApiResponse_Error) Descriptor() ([]byte, []int) {
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP(), []int{1, 2}
}

func (x *MemberInvitationApiResponse_Error) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *MemberInvitationApiResponse_Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *MemberInvitationApiResponse_Error) GetRow() string {
	if x != nil {
		return x.Row
	}
	return ""
}

func (x *MemberInvitationApiResponse_Error) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_api_vendors_onsurity_member_invitation_vendor_proto protoreflect.FileDescriptor

var file_api_vendors_onsurity_member_invitation_vendor_proto_rawDesc = []byte{
	0x0a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6f, 0x6e,
	0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f,
	0x6e, 0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfd, 0x02, 0x0a, 0x1a, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x50, 0x49, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x08, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x64, 0x65,
	0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x2e, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x64, 0x65, 0x70,
	0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x37, 0x0a, 0x09, 0x6c, 0x69, 0x66, 0x65, 0x5f, 0x70,
	0x6c, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x4c, 0x69, 0x66,
	0x65, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x08, 0x6c, 0x69, 0x66, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x12,
	0x64, 0x0a, 0x19, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x5f, 0x70, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x17, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xe1, 0x04, 0x0a, 0x1b, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e,
	0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a,
	0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x70, 0x69, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x04, 0x6d, 0x65, 0x74, 0x61, 0x1a, 0x25, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a, 0xa7, 0x02, 0x0a,
	0x04, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x27, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x56, 0x0a, 0x06, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x70, 0x69,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x11,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x5f, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x12, 0x33, 0x0a, 0x0b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x06,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x1a, 0x61, 0x0a, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x72, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x92, 0x07, 0x0a, 0x0f, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x6f,
	0x62, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x61, 0x72,
	0x74, 0x6e, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f,
	0x6e, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x65, 0x65, 0x5f, 0x6e, 0x6f, 0x12, 0x49, 0x0a, 0x0f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0e, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x20, 0x0a, 0x0c, 0x67, 0x6f, 0x76, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x6f, 0x76, 0x74, 0x49, 0x64, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x67, 0x6f, 0x76, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x6f, 0x76, 0x74, 0x49,
	0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x64, 0x72, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x64, 0x64, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x34, 0x0a,
	0x16, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61,
	0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6a, 0x6f, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6a, 0x6f, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x73, 0x69, 0x63, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x65, 0x73, 0x69, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65,
	0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x79, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x6e, 0x6e,
	0x75, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0x52,
	0x0a, 0x0e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x65, 0x0a, 0x09, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x64,
	0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x0a, 0x0a, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x48, 0x0a, 0x08, 0x4c, 0x69, 0x66, 0x65, 0x50, 0x6c, 0x61,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xb0, 0x01, 0x0a, 0x16, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x50, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x6f, 0x62,
	0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6f, 0x6e, 0x73, 0x75, 0x72, 0x69, 0x74, 0x79, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescOnce sync.Once
	file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescData = file_api_vendors_onsurity_member_invitation_vendor_proto_rawDesc
)

func file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescGZIP() []byte {
	file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescOnce.Do(func() {
		file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescData)
	})
	return file_api_vendors_onsurity_member_invitation_vendor_proto_rawDescData
}

var file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_vendors_onsurity_member_invitation_vendor_proto_goTypes = []interface{}{
	(*MemberInvitationAPIRequest)(nil),        // 0: vendors.onsurity.MemberInvitationAPIRequest
	(*MemberInvitationApiResponse)(nil),       // 1: vendors.onsurity.MemberInvitationApiResponse
	(*EmployeeDetails)(nil),                   // 2: vendors.onsurity.EmployeeDetails
	(*NomineeDetails)(nil),                    // 3: vendors.onsurity.NomineeDetails
	(*Dependent)(nil),                         // 4: vendors.onsurity.Dependent
	(*HealthPlan)(nil),                        // 5: vendors.onsurity.HealthPlan
	(*LifePlan)(nil),                          // 6: vendors.onsurity.LifePlan
	(*EmployerPurchaseDetail)(nil),            // 7: vendors.onsurity.EmployerPurchaseDetail
	(*MemberInvitationApiResponse_Data)(nil),  // 8: vendors.onsurity.MemberInvitationApiResponse.Data
	(*MemberInvitationApiResponse_Meta)(nil),  // 9: vendors.onsurity.MemberInvitationApiResponse.Meta
	(*MemberInvitationApiResponse_Error)(nil), // 10: vendors.onsurity.MemberInvitationApiResponse.Error
	(*structpb.Value)(nil),                    // 11: google.protobuf.Value
}
var file_api_vendors_onsurity_member_invitation_vendor_proto_depIdxs = []int32{
	2,  // 0: vendors.onsurity.MemberInvitationAPIRequest.employee:type_name -> vendors.onsurity.EmployeeDetails
	4,  // 1: vendors.onsurity.MemberInvitationAPIRequest.dependents:type_name -> vendors.onsurity.Dependent
	5,  // 2: vendors.onsurity.MemberInvitationAPIRequest.health_plan:type_name -> vendors.onsurity.HealthPlan
	6,  // 3: vendors.onsurity.MemberInvitationAPIRequest.life_plan:type_name -> vendors.onsurity.LifePlan
	7,  // 4: vendors.onsurity.MemberInvitationAPIRequest.employer_purchase_details:type_name -> vendors.onsurity.EmployerPurchaseDetail
	8,  // 5: vendors.onsurity.MemberInvitationApiResponse.data:type_name -> vendors.onsurity.MemberInvitationApiResponse.Data
	9,  // 6: vendors.onsurity.MemberInvitationApiResponse.meta:type_name -> vendors.onsurity.MemberInvitationApiResponse.Meta
	3,  // 7: vendors.onsurity.EmployeeDetails.nominee_details:type_name -> vendors.onsurity.NomineeDetails
	10, // 8: vendors.onsurity.MemberInvitationApiResponse.Meta.errors:type_name -> vendors.onsurity.MemberInvitationApiResponse.Error
	11, // 9: vendors.onsurity.MemberInvitationApiResponse.Meta.errors_list:type_name -> google.protobuf.Value
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_vendors_onsurity_member_invitation_vendor_proto_init() }
func file_api_vendors_onsurity_member_invitation_vendor_proto_init() {
	if File_api_vendors_onsurity_member_invitation_vendor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberInvitationAPIRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberInvitationApiResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmployeeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NomineeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Dependent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthPlan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LifePlan); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmployerPurchaseDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberInvitationApiResponse_Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberInvitationApiResponse_Meta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MemberInvitationApiResponse_Error); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_onsurity_member_invitation_vendor_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_onsurity_member_invitation_vendor_proto_goTypes,
		DependencyIndexes: file_api_vendors_onsurity_member_invitation_vendor_proto_depIdxs,
		MessageInfos:      file_api_vendors_onsurity_member_invitation_vendor_proto_msgTypes,
	}.Build()
	File_api_vendors_onsurity_member_invitation_vendor_proto = out.File
	file_api_vendors_onsurity_member_invitation_vendor_proto_rawDesc = nil
	file_api_vendors_onsurity_member_invitation_vendor_proto_goTypes = nil
	file_api_vendors_onsurity_member_invitation_vendor_proto_depIdxs = nil
}
