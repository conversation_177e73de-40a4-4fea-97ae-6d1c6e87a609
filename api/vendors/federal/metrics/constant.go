package metrics

const (
	CREDIT                      = "CREDIT"
	DEBIT                       = "DEBIT"
	MANDATE                     = "MANDATE"
	FALLBACK                    = "FALLBACK"
	POS                         = "POS"
	VISA_REFUND                 = "VISA_REFUND"
	FOREX                       = "FOREX"
	ATM                         = "ATM"
	ECM                         = "ECM"
	ECM_REV                     = "ECM_REV"
	VMT                         = "VMT"
	P2P                         = "P2P"
	MOBILE_BANKING              = "MO<PERSON>LE_BANKING"
	CHARGE                      = "CHARGE"
	REFUND                      = "REFUND"
	MANUAL_SETTLEMENT           = "MANUAL_SETTLEMENT"
	UTR                         = "UTR"
	REQUEST_ID                  = "REQUEST_ID"
	VPA                         = "VPA"
	PREDICTED_VPA               = "PREDICTED_VPA"
	MERCHANT_NAME               = "MERCHANT_NAME"
	MERCHANT_INFO               = "MERCHANT_INFO"
	MERCHANT_MCC                = "MERCHANT_MCC"
	MERCHANT_MID                = "MERCHANT_MID"
	MERCHANT_CITY               = "MERCHANT_CITY"
	MERCHANT_COUNTRY            = "MERCHANT_COUNTRY"
	ATM_MACHINE_NAME            = "ATM_MACHINE_NAME"
	OTHER_ACTOR_NAME            = "OTHER_ACTOR_NAME"
	OTHER_ACTOR_ACCOUNT         = "OTHER_ACTOR_ACCOUNT"
	OTHER_ACTOR_PARTIAL_ACCOUNT = "OTHER_ACTOR_PARTIAL_ACCOUNT"
	ALL_PARAMS                  = "ALL_PARAMS"
	IFT_SELL                    = "IFT_SELL"
	IFT_DIV                     = "IFT_DIV"
	IFT_REF                     = "IFT_REF"
	ATM_ADDRESS                 = "ATM_ADDRESS"
	VISA_FUEL_SURCHARGE         = "VISA_FUEL_SURCHARGE"
	VISA_SETTLEMENT_DIFFERENCE  = "VISA_SETTLEMENT_DIFFERENCE"
	FOREX_MARKUP_REVERSAL       = "FOREX_MARKUP_REVERSAL"
)

var (
	upiLabels = []string{
		CREDIT,
		DEBIT,
		MANDATE,
		FALLBACK,
	}
	cardLabels = []string{
		POS,
		VISA_REFUND,
		FOREX,
		ATM,
		ECM,
		ECM_REV,
		VMT,
	}
	neftLabels = []string{
		CREDIT,
		DEBIT,
		P2P,
	}
	rtgsLabels = []string{
		CREDIT,
		DEBIT,
	}
	impsLabels = []string{
		MOBILE_BANKING,
		CREDIT,
		DEBIT,
		CHARGE,
		REFUND,
		MANUAL_SETTLEMENT,
	}
	intraLabels = []string{
		IFT_SELL,
		IFT_DIV,
		IFT_REF,
	}

	upiParams = []string{
		ALL_PARAMS,
		UTR,
		REQUEST_ID,
		VPA,
		PREDICTED_VPA,
	}

	cardParams = []string{
		ALL_PARAMS,
		MERCHANT_NAME,
		UTR,
		MERCHANT_INFO,
		MERCHANT_MCC,
		MERCHANT_MID,
		MERCHANT_CITY,
		MERCHANT_COUNTRY,
		MERCHANT_CITY,
		ATM_MACHINE_NAME,
	}

	neftParams = []string{
		ALL_PARAMS,
		UTR,
		OTHER_ACTOR_NAME,
		REQUEST_ID,
		OTHER_ACTOR_ACCOUNT,
	}
	rtgsParams = []string{
		ALL_PARAMS,
		UTR,
		OTHER_ACTOR_NAME,
		REQUEST_ID,
		OTHER_ACTOR_ACCOUNT,
	}
	impsParams = []string{
		ALL_PARAMS,
		OTHER_ACTOR_NAME,
		OTHER_ACTOR_ACCOUNT,
		OTHER_ACTOR_PARTIAL_ACCOUNT,
		UTR,
		REQUEST_ID,
		MANUAL_SETTLEMENT,
	}
	intraParams = []string{
		ALL_PARAMS,
	}
)
