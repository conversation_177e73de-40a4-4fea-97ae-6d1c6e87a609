// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/mfcentral/mf_central_payload.proto

package mfcentral

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MFCentralPayload with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MFCentralPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MFCentralPayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MFCentralPayloadMultiError, or nil if none found.
func (m *MFCentralPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *MFCentralPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReqId

	// no validation rules for Pan

	// no validation rules for Pekrn

	// no validation rules for Email

	// no validation rules for FromDate

	// no validation rules for ToDate

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MFCentralPayloadValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MFCentralPayloadValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MFCentralPayloadValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInvestorDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MFCentralPayloadValidationError{
					field:  "InvestorDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MFCentralPayloadValidationError{
					field:  "InvestorDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestorDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MFCentralPayloadValidationError{
				field:  "InvestorDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetErrors() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MFCentralPayloadValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MFCentralPayloadValidationError{
						field:  fmt.Sprintf("Errors[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MFCentralPayloadValidationError{
					field:  fmt.Sprintf("Errors[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MFCentralPayloadMultiError(errors)
	}

	return nil
}

// MFCentralPayloadMultiError is an error wrapping multiple validation errors
// returned by MFCentralPayload.ValidateAll() if the designated constraints
// aren't met.
type MFCentralPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MFCentralPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MFCentralPayloadMultiError) AllErrors() []error { return m }

// MFCentralPayloadValidationError is the validation error returned by
// MFCentralPayload.Validate if the designated constraints aren't met.
type MFCentralPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MFCentralPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MFCentralPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MFCentralPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MFCentralPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MFCentralPayloadValidationError) ErrorName() string { return "MFCentralPayloadValidationError" }

// Error satisfies the builtin error interface
func (e MFCentralPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMFCentralPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MFCentralPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MFCentralPayloadValidationError{}

// Validate checks the field values on Data with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Data with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DataMultiError, or nil if none found.
func (m *Data) ValidateAll() error {
	return m.validate(true)
}

func (m *Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDtTransaction() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataValidationError{
						field:  fmt.Sprintf("DtTransaction[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataValidationError{
						field:  fmt.Sprintf("DtTransaction[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataValidationError{
					field:  fmt.Sprintf("DtTransaction[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDtSummary() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataValidationError{
						field:  fmt.Sprintf("DtSummary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataValidationError{
						field:  fmt.Sprintf("DtSummary[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataValidationError{
					field:  fmt.Sprintf("DtSummary[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DataMultiError(errors)
	}

	return nil
}

// DataMultiError is an error wrapping multiple validation errors returned by
// Data.ValidateAll() if the designated constraints aren't met.
type DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataMultiError) AllErrors() []error { return m }

// DataValidationError is the validation error returned by Data.Validate if the
// designated constraints aren't met.
type DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataValidationError) ErrorName() string { return "DataValidationError" }

// Error satisfies the builtin error interface
func (e DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataValidationError{}

// Validate checks the field values on DtTransaction with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DtTransaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DtTransaction with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DtTransactionMultiError, or
// nil if none found.
func (m *DtTransaction) ValidateAll() error {
	return m.validate(true)
}

func (m *DtTransaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Amc

	// no validation rules for AmcName

	// no validation rules for Folio

	// no validation rules for CheckDigit

	// no validation rules for TransactionDate

	// no validation rules for PostedDate

	// no validation rules for Scheme

	// no validation rules for SchemeName

	// no validation rules for TrxnDesc

	// no validation rules for TrxnAmount

	// no validation rules for TrxnUnits

	// no validation rules for SttTax

	// no validation rules for Tax

	// no validation rules for TotalTax

	// no validation rules for TrxnMode

	// no validation rules for PurchasePrice

	// no validation rules for StampDuty

	// no validation rules for TrxnCharge

	// no validation rules for TrxnTypeFlag

	// no validation rules for Isin

	if len(errors) > 0 {
		return DtTransactionMultiError(errors)
	}

	return nil
}

// DtTransactionMultiError is an error wrapping multiple validation errors
// returned by DtTransaction.ValidateAll() if the designated constraints
// aren't met.
type DtTransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DtTransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DtTransactionMultiError) AllErrors() []error { return m }

// DtTransactionValidationError is the validation error returned by
// DtTransaction.Validate if the designated constraints aren't met.
type DtTransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DtTransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DtTransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DtTransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DtTransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DtTransactionValidationError) ErrorName() string { return "DtTransactionValidationError" }

// Error satisfies the builtin error interface
func (e DtTransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDtTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DtTransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DtTransactionValidationError{}

// Validate checks the field values on DtSummary with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DtSummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DtSummary with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DtSummaryMultiError, or nil
// if none found.
func (m *DtSummary) ValidateAll() error {
	return m.validate(true)
}

func (m *DtSummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Amc

	// no validation rules for AmcName

	// no validation rules for Folio

	// no validation rules for Scheme

	// no validation rules for SchemeName

	// no validation rules for KycStatus

	// no validation rules for BrokerCode

	// no validation rules for BrokerName

	// no validation rules for RtaCode

	// no validation rules for DecimalUnits

	// no validation rules for DecimalAmount

	// no validation rules for DecimalNav

	// no validation rules for LastTrxnDate

	// no validation rules for OpeningBal

	// no validation rules for MarketValue

	// no validation rules for Nav

	// no validation rules for ClosingBalance

	// no validation rules for LastNavDate

	// no validation rules for IsDemat

	// no validation rules for AssetType

	// no validation rules for Isin

	// no validation rules for NomineeStatus

	if len(errors) > 0 {
		return DtSummaryMultiError(errors)
	}

	return nil
}

// DtSummaryMultiError is an error wrapping multiple validation errors returned
// by DtSummary.ValidateAll() if the designated constraints aren't met.
type DtSummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DtSummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DtSummaryMultiError) AllErrors() []error { return m }

// DtSummaryValidationError is the validation error returned by
// DtSummary.Validate if the designated constraints aren't met.
type DtSummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DtSummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DtSummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DtSummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DtSummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DtSummaryValidationError) ErrorName() string { return "DtSummaryValidationError" }

// Error satisfies the builtin error interface
func (e DtSummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDtSummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DtSummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DtSummaryValidationError{}

// Validate checks the field values on InvestorDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InvestorDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestorDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestorDetailsMultiError, or nil if none found.
func (m *InvestorDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestorDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestorDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestorDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestorDetailsValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for Mobile

	// no validation rules for InvestorFirstName

	// no validation rules for InvestorMiddleName

	// no validation rules for InvestorLastName

	if len(errors) > 0 {
		return InvestorDetailsMultiError(errors)
	}

	return nil
}

// InvestorDetailsMultiError is an error wrapping multiple validation errors
// returned by InvestorDetails.ValidateAll() if the designated constraints
// aren't met.
type InvestorDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestorDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestorDetailsMultiError) AllErrors() []error { return m }

// InvestorDetailsValidationError is the validation error returned by
// InvestorDetails.Validate if the designated constraints aren't met.
type InvestorDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestorDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestorDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestorDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestorDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestorDetailsValidationError) ErrorName() string { return "InvestorDetailsValidationError" }

// Error satisfies the builtin error interface
func (e InvestorDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestorDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestorDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestorDetailsValidationError{}

// Validate checks the field values on Address with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Address) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Address with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AddressMultiError, or nil if none found.
func (m *Address) ValidateAll() error {
	return m.validate(true)
}

func (m *Address) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressLine_1

	// no validation rules for AddressLine_2

	// no validation rules for AddressLine_3

	// no validation rules for City

	// no validation rules for State

	// no validation rules for District

	// no validation rules for Pincode

	// no validation rules for Country

	if len(errors) > 0 {
		return AddressMultiError(errors)
	}

	return nil
}

// AddressMultiError is an error wrapping multiple validation errors returned
// by Address.ValidateAll() if the designated constraints aren't met.
type AddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressMultiError) AllErrors() []error { return m }

// AddressValidationError is the validation error returned by Address.Validate
// if the designated constraints aren't met.
type AddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressValidationError) ErrorName() string { return "AddressValidationError" }

// Error satisfies the builtin error interface
func (e AddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressValidationError{}
