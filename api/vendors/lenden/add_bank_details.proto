syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";


option go_package = "github.com/epifi/gamma/api/vendors/lenden";
option java_package = "com.github.epifi.gamma.api.vendors.lenden";

message AddBankDetailsRequestPayload {
  string product_id = 1 [json_name = "product_id"];
  string user_id = 2 [json_name = "user_id"];
  string loan_id = 3 [json_name = "loan_id"];
  string holder_name = 4 [json_name = "holder_name"];
  string account_number = 5 [json_name = "account_number"];
  string ifsc = 6 [json_name = "ifsc"];
  string type = 7 [json_name = "type"];
}

message AddBankDetailsRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  AddBankDetailsRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message AddBankDetailsResponseWrapper {
  string message = 1 [json_name = "message"];
  ResponseData response = 2 [json_name = "response"];
  message ResponseData {
    string trace_id = 1 [json_name = "trace_id"];
    string message_code = 2 [json_name = "message_code"];
    string message = 3 [json_name = "message"];
    AddBankDetailsResponse response_data = 4 [json_name = "response_data"];
  }
  message AddBankDetailsResponse {
    // currently empty
    // TODO(mohitswain): raise to Lenden team
  }
}

