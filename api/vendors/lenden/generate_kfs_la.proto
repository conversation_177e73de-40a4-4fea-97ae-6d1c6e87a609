syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";


option go_package = "github.com/epifi/gamma/api/vendors/lenden";
option java_package = "com.github.epifi.gamma.api.vendors.lenden";

// This method generate both KFS and LA
message GenerateKfsLaRequestPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string user_id = 2 [json_name = "user_id"];
  string product_id = 3 [json_name = "product_id"];
  bool is_signed = 4 [json_name = "is_signed"];
}

message GenerateKfsLaRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  GenerateKfsLaRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message GenerateKfsLaResponseWrapper {
  string message = 1 [json_name = "message"];
  ResponseData response = 2 [json_name = "response"];
  message ResponseData {
    string trace_id = 1 [json_name = "trace_id"];
    string message_code = 2 [json_name = "message_code"];
    string message = 3 [json_name = "message"];
    GenerateKfsLaResponse response_data = 4 [json_name = "response_data"];
  }
  message GenerateKfsLaResponse {
    string kfs = 1 [json_name = "kfs"];
    string loan_agreement = 2 [json_name = "loan_agreement"];
  }
}
