syntax = "proto3";

package vendors.karza;

option go_package = "github.com/epifi/gamma/api/vendors/karza";
option java_package = "com.github.epifi.gamma.api.vendors.karza";

// Message to receive the data shared on a callback API provided by the client, Once the ePAN is downloaded
message ProcessEPANEventCallbackResponse {
  // Unique ID passed by clients in sdk invocation (client request id)
  string client_req_id = 1 [json_name = "caseId"];
  string customer_id = 2 [json_name = "customerId"];
  CallbackData callback_data = 3 [json_name = "callbackData"];
  bool success = 4 [json_name = "success"];
  string stage = 5 [json_name = "stage"];
}

message CallbackData {
  ParsedData parsed_data = 1 [json_name = "parsedData"];
  string mobile = 2 [json_name = "mobile"];
  string email = 3 [json_name = "email"];
  Xml xml = 4 [json_name = "xml"];
  Pdf pdf = 5 [json_name = "pdf"];
}

message ParsedData {
  string reference_number = 1 [json_name = "referenceNo"];
  string pan = 2 [json_name = "pan"];
  string name = 3 [json_name = "name"];
  string father_name = 4 [json_name = "fatherName"];
  string dob = 5 [json_name = "dob"];
  string gender = 6 [json_name = "gender"];
  string photo = 7 [json_name = "photo"];
  string signature_image = 8 [json_name = "signatureImage"];
  string qrcode = 9 [json_name = "qrcode"];
}

message Xml {
  string xml_content = 1 [json_name = "xmlContent"];
}

message Pdf {
  string pdf_content = 1 [json_name = "pdfContent"];
}

message EPANStatusRequest {
  string case_id = 1 [json_name = "caseId"];
}

message EPANStatusResponse {
  // Internal Status Code that denotes the status of the API request.
  int64 status_code = 1 [json_name = "statusCode"];
  // Unique id of the API request.
  string request_id = 2 [json_name = "requestId"];
  // Response object for the given inputs.
  Result result = 3 [json_name = "result"];
}

message Result {
  // Response object for activitylog
  ResponseActivityLog data = 1 [json_name = "data"];
  // This field will denote the reason of failure of the status API.
  string reason = 2[json_name = "reason"];
  // This field will denote whether the API call was successfully completed.
  bool success = 3 [json_name = "success"];
}

message ResponseActivityLog {
  // This field will denote the list of stages in ePAN.
  repeated ActivityLog activity_log = 1 [json_name = "activityLog"];
  // This field will denote the transaction ID/customer ID that was passed in the API request.
  string customer_id = 2 [json_name = "customerId"];
  // This field will denote Case id that was passed by client in SDK invocation stage.
  string client_req_id = 3 [json_name = "caseId"];
}

message ActivityLog {
  // This field will denote the timestamp when the particular stage was triggered by the customer.
  string client_timestamp = 1 [json_name = "clientTimestamp"];
  // This field will denote the backend timestamp of a particular stage that was completed.
  string activity_time = 2 [json_name = "activityTime"];
  // This field will denote the stage in the ePAN journey.
  string stage = 3 [json_name = "stage"];
  // This field will denote whether the mentioned stage has been successfully completed by customer.
  bool success = 4 [json_name = "success"];
  // This field will denote the error/success message for each stages of the ePAN journey.
  string message = 5 [json_name = "message"];
  // this field denotes if following activity is latest or not
  bool is_latest_stage = 6 [json_name = "isFinalStage"];
}
