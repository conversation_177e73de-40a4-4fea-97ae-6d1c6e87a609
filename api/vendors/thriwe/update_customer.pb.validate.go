// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/thriwe/update_customer.proto

package thriwe

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerRequestMultiError, or nil if none found.
func (m *UpdateCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FirstName

	// no validation rules for LastName

	// no validation rules for Gender

	// no validation rules for MobileNumber

	// no validation rules for CountryCode

	// no validation rules for Email

	if len(errors) > 0 {
		return UpdateCustomerRequestMultiError(errors)
	}

	return nil
}

// UpdateCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerRequestMultiError) AllErrors() []error { return m }

// UpdateCustomerRequestValidationError is the validation error returned by
// UpdateCustomerRequest.Validate if the designated constraints aren't met.
type UpdateCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerRequestValidationError) ErrorName() string {
	return "UpdateCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerRequestValidationError{}

// Validate checks the field values on UpdateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCustomerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCustomerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCustomerResponseMultiError, or nil if none found.
func (m *UpdateCustomerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCustomerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return UpdateCustomerResponseMultiError(errors)
	}

	return nil
}

// UpdateCustomerResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateCustomerResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateCustomerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCustomerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCustomerResponseMultiError) AllErrors() []error { return m }

// UpdateCustomerResponseValidationError is the validation error returned by
// UpdateCustomerResponse.Validate if the designated constraints aren't met.
type UpdateCustomerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCustomerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCustomerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCustomerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCustomerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCustomerResponseValidationError) ErrorName() string {
	return "UpdateCustomerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCustomerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCustomerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCustomerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCustomerResponseValidationError{}
