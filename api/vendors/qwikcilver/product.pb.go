// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/qwikcilver/product.proto

package qwikcilver

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetProductListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetProductListRequest) Reset() {
	*x = GetProductListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductListRequest) ProtoMessage() {}

func (x *GetProductListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductListRequest.ProtoReflect.Descriptor instead.
func (*GetProductListRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_qwikcilver_product_proto_rawDescGZIP(), []int{0}
}

type GetProductListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Products []*GetProductListResponse_Product `protobuf:"bytes,1,rep,name=products,proto3" json:"products,omitempty"`
	// following fields get populated in case of failure
	// denotes error code
	Code int32 `protobuf:"varint,9,opt,name=code,proto3" json:"code,omitempty"`
	// denotes message in case of error
	Message string `protobuf:"bytes,10,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *GetProductListResponse) Reset() {
	*x = GetProductListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductListResponse) ProtoMessage() {}

func (x *GetProductListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductListResponse.ProtoReflect.Descriptor instead.
func (*GetProductListResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_qwikcilver_product_proto_rawDescGZIP(), []int{1}
}

func (x *GetProductListResponse) GetProducts() []*GetProductListResponse_Product {
	if x != nil {
		return x.Products
	}
	return nil
}

func (x *GetProductListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetProductListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetProductRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetProductRequest) Reset() {
	*x = GetProductRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductRequest) ProtoMessage() {}

func (x *GetProductRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductRequest.ProtoReflect.Descriptor instead.
func (*GetProductRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_qwikcilver_product_proto_rawDescGZIP(), []int{2}
}

type GetProductResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sku  string `protobuf:"bytes,1,opt,name=sku,proto3" json:"sku,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc string `protobuf:"bytes,3,opt,name=desc,json=description,proto3" json:"desc,omitempty"`
	// type of product i.e DIGITAL or PHYSICAL
	Type   string                    `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Price  *GetProductResponse_Price `protobuf:"bytes,5,opt,name=price,proto3" json:"price,omitempty"`
	Images *Images                   `protobuf:"bytes,6,opt,name=images,proto3" json:"images,omitempty"`
	Tnc    *GetProductResponse_Tnc   `protobuf:"bytes,7,opt,name=tnc,proto3" json:"tnc,omitempty"`
	// following fields get populated in case of failure
	// denotes error code
	Code int32 `protobuf:"varint,9,opt,name=code,proto3" json:"code,omitempty"`
	// denotes message in case of error
	Message string `protobuf:"bytes,10,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *GetProductResponse) Reset() {
	*x = GetProductResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductResponse) ProtoMessage() {}

func (x *GetProductResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductResponse.ProtoReflect.Descriptor instead.
func (*GetProductResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_qwikcilver_product_proto_rawDescGZIP(), []int{3}
}

func (x *GetProductResponse) GetSku() string {
	if x != nil {
		return x.Sku
	}
	return ""
}

func (x *GetProductResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetProductResponse) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *GetProductResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetProductResponse) GetPrice() *GetProductResponse_Price {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *GetProductResponse) GetImages() *Images {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *GetProductResponse) GetTnc() *GetProductResponse_Tnc {
	if x != nil {
		return x.Tnc
	}
	return nil
}

func (x *GetProductResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetProductResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetProductListResponse_Product struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sku      string  `protobuf:"bytes,1,opt,name=sku,proto3" json:"sku,omitempty"`
	Name     string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	MinPrice string  `protobuf:"bytes,3,opt,name=min_price,json=minPrice,proto3" json:"min_price,omitempty"`
	MaxPrice string  `protobuf:"bytes,4,opt,name=max_price,json=maxPrice,proto3" json:"max_price,omitempty"`
	Images   *Images `protobuf:"bytes,5,opt,name=images,proto3" json:"images,omitempty"`
}

func (x *GetProductListResponse_Product) Reset() {
	*x = GetProductListResponse_Product{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductListResponse_Product) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductListResponse_Product) ProtoMessage() {}

func (x *GetProductListResponse_Product) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductListResponse_Product.ProtoReflect.Descriptor instead.
func (*GetProductListResponse_Product) Descriptor() ([]byte, []int) {
	return file_api_vendors_qwikcilver_product_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetProductListResponse_Product) GetSku() string {
	if x != nil {
		return x.Sku
	}
	return ""
}

func (x *GetProductListResponse_Product) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetProductListResponse_Product) GetMinPrice() string {
	if x != nil {
		return x.MinPrice
	}
	return ""
}

func (x *GetProductListResponse_Product) GetMaxPrice() string {
	if x != nil {
		return x.MaxPrice
	}
	return ""
}

func (x *GetProductListResponse_Product) GetImages() *Images {
	if x != nil {
		return x.Images
	}
	return nil
}

type GetProductResponse_Price struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type          string   `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Min           string   `protobuf:"bytes,2,opt,name=min,proto3" json:"min,omitempty"`
	Max           string   `protobuf:"bytes,3,opt,name=max,proto3" json:"max,omitempty"`
	Denominations []string `protobuf:"bytes,4,rep,name=denominations,proto3" json:"denominations,omitempty"`
}

func (x *GetProductResponse_Price) Reset() {
	*x = GetProductResponse_Price{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductResponse_Price) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductResponse_Price) ProtoMessage() {}

func (x *GetProductResponse_Price) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductResponse_Price.ProtoReflect.Descriptor instead.
func (*GetProductResponse_Price) Descriptor() ([]byte, []int) {
	return file_api_vendors_qwikcilver_product_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetProductResponse_Price) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *GetProductResponse_Price) GetMin() string {
	if x != nil {
		return x.Min
	}
	return ""
}

func (x *GetProductResponse_Price) GetMax() string {
	if x != nil {
		return x.Max
	}
	return ""
}

func (x *GetProductResponse_Price) GetDenominations() []string {
	if x != nil {
		return x.Denominations
	}
	return nil
}

type GetProductResponse_Tnc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Link    string `protobuf:"bytes,1,opt,name=link,proto3" json:"link,omitempty"`
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *GetProductResponse_Tnc) Reset() {
	*x = GetProductResponse_Tnc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProductResponse_Tnc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProductResponse_Tnc) ProtoMessage() {}

func (x *GetProductResponse_Tnc) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_qwikcilver_product_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProductResponse_Tnc.ProtoReflect.Descriptor instead.
func (*GetProductResponse_Tnc) Descriptor() ([]byte, []int) {
	return file_api_vendors_qwikcilver_product_proto_rawDescGZIP(), []int{3, 1}
}

func (x *GetProductResponse_Tnc) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *GetProductResponse_Tnc) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

var File_api_vendors_qwikcilver_product_proto protoreflect.FileDescriptor

var file_api_vendors_qwikcilver_product_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x71, 0x77,
	0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76,
	0x65, 0x72, 0x2f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x17,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb6, 0x02, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4e, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x71,
	0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x1a, 0x9d, 0x01, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x6b, 0x75, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x6b, 0x75, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x06,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65,
	0x72, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x22, 0x13, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xe9, 0x03, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x6b, 0x75, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x6b, 0x75, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x42, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x71, 0x77, 0x69, 0x6b, 0x63,
	0x69, 0x6c, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x03, 0x74, 0x6e, 0x63,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x54,
	0x6e, 0x63, 0x52, 0x03, 0x74, 0x6e, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x65, 0x0a, 0x05, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x12, 0x24, 0x0a, 0x0d, 0x64, 0x65, 0x6e, 0x6f, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x64,
	0x65, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x33, 0x0a, 0x03,
	0x54, 0x6e, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76,
	0x65, 0x72, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x71, 0x77, 0x69, 0x6b, 0x63, 0x69, 0x6c, 0x76, 0x65,
	0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_qwikcilver_product_proto_rawDescOnce sync.Once
	file_api_vendors_qwikcilver_product_proto_rawDescData = file_api_vendors_qwikcilver_product_proto_rawDesc
)

func file_api_vendors_qwikcilver_product_proto_rawDescGZIP() []byte {
	file_api_vendors_qwikcilver_product_proto_rawDescOnce.Do(func() {
		file_api_vendors_qwikcilver_product_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_qwikcilver_product_proto_rawDescData)
	})
	return file_api_vendors_qwikcilver_product_proto_rawDescData
}

var file_api_vendors_qwikcilver_product_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_vendors_qwikcilver_product_proto_goTypes = []interface{}{
	(*GetProductListRequest)(nil),          // 0: vendors.qwikcilver.GetProductListRequest
	(*GetProductListResponse)(nil),         // 1: vendors.qwikcilver.GetProductListResponse
	(*GetProductRequest)(nil),              // 2: vendors.qwikcilver.GetProductRequest
	(*GetProductResponse)(nil),             // 3: vendors.qwikcilver.GetProductResponse
	(*GetProductListResponse_Product)(nil), // 4: vendors.qwikcilver.GetProductListResponse.Product
	(*GetProductResponse_Price)(nil),       // 5: vendors.qwikcilver.GetProductResponse.Price
	(*GetProductResponse_Tnc)(nil),         // 6: vendors.qwikcilver.GetProductResponse.Tnc
	(*Images)(nil),                         // 7: vendors.qwikcilver.Images
}
var file_api_vendors_qwikcilver_product_proto_depIdxs = []int32{
	4, // 0: vendors.qwikcilver.GetProductListResponse.products:type_name -> vendors.qwikcilver.GetProductListResponse.Product
	5, // 1: vendors.qwikcilver.GetProductResponse.price:type_name -> vendors.qwikcilver.GetProductResponse.Price
	7, // 2: vendors.qwikcilver.GetProductResponse.images:type_name -> vendors.qwikcilver.Images
	6, // 3: vendors.qwikcilver.GetProductResponse.tnc:type_name -> vendors.qwikcilver.GetProductResponse.Tnc
	7, // 4: vendors.qwikcilver.GetProductListResponse.Product.images:type_name -> vendors.qwikcilver.Images
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_vendors_qwikcilver_product_proto_init() }
func file_api_vendors_qwikcilver_product_proto_init() {
	if File_api_vendors_qwikcilver_product_proto != nil {
		return
	}
	file_api_vendors_qwikcilver_image_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_qwikcilver_product_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProductListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_qwikcilver_product_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProductListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_qwikcilver_product_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProductRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_qwikcilver_product_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProductResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_qwikcilver_product_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProductListResponse_Product); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_qwikcilver_product_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProductResponse_Price); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_qwikcilver_product_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProductResponse_Tnc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_qwikcilver_product_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_qwikcilver_product_proto_goTypes,
		DependencyIndexes: file_api_vendors_qwikcilver_product_proto_depIdxs,
		MessageInfos:      file_api_vendors_qwikcilver_product_proto_msgTypes,
	}.Build()
	File_api_vendors_qwikcilver_product_proto = out.File
	file_api_vendors_qwikcilver_product_proto_rawDesc = nil
	file_api_vendors_qwikcilver_product_proto_goTypes = nil
	file_api_vendors_qwikcilver_product_proto_depIdxs = nil
}
