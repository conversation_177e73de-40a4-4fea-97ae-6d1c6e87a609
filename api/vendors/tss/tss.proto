syntax = "proto3";

package vendors.tss;

option go_package = "github.com/epifi/gamma/api/vendors/tss";
option java_package = "com.github.epifi.gamma.api.vendors.tss";

message ScreenCustomerRequest {
  string xml_string = 1 [json_name = "data"];
}

message ScreenCustomerResponse {
  string result_xml = 1 [json_name = "Result"];
  string error_xml = 2 [json_name = "Message"];
}

message ProcessWebhookCallBackRequest{
  string api_token = 1 [json_name = "apiToken"];
  string request_id = 2 [json_name = "requestId"];
  string parent_company = 3 [json_name = "parentCompany"];
  string source_system = 4 [json_name = "sourceSystem"];
  repeated ScreeningCaseRequestTransactions screening_case_request_transactions = 5 [json_name = "screeningCaseRequestTransactions"];
}

message ScreeningCaseRequestTransactions{
  string transaction_id = 1 [json_name = "transactionId"];
  string record_identifier = 2 [json_name = "recordIdentifier"];
  int64 case_id = 3 [json_name = "caseId"];
  string case_url = 4 [json_name = "caseUrl"];
  string final_decision = 5 [json_name = "finalDecision"];
  string pep = 6 [json_name = "pep"];
  string pep_classification = 7 [json_name = "pepClassification"];
  string adverse_media = 8 [json_name = "adverseMedia"];
  string adverse_media_classification = 9 [json_name = "adverseMediaClassification"];
  string reputational_classification = 10 [json_name = "reputationalClassification"];
  string final_remarks = 11 [json_name = "finalRemarks"];
  string approved_on = 12 [json_name = "approvedOn"];
  string approved_by = 13 [json_name = "approvedBy"];
}

message ProcessWebhookCallBackResponse{
  string request_id = 1 [json_name = "requestId"];
  string request_status = 2 [json_name = "requestStatus"];
  repeated ScreeningCaseResponseTransactions screening_case_response_transactions = 3 [json_name = "screeningCaseResponseTransactions"];
}

message ScreeningCaseResponseTransactions {
  string transaction_id = 1         [json_name = "transactionId"];
  string transaction_status = 2     [json_name = "transactionStatus"];
  string transaction_description = 3 [json_name = "transactionDescription"];
}
