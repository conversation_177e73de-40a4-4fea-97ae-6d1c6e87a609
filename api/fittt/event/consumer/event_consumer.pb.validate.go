// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/fittt/event/consumer/event_consumer.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessSalaryDetectedEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessSalaryDetectedEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessSalaryDetectedEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessSalaryDetectedEventResponseMultiError, or nil if none found.
func (m *ProcessSalaryDetectedEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessSalaryDetectedEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSalaryDetectedEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSalaryDetectedEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSalaryDetectedEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessSalaryDetectedEventResponseMultiError(errors)
	}

	return nil
}

// ProcessSalaryDetectedEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessSalaryDetectedEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessSalaryDetectedEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessSalaryDetectedEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessSalaryDetectedEventResponseMultiError) AllErrors() []error { return m }

// ProcessSalaryDetectedEventResponseValidationError is the validation error
// returned by ProcessSalaryDetectedEventResponse.Validate if the designated
// constraints aren't met.
type ProcessSalaryDetectedEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessSalaryDetectedEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessSalaryDetectedEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessSalaryDetectedEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessSalaryDetectedEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessSalaryDetectedEventResponseValidationError) ErrorName() string {
	return "ProcessSalaryDetectedEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessSalaryDetectedEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessSalaryDetectedEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessSalaryDetectedEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessSalaryDetectedEventResponseValidationError{}

// Validate checks the field values on ProcessOrderUpdateEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessOrderUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessOrderUpdateEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessOrderUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessOrderUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessOrderUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessOrderUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessOrderUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessOrderUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessOrderUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessOrderUpdateEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessOrderUpdateEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessOrderUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessOrderUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessOrderUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessOrderUpdateEventResponseValidationError is the validation error
// returned by ProcessOrderUpdateEventResponse.Validate if the designated
// constraints aren't met.
type ProcessOrderUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessOrderUpdateEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessOrderUpdateEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessOrderUpdateEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessOrderUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessOrderUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessOrderUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessOrderUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessOrderUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessOrderUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessOrderUpdateEventResponseValidationError{}

// Validate checks the field values on ProcessSportsMatchUpdateEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessSportsMatchUpdateEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessSportsMatchUpdateEventRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessSportsMatchUpdateEventRequestMultiError, or nil if none found.
func (m *ProcessSportsMatchUpdateEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessSportsMatchUpdateEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MatchId

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RetrySuffix

	if all {
		switch v := interface{}(m.GetExecScope()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "ExecScope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "ExecScope",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecScope()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "ExecScope",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.MatchEvents.(type) {
	case *ProcessSportsMatchUpdateEventRequest_BattingEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBattingEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "BattingEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "BattingEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBattingEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "BattingEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_BowlingEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBowlingEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "BowlingEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "BowlingEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBowlingEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "BowlingEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_MatchEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMatchEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "MatchEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "MatchEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMatchEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "MatchEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_MaidenEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMaidenEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "MaidenEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "MaidenEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMaidenEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "MaidenEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_PartnershipEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPartnershipEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "PartnershipEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "PartnershipEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPartnershipEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "PartnershipEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_TeamMaidenEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTeamMaidenEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "TeamMaidenEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "TeamMaidenEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTeamMaidenEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "TeamMaidenEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_FootballPlayerEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFootballPlayerEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "FootballPlayerEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "FootballPlayerEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFootballPlayerEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "FootballPlayerEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_FootballMatchEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFootballMatchEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "FootballMatchEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "FootballMatchEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFootballMatchEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "FootballMatchEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ProcessSportsMatchUpdateEventRequest_OlympicsMedalEvent:
		if v == nil {
			err := ProcessSportsMatchUpdateEventRequestValidationError{
				field:  "MatchEvents",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOlympicsMedalEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "OlympicsMedalEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessSportsMatchUpdateEventRequestValidationError{
						field:  "OlympicsMedalEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOlympicsMedalEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessSportsMatchUpdateEventRequestValidationError{
					field:  "OlympicsMedalEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProcessSportsMatchUpdateEventRequestMultiError(errors)
	}

	return nil
}

// ProcessSportsMatchUpdateEventRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessSportsMatchUpdateEventRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessSportsMatchUpdateEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessSportsMatchUpdateEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessSportsMatchUpdateEventRequestMultiError) AllErrors() []error { return m }

// ProcessSportsMatchUpdateEventRequestValidationError is the validation error
// returned by ProcessSportsMatchUpdateEventRequest.Validate if the designated
// constraints aren't met.
type ProcessSportsMatchUpdateEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessSportsMatchUpdateEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessSportsMatchUpdateEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessSportsMatchUpdateEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessSportsMatchUpdateEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessSportsMatchUpdateEventRequestValidationError) ErrorName() string {
	return "ProcessSportsMatchUpdateEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessSportsMatchUpdateEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessSportsMatchUpdateEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessSportsMatchUpdateEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessSportsMatchUpdateEventRequestValidationError{}

// Validate checks the field values on ProcessSportsMatchUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessSportsMatchUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessSportsMatchUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessSportsMatchUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessSportsMatchUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessSportsMatchUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessSportsMatchUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessSportsMatchUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessSportsMatchUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessSportsMatchUpdateEventResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessSportsMatchUpdateEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessSportsMatchUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessSportsMatchUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessSportsMatchUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessSportsMatchUpdateEventResponseValidationError is the validation error
// returned by ProcessSportsMatchUpdateEventResponse.Validate if the
// designated constraints aren't met.
type ProcessSportsMatchUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessSportsMatchUpdateEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessSportsMatchUpdateEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessSportsMatchUpdateEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessSportsMatchUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessSportsMatchUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessSportsMatchUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessSportsMatchUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessSportsMatchUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessSportsMatchUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessSportsMatchUpdateEventResponseValidationError{}
