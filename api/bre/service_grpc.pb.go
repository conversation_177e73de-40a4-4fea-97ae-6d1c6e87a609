// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/bre/service.proto

package bre

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Bre_GetLoanDecisioning_FullMethodName            = "/bre.Bre/GetLoanDecisioning"
	Bre_GetLoanPreScreening_FullMethodName           = "/bre.Bre/GetLoanPreScreening"
	Bre_InhouseBreCheckForCC_FullMethodName          = "/bre.Bre/InhouseBreCheckForCC"
	Bre_GetPreBreEligibilityDetails_FullMethodName   = "/bre.Bre/GetPreBreEligibilityDetails"
	Bre_GetFinalBreEligibilityDetails_FullMethodName = "/bre.Bre/GetFinalBreEligibilityDetails"
	Bre_GetPreBreEligibilityOffer_FullMethodName     = "/bre.Bre/GetPreBreEligibilityOffer"
)

// BreClient is the client API for Bre service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BreClient interface {
	// This rpc will be used to get a decision on the loan eligibility for a user and a regulated entity.
	GetLoanDecisioning(ctx context.Context, in *GetLoanDecisioningRequest, opts ...grpc.CallOption) (*GetLoanDecisioningResponse, error)
	// This rpc will be used to get a pre decision on the loan eligibility for a user and a regulated entity
	// and return pre offer bre decision and if Employment needs to be fetched check
	GetLoanPreScreening(ctx context.Context, in *GetLoanPreScreeningRequest, opts ...grpc.CallOption) (*GetLoanPreScreeningResponse, error)
	// rpc for calling inhouse bre api for CC
	InhouseBreCheckForCC(ctx context.Context, in *InhouseBRECheckForCCRequest, opts ...grpc.CallOption) (*InhouseBRECheckForCCResponse, error)
	// This rpc will be used to get a pre decision on the loan eligibility for a user and get applicable vendors.
	GetPreBreEligibilityDetails(ctx context.Context, in *GetPreBreEligibilityDetailsRequest, opts ...grpc.CallOption) (*GetPreBreEligibilityDetailsResponse, error)
	// This rpc will be used to get a decision on the loan eligibility for a user specific to a vendor.
	// This returns the final decision and the offer details OR asks for extra data required for the final decision.
	GetFinalBreEligibilityDetails(ctx context.Context, in *GetFinalBreEligibilityDetailsRequest, opts ...grpc.CallOption) (*GetFinalBreEligibilityDetailsResponse, error)
	// This rpc will be used to get a decision on the pre loan eligibility for a user
	GetPreBreEligibilityOffer(ctx context.Context, in *GetPreBreEligibilityOfferRequest, opts ...grpc.CallOption) (*GetPreBreEligibilityOfferResponse, error)
}

type breClient struct {
	cc grpc.ClientConnInterface
}

func NewBreClient(cc grpc.ClientConnInterface) BreClient {
	return &breClient{cc}
}

func (c *breClient) GetLoanDecisioning(ctx context.Context, in *GetLoanDecisioningRequest, opts ...grpc.CallOption) (*GetLoanDecisioningResponse, error) {
	out := new(GetLoanDecisioningResponse)
	err := c.cc.Invoke(ctx, Bre_GetLoanDecisioning_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *breClient) GetLoanPreScreening(ctx context.Context, in *GetLoanPreScreeningRequest, opts ...grpc.CallOption) (*GetLoanPreScreeningResponse, error) {
	out := new(GetLoanPreScreeningResponse)
	err := c.cc.Invoke(ctx, Bre_GetLoanPreScreening_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *breClient) InhouseBreCheckForCC(ctx context.Context, in *InhouseBRECheckForCCRequest, opts ...grpc.CallOption) (*InhouseBRECheckForCCResponse, error) {
	out := new(InhouseBRECheckForCCResponse)
	err := c.cc.Invoke(ctx, Bre_InhouseBreCheckForCC_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *breClient) GetPreBreEligibilityDetails(ctx context.Context, in *GetPreBreEligibilityDetailsRequest, opts ...grpc.CallOption) (*GetPreBreEligibilityDetailsResponse, error) {
	out := new(GetPreBreEligibilityDetailsResponse)
	err := c.cc.Invoke(ctx, Bre_GetPreBreEligibilityDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *breClient) GetFinalBreEligibilityDetails(ctx context.Context, in *GetFinalBreEligibilityDetailsRequest, opts ...grpc.CallOption) (*GetFinalBreEligibilityDetailsResponse, error) {
	out := new(GetFinalBreEligibilityDetailsResponse)
	err := c.cc.Invoke(ctx, Bre_GetFinalBreEligibilityDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *breClient) GetPreBreEligibilityOffer(ctx context.Context, in *GetPreBreEligibilityOfferRequest, opts ...grpc.CallOption) (*GetPreBreEligibilityOfferResponse, error) {
	out := new(GetPreBreEligibilityOfferResponse)
	err := c.cc.Invoke(ctx, Bre_GetPreBreEligibilityOffer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BreServer is the server API for Bre service.
// All implementations should embed UnimplementedBreServer
// for forward compatibility
type BreServer interface {
	// This rpc will be used to get a decision on the loan eligibility for a user and a regulated entity.
	GetLoanDecisioning(context.Context, *GetLoanDecisioningRequest) (*GetLoanDecisioningResponse, error)
	// This rpc will be used to get a pre decision on the loan eligibility for a user and a regulated entity
	// and return pre offer bre decision and if Employment needs to be fetched check
	GetLoanPreScreening(context.Context, *GetLoanPreScreeningRequest) (*GetLoanPreScreeningResponse, error)
	// rpc for calling inhouse bre api for CC
	InhouseBreCheckForCC(context.Context, *InhouseBRECheckForCCRequest) (*InhouseBRECheckForCCResponse, error)
	// This rpc will be used to get a pre decision on the loan eligibility for a user and get applicable vendors.
	GetPreBreEligibilityDetails(context.Context, *GetPreBreEligibilityDetailsRequest) (*GetPreBreEligibilityDetailsResponse, error)
	// This rpc will be used to get a decision on the loan eligibility for a user specific to a vendor.
	// This returns the final decision and the offer details OR asks for extra data required for the final decision.
	GetFinalBreEligibilityDetails(context.Context, *GetFinalBreEligibilityDetailsRequest) (*GetFinalBreEligibilityDetailsResponse, error)
	// This rpc will be used to get a decision on the pre loan eligibility for a user
	GetPreBreEligibilityOffer(context.Context, *GetPreBreEligibilityOfferRequest) (*GetPreBreEligibilityOfferResponse, error)
}

// UnimplementedBreServer should be embedded to have forward compatible implementations.
type UnimplementedBreServer struct {
}

func (UnimplementedBreServer) GetLoanDecisioning(context.Context, *GetLoanDecisioningRequest) (*GetLoanDecisioningResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanDecisioning not implemented")
}
func (UnimplementedBreServer) GetLoanPreScreening(context.Context, *GetLoanPreScreeningRequest) (*GetLoanPreScreeningResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanPreScreening not implemented")
}
func (UnimplementedBreServer) InhouseBreCheckForCC(context.Context, *InhouseBRECheckForCCRequest) (*InhouseBRECheckForCCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InhouseBreCheckForCC not implemented")
}
func (UnimplementedBreServer) GetPreBreEligibilityDetails(context.Context, *GetPreBreEligibilityDetailsRequest) (*GetPreBreEligibilityDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreBreEligibilityDetails not implemented")
}
func (UnimplementedBreServer) GetFinalBreEligibilityDetails(context.Context, *GetFinalBreEligibilityDetailsRequest) (*GetFinalBreEligibilityDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFinalBreEligibilityDetails not implemented")
}
func (UnimplementedBreServer) GetPreBreEligibilityOffer(context.Context, *GetPreBreEligibilityOfferRequest) (*GetPreBreEligibilityOfferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreBreEligibilityOffer not implemented")
}

// UnsafeBreServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BreServer will
// result in compilation errors.
type UnsafeBreServer interface {
	mustEmbedUnimplementedBreServer()
}

func RegisterBreServer(s grpc.ServiceRegistrar, srv BreServer) {
	s.RegisterService(&Bre_ServiceDesc, srv)
}

func _Bre_GetLoanDecisioning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanDecisioningRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BreServer).GetLoanDecisioning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bre_GetLoanDecisioning_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BreServer).GetLoanDecisioning(ctx, req.(*GetLoanDecisioningRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bre_GetLoanPreScreening_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanPreScreeningRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BreServer).GetLoanPreScreening(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bre_GetLoanPreScreening_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BreServer).GetLoanPreScreening(ctx, req.(*GetLoanPreScreeningRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bre_InhouseBreCheckForCC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InhouseBRECheckForCCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BreServer).InhouseBreCheckForCC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bre_InhouseBreCheckForCC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BreServer).InhouseBreCheckForCC(ctx, req.(*InhouseBRECheckForCCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bre_GetPreBreEligibilityDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPreBreEligibilityDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BreServer).GetPreBreEligibilityDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bre_GetPreBreEligibilityDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BreServer).GetPreBreEligibilityDetails(ctx, req.(*GetPreBreEligibilityDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bre_GetFinalBreEligibilityDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFinalBreEligibilityDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BreServer).GetFinalBreEligibilityDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bre_GetFinalBreEligibilityDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BreServer).GetFinalBreEligibilityDetails(ctx, req.(*GetFinalBreEligibilityDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bre_GetPreBreEligibilityOffer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPreBreEligibilityOfferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BreServer).GetPreBreEligibilityOffer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bre_GetPreBreEligibilityOffer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BreServer).GetPreBreEligibilityOffer(ctx, req.(*GetPreBreEligibilityOfferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Bre_ServiceDesc is the grpc.ServiceDesc for Bre service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Bre_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "bre.Bre",
	HandlerType: (*BreServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLoanDecisioning",
			Handler:    _Bre_GetLoanDecisioning_Handler,
		},
		{
			MethodName: "GetLoanPreScreening",
			Handler:    _Bre_GetLoanPreScreening_Handler,
		},
		{
			MethodName: "InhouseBreCheckForCC",
			Handler:    _Bre_InhouseBreCheckForCC_Handler,
		},
		{
			MethodName: "GetPreBreEligibilityDetails",
			Handler:    _Bre_GetPreBreEligibilityDetails_Handler,
		},
		{
			MethodName: "GetFinalBreEligibilityDetails",
			Handler:    _Bre_GetFinalBreEligibilityDetails_Handler,
		},
		{
			MethodName: "GetPreBreEligibilityOffer",
			Handler:    _Bre_GetPreBreEligibilityOffer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/bre/service.proto",
}
