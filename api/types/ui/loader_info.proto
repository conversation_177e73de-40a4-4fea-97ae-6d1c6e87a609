syntax = "proto3";

package types.ui;

import "api/types/text.proto";
import "api/types/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/types/ui";
option java_package = "com.github.epifi.gamma.api.types.ui";

//  Figma Link:https://www.figma.com/file/g2x9BTVs01uHsAZ6D8g6ss/%E2%9A%A1%EF%B8%8F-Video-KYC---FFF---26-Nov-2020?type=design&node-id=15275-90731&mode=design&t=DdltHVglxCuGshI6-0
message LoaderInfo {
  types.VisualElement icon = 1;
  types.Text title = 2;
  types.Text description = 3;
}
