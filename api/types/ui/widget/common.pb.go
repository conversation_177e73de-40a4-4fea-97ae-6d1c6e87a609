// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/ui/widget/common.proto

package widget

import (
	types "github.com/epifi/gamma/api/types"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ImageTitleSubtitleElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IconImage       *types.Image `protobuf:"bytes,1,opt,name=icon_image,json=iconImage,proto3" json:"icon_image,omitempty"`
	TitleText       *types.Text  `protobuf:"bytes,2,opt,name=title_text,json=titleText,proto3" json:"title_text,omitempty"`
	SubtitleText    *types.Text  `protobuf:"bytes,3,opt,name=subtitle_text,json=subtitleText,proto3" json:"subtitle_text,omitempty"`
	BackgroundColor string       `protobuf:"bytes,4,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
}

func (x *ImageTitleSubtitleElement) Reset() {
	*x = ImageTitleSubtitleElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_ui_widget_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageTitleSubtitleElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageTitleSubtitleElement) ProtoMessage() {}

func (x *ImageTitleSubtitleElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_ui_widget_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageTitleSubtitleElement.ProtoReflect.Descriptor instead.
func (*ImageTitleSubtitleElement) Descriptor() ([]byte, []int) {
	return file_api_types_ui_widget_common_proto_rawDescGZIP(), []int{0}
}

func (x *ImageTitleSubtitleElement) GetIconImage() *types.Image {
	if x != nil {
		return x.IconImage
	}
	return nil
}

func (x *ImageTitleSubtitleElement) GetTitleText() *types.Text {
	if x != nil {
		return x.TitleText
	}
	return nil
}

func (x *ImageTitleSubtitleElement) GetSubtitleText() *types.Text {
	if x != nil {
		return x.SubtitleText
	}
	return nil
}

func (x *ImageTitleSubtitleElement) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

// UI element that contains a check box on the left and text on the right
// example: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14687%3A51369&t=tBBrHa1rgG9HX3Av-0
// text can also contain urls which can be handled inside the text field
type CheckboxItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// string identifier to uniquely identify a checkbox item
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// text to be displayed to the right of the checkbox
	DisplayText *types.Text `protobuf:"bytes,2,opt,name=display_text,json=displayText,proto3" json:"display_text,omitempty"`
	// default mode of the check box
	IsChecked bool `protobuf:"varint,3,opt,name=is_checked,json=isChecked,proto3" json:"is_checked,omitempty"`
	// sub title
	SubTitle *types.Text `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
}

func (x *CheckboxItem) Reset() {
	*x = CheckboxItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_ui_widget_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckboxItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckboxItem) ProtoMessage() {}

func (x *CheckboxItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_ui_widget_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckboxItem.ProtoReflect.Descriptor instead.
func (*CheckboxItem) Descriptor() ([]byte, []int) {
	return file_api_types_ui_widget_common_proto_rawDescGZIP(), []int{1}
}

func (x *CheckboxItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CheckboxItem) GetDisplayText() *types.Text {
	if x != nil {
		return x.DisplayText
	}
	return nil
}

func (x *CheckboxItem) GetIsChecked() bool {
	if x != nil {
		return x.IsChecked
	}
	return false
}

func (x *CheckboxItem) GetSubTitle() *types.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

type VisualElementTitleSubtitleElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VisualElement   *types.VisualElement `protobuf:"bytes,1,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
	TitleText       *types.Text          `protobuf:"bytes,2,opt,name=title_text,json=titleText,proto3" json:"title_text,omitempty"`
	SubtitleText    *types.Text          `protobuf:"bytes,3,opt,name=subtitle_text,json=subtitleText,proto3" json:"subtitle_text,omitempty"`
	BackgroundColor string               `protobuf:"bytes,4,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
}

func (x *VisualElementTitleSubtitleElement) Reset() {
	*x = VisualElementTitleSubtitleElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_ui_widget_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisualElementTitleSubtitleElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisualElementTitleSubtitleElement) ProtoMessage() {}

func (x *VisualElementTitleSubtitleElement) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_ui_widget_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisualElementTitleSubtitleElement.ProtoReflect.Descriptor instead.
func (*VisualElementTitleSubtitleElement) Descriptor() ([]byte, []int) {
	return file_api_types_ui_widget_common_proto_rawDescGZIP(), []int{2}
}

func (x *VisualElementTitleSubtitleElement) GetVisualElement() *types.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

func (x *VisualElementTitleSubtitleElement) GetTitleText() *types.Text {
	if x != nil {
		return x.TitleText
	}
	return nil
}

func (x *VisualElementTitleSubtitleElement) GetSubtitleText() *types.Text {
	if x != nil {
		return x.SubtitleText
	}
	return nil
}

func (x *VisualElementTitleSubtitleElement) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

var File_api_types_ui_widget_common_proto protoreflect.FileDescriptor

var file_api_types_ui_widget_common_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x75, 0x69, 0x2f, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x1a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xd1, 0x01, 0x0a, 0x19, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x53,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2b,
	0x0a, 0x0a, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x52, 0x09, 0x69, 0x63, 0x6f, 0x6e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x0a, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x09, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x30, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x97, 0x01, 0x0a, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x62, 0x6f,
	0x78, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x22, 0xe9,
	0x01, 0x0a, 0x21, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0e, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x2a, 0x0a, 0x0a, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x30, 0x0a,
	0x0d, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x75,
	0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_ui_widget_common_proto_rawDescOnce sync.Once
	file_api_types_ui_widget_common_proto_rawDescData = file_api_types_ui_widget_common_proto_rawDesc
)

func file_api_types_ui_widget_common_proto_rawDescGZIP() []byte {
	file_api_types_ui_widget_common_proto_rawDescOnce.Do(func() {
		file_api_types_ui_widget_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_ui_widget_common_proto_rawDescData)
	})
	return file_api_types_ui_widget_common_proto_rawDescData
}

var file_api_types_ui_widget_common_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_types_ui_widget_common_proto_goTypes = []interface{}{
	(*ImageTitleSubtitleElement)(nil),         // 0: types.ui.widget.ImageTitleSubtitleElement
	(*CheckboxItem)(nil),                      // 1: types.ui.widget.CheckboxItem
	(*VisualElementTitleSubtitleElement)(nil), // 2: types.ui.widget.VisualElementTitleSubtitleElement
	(*types.Image)(nil),                       // 3: types.Image
	(*types.Text)(nil),                        // 4: types.Text
	(*types.VisualElement)(nil),               // 5: types.VisualElement
}
var file_api_types_ui_widget_common_proto_depIdxs = []int32{
	3, // 0: types.ui.widget.ImageTitleSubtitleElement.icon_image:type_name -> types.Image
	4, // 1: types.ui.widget.ImageTitleSubtitleElement.title_text:type_name -> types.Text
	4, // 2: types.ui.widget.ImageTitleSubtitleElement.subtitle_text:type_name -> types.Text
	4, // 3: types.ui.widget.CheckboxItem.display_text:type_name -> types.Text
	4, // 4: types.ui.widget.CheckboxItem.sub_title:type_name -> types.Text
	5, // 5: types.ui.widget.VisualElementTitleSubtitleElement.visual_element:type_name -> types.VisualElement
	4, // 6: types.ui.widget.VisualElementTitleSubtitleElement.title_text:type_name -> types.Text
	4, // 7: types.ui.widget.VisualElementTitleSubtitleElement.subtitle_text:type_name -> types.Text
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_types_ui_widget_common_proto_init() }
func file_api_types_ui_widget_common_proto_init() {
	if File_api_types_ui_widget_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_ui_widget_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageTitleSubtitleElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_ui_widget_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckboxItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_ui_widget_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisualElementTitleSubtitleElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_ui_widget_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_ui_widget_common_proto_goTypes,
		DependencyIndexes: file_api_types_ui_widget_common_proto_depIdxs,
		MessageInfos:      file_api_types_ui_widget_common_proto_msgTypes,
	}.Build()
	File_api_types_ui_widget_common_proto = out.File
	file_api_types_ui_widget_common_proto_rawDesc = nil
	file_api_types_ui_widget_common_proto_goTypes = nil
	file_api_types_ui_widget_common_proto_depIdxs = nil
}
