syntax = "proto3";

package types;

option go_package = "github.com/epifi/gamma/api/types";
option java_package = "com.github.epifi.gamma.api.types";

enum PoliticallyExposedStatus {
  POLITICALLY_EXPOSED_STATUS_UNSPECIFIED = 0;
  POLITICALLY_EXPOSED_STATUS_NOT_APPLICABLE = 1;
  POLITICALLY_EXPOSED_PERSON = 2;
  RELATED_TO_POLITICALLY_EXPOSED_PERSON = 3;
  // this is for other that non individual cases
  POLITICALLY_EXPOSED_STATUS_OTHER = 4;
}
