package types

func GetImageFromUrl(url string) *Image {
	return &Image{
		ImageUrl: url,
	}
}

func (x *Image) WithUrl(url string) *Image {
	if x == nil {
		x = &Image{}
	}
	x.ImageUrl = url
	return x
}

func (x *Image) WithHeight(height int32) *Image {
	x.Height = height
	return x
}
func (x *Image) WithWidth(width int32) *Image {
	x.Width = width
	return x
}

func (x *Image) IsEmpty() bool {
	if x == nil || (x.GetImageDataBase64() == "" && x.GetImageUrl() == "") {
		return true
	}
	return false
}

func (x *Image) IsImageURLEmpty() bool {
	if x == nil || x.GetImageUrl() == "" {
		return true
	}
	return false
}

func (x *Image) IsImageDataEmpty() bool {
	if x == nil || x.GetImageDataBase64() == "" {
		return true
	}
	return false
}
