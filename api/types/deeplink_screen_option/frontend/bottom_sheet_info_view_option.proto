syntax = "proto3";

package types.deeplink_screen_option.frontend;

import "api/frontend/deeplink/deeplink.proto";
import "api/types/deeplink_screen_option/header.proto";
import "api/types/image.proto";
import "api/types/text.proto";
import "api/types/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/frontend";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.frontend";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen Options for BOTTOM_SHEET_INFO_VIEW deeplink
// https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=18258-17222&mode=design&t=N9jnqcV5S6KXdJx8-0
// https://drive.google.com/file/d/1z3KYyCug5leYRLRQsvFX8yzDC20NpHGP/view?usp=sharing
message BottomSheetInfoViewOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  types.Image icon = 2;
  // eg: Tax Saver funds come with a 3-year lock-in period
  types.Text title = 3;
  // eg: As none of your investments have completed this period, you will not be able to withdraw now..
  types.Text sub_title = 4;
  // eg: Ok, got it
  .frontend.deeplink.Cta cta = 5 [deprecated = true];
  // cta list for multiple cta case
  // example: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?node-id=34156%3A50534&mode=dev
  repeated .frontend.deeplink.Cta ctas = 6;
  // example: https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=41459-155822&mode=design&t=Ejp6JFMgp0JEBNoj-4
  types.ui.IconTextComponent badge = 7;
}
