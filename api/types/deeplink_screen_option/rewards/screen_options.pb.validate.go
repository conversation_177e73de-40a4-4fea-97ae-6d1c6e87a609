// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/types/deeplink_screen_option/rewards/screen_options.proto

package rewards

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	types "github.com/epifi/gamma/api/types"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = types.AddressType(0)
)

// Validate checks the field values on RewardOfferDetailsScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardOfferDetailsScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardOfferDetailsScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RewardOfferDetailsScreenOptionsMultiError, or nil if none found.
func (m *RewardOfferDetailsScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOfferDetailsScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardOfferDetailsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardOfferDetailsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardOfferDetailsScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardOfferId

	if len(errors) > 0 {
		return RewardOfferDetailsScreenOptionsMultiError(errors)
	}

	return nil
}

// RewardOfferDetailsScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by RewardOfferDetailsScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type RewardOfferDetailsScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOfferDetailsScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOfferDetailsScreenOptionsMultiError) AllErrors() []error { return m }

// RewardOfferDetailsScreenOptionsValidationError is the validation error
// returned by RewardOfferDetailsScreenOptions.Validate if the designated
// constraints aren't met.
type RewardOfferDetailsScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOfferDetailsScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOfferDetailsScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOfferDetailsScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOfferDetailsScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOfferDetailsScreenOptionsValidationError) ErrorName() string {
	return "RewardOfferDetailsScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOfferDetailsScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOfferDetailsScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOfferDetailsScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOfferDetailsScreenOptionsValidationError{}

// Validate checks the field values on ClaimedRewardDetailsScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ClaimedRewardDetailsScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClaimedRewardDetailsScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ClaimedRewardDetailsScreenOptionsMultiError, or nil if none found.
func (m *ClaimedRewardDetailsScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *ClaimedRewardDetailsScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClaimedRewardDetailsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClaimedRewardDetailsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClaimedRewardDetailsScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RewardId

	if len(errors) > 0 {
		return ClaimedRewardDetailsScreenOptionsMultiError(errors)
	}

	return nil
}

// ClaimedRewardDetailsScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// ClaimedRewardDetailsScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type ClaimedRewardDetailsScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClaimedRewardDetailsScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClaimedRewardDetailsScreenOptionsMultiError) AllErrors() []error { return m }

// ClaimedRewardDetailsScreenOptionsValidationError is the validation error
// returned by ClaimedRewardDetailsScreenOptions.Validate if the designated
// constraints aren't met.
type ClaimedRewardDetailsScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClaimedRewardDetailsScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClaimedRewardDetailsScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClaimedRewardDetailsScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClaimedRewardDetailsScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClaimedRewardDetailsScreenOptionsValidationError) ErrorName() string {
	return "ClaimedRewardDetailsScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e ClaimedRewardDetailsScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClaimedRewardDetailsScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClaimedRewardDetailsScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClaimedRewardDetailsScreenOptionsValidationError{}

// Validate checks the field values on
// CatalogOfferRedemptionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CatalogOfferRedemptionBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CatalogOfferRedemptionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CatalogOfferRedemptionBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *CatalogOfferRedemptionBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CatalogOfferRedemptionBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.BottomSheetData.(type) {
	case *CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionBottomSheetData:
		if v == nil {
			err := CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
				field:  "BottomSheetData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOfferRedemptionBottomSheetData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
						field:  "OfferRedemptionBottomSheetData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
						field:  "OfferRedemptionBottomSheetData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOfferRedemptionBottomSheetData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
					field:  "OfferRedemptionBottomSheetData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionCustomActionBottomSheetData:
		if v == nil {
			err := CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
				field:  "BottomSheetData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOfferRedemptionCustomActionBottomSheetData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
						field:  "OfferRedemptionCustomActionBottomSheetData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
						field:  "OfferRedemptionCustomActionBottomSheetData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOfferRedemptionCustomActionBottomSheetData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{
					field:  "OfferRedemptionCustomActionBottomSheetData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CatalogOfferRedemptionBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// CatalogOfferRedemptionBottomSheetScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// CatalogOfferRedemptionBottomSheetScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type CatalogOfferRedemptionBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CatalogOfferRedemptionBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CatalogOfferRedemptionBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// CatalogOfferRedemptionBottomSheetScreenOptionsValidationError is the
// validation error returned by
// CatalogOfferRedemptionBottomSheetScreenOptions.Validate if the designated
// constraints aren't met.
type CatalogOfferRedemptionBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CatalogOfferRedemptionBottomSheetScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CatalogOfferRedemptionBottomSheetScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CatalogOfferRedemptionBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CatalogOfferRedemptionBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CatalogOfferRedemptionBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "CatalogOfferRedemptionBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CatalogOfferRedemptionBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCatalogOfferRedemptionBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CatalogOfferRedemptionBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on OfferRedemptionBottomSheetData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OfferRedemptionBottomSheetData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferRedemptionBottomSheetData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// OfferRedemptionBottomSheetDataMultiError, or nil if none found.
func (m *OfferRedemptionBottomSheetData) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferRedemptionBottomSheetData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferRedemptionBottomSheetDataValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBanner()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "Banner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "Banner",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBanner()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferRedemptionBottomSheetDataValidationError{
				field:  "Banner",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDescriptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
						field:  fmt.Sprintf("Descriptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
						field:  fmt.Sprintf("Descriptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferRedemptionBottomSheetDataValidationError{
					field:  fmt.Sprintf("Descriptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetNextActionCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "NextActionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "NextActionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferRedemptionBottomSheetDataValidationError{
				field:  "NextActionCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "BackCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "BackCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferRedemptionBottomSheetDataValidationError{
				field:  "BackCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCloseCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "CloseCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferRedemptionBottomSheetDataValidationError{
					field:  "CloseCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloseCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferRedemptionBottomSheetDataValidationError{
				field:  "CloseCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityId

	if len(errors) > 0 {
		return OfferRedemptionBottomSheetDataMultiError(errors)
	}

	return nil
}

// OfferRedemptionBottomSheetDataMultiError is an error wrapping multiple
// validation errors returned by OfferRedemptionBottomSheetData.ValidateAll()
// if the designated constraints aren't met.
type OfferRedemptionBottomSheetDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferRedemptionBottomSheetDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferRedemptionBottomSheetDataMultiError) AllErrors() []error { return m }

// OfferRedemptionBottomSheetDataValidationError is the validation error
// returned by OfferRedemptionBottomSheetData.Validate if the designated
// constraints aren't met.
type OfferRedemptionBottomSheetDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferRedemptionBottomSheetDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferRedemptionBottomSheetDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferRedemptionBottomSheetDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferRedemptionBottomSheetDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferRedemptionBottomSheetDataValidationError) ErrorName() string {
	return "OfferRedemptionBottomSheetDataValidationError"
}

// Error satisfies the builtin error interface
func (e OfferRedemptionBottomSheetDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferRedemptionBottomSheetData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferRedemptionBottomSheetDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferRedemptionBottomSheetDataValidationError{}

// Validate checks the field values on
// OfferRedemptionCustomActionBottomSheetData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferRedemptionCustomActionBottomSheetData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OfferRedemptionCustomActionBottomSheetData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// OfferRedemptionCustomActionBottomSheetDataMultiError, or nil if none found.
func (m *OfferRedemptionCustomActionBottomSheetData) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferRedemptionCustomActionBottomSheetData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCustomAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferRedemptionCustomActionBottomSheetDataValidationError{
					field:  "CustomAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferRedemptionCustomActionBottomSheetDataValidationError{
					field:  "CustomAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferRedemptionCustomActionBottomSheetDataValidationError{
				field:  "CustomAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OfferRedemptionCustomActionBottomSheetDataMultiError(errors)
	}

	return nil
}

// OfferRedemptionCustomActionBottomSheetDataMultiError is an error wrapping
// multiple validation errors returned by
// OfferRedemptionCustomActionBottomSheetData.ValidateAll() if the designated
// constraints aren't met.
type OfferRedemptionCustomActionBottomSheetDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferRedemptionCustomActionBottomSheetDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferRedemptionCustomActionBottomSheetDataMultiError) AllErrors() []error { return m }

// OfferRedemptionCustomActionBottomSheetDataValidationError is the validation
// error returned by OfferRedemptionCustomActionBottomSheetData.Validate if
// the designated constraints aren't met.
type OfferRedemptionCustomActionBottomSheetDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferRedemptionCustomActionBottomSheetDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferRedemptionCustomActionBottomSheetDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferRedemptionCustomActionBottomSheetDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferRedemptionCustomActionBottomSheetDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferRedemptionCustomActionBottomSheetDataValidationError) ErrorName() string {
	return "OfferRedemptionCustomActionBottomSheetDataValidationError"
}

// Error satisfies the builtin error interface
func (e OfferRedemptionCustomActionBottomSheetDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferRedemptionCustomActionBottomSheetData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferRedemptionCustomActionBottomSheetDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferRedemptionCustomActionBottomSheetDataValidationError{}

// Validate checks the field values on
// RewardsAddressSelectionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RewardsAddressSelectionBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RewardsAddressSelectionBottomSheetScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// RewardsAddressSelectionBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *RewardsAddressSelectionBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardsAddressSelectionBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardsAddressSelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardsAddressSelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardsAddressSelectionBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddressBottomSheetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RewardsAddressSelectionBottomSheetScreenOptionsValidationError{
					field:  "AddressBottomSheetData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RewardsAddressSelectionBottomSheetScreenOptionsValidationError{
					field:  "AddressBottomSheetData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressBottomSheetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RewardsAddressSelectionBottomSheetScreenOptionsValidationError{
				field:  "AddressBottomSheetData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RewardsAddressSelectionBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// RewardsAddressSelectionBottomSheetScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// RewardsAddressSelectionBottomSheetScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type RewardsAddressSelectionBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardsAddressSelectionBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardsAddressSelectionBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// RewardsAddressSelectionBottomSheetScreenOptionsValidationError is the
// validation error returned by
// RewardsAddressSelectionBottomSheetScreenOptions.Validate if the designated
// constraints aren't met.
type RewardsAddressSelectionBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardsAddressSelectionBottomSheetScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e RewardsAddressSelectionBottomSheetScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RewardsAddressSelectionBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardsAddressSelectionBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardsAddressSelectionBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "RewardsAddressSelectionBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e RewardsAddressSelectionBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardsAddressSelectionBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardsAddressSelectionBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardsAddressSelectionBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on AddressBottomSheetData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddressBottomSheetData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddressBottomSheetData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddressBottomSheetDataMultiError, or nil if none found.
func (m *AddressBottomSheetData) ValidateAll() error {
	return m.validate(true)
}

func (m *AddressBottomSheetData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConfirmAddressSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetDataValidationError{
					field:  "ConfirmAddressSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetDataValidationError{
					field:  "ConfirmAddressSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmAddressSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetDataValidationError{
				field:  "ConfirmAddressSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConfirmOrderSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetDataValidationError{
					field:  "ConfirmOrderSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetDataValidationError{
					field:  "ConfirmOrderSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmOrderSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetDataValidationError{
				field:  "ConfirmOrderSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityId

	// no validation rules for AddressType

	if len(errors) > 0 {
		return AddressBottomSheetDataMultiError(errors)
	}

	return nil
}

// AddressBottomSheetDataMultiError is an error wrapping multiple validation
// errors returned by AddressBottomSheetData.ValidateAll() if the designated
// constraints aren't met.
type AddressBottomSheetDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressBottomSheetDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressBottomSheetDataMultiError) AllErrors() []error { return m }

// AddressBottomSheetDataValidationError is the validation error returned by
// AddressBottomSheetData.Validate if the designated constraints aren't met.
type AddressBottomSheetDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressBottomSheetDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressBottomSheetDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressBottomSheetDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressBottomSheetDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressBottomSheetDataValidationError) ErrorName() string {
	return "AddressBottomSheetDataValidationError"
}

// Error satisfies the builtin error interface
func (e AddressBottomSheetDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddressBottomSheetData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressBottomSheetDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressBottomSheetDataValidationError{}

// Validate checks the field values on OfferDetailsBottomSheetScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *OfferDetailsBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferDetailsBottomSheetScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// OfferDetailsBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *OfferDetailsBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferDetailsBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "TopSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "TopSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptionsValidationError{
				field:  "TopSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOfferDetailsSection() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("OfferDetailsSection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("OfferDetailsSection[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("OfferDetailsSection[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptionsValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCountdownTimer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "CountdownTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptionsValidationError{
					field:  "CountdownTimer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCountdownTimer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptionsValidationError{
				field:  "CountdownTimer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferId

	if len(errors) > 0 {
		return OfferDetailsBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// OfferDetailsBottomSheetScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// OfferDetailsBottomSheetScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type OfferDetailsBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferDetailsBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferDetailsBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// OfferDetailsBottomSheetScreenOptionsValidationError is the validation error
// returned by OfferDetailsBottomSheetScreenOptions.Validate if the designated
// constraints aren't met.
type OfferDetailsBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferDetailsBottomSheetScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferDetailsBottomSheetScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferDetailsBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferDetailsBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferDetailsBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "OfferDetailsBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e OfferDetailsBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferDetailsBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferDetailsBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferDetailsBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on
// AddressBottomSheetData_ConfirmAddressSection with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddressBottomSheetData_ConfirmAddressSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AddressBottomSheetData_ConfirmAddressSection with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AddressBottomSheetData_ConfirmAddressSectionMultiError, or nil if none found.
func (m *AddressBottomSheetData_ConfirmAddressSection) ValidateAll() error {
	return m.validate(true)
}

func (m *AddressBottomSheetData_ConfirmAddressSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSectionValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSectionValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeliveryAddressTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "DeliveryAddressTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "DeliveryAddressTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeliveryAddressTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSectionValidationError{
				field:  "DeliveryAddressTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEditIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "EditIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "EditIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEditIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSectionValidationError{
				field:  "EditIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextActionCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "NextActionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "NextActionCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextActionCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSectionValidationError{
				field:  "NextActionCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetChangeAddressSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "ChangeAddressSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSectionValidationError{
					field:  "ChangeAddressSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChangeAddressSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSectionValidationError{
				field:  "ChangeAddressSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddressBottomSheetData_ConfirmAddressSectionMultiError(errors)
	}

	return nil
}

// AddressBottomSheetData_ConfirmAddressSectionMultiError is an error wrapping
// multiple validation errors returned by
// AddressBottomSheetData_ConfirmAddressSection.ValidateAll() if the
// designated constraints aren't met.
type AddressBottomSheetData_ConfirmAddressSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressBottomSheetData_ConfirmAddressSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressBottomSheetData_ConfirmAddressSectionMultiError) AllErrors() []error { return m }

// AddressBottomSheetData_ConfirmAddressSectionValidationError is the
// validation error returned by
// AddressBottomSheetData_ConfirmAddressSection.Validate if the designated
// constraints aren't met.
type AddressBottomSheetData_ConfirmAddressSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressBottomSheetData_ConfirmAddressSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressBottomSheetData_ConfirmAddressSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressBottomSheetData_ConfirmAddressSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressBottomSheetData_ConfirmAddressSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressBottomSheetData_ConfirmAddressSectionValidationError) ErrorName() string {
	return "AddressBottomSheetData_ConfirmAddressSectionValidationError"
}

// Error satisfies the builtin error interface
func (e AddressBottomSheetData_ConfirmAddressSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddressBottomSheetData_ConfirmAddressSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressBottomSheetData_ConfirmAddressSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressBottomSheetData_ConfirmAddressSectionValidationError{}

// Validate checks the field values on
// AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionMultiError,
// or nil if none found.
func (m *AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection) ValidateAll() error {
	return m.validate(true)
}

func (m *AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAddress() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
						field:  fmt.Sprintf("Address[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
						field:  fmt.Sprintf("Address[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
					field:  fmt.Sprintf("Address[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAddAddressCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
					field:  "AddAddressCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
					field:  "AddAddressCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddAddressCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{
				field:  "AddAddressCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionMultiError(errors)
	}

	return nil
}

// AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionMultiError
// is an error wrapping multiple validation errors returned by
// AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection.ValidateAll()
// if the designated constraints aren't met.
type AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionMultiError) AllErrors() []error {
	return m
}

// AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError
// is the validation error returned by
// AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection.Validate
// if the designated constraints aren't met.
type AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError) ErrorName() string {
	return "AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError"
}

// Error satisfies the builtin error interface
func (e AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddressBottomSheetData_ConfirmAddressSection_ChangeAddressSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressBottomSheetData_ConfirmAddressSection_ChangeAddressSectionValidationError{}

// Validate checks the field values on
// OfferDetailsBottomSheetScreenOptions_TopSection with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferDetailsBottomSheetScreenOptions_TopSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// OfferDetailsBottomSheetScreenOptions_TopSection with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// OfferDetailsBottomSheetScreenOptions_TopSectionMultiError, or nil if none found.
func (m *OfferDetailsBottomSheetScreenOptions_TopSection) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferDetailsBottomSheetScreenOptions_TopSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOfferTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "OfferTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "OfferTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
				field:  "OfferTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferLogo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "OfferLogo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "OfferLogo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferLogo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
				field:  "OfferLogo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferProductImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "OfferProductImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
					field:  "OfferProductImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferProductImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{
				field:  "OfferProductImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OfferDetailsBottomSheetScreenOptions_TopSectionMultiError(errors)
	}

	return nil
}

// OfferDetailsBottomSheetScreenOptions_TopSectionMultiError is an error
// wrapping multiple validation errors returned by
// OfferDetailsBottomSheetScreenOptions_TopSection.ValidateAll() if the
// designated constraints aren't met.
type OfferDetailsBottomSheetScreenOptions_TopSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferDetailsBottomSheetScreenOptions_TopSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferDetailsBottomSheetScreenOptions_TopSectionMultiError) AllErrors() []error { return m }

// OfferDetailsBottomSheetScreenOptions_TopSectionValidationError is the
// validation error returned by
// OfferDetailsBottomSheetScreenOptions_TopSection.Validate if the designated
// constraints aren't met.
type OfferDetailsBottomSheetScreenOptions_TopSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferDetailsBottomSheetScreenOptions_TopSectionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e OfferDetailsBottomSheetScreenOptions_TopSectionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e OfferDetailsBottomSheetScreenOptions_TopSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferDetailsBottomSheetScreenOptions_TopSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferDetailsBottomSheetScreenOptions_TopSectionValidationError) ErrorName() string {
	return "OfferDetailsBottomSheetScreenOptions_TopSectionValidationError"
}

// Error satisfies the builtin error interface
func (e OfferDetailsBottomSheetScreenOptions_TopSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferDetailsBottomSheetScreenOptions_TopSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferDetailsBottomSheetScreenOptions_TopSectionValidationError{}
