syntax = "proto3";

package types.deeplink_screen_option.tiering;

import "api/types/deeplink_screen_option/header.proto";
import "api/types/text.proto";
import "api/types/ui/widget/widget_themes.proto";
import "api/types/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/tiering";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.tiering";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message EarnedBenefitScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Background color for header
  ui.widget.BackgroundColour header_bg_color = 2;
  // [Icon]
  types.VisualElement header_visual_element = 3;
  // navigation bar title.
  types.Text navigation_bar_title = 4;
  // Screen background color
  ui.widget.BackgroundColour page_background_colour = 5;
  // https://www.figma.com/file/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?type=design&node-id=1540-6430&mode=design&t=eiArYdKdW8YeboHu-4
  string shimmer_header_color = 6;
  // Users current tier for events purpose.
  string tier_identifier = 8;
}

message EarnedBenefitsHistoryScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Screen background color
  ui.widget.BackgroundColour page_background_colour = 2;
  // Users current tier for events purpose.
  string tier_identifier = 8;
}
