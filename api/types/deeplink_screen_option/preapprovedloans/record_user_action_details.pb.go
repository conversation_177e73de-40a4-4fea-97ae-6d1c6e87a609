// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/deeplink_screen_option/preapprovedloans/record_user_action_details.proto

package preapprovedloans

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RecordUserActionDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*RecordUserActionDetails_LamfLinkMfDetails
	Details isRecordUserActionDetails_Details `protobuf_oneof:"details"`
}

func (x *RecordUserActionDetails) Reset() {
	*x = RecordUserActionDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordUserActionDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordUserActionDetails) ProtoMessage() {}

func (x *RecordUserActionDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordUserActionDetails.ProtoReflect.Descriptor instead.
func (*RecordUserActionDetails) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescGZIP(), []int{0}
}

func (m *RecordUserActionDetails) GetDetails() isRecordUserActionDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *RecordUserActionDetails) GetLamfLinkMfDetails() *LamfLinkMfDetails {
	if x, ok := x.GetDetails().(*RecordUserActionDetails_LamfLinkMfDetails); ok {
		return x.LamfLinkMfDetails
	}
	return nil
}

type isRecordUserActionDetails_Details interface {
	isRecordUserActionDetails_Details()
}

type RecordUserActionDetails_LamfLinkMfDetails struct {
	LamfLinkMfDetails *LamfLinkMfDetails `protobuf:"bytes,1,opt,name=lamf_link_mf_details,json=lamfLinkMfDetails,proto3,oneof"`
}

func (*RecordUserActionDetails_LamfLinkMfDetails) isRecordUserActionDetails_Details() {}

type LamfLinkMfDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// link mf screen can have multiple actions.
	// this field will help BE in evaluating which action was taken by the user.
	ActionIdentifier string `protobuf:"bytes,1,opt,name=action_identifier,json=actionIdentifier,proto3" json:"action_identifier,omitempty"`
}

func (x *LamfLinkMfDetails) Reset() {
	*x = LamfLinkMfDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LamfLinkMfDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LamfLinkMfDetails) ProtoMessage() {}

func (x *LamfLinkMfDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LamfLinkMfDetails.ProtoReflect.Descriptor instead.
func (*LamfLinkMfDetails) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescGZIP(), []int{1}
}

func (x *LamfLinkMfDetails) GetActionIdentifier() string {
	if x != nil {
		return x.ActionIdentifier
	}
	return ""
}

var File_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto protoreflect.FileDescriptor

var file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDesc = []byte{
	0x0a, 0x52, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2d, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x73, 0x22, 0x99, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x73, 0x0a, 0x14, 0x6c, 0x61, 0x6d, 0x66, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x6d, 0x66, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x2e, 0x4c, 0x61,
	0x6d, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x4d, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48,
	0x00, 0x52, 0x11, 0x6c, 0x61, 0x6d, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x4d, 0x66, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0x40, 0x0a, 0x11, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x69, 0x6e, 0x6b, 0x4d, 0x66, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x42, 0x94, 0x01, 0x0a, 0x48, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x5a, 0x48,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescOnce sync.Once
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescData = file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDesc
)

func file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescGZIP() []byte {
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescOnce.Do(func() {
		file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescData)
	})
	return file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDescData
}

var file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_goTypes = []interface{}{
	(*RecordUserActionDetails)(nil), // 0: types.deeplink_screen_option.preapprovedloans.RecordUserActionDetails
	(*LamfLinkMfDetails)(nil),       // 1: types.deeplink_screen_option.preapprovedloans.LamfLinkMfDetails
}
var file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_depIdxs = []int32{
	1, // 0: types.deeplink_screen_option.preapprovedloans.RecordUserActionDetails.lamf_link_mf_details:type_name -> types.deeplink_screen_option.preapprovedloans.LamfLinkMfDetails
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() {
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_init()
}
func file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_init() {
	if File_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordUserActionDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LamfLinkMfDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*RecordUserActionDetails_LamfLinkMfDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_goTypes,
		DependencyIndexes: file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_depIdxs,
		MessageInfos:      file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_msgTypes,
	}.Build()
	File_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto = out.File
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_rawDesc = nil
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_goTypes = nil
	file_api_types_deeplink_screen_option_preapprovedloans_record_user_action_details_proto_depIdxs = nil
}
