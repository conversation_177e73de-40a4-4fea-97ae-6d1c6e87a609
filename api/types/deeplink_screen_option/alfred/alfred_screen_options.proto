syntax = "proto3";

package types.deeplink_screen_option.alfred;

import "api/types/deeplink_screen_option/header.proto";
import "google/type/date.proto";
import "api/types/text.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/types/ui/icon_text_component.proto";
import "api/types/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/alfred";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.alfred";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen options for RECORD_CONSENT_PROFILE_UPDATE_API
message RecordConsentProfileUpdateScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string actor_id = 2;
  string client_request_id = 3;
  string consent_request_id = 4;
  string consent_type = 5;
}

// Screen options for SAVE_DOB_FOR_PROFILE_UPDATE
message SaveDobForProfileUpdateScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string client_request_id = 2;
  google.type.Date dob = 3;
}

message RequestChoiceBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  types.Text title = 2;
  repeated RequestChoice request_choices = 3;
}

message RequestChoice {
  types.Text title = 1;
  types.Text subtitle = 2;
  types.Text price = 3;
  repeated frontend.deeplink.Cta ctas = 4;
}

message ProvisionNewRequestScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string request_type = 2;
  // OPTIONAL blob data which can be used by different request handlers to determine some parameters e.g. year in case of tax document generation
  bytes blob = 3;
}

// screen options for REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET
// figma: https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10655&t=EdZ92LttpzP4EDW3-4
message RequestChoiceScrollableBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  types.ui.IconTextComponent title = 2;
  // descriptions for options
  // e.g. CHOOSE A FINANCIAL YEAR
  types.ui.IconTextComponent choices_description = 3;
  repeated RequestChoiceV2 choices = 4;
  types.ui.IconTextComponent footer_info = 5;
  // the cta only defines buttons state (text and color), the action on click is determined via the cta od selected request_choice
  frontend.deeplink.Cta cta = 6;
}

message RequestChoiceV2 {
  types.ui.IconTextComponent choice_text = 1;
  // action to be applied when the submit button on bottom sheet is clicked
  frontend.deeplink.Cta cta = 2;
  // default select state, note that only one option is selected at max
  bool is_selected = 3;
}

// screen options for INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET
// figma: https://www.figma.com/design/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=8517-10700&t=EdZ92LttpzP4EDW3-4
message InfoAcknowledgementBottomSheetScreenOption {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  types.ui.IconTextComponent title = 2;
  types.VisualElement icon = 3;
  // description text below icon
  types.ui.IconTextComponent description = 4;
  repeated frontend.deeplink.Cta ctas = 7;
}

// screen options for ALFRED_REQUEST_CHOICE
// when client receives this deeplink then client will make call to GetRequestChoices rpc
message AlfredRequestChoicesScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // stringified RequestChoiceFilter. Refer: api/alfred/internal/request_choice_filter.proto
  bytes choices_filter = 2;
}
