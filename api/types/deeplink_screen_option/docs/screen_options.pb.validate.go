// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/types/deeplink_screen_option/docs/screen_options.proto

package docs

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UploadFileScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadFileScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadFileScreenOptionsMultiError, or nil if none found.
func (m *UploadFileScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UploadFileScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UploadFileScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UploadFileScreenOptionsValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	// no validation rules for Blob

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Flow

	if all {
		switch v := interface{}(m.GetFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "FileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPasswordInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "PasswordInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "PasswordInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPasswordInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "PasswordInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OpenFilePicker

	if all {
		switch v := interface{}(m.GetLoaderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "LoaderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "LoaderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoaderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "LoaderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPollingLoaderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "PollingLoaderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadFileScreenOptionsValidationError{
					field:  "PollingLoaderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPollingLoaderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadFileScreenOptionsValidationError{
				field:  "PollingLoaderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadFileScreenOptionsMultiError(errors)
	}

	return nil
}

// UploadFileScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by UploadFileScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type UploadFileScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileScreenOptionsMultiError) AllErrors() []error { return m }

// UploadFileScreenOptionsValidationError is the validation error returned by
// UploadFileScreenOptions.Validate if the designated constraints aren't met.
type UploadFileScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileScreenOptionsValidationError) ErrorName() string {
	return "UploadFileScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e UploadFileScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileScreenOptionsValidationError{}

// Validate checks the field values on FileInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileInfoMultiError, or nil
// if none found.
func (m *FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFileIllustration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileInfoValidationError{
					field:  "FileIllustration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileInfoValidationError{
					field:  "FileIllustration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileIllustration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileInfoValidationError{
				field:  "FileIllustration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxSizeKB

	if len(errors) > 0 {
		return FileInfoMultiError(errors)
	}

	return nil
}

// FileInfoMultiError is an error wrapping multiple validation errors returned
// by FileInfo.ValidateAll() if the designated constraints aren't met.
type FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileInfoMultiError) AllErrors() []error { return m }

// FileInfoValidationError is the validation error returned by
// FileInfo.Validate if the designated constraints aren't met.
type FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileInfoValidationError) ErrorName() string { return "FileInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileInfoValidationError{}

// Validate checks the field values on LoaderInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoaderInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoaderInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoaderInfoMultiError, or
// nil if none found.
func (m *LoaderInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LoaderInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderInfoValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderInfoValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderInfoValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderInfoValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderInfoValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoaderInfoValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoaderInfoValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoaderInfoValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoaderInfoMultiError(errors)
	}

	return nil
}

// LoaderInfoMultiError is an error wrapping multiple validation errors
// returned by LoaderInfo.ValidateAll() if the designated constraints aren't met.
type LoaderInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoaderInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoaderInfoMultiError) AllErrors() []error { return m }

// LoaderInfoValidationError is the validation error returned by
// LoaderInfo.Validate if the designated constraints aren't met.
type LoaderInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoaderInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoaderInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoaderInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoaderInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoaderInfoValidationError) ErrorName() string { return "LoaderInfoValidationError" }

// Error satisfies the builtin error interface
func (e LoaderInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoaderInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoaderInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoaderInfoValidationError{}

// Validate checks the field values on PasswordInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PasswordInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PasswordInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PasswordInfoMultiError, or
// nil if none found.
func (m *PasswordInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PasswordInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPwdHint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PasswordInfoValidationError{
					field:  "PwdHint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PasswordInfoValidationError{
					field:  "PwdHint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPwdHint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PasswordInfoValidationError{
				field:  "PwdHint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPwdLabel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PasswordInfoValidationError{
					field:  "PwdLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PasswordInfoValidationError{
					field:  "PwdLabel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPwdLabel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PasswordInfoValidationError{
				field:  "PwdLabel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPwdHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PasswordInfoValidationError{
					field:  "PwdHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PasswordInfoValidationError{
					field:  "PwdHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPwdHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PasswordInfoValidationError{
				field:  "PwdHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PasswordInfoMultiError(errors)
	}

	return nil
}

// PasswordInfoMultiError is an error wrapping multiple validation errors
// returned by PasswordInfo.ValidateAll() if the designated constraints aren't met.
type PasswordInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PasswordInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PasswordInfoMultiError) AllErrors() []error { return m }

// PasswordInfoValidationError is the validation error returned by
// PasswordInfo.Validate if the designated constraints aren't met.
type PasswordInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PasswordInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PasswordInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PasswordInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PasswordInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PasswordInfoValidationError) ErrorName() string { return "PasswordInfoValidationError" }

// Error satisfies the builtin error interface
func (e PasswordInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPasswordInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PasswordInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PasswordInfoValidationError{}
