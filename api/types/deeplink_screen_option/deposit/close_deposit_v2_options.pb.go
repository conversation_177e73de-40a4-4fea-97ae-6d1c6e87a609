// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/deeplink_screen_option/deposit/close_deposit_v2_options.proto

package deposit

import (
	accounts "github.com/epifi/gamma/api/accounts"
	types "github.com/epifi/gamma/api/types"
	deeplink_screen_option "github.com/epifi/gamma/api/types/deeplink_screen_option"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// screen options for DEPOSIT_CLOSE_ACCOUNT_V2 screen
type CloseAccountV2ScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header           *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	DepositAccountId string                                     `protobuf:"bytes,2,opt,name=deposit_account_id,json=depositAccountId,proto3" json:"deposit_account_id,omitempty"`
	DepositType      accounts.Type                              `protobuf:"varint,3,opt,name=deposit_type,json=depositType,proto3,enum=accounts.Type" json:"deposit_type,omitempty"`
	DepositName      string                                     `protobuf:"bytes,4,opt,name=deposit_name,json=depositName,proto3" json:"deposit_name,omitempty"`
	// represent amount to be shown while closing of deposit
	Amount *types.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *CloseAccountV2ScreenOptions) Reset() {
	*x = CloseAccountV2ScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CloseAccountV2ScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloseAccountV2ScreenOptions) ProtoMessage() {}

func (x *CloseAccountV2ScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloseAccountV2ScreenOptions.ProtoReflect.Descriptor instead.
func (*CloseAccountV2ScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescGZIP(), []int{0}
}

func (x *CloseAccountV2ScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CloseAccountV2ScreenOptions) GetDepositAccountId() string {
	if x != nil {
		return x.DepositAccountId
	}
	return ""
}

func (x *CloseAccountV2ScreenOptions) GetDepositType() accounts.Type {
	if x != nil {
		return x.DepositType
	}
	return accounts.Type(0)
}

func (x *CloseAccountV2ScreenOptions) GetDepositName() string {
	if x != nil {
		return x.DepositName
	}
	return ""
}

func (x *CloseAccountV2ScreenOptions) GetAmount() *types.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

var File_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto protoreflect.FileDescriptor

var file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDesc = []byte{
	0x0a, 0x47, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x76, 0x32, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x1a,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x15, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x02, 0x0a, 0x1b, 0x43, 0x6c, 0x6f, 0x73, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x2c, 0x0a, 0x12, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31,
	0x0a, 0x0c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x84, 0x01, 0x0a, 0x3f, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x50, 0x01,
	0x5a, 0x3f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescOnce sync.Once
	file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescData = file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDesc
)

func file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescGZIP() []byte {
	file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescOnce.Do(func() {
		file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescData)
	})
	return file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDescData
}

var file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_goTypes = []interface{}{
	(*CloseAccountV2ScreenOptions)(nil),               // 0: types.deeplink_screen_option.deposit.CloseAccountV2ScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 1: types.deeplink_screen_option.ScreenOptionHeader
	(accounts.Type)(0),                                // 2: accounts.Type
	(*types.Money)(nil),                               // 3: types.Money
}
var file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_depIdxs = []int32{
	1, // 0: types.deeplink_screen_option.deposit.CloseAccountV2ScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	2, // 1: types.deeplink_screen_option.deposit.CloseAccountV2ScreenOptions.deposit_type:type_name -> accounts.Type
	3, // 2: types.deeplink_screen_option.deposit.CloseAccountV2ScreenOptions.amount:type_name -> types.Money
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_init() }
func file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_init() {
	if File_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CloseAccountV2ScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_goTypes,
		DependencyIndexes: file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_depIdxs,
		MessageInfos:      file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_msgTypes,
	}.Build()
	File_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto = out.File
	file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_rawDesc = nil
	file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_goTypes = nil
	file_api_types_deeplink_screen_option_deposit_close_deposit_v2_options_proto_depIdxs = nil
}
