// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/deeplink_screen_option/vkyc/video_kyc_call_screen_options.proto

package vkyc

import (
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/types"
	deeplink_screen_option "github.com/epifi/gamma/api/types/deeplink_screen_option"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoKycCallScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header     *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoaderInfo *VideoKycCallScreenOptions_LoaderInfo      `protobuf:"bytes,2,opt,name=loader_info,json=loaderInfo,proto3" json:"loader_info,omitempty"`
	// ApplicationId is used to fetch applicant details
	ApplicationId string `protobuf:"bytes,3,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	// CallId is used to check the status of call
	CallId string `protobuf:"bytes,4,opt,name=call_id,json=callId,proto3" json:"call_id,omitempty"`
	// ExitDeeplink to redirect user once call is completed
	ExitDeeplink *deeplink.Deeplink   `protobuf:"bytes,5,opt,name=exit_deeplink,json=exitDeeplink,proto3" json:"exit_deeplink,omitempty"`
	BackAction   *deeplink.BackAction `protobuf:"bytes,6,opt,name=back_action,json=backAction,proto3" json:"back_action,omitempty"`
}

func (x *VideoKycCallScreenOptions) Reset() {
	*x = VideoKycCallScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoKycCallScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoKycCallScreenOptions) ProtoMessage() {}

func (x *VideoKycCallScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoKycCallScreenOptions.ProtoReflect.Descriptor instead.
func (*VideoKycCallScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *VideoKycCallScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *VideoKycCallScreenOptions) GetLoaderInfo() *VideoKycCallScreenOptions_LoaderInfo {
	if x != nil {
		return x.LoaderInfo
	}
	return nil
}

func (x *VideoKycCallScreenOptions) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *VideoKycCallScreenOptions) GetCallId() string {
	if x != nil {
		return x.CallId
	}
	return ""
}

func (x *VideoKycCallScreenOptions) GetExitDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.ExitDeeplink
	}
	return nil
}

func (x *VideoKycCallScreenOptions) GetBackAction() *deeplink.BackAction {
	if x != nil {
		return x.BackAction
	}
	return nil
}

// Image/Lottie to be shown on screen
type VideoKycCallScreenOptions_LoaderInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image                 *types.VisualElement `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	Title                 *types.Text          `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Subtitle              *types.Text          `protobuf:"bytes,3,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	ExpectedWaitTimeInSec int32                `protobuf:"varint,4,opt,name=expected_wait_time_in_sec,json=expectedWaitTimeInSec,proto3" json:"expected_wait_time_in_sec,omitempty"`
}

func (x *VideoKycCallScreenOptions_LoaderInfo) Reset() {
	*x = VideoKycCallScreenOptions_LoaderInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VideoKycCallScreenOptions_LoaderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoKycCallScreenOptions_LoaderInfo) ProtoMessage() {}

func (x *VideoKycCallScreenOptions_LoaderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoKycCallScreenOptions_LoaderInfo.ProtoReflect.Descriptor instead.
func (*VideoKycCallScreenOptions_LoaderInfo) Descriptor() ([]byte, []int) {
	return file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescGZIP(), []int{0, 0}
}

func (x *VideoKycCallScreenOptions_LoaderInfo) GetImage() *types.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *VideoKycCallScreenOptions_LoaderInfo) GetTitle() *types.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *VideoKycCallScreenOptions_LoaderInfo) GetSubtitle() *types.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *VideoKycCallScreenOptions_LoaderInfo) GetExpectedWaitTimeInSec() int32 {
	if x != nil {
		return x.ExpectedWaitTimeInSec
	}
	return 0
}

var File_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto protoreflect.FileDescriptor

var file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDesc = []byte{
	0x0a, 0x49, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x6b, 0x79, 0x63, 0x2f, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x6b, 0x79,
	0x63, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x6b, 0x79, 0x63, 0x1a, 0x2d,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x74,
	0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd2, 0x04, 0x0a, 0x19, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x4b, 0x79, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x68, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x6b, 0x79, 0x63, 0x2e, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x4b, 0x79, 0x63, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0d, 0x65,
	0x78, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x0c, 0x65, 0x78, 0x69, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x3e, 0x0a,
	0x0b, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0a, 0x62, 0x61, 0x63, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xbe, 0x01,
	0x0a, 0x0a, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x38, 0x0a, 0x19, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x65,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x57, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x53, 0x65, 0x63, 0x42, 0x7e,
	0x0a, 0x3c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x6b, 0x79, 0x63, 0x50, 0x01,
	0x5a, 0x3c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x6b, 0x79, 0x63, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescOnce sync.Once
	file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescData = file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDesc
)

func file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescGZIP() []byte {
	file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescOnce.Do(func() {
		file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescData)
	})
	return file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDescData
}

var file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_goTypes = []interface{}{
	(*VideoKycCallScreenOptions)(nil),                 // 0: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions
	(*VideoKycCallScreenOptions_LoaderInfo)(nil),      // 1: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.LoaderInfo
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 2: types.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.Deeplink)(nil),                         // 3: frontend.deeplink.Deeplink
	(*deeplink.BackAction)(nil),                       // 4: frontend.deeplink.BackAction
	(*types.VisualElement)(nil),                       // 5: types.VisualElement
	(*types.Text)(nil),                                // 6: types.Text
}
var file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_depIdxs = []int32{
	2, // 0: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.header:type_name -> types.deeplink_screen_option.ScreenOptionHeader
	1, // 1: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.loader_info:type_name -> types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.LoaderInfo
	3, // 2: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.exit_deeplink:type_name -> frontend.deeplink.Deeplink
	4, // 3: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.back_action:type_name -> frontend.deeplink.BackAction
	5, // 4: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.LoaderInfo.image:type_name -> types.VisualElement
	6, // 5: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.LoaderInfo.title:type_name -> types.Text
	6, // 6: types.deeplink_screen_option.vkyc.VideoKycCallScreenOptions.LoaderInfo.subtitle:type_name -> types.Text
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_init() }
func file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_init() {
	if File_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoKycCallScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VideoKycCallScreenOptions_LoaderInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_goTypes,
		DependencyIndexes: file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_depIdxs,
		MessageInfos:      file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_msgTypes,
	}.Build()
	File_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto = out.File
	file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_rawDesc = nil
	file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_goTypes = nil
	file_api_types_deeplink_screen_option_vkyc_video_kyc_call_screen_options_proto_depIdxs = nil
}
