// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/types/deeplink_screen_option/pkg/info_acknowledgement_screen_options.proto

package pkg

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on InfoAcknowledgementV2ScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InfoAcknowledgementV2ScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InfoAcknowledgementV2ScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InfoAcknowledgementV2ScreenOptionsMultiError, or nil if none found.
func (m *InfoAcknowledgementV2ScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *InfoAcknowledgementV2ScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenImageUrl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "ScreenImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "ScreenImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenImageUrl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptionsValidationError{
				field:  "ScreenImageUrl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstructionsImageUrl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "InstructionsImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "InstructionsImageUrl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstructionsImageUrl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptionsValidationError{
				field:  "InstructionsImageUrl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetInstructions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
						field:  fmt.Sprintf("Instructions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
						field:  fmt.Sprintf("Instructions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  fmt.Sprintf("Instructions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConsent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Consent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "Consent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptionsValidationError{
				field:  "Consent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptionsValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InfoAcknowledgementV2ScreenOptionsMultiError(errors)
	}

	return nil
}

// InfoAcknowledgementV2ScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// InfoAcknowledgementV2ScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type InfoAcknowledgementV2ScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InfoAcknowledgementV2ScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InfoAcknowledgementV2ScreenOptionsMultiError) AllErrors() []error { return m }

// InfoAcknowledgementV2ScreenOptionsValidationError is the validation error
// returned by InfoAcknowledgementV2ScreenOptions.Validate if the designated
// constraints aren't met.
type InfoAcknowledgementV2ScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InfoAcknowledgementV2ScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InfoAcknowledgementV2ScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InfoAcknowledgementV2ScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InfoAcknowledgementV2ScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InfoAcknowledgementV2ScreenOptionsValidationError) ErrorName() string {
	return "InfoAcknowledgementV2ScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e InfoAcknowledgementV2ScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInfoAcknowledgementV2ScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InfoAcknowledgementV2ScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InfoAcknowledgementV2ScreenOptionsValidationError{}

// Validate checks the field values on
// InfoAcknowledgementV2ScreenOptions_Instruction with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InfoAcknowledgementV2ScreenOptions_Instruction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InfoAcknowledgementV2ScreenOptions_Instruction with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// InfoAcknowledgementV2ScreenOptions_InstructionMultiError, or nil if none found.
func (m *InfoAcknowledgementV2ScreenOptions_Instruction) ValidateAll() error {
	return m.validate(true)
}

func (m *InfoAcknowledgementV2ScreenOptions_Instruction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptions_InstructionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptions_InstructionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptions_InstructionValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptions_InstructionValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InfoAcknowledgementV2ScreenOptions_InstructionValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InfoAcknowledgementV2ScreenOptions_InstructionValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InfoAcknowledgementV2ScreenOptions_InstructionMultiError(errors)
	}

	return nil
}

// InfoAcknowledgementV2ScreenOptions_InstructionMultiError is an error
// wrapping multiple validation errors returned by
// InfoAcknowledgementV2ScreenOptions_Instruction.ValidateAll() if the
// designated constraints aren't met.
type InfoAcknowledgementV2ScreenOptions_InstructionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InfoAcknowledgementV2ScreenOptions_InstructionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InfoAcknowledgementV2ScreenOptions_InstructionMultiError) AllErrors() []error { return m }

// InfoAcknowledgementV2ScreenOptions_InstructionValidationError is the
// validation error returned by
// InfoAcknowledgementV2ScreenOptions_Instruction.Validate if the designated
// constraints aren't met.
type InfoAcknowledgementV2ScreenOptions_InstructionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InfoAcknowledgementV2ScreenOptions_InstructionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InfoAcknowledgementV2ScreenOptions_InstructionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InfoAcknowledgementV2ScreenOptions_InstructionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InfoAcknowledgementV2ScreenOptions_InstructionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InfoAcknowledgementV2ScreenOptions_InstructionValidationError) ErrorName() string {
	return "InfoAcknowledgementV2ScreenOptions_InstructionValidationError"
}

// Error satisfies the builtin error interface
func (e InfoAcknowledgementV2ScreenOptions_InstructionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInfoAcknowledgementV2ScreenOptions_Instruction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InfoAcknowledgementV2ScreenOptions_InstructionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InfoAcknowledgementV2ScreenOptions_InstructionValidationError{}
