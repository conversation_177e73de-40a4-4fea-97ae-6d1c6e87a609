package types

import "google.golang.org/genproto/googleapis/type/postaladdress"

func (x *PostalAddress) GetBeAddress() *postaladdress.PostalAddress {
	if x == nil {
		return nil
	}
	return &postaladdress.PostalAddress{
		Revision:           x.GetRevision(),
		RegionCode:         x.GetRegionCode(),
		LanguageCode:       x.GetLanguageCode(),
		PostalCode:         x.GetPostalCode(),
		SortingCode:        x.GetSortingCode(),
		AdministrativeArea: x.GetAdministrativeArea(),
		Locality:           x.GetLocality(),
		Sublocality:        x.GetSublocality(),
		AddressLines:       x.GetAddressLines(),
		Recipients:         x.GetRecipients(),
		Organization:       x.GetOrganization(),
	}
}

func GetFromBeAddress(x *postaladdress.PostalAddress) *PostalAddress {
	if x == nil {
		return nil
	}
	return &PostalAddress{
		Revision:           x.GetRevision(),
		RegionCode:         x.GetRegionCode(),
		LanguageCode:       x.GetLanguageCode(),
		PostalCode:         x.GetPostalCode(),
		SortingCode:        x.GetSortingCode(),
		AdministrativeArea: x.GetAdministrativeArea(),
		Locality:           x.GetLocality(),
		Sublocality:        x.GetSublocality(),
		AddressLines:       x.GetAddressLines(),
		Recipients:         x.GetRecipients(),
		Organization:       x.GetOrganization(),
	}
}
