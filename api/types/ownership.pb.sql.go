package types

import (
	"database/sql/driver"
	"fmt"
)

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x Ownership) Value() (driver.Value, error) {
	return x.String(), nil
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *Ownership) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("ownership conversion to string failed, src: %T", val)
	}

	valInt, ok := Ownership_value[val]
	if !ok {
		return fmt.Errorf("unexpected value: %s", val)
	}

	*x = Ownership(valInt)
	return nil
}
