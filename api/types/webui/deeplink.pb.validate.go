// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/types/webui/deeplink.proto

package webui

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Deeplink with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Deeplink) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Deeplink with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeeplinkMultiError, or nil
// if none found.
func (m *Deeplink) ValidateAll() error {
	return m.validate(true)
}

func (m *Deeplink) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScreenName

	if all {
		switch v := interface{}(m.GetScreenData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DeeplinkValidationError{
					field:  "ScreenData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DeeplinkValidationError{
					field:  "ScreenData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DeeplinkValidationError{
				field:  "ScreenData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DeeplinkMultiError(errors)
	}

	return nil
}

// DeeplinkMultiError is an error wrapping multiple validation errors returned
// by Deeplink.ValidateAll() if the designated constraints aren't met.
type DeeplinkMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeeplinkMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeeplinkMultiError) AllErrors() []error { return m }

// DeeplinkValidationError is the validation error returned by
// Deeplink.Validate if the designated constraints aren't met.
type DeeplinkValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeeplinkValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeeplinkValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeeplinkValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeeplinkValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeeplinkValidationError) ErrorName() string { return "DeeplinkValidationError" }

// Error satisfies the builtin error interface
func (e DeeplinkValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeeplink.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeeplinkValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeeplinkValidationError{}

// Validate checks the field values on ScreenData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScreenData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScreenData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScreenDataMultiError, or
// nil if none found.
func (m *ScreenData) ValidateAll() error {
	return m.validate(true)
}

func (m *ScreenData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return ScreenDataMultiError(errors)
	}

	return nil
}

// ScreenDataMultiError is an error wrapping multiple validation errors
// returned by ScreenData.ValidateAll() if the designated constraints aren't met.
type ScreenDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScreenDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScreenDataMultiError) AllErrors() []error { return m }

// ScreenDataValidationError is the validation error returned by
// ScreenData.Validate if the designated constraints aren't met.
type ScreenDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScreenDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScreenDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScreenDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScreenDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScreenDataValidationError) ErrorName() string { return "ScreenDataValidationError" }

// Error satisfies the builtin error interface
func (e ScreenDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScreenData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScreenDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScreenDataValidationError{}
