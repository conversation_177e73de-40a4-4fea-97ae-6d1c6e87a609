// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/types/webui/deeplink.proto

package webui

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ScreenName int32

const (
	ScreenName_SCREEN_NAME_UNSPECIFIED                            ScreenName = 0
	ScreenName_SCREEN_NAME_TRANSACTION_REVIEW_SCREEN              ScreenName = 1
	ScreenName_SCREEN_NAME_RISK_SCREENER_ADDITIONAL_DETAILS_POPUP ScreenName = 2
	ScreenName_SCREEN_NAME_CREDIT_CARD_KYC_DETAILS                ScreenName = 3
	ScreenName_SCREEN_NAME_CREDIT_CARD_DETAILS                    ScreenName = 4
	ScreenName_SCREEN_NAME_CREDIT_CARD_CURRENT_STAGE_DETAILS      ScreenName = 5
	ScreenName_SCREEN_NAME_CREDIT_CARD_NEXT_STAGE_DETAILS         ScreenName = 6
	ScreenName_SCREEN_NAME_RECENT_ACTIVITY_VIEW_DISPUTE_DETAILS   ScreenName = 7
	ScreenName_SCREEN_NAME_RECENT_ACTIVITY_RAISE_DISPUTE          ScreenName = 8
	ScreenName_SCREEN_NAME_RECENT_ACTIVITY_MARK_AGAINST_TICKET    ScreenName = 9
	ScreenName_SCREEN_NAME_RECENT_ACTIVITY_VIEW_DETAILS           ScreenName = 10
	ScreenName_SCREEN_NAME_LOANS_VIEW_DETAILS                     ScreenName = 11
)

// Enum value maps for ScreenName.
var (
	ScreenName_name = map[int32]string{
		0:  "SCREEN_NAME_UNSPECIFIED",
		1:  "SCREEN_NAME_TRANSACTION_REVIEW_SCREEN",
		2:  "SCREEN_NAME_RISK_SCREENER_ADDITIONAL_DETAILS_POPUP",
		3:  "SCREEN_NAME_CREDIT_CARD_KYC_DETAILS",
		4:  "SCREEN_NAME_CREDIT_CARD_DETAILS",
		5:  "SCREEN_NAME_CREDIT_CARD_CURRENT_STAGE_DETAILS",
		6:  "SCREEN_NAME_CREDIT_CARD_NEXT_STAGE_DETAILS",
		7:  "SCREEN_NAME_RECENT_ACTIVITY_VIEW_DISPUTE_DETAILS",
		8:  "SCREEN_NAME_RECENT_ACTIVITY_RAISE_DISPUTE",
		9:  "SCREEN_NAME_RECENT_ACTIVITY_MARK_AGAINST_TICKET",
		10: "SCREEN_NAME_RECENT_ACTIVITY_VIEW_DETAILS",
		11: "SCREEN_NAME_LOANS_VIEW_DETAILS",
	}
	ScreenName_value = map[string]int32{
		"SCREEN_NAME_UNSPECIFIED":                            0,
		"SCREEN_NAME_TRANSACTION_REVIEW_SCREEN":              1,
		"SCREEN_NAME_RISK_SCREENER_ADDITIONAL_DETAILS_POPUP": 2,
		"SCREEN_NAME_CREDIT_CARD_KYC_DETAILS":                3,
		"SCREEN_NAME_CREDIT_CARD_DETAILS":                    4,
		"SCREEN_NAME_CREDIT_CARD_CURRENT_STAGE_DETAILS":      5,
		"SCREEN_NAME_CREDIT_CARD_NEXT_STAGE_DETAILS":         6,
		"SCREEN_NAME_RECENT_ACTIVITY_VIEW_DISPUTE_DETAILS":   7,
		"SCREEN_NAME_RECENT_ACTIVITY_RAISE_DISPUTE":          8,
		"SCREEN_NAME_RECENT_ACTIVITY_MARK_AGAINST_TICKET":    9,
		"SCREEN_NAME_RECENT_ACTIVITY_VIEW_DETAILS":           10,
		"SCREEN_NAME_LOANS_VIEW_DETAILS":                     11,
	}
)

func (x ScreenName) Enum() *ScreenName {
	p := new(ScreenName)
	*p = x
	return p
}

func (x ScreenName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ScreenName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_types_webui_deeplink_proto_enumTypes[0].Descriptor()
}

func (ScreenName) Type() protoreflect.EnumType {
	return &file_api_types_webui_deeplink_proto_enumTypes[0]
}

func (x ScreenName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ScreenName.Descriptor instead.
func (ScreenName) EnumDescriptor() ([]byte, []int) {
	return file_api_types_webui_deeplink_proto_rawDescGZIP(), []int{0}
}

type Deeplink struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the type of action to be used by client UI to render and update the next screen flow
	ScreenName ScreenName `protobuf:"varint,1,opt,name=screen_name,json=screenName,proto3,enum=types.webui.ScreenName" json:"screen_name,omitempty"`
	// additional data corresponding to each screen type to be stored, can be dev actions names to load on next screen,
	// the label of buttons to be displayed on this screen, etc
	ScreenData *ScreenData `protobuf:"bytes,3,opt,name=screen_data,json=screenData,proto3" json:"screen_data,omitempty"`
}

func (x *Deeplink) Reset() {
	*x = Deeplink{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_webui_deeplink_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deeplink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deeplink) ProtoMessage() {}

func (x *Deeplink) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_webui_deeplink_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deeplink.ProtoReflect.Descriptor instead.
func (*Deeplink) Descriptor() ([]byte, []int) {
	return file_api_types_webui_deeplink_proto_rawDescGZIP(), []int{0}
}

func (x *Deeplink) GetScreenName() ScreenName {
	if x != nil {
		return x.ScreenName
	}
	return ScreenName_SCREEN_NAME_UNSPECIFIED
}

func (x *Deeplink) GetScreenData() *ScreenData {
	if x != nil {
		return x.ScreenData
	}
	return nil
}

type ScreenData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// generic object to be used by client for receiving any additional data for the screen
	Data map[string]string `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ScreenData) Reset() {
	*x = ScreenData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_types_webui_deeplink_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScreenData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScreenData) ProtoMessage() {}

func (x *ScreenData) ProtoReflect() protoreflect.Message {
	mi := &file_api_types_webui_deeplink_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScreenData.ProtoReflect.Descriptor instead.
func (*ScreenData) Descriptor() ([]byte, []int) {
	return file_api_types_webui_deeplink_proto_rawDescGZIP(), []int{1}
}

func (x *ScreenData) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_api_types_webui_deeplink_proto protoreflect.FileDescriptor

var file_api_types_webui_deeplink_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x77, 0x65, 0x62, 0x75,
	0x69, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0b, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x22, 0x7e, 0x0a,
	0x08, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x38, 0x0a, 0x0b, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x22, 0x7c, 0x0a,
	0x0a, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x2e, 0x77, 0x65, 0x62, 0x75, 0x69, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0xa9, 0x04, 0x0a, 0x0a,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x10, 0x01, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f,
	0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49,
	0x4c, 0x53, 0x5f, 0x50, 0x4f, 0x50, 0x55, 0x50, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x04, 0x12, 0x31, 0x0a, 0x2d, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x43, 0x55, 0x52, 0x52, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x47,
	0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x06, 0x12, 0x34, 0x0a, 0x30, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f,
	0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x07, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59,
	0x5f, 0x52, 0x41, 0x49, 0x53, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x10, 0x08,
	0x12, 0x33, 0x0a, 0x2f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f,
	0x4d, 0x41, 0x52, 0x4b, 0x5f, 0x41, 0x47, 0x41, 0x49, 0x4e, 0x53, 0x54, 0x5f, 0x54, 0x49, 0x43,
	0x4b, 0x45, 0x54, 0x10, 0x09, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x49, 0x54, 0x59, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x10, 0x0a, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x0b, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x77, 0x65, 0x62, 0x75,
	0x69, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x2f, 0x77, 0x65, 0x62, 0x75, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_types_webui_deeplink_proto_rawDescOnce sync.Once
	file_api_types_webui_deeplink_proto_rawDescData = file_api_types_webui_deeplink_proto_rawDesc
)

func file_api_types_webui_deeplink_proto_rawDescGZIP() []byte {
	file_api_types_webui_deeplink_proto_rawDescOnce.Do(func() {
		file_api_types_webui_deeplink_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_types_webui_deeplink_proto_rawDescData)
	})
	return file_api_types_webui_deeplink_proto_rawDescData
}

var file_api_types_webui_deeplink_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_types_webui_deeplink_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_types_webui_deeplink_proto_goTypes = []interface{}{
	(ScreenName)(0),    // 0: types.webui.ScreenName
	(*Deeplink)(nil),   // 1: types.webui.Deeplink
	(*ScreenData)(nil), // 2: types.webui.ScreenData
	nil,                // 3: types.webui.ScreenData.DataEntry
}
var file_api_types_webui_deeplink_proto_depIdxs = []int32{
	0, // 0: types.webui.Deeplink.screen_name:type_name -> types.webui.ScreenName
	2, // 1: types.webui.Deeplink.screen_data:type_name -> types.webui.ScreenData
	3, // 2: types.webui.ScreenData.data:type_name -> types.webui.ScreenData.DataEntry
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_types_webui_deeplink_proto_init() }
func file_api_types_webui_deeplink_proto_init() {
	if File_api_types_webui_deeplink_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_types_webui_deeplink_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deeplink); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_types_webui_deeplink_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScreenData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_types_webui_deeplink_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_types_webui_deeplink_proto_goTypes,
		DependencyIndexes: file_api_types_webui_deeplink_proto_depIdxs,
		EnumInfos:         file_api_types_webui_deeplink_proto_enumTypes,
		MessageInfos:      file_api_types_webui_deeplink_proto_msgTypes,
	}.Build()
	File_api_types_webui_deeplink_proto = out.File
	file_api_types_webui_deeplink_proto_rawDesc = nil
	file_api_types_webui_deeplink_proto_goTypes = nil
	file_api_types_webui_deeplink_proto_depIdxs = nil
}
