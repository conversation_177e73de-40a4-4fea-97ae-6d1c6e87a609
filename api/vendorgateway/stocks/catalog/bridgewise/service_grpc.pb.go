// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/stocks/catalog/bridgewise/service.proto

package bridgewise

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Catalog_GetAssetIdentifierDetails_FullMethodName       = "/vendorgateway.bridgewise.Catalog/GetAssetIdentifierDetails"
	Catalog_GetCompany_FullMethodName                      = "/vendorgateway.bridgewise.Catalog/GetCompany"
	Catalog_GetCompanyFundamentalParameters_FullMethodName = "/vendorgateway.bridgewise.Catalog/GetCompanyFundamentalParameters"
	Catalog_GetCompanyFundamentalParagraphs_FullMethodName = "/vendorgateway.bridgewise.Catalog/GetCompanyFundamentalParagraphs"
	Catalog_GetCompanyMarketData_FullMethodName            = "/vendorgateway.bridgewise.Catalog/GetCompanyMarketData"
	Catalog_GetCompanyLogos_FullMethodName                 = "/vendorgateway.bridgewise.Catalog/GetCompanyLogos"
	Catalog_GetCompanyMarketStatistics_FullMethodName      = "/vendorgateway.bridgewise.Catalog/GetCompanyMarketStatistics"
	Catalog_GetCompanies_FullMethodName                    = "/vendorgateway.bridgewise.Catalog/GetCompanies"
	Catalog_GetCompanyTradingItems_FullMethodName          = "/vendorgateway.bridgewise.Catalog/GetCompanyTradingItems"
)

// CatalogClient is the client API for Catalog service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CatalogClient interface {
	// GetAssetIdentifierDetails rpc retrieves detailed information about a specific asset based on the provided external identifier.
	// The external identifier can be of various types (e.g., ISIN, ticker-exchange) and is used to uniquely identify the asset across different exchanges.
	GetAssetIdentifierDetails(ctx context.Context, in *GetAssetIdentifierDetailsRequest, opts ...grpc.CallOption) (*GetAssetIdentifierDetailsResponse, error)
	// GetCompany rpc retrieves detailed information about one or more companies based on the provided company ID.
	GetCompany(ctx context.Context, in *GetCompanyRequest, opts ...grpc.CallOption) (*GetCompanyResponse, error)
	// GetCompanyFundamentalParameters rpc retrieves fundamental financial and operational parameters for a specified company based on the provided calendar year, quarter, and company ID.
	GetCompanyFundamentalParameters(ctx context.Context, in *GetCompanyFundamentalParametersRequest, opts ...grpc.CallOption) (*GetCompanyFundamentalParametersResponse, error)
	// GetCompanyFundamentalParagraphs rpc Retrieves narrative paragraphs that provide qualitative insights into a company's fundamentals,
	// such as analysis summaries, section-specific commentary, or textual descriptions.
	GetCompanyFundamentalParagraphs(ctx context.Context, in *GetCompanyFundamentalParagraphsRequest, opts ...grpc.CallOption) (*GetCompanyFundamentalParagraphsResponse, error)
	// GetCompanyMarketData rpc retrieves historical market data for a specified company within a given date range.
	GetCompanyMarketData(ctx context.Context, in *GetCompanyMarketDataRequest, opts ...grpc.CallOption) (*GetCompanyMarketDataResponse, error)
	// GetCompanyLogos rpc retrieves logos associated with a specified company.
	// It only returns logos for companies that have logos available and can return empty array if nothing is found.
	GetCompanyLogos(ctx context.Context, in *GetCompanyLogosRequest, opts ...grpc.CallOption) (*GetCompanyLogosResponse, error)
	// GetCompanyMarketStatistics rpc retrieves market performance statistics for a specified company.
	GetCompanyMarketStatistics(ctx context.Context, in *GetCompanyMarketStatisticsRequest, opts ...grpc.CallOption) (*GetCompanyMarketStatisticsResponse, error)
	// GetCompanies rpc retrieves companies in a paginated manner based on given filter
	GetCompanies(ctx context.Context, in *GetCompaniesRequest, opts ...grpc.CallOption) (*GetCompaniesResponse, error)
	// GetCompanyTradingItems rpc retrieves trading items for a specified company.
	GetCompanyTradingItems(ctx context.Context, in *GetCompanyTradingItemsRequest, opts ...grpc.CallOption) (*GetCompanyTradingItemsResponse, error)
}

type catalogClient struct {
	cc grpc.ClientConnInterface
}

func NewCatalogClient(cc grpc.ClientConnInterface) CatalogClient {
	return &catalogClient{cc}
}

func (c *catalogClient) GetAssetIdentifierDetails(ctx context.Context, in *GetAssetIdentifierDetailsRequest, opts ...grpc.CallOption) (*GetAssetIdentifierDetailsResponse, error) {
	out := new(GetAssetIdentifierDetailsResponse)
	err := c.cc.Invoke(ctx, Catalog_GetAssetIdentifierDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompany(ctx context.Context, in *GetCompanyRequest, opts ...grpc.CallOption) (*GetCompanyResponse, error) {
	out := new(GetCompanyResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompany_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompanyFundamentalParameters(ctx context.Context, in *GetCompanyFundamentalParametersRequest, opts ...grpc.CallOption) (*GetCompanyFundamentalParametersResponse, error) {
	out := new(GetCompanyFundamentalParametersResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompanyFundamentalParameters_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompanyFundamentalParagraphs(ctx context.Context, in *GetCompanyFundamentalParagraphsRequest, opts ...grpc.CallOption) (*GetCompanyFundamentalParagraphsResponse, error) {
	out := new(GetCompanyFundamentalParagraphsResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompanyFundamentalParagraphs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompanyMarketData(ctx context.Context, in *GetCompanyMarketDataRequest, opts ...grpc.CallOption) (*GetCompanyMarketDataResponse, error) {
	out := new(GetCompanyMarketDataResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompanyMarketData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompanyLogos(ctx context.Context, in *GetCompanyLogosRequest, opts ...grpc.CallOption) (*GetCompanyLogosResponse, error) {
	out := new(GetCompanyLogosResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompanyLogos_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompanyMarketStatistics(ctx context.Context, in *GetCompanyMarketStatisticsRequest, opts ...grpc.CallOption) (*GetCompanyMarketStatisticsResponse, error) {
	out := new(GetCompanyMarketStatisticsResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompanyMarketStatistics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompanies(ctx context.Context, in *GetCompaniesRequest, opts ...grpc.CallOption) (*GetCompaniesResponse, error) {
	out := new(GetCompaniesResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompanies_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *catalogClient) GetCompanyTradingItems(ctx context.Context, in *GetCompanyTradingItemsRequest, opts ...grpc.CallOption) (*GetCompanyTradingItemsResponse, error) {
	out := new(GetCompanyTradingItemsResponse)
	err := c.cc.Invoke(ctx, Catalog_GetCompanyTradingItems_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CatalogServer is the server API for Catalog service.
// All implementations should embed UnimplementedCatalogServer
// for forward compatibility
type CatalogServer interface {
	// GetAssetIdentifierDetails rpc retrieves detailed information about a specific asset based on the provided external identifier.
	// The external identifier can be of various types (e.g., ISIN, ticker-exchange) and is used to uniquely identify the asset across different exchanges.
	GetAssetIdentifierDetails(context.Context, *GetAssetIdentifierDetailsRequest) (*GetAssetIdentifierDetailsResponse, error)
	// GetCompany rpc retrieves detailed information about one or more companies based on the provided company ID.
	GetCompany(context.Context, *GetCompanyRequest) (*GetCompanyResponse, error)
	// GetCompanyFundamentalParameters rpc retrieves fundamental financial and operational parameters for a specified company based on the provided calendar year, quarter, and company ID.
	GetCompanyFundamentalParameters(context.Context, *GetCompanyFundamentalParametersRequest) (*GetCompanyFundamentalParametersResponse, error)
	// GetCompanyFundamentalParagraphs rpc Retrieves narrative paragraphs that provide qualitative insights into a company's fundamentals,
	// such as analysis summaries, section-specific commentary, or textual descriptions.
	GetCompanyFundamentalParagraphs(context.Context, *GetCompanyFundamentalParagraphsRequest) (*GetCompanyFundamentalParagraphsResponse, error)
	// GetCompanyMarketData rpc retrieves historical market data for a specified company within a given date range.
	GetCompanyMarketData(context.Context, *GetCompanyMarketDataRequest) (*GetCompanyMarketDataResponse, error)
	// GetCompanyLogos rpc retrieves logos associated with a specified company.
	// It only returns logos for companies that have logos available and can return empty array if nothing is found.
	GetCompanyLogos(context.Context, *GetCompanyLogosRequest) (*GetCompanyLogosResponse, error)
	// GetCompanyMarketStatistics rpc retrieves market performance statistics for a specified company.
	GetCompanyMarketStatistics(context.Context, *GetCompanyMarketStatisticsRequest) (*GetCompanyMarketStatisticsResponse, error)
	// GetCompanies rpc retrieves companies in a paginated manner based on given filter
	GetCompanies(context.Context, *GetCompaniesRequest) (*GetCompaniesResponse, error)
	// GetCompanyTradingItems rpc retrieves trading items for a specified company.
	GetCompanyTradingItems(context.Context, *GetCompanyTradingItemsRequest) (*GetCompanyTradingItemsResponse, error)
}

// UnimplementedCatalogServer should be embedded to have forward compatible implementations.
type UnimplementedCatalogServer struct {
}

func (UnimplementedCatalogServer) GetAssetIdentifierDetails(context.Context, *GetAssetIdentifierDetailsRequest) (*GetAssetIdentifierDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssetIdentifierDetails not implemented")
}
func (UnimplementedCatalogServer) GetCompany(context.Context, *GetCompanyRequest) (*GetCompanyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompany not implemented")
}
func (UnimplementedCatalogServer) GetCompanyFundamentalParameters(context.Context, *GetCompanyFundamentalParametersRequest) (*GetCompanyFundamentalParametersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyFundamentalParameters not implemented")
}
func (UnimplementedCatalogServer) GetCompanyFundamentalParagraphs(context.Context, *GetCompanyFundamentalParagraphsRequest) (*GetCompanyFundamentalParagraphsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyFundamentalParagraphs not implemented")
}
func (UnimplementedCatalogServer) GetCompanyMarketData(context.Context, *GetCompanyMarketDataRequest) (*GetCompanyMarketDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyMarketData not implemented")
}
func (UnimplementedCatalogServer) GetCompanyLogos(context.Context, *GetCompanyLogosRequest) (*GetCompanyLogosResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyLogos not implemented")
}
func (UnimplementedCatalogServer) GetCompanyMarketStatistics(context.Context, *GetCompanyMarketStatisticsRequest) (*GetCompanyMarketStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyMarketStatistics not implemented")
}
func (UnimplementedCatalogServer) GetCompanies(context.Context, *GetCompaniesRequest) (*GetCompaniesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanies not implemented")
}
func (UnimplementedCatalogServer) GetCompanyTradingItems(context.Context, *GetCompanyTradingItemsRequest) (*GetCompanyTradingItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyTradingItems not implemented")
}

// UnsafeCatalogServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CatalogServer will
// result in compilation errors.
type UnsafeCatalogServer interface {
	mustEmbedUnimplementedCatalogServer()
}

func RegisterCatalogServer(s grpc.ServiceRegistrar, srv CatalogServer) {
	s.RegisterService(&Catalog_ServiceDesc, srv)
}

func _Catalog_GetAssetIdentifierDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssetIdentifierDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetAssetIdentifierDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetAssetIdentifierDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetAssetIdentifierDetails(ctx, req.(*GetAssetIdentifierDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompany_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompany(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompany_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompany(ctx, req.(*GetCompanyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompanyFundamentalParameters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyFundamentalParametersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompanyFundamentalParameters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompanyFundamentalParameters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompanyFundamentalParameters(ctx, req.(*GetCompanyFundamentalParametersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompanyFundamentalParagraphs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyFundamentalParagraphsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompanyFundamentalParagraphs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompanyFundamentalParagraphs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompanyFundamentalParagraphs(ctx, req.(*GetCompanyFundamentalParagraphsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompanyMarketData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyMarketDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompanyMarketData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompanyMarketData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompanyMarketData(ctx, req.(*GetCompanyMarketDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompanyLogos_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyLogosRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompanyLogos(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompanyLogos_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompanyLogos(ctx, req.(*GetCompanyLogosRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompanyMarketStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyMarketStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompanyMarketStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompanyMarketStatistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompanyMarketStatistics(ctx, req.(*GetCompanyMarketStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompanies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompaniesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompanies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompanies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompanies(ctx, req.(*GetCompaniesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Catalog_GetCompanyTradingItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCompanyTradingItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CatalogServer).GetCompanyTradingItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Catalog_GetCompanyTradingItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CatalogServer).GetCompanyTradingItems(ctx, req.(*GetCompanyTradingItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Catalog_ServiceDesc is the grpc.ServiceDesc for Catalog service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Catalog_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.bridgewise.Catalog",
	HandlerType: (*CatalogServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAssetIdentifierDetails",
			Handler:    _Catalog_GetAssetIdentifierDetails_Handler,
		},
		{
			MethodName: "GetCompany",
			Handler:    _Catalog_GetCompany_Handler,
		},
		{
			MethodName: "GetCompanyFundamentalParameters",
			Handler:    _Catalog_GetCompanyFundamentalParameters_Handler,
		},
		{
			MethodName: "GetCompanyFundamentalParagraphs",
			Handler:    _Catalog_GetCompanyFundamentalParagraphs_Handler,
		},
		{
			MethodName: "GetCompanyMarketData",
			Handler:    _Catalog_GetCompanyMarketData_Handler,
		},
		{
			MethodName: "GetCompanyLogos",
			Handler:    _Catalog_GetCompanyLogos_Handler,
		},
		{
			MethodName: "GetCompanyMarketStatistics",
			Handler:    _Catalog_GetCompanyMarketStatistics_Handler,
		},
		{
			MethodName: "GetCompanies",
			Handler:    _Catalog_GetCompanies_Handler,
		},
		{
			MethodName: "GetCompanyTradingItems",
			Handler:    _Catalog_GetCompanyTradingItems_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/stocks/catalog/bridgewise/service.proto",
}
