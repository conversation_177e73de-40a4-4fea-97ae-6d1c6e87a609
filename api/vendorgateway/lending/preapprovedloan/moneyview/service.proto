syntax = "proto3";

package vendorgateway.lending.preapprovedloan.moneyview;

import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/gender.proto";
import "api/vendorgateway/lending/preapprovedloan/moneyview/enums.proto";
import "api/vendorgateway/lending/preapprovedloan/moneyview/offer.proto";
import "api/vendorgateway/request_header.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan.moneyview";

service Moneyview {
  // CreateLead is useful to create a lead for a loan offer.
  rpc CreateLead (CreateLeadRequest) returns (CreateLeadResponse);
  // GetLeadStatus is useful to fetch the status of an already created lead.
  rpc GetLeadStatus (GetLeadStatusRequest) returns (GetLeadStatusResponse);
  // GetOffers is useful to fetch the offers from for a given lead.
  rpc GetOffers (GetOffersRequest) returns (GetOffersResponse);
  // GetPWAJourneyUrl is useful to fetch the moneyview PWA journey url for a loan application/account created for a given lead.
  rpc GetPWAJourneyUrl (GetPWAJourneyUrlRequest) returns (GetPWAJourneyUrlResponse);
  //GetDeDupeStatus is useful to check if some loan request is already in progress.
  rpc GetDeDupeStatus (GetDeDupeStatusRequest) returns (GetDeDupeStatusResponse);
  //GetSingleDeDupeStatus is useful to check if a loan request is already in progress for a single user.
  rpc GetSingleDeDupeStatus (GetSingleDeDupeStatusRequest) returns (GetSingleDeDupeStatusResponse);
}

// needed for returning auth token response from vg http handler.
// **Note** : the corresponding api to generate auth token is intentionally not exposed to vg clients.

message GetDeDupeStatusRequest {
  vendorgateway.RequestHeader header = 1;
  repeated string pans = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
  // denotes the user type
  MvUserType user_type = 3 [(validate.rules).enum = {not_in: [0]}];
}


message GetDeDupeStatusResponse {
  rpc.Status status = 1;
  message PanDeDupeStatus {
    string pan = 1;
    enum DeDupeStatus {
      DEDUPE_STATUS_UNSPECIFIED = 0;
      // denotes that shared pan passes the dedupe check i.e currently user does not has any active loan application at moneyview's end.
      DEDUPE_STATUS_DEDUPE_PASSED = 1;
      // denotes that shared pan does NOT passed the dedupe check i.e currently user does has an active loan application at moneyview's end.
      DEDUPE_STATUS_DEDUPE_FAILED = 2;
    }
    DeDupeStatus status = 2;
  }
  repeated PanDeDupeStatus pan_dedupe_statuses = 2;
}

message GenerateAuthTokenResponse {
  // rpc status
  rpc.Status status = 1;
  // auth token
  string auth_token = 2;
}

message CreateLeadRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // denotes the pre-approved offer-id which was shared offline with the vendor.
  string partner_ref_id = 3 [(validate.rules).string.min_len = 1];
  // denotes the phone number of the user
  api.typesv2.common.PhoneNumber phone_number = 4 [(validate.rules).message.required = true];
  // denotes the pan number of the user
  string pan = 5 [(validate.rules).string.min_len = 1];
  // denotes the name of the user
  api.typesv2.common.Name name = 6 [(validate.rules).message.required = true];
  // denotes the gender of the user
  api.typesv2.Gender gender = 7;
  // denotes the date of birth of the user
  google.type.Date dob = 8 [(validate.rules).message.required = true];
  // denotes the employment type of the user
  EmploymentType employment_type = 9 [(validate.rules).enum = {not_in: [0]}];
  // denotes the income declared by the user.
  google.type.Money declared_income = 10 [(validate.rules).message.required = true];
  // denotes the list of addresses of the user.
  repeated AddressWithType addresses = 11 [(validate.rules).repeated.min_items = 1];
  // denotes the email-id of the user.
  string email_id = 12 [(validate.rules).string.min_len = 1];
  // optional field, denotes the annual family income of the user.
  google.type.Money annual_family_income = 13;

  message AddressWithType {
    AddressType type = 1 [(validate.rules).enum = {not_in: [0]}];
    api.typesv2.PostalAddress address = 2 [(validate.rules).message.required = true];
  }
  // denotes the user type
  MvUserType user_type = 14 [(validate.rules).enum = {not_in: [0]}];
}

message CreateLeadResponse {
  enum Status {
    // denotes that the request was successful
    OK = 0;
    // denotes that the precondition to create a new lead is not satisfied.
    FAILED_PRECONDITION = 9;
    // denotes that a lead with passed details already exists in MV system and therefore a new/fresh lead can't be created now.
    FAILED_PRECONDITION_DUPLICATE_LEAD = 101;
    // denotes that the salary details passed to MV does not meet the minimum qualifying criteria for generating a loan offer at MV's end e.g when the
    // salary amount passed is less than min required salary amount for MV offer generation then we can get this error.
    FAILED_PRECONDITION_INVALID_USER_INCOME_DETAILS = 102;
    // denotes that user age passed was invalid, can happen if the passed age does not qualifies the min age criteria for applying for a loan at MV's end.
    FAILED_PRECONDITION_INVALID_USER_AGE = 103;
    // denotes that the lead has been REJECTED at MV's end.
    FAILED_PRECONDITION_LEAD_REJECTED = 104;
  }
  // rpc status
  rpc.Status status = 1;
  // identifier of lead generated at moneyview's end.
  string leadId = 2;
}

message GetLeadStatusRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // denotes the pre-approved offer-id which was shared offline with the vendor.
  string lead_id = 2;
  // denotes the user type
  MvUserType user_type = 3 [(validate.rules).enum = {not_in: [0]}];
}

message GetLeadStatusResponse {
  // rpc status
  rpc.Status status = 1;
  // denotes the current lead status.
  LeadStatus lead_status = 2;
  // these fields would only be populated only when lead status is DISBURSED
  google.type.Money loan_amount = 3;
  google.type.Money disbursed_amount = 4;
}


message GetOffersRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // denotes the already created lead using CreateLead api for which offers need to be fetched.
  string lead_id = 2 [(validate.rules).string.min_len = 1];
  // denotes the user type
  MvUserType user_type = 3 [(validate.rules).enum = {not_in: [0]}];
}

message GetOffersResponse {
  // rpc status
  rpc.Status status = 1;
  // list of offers.
  repeated Offer offers = 2;
}

message GetPWAJourneyUrlRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // denotes the lead for which journey url needs to be fetched.
  string lead_id = 2 [(validate.rules).string.min_len = 1];
  // denotes the user type
  MvUserType user_type = 3 [(validate.rules).enum = {not_in: [0]}];
}

message GetPWAJourneyUrlResponse {
  // rpc status
  rpc.Status status = 1;
  // denotes the sso url for the PWA journey.
  string sso_url = 2;
}

message GetSingleDeDupeStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string pan_number = 2 [(validate.rules).string.min_len = 1];
  string email = 3 [(validate.rules).string.min_len = 1];
  string mobile_number = 4 [(validate.rules).string.min_len = 1];
  // denotes the user type
  MvUserType user_type = 5 [(validate.rules).enum = {not_in: [0]}];
}

message GetSingleDeDupeStatusResponse {
  rpc.Status status = 1;
  DeDupeStatus dedupe_status = 2;
}

