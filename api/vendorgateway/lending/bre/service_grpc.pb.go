// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/lending/bre/service.proto

package bre

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	BusinessRuleEngine_InhouseBRECheckForCC_FullMethodName = "/vendorgateway.lending.bre.BusinessRuleEngine/InhouseBRECheckForCC"
)

// BusinessRuleEngineClient is the client API for BusinessRuleEngine service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessRuleEngineClient interface {
	// RPC to run inhouse BRE check for credit card eligibility
	InhouseBRECheckForCC(ctx context.Context, in *InhouseBRECheckForCCRequest, opts ...grpc.CallOption) (*InhouseBRECheckForCCResponse, error)
}

type businessRuleEngineClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessRuleEngineClient(cc grpc.ClientConnInterface) BusinessRuleEngineClient {
	return &businessRuleEngineClient{cc}
}

func (c *businessRuleEngineClient) InhouseBRECheckForCC(ctx context.Context, in *InhouseBRECheckForCCRequest, opts ...grpc.CallOption) (*InhouseBRECheckForCCResponse, error) {
	out := new(InhouseBRECheckForCCResponse)
	err := c.cc.Invoke(ctx, BusinessRuleEngine_InhouseBRECheckForCC_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessRuleEngineServer is the server API for BusinessRuleEngine service.
// All implementations should embed UnimplementedBusinessRuleEngineServer
// for forward compatibility
type BusinessRuleEngineServer interface {
	// RPC to run inhouse BRE check for credit card eligibility
	InhouseBRECheckForCC(context.Context, *InhouseBRECheckForCCRequest) (*InhouseBRECheckForCCResponse, error)
}

// UnimplementedBusinessRuleEngineServer should be embedded to have forward compatible implementations.
type UnimplementedBusinessRuleEngineServer struct {
}

func (UnimplementedBusinessRuleEngineServer) InhouseBRECheckForCC(context.Context, *InhouseBRECheckForCCRequest) (*InhouseBRECheckForCCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InhouseBRECheckForCC not implemented")
}

// UnsafeBusinessRuleEngineServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessRuleEngineServer will
// result in compilation errors.
type UnsafeBusinessRuleEngineServer interface {
	mustEmbedUnimplementedBusinessRuleEngineServer()
}

func RegisterBusinessRuleEngineServer(s grpc.ServiceRegistrar, srv BusinessRuleEngineServer) {
	s.RegisterService(&BusinessRuleEngine_ServiceDesc, srv)
}

func _BusinessRuleEngine_InhouseBRECheckForCC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InhouseBRECheckForCCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessRuleEngineServer).InhouseBRECheckForCC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BusinessRuleEngine_InhouseBRECheckForCC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessRuleEngineServer).InhouseBRECheckForCC(ctx, req.(*InhouseBRECheckForCCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessRuleEngine_ServiceDesc is the grpc.ServiceDesc for BusinessRuleEngine service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessRuleEngine_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.lending.bre.BusinessRuleEngine",
	HandlerType: (*BusinessRuleEngineServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "InhouseBRECheckForCC",
			Handler:    _BusinessRuleEngine_InhouseBRECheckForCC_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/lending/bre/service.proto",
}
