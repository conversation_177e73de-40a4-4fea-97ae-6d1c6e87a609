// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/risk/service.proto

package risk

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	afu "github.com/epifi/gamma/api/auth/afu"

	common "github.com/epifi/be-common/api/typesv2/common"

	external "github.com/epifi/gamma/api/tiering/external"

	kyc "github.com/epifi/gamma/api/kyc"

	liveness "github.com/epifi/gamma/api/auth/liveness"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = afu.OverallStatus(0)

	_ = common.BooleanEnum(0)

	_ = external.Tier(0)

	_ = kyc.FailureType(0)

	_ = liveness.LivenessStatus(0)
)

// Validate checks the field values on GetCasePrioritisationScoreRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCasePrioritisationScoreRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCasePrioritisationScoreRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCasePrioritisationScoreRequestMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAlertWithRuleDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
						field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCasePrioritisationScoreRequestValidationError{
						field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCasePrioritisationScoreRequestValidationError{
					field:  fmt.Sprintf("AlertWithRuleDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ModelVersion

	if len(errors) > 0 {
		return GetCasePrioritisationScoreRequestMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetCasePrioritisationScoreRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreRequestMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreRequestValidationError is the validation error
// returned by GetCasePrioritisationScoreRequest.Validate if the designated
// constraints aren't met.
type GetCasePrioritisationScoreRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCasePrioritisationScoreRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreRequestValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreRequestValidationError{}

// Validate checks the field values on GetCasePrioritisationScoreResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetCasePrioritisationScoreResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCasePrioritisationScoreResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCasePrioritisationScoreResponseMultiError, or nil if none found.
func (m *GetCasePrioritisationScoreResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCasePrioritisationScoreResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCasePrioritisationScoreResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCasePrioritisationScoreResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for ModelVersion

	// no validation rules for Score

	// no validation rules for RawVendorResponse

	if len(errors) > 0 {
		return GetCasePrioritisationScoreResponseMultiError(errors)
	}

	return nil
}

// GetCasePrioritisationScoreResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetCasePrioritisationScoreResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCasePrioritisationScoreResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCasePrioritisationScoreResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCasePrioritisationScoreResponseMultiError) AllErrors() []error { return m }

// GetCasePrioritisationScoreResponseValidationError is the validation error
// returned by GetCasePrioritisationScoreResponse.Validate if the designated
// constraints aren't met.
type GetCasePrioritisationScoreResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCasePrioritisationScoreResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCasePrioritisationScoreResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCasePrioritisationScoreResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCasePrioritisationScoreResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCasePrioritisationScoreResponseValidationError) ErrorName() string {
	return "GetCasePrioritisationScoreResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCasePrioritisationScoreResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCasePrioritisationScoreResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCasePrioritisationScoreResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCasePrioritisationScoreResponseValidationError{}

// Validate checks the field values on GetBureauIdRiskDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBureauIdRiskDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBureauIdRiskDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBureauIdRiskDetailsRequestMultiError, or nil if none found.
func (m *GetBureauIdRiskDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBureauIdRiskDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetUserData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsRequestValidationError{
					field:  "UserData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsRequestValidationError{
					field:  "UserData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsRequestValidationError{
				field:  "UserData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetBureauIdRiskDetailsRequestMultiError(errors)
	}

	return nil
}

// GetBureauIdRiskDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetBureauIdRiskDetailsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetBureauIdRiskDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBureauIdRiskDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBureauIdRiskDetailsRequestMultiError) AllErrors() []error { return m }

// GetBureauIdRiskDetailsRequestValidationError is the validation error
// returned by GetBureauIdRiskDetailsRequest.Validate if the designated
// constraints aren't met.
type GetBureauIdRiskDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBureauIdRiskDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBureauIdRiskDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBureauIdRiskDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBureauIdRiskDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBureauIdRiskDetailsRequestValidationError) ErrorName() string {
	return "GetBureauIdRiskDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBureauIdRiskDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBureauIdRiskDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBureauIdRiskDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBureauIdRiskDetailsRequestValidationError{}

// Validate checks the field values on UserData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserDataMultiError, or nil
// if none found.
func (m *UserData) ValidateAll() error {
	return m.validate(true)
}

func (m *UserData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for EmailId

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserDataValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserDataValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserDataMultiError(errors)
	}

	return nil
}

// UserDataMultiError is an error wrapping multiple validation errors returned
// by UserData.ValidateAll() if the designated constraints aren't met.
type UserDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserDataMultiError) AllErrors() []error { return m }

// UserDataValidationError is the validation error returned by
// UserData.Validate if the designated constraints aren't met.
type UserDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserDataValidationError) ErrorName() string { return "UserDataValidationError" }

// Error satisfies the builtin error interface
func (e UserDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserDataValidationError{}

// Validate checks the field values on GetBureauIdRiskDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBureauIdRiskDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBureauIdRiskDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBureauIdRiskDetailsResponseMultiError, or nil if none found.
func (m *GetBureauIdRiskDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBureauIdRiskDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Outcome

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetEmailAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "EmailAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "EmailAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmailAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "EmailAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmailNameAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "EmailNameAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "EmailNameAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmailNameAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "EmailNameAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "PhoneAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNameAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneNameAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneNameAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNameAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "PhoneNameAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNetworkAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneNetworkAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneNetworkAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNetworkAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "PhoneNetworkAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmailSocialAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "EmailSocialAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "EmailSocialAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmailSocialAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "EmailSocialAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneSocialAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneSocialAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "PhoneSocialAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneSocialAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "PhoneSocialAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRiskAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "RiskAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBureauIdRiskDetailsResponseValidationError{
					field:  "RiskAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBureauIdRiskDetailsResponseValidationError{
				field:  "RiskAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawResponse

	if len(errors) > 0 {
		return GetBureauIdRiskDetailsResponseMultiError(errors)
	}

	return nil
}

// GetBureauIdRiskDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetBureauIdRiskDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetBureauIdRiskDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBureauIdRiskDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBureauIdRiskDetailsResponseMultiError) AllErrors() []error { return m }

// GetBureauIdRiskDetailsResponseValidationError is the validation error
// returned by GetBureauIdRiskDetailsResponse.Validate if the designated
// constraints aren't met.
type GetBureauIdRiskDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBureauIdRiskDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBureauIdRiskDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBureauIdRiskDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBureauIdRiskDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBureauIdRiskDetailsResponseValidationError) ErrorName() string {
	return "GetBureauIdRiskDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBureauIdRiskDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBureauIdRiskDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBureauIdRiskDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBureauIdRiskDetailsResponseValidationError{}

// Validate checks the field values on EmailAttributes with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EmailAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmailAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmailAttributesMultiError, or nil if none found.
func (m *EmailAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *EmailAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for EmailExists

	// no validation rules for DigitalAge

	// no validation rules for DomainExists

	// no validation rules for DomainRiskLevel

	// no validation rules for DomainCategory

	// no validation rules for DomainCorporate

	// no validation rules for UniqueHits

	// no validation rules for EmailFinalRecommendation

	// no validation rules for LastVerificationDate

	// no validation rules for FirstVerificationDate

	if len(errors) > 0 {
		return EmailAttributesMultiError(errors)
	}

	return nil
}

// EmailAttributesMultiError is an error wrapping multiple validation errors
// returned by EmailAttributes.ValidateAll() if the designated constraints
// aren't met.
type EmailAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailAttributesMultiError) AllErrors() []error { return m }

// EmailAttributesValidationError is the validation error returned by
// EmailAttributes.Validate if the designated constraints aren't met.
type EmailAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailAttributesValidationError) ErrorName() string { return "EmailAttributesValidationError" }

// Error satisfies the builtin error interface
func (e EmailAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmailAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailAttributesValidationError{}

// Validate checks the field values on EmailNameAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EmailNameAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmailNameAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmailNameAttributesMultiError, or nil if none found.
func (m *EmailNameAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *EmailNameAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for Address

	// no validation rules for DigitalAge

	// no validation rules for PhoneNumber

	// no validation rules for FirstNameMatch

	// no validation rules for LastNameMatch

	// no validation rules for BusinessNameDetected

	// no validation rules for EmailFootprintStrength

	// no validation rules for EmailNameDigitalAge

	// no validation rules for EmailSpclChars

	// no validation rules for NdrScore

	// no validation rules for UnrScore

	// no validation rules for NameMatchScore

	// no validation rules for NameEmailMatch

	// no validation rules for MultiplePhoneAttached

	if len(errors) > 0 {
		return EmailNameAttributesMultiError(errors)
	}

	return nil
}

// EmailNameAttributesMultiError is an error wrapping multiple validation
// errors returned by EmailNameAttributes.ValidateAll() if the designated
// constraints aren't met.
type EmailNameAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailNameAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailNameAttributesMultiError) AllErrors() []error { return m }

// EmailNameAttributesValidationError is the validation error returned by
// EmailNameAttributes.Validate if the designated constraints aren't met.
type EmailNameAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailNameAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailNameAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailNameAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailNameAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailNameAttributesValidationError) ErrorName() string {
	return "EmailNameAttributesValidationError"
}

// Error satisfies the builtin error interface
func (e EmailNameAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmailNameAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailNameAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailNameAttributesValidationError{}

// Validate checks the field values on PhoneAttributes with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PhoneAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhoneAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhoneAttributesMultiError, or nil if none found.
func (m *PhoneAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *PhoneAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for Name

	// no validation rules for Source

	// no validation rules for Vpa

	if len(errors) > 0 {
		return PhoneAttributesMultiError(errors)
	}

	return nil
}

// PhoneAttributesMultiError is an error wrapping multiple validation errors
// returned by PhoneAttributes.ValidateAll() if the designated constraints
// aren't met.
type PhoneAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhoneAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhoneAttributesMultiError) AllErrors() []error { return m }

// PhoneAttributesValidationError is the validation error returned by
// PhoneAttributes.Validate if the designated constraints aren't met.
type PhoneAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhoneAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhoneAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhoneAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhoneAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhoneAttributesValidationError) ErrorName() string { return "PhoneAttributesValidationError" }

// Error satisfies the builtin error interface
func (e PhoneAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhoneAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhoneAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhoneAttributesValidationError{}

// Validate checks the field values on PhoneNameAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhoneNameAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhoneNameAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhoneNameAttributesMultiError, or nil if none found.
func (m *PhoneNameAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *PhoneNameAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for Address

	// no validation rules for DigitalAge

	// no validation rules for FirstNameMatch

	// no validation rules for LastNameMatch

	// no validation rules for BusinessNameDetected

	// no validation rules for NdrScore

	// no validation rules for UnrScore

	// no validation rules for NameMatchScore

	// no validation rules for FootprintStrengthOverall

	// no validation rules for PhoneNameDigitalAge

	if len(errors) > 0 {
		return PhoneNameAttributesMultiError(errors)
	}

	return nil
}

// PhoneNameAttributesMultiError is an error wrapping multiple validation
// errors returned by PhoneNameAttributes.ValidateAll() if the designated
// constraints aren't met.
type PhoneNameAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhoneNameAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhoneNameAttributesMultiError) AllErrors() []error { return m }

// PhoneNameAttributesValidationError is the validation error returned by
// PhoneNameAttributes.Validate if the designated constraints aren't met.
type PhoneNameAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhoneNameAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhoneNameAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhoneNameAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhoneNameAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhoneNameAttributesValidationError) ErrorName() string {
	return "PhoneNameAttributesValidationError"
}

// Error satisfies the builtin error interface
func (e PhoneNameAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhoneNameAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhoneNameAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhoneNameAttributesValidationError{}

// Validate checks the field values on PhoneNetworkAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhoneNetworkAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhoneNetworkAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhoneNetworkAttributesMultiError, or nil if none found.
func (m *PhoneNetworkAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *PhoneNetworkAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for Imsi

	// no validation rules for CurrentNetworkName

	// no validation rules for CurrentNetworkRegion

	// no validation rules for CurrentNetworkCountryCodeIso2

	// no validation rules for IsPhoneReachable

	// no validation rules for IsValidPhoneNumber

	// no validation rules for NumberBillingType

	// no validation rules for NumberHasPortingHistory

	// no validation rules for PortedFromNetworkName

	// no validation rules for PortedFromNetworkRegion

	// no validation rules for PortedFromNetworkCountryCodeIso2

	// no validation rules for Roaming

	// no validation rules for RoamingNetworkName

	// no validation rules for RoamingNetworkRegion

	// no validation rules for RoamingNetworkCountryCodeIso2

	if len(errors) > 0 {
		return PhoneNetworkAttributesMultiError(errors)
	}

	return nil
}

// PhoneNetworkAttributesMultiError is an error wrapping multiple validation
// errors returned by PhoneNetworkAttributes.ValidateAll() if the designated
// constraints aren't met.
type PhoneNetworkAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhoneNetworkAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhoneNetworkAttributesMultiError) AllErrors() []error { return m }

// PhoneNetworkAttributesValidationError is the validation error returned by
// PhoneNetworkAttributes.Validate if the designated constraints aren't met.
type PhoneNetworkAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhoneNetworkAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhoneNetworkAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhoneNetworkAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhoneNetworkAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhoneNetworkAttributesValidationError) ErrorName() string {
	return "PhoneNetworkAttributesValidationError"
}

// Error satisfies the builtin error interface
func (e PhoneNetworkAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhoneNetworkAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhoneNetworkAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhoneNetworkAttributesValidationError{}

// Validate checks the field values on PhoneSocialAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PhoneSocialAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PhoneSocialAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PhoneSocialAttributesMultiError, or nil if none found.
func (m *PhoneSocialAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *PhoneSocialAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for Amazon

	// no validation rules for Flipkart

	// no validation rules for Housing

	// no validation rules for Indiamart

	// no validation rules for Instagram

	// no validation rules for IsWABusiness

	// no validation rules for Jeevansaathi

	// no validation rules for Jiomart

	// no validation rules for Microsoft

	// no validation rules for Mobile

	// no validation rules for Paytm

	// no validation rules for Shaadi

	// no validation rules for Skype

	// no validation rules for Swiggy

	// no validation rules for Toi

	// no validation rules for Whatsapp

	// no validation rules for Yatra

	// no validation rules for Zoho

	if len(errors) > 0 {
		return PhoneSocialAttributesMultiError(errors)
	}

	return nil
}

// PhoneSocialAttributesMultiError is an error wrapping multiple validation
// errors returned by PhoneSocialAttributes.ValidateAll() if the designated
// constraints aren't met.
type PhoneSocialAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PhoneSocialAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PhoneSocialAttributesMultiError) AllErrors() []error { return m }

// PhoneSocialAttributesValidationError is the validation error returned by
// PhoneSocialAttributes.Validate if the designated constraints aren't met.
type PhoneSocialAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PhoneSocialAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PhoneSocialAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PhoneSocialAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PhoneSocialAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PhoneSocialAttributesValidationError) ErrorName() string {
	return "PhoneSocialAttributesValidationError"
}

// Error satisfies the builtin error interface
func (e PhoneSocialAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPhoneSocialAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PhoneSocialAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PhoneSocialAttributesValidationError{}

// Validate checks the field values on EmailSocialAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EmailSocialAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmailSocialAttributes with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmailSocialAttributesMultiError, or nil if none found.
func (m *EmailSocialAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *EmailSocialAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for Amazon

	// no validation rules for Booking

	// no validation rules for Email

	// no validation rules for Flickr

	// no validation rules for Flipkart

	// no validation rules for Housing

	// no validation rules for Instagram

	// no validation rules for Jeevansaathi

	// no validation rules for Microsoft

	// no validation rules for Paytm

	// no validation rules for Pinterest

	// no validation rules for Quora

	// no validation rules for Shaadi

	// no validation rules for Skype

	// no validation rules for Spotify

	// no validation rules for Toi

	// no validation rules for Wordpress

	// no validation rules for Yatra

	// no validation rules for Zoho

	if len(errors) > 0 {
		return EmailSocialAttributesMultiError(errors)
	}

	return nil
}

// EmailSocialAttributesMultiError is an error wrapping multiple validation
// errors returned by EmailSocialAttributes.ValidateAll() if the designated
// constraints aren't met.
type EmailSocialAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmailSocialAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmailSocialAttributesMultiError) AllErrors() []error { return m }

// EmailSocialAttributesValidationError is the validation error returned by
// EmailSocialAttributes.Validate if the designated constraints aren't met.
type EmailSocialAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmailSocialAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmailSocialAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmailSocialAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmailSocialAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmailSocialAttributesValidationError) ErrorName() string {
	return "EmailSocialAttributesValidationError"
}

// Error satisfies the builtin error interface
func (e EmailSocialAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmailSocialAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmailSocialAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmailSocialAttributesValidationError{}

// Validate checks the field values on RiskAttributes with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskAttributes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskAttributes with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskAttributesMultiError,
// or nil if none found.
func (m *RiskAttributes) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskAttributes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttributeStatus

	// no validation rules for Address1

	// no validation rules for Address2

	// no validation rules for Landmark

	// no validation rules for AreaName

	// no validation rules for AreaPincode

	// no validation rules for SocietyName

	// no validation rules for StreetName

	// no validation rules for CityName

	// no validation rules for StateName

	// no validation rules for AddressInsights

	// no validation rules for AddressRisk

	// no validation rules for AddressCompletenessScore

	// no validation rules for CommonEmailCount

	// no validation rules for EmailFraud

	// no validation rules for EmailFraudCount

	// no validation rules for EmailFraudNetwork

	// no validation rules for EmailIdentityTrust

	// no validation rules for EmailSocialMediaCount

	// no validation rules for IdentityConfidence

	// no validation rules for TelecomRisk

	// no validation rules for IsPhoneReachable

	// no validation rules for PhoneFraud

	// no validation rules for PhoneFraudCount

	// no validation rules for PhoneFraudNetwork

	// no validation rules for PhoneIdentityTrust

	// no validation rules for PhoneSocialMediaCount

	// no validation rules for UpiPhoneNameMatch

	// no validation rules for UpiPhoneNameMatchScore

	// no validation rules for AlternateRiskScore

	if len(errors) > 0 {
		return RiskAttributesMultiError(errors)
	}

	return nil
}

// RiskAttributesMultiError is an error wrapping multiple validation errors
// returned by RiskAttributes.ValidateAll() if the designated constraints
// aren't met.
type RiskAttributesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskAttributesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskAttributesMultiError) AllErrors() []error { return m }

// RiskAttributesValidationError is the validation error returned by
// RiskAttributes.Validate if the designated constraints aren't met.
type RiskAttributesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskAttributesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskAttributesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskAttributesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskAttributesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskAttributesValidationError) ErrorName() string { return "RiskAttributesValidationError" }

// Error satisfies the builtin error interface
func (e RiskAttributesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskAttributes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskAttributesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskAttributesValidationError{}

// Validate checks the field values on DetectReOnboardingRiskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectReOnboardingRiskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectReOnboardingRiskRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DetectReOnboardingRiskRequestMultiError, or nil if none found.
func (m *DetectReOnboardingRiskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectReOnboardingRiskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ReferrerActorId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmailId

	// no validation rules for IsCreditReportPresent

	// no validation rules for CreditReportDownloadConsent

	// no validation rules for IsDevicePremium

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GmailPanNameMatchScore

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenerMailCount

	if all {
		switch v := interface{}(m.GetGeolocation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Geolocation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Geolocation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeolocation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "Geolocation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Threshold

	// no validation rules for HashedPhoneNumber

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingEkycNumberMismatch

	if all {
		switch v := interface{}(m.GetUserPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "UserPanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserCity

	// no validation rules for UserPostalCode

	// no validation rules for KycLevel

	// no validation rules for OverallAfuStatus

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Context",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Context",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "Context",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AfuAttemptNum

	// no validation rules for UserState

	if all {
		switch v := interface{}(m.GetAfuGeolocation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "AfuGeolocation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "AfuGeolocation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAfuGeolocation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "AfuGeolocation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingCompletedAt

	for idx, item := range m.GetCbDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetectReOnboardingRiskRequestValidationError{
					field:  fmt.Sprintf("CbDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetEmploymentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "EmploymentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCheckDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("CheckDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("CheckDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetectReOnboardingRiskRequestValidationError{
					field:  fmt.Sprintf("CheckDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAfuAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("AfuAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("AfuAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetectReOnboardingRiskRequestValidationError{
					field:  fmt.Sprintf("AfuAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetAccountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "AccountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProfile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "Profile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProfile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "Profile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetDevicePropertyValueMap()))
		i := 0
		for key := range m.GetDevicePropertyValueMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetDevicePropertyValueMap()[key]
			_ = val

			// no validation rules for DevicePropertyValueMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, DetectReOnboardingRiskRequestValidationError{
							field:  fmt.Sprintf("DevicePropertyValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, DetectReOnboardingRiskRequestValidationError{
							field:  fmt.Sprintf("DevicePropertyValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return DetectReOnboardingRiskRequestValidationError{
						field:  fmt.Sprintf("DevicePropertyValueMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetOnboardingDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "OnboardingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "OnboardingDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOnboardingDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "OnboardingDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOldDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "OldDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskRequestValidationError{
					field:  "OldDevice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOldDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskRequestValidationError{
				field:  "OldDevice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DetectReOnboardingRiskRequestMultiError(errors)
	}

	return nil
}

// DetectReOnboardingRiskRequestMultiError is an error wrapping multiple
// validation errors returned by DetectReOnboardingRiskRequest.ValidateAll()
// if the designated constraints aren't met.
type DetectReOnboardingRiskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectReOnboardingRiskRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectReOnboardingRiskRequestMultiError) AllErrors() []error { return m }

// DetectReOnboardingRiskRequestValidationError is the validation error
// returned by DetectReOnboardingRiskRequest.Validate if the designated
// constraints aren't met.
type DetectReOnboardingRiskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectReOnboardingRiskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectReOnboardingRiskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectReOnboardingRiskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectReOnboardingRiskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectReOnboardingRiskRequestValidationError) ErrorName() string {
	return "DetectReOnboardingRiskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DetectReOnboardingRiskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectReOnboardingRiskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectReOnboardingRiskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectReOnboardingRiskRequestValidationError{}

// Validate checks the field values on DetectReOnboardingRiskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectReOnboardingRiskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectReOnboardingRiskResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DetectReOnboardingRiskResponseMultiError, or nil if none found.
func (m *DetectReOnboardingRiskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectReOnboardingRiskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Score

	// no validation rules for Threshold

	// no validation rules for RiskyUser

	if all {
		switch v := interface{}(m.GetTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectReOnboardingRiskResponseValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectReOnboardingRiskResponseValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectReOnboardingRiskResponseValidationError{
				field:  "Time",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawVendorResponse

	if len(errors) > 0 {
		return DetectReOnboardingRiskResponseMultiError(errors)
	}

	return nil
}

// DetectReOnboardingRiskResponseMultiError is an error wrapping multiple
// validation errors returned by DetectReOnboardingRiskResponse.ValidateAll()
// if the designated constraints aren't met.
type DetectReOnboardingRiskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectReOnboardingRiskResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectReOnboardingRiskResponseMultiError) AllErrors() []error { return m }

// DetectReOnboardingRiskResponseValidationError is the validation error
// returned by DetectReOnboardingRiskResponse.Validate if the designated
// constraints aren't met.
type DetectReOnboardingRiskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectReOnboardingRiskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectReOnboardingRiskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectReOnboardingRiskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectReOnboardingRiskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectReOnboardingRiskResponseValidationError) ErrorName() string {
	return "DetectReOnboardingRiskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DetectReOnboardingRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectReOnboardingRiskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectReOnboardingRiskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectReOnboardingRiskResponseValidationError{}

// Validate checks the field values on DetectRiskRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DetectRiskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectRiskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectRiskRequestMultiError, or nil if none found.
func (m *DetectRiskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectRiskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for RequestId

	// no validation rules for ReferrerActorId

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmailId

	// no validation rules for IsCreditReportPresent

	// no validation rules for CreditReportDownloadConsent

	// no validation rules for IsDevicePremium

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GmailPanNameMatchScore

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenerMailCount

	if all {
		switch v := interface{}(m.GetGeolocation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Geolocation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "Geolocation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGeolocation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "Geolocation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Threshold

	// no validation rules for HashedPhoneNumber

	if all {
		switch v := interface{}(m.GetFatherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "FatherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFatherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "FatherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMotherName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "MotherName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMotherName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "MotherName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OnboardingEkycNumberMismatch

	if all {
		switch v := interface{}(m.GetUserPanName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "UserPanName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserPanName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "UserPanName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCbDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DetectRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DetectRiskRequestValidationError{
						field:  fmt.Sprintf("CbDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DetectRiskRequestValidationError{
					field:  fmt.Sprintf("CbDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetEmploymentData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskRequestValidationError{
					field:  "EmploymentData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskRequestValidationError{
				field:  "EmploymentData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetDevicePropertyValueMap()))
		i := 0
		for key := range m.GetDevicePropertyValueMap() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetDevicePropertyValueMap()[key]
			_ = val

			// no validation rules for DevicePropertyValueMap[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, DetectRiskRequestValidationError{
							field:  fmt.Sprintf("DevicePropertyValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, DetectRiskRequestValidationError{
							field:  fmt.Sprintf("DevicePropertyValueMap[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return DetectRiskRequestValidationError{
						field:  fmt.Sprintf("DevicePropertyValueMap[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for ModelVersion

	if len(errors) > 0 {
		return DetectRiskRequestMultiError(errors)
	}

	return nil
}

// DetectRiskRequestMultiError is an error wrapping multiple validation errors
// returned by DetectRiskRequest.ValidateAll() if the designated constraints
// aren't met.
type DetectRiskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectRiskRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectRiskRequestMultiError) AllErrors() []error { return m }

// DetectRiskRequestValidationError is the validation error returned by
// DetectRiskRequest.Validate if the designated constraints aren't met.
type DetectRiskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectRiskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectRiskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectRiskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectRiskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectRiskRequestValidationError) ErrorName() string {
	return "DetectRiskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DetectRiskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectRiskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectRiskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectRiskRequestValidationError{}

// Validate checks the field values on DetectRiskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectRiskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectRiskResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectRiskResponseMultiError, or nil if none found.
func (m *DetectRiskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectRiskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Score

	// no validation rules for Threshold

	// no validation rules for RiskyUser

	if all {
		switch v := interface{}(m.GetTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectRiskResponseValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectRiskResponseValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectRiskResponseValidationError{
				field:  "Time",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawVendorResponse

	if len(errors) > 0 {
		return DetectRiskResponseMultiError(errors)
	}

	return nil
}

// DetectRiskResponseMultiError is an error wrapping multiple validation errors
// returned by DetectRiskResponse.ValidateAll() if the designated constraints
// aren't met.
type DetectRiskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectRiskResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectRiskResponseMultiError) AllErrors() []error { return m }

// DetectRiskResponseValidationError is the validation error returned by
// DetectRiskResponse.Validate if the designated constraints aren't met.
type DetectRiskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectRiskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectRiskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectRiskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectRiskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectRiskResponseValidationError) ErrorName() string {
	return "DetectRiskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DetectRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectRiskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectRiskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectRiskResponseValidationError{}

// Validate checks the field values on DetectLocationRiskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectLocationRiskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectLocationRiskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectLocationRiskRequestMultiError, or nil if none found.
func (m *DetectLocationRiskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectLocationRiskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetHeader() == nil {
		err := DetectLocationRiskRequestValidationError{
			field:  "Header",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectLocationRiskRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectLocationRiskRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectLocationRiskRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRequestId()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "RequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetLatLng() == nil {
		err := DetectLocationRiskRequestValidationError{
			field:  "LatLng",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetLatLng()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectLocationRiskRequestValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectLocationRiskRequestValidationError{
					field:  "LatLng",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatLng()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectLocationRiskRequestValidationError{
				field:  "LatLng",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetPincode()) < 1 {
		err := DetectLocationRiskRequestValidationError{
			field:  "Pincode",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DetectLocationRiskRequestMultiError(errors)
	}

	return nil
}

// DetectLocationRiskRequestMultiError is an error wrapping multiple validation
// errors returned by DetectLocationRiskRequest.ValidateAll() if the
// designated constraints aren't met.
type DetectLocationRiskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectLocationRiskRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectLocationRiskRequestMultiError) AllErrors() []error { return m }

// DetectLocationRiskRequestValidationError is the validation error returned by
// DetectLocationRiskRequest.Validate if the designated constraints aren't met.
type DetectLocationRiskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectLocationRiskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectLocationRiskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectLocationRiskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectLocationRiskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectLocationRiskRequestValidationError) ErrorName() string {
	return "DetectLocationRiskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DetectLocationRiskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectLocationRiskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectLocationRiskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectLocationRiskRequestValidationError{}

// Validate checks the field values on DetectLocationRiskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectLocationRiskResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectLocationRiskResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectLocationRiskResponseMultiError, or nil if none found.
func (m *DetectLocationRiskResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectLocationRiskResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectLocationRiskResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectLocationRiskResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectLocationRiskResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Score

	// no validation rules for RiskSeverity

	// no validation rules for ModelVersion

	// no validation rules for RawVendorResponse

	if len(errors) > 0 {
		return DetectLocationRiskResponseMultiError(errors)
	}

	return nil
}

// DetectLocationRiskResponseMultiError is an error wrapping multiple
// validation errors returned by DetectLocationRiskResponse.ValidateAll() if
// the designated constraints aren't met.
type DetectLocationRiskResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectLocationRiskResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectLocationRiskResponseMultiError) AllErrors() []error { return m }

// DetectLocationRiskResponseValidationError is the validation error returned
// by DetectLocationRiskResponse.Validate if the designated constraints aren't met.
type DetectLocationRiskResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectLocationRiskResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectLocationRiskResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectLocationRiskResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectLocationRiskResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectLocationRiskResponseValidationError) ErrorName() string {
	return "DetectLocationRiskResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DetectLocationRiskResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectLocationRiskResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectLocationRiskResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectLocationRiskResponseValidationError{}

// Validate checks the field values on AFUAttempt with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AFUAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AFUAttempt with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AFUAttemptMultiError, or
// nil if none found.
func (m *AFUAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *AFUAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OverallStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AFUAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AFUAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AFUAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AFUAttemptMultiError(errors)
	}

	return nil
}

// AFUAttemptMultiError is an error wrapping multiple validation errors
// returned by AFUAttempt.ValidateAll() if the designated constraints aren't met.
type AFUAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AFUAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AFUAttemptMultiError) AllErrors() []error { return m }

// AFUAttemptValidationError is the validation error returned by
// AFUAttempt.Validate if the designated constraints aren't met.
type AFUAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AFUAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AFUAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AFUAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AFUAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AFUAttemptValidationError) ErrorName() string { return "AFUAttemptValidationError" }

// Error satisfies the builtin error interface
func (e AFUAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAFUAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AFUAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AFUAttemptValidationError{}

// Validate checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccountInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccountInfoMultiError, or
// nil if none found.
func (m *AccountInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tier

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AccountInfoMultiError(errors)
	}

	return nil
}

// AccountInfoMultiError is an error wrapping multiple validation errors
// returned by AccountInfo.ValidateAll() if the designated constraints aren't met.
type AccountInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInfoMultiError) AllErrors() []error { return m }

// AccountInfoValidationError is the validation error returned by
// AccountInfo.Validate if the designated constraints aren't met.
type AccountInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInfoValidationError) ErrorName() string { return "AccountInfoValidationError" }

// Error satisfies the builtin error interface
func (e AccountInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInfoValidationError{}

// Validate checks the field values on OnboardingDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnboardingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnboardingDetailsMultiError, or nil if none found.
func (m *OnboardingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnboardingRiskModelScore

	if len(errors) > 0 {
		return OnboardingDetailsMultiError(errors)
	}

	return nil
}

// OnboardingDetailsMultiError is an error wrapping multiple validation errors
// returned by OnboardingDetails.ValidateAll() if the designated constraints
// aren't met.
type OnboardingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingDetailsMultiError) AllErrors() []error { return m }

// OnboardingDetailsValidationError is the validation error returned by
// OnboardingDetails.Validate if the designated constraints aren't met.
type OnboardingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingDetailsValidationError) ErrorName() string {
	return "OnboardingDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingDetailsValidationError{}
