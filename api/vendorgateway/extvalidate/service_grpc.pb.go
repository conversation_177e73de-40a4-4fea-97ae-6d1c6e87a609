// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/extvalidate/service.proto

package extvalidate

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ExternalValidate_VerifyBankAccount_FullMethodName = "/vendorgateway.extvalidate.ExternalValidate/VerifyBankAccount"
)

// ExternalValidateClient is the client API for ExternalValidate service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExternalValidateClient interface {
	// Checks if bank account exists by performing penny drop into the account
	VerifyBankAccount(ctx context.Context, in *VerifyBankAccountRequest, opts ...grpc.CallOption) (*VerifyBankAccountResponse, error)
}

type externalValidateClient struct {
	cc grpc.ClientConnInterface
}

func NewExternalValidateClient(cc grpc.ClientConnInterface) ExternalValidateClient {
	return &externalValidateClient{cc}
}

func (c *externalValidateClient) VerifyBankAccount(ctx context.Context, in *VerifyBankAccountRequest, opts ...grpc.CallOption) (*VerifyBankAccountResponse, error) {
	out := new(VerifyBankAccountResponse)
	err := c.cc.Invoke(ctx, ExternalValidate_VerifyBankAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExternalValidateServer is the server API for ExternalValidate service.
// All implementations should embed UnimplementedExternalValidateServer
// for forward compatibility
type ExternalValidateServer interface {
	// Checks if bank account exists by performing penny drop into the account
	VerifyBankAccount(context.Context, *VerifyBankAccountRequest) (*VerifyBankAccountResponse, error)
}

// UnimplementedExternalValidateServer should be embedded to have forward compatible implementations.
type UnimplementedExternalValidateServer struct {
}

func (UnimplementedExternalValidateServer) VerifyBankAccount(context.Context, *VerifyBankAccountRequest) (*VerifyBankAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyBankAccount not implemented")
}

// UnsafeExternalValidateServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExternalValidateServer will
// result in compilation errors.
type UnsafeExternalValidateServer interface {
	mustEmbedUnimplementedExternalValidateServer()
}

func RegisterExternalValidateServer(s grpc.ServiceRegistrar, srv ExternalValidateServer) {
	s.RegisterService(&ExternalValidate_ServiceDesc, srv)
}

func _ExternalValidate_VerifyBankAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyBankAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalValidateServer).VerifyBankAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalValidate_VerifyBankAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalValidateServer).VerifyBankAccount(ctx, req.(*VerifyBankAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ExternalValidate_ServiceDesc is the grpc.ServiceDesc for ExternalValidate service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExternalValidate_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.extvalidate.ExternalValidate",
	HandlerType: (*ExternalValidateServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "VerifyBankAccount",
			Handler:    _ExternalValidate_VerifyBankAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/extvalidate/service.proto",
}
