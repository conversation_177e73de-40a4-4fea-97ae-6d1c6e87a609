// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/fennel/service.proto

package fennel

import (
	rpc "github.com/epifi/be-common/api/rpc"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LogDatasetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendor gateway APIs
	// Denotes the vendor that is supposed to process this request
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Name of the dataset to which data will be added
	DatasetName string `protobuf:"bytes,2,opt,name=dataset_name,json=datasetName,proto3" json:"dataset_name,omitempty"`
	// List of stringified objects to be added in specified dataset
	DatasetPayload []string `protobuf:"bytes,3,rep,name=dataset_payload,json=datasetPayload,proto3" json:"dataset_payload,omitempty"`
	// Workflow name
	// NOTE : this is for internal purposes only and should be the same as the workflow which will be used while extracting feature sets
	Workflow string `protobuf:"bytes,4,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *LogDatasetsRequest) Reset() {
	*x = LogDatasetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogDatasetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogDatasetsRequest) ProtoMessage() {}

func (x *LogDatasetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogDatasetsRequest.ProtoReflect.Descriptor instead.
func (*LogDatasetsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_fennel_service_proto_rawDescGZIP(), []int{0}
}

func (x *LogDatasetsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *LogDatasetsRequest) GetDatasetName() string {
	if x != nil {
		return x.DatasetName
	}
	return ""
}

func (x *LogDatasetsRequest) GetDatasetPayload() []string {
	if x != nil {
		return x.DatasetPayload
	}
	return nil
}

func (x *LogDatasetsRequest) GetWorkflow() string {
	if x != nil {
		return x.Workflow
	}
	return ""
}

type LogDatasetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Response coming from vendor or in house feature store after logging
	Response string `protobuf:"bytes,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *LogDatasetsResponse) Reset() {
	*x = LogDatasetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogDatasetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogDatasetsResponse) ProtoMessage() {}

func (x *LogDatasetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogDatasetsResponse.ProtoReflect.Descriptor instead.
func (*LogDatasetsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_fennel_service_proto_rawDescGZIP(), []int{1}
}

func (x *LogDatasetsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *LogDatasetsResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

type ExtractFeatureSetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Common request header across all vendor gateway APIs
	// Denotes the vendor that is supposed to process this request
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// List of features names to extract
	// Ex: \["a", "b"]
	FeatureNameList []string `protobuf:"bytes,2,rep,name=feature_name_list,json=featureNameList,proto3" json:"feature_name_list,omitempty"`
	// Features will be extracted this identifier type can be actor_id or account_id or both ex : \["actor_id", "account_id"]
	IdentifierTypeList []IdentifierType `protobuf:"varint,3,rep,packed,name=identifier_type_list,json=identifierTypeList,proto3,enum=vendorgateway.fennel.IdentifierType" json:"identifier_type_list,omitempty"`
	// List of ids for the identifier type for which features will be extracted
	// Ex : \[{"actor_id": "AC12", "account_id": "SV12"}, {"actor_id": "AC13", "account_id": "SV13"}]
	IdentifierList []*Identifier `protobuf:"bytes,4,rep,name=identifier_list,json=identifierList,proto3" json:"identifier_list,omitempty"`
	// workflow name under which extracted features will be dumped in the s3
	Workflow string `protobuf:"bytes,5,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *ExtractFeatureSetsRequest) Reset() {
	*x = ExtractFeatureSetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractFeatureSetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractFeatureSetsRequest) ProtoMessage() {}

func (x *ExtractFeatureSetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractFeatureSetsRequest.ProtoReflect.Descriptor instead.
func (*ExtractFeatureSetsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_fennel_service_proto_rawDescGZIP(), []int{2}
}

func (x *ExtractFeatureSetsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ExtractFeatureSetsRequest) GetFeatureNameList() []string {
	if x != nil {
		return x.FeatureNameList
	}
	return nil
}

func (x *ExtractFeatureSetsRequest) GetIdentifierTypeList() []IdentifierType {
	if x != nil {
		return x.IdentifierTypeList
	}
	return nil
}

func (x *ExtractFeatureSetsRequest) GetIdentifierList() []*Identifier {
	if x != nil {
		return x.IdentifierList
	}
	return nil
}

func (x *ExtractFeatureSetsRequest) GetWorkflow() string {
	if x != nil {
		return x.Workflow
	}
	return ""
}

type ExtractFeatureSetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// List of all the output features against an identifier
	// Ex: {"name": "a", "value": \["aa1", ""aa2]}, "name": "b", "value": \["bb", "bb2"]}
	FeatureMap map[string]*structpb.Value `protobuf:"bytes,2,rep,name=feature_map,json=featureMap,proto3" json:"feature_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ExtractFeatureSetsResponse) Reset() {
	*x = ExtractFeatureSetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtractFeatureSetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtractFeatureSetsResponse) ProtoMessage() {}

func (x *ExtractFeatureSetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtractFeatureSetsResponse.ProtoReflect.Descriptor instead.
func (*ExtractFeatureSetsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_fennel_service_proto_rawDescGZIP(), []int{3}
}

func (x *ExtractFeatureSetsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ExtractFeatureSetsResponse) GetFeatureMap() map[string]*structpb.Value {
	if x != nil {
		return x.FeatureMap
	}
	return nil
}

type GetPdScoreRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header          *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ActorId         string                       `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RawCreditReport []byte                       `protobuf:"bytes,3,opt,name=raw_credit_report,json=rawCreditReport,proto3" json:"raw_credit_report,omitempty"`
}

func (x *GetPdScoreRequest) Reset() {
	*x = GetPdScoreRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdScoreRequest) ProtoMessage() {}

func (x *GetPdScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdScoreRequest.ProtoReflect.Descriptor instead.
func (*GetPdScoreRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_fennel_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetPdScoreRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetPdScoreRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetPdScoreRequest) GetRawCreditReport() []byte {
	if x != nil {
		return x.RawCreditReport
	}
	return nil
}

type GetPdScoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Score        float64     `protobuf:"fixed64,2,opt,name=score,proto3" json:"score,omitempty"`
	ModelContext string      `protobuf:"bytes,3,opt,name=model_context,json=modelContext,proto3" json:"model_context,omitempty"`
	ModelVersion string      `protobuf:"bytes,4,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
}

func (x *GetPdScoreResponse) Reset() {
	*x = GetPdScoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPdScoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPdScoreResponse) ProtoMessage() {}

func (x *GetPdScoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_fennel_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPdScoreResponse.ProtoReflect.Descriptor instead.
func (*GetPdScoreResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_fennel_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetPdScoreResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPdScoreResponse) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *GetPdScoreResponse) GetModelContext() string {
	if x != nil {
		return x.ModelContext
	}
	return ""
}

func (x *GetPdScoreResponse) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

var File_api_vendorgateway_fennel_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_fennel_service_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x66, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x1a, 0x14,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x66, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x2f, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xb2, 0x01, 0x0a, 0x12, 0x4c, 0x6f, 0x67, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x5f, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x61, 0x74,
	0x61, 0x73, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x56, 0x0a, 0x13, 0x4c, 0x6f, 0x67, 0x44, 0x61,
	0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xbc, 0x02, 0x0a, 0x19, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x53, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x56, 0x0a, 0x14, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x12, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x66, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0xfb,
	0x01, 0x0a, 0x1a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x53, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x61, 0x0a, 0x0b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x55, 0x0a, 0x0f, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x90, 0x01, 0x0a,
	0x11, 0x47, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x61, 0x77, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f,
	0x72, 0x61, 0x77, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22,
	0x99, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0xd2, 0x02, 0x0a, 0x12,
	0x46, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x12, 0x62, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x73, 0x12, 0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x66, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x4c, 0x6f, 0x67, 0x44, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x12, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x53, 0x65, 0x74, 0x73, 0x12, 0x2f, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x53, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x53, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x5f, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x27, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x66, 0x65, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x31, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x66, 0x65, 0x6e,
	0x6e, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_fennel_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_fennel_service_proto_rawDescData = file_api_vendorgateway_fennel_service_proto_rawDesc
)

func file_api_vendorgateway_fennel_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_fennel_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_fennel_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_fennel_service_proto_rawDescData)
	})
	return file_api_vendorgateway_fennel_service_proto_rawDescData
}

var file_api_vendorgateway_fennel_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_vendorgateway_fennel_service_proto_goTypes = []interface{}{
	(*LogDatasetsRequest)(nil),          // 0: vendorgateway.fennel.LogDatasetsRequest
	(*LogDatasetsResponse)(nil),         // 1: vendorgateway.fennel.LogDatasetsResponse
	(*ExtractFeatureSetsRequest)(nil),   // 2: vendorgateway.fennel.ExtractFeatureSetsRequest
	(*ExtractFeatureSetsResponse)(nil),  // 3: vendorgateway.fennel.ExtractFeatureSetsResponse
	(*GetPdScoreRequest)(nil),           // 4: vendorgateway.fennel.GetPdScoreRequest
	(*GetPdScoreResponse)(nil),          // 5: vendorgateway.fennel.GetPdScoreResponse
	nil,                                 // 6: vendorgateway.fennel.ExtractFeatureSetsResponse.FeatureMapEntry
	(*vendorgateway.RequestHeader)(nil), // 7: vendorgateway.RequestHeader
	(*rpc.Status)(nil),                  // 8: rpc.Status
	(IdentifierType)(0),                 // 9: vendorgateway.fennel.IdentifierType
	(*Identifier)(nil),                  // 10: vendorgateway.fennel.Identifier
	(*structpb.Value)(nil),              // 11: google.protobuf.Value
}
var file_api_vendorgateway_fennel_service_proto_depIdxs = []int32{
	7,  // 0: vendorgateway.fennel.LogDatasetsRequest.header:type_name -> vendorgateway.RequestHeader
	8,  // 1: vendorgateway.fennel.LogDatasetsResponse.status:type_name -> rpc.Status
	7,  // 2: vendorgateway.fennel.ExtractFeatureSetsRequest.header:type_name -> vendorgateway.RequestHeader
	9,  // 3: vendorgateway.fennel.ExtractFeatureSetsRequest.identifier_type_list:type_name -> vendorgateway.fennel.IdentifierType
	10, // 4: vendorgateway.fennel.ExtractFeatureSetsRequest.identifier_list:type_name -> vendorgateway.fennel.Identifier
	8,  // 5: vendorgateway.fennel.ExtractFeatureSetsResponse.status:type_name -> rpc.Status
	6,  // 6: vendorgateway.fennel.ExtractFeatureSetsResponse.feature_map:type_name -> vendorgateway.fennel.ExtractFeatureSetsResponse.FeatureMapEntry
	7,  // 7: vendorgateway.fennel.GetPdScoreRequest.header:type_name -> vendorgateway.RequestHeader
	8,  // 8: vendorgateway.fennel.GetPdScoreResponse.status:type_name -> rpc.Status
	11, // 9: vendorgateway.fennel.ExtractFeatureSetsResponse.FeatureMapEntry.value:type_name -> google.protobuf.Value
	0,  // 10: vendorgateway.fennel.FennelFeatureStore.LogDatasets:input_type -> vendorgateway.fennel.LogDatasetsRequest
	2,  // 11: vendorgateway.fennel.FennelFeatureStore.ExtractFeatureSets:input_type -> vendorgateway.fennel.ExtractFeatureSetsRequest
	4,  // 12: vendorgateway.fennel.FennelFeatureStore.GetPdScore:input_type -> vendorgateway.fennel.GetPdScoreRequest
	1,  // 13: vendorgateway.fennel.FennelFeatureStore.LogDatasets:output_type -> vendorgateway.fennel.LogDatasetsResponse
	3,  // 14: vendorgateway.fennel.FennelFeatureStore.ExtractFeatureSets:output_type -> vendorgateway.fennel.ExtractFeatureSetsResponse
	5,  // 15: vendorgateway.fennel.FennelFeatureStore.GetPdScore:output_type -> vendorgateway.fennel.GetPdScoreResponse
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_fennel_service_proto_init() }
func file_api_vendorgateway_fennel_service_proto_init() {
	if File_api_vendorgateway_fennel_service_proto != nil {
		return
	}
	file_api_vendorgateway_fennel_feature_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_fennel_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogDatasetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_fennel_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogDatasetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_fennel_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractFeatureSetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_fennel_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtractFeatureSetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_fennel_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdScoreRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_fennel_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPdScoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_fennel_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_fennel_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_fennel_service_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_fennel_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_fennel_service_proto = out.File
	file_api_vendorgateway_fennel_service_proto_rawDesc = nil
	file_api_vendorgateway_fennel_service_proto_goTypes = nil
	file_api_vendorgateway_fennel_service_proto_depIdxs = nil
}
