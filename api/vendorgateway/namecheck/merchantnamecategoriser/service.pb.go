// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/namecheck/merchantnamecategoriser/service.proto

package merchantnamecategoriser

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantNameCategoriserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// contains vendor to be used
	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// unique id against which each request is made
	RequestId string `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// name that is to be categorised as merchant or not
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *MerchantNameCategoriserRequest) Reset() {
	*x = MerchantNameCategoriserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantNameCategoriserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantNameCategoriserRequest) ProtoMessage() {}

func (x *MerchantNameCategoriserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantNameCategoriserRequest.ProtoReflect.Descriptor instead.
func (*MerchantNameCategoriserRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantNameCategoriserRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MerchantNameCategoriserRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *MerchantNameCategoriserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type MerchantNameCategoriserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// probability that the given name is of a merchant, values between 0 and 1
	Prob float32 `protobuf:"fixed32,2,opt,name=prob,proto3" json:"prob,omitempty"`
	// final decision that if the given name is a merchant values: 0(fail), 1(pass)
	Decision int32 `protobuf:"varint,3,opt,name=decision,proto3" json:"decision,omitempty"`
	// version of the model used for this prediction
	ModelVersion string `protobuf:"bytes,4,opt,name=model_version,json=modelVersion,proto3" json:"model_version,omitempty"`
}

func (x *MerchantNameCategoriserResponse) Reset() {
	*x = MerchantNameCategoriserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantNameCategoriserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantNameCategoriserResponse) ProtoMessage() {}

func (x *MerchantNameCategoriserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantNameCategoriserResponse.ProtoReflect.Descriptor instead.
func (*MerchantNameCategoriserResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantNameCategoriserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *MerchantNameCategoriserResponse) GetProb() float32 {
	if x != nil {
		return x.Prob
	}
	return 0
}

func (x *MerchantNameCategoriserResponse) GetDecision() int32 {
	if x != nil {
		return x.Decision
	}
	return 0
}

func (x *MerchantNameCategoriserResponse) GetModelVersion() string {
	if x != nil {
		return x.ModelVersion
	}
	return ""
}

var File_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDesc = []byte{
	0x0a, 0x41, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2f, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x73, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x73, 0x65, 0x72, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x01, 0x0a, 0x1e,
	0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x9b, 0x01, 0x0a, 0x1f, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x72, 0x6f,
	0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x72, 0x6f, 0x62, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x32, 0xd8,
	0x01, 0x0a, 0x17, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x72, 0x12, 0xbc, 0x01, 0x0a, 0x17, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x73, 0x65, 0x72, 0x12, 0x4f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x72, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x50, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x72, 0x2e, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x98, 0x01, 0x0a, 0x4a, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x2e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x73, 0x65, 0x72, 0x5a, 0x4a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x2f, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x73, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescData = file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDesc
)

func file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescData)
	})
	return file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDescData
}

var file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_goTypes = []interface{}{
	(*MerchantNameCategoriserRequest)(nil),  // 0: vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriserRequest
	(*MerchantNameCategoriserResponse)(nil), // 1: vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriserResponse
	(*vendorgateway.RequestHeader)(nil),     // 2: vendorgateway.RequestHeader
	(*rpc.Status)(nil),                      // 3: rpc.Status
}
var file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_depIdxs = []int32{
	2, // 0: vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriserRequest.header:type_name -> vendorgateway.RequestHeader
	3, // 1: vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriserResponse.status:type_name -> rpc.Status
	0, // 2: vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriser.MerchantNameCategoriser:input_type -> vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriserRequest
	1, // 3: vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriser.MerchantNameCategoriser:output_type -> vendorgateway.namecheck.merchantnamecategoriser.MerchantNameCategoriserResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_init() }
func file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_init() {
	if File_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerchantNameCategoriserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerchantNameCategoriserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto = out.File
	file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_rawDesc = nil
	file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_goTypes = nil
	file_api_vendorgateway_namecheck_merchantnamecategoriser_service_proto_depIdxs = nil
}
