//go:generate gen_sql -types=AccountStatusEnquiryResponse
syntax = "proto3";

package vendorgateway.openbanking.accounts.status;

import "api/accounts/account_type.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts/status";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.accounts.status";

message AccountStatusEnquiryRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Request id which is sent to vendor
  string request_id = 2;
  string account_number = 3;
  api.typesv2.common.PhoneNumber phone_number = 4;
}

// Represents response object to get account status
// More fields present for this API, please refer to API DOC
message AccountStatusEnquiryResponse {
  enum Status {
    OK = 0;
    // Request parameters invalid
    INVALID_ARGUMENT = 3;
    // No data found for request params
    NOT_FOUND = 5;
    // Internal Error
    INTERNAL = 13;
    // Status Code for mismatch in account number - phone number combination in Request vs the combination at CBS
    ACC_NUMBER_PHONE_MISMATCH = 106;
  }
  rpc.Status status = 1;

  enum AccountStatus {
    ACCOUNT_STATUS_UNDEFINED = 0;
    ACTIVE = 1;
    INACTIVE = 2;
    DORMANT = 3;
    CLOSED = 4;
  }
  AccountStatus account_status = 2;

  enum AccountFreezeStatus {
    ACCOUNT_FREEZE_STATUS_UNDEFINED = 0;
    FULL_FREEZE = 1;
    CREDIT_FREEZE = 2;
    DEBIT_FREEZE = 3;
  }

  AccountFreezeStatus freeze_status = 3;
  // Total lien amount marked in the Account.
  google.type.Money lien_balance = 4;
  int32 customer_id = 5;
  int32 sol_id = 6;
  string name = 7;
  // Amount remaining in the account after clearing all outstanding transactions
  google.type.Money clear_balance = 8;
  // Sum of all transactions that have not been processed yet
  google.type.Money unclear_balance = 9;
  // Freeze reason codes for additional freeze info, latest entry comes first
  repeated string freeze_reason_codes = 10;
  google.protobuf.Timestamp account_opening_date = 11;
  google.protobuf.Timestamp account_closing_date = 12;
  google.type.Money cumulative_debit_amount = 13;
  google.type.Money cumulative_credit_amount = 14;
  google.protobuf.Timestamp last_credit_transaction_date = 15;
  google.protobuf.Timestamp last_debit_transaction_date = 16;
  string last_credit_transaction_id = 17;
  string last_debit_transaction_id = 18;
  // Account holder type, whether its single, either or survivor or jointly operated
  enum HolderType {
    HOLDER_TYPE_TYPE_UNSPECIFIED = 0;
    HOLDER_TYPE_SINGLE = 1;
    HOLDER_TYPE_JOINT = 2;
    HOLDER_TYPE_EITHER_OR_SURVIVOR = 3;
  }
  HolderType holder_type = 19;
  // KYC status of account
  api.typesv2.common.BooleanEnum is_kyc_present = 20;
  // Count of signature in account, if value is 1 or more than signature exists.
  int32 sign_count = 21;
  string bank_id = 22;
  // type of account like saving account, deposit, loan account etc.
  .accounts.Type account_type = 23;
  string scheme_code = 24;
  // Dates relevant for KYC Compliance
  google.type.Date k_y_c_review_date = 25;
  google.type.Date k_y_c_due_date = 26;
  google.type.Date k_y_c_grace_period_date = 27;
  bool is_upi_lite_enabled = 28;
  google.type.Money consolidated_pending_amount = 29;
}
