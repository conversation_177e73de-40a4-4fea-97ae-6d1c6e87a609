package lien

import (
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	federalPb "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/gamma/vendorgateway/config"
)

func (r *EnquireLienRequest) ToVendorEnquireLienRequest(conf *config.Config) (*federalPb.EnquireLienRequest, error) {
	enquireReq := &federalPb.EnquireLienRequest{
		RequestType:      conf.FederalLien.LienRequestTypeEnumToValueMap[federalPb.LienRequestType_LIEN_REQUEST_TYPE_ENQUIRY.String()],
		ChannelRequestId: r.GetRequestId(),
		Channel:          conf.FederalLien.Channel,
	}
	if err := enquireReq.Validate(); err != nil {
		return nil, fmt.Errorf("failed to validate federal enquire lien request %w", err)
	}
	return enquireReq, nil
}

// federal lien apis response contains two status, request status and federal internal system status
func getEnquireLienRequestStatus(conf *config.Config, federalLienResponse *federalPb.LienResponse) (*rpc.Status, *rpc.Status, error) {
	statusCode, ok := conf.FederalLien.StatusCodeToEnumMap[federalLienResponse.GetStatus()]
	if !ok {
		return nil, nil, fmt.Errorf("failed to parse federal request status %s", federalLienResponse.GetStatus())
	}
	status, ok := EnquireLienResponse_Status_value[statusCode]
	if !ok {
		return nil, nil, fmt.Errorf("failed to parse federal request status %s", federalLienResponse.GetStatus())
	}
	cbsStatusCode, ok := conf.FederalLien.CBSStatusCodeToEnumMap[federalLienResponse.GetCbsStatus()]
	if !ok {
		cbsStatusCode = PENDING
		logger.ErrorNoCtx("failed to parse federal cbs status", zap.String(logger.CBS_STATUS, federalLienResponse.GetCbsStatus()))
	}
	cbsStatus, ok := EnquireLienResponse_Status_value[cbsStatusCode]
	if !ok {
		logger.ErrorNoCtx("failed to parse federal cbs status", zap.String(logger.CBS_STATUS, federalLienResponse.GetCbsStatus()))
	}
	return rpc.NewStatusWithoutDebug(uint32(status), federalLienResponse.GetMessage()),
		rpc.NewStatusWithoutDebug(uint32(cbsStatus), federalLienResponse.GetCbsResponse()), nil
}

func ParseEnquireLienResponse(conf *config.Config, federalLienResponse *federalPb.LienResponse) (*EnquireLienResponse, error) {
	status, cbsStatus, err := getEnquireLienRequestStatus(conf, federalLienResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to parse federal response status %w", err)
	}

	// parse lien type, throws error for unknown lien types
	lienType, err := getLienTypeEnum(conf, federalLienResponse.GetLienType())
	if err != nil || lienType == LienType_LIEN_TYPE_UNSPECIFIED {
		return nil, fmt.Errorf("failed to parse federal lien type %s %w", federalLienResponse.GetLienType(), err)
	}

	// parse lien amounts
	newAmount, _ := money.ParseString(federalLienResponse.GetNewAmountValue(), federalLienResponse.GetCurrencyCode())
	oldAmount, _ := money.ParseString(federalLienResponse.GetOldAmountValue(), federalLienResponse.GetCurrencyCode())

	// parse lien reason, throws error for unknown reason codes
	reason, _ := getReasonEnum(conf, federalLienResponse.GetReasonCode())

	// Parse lien start and end time
	startTime, _ := datetime.ParseFederalTS(federalLienResponse.GetStartDate())
	endTime, _ := datetime.ParseFederalTS(federalLienResponse.GetEndDate())

	isDeleted := stringToBoolMap[federalLienResponse.GetIsDeleted()]

	return &EnquireLienResponse{
		Status:               status,
		VendorInternalStatus: cbsStatus,
		LienResponse: &LienResponse{
			ChannelRequestId: federalLienResponse.GetChannelRequestId(),
			LienType:         lienType,
			AccountNumber:    federalLienResponse.GetAccountId(),
			NewAmount:        newAmount,
			OldAmount:        oldAmount,
			Reason:           reason,
			LienId:           federalLienResponse.GetLienId(),
			Remarks:          federalLienResponse.GetRemarks(),
			StartTimestamp:   startTime,
			EndTimestamp:     endTime,
			IsDeleted:        isDeleted,
		},
	}, nil
}
