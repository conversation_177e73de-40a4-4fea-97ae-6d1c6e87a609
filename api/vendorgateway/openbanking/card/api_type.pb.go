// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/openbanking/card/provisioning/api_type.proto

package card

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ApiType represents a specific type of API
// This is needed to convert raw status code of an API to epifi status code
type ApiType int32

const (
	ApiType_API_TYPE_UNSPECIFIED   ApiType = 0
	ApiType_CREATE_CARD            ApiType = 1
	ApiType_CARD_CREATION_ENQUIRY  ApiType = 2
	ApiType_CARD_PIN               ApiType = 3
	ApiType_CARD_CONTROL           ApiType = 4
	ApiType_CVV_ENQUIRY            ApiType = 5
	ApiType_LIMIT_ENQUIRY          ApiType = 6
	ApiType_LIMIT_UPDATE           ApiType = 7
	ApiType_DISPATCH_PHYSICAL_CARD ApiType = 8
	ApiType_PHYSICAL_CARD_ENQUIRY  ApiType = 9
	ApiType_CONSL_CARD_CONTROL     ApiType = 10
)

// Enum value maps for ApiType.
var (
	ApiType_name = map[int32]string{
		0:  "API_TYPE_UNSPECIFIED",
		1:  "CREATE_CARD",
		2:  "CARD_CREATION_ENQUIRY",
		3:  "CARD_PIN",
		4:  "CARD_CONTROL",
		5:  "CVV_ENQUIRY",
		6:  "LIMIT_ENQUIRY",
		7:  "LIMIT_UPDATE",
		8:  "DISPATCH_PHYSICAL_CARD",
		9:  "PHYSICAL_CARD_ENQUIRY",
		10: "CONSL_CARD_CONTROL",
	}
	ApiType_value = map[string]int32{
		"API_TYPE_UNSPECIFIED":   0,
		"CREATE_CARD":            1,
		"CARD_CREATION_ENQUIRY":  2,
		"CARD_PIN":               3,
		"CARD_CONTROL":           4,
		"CVV_ENQUIRY":            5,
		"LIMIT_ENQUIRY":          6,
		"LIMIT_UPDATE":           7,
		"DISPATCH_PHYSICAL_CARD": 8,
		"PHYSICAL_CARD_ENQUIRY":  9,
		"CONSL_CARD_CONTROL":     10,
	}
)

func (x ApiType) Enum() *ApiType {
	p := new(ApiType)
	*p = x
	return p
}

func (x ApiType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApiType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_enumTypes[0].Descriptor()
}

func (ApiType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_enumTypes[0]
}

func (x ApiType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApiType.Descriptor instead.
func (ApiType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescGZIP(), []int{0}
}

var File_api_vendorgateway_openbanking_card_provisioning_api_type_proto protoreflect.FileDescriptor

var file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x61, 0x72, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e,
	0x67, 0x2f, 0x61, 0x70, 0x69, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x2b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72, 0x64,
	0x2e, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x2a, 0xf4, 0x01,
	0x0a, 0x07, 0x41, 0x70, 0x69, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x50, 0x49,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x10, 0x02, 0x12,
	0x0c, 0x0a, 0x08, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x49, 0x4e, 0x10, 0x03, 0x12, 0x10, 0x0a,
	0x0c, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x10, 0x04, 0x12,
	0x0f, 0x0a, 0x0b, 0x43, 0x56, 0x56, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x10, 0x05,
	0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52,
	0x59, 0x10, 0x06, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x49, 0x53, 0x50, 0x41, 0x54, 0x43,
	0x48, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x08, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x45, 0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x10, 0x09, 0x12, 0x16, 0x0a, 0x12,
	0x43, 0x4f, 0x4e, 0x53, 0x4c, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52,
	0x4f, 0x4c, 0x10, 0x0a, 0x42, 0x76, 0x0a, 0x39, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x61, 0x72,
	0x64, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e,
	0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescOnce sync.Once
	file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescData = file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDesc
)

func file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescData)
	})
	return file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDescData
}

var file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_goTypes = []interface{}{
	(ApiType)(0), // 0: vendorgateway.openbanking.card.provisioning.ApiType
}
var file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_init() }
func file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_init() {
	if File_api_vendorgateway_openbanking_card_provisioning_api_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_openbanking_card_provisioning_api_type_proto = out.File
	file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_rawDesc = nil
	file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_goTypes = nil
	file_api_vendorgateway_openbanking_card_provisioning_api_type_proto_depIdxs = nil
}
