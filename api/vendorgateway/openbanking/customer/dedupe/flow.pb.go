// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/openbanking/customer/dedupe/flow.proto

package dedupe

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Flow int32

const (
	Flow_FLOW_UNSPECIFIED Flow = 0
	// This flow signifies when dedupe check is called during non resident user onboarding
	Flow_FLOW_NON_RESIDENT_ONBOARDING Flow = 1
)

// Enum value maps for Flow.
var (
	Flow_name = map[int32]string{
		0: "FLOW_UNSPECIFIED",
		1: "FLOW_NON_RESIDENT_ONBOARDING",
	}
	Flow_value = map[string]int32{
		"FLOW_UNSPECIFIED":             0,
		"FLOW_NON_RESIDENT_ONBOARDING": 1,
	}
)

func (x Flow) Enum() *Flow {
	p := new(Flow)
	*p = x
	return p
}

func (x Flow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Flow) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_enumTypes[0].Descriptor()
}

func (Flow) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_enumTypes[0]
}

func (x Flow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Flow.Descriptor instead.
func (Flow) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescGZIP(), []int{0}
}

var File_api_vendorgateway_openbanking_customer_dedupe_flow_proto protoreflect.FileDescriptor

var file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDesc = []byte{
	0x0a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x2f,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x29, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x64,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x2a, 0x3e, 0x0a, 0x04, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x14, 0x0a,
	0x10, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x53, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x01, 0x42, 0x8c, 0x01, 0x0a, 0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5a, 0x44,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x64, 0x65,
	0x64, 0x75, 0x70, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescOnce sync.Once
	file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescData = file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDesc
)

func file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescData)
	})
	return file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDescData
}

var file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_goTypes = []interface{}{
	(Flow)(0), // 0: vendorgateway.openbanking.customer.dedupe.Flow
}
var file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_init() }
func file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_init() {
	if File_api_vendorgateway_openbanking_customer_dedupe_flow_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_openbanking_customer_dedupe_flow_proto = out.File
	file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_rawDesc = nil
	file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_goTypes = nil
	file_api_vendorgateway_openbanking_customer_dedupe_flow_proto_depIdxs = nil
}
