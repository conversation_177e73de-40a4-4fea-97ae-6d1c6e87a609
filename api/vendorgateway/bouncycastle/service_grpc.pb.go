// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/bouncycastle/service.proto

package bouncycastle

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	BouncyCastle_GenerateKeyPair_FullMethodName      = "/vendorgateway.bouncycastle.BouncyCastle/GenerateKeyPair"
	BouncyCastle_GetSharedSecret_FullMethodName      = "/vendorgateway.bouncycastle.BouncyCastle/GetSharedSecret"
	BouncyCastle_DecryptData_FullMethodName          = "/vendorgateway.bouncycastle.BouncyCastle/DecryptData"
	BouncyCastle_CkycEncryptAndSign_FullMethodName   = "/vendorgateway.bouncycastle.BouncyCastle/CkycEncryptAndSign"
	BouncyCastle_CkycVerifyAndDecrypt_FullMethodName = "/vendorgateway.bouncycastle.BouncyCastle/CkycVerifyAndDecrypt"
)

// BouncyCastleClient is the client API for BouncyCastle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BouncyCastleClient interface {
	// Generate a public private key pair
	GenerateKeyPair(ctx context.Context, in *GenerateKeyPairRequest, opts ...grpc.CallOption) (*GenerateKeyPairResponse, error)
	// Get shared secret using our private key and remote public key
	GetSharedSecret(ctx context.Context, in *GetSharedSecretRequest, opts ...grpc.CallOption) (*GetSharedSecretResponse, error)
	// Decrypt data using our private, two nonces and remote public key
	// returns base64 decoded data
	DecryptData(ctx context.Context, in *DecryptDataRequest, opts ...grpc.CallOption) (*DecryptDataResponse, error)
	// encrypts and signs marshalled xml data
	CkycEncryptAndSign(ctx context.Context, in *CkycEncryptAndSignRequest, opts ...grpc.CallOption) (*CkycEncryptAndSignResponse, error)
	// verifies and decrypts marshalled xml data
	CkycVerifyAndDecrypt(ctx context.Context, in *CkycVerifyAndDecryptRequest, opts ...grpc.CallOption) (*CkycVerifyAndDecryptResponse, error)
}

type bouncyCastleClient struct {
	cc grpc.ClientConnInterface
}

func NewBouncyCastleClient(cc grpc.ClientConnInterface) BouncyCastleClient {
	return &bouncyCastleClient{cc}
}

func (c *bouncyCastleClient) GenerateKeyPair(ctx context.Context, in *GenerateKeyPairRequest, opts ...grpc.CallOption) (*GenerateKeyPairResponse, error) {
	out := new(GenerateKeyPairResponse)
	err := c.cc.Invoke(ctx, BouncyCastle_GenerateKeyPair_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bouncyCastleClient) GetSharedSecret(ctx context.Context, in *GetSharedSecretRequest, opts ...grpc.CallOption) (*GetSharedSecretResponse, error) {
	out := new(GetSharedSecretResponse)
	err := c.cc.Invoke(ctx, BouncyCastle_GetSharedSecret_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bouncyCastleClient) DecryptData(ctx context.Context, in *DecryptDataRequest, opts ...grpc.CallOption) (*DecryptDataResponse, error) {
	out := new(DecryptDataResponse)
	err := c.cc.Invoke(ctx, BouncyCastle_DecryptData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bouncyCastleClient) CkycEncryptAndSign(ctx context.Context, in *CkycEncryptAndSignRequest, opts ...grpc.CallOption) (*CkycEncryptAndSignResponse, error) {
	out := new(CkycEncryptAndSignResponse)
	err := c.cc.Invoke(ctx, BouncyCastle_CkycEncryptAndSign_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bouncyCastleClient) CkycVerifyAndDecrypt(ctx context.Context, in *CkycVerifyAndDecryptRequest, opts ...grpc.CallOption) (*CkycVerifyAndDecryptResponse, error) {
	out := new(CkycVerifyAndDecryptResponse)
	err := c.cc.Invoke(ctx, BouncyCastle_CkycVerifyAndDecrypt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BouncyCastleServer is the server API for BouncyCastle service.
// All implementations should embed UnimplementedBouncyCastleServer
// for forward compatibility
type BouncyCastleServer interface {
	// Generate a public private key pair
	GenerateKeyPair(context.Context, *GenerateKeyPairRequest) (*GenerateKeyPairResponse, error)
	// Get shared secret using our private key and remote public key
	GetSharedSecret(context.Context, *GetSharedSecretRequest) (*GetSharedSecretResponse, error)
	// Decrypt data using our private, two nonces and remote public key
	// returns base64 decoded data
	DecryptData(context.Context, *DecryptDataRequest) (*DecryptDataResponse, error)
	// encrypts and signs marshalled xml data
	CkycEncryptAndSign(context.Context, *CkycEncryptAndSignRequest) (*CkycEncryptAndSignResponse, error)
	// verifies and decrypts marshalled xml data
	CkycVerifyAndDecrypt(context.Context, *CkycVerifyAndDecryptRequest) (*CkycVerifyAndDecryptResponse, error)
}

// UnimplementedBouncyCastleServer should be embedded to have forward compatible implementations.
type UnimplementedBouncyCastleServer struct {
}

func (UnimplementedBouncyCastleServer) GenerateKeyPair(context.Context, *GenerateKeyPairRequest) (*GenerateKeyPairResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateKeyPair not implemented")
}
func (UnimplementedBouncyCastleServer) GetSharedSecret(context.Context, *GetSharedSecretRequest) (*GetSharedSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSharedSecret not implemented")
}
func (UnimplementedBouncyCastleServer) DecryptData(context.Context, *DecryptDataRequest) (*DecryptDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DecryptData not implemented")
}
func (UnimplementedBouncyCastleServer) CkycEncryptAndSign(context.Context, *CkycEncryptAndSignRequest) (*CkycEncryptAndSignResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CkycEncryptAndSign not implemented")
}
func (UnimplementedBouncyCastleServer) CkycVerifyAndDecrypt(context.Context, *CkycVerifyAndDecryptRequest) (*CkycVerifyAndDecryptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CkycVerifyAndDecrypt not implemented")
}

// UnsafeBouncyCastleServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BouncyCastleServer will
// result in compilation errors.
type UnsafeBouncyCastleServer interface {
	mustEmbedUnimplementedBouncyCastleServer()
}

func RegisterBouncyCastleServer(s grpc.ServiceRegistrar, srv BouncyCastleServer) {
	s.RegisterService(&BouncyCastle_ServiceDesc, srv)
}

func _BouncyCastle_GenerateKeyPair_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateKeyPairRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BouncyCastleServer).GenerateKeyPair(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BouncyCastle_GenerateKeyPair_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BouncyCastleServer).GenerateKeyPair(ctx, req.(*GenerateKeyPairRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BouncyCastle_GetSharedSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSharedSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BouncyCastleServer).GetSharedSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BouncyCastle_GetSharedSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BouncyCastleServer).GetSharedSecret(ctx, req.(*GetSharedSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BouncyCastle_DecryptData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DecryptDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BouncyCastleServer).DecryptData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BouncyCastle_DecryptData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BouncyCastleServer).DecryptData(ctx, req.(*DecryptDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BouncyCastle_CkycEncryptAndSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CkycEncryptAndSignRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BouncyCastleServer).CkycEncryptAndSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BouncyCastle_CkycEncryptAndSign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BouncyCastleServer).CkycEncryptAndSign(ctx, req.(*CkycEncryptAndSignRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BouncyCastle_CkycVerifyAndDecrypt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CkycVerifyAndDecryptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BouncyCastleServer).CkycVerifyAndDecrypt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BouncyCastle_CkycVerifyAndDecrypt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BouncyCastleServer).CkycVerifyAndDecrypt(ctx, req.(*CkycVerifyAndDecryptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BouncyCastle_ServiceDesc is the grpc.ServiceDesc for BouncyCastle service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BouncyCastle_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.bouncycastle.BouncyCastle",
	HandlerType: (*BouncyCastleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateKeyPair",
			Handler:    _BouncyCastle_GenerateKeyPair_Handler,
		},
		{
			MethodName: "GetSharedSecret",
			Handler:    _BouncyCastle_GetSharedSecret_Handler,
		},
		{
			MethodName: "DecryptData",
			Handler:    _BouncyCastle_DecryptData_Handler,
		},
		{
			MethodName: "CkycEncryptAndSign",
			Handler:    _BouncyCastle_CkycEncryptAndSign_Handler,
		},
		{
			MethodName: "CkycVerifyAndDecrypt",
			Handler:    _BouncyCastle_CkycVerifyAndDecrypt_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/bouncycastle/service.proto",
}
