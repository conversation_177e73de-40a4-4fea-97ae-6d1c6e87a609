syntax = "proto3";

package savings.activity;

option go_package = "github.com/epifi/gamma/api/savings/activity";
option java_package = "com.github.epifi.gamma.api.savings.activity";

import "api/celestial/activity/header.proto";

/*
  UpdateSchemeAtVendorRequest is used to update the Latest Scheme on vendor when we
  received the event for tiering-tier-update-topic
 */
message UpdateSchemeAtVendorRequest{
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message UpdateSchemeAtVendorResponse{
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

/*
  EnquireSchemeAtVendor is used to poll the vendor to check whether scheme change in succeeded or not
 */
message EnquireSchemeAtVendorRequest{
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message EnquireSchemeAtVendorResponse{
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

/*
  UpdateSchemeInDB is used to update the latest scheme on savings db after scheme is updated on vendor
 */
message UpdateSchemeInDBRequest{
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message UpdateSchemeInDBResponse{
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

/*
  WorkflowDecisionOrchestrator will decide whether the current workflow needs to retry, continue or terminate
 */
message WorkflowDecisionOrchestratorRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message WorkflowDecisionOrchestratorResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

/*
  RemoveRequestIdFromSKUInfo will remove the request ids of  successful workflow
 */
message RemoveRequestIdFromSKUInfoRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message RemoveRequestIdFromSKUInfoResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}
