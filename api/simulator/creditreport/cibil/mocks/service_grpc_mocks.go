// Code generated by MockGen. DO NOT EDIT.
// Source: api/./simulator/creditreport/cibil/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	cibil "github.com/epifi/gamma/api/vendors/cibil"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCibilClient is a mock of CibilClient interface.
type MockCibilClient struct {
	ctrl     *gomock.Controller
	recorder *MockCibilClientMockRecorder
}

// MockCibilClientMockRecorder is the mock recorder for MockCibilClient.
type MockCibilClientMockRecorder struct {
	mock *MockCibilClient
}

// NewMockCibilClient creates a new mock instance.
func NewMockCibilClient(ctrl *gomock.Controller) *MockCibilClient {
	mock := &MockCibilClient{ctrl: ctrl}
	mock.recorder = &MockCibilClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCibilClient) EXPECT() *MockCibilClientMockRecorder {
	return m.recorder
}

// FulfillOffer mocks base method.
func (m *MockCibilClient) FulfillOffer(ctx context.Context, in *cibil.AtlasFulfillOfferRequest, opts ...grpc.CallOption) (*cibil.AtlasFulfillOfferResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FulfillOffer", varargs...)
	ret0, _ := ret[0].(*cibil.AtlasFulfillOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FulfillOffer indicates an expected call of FulfillOffer.
func (mr *MockCibilClientMockRecorder) FulfillOffer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FulfillOffer", reflect.TypeOf((*MockCibilClient)(nil).FulfillOffer), varargs...)
}

// GetAuthenticationQuestions mocks base method.
func (m *MockCibilClient) GetAuthenticationQuestions(ctx context.Context, in *cibil.AtlasGetAuthenticationQuestionsRequest, opts ...grpc.CallOption) (*cibil.AtlasGetAuthenticationQuestionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAuthenticationQuestions", varargs...)
	ret0, _ := ret[0].(*cibil.AtlasGetAuthenticationQuestionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthenticationQuestions indicates an expected call of GetAuthenticationQuestions.
func (mr *MockCibilClientMockRecorder) GetAuthenticationQuestions(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthenticationQuestions", reflect.TypeOf((*MockCibilClient)(nil).GetAuthenticationQuestions), varargs...)
}

// GetCustomerAssets mocks base method.
func (m *MockCibilClient) GetCustomerAssets(ctx context.Context, in *cibil.AtlasGetCustomerAssetsRequest, opts ...grpc.CallOption) (*cibil.AtlasGetCustomerAssetsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCustomerAssets", varargs...)
	ret0, _ := ret[0].(*cibil.AtlasGetCustomerAssetsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerAssets indicates an expected call of GetCustomerAssets.
func (mr *MockCibilClientMockRecorder) GetCustomerAssets(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerAssets", reflect.TypeOf((*MockCibilClient)(nil).GetCustomerAssets), varargs...)
}

// GetProductWebToken mocks base method.
func (m *MockCibilClient) GetProductWebToken(ctx context.Context, in *cibil.AtlasGetProductWebTokenRequest, opts ...grpc.CallOption) (*cibil.AtlasGetProductWebTokenResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetProductWebToken", varargs...)
	ret0, _ := ret[0].(*cibil.AtlasGetProductWebTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductWebToken indicates an expected call of GetProductWebToken.
func (mr *MockCibilClientMockRecorder) GetProductWebToken(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductWebToken", reflect.TypeOf((*MockCibilClient)(nil).GetProductWebToken), varargs...)
}

// Ping mocks base method.
func (m *MockCibilClient) Ping(ctx context.Context, in *cibil.AtlasPingRequest, opts ...grpc.CallOption) (*cibil.AtlasPingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Ping", varargs...)
	ret0, _ := ret[0].(*cibil.AtlasPingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Ping indicates an expected call of Ping.
func (mr *MockCibilClientMockRecorder) Ping(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockCibilClient)(nil).Ping), varargs...)
}

// VerifyAuthenticationAnswers mocks base method.
func (m *MockCibilClient) VerifyAuthenticationAnswers(ctx context.Context, in *cibil.AtlasVerifyAuthenticationAnswersRequest, opts ...grpc.CallOption) (*cibil.AtlasVerifyAuthenticationAnswersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyAuthenticationAnswers", varargs...)
	ret0, _ := ret[0].(*cibil.AtlasVerifyAuthenticationAnswersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAuthenticationAnswers indicates an expected call of VerifyAuthenticationAnswers.
func (mr *MockCibilClientMockRecorder) VerifyAuthenticationAnswers(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAuthenticationAnswers", reflect.TypeOf((*MockCibilClient)(nil).VerifyAuthenticationAnswers), varargs...)
}

// MockCibilServer is a mock of CibilServer interface.
type MockCibilServer struct {
	ctrl     *gomock.Controller
	recorder *MockCibilServerMockRecorder
}

// MockCibilServerMockRecorder is the mock recorder for MockCibilServer.
type MockCibilServerMockRecorder struct {
	mock *MockCibilServer
}

// NewMockCibilServer creates a new mock instance.
func NewMockCibilServer(ctrl *gomock.Controller) *MockCibilServer {
	mock := &MockCibilServer{ctrl: ctrl}
	mock.recorder = &MockCibilServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCibilServer) EXPECT() *MockCibilServerMockRecorder {
	return m.recorder
}

// FulfillOffer mocks base method.
func (m *MockCibilServer) FulfillOffer(arg0 context.Context, arg1 *cibil.AtlasFulfillOfferRequest) (*cibil.AtlasFulfillOfferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FulfillOffer", arg0, arg1)
	ret0, _ := ret[0].(*cibil.AtlasFulfillOfferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FulfillOffer indicates an expected call of FulfillOffer.
func (mr *MockCibilServerMockRecorder) FulfillOffer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FulfillOffer", reflect.TypeOf((*MockCibilServer)(nil).FulfillOffer), arg0, arg1)
}

// GetAuthenticationQuestions mocks base method.
func (m *MockCibilServer) GetAuthenticationQuestions(arg0 context.Context, arg1 *cibil.AtlasGetAuthenticationQuestionsRequest) (*cibil.AtlasGetAuthenticationQuestionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthenticationQuestions", arg0, arg1)
	ret0, _ := ret[0].(*cibil.AtlasGetAuthenticationQuestionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthenticationQuestions indicates an expected call of GetAuthenticationQuestions.
func (mr *MockCibilServerMockRecorder) GetAuthenticationQuestions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthenticationQuestions", reflect.TypeOf((*MockCibilServer)(nil).GetAuthenticationQuestions), arg0, arg1)
}

// GetCustomerAssets mocks base method.
func (m *MockCibilServer) GetCustomerAssets(arg0 context.Context, arg1 *cibil.AtlasGetCustomerAssetsRequest) (*cibil.AtlasGetCustomerAssetsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerAssets", arg0, arg1)
	ret0, _ := ret[0].(*cibil.AtlasGetCustomerAssetsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerAssets indicates an expected call of GetCustomerAssets.
func (mr *MockCibilServerMockRecorder) GetCustomerAssets(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerAssets", reflect.TypeOf((*MockCibilServer)(nil).GetCustomerAssets), arg0, arg1)
}

// GetProductWebToken mocks base method.
func (m *MockCibilServer) GetProductWebToken(arg0 context.Context, arg1 *cibil.AtlasGetProductWebTokenRequest) (*cibil.AtlasGetProductWebTokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductWebToken", arg0, arg1)
	ret0, _ := ret[0].(*cibil.AtlasGetProductWebTokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductWebToken indicates an expected call of GetProductWebToken.
func (mr *MockCibilServerMockRecorder) GetProductWebToken(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductWebToken", reflect.TypeOf((*MockCibilServer)(nil).GetProductWebToken), arg0, arg1)
}

// Ping mocks base method.
func (m *MockCibilServer) Ping(arg0 context.Context, arg1 *cibil.AtlasPingRequest) (*cibil.AtlasPingResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping", arg0, arg1)
	ret0, _ := ret[0].(*cibil.AtlasPingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Ping indicates an expected call of Ping.
func (mr *MockCibilServerMockRecorder) Ping(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockCibilServer)(nil).Ping), arg0, arg1)
}

// VerifyAuthenticationAnswers mocks base method.
func (m *MockCibilServer) VerifyAuthenticationAnswers(arg0 context.Context, arg1 *cibil.AtlasVerifyAuthenticationAnswersRequest) (*cibil.AtlasVerifyAuthenticationAnswersResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyAuthenticationAnswers", arg0, arg1)
	ret0, _ := ret[0].(*cibil.AtlasVerifyAuthenticationAnswersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyAuthenticationAnswers indicates an expected call of VerifyAuthenticationAnswers.
func (mr *MockCibilServerMockRecorder) VerifyAuthenticationAnswers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyAuthenticationAnswers", reflect.TypeOf((*MockCibilServer)(nil).VerifyAuthenticationAnswers), arg0, arg1)
}

// MockUnsafeCibilServer is a mock of UnsafeCibilServer interface.
type MockUnsafeCibilServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCibilServerMockRecorder
}

// MockUnsafeCibilServerMockRecorder is the mock recorder for MockUnsafeCibilServer.
type MockUnsafeCibilServerMockRecorder struct {
	mock *MockUnsafeCibilServer
}

// NewMockUnsafeCibilServer creates a new mock instance.
func NewMockUnsafeCibilServer(ctrl *gomock.Controller) *MockUnsafeCibilServer {
	mock := &MockUnsafeCibilServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCibilServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCibilServer) EXPECT() *MockUnsafeCibilServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCibilServer mocks base method.
func (m *MockUnsafeCibilServer) mustEmbedUnimplementedCibilServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCibilServer")
}

// mustEmbedUnimplementedCibilServer indicates an expected call of mustEmbedUnimplementedCibilServer.
func (mr *MockUnsafeCibilServerMockRecorder) mustEmbedUnimplementedCibilServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCibilServer", reflect.TypeOf((*MockUnsafeCibilServer)(nil).mustEmbedUnimplementedCibilServer))
}
