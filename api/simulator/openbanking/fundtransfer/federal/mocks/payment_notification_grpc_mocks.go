// Code generated by MockGen. DO NOT EDIT.
// Source: api/simulator/openbanking/fundtransfer/federal/payment_notification_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	federal "github.com/epifi/gamma/api/simulator/openbanking/fundtransfer/federal"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNotificationConsumerClient is a mock of NotificationConsumerClient interface.
type MockNotificationConsumerClient struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationConsumerClientMockRecorder
}

// MockNotificationConsumerClientMockRecorder is the mock recorder for MockNotificationConsumerClient.
type MockNotificationConsumerClientMockRecorder struct {
	mock *MockNotificationConsumerClient
}

// NewMockNotificationConsumerClient creates a new mock instance.
func NewMockNotificationConsumerClient(ctrl *gomock.Controller) *MockNotificationConsumerClient {
	mock := &MockNotificationConsumerClient{ctrl: ctrl}
	mock.recorder = &MockNotificationConsumerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationConsumerClient) EXPECT() *MockNotificationConsumerClientMockRecorder {
	return m.recorder
}

// ProcessInboundNotification mocks base method.
func (m *MockNotificationConsumerClient) ProcessInboundNotification(ctx context.Context, in *federal.ProcessInboundNotificationRequest, opts ...grpc.CallOption) (*federal.ProcessInboundNotificationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProcessInboundNotification", varargs...)
	ret0, _ := ret[0].(*federal.ProcessInboundNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessInboundNotification indicates an expected call of ProcessInboundNotification.
func (mr *MockNotificationConsumerClientMockRecorder) ProcessInboundNotification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessInboundNotification", reflect.TypeOf((*MockNotificationConsumerClient)(nil).ProcessInboundNotification), varargs...)
}

// MockNotificationConsumerServer is a mock of NotificationConsumerServer interface.
type MockNotificationConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationConsumerServerMockRecorder
}

// MockNotificationConsumerServerMockRecorder is the mock recorder for MockNotificationConsumerServer.
type MockNotificationConsumerServerMockRecorder struct {
	mock *MockNotificationConsumerServer
}

// NewMockNotificationConsumerServer creates a new mock instance.
func NewMockNotificationConsumerServer(ctrl *gomock.Controller) *MockNotificationConsumerServer {
	mock := &MockNotificationConsumerServer{ctrl: ctrl}
	mock.recorder = &MockNotificationConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationConsumerServer) EXPECT() *MockNotificationConsumerServerMockRecorder {
	return m.recorder
}

// ProcessInboundNotification mocks base method.
func (m *MockNotificationConsumerServer) ProcessInboundNotification(arg0 context.Context, arg1 *federal.ProcessInboundNotificationRequest) (*federal.ProcessInboundNotificationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessInboundNotification", arg0, arg1)
	ret0, _ := ret[0].(*federal.ProcessInboundNotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessInboundNotification indicates an expected call of ProcessInboundNotification.
func (mr *MockNotificationConsumerServerMockRecorder) ProcessInboundNotification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessInboundNotification", reflect.TypeOf((*MockNotificationConsumerServer)(nil).ProcessInboundNotification), arg0, arg1)
}

// MockUnsafeNotificationConsumerServer is a mock of UnsafeNotificationConsumerServer interface.
type MockUnsafeNotificationConsumerServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNotificationConsumerServerMockRecorder
}

// MockUnsafeNotificationConsumerServerMockRecorder is the mock recorder for MockUnsafeNotificationConsumerServer.
type MockUnsafeNotificationConsumerServerMockRecorder struct {
	mock *MockUnsafeNotificationConsumerServer
}

// NewMockUnsafeNotificationConsumerServer creates a new mock instance.
func NewMockUnsafeNotificationConsumerServer(ctrl *gomock.Controller) *MockUnsafeNotificationConsumerServer {
	mock := &MockUnsafeNotificationConsumerServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNotificationConsumerServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNotificationConsumerServer) EXPECT() *MockUnsafeNotificationConsumerServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNotificationConsumerServer mocks base method.
func (m *MockUnsafeNotificationConsumerServer) mustEmbedUnimplementedNotificationConsumerServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNotificationConsumerServer")
}

// mustEmbedUnimplementedNotificationConsumerServer indicates an expected call of mustEmbedUnimplementedNotificationConsumerServer.
func (mr *MockUnsafeNotificationConsumerServerMockRecorder) mustEmbedUnimplementedNotificationConsumerServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNotificationConsumerServer", reflect.TypeOf((*MockUnsafeNotificationConsumerServer)(nil).mustEmbedUnimplementedNotificationConsumerServer))
}
