// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/segment/service.proto

package segment

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SegmentationService_CreateOrGetSegment_FullMethodName             = "/segment.SegmentationService/CreateOrGetSegment"
	SegmentationService_DeleteSegment_FullMethodName                  = "/segment.SegmentationService/DeleteSegment"
	SegmentationService_IsMember_FullMethodName                       = "/segment.SegmentationService/IsMember"
	SegmentationService_IsMemberOfExpressions_FullMethodName          = "/segment.SegmentationService/IsMemberOfExpressions"
	SegmentationService_GetMembers_FullMethodName                     = "/segment.SegmentationService/GetMembers"
	SegmentationService_PushPinpointSegmentIds_FullMethodName         = "/segment.SegmentationService/PushPinpointSegmentIds"
	SegmentationService_GetNumberOfUsersInSegment_FullMethodName      = "/segment.SegmentationService/GetNumberOfUsersInSegment"
	SegmentationService_GetSegmentTypes_FullMethodName                = "/segment.SegmentationService/GetSegmentTypes"
	SegmentationService_AreSegmentsAvailable_FullMethodName           = "/segment.SegmentationService/AreSegmentsAvailable"
	SegmentationService_UpdateSegment_FullMethodName                  = "/segment.SegmentationService/UpdateSegment"
	SegmentationService_CreateSegmentMetadata_FullMethodName          = "/segment.SegmentationService/CreateSegmentMetadata"
	SegmentationService_UpdateSegmentMetadata_FullMethodName          = "/segment.SegmentationService/UpdateSegmentMetadata"
	SegmentationService_GetSegmentsBetweenUpdatedAt_FullMethodName    = "/segment.SegmentationService/GetSegmentsBetweenUpdatedAt"
	SegmentationService_GetNumberOfUsersInSegmentQuery_FullMethodName = "/segment.SegmentationService/GetNumberOfUsersInSegmentQuery"
	SegmentationService_GetActiveDynamicSegmentCount_FullMethodName   = "/segment.SegmentationService/GetActiveDynamicSegmentCount"
	SegmentationService_GetSegmentMetadata_FullMethodName             = "/segment.SegmentationService/GetSegmentMetadata"
	SegmentationService_GetSegmentEntryTimeForUser_FullMethodName     = "/segment.SegmentationService/GetSegmentEntryTimeForUser"
	SegmentationService_GetUsersInSegmentOnDate_FullMethodName        = "/segment.SegmentationService/GetUsersInSegmentOnDate"
)

// SegmentationServiceClient is the client API for SegmentationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SegmentationServiceClient interface {
	// To insert provider segment data and map it to internal segment id in segment database
	// segment_provider_id will be generated for provider_name based on provider_metadata
	CreateOrGetSegment(ctx context.Context, in *CreateOrGetSegmentRequest, opts ...grpc.CallOption) (*CreateOrGetSegmentResponse, error)
	// To delete segment_instance_id entry from segment->timestamps sorted set and corresponding segmentInstance->users set
	// If segment_instance_ids is nil, entire segment->timestamps sorted set and all segmentInstance->users sets are deleted
	DeleteSegment(ctx context.Context, in *DeleteSegmentRequest, opts ...grpc.CallOption) (*DeleteSegmentResponse, error)
	// To check if actor belongs to segment(s) at latest timestamp in db before given timestamp
	IsMember(ctx context.Context, in *IsMemberRequest, opts ...grpc.CallOption) (*IsMemberResponse, error)
	// To check if actor belongs to logical expression of segments(s) at latest timestamp in db before given timestamp
	// for eg - IsMember('segment-1') && !IsMember('segment-2')
	IsMemberOfExpressions(ctx context.Context, in *IsMemberOfExpressionsRequest, opts ...grpc.CallOption) (*IsMemberOfExpressionsResponse, error)
	// To get list of all actors belonging to a segment at latest timestamp in db before given timestamp
	// before_token in PageContextRequest should not be specified since this is a move-forward paginated call
	// page_size in PageContextRequest is just a hint for response size and does not guarantee the elements returned
	GetMembers(ctx context.Context, in *GetMembersRequest, opts ...grpc.CallOption) (*GetMembersResponse, error)
	// To get all segment ids from pinpoint and push them to trigger-segment-export queue
	PushPinpointSegmentIds(ctx context.Context, in *PushPinpointSegmentIdsRequest, opts ...grpc.CallOption) (*PushPinpointSegmentIdsResponse, error)
	// To get number of users in a segment
	GetNumberOfUsersInSegment(ctx context.Context, in *GetNumberOfUsersInSegmentRequest, opts ...grpc.CallOption) (*GetNumberOfUsersInSegmentResponse, error)
	// To get types of multiple segments
	GetSegmentTypes(ctx context.Context, in *GetSegmentTypesRequest, opts ...grpc.CallOption) (*GetSegmentTypesResponse, error)
	// To get availability of segments from PGDB in bulk
	AreSegmentsAvailable(ctx context.Context, in *AreSegmentsAvailableRequest, opts ...grpc.CallOption) (*AreSegmentsAvailableResponse, error)
	// To update segment details like export_till time, etc
	UpdateSegment(ctx context.Context, in *UpdateSegmentRequest, opts ...grpc.CallOption) (*UpdateSegmentResponse, error)
	// To add segment metadata details for segmentation service
	CreateSegmentMetadata(ctx context.Context, in *CreateSegmentMetadataRequest, opts ...grpc.CallOption) (*CreateSegmentMetadataResponse, error)
	// To update segment metadata details like segment type, approval status, query filter and segment name
	UpdateSegmentMetadata(ctx context.Context, in *UpdateSegmentMetadataRequest, opts ...grpc.CallOption) (*UpdateSegmentMetadataResponse, error)
	// Get Segment metadata details for approval and update segment
	GetSegmentsBetweenUpdatedAt(ctx context.Context, in *GetSegmentsBetweenUpdatedAtRequest, opts ...grpc.CallOption) (*GetSegmentsBetweenUpdatedAtResponse, error)
	// Get segment count on the basis of provided query filter
	GetNumberOfUsersInSegmentQuery(ctx context.Context, in *GetNumberOfUsersInSegmentQueryRequest, opts ...grpc.CallOption) (*GetNumberOfUsersInSegmentQueryResponse, error)
	// Get active dynamic segment count from segment metadata table
	GetActiveDynamicSegmentCount(ctx context.Context, in *GetActiveDynamicSegmentCountRequest, opts ...grpc.CallOption) (*GetActiveDynamicSegmentCountResponse, error)
	// Get segment metadata details on the basis of segment id
	GetSegmentMetadata(ctx context.Context, in *GetSegmentMetadataRequest, opts ...grpc.CallOption) (*GetSegmentMetadataResponse, error)
	// GetSegmentEntryTimeForUser returns the timestamp when user first entered the segment
	// This is fetched from redis when diff is computed for selected segments(SegmentIdsToCompareInstancesDiff config field)
	GetSegmentEntryTimeForUser(ctx context.Context, in *GetSegmentEntryTimeForUserRequest, opts ...grpc.CallOption) (*GetSegmentEntryTimeForUserResponse, error)
	// GetUsersInSegmentOnDate returns the list of actors in the segment on the given date
	GetUsersInSegmentOnDate(ctx context.Context, in *GetUsersInSegmentOnDateRequest, opts ...grpc.CallOption) (*GetUsersInSegmentOnDateResponse, error)
}

type segmentationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSegmentationServiceClient(cc grpc.ClientConnInterface) SegmentationServiceClient {
	return &segmentationServiceClient{cc}
}

func (c *segmentationServiceClient) CreateOrGetSegment(ctx context.Context, in *CreateOrGetSegmentRequest, opts ...grpc.CallOption) (*CreateOrGetSegmentResponse, error) {
	out := new(CreateOrGetSegmentResponse)
	err := c.cc.Invoke(ctx, SegmentationService_CreateOrGetSegment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) DeleteSegment(ctx context.Context, in *DeleteSegmentRequest, opts ...grpc.CallOption) (*DeleteSegmentResponse, error) {
	out := new(DeleteSegmentResponse)
	err := c.cc.Invoke(ctx, SegmentationService_DeleteSegment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) IsMember(ctx context.Context, in *IsMemberRequest, opts ...grpc.CallOption) (*IsMemberResponse, error) {
	out := new(IsMemberResponse)
	err := c.cc.Invoke(ctx, SegmentationService_IsMember_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) IsMemberOfExpressions(ctx context.Context, in *IsMemberOfExpressionsRequest, opts ...grpc.CallOption) (*IsMemberOfExpressionsResponse, error) {
	out := new(IsMemberOfExpressionsResponse)
	err := c.cc.Invoke(ctx, SegmentationService_IsMemberOfExpressions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetMembers(ctx context.Context, in *GetMembersRequest, opts ...grpc.CallOption) (*GetMembersResponse, error) {
	out := new(GetMembersResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetMembers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) PushPinpointSegmentIds(ctx context.Context, in *PushPinpointSegmentIdsRequest, opts ...grpc.CallOption) (*PushPinpointSegmentIdsResponse, error) {
	out := new(PushPinpointSegmentIdsResponse)
	err := c.cc.Invoke(ctx, SegmentationService_PushPinpointSegmentIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetNumberOfUsersInSegment(ctx context.Context, in *GetNumberOfUsersInSegmentRequest, opts ...grpc.CallOption) (*GetNumberOfUsersInSegmentResponse, error) {
	out := new(GetNumberOfUsersInSegmentResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetNumberOfUsersInSegment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetSegmentTypes(ctx context.Context, in *GetSegmentTypesRequest, opts ...grpc.CallOption) (*GetSegmentTypesResponse, error) {
	out := new(GetSegmentTypesResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetSegmentTypes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) AreSegmentsAvailable(ctx context.Context, in *AreSegmentsAvailableRequest, opts ...grpc.CallOption) (*AreSegmentsAvailableResponse, error) {
	out := new(AreSegmentsAvailableResponse)
	err := c.cc.Invoke(ctx, SegmentationService_AreSegmentsAvailable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) UpdateSegment(ctx context.Context, in *UpdateSegmentRequest, opts ...grpc.CallOption) (*UpdateSegmentResponse, error) {
	out := new(UpdateSegmentResponse)
	err := c.cc.Invoke(ctx, SegmentationService_UpdateSegment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) CreateSegmentMetadata(ctx context.Context, in *CreateSegmentMetadataRequest, opts ...grpc.CallOption) (*CreateSegmentMetadataResponse, error) {
	out := new(CreateSegmentMetadataResponse)
	err := c.cc.Invoke(ctx, SegmentationService_CreateSegmentMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) UpdateSegmentMetadata(ctx context.Context, in *UpdateSegmentMetadataRequest, opts ...grpc.CallOption) (*UpdateSegmentMetadataResponse, error) {
	out := new(UpdateSegmentMetadataResponse)
	err := c.cc.Invoke(ctx, SegmentationService_UpdateSegmentMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetSegmentsBetweenUpdatedAt(ctx context.Context, in *GetSegmentsBetweenUpdatedAtRequest, opts ...grpc.CallOption) (*GetSegmentsBetweenUpdatedAtResponse, error) {
	out := new(GetSegmentsBetweenUpdatedAtResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetSegmentsBetweenUpdatedAt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetNumberOfUsersInSegmentQuery(ctx context.Context, in *GetNumberOfUsersInSegmentQueryRequest, opts ...grpc.CallOption) (*GetNumberOfUsersInSegmentQueryResponse, error) {
	out := new(GetNumberOfUsersInSegmentQueryResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetNumberOfUsersInSegmentQuery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetActiveDynamicSegmentCount(ctx context.Context, in *GetActiveDynamicSegmentCountRequest, opts ...grpc.CallOption) (*GetActiveDynamicSegmentCountResponse, error) {
	out := new(GetActiveDynamicSegmentCountResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetActiveDynamicSegmentCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetSegmentMetadata(ctx context.Context, in *GetSegmentMetadataRequest, opts ...grpc.CallOption) (*GetSegmentMetadataResponse, error) {
	out := new(GetSegmentMetadataResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetSegmentMetadata_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetSegmentEntryTimeForUser(ctx context.Context, in *GetSegmentEntryTimeForUserRequest, opts ...grpc.CallOption) (*GetSegmentEntryTimeForUserResponse, error) {
	out := new(GetSegmentEntryTimeForUserResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetSegmentEntryTimeForUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *segmentationServiceClient) GetUsersInSegmentOnDate(ctx context.Context, in *GetUsersInSegmentOnDateRequest, opts ...grpc.CallOption) (*GetUsersInSegmentOnDateResponse, error) {
	out := new(GetUsersInSegmentOnDateResponse)
	err := c.cc.Invoke(ctx, SegmentationService_GetUsersInSegmentOnDate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SegmentationServiceServer is the server API for SegmentationService service.
// All implementations should embed UnimplementedSegmentationServiceServer
// for forward compatibility
type SegmentationServiceServer interface {
	// To insert provider segment data and map it to internal segment id in segment database
	// segment_provider_id will be generated for provider_name based on provider_metadata
	CreateOrGetSegment(context.Context, *CreateOrGetSegmentRequest) (*CreateOrGetSegmentResponse, error)
	// To delete segment_instance_id entry from segment->timestamps sorted set and corresponding segmentInstance->users set
	// If segment_instance_ids is nil, entire segment->timestamps sorted set and all segmentInstance->users sets are deleted
	DeleteSegment(context.Context, *DeleteSegmentRequest) (*DeleteSegmentResponse, error)
	// To check if actor belongs to segment(s) at latest timestamp in db before given timestamp
	IsMember(context.Context, *IsMemberRequest) (*IsMemberResponse, error)
	// To check if actor belongs to logical expression of segments(s) at latest timestamp in db before given timestamp
	// for eg - IsMember('segment-1') && !IsMember('segment-2')
	IsMemberOfExpressions(context.Context, *IsMemberOfExpressionsRequest) (*IsMemberOfExpressionsResponse, error)
	// To get list of all actors belonging to a segment at latest timestamp in db before given timestamp
	// before_token in PageContextRequest should not be specified since this is a move-forward paginated call
	// page_size in PageContextRequest is just a hint for response size and does not guarantee the elements returned
	GetMembers(context.Context, *GetMembersRequest) (*GetMembersResponse, error)
	// To get all segment ids from pinpoint and push them to trigger-segment-export queue
	PushPinpointSegmentIds(context.Context, *PushPinpointSegmentIdsRequest) (*PushPinpointSegmentIdsResponse, error)
	// To get number of users in a segment
	GetNumberOfUsersInSegment(context.Context, *GetNumberOfUsersInSegmentRequest) (*GetNumberOfUsersInSegmentResponse, error)
	// To get types of multiple segments
	GetSegmentTypes(context.Context, *GetSegmentTypesRequest) (*GetSegmentTypesResponse, error)
	// To get availability of segments from PGDB in bulk
	AreSegmentsAvailable(context.Context, *AreSegmentsAvailableRequest) (*AreSegmentsAvailableResponse, error)
	// To update segment details like export_till time, etc
	UpdateSegment(context.Context, *UpdateSegmentRequest) (*UpdateSegmentResponse, error)
	// To add segment metadata details for segmentation service
	CreateSegmentMetadata(context.Context, *CreateSegmentMetadataRequest) (*CreateSegmentMetadataResponse, error)
	// To update segment metadata details like segment type, approval status, query filter and segment name
	UpdateSegmentMetadata(context.Context, *UpdateSegmentMetadataRequest) (*UpdateSegmentMetadataResponse, error)
	// Get Segment metadata details for approval and update segment
	GetSegmentsBetweenUpdatedAt(context.Context, *GetSegmentsBetweenUpdatedAtRequest) (*GetSegmentsBetweenUpdatedAtResponse, error)
	// Get segment count on the basis of provided query filter
	GetNumberOfUsersInSegmentQuery(context.Context, *GetNumberOfUsersInSegmentQueryRequest) (*GetNumberOfUsersInSegmentQueryResponse, error)
	// Get active dynamic segment count from segment metadata table
	GetActiveDynamicSegmentCount(context.Context, *GetActiveDynamicSegmentCountRequest) (*GetActiveDynamicSegmentCountResponse, error)
	// Get segment metadata details on the basis of segment id
	GetSegmentMetadata(context.Context, *GetSegmentMetadataRequest) (*GetSegmentMetadataResponse, error)
	// GetSegmentEntryTimeForUser returns the timestamp when user first entered the segment
	// This is fetched from redis when diff is computed for selected segments(SegmentIdsToCompareInstancesDiff config field)
	GetSegmentEntryTimeForUser(context.Context, *GetSegmentEntryTimeForUserRequest) (*GetSegmentEntryTimeForUserResponse, error)
	// GetUsersInSegmentOnDate returns the list of actors in the segment on the given date
	GetUsersInSegmentOnDate(context.Context, *GetUsersInSegmentOnDateRequest) (*GetUsersInSegmentOnDateResponse, error)
}

// UnimplementedSegmentationServiceServer should be embedded to have forward compatible implementations.
type UnimplementedSegmentationServiceServer struct {
}

func (UnimplementedSegmentationServiceServer) CreateOrGetSegment(context.Context, *CreateOrGetSegmentRequest) (*CreateOrGetSegmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrGetSegment not implemented")
}
func (UnimplementedSegmentationServiceServer) DeleteSegment(context.Context, *DeleteSegmentRequest) (*DeleteSegmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSegment not implemented")
}
func (UnimplementedSegmentationServiceServer) IsMember(context.Context, *IsMemberRequest) (*IsMemberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsMember not implemented")
}
func (UnimplementedSegmentationServiceServer) IsMemberOfExpressions(context.Context, *IsMemberOfExpressionsRequest) (*IsMemberOfExpressionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsMemberOfExpressions not implemented")
}
func (UnimplementedSegmentationServiceServer) GetMembers(context.Context, *GetMembersRequest) (*GetMembersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMembers not implemented")
}
func (UnimplementedSegmentationServiceServer) PushPinpointSegmentIds(context.Context, *PushPinpointSegmentIdsRequest) (*PushPinpointSegmentIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPinpointSegmentIds not implemented")
}
func (UnimplementedSegmentationServiceServer) GetNumberOfUsersInSegment(context.Context, *GetNumberOfUsersInSegmentRequest) (*GetNumberOfUsersInSegmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNumberOfUsersInSegment not implemented")
}
func (UnimplementedSegmentationServiceServer) GetSegmentTypes(context.Context, *GetSegmentTypesRequest) (*GetSegmentTypesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSegmentTypes not implemented")
}
func (UnimplementedSegmentationServiceServer) AreSegmentsAvailable(context.Context, *AreSegmentsAvailableRequest) (*AreSegmentsAvailableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AreSegmentsAvailable not implemented")
}
func (UnimplementedSegmentationServiceServer) UpdateSegment(context.Context, *UpdateSegmentRequest) (*UpdateSegmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSegment not implemented")
}
func (UnimplementedSegmentationServiceServer) CreateSegmentMetadata(context.Context, *CreateSegmentMetadataRequest) (*CreateSegmentMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSegmentMetadata not implemented")
}
func (UnimplementedSegmentationServiceServer) UpdateSegmentMetadata(context.Context, *UpdateSegmentMetadataRequest) (*UpdateSegmentMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSegmentMetadata not implemented")
}
func (UnimplementedSegmentationServiceServer) GetSegmentsBetweenUpdatedAt(context.Context, *GetSegmentsBetweenUpdatedAtRequest) (*GetSegmentsBetweenUpdatedAtResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSegmentsBetweenUpdatedAt not implemented")
}
func (UnimplementedSegmentationServiceServer) GetNumberOfUsersInSegmentQuery(context.Context, *GetNumberOfUsersInSegmentQueryRequest) (*GetNumberOfUsersInSegmentQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNumberOfUsersInSegmentQuery not implemented")
}
func (UnimplementedSegmentationServiceServer) GetActiveDynamicSegmentCount(context.Context, *GetActiveDynamicSegmentCountRequest) (*GetActiveDynamicSegmentCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveDynamicSegmentCount not implemented")
}
func (UnimplementedSegmentationServiceServer) GetSegmentMetadata(context.Context, *GetSegmentMetadataRequest) (*GetSegmentMetadataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSegmentMetadata not implemented")
}
func (UnimplementedSegmentationServiceServer) GetSegmentEntryTimeForUser(context.Context, *GetSegmentEntryTimeForUserRequest) (*GetSegmentEntryTimeForUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSegmentEntryTimeForUser not implemented")
}
func (UnimplementedSegmentationServiceServer) GetUsersInSegmentOnDate(context.Context, *GetUsersInSegmentOnDateRequest) (*GetUsersInSegmentOnDateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersInSegmentOnDate not implemented")
}

// UnsafeSegmentationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SegmentationServiceServer will
// result in compilation errors.
type UnsafeSegmentationServiceServer interface {
	mustEmbedUnimplementedSegmentationServiceServer()
}

func RegisterSegmentationServiceServer(s grpc.ServiceRegistrar, srv SegmentationServiceServer) {
	s.RegisterService(&SegmentationService_ServiceDesc, srv)
}

func _SegmentationService_CreateOrGetSegment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrGetSegmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).CreateOrGetSegment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_CreateOrGetSegment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).CreateOrGetSegment(ctx, req.(*CreateOrGetSegmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_DeleteSegment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSegmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).DeleteSegment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_DeleteSegment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).DeleteSegment(ctx, req.(*DeleteSegmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_IsMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMemberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).IsMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_IsMember_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).IsMember(ctx, req.(*IsMemberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_IsMemberOfExpressions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMemberOfExpressionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).IsMemberOfExpressions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_IsMemberOfExpressions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).IsMemberOfExpressions(ctx, req.(*IsMemberOfExpressionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMembersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetMembers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetMembers(ctx, req.(*GetMembersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_PushPinpointSegmentIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPinpointSegmentIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).PushPinpointSegmentIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_PushPinpointSegmentIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).PushPinpointSegmentIds(ctx, req.(*PushPinpointSegmentIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetNumberOfUsersInSegment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNumberOfUsersInSegmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetNumberOfUsersInSegment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetNumberOfUsersInSegment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetNumberOfUsersInSegment(ctx, req.(*GetNumberOfUsersInSegmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetSegmentTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSegmentTypesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetSegmentTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetSegmentTypes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetSegmentTypes(ctx, req.(*GetSegmentTypesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_AreSegmentsAvailable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AreSegmentsAvailableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).AreSegmentsAvailable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_AreSegmentsAvailable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).AreSegmentsAvailable(ctx, req.(*AreSegmentsAvailableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_UpdateSegment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSegmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).UpdateSegment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_UpdateSegment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).UpdateSegment(ctx, req.(*UpdateSegmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_CreateSegmentMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSegmentMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).CreateSegmentMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_CreateSegmentMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).CreateSegmentMetadata(ctx, req.(*CreateSegmentMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_UpdateSegmentMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSegmentMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).UpdateSegmentMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_UpdateSegmentMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).UpdateSegmentMetadata(ctx, req.(*UpdateSegmentMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetSegmentsBetweenUpdatedAt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSegmentsBetweenUpdatedAtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetSegmentsBetweenUpdatedAt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetSegmentsBetweenUpdatedAt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetSegmentsBetweenUpdatedAt(ctx, req.(*GetSegmentsBetweenUpdatedAtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetNumberOfUsersInSegmentQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNumberOfUsersInSegmentQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetNumberOfUsersInSegmentQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetNumberOfUsersInSegmentQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetNumberOfUsersInSegmentQuery(ctx, req.(*GetNumberOfUsersInSegmentQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetActiveDynamicSegmentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveDynamicSegmentCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetActiveDynamicSegmentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetActiveDynamicSegmentCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetActiveDynamicSegmentCount(ctx, req.(*GetActiveDynamicSegmentCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetSegmentMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSegmentMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetSegmentMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetSegmentMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetSegmentMetadata(ctx, req.(*GetSegmentMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetSegmentEntryTimeForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSegmentEntryTimeForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetSegmentEntryTimeForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetSegmentEntryTimeForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetSegmentEntryTimeForUser(ctx, req.(*GetSegmentEntryTimeForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SegmentationService_GetUsersInSegmentOnDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUsersInSegmentOnDateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SegmentationServiceServer).GetUsersInSegmentOnDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SegmentationService_GetUsersInSegmentOnDate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SegmentationServiceServer).GetUsersInSegmentOnDate(ctx, req.(*GetUsersInSegmentOnDateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SegmentationService_ServiceDesc is the grpc.ServiceDesc for SegmentationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SegmentationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "segment.SegmentationService",
	HandlerType: (*SegmentationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateOrGetSegment",
			Handler:    _SegmentationService_CreateOrGetSegment_Handler,
		},
		{
			MethodName: "DeleteSegment",
			Handler:    _SegmentationService_DeleteSegment_Handler,
		},
		{
			MethodName: "IsMember",
			Handler:    _SegmentationService_IsMember_Handler,
		},
		{
			MethodName: "IsMemberOfExpressions",
			Handler:    _SegmentationService_IsMemberOfExpressions_Handler,
		},
		{
			MethodName: "GetMembers",
			Handler:    _SegmentationService_GetMembers_Handler,
		},
		{
			MethodName: "PushPinpointSegmentIds",
			Handler:    _SegmentationService_PushPinpointSegmentIds_Handler,
		},
		{
			MethodName: "GetNumberOfUsersInSegment",
			Handler:    _SegmentationService_GetNumberOfUsersInSegment_Handler,
		},
		{
			MethodName: "GetSegmentTypes",
			Handler:    _SegmentationService_GetSegmentTypes_Handler,
		},
		{
			MethodName: "AreSegmentsAvailable",
			Handler:    _SegmentationService_AreSegmentsAvailable_Handler,
		},
		{
			MethodName: "UpdateSegment",
			Handler:    _SegmentationService_UpdateSegment_Handler,
		},
		{
			MethodName: "CreateSegmentMetadata",
			Handler:    _SegmentationService_CreateSegmentMetadata_Handler,
		},
		{
			MethodName: "UpdateSegmentMetadata",
			Handler:    _SegmentationService_UpdateSegmentMetadata_Handler,
		},
		{
			MethodName: "GetSegmentsBetweenUpdatedAt",
			Handler:    _SegmentationService_GetSegmentsBetweenUpdatedAt_Handler,
		},
		{
			MethodName: "GetNumberOfUsersInSegmentQuery",
			Handler:    _SegmentationService_GetNumberOfUsersInSegmentQuery_Handler,
		},
		{
			MethodName: "GetActiveDynamicSegmentCount",
			Handler:    _SegmentationService_GetActiveDynamicSegmentCount_Handler,
		},
		{
			MethodName: "GetSegmentMetadata",
			Handler:    _SegmentationService_GetSegmentMetadata_Handler,
		},
		{
			MethodName: "GetSegmentEntryTimeForUser",
			Handler:    _SegmentationService_GetSegmentEntryTimeForUser_Handler,
		},
		{
			MethodName: "GetUsersInSegmentOnDate",
			Handler:    _SegmentationService_GetUsersInSegmentOnDate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/segment/service.proto",
}
