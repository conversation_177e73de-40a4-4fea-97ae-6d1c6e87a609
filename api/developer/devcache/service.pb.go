// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/developer/devcache/service.proto

package devcache

import (
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetCacheStoragesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCacheStoragesRequest) Reset() {
	*x = GetCacheStoragesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_developer_devcache_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCacheStoragesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCacheStoragesRequest) ProtoMessage() {}

func (x *GetCacheStoragesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_developer_devcache_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCacheStoragesRequest.ProtoReflect.Descriptor instead.
func (*GetCacheStoragesRequest) Descriptor() ([]byte, []int) {
	return file_api_developer_devcache_service_proto_rawDescGZIP(), []int{0}
}

type GetCacheStoragesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// CacheStorage enums as strings
	CacheStorages []string `protobuf:"bytes,2,rep,name=cache_storages,json=cacheStorages,proto3" json:"cache_storages,omitempty"`
}

func (x *GetCacheStoragesResponse) Reset() {
	*x = GetCacheStoragesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_developer_devcache_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCacheStoragesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCacheStoragesResponse) ProtoMessage() {}

func (x *GetCacheStoragesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_developer_devcache_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCacheStoragesResponse.ProtoReflect.Descriptor instead.
func (*GetCacheStoragesResponse) Descriptor() ([]byte, []int) {
	return file_api_developer_devcache_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetCacheStoragesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCacheStoragesResponse) GetCacheStorages() []string {
	if x != nil {
		return x.CacheStorages
	}
	return nil
}

type GetCacheEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Cache Storage in which key will be searched. Send CacheStorage enum as string.
	// String is used instead of enum to not require any deployment in callers if any changes in storage enum.
	// eg. "CACHE_STORAGE_ONBOARDING"
	CacheStorage string `protobuf:"bytes,1,opt,name=cache_storage,json=cacheStorage,proto3" json:"cache_storage,omitempty"`
	// Key to be fetched. e.g. "minUser:919123123123"
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *GetCacheEntryRequest) Reset() {
	*x = GetCacheEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_developer_devcache_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCacheEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCacheEntryRequest) ProtoMessage() {}

func (x *GetCacheEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_developer_devcache_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCacheEntryRequest.ProtoReflect.Descriptor instead.
func (*GetCacheEntryRequest) Descriptor() ([]byte, []int) {
	return file_api_developer_devcache_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetCacheEntryRequest) GetCacheStorage() string {
	if x != nil {
		return x.CacheStorage
	}
	return ""
}

func (x *GetCacheEntryRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type GetCacheEntryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// cache value of the key.
	CacheEntry string `protobuf:"bytes,2,opt,name=cache_entry,json=cacheEntry,proto3" json:"cache_entry,omitempty"`
}

func (x *GetCacheEntryResponse) Reset() {
	*x = GetCacheEntryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_developer_devcache_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCacheEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCacheEntryResponse) ProtoMessage() {}

func (x *GetCacheEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_developer_devcache_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCacheEntryResponse.ProtoReflect.Descriptor instead.
func (*GetCacheEntryResponse) Descriptor() ([]byte, []int) {
	return file_api_developer_devcache_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetCacheEntryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCacheEntryResponse) GetCacheEntry() string {
	if x != nil {
		return x.CacheEntry
	}
	return ""
}

type DeleteCacheEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Cache Storage in which key will be searched. Send CacheStorage enum as string.
	// String is used instead of enum to not require any deployment in callers if any changes in storage enum.
	// eg. "CACHE_STORAGE_ONBOARDING"
	CacheStorage string `protobuf:"bytes,1,opt,name=cache_storage,json=cacheStorage,proto3" json:"cache_storage,omitempty"`
	// Key to be deleted. e.g. "minUser:919123123123"
	Key string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
}

func (x *DeleteCacheEntryRequest) Reset() {
	*x = DeleteCacheEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_developer_devcache_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCacheEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCacheEntryRequest) ProtoMessage() {}

func (x *DeleteCacheEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_developer_devcache_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCacheEntryRequest.ProtoReflect.Descriptor instead.
func (*DeleteCacheEntryRequest) Descriptor() ([]byte, []int) {
	return file_api_developer_devcache_service_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteCacheEntryRequest) GetCacheStorage() string {
	if x != nil {
		return x.CacheStorage
	}
	return ""
}

func (x *DeleteCacheEntryRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type DeleteCacheEntryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *DeleteCacheEntryResponse) Reset() {
	*x = DeleteCacheEntryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_developer_devcache_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCacheEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCacheEntryResponse) ProtoMessage() {}

func (x *DeleteCacheEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_developer_devcache_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCacheEntryResponse.ProtoReflect.Descriptor instead.
func (*DeleteCacheEntryResponse) Descriptor() ([]byte, []int) {
	return file_api_developer_devcache_service_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteCacheEntryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_developer_devcache_service_proto protoreflect.FileDescriptor

var file_api_developer_devcache_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f,
	0x64, 0x65, 0x76, 0x63, 0x61, 0x63, 0x68, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x2e, 0x64, 0x65, 0x76, 0x63, 0x61, 0x63, 0x68, 0x65, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x66, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x73, 0x22, 0x4d, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x61, 0x63, 0x68, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x22, 0x5d, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x63, 0x68, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x22, 0x50, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x22, 0x3f, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x32, 0xd4, 0x02, 0x0a, 0x08, 0x44, 0x65, 0x76, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x12, 0x6f, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x53, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x2e, 0x64, 0x65, 0x76, 0x63, 0x61, 0x63, 0x68, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64,
	0x65, 0x76, 0x63, 0x61, 0x63, 0x68, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x66, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x28, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e,
	0x64, 0x65, 0x76, 0x63, 0x61, 0x63, 0x68, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x65, 0x76, 0x63, 0x61, 0x63,
	0x68, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x63, 0x68, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x10, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x2b,
	0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x65, 0x76, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x65, 0x76, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x66, 0x0a, 0x31, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x62, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x65, 0x76, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x5a, 0x31, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x62, 0x65, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x76, 0x63, 0x61,
	0x63, 0x68, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_developer_devcache_service_proto_rawDescOnce sync.Once
	file_api_developer_devcache_service_proto_rawDescData = file_api_developer_devcache_service_proto_rawDesc
)

func file_api_developer_devcache_service_proto_rawDescGZIP() []byte {
	file_api_developer_devcache_service_proto_rawDescOnce.Do(func() {
		file_api_developer_devcache_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_developer_devcache_service_proto_rawDescData)
	})
	return file_api_developer_devcache_service_proto_rawDescData
}

var file_api_developer_devcache_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_developer_devcache_service_proto_goTypes = []interface{}{
	(*GetCacheStoragesRequest)(nil),  // 0: developer.devcache.GetCacheStoragesRequest
	(*GetCacheStoragesResponse)(nil), // 1: developer.devcache.GetCacheStoragesResponse
	(*GetCacheEntryRequest)(nil),     // 2: developer.devcache.GetCacheEntryRequest
	(*GetCacheEntryResponse)(nil),    // 3: developer.devcache.GetCacheEntryResponse
	(*DeleteCacheEntryRequest)(nil),  // 4: developer.devcache.DeleteCacheEntryRequest
	(*DeleteCacheEntryResponse)(nil), // 5: developer.devcache.DeleteCacheEntryResponse
	(*rpc.Status)(nil),               // 6: rpc.Status
}
var file_api_developer_devcache_service_proto_depIdxs = []int32{
	6, // 0: developer.devcache.GetCacheStoragesResponse.status:type_name -> rpc.Status
	6, // 1: developer.devcache.GetCacheEntryResponse.status:type_name -> rpc.Status
	6, // 2: developer.devcache.DeleteCacheEntryResponse.status:type_name -> rpc.Status
	0, // 3: developer.devcache.DevCache.GetCacheStorages:input_type -> developer.devcache.GetCacheStoragesRequest
	2, // 4: developer.devcache.DevCache.GetCacheEntry:input_type -> developer.devcache.GetCacheEntryRequest
	4, // 5: developer.devcache.DevCache.DeleteCacheEntry:input_type -> developer.devcache.DeleteCacheEntryRequest
	1, // 6: developer.devcache.DevCache.GetCacheStorages:output_type -> developer.devcache.GetCacheStoragesResponse
	3, // 7: developer.devcache.DevCache.GetCacheEntry:output_type -> developer.devcache.GetCacheEntryResponse
	5, // 8: developer.devcache.DevCache.DeleteCacheEntry:output_type -> developer.devcache.DeleteCacheEntryResponse
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_developer_devcache_service_proto_init() }
func file_api_developer_devcache_service_proto_init() {
	if File_api_developer_devcache_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_developer_devcache_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCacheStoragesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_developer_devcache_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCacheStoragesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_developer_devcache_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCacheEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_developer_devcache_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCacheEntryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_developer_devcache_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCacheEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_developer_devcache_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCacheEntryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_developer_devcache_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_developer_devcache_service_proto_goTypes,
		DependencyIndexes: file_api_developer_devcache_service_proto_depIdxs,
		MessageInfos:      file_api_developer_devcache_service_proto_msgTypes,
	}.Build()
	File_api_developer_devcache_service_proto = out.File
	file_api_developer_devcache_service_proto_rawDesc = nil
	file_api_developer_devcache_service_proto_goTypes = nil
	file_api_developer_devcache_service_proto_depIdxs = nil
}
