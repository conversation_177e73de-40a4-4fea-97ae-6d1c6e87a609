// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/connected_account/notification/notification.proto

package notification

import (
	connected_account "github.com/epifi/gamma/api/connected_account"
	enums "github.com/epifi/gamma/api/connected_account/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AaNotificationFieldMask int32

const (
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_UNSPECIFIED       AaNotificationFieldMask = 0
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_ID                AaNotificationFieldMask = 1
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_ACTOR_ID          AaNotificationFieldMask = 2
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_COMMS_MSG_ID      AaNotificationFieldMask = 3
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_COMMS_TYPE        AaNotificationFieldMask = 4
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_COMMS_CHANNEL     AaNotificationFieldMask = 5
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_COMMS_CALL_STATUS AaNotificationFieldMask = 6
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_META_DATA         AaNotificationFieldMask = 7
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_CREATED_AT        AaNotificationFieldMask = 8
	AaNotificationFieldMask_AA_NOTIFICATION_FIELD_MASK_UPDATED_AT        AaNotificationFieldMask = 9
)

// Enum value maps for AaNotificationFieldMask.
var (
	AaNotificationFieldMask_name = map[int32]string{
		0: "AA_NOTIFICATION_FIELD_MASK_UNSPECIFIED",
		1: "AA_NOTIFICATION_FIELD_MASK_ID",
		2: "AA_NOTIFICATION_FIELD_MASK_ACTOR_ID",
		3: "AA_NOTIFICATION_FIELD_MASK_COMMS_MSG_ID",
		4: "AA_NOTIFICATION_FIELD_MASK_COMMS_TYPE",
		5: "AA_NOTIFICATION_FIELD_MASK_COMMS_CHANNEL",
		6: "AA_NOTIFICATION_FIELD_MASK_COMMS_CALL_STATUS",
		7: "AA_NOTIFICATION_FIELD_MASK_META_DATA",
		8: "AA_NOTIFICATION_FIELD_MASK_CREATED_AT",
		9: "AA_NOTIFICATION_FIELD_MASK_UPDATED_AT",
	}
	AaNotificationFieldMask_value = map[string]int32{
		"AA_NOTIFICATION_FIELD_MASK_UNSPECIFIED":       0,
		"AA_NOTIFICATION_FIELD_MASK_ID":                1,
		"AA_NOTIFICATION_FIELD_MASK_ACTOR_ID":          2,
		"AA_NOTIFICATION_FIELD_MASK_COMMS_MSG_ID":      3,
		"AA_NOTIFICATION_FIELD_MASK_COMMS_TYPE":        4,
		"AA_NOTIFICATION_FIELD_MASK_COMMS_CHANNEL":     5,
		"AA_NOTIFICATION_FIELD_MASK_COMMS_CALL_STATUS": 6,
		"AA_NOTIFICATION_FIELD_MASK_META_DATA":         7,
		"AA_NOTIFICATION_FIELD_MASK_CREATED_AT":        8,
		"AA_NOTIFICATION_FIELD_MASK_UPDATED_AT":        9,
	}
)

func (x AaNotificationFieldMask) Enum() *AaNotificationFieldMask {
	p := new(AaNotificationFieldMask)
	*p = x
	return p
}

func (x AaNotificationFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AaNotificationFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_connected_account_notification_notification_proto_enumTypes[0].Descriptor()
}

func (AaNotificationFieldMask) Type() protoreflect.EnumType {
	return &file_api_connected_account_notification_notification_proto_enumTypes[0]
}

func (x AaNotificationFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AaNotificationFieldMask.Descriptor instead.
func (AaNotificationFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{0}
}

// connected account notifications request
// parameters include
// 1) comms_type : first data pull fail, first data pull success, etc.
// 2) CommsParams : parameters specific to comms_type
// 3) actor_id : concerned actor_id
type CaComms struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommsType enums.CommsType `protobuf:"varint,1,opt,name=comms_type,json=commsType,proto3,enum=connected_account.enums.CommsType" json:"comms_type,omitempty"`
	// add message to CommParams to use/add notification-specific params
	//
	// Types that are assignable to CommsParams:
	//	*CaComms_FirstDataPullFailParams
	//	*CaComms_FirstDataPullSuccessParams
	//	*CaComms_SuccessfulConsentGrantParams
	//	*CaComms_ConsentPausedFromAaParams
	//	*CaComms_AccountDelinkedFromAaParams
	//	*CaComms_ConsentExpiredParams
	//	*CaComms_AaHeartbeatUpParams
	CommsParams isCaComms_CommsParams `protobuf_oneof:"CommsParams"`
	// actor id to whom notification is to be sent
	ActorId string `protobuf:"bytes,8,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *CaComms) Reset() {
	*x = CaComms{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaComms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaComms) ProtoMessage() {}

func (x *CaComms) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaComms.ProtoReflect.Descriptor instead.
func (*CaComms) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{0}
}

func (x *CaComms) GetCommsType() enums.CommsType {
	if x != nil {
		return x.CommsType
	}
	return enums.CommsType(0)
}

func (m *CaComms) GetCommsParams() isCaComms_CommsParams {
	if m != nil {
		return m.CommsParams
	}
	return nil
}

func (x *CaComms) GetFirstDataPullFailParams() *FirstDataPullFailParams {
	if x, ok := x.GetCommsParams().(*CaComms_FirstDataPullFailParams); ok {
		return x.FirstDataPullFailParams
	}
	return nil
}

func (x *CaComms) GetFirstDataPullSuccessParams() *FirstDataPullSuccessParams {
	if x, ok := x.GetCommsParams().(*CaComms_FirstDataPullSuccessParams); ok {
		return x.FirstDataPullSuccessParams
	}
	return nil
}

func (x *CaComms) GetSuccessfulConsentGrantParams() *SuccessfulConsentGrantParams {
	if x, ok := x.GetCommsParams().(*CaComms_SuccessfulConsentGrantParams); ok {
		return x.SuccessfulConsentGrantParams
	}
	return nil
}

func (x *CaComms) GetConsentPausedFromAaParams() *ConsentPausedFromAaParams {
	if x, ok := x.GetCommsParams().(*CaComms_ConsentPausedFromAaParams); ok {
		return x.ConsentPausedFromAaParams
	}
	return nil
}

func (x *CaComms) GetAccountDelinkedFromAaParams() *AccountDelinkedFromAaParams {
	if x, ok := x.GetCommsParams().(*CaComms_AccountDelinkedFromAaParams); ok {
		return x.AccountDelinkedFromAaParams
	}
	return nil
}

func (x *CaComms) GetConsentExpiredParams() *ConsentExpiredParams {
	if x, ok := x.GetCommsParams().(*CaComms_ConsentExpiredParams); ok {
		return x.ConsentExpiredParams
	}
	return nil
}

func (x *CaComms) GetAaHeartbeatUpParams() *AaHeartbeatUpParams {
	if x, ok := x.GetCommsParams().(*CaComms_AaHeartbeatUpParams); ok {
		return x.AaHeartbeatUpParams
	}
	return nil
}

func (x *CaComms) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type isCaComms_CommsParams interface {
	isCaComms_CommsParams()
}

type CaComms_FirstDataPullFailParams struct {
	FirstDataPullFailParams *FirstDataPullFailParams `protobuf:"bytes,2,opt,name=first_data_pull_fail_params,json=firstDataPullFailParams,proto3,oneof"`
}

type CaComms_FirstDataPullSuccessParams struct {
	FirstDataPullSuccessParams *FirstDataPullSuccessParams `protobuf:"bytes,3,opt,name=first_data_pull_success_params,json=firstDataPullSuccessParams,proto3,oneof"`
}

type CaComms_SuccessfulConsentGrantParams struct {
	SuccessfulConsentGrantParams *SuccessfulConsentGrantParams `protobuf:"bytes,4,opt,name=successful_consent_grant_params,json=successfulConsentGrantParams,proto3,oneof"`
}

type CaComms_ConsentPausedFromAaParams struct {
	ConsentPausedFromAaParams *ConsentPausedFromAaParams `protobuf:"bytes,5,opt,name=consent_paused_from_aa_params,json=consentPausedFromAaParams,proto3,oneof"`
}

type CaComms_AccountDelinkedFromAaParams struct {
	AccountDelinkedFromAaParams *AccountDelinkedFromAaParams `protobuf:"bytes,6,opt,name=account_delinked_from_aa_params,json=accountDelinkedFromAaParams,proto3,oneof"`
}

type CaComms_ConsentExpiredParams struct {
	ConsentExpiredParams *ConsentExpiredParams `protobuf:"bytes,7,opt,name=consent_expired_params,json=consentExpiredParams,proto3,oneof"`
}

type CaComms_AaHeartbeatUpParams struct {
	AaHeartbeatUpParams *AaHeartbeatUpParams `protobuf:"bytes,9,opt,name=aa_heartbeat_up_params,json=aaHeartbeatUpParams,proto3,oneof"`
}

func (*CaComms_FirstDataPullFailParams) isCaComms_CommsParams() {}

func (*CaComms_FirstDataPullSuccessParams) isCaComms_CommsParams() {}

func (*CaComms_SuccessfulConsentGrantParams) isCaComms_CommsParams() {}

func (*CaComms_ConsentPausedFromAaParams) isCaComms_CommsParams() {}

func (*CaComms_AccountDelinkedFromAaParams) isCaComms_CommsParams() {}

func (*CaComms_ConsentExpiredParams) isCaComms_CommsParams() {}

func (*CaComms_AaHeartbeatUpParams) isCaComms_CommsParams() {}

type FirstDataPullFailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// consent reference id
	Consent *connected_account.Consent `protobuf:"bytes,1,opt,name=consent,proto3" json:"consent,omitempty"`
	// dfa id
	Attempt *connected_account.DataFetchAttempt `protobuf:"bytes,2,opt,name=attempt,proto3" json:"attempt,omitempty"`
}

func (x *FirstDataPullFailParams) Reset() {
	*x = FirstDataPullFailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FirstDataPullFailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FirstDataPullFailParams) ProtoMessage() {}

func (x *FirstDataPullFailParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FirstDataPullFailParams.ProtoReflect.Descriptor instead.
func (*FirstDataPullFailParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{1}
}

func (x *FirstDataPullFailParams) GetConsent() *connected_account.Consent {
	if x != nil {
		return x.Consent
	}
	return nil
}

func (x *FirstDataPullFailParams) GetAttempt() *connected_account.DataFetchAttempt {
	if x != nil {
		return x.Attempt
	}
	return nil
}

type FirstDataPullSuccessParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// consent reference id
	Consent *connected_account.Consent `protobuf:"bytes,1,opt,name=consent,proto3" json:"consent,omitempty"`
	// dfa id
	Attempt *connected_account.DataFetchAttempt `protobuf:"bytes,2,opt,name=attempt,proto3" json:"attempt,omitempty"`
}

func (x *FirstDataPullSuccessParams) Reset() {
	*x = FirstDataPullSuccessParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FirstDataPullSuccessParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FirstDataPullSuccessParams) ProtoMessage() {}

func (x *FirstDataPullSuccessParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FirstDataPullSuccessParams.ProtoReflect.Descriptor instead.
func (*FirstDataPullSuccessParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{2}
}

func (x *FirstDataPullSuccessParams) GetConsent() *connected_account.Consent {
	if x != nil {
		return x.Consent
	}
	return nil
}

func (x *FirstDataPullSuccessParams) GetAttempt() *connected_account.DataFetchAttempt {
	if x != nil {
		return x.Attempt
	}
	return nil
}

type SuccessfulConsentGrantParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SuccessfulConsentGrantParams) Reset() {
	*x = SuccessfulConsentGrantParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuccessfulConsentGrantParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuccessfulConsentGrantParams) ProtoMessage() {}

func (x *SuccessfulConsentGrantParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuccessfulConsentGrantParams.ProtoReflect.Descriptor instead.
func (*SuccessfulConsentGrantParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{3}
}

type ConsentPausedFromAaParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// consent_reference id
	Consent *connected_account.Consent `protobuf:"bytes,1,opt,name=consent,proto3" json:"consent,omitempty"`
}

func (x *ConsentPausedFromAaParams) Reset() {
	*x = ConsentPausedFromAaParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsentPausedFromAaParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsentPausedFromAaParams) ProtoMessage() {}

func (x *ConsentPausedFromAaParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsentPausedFromAaParams.ProtoReflect.Descriptor instead.
func (*ConsentPausedFromAaParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{4}
}

func (x *ConsentPausedFromAaParams) GetConsent() *connected_account.Consent {
	if x != nil {
		return x.Consent
	}
	return nil
}

type AccountDelinkedFromAaParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AaAccount *connected_account.AaAccount `protobuf:"bytes,1,opt,name=aa_account,json=aaAccount,proto3" json:"aa_account,omitempty"`
}

func (x *AccountDelinkedFromAaParams) Reset() {
	*x = AccountDelinkedFromAaParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountDelinkedFromAaParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountDelinkedFromAaParams) ProtoMessage() {}

func (x *AccountDelinkedFromAaParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountDelinkedFromAaParams.ProtoReflect.Descriptor instead.
func (*AccountDelinkedFromAaParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{5}
}

func (x *AccountDelinkedFromAaParams) GetAaAccount() *connected_account.AaAccount {
	if x != nil {
		return x.AaAccount
	}
	return nil
}

type ConsentExpiredParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// consent_reference id
	ConsentReferenceId string `protobuf:"bytes,1,opt,name=consent_reference_id,json=consentReferenceId,proto3" json:"consent_reference_id,omitempty"`
}

func (x *ConsentExpiredParams) Reset() {
	*x = ConsentExpiredParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsentExpiredParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsentExpiredParams) ProtoMessage() {}

func (x *ConsentExpiredParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsentExpiredParams.ProtoReflect.Descriptor instead.
func (*ConsentExpiredParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{6}
}

func (x *ConsentExpiredParams) GetConsentReferenceId() string {
	if x != nil {
		return x.ConsentReferenceId
	}
	return ""
}

type AaHeartbeatUpParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AaHeartbeatUpParams) Reset() {
	*x = AaHeartbeatUpParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaHeartbeatUpParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaHeartbeatUpParams) ProtoMessage() {}

func (x *AaHeartbeatUpParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaHeartbeatUpParams.ProtoReflect.Descriptor instead.
func (*AaHeartbeatUpParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{7}
}

// to store information specific to comms_type
// example - account_ref_id, consent_ref_id
// expand if required in future
type MetaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountReferenceId string `protobuf:"bytes,1,opt,name=account_reference_id,json=accountReferenceId,proto3" json:"account_reference_id,omitempty"`
	ConsentReferenceId string `protobuf:"bytes,2,opt,name=consent_reference_id,json=consentReferenceId,proto3" json:"consent_reference_id,omitempty"`
}

func (x *MetaData) Reset() {
	*x = MetaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaData) ProtoMessage() {}

func (x *MetaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaData.ProtoReflect.Descriptor instead.
func (*MetaData) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{8}
}

func (x *MetaData) GetAccountReferenceId() string {
	if x != nil {
		return x.AccountReferenceId
	}
	return ""
}

func (x *MetaData) GetConsentReferenceId() string {
	if x != nil {
		return x.ConsentReferenceId
	}
	return ""
}

type AaNotification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId         string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	CommsMsgId      string                 `protobuf:"bytes,3,opt,name=comms_msg_id,json=commsMsgId,proto3" json:"comms_msg_id,omitempty"`
	CommsType       enums.CommsType        `protobuf:"varint,4,opt,name=comms_type,json=commsType,proto3,enum=connected_account.enums.CommsType" json:"comms_type,omitempty"`
	CommsChannel    enums.CommsChannel     `protobuf:"varint,5,opt,name=comms_channel,json=commsChannel,proto3,enum=connected_account.enums.CommsChannel" json:"comms_channel,omitempty"`
	CommsCallStatus enums.CommsCallStatus  `protobuf:"varint,6,opt,name=comms_call_status,json=commsCallStatus,proto3,enum=connected_account.enums.CommsCallStatus" json:"comms_call_status,omitempty"`
	MetaData        *MetaData              `protobuf:"bytes,7,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *AaNotification) Reset() {
	*x = AaNotification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_notification_notification_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AaNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AaNotification) ProtoMessage() {}

func (x *AaNotification) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_notification_notification_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AaNotification.ProtoReflect.Descriptor instead.
func (*AaNotification) Descriptor() ([]byte, []int) {
	return file_api_connected_account_notification_notification_proto_rawDescGZIP(), []int{9}
}

func (x *AaNotification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AaNotification) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AaNotification) GetCommsMsgId() string {
	if x != nil {
		return x.CommsMsgId
	}
	return ""
}

func (x *AaNotification) GetCommsType() enums.CommsType {
	if x != nil {
		return x.CommsType
	}
	return enums.CommsType(0)
}

func (x *AaNotification) GetCommsChannel() enums.CommsChannel {
	if x != nil {
		return x.CommsChannel
	}
	return enums.CommsChannel(0)
}

func (x *AaNotification) GetCommsCallStatus() enums.CommsCallStatus {
	if x != nil {
		return x.CommsCallStatus
	}
	return enums.CommsCallStatus(0)
}

func (x *AaNotification) GetMetaData() *MetaData {
	if x != nil {
		return x.MetaData
	}
	return nil
}

func (x *AaNotification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AaNotification) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_connected_account_notification_notification_proto protoreflect.FileDescriptor

var file_api_connected_account_notification_notification_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c,
	0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x07, 0x0a, 0x07, 0x43, 0x61, 0x43,
	0x6f, 0x6d, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x77, 0x0a, 0x1b, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x17, 0x66, 0x69, 0x72, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x80, 0x01, 0x0a, 0x1e, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x72, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x1a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x85, 0x01, 0x0a, 0x1f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66,
	0x75, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x61, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x1c, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x61, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x7d, 0x0a, 0x1d, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x61, 0x61, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x39, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x75, 0x73, 0x65,
	0x64, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52,
	0x19, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x75, 0x73, 0x65, 0x64, 0x46, 0x72,
	0x6f, 0x6d, 0x41, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x83, 0x01, 0x0a, 0x1f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f,
	0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x61, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c,
	0x69, 0x6e, 0x6b, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x48, 0x00, 0x52, 0x1b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x69,
	0x6e, 0x6b, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x6c, 0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x34, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x14, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x6a,
	0x0a, 0x16, 0x61, 0x61, 0x5f, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x5f, 0x75,
	0x70, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x41, 0x61, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x55, 0x70, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x13, 0x61, 0x61, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65,
	0x61, 0x74, 0x55, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x17, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x34, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x07, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x1a, 0x46, 0x69, 0x72, 0x73, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x52, 0x07, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x47, 0x72,
	0x61, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x51, 0x0a, 0x19, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x75, 0x73, 0x65, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x41, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x22, 0x5a, 0x0a, 0x1b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x46,
	0x72, 0x6f, 0x6d, 0x41, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x61,
	0x61, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x41, 0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x09, 0x61,
	0x61, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x48, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x41, 0x61, 0x48, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61,
	0x74, 0x55, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x6e, 0x0a, 0x08, 0x4d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xff, 0x03, 0x0a, 0x0e, 0x41, 0x61,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x73,
	0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x6d, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0d,
	0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x6d,
	0x73, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x54, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x73, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x63,
	0x6f, 0x6d, 0x6d, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x45,
	0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0xc9, 0x03, 0x0a, 0x17,
	0x41, 0x61, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x41, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x27, 0x0a, 0x23, 0x41, 0x41, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12,
	0x2b, 0x0a, 0x27, 0x41, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f,
	0x4d, 0x4d, 0x53, 0x5f, 0x4d, 0x53, 0x47, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25,
	0x41, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x41, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x4e,
	0x4e, 0x45, 0x4c, 0x10, 0x05, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x06, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x41, 0x5f, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4d, 0x45, 0x54, 0x41, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10,
	0x07, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x29, 0x0a, 0x25,
	0x41, 0x41, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x42, 0x76, 0x0a, 0x39, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_connected_account_notification_notification_proto_rawDescOnce sync.Once
	file_api_connected_account_notification_notification_proto_rawDescData = file_api_connected_account_notification_notification_proto_rawDesc
)

func file_api_connected_account_notification_notification_proto_rawDescGZIP() []byte {
	file_api_connected_account_notification_notification_proto_rawDescOnce.Do(func() {
		file_api_connected_account_notification_notification_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_connected_account_notification_notification_proto_rawDescData)
	})
	return file_api_connected_account_notification_notification_proto_rawDescData
}

var file_api_connected_account_notification_notification_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_connected_account_notification_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_connected_account_notification_notification_proto_goTypes = []interface{}{
	(AaNotificationFieldMask)(0),               // 0: connected_account.notification.AaNotificationFieldMask
	(*CaComms)(nil),                            // 1: connected_account.notification.CaComms
	(*FirstDataPullFailParams)(nil),            // 2: connected_account.notification.FirstDataPullFailParams
	(*FirstDataPullSuccessParams)(nil),         // 3: connected_account.notification.FirstDataPullSuccessParams
	(*SuccessfulConsentGrantParams)(nil),       // 4: connected_account.notification.SuccessfulConsentGrantParams
	(*ConsentPausedFromAaParams)(nil),          // 5: connected_account.notification.ConsentPausedFromAaParams
	(*AccountDelinkedFromAaParams)(nil),        // 6: connected_account.notification.AccountDelinkedFromAaParams
	(*ConsentExpiredParams)(nil),               // 7: connected_account.notification.ConsentExpiredParams
	(*AaHeartbeatUpParams)(nil),                // 8: connected_account.notification.AaHeartbeatUpParams
	(*MetaData)(nil),                           // 9: connected_account.notification.MetaData
	(*AaNotification)(nil),                     // 10: connected_account.notification.AaNotification
	(enums.CommsType)(0),                       // 11: connected_account.enums.CommsType
	(*connected_account.Consent)(nil),          // 12: connected_account.Consent
	(*connected_account.DataFetchAttempt)(nil), // 13: connected_account.DataFetchAttempt
	(*connected_account.AaAccount)(nil),        // 14: connected_account.AaAccount
	(enums.CommsChannel)(0),                    // 15: connected_account.enums.CommsChannel
	(enums.CommsCallStatus)(0),                 // 16: connected_account.enums.CommsCallStatus
	(*timestamppb.Timestamp)(nil),              // 17: google.protobuf.Timestamp
}
var file_api_connected_account_notification_notification_proto_depIdxs = []int32{
	11, // 0: connected_account.notification.CaComms.comms_type:type_name -> connected_account.enums.CommsType
	2,  // 1: connected_account.notification.CaComms.first_data_pull_fail_params:type_name -> connected_account.notification.FirstDataPullFailParams
	3,  // 2: connected_account.notification.CaComms.first_data_pull_success_params:type_name -> connected_account.notification.FirstDataPullSuccessParams
	4,  // 3: connected_account.notification.CaComms.successful_consent_grant_params:type_name -> connected_account.notification.SuccessfulConsentGrantParams
	5,  // 4: connected_account.notification.CaComms.consent_paused_from_aa_params:type_name -> connected_account.notification.ConsentPausedFromAaParams
	6,  // 5: connected_account.notification.CaComms.account_delinked_from_aa_params:type_name -> connected_account.notification.AccountDelinkedFromAaParams
	7,  // 6: connected_account.notification.CaComms.consent_expired_params:type_name -> connected_account.notification.ConsentExpiredParams
	8,  // 7: connected_account.notification.CaComms.aa_heartbeat_up_params:type_name -> connected_account.notification.AaHeartbeatUpParams
	12, // 8: connected_account.notification.FirstDataPullFailParams.consent:type_name -> connected_account.Consent
	13, // 9: connected_account.notification.FirstDataPullFailParams.attempt:type_name -> connected_account.DataFetchAttempt
	12, // 10: connected_account.notification.FirstDataPullSuccessParams.consent:type_name -> connected_account.Consent
	13, // 11: connected_account.notification.FirstDataPullSuccessParams.attempt:type_name -> connected_account.DataFetchAttempt
	12, // 12: connected_account.notification.ConsentPausedFromAaParams.consent:type_name -> connected_account.Consent
	14, // 13: connected_account.notification.AccountDelinkedFromAaParams.aa_account:type_name -> connected_account.AaAccount
	11, // 14: connected_account.notification.AaNotification.comms_type:type_name -> connected_account.enums.CommsType
	15, // 15: connected_account.notification.AaNotification.comms_channel:type_name -> connected_account.enums.CommsChannel
	16, // 16: connected_account.notification.AaNotification.comms_call_status:type_name -> connected_account.enums.CommsCallStatus
	9,  // 17: connected_account.notification.AaNotification.meta_data:type_name -> connected_account.notification.MetaData
	17, // 18: connected_account.notification.AaNotification.created_at:type_name -> google.protobuf.Timestamp
	17, // 19: connected_account.notification.AaNotification.updated_at:type_name -> google.protobuf.Timestamp
	20, // [20:20] is the sub-list for method output_type
	20, // [20:20] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_connected_account_notification_notification_proto_init() }
func file_api_connected_account_notification_notification_proto_init() {
	if File_api_connected_account_notification_notification_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_connected_account_notification_notification_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaComms); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FirstDataPullFailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FirstDataPullSuccessParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuccessfulConsentGrantParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsentPausedFromAaParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountDelinkedFromAaParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsentExpiredParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaHeartbeatUpParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_notification_notification_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AaNotification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_connected_account_notification_notification_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CaComms_FirstDataPullFailParams)(nil),
		(*CaComms_FirstDataPullSuccessParams)(nil),
		(*CaComms_SuccessfulConsentGrantParams)(nil),
		(*CaComms_ConsentPausedFromAaParams)(nil),
		(*CaComms_AccountDelinkedFromAaParams)(nil),
		(*CaComms_ConsentExpiredParams)(nil),
		(*CaComms_AaHeartbeatUpParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_connected_account_notification_notification_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_connected_account_notification_notification_proto_goTypes,
		DependencyIndexes: file_api_connected_account_notification_notification_proto_depIdxs,
		EnumInfos:         file_api_connected_account_notification_notification_proto_enumTypes,
		MessageInfos:      file_api_connected_account_notification_notification_proto_msgTypes,
	}.Build()
	File_api_connected_account_notification_notification_proto = out.File
	file_api_connected_account_notification_notification_proto_rawDesc = nil
	file_api_connected_account_notification_notification_proto_goTypes = nil
	file_api_connected_account_notification_notification_proto_depIdxs = nil
}
