// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package consumer;

import "api/queue/consumer_headers.proto";
import "api/customdelayqueue/consumer/header.proto";

option go_package = "github.com/epifi/gamma/api/customdelayqueue/consumer";
option java_package = "com.github.epifi.gamma.api.customdelayqueue.consumer";

service OrchestratorConsumer {
  // consumes an event and pushes the event to the actual destination queue (if the delay is already achieved)
  // or to some intermediate queue to achieve the required delay.
  rpc ProcessEvent(OrchestratorConsumerRequest) returns (OrchestratorConsumerResponse) {};
}

message OrchestratorConsumerRequest {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;
  // header containing actual event information with dest queue and delivery time info.
  OrchestratorHeader header = 2;
}

message OrchestratorConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
