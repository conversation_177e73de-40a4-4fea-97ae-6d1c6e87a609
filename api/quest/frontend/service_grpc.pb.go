// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/quest/frontend/service.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Frontend_HomePage_FullMethodName                            = "/api.quest.frontend.Frontend/HomePage"
	Frontend_ListExperimentsPage_FullMethodName                 = "/api.quest.frontend.Frontend/ListExperimentsPage"
	Frontend_NewExperimentPage_FullMethodName                   = "/api.quest.frontend.Frontend/NewExperimentPage"
	Frontend_ListVariables_FullMethodName                       = "/api.quest.frontend.Frontend/ListVariables"
	Frontend_CreateNewExperiment_FullMethodName                 = "/api.quest.frontend.Frontend/CreateNewExperiment"
	Frontend_GetExperiment_FullMethodName                       = "/api.quest.frontend.Frontend/GetExperiment"
	Frontend_GetExperimentMetrics_FullMethodName                = "/api.quest.frontend.Frontend/GetExperimentMetrics"
	Frontend_EditExperimentPage_FullMethodName                  = "/api.quest.frontend.Frontend/EditExperimentPage"
	Frontend_EditExperiment_FullMethodName                      = "/api.quest.frontend.Frontend/EditExperiment"
	Frontend_UpdateExperimentStatus_FullMethodName              = "/api.quest.frontend.Frontend/UpdateExperimentStatus"
	Frontend_UpdateExperimentVersionStatus_FullMethodName       = "/api.quest.frontend.Frontend/UpdateExperimentVersionStatus"
	Frontend_GetNumberOfUsersInSegmentExpression_FullMethodName = "/api.quest.frontend.Frontend/GetNumberOfUsersInSegmentExpression"
	Frontend_GetUserBucketProgressBarValues_FullMethodName      = "/api.quest.frontend.Frontend/GetUserBucketProgressBarValues"
	Frontend_NewExperimentPageV2_FullMethodName                 = "/api.quest.frontend.Frontend/NewExperimentPageV2"
	Frontend_CreateNewExperimentV2_FullMethodName               = "/api.quest.frontend.Frontend/CreateNewExperimentV2"
	Frontend_GetEditExperimentPageData_FullMethodName           = "/api.quest.frontend.Frontend/GetEditExperimentPageData"
	Frontend_EditExperimentV2_FullMethodName                    = "/api.quest.frontend.Frontend/EditExperimentV2"
	Frontend_SaveExperimentDraft_FullMethodName                 = "/api.quest.frontend.Frontend/SaveExperimentDraft"
	Frontend_GetDatalayerForGivenExperiment_FullMethodName      = "/api.quest.frontend.Frontend/GetDatalayerForGivenExperiment"
)

// FrontendClient is the client API for Frontend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FrontendClient interface {
	// RPC to fetch information to display on the home page load.
	// which includes, List of the logged user's experiments and
	HomePage(ctx context.Context, in *HomePageRequest, opts ...grpc.CallOption) (*HomePageResponse, error)
	// RPC to get experiments by filtering with optional attributes of the experiment.
	// ex: Experiments for the logged-in user can be fetched by using filter created_by:<user>
	ListExperimentsPage(ctx context.Context, in *ListExperimentsPageRequest, opts ...grpc.CallOption) (*ListExperimentsPageResponse, error)
	// RPC to fetch data for New Experiment creation page
	// Gives the list of area, metrics, user groups to choose from.
	// Gives the percent of segment that is taken and remaining.
	NewExperimentPage(ctx context.Context, in *NewExperimentPageRequest, opts ...grpc.CallOption) (*NewExperimentPageResponse, error)
	// RPC to list variables available to choose for the given experiment
	ListVariables(ctx context.Context, in *ListVariablesRequest, opts ...grpc.CallOption) (*ListVariablesResponse, error)
	// RPC to create a new experiment with the data provided in the forms
	CreateNewExperiment(ctx context.Context, in *CreateNewExperimentRequest, opts ...grpc.CallOption) (*CreateNewExperimentResponse, error)
	// RPC to fetch details of a single experiment
	// Will return responses based on experiment status
	GetExperiment(ctx context.Context, in *GetExperimentRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error)
	// RPC to fetch metrics details of a single experiment
	GetExperimentMetrics(ctx context.Context, in *GetExperimentMetricsRequest, opts ...grpc.CallOption) (*GetExperimentMetricsResponse, error)
	// RPC to fetch data for edit experiment page
	EditExperimentPage(ctx context.Context, in *EditExperimentPageRequest, opts ...grpc.CallOption) (*EditExperimentPageResponse, error)
	// RPC to edit experiment values based on data provided in the form
	// if experiment is in running / Approved / Paused state, new experiment version is created
	// if experiment is not approved, existing experiment version is updated
	EditExperiment(ctx context.Context, in *EditExperimentRequest, opts ...grpc.CallOption) (*EditExperimentResponse, error)
	UpdateExperimentStatus(ctx context.Context, in *UpdateExperimentStatusRequest, opts ...grpc.CallOption) (*UpdateExperimentStatusResponse, error)
	UpdateExperimentVersionStatus(ctx context.Context, in *UpdateExperimentVersionStatusRequest, opts ...grpc.CallOption) (*UpdateExperimentVersionStatusResponse, error)
	GetNumberOfUsersInSegmentExpression(ctx context.Context, in *GetNumberOfUsersInSegmentExpressionRequest, opts ...grpc.CallOption) (*GetNumberOfUsersInSegmentExpressionResponse, error)
	GetUserBucketProgressBarValues(ctx context.Context, in *GetUserBucketProgressBarValuesRequest, opts ...grpc.CallOption) (*GetUserBucketProgressBarValuesResponse, error)
	// RPC to fetch data for New Experiment creation & edit page
	NewExperimentPageV2(ctx context.Context, in *NewExperimentPageV2Request, opts ...grpc.CallOption) (*NewExperimentPageV2Response, error)
	// RPC to create a new v2 experiment with the data provided in the forms
	CreateNewExperimentV2(ctx context.Context, in *CreateNewExperimentV2Request, opts ...grpc.CallOption) (*CreateNewExperimentV2Response, error)
	// RPC to fetch data for edit experiment page v2
	GetEditExperimentPageData(ctx context.Context, in *GetEditExperimentPageDataRequest, opts ...grpc.CallOption) (*GetEditExperimentPageDataResponse, error)
	// RPC to edit experiment v2 values based on data provided in the form
	// if experiment is in running / Approved / Paused state, new experiment version is created
	// if experiment is not approved, existing experiment version is updated
	EditExperimentV2(ctx context.Context, in *EditExperimentV2Request, opts ...grpc.CallOption) (*EditExperimentV2Response, error)
	// RPC to save the draft of the experiment
	SaveExperimentDraft(ctx context.Context, in *SaveExperimentDraftRequest, opts ...grpc.CallOption) (*SaveExperimentDraftResponse, error)
	// RPC to get datalayer values for given experiment
	GetDatalayerForGivenExperiment(ctx context.Context, in *GetDatalayerForGivenExperimentRequest, opts ...grpc.CallOption) (*GetDatalayerForGivenExperimentResponse, error)
}

type frontendClient struct {
	cc grpc.ClientConnInterface
}

func NewFrontendClient(cc grpc.ClientConnInterface) FrontendClient {
	return &frontendClient{cc}
}

func (c *frontendClient) HomePage(ctx context.Context, in *HomePageRequest, opts ...grpc.CallOption) (*HomePageResponse, error) {
	out := new(HomePageResponse)
	err := c.cc.Invoke(ctx, Frontend_HomePage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) ListExperimentsPage(ctx context.Context, in *ListExperimentsPageRequest, opts ...grpc.CallOption) (*ListExperimentsPageResponse, error) {
	out := new(ListExperimentsPageResponse)
	err := c.cc.Invoke(ctx, Frontend_ListExperimentsPage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) NewExperimentPage(ctx context.Context, in *NewExperimentPageRequest, opts ...grpc.CallOption) (*NewExperimentPageResponse, error) {
	out := new(NewExperimentPageResponse)
	err := c.cc.Invoke(ctx, Frontend_NewExperimentPage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) ListVariables(ctx context.Context, in *ListVariablesRequest, opts ...grpc.CallOption) (*ListVariablesResponse, error) {
	out := new(ListVariablesResponse)
	err := c.cc.Invoke(ctx, Frontend_ListVariables_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) CreateNewExperiment(ctx context.Context, in *CreateNewExperimentRequest, opts ...grpc.CallOption) (*CreateNewExperimentResponse, error) {
	out := new(CreateNewExperimentResponse)
	err := c.cc.Invoke(ctx, Frontend_CreateNewExperiment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) GetExperiment(ctx context.Context, in *GetExperimentRequest, opts ...grpc.CallOption) (*GetExperimentResponse, error) {
	out := new(GetExperimentResponse)
	err := c.cc.Invoke(ctx, Frontend_GetExperiment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) GetExperimentMetrics(ctx context.Context, in *GetExperimentMetricsRequest, opts ...grpc.CallOption) (*GetExperimentMetricsResponse, error) {
	out := new(GetExperimentMetricsResponse)
	err := c.cc.Invoke(ctx, Frontend_GetExperimentMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) EditExperimentPage(ctx context.Context, in *EditExperimentPageRequest, opts ...grpc.CallOption) (*EditExperimentPageResponse, error) {
	out := new(EditExperimentPageResponse)
	err := c.cc.Invoke(ctx, Frontend_EditExperimentPage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) EditExperiment(ctx context.Context, in *EditExperimentRequest, opts ...grpc.CallOption) (*EditExperimentResponse, error) {
	out := new(EditExperimentResponse)
	err := c.cc.Invoke(ctx, Frontend_EditExperiment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) UpdateExperimentStatus(ctx context.Context, in *UpdateExperimentStatusRequest, opts ...grpc.CallOption) (*UpdateExperimentStatusResponse, error) {
	out := new(UpdateExperimentStatusResponse)
	err := c.cc.Invoke(ctx, Frontend_UpdateExperimentStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) UpdateExperimentVersionStatus(ctx context.Context, in *UpdateExperimentVersionStatusRequest, opts ...grpc.CallOption) (*UpdateExperimentVersionStatusResponse, error) {
	out := new(UpdateExperimentVersionStatusResponse)
	err := c.cc.Invoke(ctx, Frontend_UpdateExperimentVersionStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) GetNumberOfUsersInSegmentExpression(ctx context.Context, in *GetNumberOfUsersInSegmentExpressionRequest, opts ...grpc.CallOption) (*GetNumberOfUsersInSegmentExpressionResponse, error) {
	out := new(GetNumberOfUsersInSegmentExpressionResponse)
	err := c.cc.Invoke(ctx, Frontend_GetNumberOfUsersInSegmentExpression_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) GetUserBucketProgressBarValues(ctx context.Context, in *GetUserBucketProgressBarValuesRequest, opts ...grpc.CallOption) (*GetUserBucketProgressBarValuesResponse, error) {
	out := new(GetUserBucketProgressBarValuesResponse)
	err := c.cc.Invoke(ctx, Frontend_GetUserBucketProgressBarValues_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) NewExperimentPageV2(ctx context.Context, in *NewExperimentPageV2Request, opts ...grpc.CallOption) (*NewExperimentPageV2Response, error) {
	out := new(NewExperimentPageV2Response)
	err := c.cc.Invoke(ctx, Frontend_NewExperimentPageV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) CreateNewExperimentV2(ctx context.Context, in *CreateNewExperimentV2Request, opts ...grpc.CallOption) (*CreateNewExperimentV2Response, error) {
	out := new(CreateNewExperimentV2Response)
	err := c.cc.Invoke(ctx, Frontend_CreateNewExperimentV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) GetEditExperimentPageData(ctx context.Context, in *GetEditExperimentPageDataRequest, opts ...grpc.CallOption) (*GetEditExperimentPageDataResponse, error) {
	out := new(GetEditExperimentPageDataResponse)
	err := c.cc.Invoke(ctx, Frontend_GetEditExperimentPageData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) EditExperimentV2(ctx context.Context, in *EditExperimentV2Request, opts ...grpc.CallOption) (*EditExperimentV2Response, error) {
	out := new(EditExperimentV2Response)
	err := c.cc.Invoke(ctx, Frontend_EditExperimentV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) SaveExperimentDraft(ctx context.Context, in *SaveExperimentDraftRequest, opts ...grpc.CallOption) (*SaveExperimentDraftResponse, error) {
	out := new(SaveExperimentDraftResponse)
	err := c.cc.Invoke(ctx, Frontend_SaveExperimentDraft_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *frontendClient) GetDatalayerForGivenExperiment(ctx context.Context, in *GetDatalayerForGivenExperimentRequest, opts ...grpc.CallOption) (*GetDatalayerForGivenExperimentResponse, error) {
	out := new(GetDatalayerForGivenExperimentResponse)
	err := c.cc.Invoke(ctx, Frontend_GetDatalayerForGivenExperiment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FrontendServer is the server API for Frontend service.
// All implementations should embed UnimplementedFrontendServer
// for forward compatibility
type FrontendServer interface {
	// RPC to fetch information to display on the home page load.
	// which includes, List of the logged user's experiments and
	HomePage(context.Context, *HomePageRequest) (*HomePageResponse, error)
	// RPC to get experiments by filtering with optional attributes of the experiment.
	// ex: Experiments for the logged-in user can be fetched by using filter created_by:<user>
	ListExperimentsPage(context.Context, *ListExperimentsPageRequest) (*ListExperimentsPageResponse, error)
	// RPC to fetch data for New Experiment creation page
	// Gives the list of area, metrics, user groups to choose from.
	// Gives the percent of segment that is taken and remaining.
	NewExperimentPage(context.Context, *NewExperimentPageRequest) (*NewExperimentPageResponse, error)
	// RPC to list variables available to choose for the given experiment
	ListVariables(context.Context, *ListVariablesRequest) (*ListVariablesResponse, error)
	// RPC to create a new experiment with the data provided in the forms
	CreateNewExperiment(context.Context, *CreateNewExperimentRequest) (*CreateNewExperimentResponse, error)
	// RPC to fetch details of a single experiment
	// Will return responses based on experiment status
	GetExperiment(context.Context, *GetExperimentRequest) (*GetExperimentResponse, error)
	// RPC to fetch metrics details of a single experiment
	GetExperimentMetrics(context.Context, *GetExperimentMetricsRequest) (*GetExperimentMetricsResponse, error)
	// RPC to fetch data for edit experiment page
	EditExperimentPage(context.Context, *EditExperimentPageRequest) (*EditExperimentPageResponse, error)
	// RPC to edit experiment values based on data provided in the form
	// if experiment is in running / Approved / Paused state, new experiment version is created
	// if experiment is not approved, existing experiment version is updated
	EditExperiment(context.Context, *EditExperimentRequest) (*EditExperimentResponse, error)
	UpdateExperimentStatus(context.Context, *UpdateExperimentStatusRequest) (*UpdateExperimentStatusResponse, error)
	UpdateExperimentVersionStatus(context.Context, *UpdateExperimentVersionStatusRequest) (*UpdateExperimentVersionStatusResponse, error)
	GetNumberOfUsersInSegmentExpression(context.Context, *GetNumberOfUsersInSegmentExpressionRequest) (*GetNumberOfUsersInSegmentExpressionResponse, error)
	GetUserBucketProgressBarValues(context.Context, *GetUserBucketProgressBarValuesRequest) (*GetUserBucketProgressBarValuesResponse, error)
	// RPC to fetch data for New Experiment creation & edit page
	NewExperimentPageV2(context.Context, *NewExperimentPageV2Request) (*NewExperimentPageV2Response, error)
	// RPC to create a new v2 experiment with the data provided in the forms
	CreateNewExperimentV2(context.Context, *CreateNewExperimentV2Request) (*CreateNewExperimentV2Response, error)
	// RPC to fetch data for edit experiment page v2
	GetEditExperimentPageData(context.Context, *GetEditExperimentPageDataRequest) (*GetEditExperimentPageDataResponse, error)
	// RPC to edit experiment v2 values based on data provided in the form
	// if experiment is in running / Approved / Paused state, new experiment version is created
	// if experiment is not approved, existing experiment version is updated
	EditExperimentV2(context.Context, *EditExperimentV2Request) (*EditExperimentV2Response, error)
	// RPC to save the draft of the experiment
	SaveExperimentDraft(context.Context, *SaveExperimentDraftRequest) (*SaveExperimentDraftResponse, error)
	// RPC to get datalayer values for given experiment
	GetDatalayerForGivenExperiment(context.Context, *GetDatalayerForGivenExperimentRequest) (*GetDatalayerForGivenExperimentResponse, error)
}

// UnimplementedFrontendServer should be embedded to have forward compatible implementations.
type UnimplementedFrontendServer struct {
}

func (UnimplementedFrontendServer) HomePage(context.Context, *HomePageRequest) (*HomePageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HomePage not implemented")
}
func (UnimplementedFrontendServer) ListExperimentsPage(context.Context, *ListExperimentsPageRequest) (*ListExperimentsPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExperimentsPage not implemented")
}
func (UnimplementedFrontendServer) NewExperimentPage(context.Context, *NewExperimentPageRequest) (*NewExperimentPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewExperimentPage not implemented")
}
func (UnimplementedFrontendServer) ListVariables(context.Context, *ListVariablesRequest) (*ListVariablesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVariables not implemented")
}
func (UnimplementedFrontendServer) CreateNewExperiment(context.Context, *CreateNewExperimentRequest) (*CreateNewExperimentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNewExperiment not implemented")
}
func (UnimplementedFrontendServer) GetExperiment(context.Context, *GetExperimentRequest) (*GetExperimentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExperiment not implemented")
}
func (UnimplementedFrontendServer) GetExperimentMetrics(context.Context, *GetExperimentMetricsRequest) (*GetExperimentMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExperimentMetrics not implemented")
}
func (UnimplementedFrontendServer) EditExperimentPage(context.Context, *EditExperimentPageRequest) (*EditExperimentPageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditExperimentPage not implemented")
}
func (UnimplementedFrontendServer) EditExperiment(context.Context, *EditExperimentRequest) (*EditExperimentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditExperiment not implemented")
}
func (UnimplementedFrontendServer) UpdateExperimentStatus(context.Context, *UpdateExperimentStatusRequest) (*UpdateExperimentStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExperimentStatus not implemented")
}
func (UnimplementedFrontendServer) UpdateExperimentVersionStatus(context.Context, *UpdateExperimentVersionStatusRequest) (*UpdateExperimentVersionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExperimentVersionStatus not implemented")
}
func (UnimplementedFrontendServer) GetNumberOfUsersInSegmentExpression(context.Context, *GetNumberOfUsersInSegmentExpressionRequest) (*GetNumberOfUsersInSegmentExpressionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNumberOfUsersInSegmentExpression not implemented")
}
func (UnimplementedFrontendServer) GetUserBucketProgressBarValues(context.Context, *GetUserBucketProgressBarValuesRequest) (*GetUserBucketProgressBarValuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserBucketProgressBarValues not implemented")
}
func (UnimplementedFrontendServer) NewExperimentPageV2(context.Context, *NewExperimentPageV2Request) (*NewExperimentPageV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewExperimentPageV2 not implemented")
}
func (UnimplementedFrontendServer) CreateNewExperimentV2(context.Context, *CreateNewExperimentV2Request) (*CreateNewExperimentV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNewExperimentV2 not implemented")
}
func (UnimplementedFrontendServer) GetEditExperimentPageData(context.Context, *GetEditExperimentPageDataRequest) (*GetEditExperimentPageDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEditExperimentPageData not implemented")
}
func (UnimplementedFrontendServer) EditExperimentV2(context.Context, *EditExperimentV2Request) (*EditExperimentV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditExperimentV2 not implemented")
}
func (UnimplementedFrontendServer) SaveExperimentDraft(context.Context, *SaveExperimentDraftRequest) (*SaveExperimentDraftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveExperimentDraft not implemented")
}
func (UnimplementedFrontendServer) GetDatalayerForGivenExperiment(context.Context, *GetDatalayerForGivenExperimentRequest) (*GetDatalayerForGivenExperimentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDatalayerForGivenExperiment not implemented")
}

// UnsafeFrontendServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FrontendServer will
// result in compilation errors.
type UnsafeFrontendServer interface {
	mustEmbedUnimplementedFrontendServer()
}

func RegisterFrontendServer(s grpc.ServiceRegistrar, srv FrontendServer) {
	s.RegisterService(&Frontend_ServiceDesc, srv)
}

func _Frontend_HomePage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HomePageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).HomePage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_HomePage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).HomePage(ctx, req.(*HomePageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_ListExperimentsPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExperimentsPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).ListExperimentsPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_ListExperimentsPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).ListExperimentsPage(ctx, req.(*ListExperimentsPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_NewExperimentPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewExperimentPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).NewExperimentPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_NewExperimentPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).NewExperimentPage(ctx, req.(*NewExperimentPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_ListVariables_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVariablesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).ListVariables(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_ListVariables_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).ListVariables(ctx, req.(*ListVariablesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_CreateNewExperiment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNewExperimentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).CreateNewExperiment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_CreateNewExperiment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).CreateNewExperiment(ctx, req.(*CreateNewExperimentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_GetExperiment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExperimentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).GetExperiment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_GetExperiment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).GetExperiment(ctx, req.(*GetExperimentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_GetExperimentMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExperimentMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).GetExperimentMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_GetExperimentMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).GetExperimentMetrics(ctx, req.(*GetExperimentMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_EditExperimentPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditExperimentPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).EditExperimentPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_EditExperimentPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).EditExperimentPage(ctx, req.(*EditExperimentPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_EditExperiment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditExperimentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).EditExperiment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_EditExperiment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).EditExperiment(ctx, req.(*EditExperimentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_UpdateExperimentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateExperimentStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).UpdateExperimentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_UpdateExperimentStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).UpdateExperimentStatus(ctx, req.(*UpdateExperimentStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_UpdateExperimentVersionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateExperimentVersionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).UpdateExperimentVersionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_UpdateExperimentVersionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).UpdateExperimentVersionStatus(ctx, req.(*UpdateExperimentVersionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_GetNumberOfUsersInSegmentExpression_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNumberOfUsersInSegmentExpressionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).GetNumberOfUsersInSegmentExpression(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_GetNumberOfUsersInSegmentExpression_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).GetNumberOfUsersInSegmentExpression(ctx, req.(*GetNumberOfUsersInSegmentExpressionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_GetUserBucketProgressBarValues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBucketProgressBarValuesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).GetUserBucketProgressBarValues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_GetUserBucketProgressBarValues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).GetUserBucketProgressBarValues(ctx, req.(*GetUserBucketProgressBarValuesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_NewExperimentPageV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NewExperimentPageV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).NewExperimentPageV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_NewExperimentPageV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).NewExperimentPageV2(ctx, req.(*NewExperimentPageV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_CreateNewExperimentV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNewExperimentV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).CreateNewExperimentV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_CreateNewExperimentV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).CreateNewExperimentV2(ctx, req.(*CreateNewExperimentV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_GetEditExperimentPageData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEditExperimentPageDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).GetEditExperimentPageData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_GetEditExperimentPageData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).GetEditExperimentPageData(ctx, req.(*GetEditExperimentPageDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_EditExperimentV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditExperimentV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).EditExperimentV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_EditExperimentV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).EditExperimentV2(ctx, req.(*EditExperimentV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_SaveExperimentDraft_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveExperimentDraftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).SaveExperimentDraft(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_SaveExperimentDraft_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).SaveExperimentDraft(ctx, req.(*SaveExperimentDraftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Frontend_GetDatalayerForGivenExperiment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDatalayerForGivenExperimentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FrontendServer).GetDatalayerForGivenExperiment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Frontend_GetDatalayerForGivenExperiment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FrontendServer).GetDatalayerForGivenExperiment(ctx, req.(*GetDatalayerForGivenExperimentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Frontend_ServiceDesc is the grpc.ServiceDesc for Frontend service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Frontend_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.quest.frontend.Frontend",
	HandlerType: (*FrontendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HomePage",
			Handler:    _Frontend_HomePage_Handler,
		},
		{
			MethodName: "ListExperimentsPage",
			Handler:    _Frontend_ListExperimentsPage_Handler,
		},
		{
			MethodName: "NewExperimentPage",
			Handler:    _Frontend_NewExperimentPage_Handler,
		},
		{
			MethodName: "ListVariables",
			Handler:    _Frontend_ListVariables_Handler,
		},
		{
			MethodName: "CreateNewExperiment",
			Handler:    _Frontend_CreateNewExperiment_Handler,
		},
		{
			MethodName: "GetExperiment",
			Handler:    _Frontend_GetExperiment_Handler,
		},
		{
			MethodName: "GetExperimentMetrics",
			Handler:    _Frontend_GetExperimentMetrics_Handler,
		},
		{
			MethodName: "EditExperimentPage",
			Handler:    _Frontend_EditExperimentPage_Handler,
		},
		{
			MethodName: "EditExperiment",
			Handler:    _Frontend_EditExperiment_Handler,
		},
		{
			MethodName: "UpdateExperimentStatus",
			Handler:    _Frontend_UpdateExperimentStatus_Handler,
		},
		{
			MethodName: "UpdateExperimentVersionStatus",
			Handler:    _Frontend_UpdateExperimentVersionStatus_Handler,
		},
		{
			MethodName: "GetNumberOfUsersInSegmentExpression",
			Handler:    _Frontend_GetNumberOfUsersInSegmentExpression_Handler,
		},
		{
			MethodName: "GetUserBucketProgressBarValues",
			Handler:    _Frontend_GetUserBucketProgressBarValues_Handler,
		},
		{
			MethodName: "NewExperimentPageV2",
			Handler:    _Frontend_NewExperimentPageV2_Handler,
		},
		{
			MethodName: "CreateNewExperimentV2",
			Handler:    _Frontend_CreateNewExperimentV2_Handler,
		},
		{
			MethodName: "GetEditExperimentPageData",
			Handler:    _Frontend_GetEditExperimentPageData_Handler,
		},
		{
			MethodName: "EditExperimentV2",
			Handler:    _Frontend_EditExperimentV2_Handler,
		},
		{
			MethodName: "SaveExperimentDraft",
			Handler:    _Frontend_SaveExperimentDraft_Handler,
		},
		{
			MethodName: "GetDatalayerForGivenExperiment",
			Handler:    _Frontend_GetDatalayerForGivenExperiment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/quest/frontend/service.proto",
}
