// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/consent/service.proto

package consent

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Consent_RecordConsent_FullMethodName           = "/consent.Consent/RecordConsent"
	Consent_FetchConsent_FullMethodName            = "/consent.Consent/FetchConsent"
	Consent_FetchConsentByReqId_FullMethodName     = "/consent.Consent/FetchConsentByReqId"
	Consent_CheckConsentRequirement_FullMethodName = "/consent.Consent/CheckConsentRequirement"
	Consent_RecordConsents_FullMethodName          = "/consent.Consent/RecordConsents"
	Consent_DeleteConsents_FullMethodName          = "/consent.Consent/DeleteConsents"
)

// ConsentClient is the client API for Consent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConsentClient interface {
	// Deprecated: Do not use.
	// Deprecated in favour of RecordConsents
	RecordConsent(ctx context.Context, in *RecordConsentRequest, opts ...grpc.CallOption) (*RecordConsentResponse, error)
	// Fetches the the actor's consent details with timestamp
	FetchConsent(ctx context.Context, in *FetchConsentRequest, opts ...grpc.CallOption) (*FetchConsentResponse, error)
	// Fetches the the actor's consent details by client_req_id
	FetchConsentByReqId(ctx context.Context, in *FetchConsentByReqIdRequest, opts ...grpc.CallOption) (*FetchConsentByReqIdResponse, error)
	// Check consent collection required or not
	CheckConsentRequirement(ctx context.Context, in *CheckConsentRequirementRequest, opts ...grpc.CallOption) (*CheckConsentRequirementResponse, error)
	// Records multiple consents for the actor with timestamp
	RecordConsents(ctx context.Context, in *RecordConsentsRequest, opts ...grpc.CallOption) (*RecordConsentsResponse, error)
	// DeleteConsents soft deletes the consents for the actor
	DeleteConsents(ctx context.Context, in *DeleteConsentsRequest, opts ...grpc.CallOption) (*DeleteConsentsResponse, error)
}

type consentClient struct {
	cc grpc.ClientConnInterface
}

func NewConsentClient(cc grpc.ClientConnInterface) ConsentClient {
	return &consentClient{cc}
}

// Deprecated: Do not use.
func (c *consentClient) RecordConsent(ctx context.Context, in *RecordConsentRequest, opts ...grpc.CallOption) (*RecordConsentResponse, error) {
	out := new(RecordConsentResponse)
	err := c.cc.Invoke(ctx, Consent_RecordConsent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consentClient) FetchConsent(ctx context.Context, in *FetchConsentRequest, opts ...grpc.CallOption) (*FetchConsentResponse, error) {
	out := new(FetchConsentResponse)
	err := c.cc.Invoke(ctx, Consent_FetchConsent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consentClient) FetchConsentByReqId(ctx context.Context, in *FetchConsentByReqIdRequest, opts ...grpc.CallOption) (*FetchConsentByReqIdResponse, error) {
	out := new(FetchConsentByReqIdResponse)
	err := c.cc.Invoke(ctx, Consent_FetchConsentByReqId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consentClient) CheckConsentRequirement(ctx context.Context, in *CheckConsentRequirementRequest, opts ...grpc.CallOption) (*CheckConsentRequirementResponse, error) {
	out := new(CheckConsentRequirementResponse)
	err := c.cc.Invoke(ctx, Consent_CheckConsentRequirement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consentClient) RecordConsents(ctx context.Context, in *RecordConsentsRequest, opts ...grpc.CallOption) (*RecordConsentsResponse, error) {
	out := new(RecordConsentsResponse)
	err := c.cc.Invoke(ctx, Consent_RecordConsents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *consentClient) DeleteConsents(ctx context.Context, in *DeleteConsentsRequest, opts ...grpc.CallOption) (*DeleteConsentsResponse, error) {
	out := new(DeleteConsentsResponse)
	err := c.cc.Invoke(ctx, Consent_DeleteConsents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConsentServer is the server API for Consent service.
// All implementations should embed UnimplementedConsentServer
// for forward compatibility
type ConsentServer interface {
	// Deprecated: Do not use.
	// Deprecated in favour of RecordConsents
	RecordConsent(context.Context, *RecordConsentRequest) (*RecordConsentResponse, error)
	// Fetches the the actor's consent details with timestamp
	FetchConsent(context.Context, *FetchConsentRequest) (*FetchConsentResponse, error)
	// Fetches the the actor's consent details by client_req_id
	FetchConsentByReqId(context.Context, *FetchConsentByReqIdRequest) (*FetchConsentByReqIdResponse, error)
	// Check consent collection required or not
	CheckConsentRequirement(context.Context, *CheckConsentRequirementRequest) (*CheckConsentRequirementResponse, error)
	// Records multiple consents for the actor with timestamp
	RecordConsents(context.Context, *RecordConsentsRequest) (*RecordConsentsResponse, error)
	// DeleteConsents soft deletes the consents for the actor
	DeleteConsents(context.Context, *DeleteConsentsRequest) (*DeleteConsentsResponse, error)
}

// UnimplementedConsentServer should be embedded to have forward compatible implementations.
type UnimplementedConsentServer struct {
}

func (UnimplementedConsentServer) RecordConsent(context.Context, *RecordConsentRequest) (*RecordConsentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordConsent not implemented")
}
func (UnimplementedConsentServer) FetchConsent(context.Context, *FetchConsentRequest) (*FetchConsentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchConsent not implemented")
}
func (UnimplementedConsentServer) FetchConsentByReqId(context.Context, *FetchConsentByReqIdRequest) (*FetchConsentByReqIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchConsentByReqId not implemented")
}
func (UnimplementedConsentServer) CheckConsentRequirement(context.Context, *CheckConsentRequirementRequest) (*CheckConsentRequirementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckConsentRequirement not implemented")
}
func (UnimplementedConsentServer) RecordConsents(context.Context, *RecordConsentsRequest) (*RecordConsentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordConsents not implemented")
}
func (UnimplementedConsentServer) DeleteConsents(context.Context, *DeleteConsentsRequest) (*DeleteConsentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteConsents not implemented")
}

// UnsafeConsentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConsentServer will
// result in compilation errors.
type UnsafeConsentServer interface {
	mustEmbedUnimplementedConsentServer()
}

func RegisterConsentServer(s grpc.ServiceRegistrar, srv ConsentServer) {
	s.RegisterService(&Consent_ServiceDesc, srv)
}

func _Consent_RecordConsent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordConsentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsentServer).RecordConsent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consent_RecordConsent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsentServer).RecordConsent(ctx, req.(*RecordConsentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consent_FetchConsent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchConsentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsentServer).FetchConsent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consent_FetchConsent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsentServer).FetchConsent(ctx, req.(*FetchConsentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consent_FetchConsentByReqId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchConsentByReqIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsentServer).FetchConsentByReqId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consent_FetchConsentByReqId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsentServer).FetchConsentByReqId(ctx, req.(*FetchConsentByReqIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consent_CheckConsentRequirement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckConsentRequirementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsentServer).CheckConsentRequirement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consent_CheckConsentRequirement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsentServer).CheckConsentRequirement(ctx, req.(*CheckConsentRequirementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consent_RecordConsents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordConsentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsentServer).RecordConsents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consent_RecordConsents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsentServer).RecordConsents(ctx, req.(*RecordConsentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Consent_DeleteConsents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConsentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConsentServer).DeleteConsents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Consent_DeleteConsents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConsentServer).DeleteConsents(ctx, req.(*DeleteConsentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Consent_ServiceDesc is the grpc.ServiceDesc for Consent service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Consent_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "consent.Consent",
	HandlerType: (*ConsentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RecordConsent",
			Handler:    _Consent_RecordConsent_Handler,
		},
		{
			MethodName: "FetchConsent",
			Handler:    _Consent_FetchConsent_Handler,
		},
		{
			MethodName: "FetchConsentByReqId",
			Handler:    _Consent_FetchConsentByReqId_Handler,
		},
		{
			MethodName: "CheckConsentRequirement",
			Handler:    _Consent_CheckConsentRequirement_Handler,
		},
		{
			MethodName: "RecordConsents",
			Handler:    _Consent_RecordConsents_Handler,
		},
		{
			MethodName: "DeleteConsents",
			Handler:    _Consent_DeleteConsents_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/consent/service.proto",
}
