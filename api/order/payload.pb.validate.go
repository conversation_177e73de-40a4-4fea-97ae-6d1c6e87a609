// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/order/payload.proto

package order

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	payment "github.com/epifi/gamma/api/order/payment"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = payment.PaymentProtocol(0)
)

// Validate checks the field values on PaymentDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PaymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaymentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaymentDetailsMultiError,
// or nil if none found.
func (m *PaymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PaymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetPiFrom()) < 1 {
		err := PaymentDetailsValidationError{
			field:  "PiFrom",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetPiTo()) < 1 {
		err := PaymentDetailsValidationError{
			field:  "PiTo",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() == nil {
		err := PaymentDetailsValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PaymentDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PaymentDetailsValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PaymentDetailsValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	// no validation rules for ReqId

	if _, ok := _PaymentDetails_PaymentProtocol_NotInLookup[m.GetPaymentProtocol()]; ok {
		err := PaymentDetailsValidationError{
			field:  "PaymentProtocol",
			reason: "value must not be in list [PAYMENT_PROTOCOL_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for MerchantRefId

	// no validation rules for ReferenceUrl

	// no validation rules for InitiationMode

	// no validation rules for Purpose

	// no validation rules for Mcc

	// no validation rules for MerchantId

	// no validation rules for MerchantStoreId

	// no validation rules for MerchantTerminalId

	// no validation rules for OrgId

	// no validation rules for MerchantGenre

	if len(errors) > 0 {
		return PaymentDetailsMultiError(errors)
	}

	return nil
}

// PaymentDetailsMultiError is an error wrapping multiple validation errors
// returned by PaymentDetails.ValidateAll() if the designated constraints
// aren't met.
type PaymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentDetailsMultiError) AllErrors() []error { return m }

// PaymentDetailsValidationError is the validation error returned by
// PaymentDetails.Validate if the designated constraints aren't met.
type PaymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentDetailsValidationError) ErrorName() string { return "PaymentDetailsValidationError" }

// Error satisfies the builtin error interface
func (e PaymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentDetailsValidationError{}

var _PaymentDetails_PaymentProtocol_NotInLookup = map[payment.PaymentProtocol]struct{}{
	0: {},
}

// Validate checks the field values on P2PFundTransfer with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *P2PFundTransfer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on P2PFundTransfer with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// P2PFundTransferMultiError, or nil if none found.
func (m *P2PFundTransfer) ValidateAll() error {
	return m.validate(true)
}

func (m *P2PFundTransfer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, P2PFundTransferValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, P2PFundTransferValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return P2PFundTransferValidationError{
				field:  "PaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayerAccountId

	if len(errors) > 0 {
		return P2PFundTransferMultiError(errors)
	}

	return nil
}

// P2PFundTransferMultiError is an error wrapping multiple validation errors
// returned by P2PFundTransfer.ValidateAll() if the designated constraints
// aren't met.
type P2PFundTransferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m P2PFundTransferMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m P2PFundTransferMultiError) AllErrors() []error { return m }

// P2PFundTransferValidationError is the validation error returned by
// P2PFundTransfer.Validate if the designated constraints aren't met.
type P2PFundTransferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e P2PFundTransferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e P2PFundTransferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e P2PFundTransferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e P2PFundTransferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e P2PFundTransferValidationError) ErrorName() string { return "P2PFundTransferValidationError" }

// Error satisfies the builtin error interface
func (e P2PFundTransferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sP2PFundTransfer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = P2PFundTransferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = P2PFundTransferValidationError{}

// Validate checks the field values on P2PCollect with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *P2PCollect) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on P2PCollect with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in P2PCollectMultiError, or
// nil if none found.
func (m *P2PCollect) ValidateAll() error {
	return m.validate(true)
}

func (m *P2PCollect) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, P2PCollectValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, P2PCollectValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return P2PCollectValidationError{
				field:  "PaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return P2PCollectMultiError(errors)
	}

	return nil
}

// P2PCollectMultiError is an error wrapping multiple validation errors
// returned by P2PCollect.ValidateAll() if the designated constraints aren't met.
type P2PCollectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m P2PCollectMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m P2PCollectMultiError) AllErrors() []error { return m }

// P2PCollectValidationError is the validation error returned by
// P2PCollect.Validate if the designated constraints aren't met.
type P2PCollectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e P2PCollectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e P2PCollectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e P2PCollectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e P2PCollectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e P2PCollectValidationError) ErrorName() string { return "P2PCollectValidationError" }

// Error satisfies the builtin error interface
func (e P2PCollectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sP2PCollect.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = P2PCollectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = P2PCollectValidationError{}

// Validate checks the field values on P2PCollectShortCircuit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *P2PCollectShortCircuit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on P2PCollectShortCircuit with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// P2PCollectShortCircuitMultiError, or nil if none found.
func (m *P2PCollectShortCircuit) ValidateAll() error {
	return m.validate(true)
}

func (m *P2PCollectShortCircuit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPaymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, P2PCollectShortCircuitValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, P2PCollectShortCircuitValidationError{
					field:  "PaymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return P2PCollectShortCircuitValidationError{
				field:  "PaymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PayeeAccountId

	// no validation rules for PayerAccountId

	if len(errors) > 0 {
		return P2PCollectShortCircuitMultiError(errors)
	}

	return nil
}

// P2PCollectShortCircuitMultiError is an error wrapping multiple validation
// errors returned by P2PCollectShortCircuit.ValidateAll() if the designated
// constraints aren't met.
type P2PCollectShortCircuitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m P2PCollectShortCircuitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m P2PCollectShortCircuitMultiError) AllErrors() []error { return m }

// P2PCollectShortCircuitValidationError is the validation error returned by
// P2PCollectShortCircuit.Validate if the designated constraints aren't met.
type P2PCollectShortCircuitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e P2PCollectShortCircuitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e P2PCollectShortCircuitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e P2PCollectShortCircuitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e P2PCollectShortCircuitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e P2PCollectShortCircuitValidationError) ErrorName() string {
	return "P2PCollectShortCircuitValidationError"
}

// Error satisfies the builtin error interface
func (e P2PCollectShortCircuitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sP2PCollectShortCircuit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = P2PCollectShortCircuitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = P2PCollectShortCircuitValidationError{}
