// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/order";
option java_package = "com.github.epifi.gamma.api.order";

message ActorAddFundOptionsMap {
  // id to uniquely identify an entry in the DB
  string id = 1;

  // actor id corresponding to the actor for whom the option is
  string actor_id = 2;

  // add fund option for the actor
  AddFundOption add_fund_option = 3;

  // Standard timestamp fields
  // timestamp corresponding to entry creation
  google.protobuf.Timestamp created_at = 4;

  // timestamp corresponding to last update of entry
  google.protobuf.Timestamp updated_at = 5;

  // timestamp corresponding to the deletion of entry (to be populated in case entry was soft deleted)
  google.protobuf.Timestamp deleted_at = 6;
}

message AddFundOption {
  // default amount shown to the user
  google.type.Money default_amount = 1;
  // minimum amount allowed for add funds option
  google.type.Money min_amount = 2;
  // maximum amount allowed for add funds option
  google.type.Money max_amount = 3;
  // list of suggested amounts
  repeated AmountWithTag suggested_amounts = 4;

  message RewardsBanner {
    // description of the rewards banner
    string description = 1;
    // icon url for the rewards banner
    string icon_url = 2;
  }
  // reward banner for the option
  RewardsBanner rewards_banner = 5;

  // denotes if the user should be blocked until the
  // first leg is completed
  bool is_blocking = 6;
}

// amount and it's corresponding tag which is shown to the user while adding funds
message AmountWithTag {
  google.type.Money amount = 1;

  // tag for the amount
  message Tag {
    // title of the tag. eg. Popular
    string title = 1;
    // url of the icon associated with the tag
    string icon_url = 2;
  }

  // tags associated with the amount
  repeated Tag tags = 2;
}
