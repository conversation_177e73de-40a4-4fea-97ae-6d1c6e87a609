// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package order.payment.notification;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/order/payment/notification";
option java_package = "com.github.epifi.gamma.api.order.payment.notification";


// TransactionNotificationMap keeps track of whether or not a notification has been sent to Payer/Payee for debit/credit for a transaction
message TransactionNotificationMap {
  // id to identify the transaction
  string transaction_id = 1;

  // id returned by the comms service after credit sms is sent
  string credit_sms_id = 2;

  // id returned by the comms service after debit sms is sent
  string debit_sms_id = 3;

  // id returned by the comms service after credit push notification is sent
  string credit_push_notif_id = 4;

  // id returned by the comms service after debit push notification is sent
  string debit_push_notif_id = 5;

  // TransactionNotificationMap creation timestamp
  google.protobuf.Timestamp created_at = 6;

  // TransactionNotificationMap last updated time stamp
  google.protobuf.Timestamp updated_at = 7;

  // TransactionNotificationMap deleted time stamp
  google.protobuf.Timestamp deleted_at = 8;

  // notification id corresponding to the in app push notification sent to the user
  string collect_push_notif_id = 9;

  // id returned by the comms service after payment reversal sms is sent
  string reversal_sms_id = 10;

  // id returned by the comms service after payment reversal push notification is sent
  string reversal_push_notif_id = 11;

  // id returned by the comms service after credit email is sent
  string comms_id_for_credit_email = 12;
}

enum TransactionNotificationMapFieldMask {
    TRANSACTION_NOTIFICATION_MAP_MASK_UNSPECIFIED = 0;

    TRANSACTION_ID = 1;

    CREDIT_SMS_ID = 2;

    DEBIT_SMS_ID = 3;

    CREDIT_PUSH_NOTIF_ID = 4;

    DEBIT_PUSH_NOTIF_ID = 5;

    COLLECT_PUSH_NOTIF_ID = 6;

    REVERSAL_SMS_ID = 7;

    REVERSAL_PUSH_NOTIF_ID = 8;

    COMMS_ID_FOR_CREDIT_EMAIL = 9;
}
