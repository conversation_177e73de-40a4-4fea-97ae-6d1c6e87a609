// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order;

option go_package = "github.com/epifi/gamma/api/order";
option java_package = "com.github.epifi.gamma.api.order";


// Workflow defines how an order is to be treated. Order can have different state machines based on the workflow
// e.g., P2P_FUND_TRANSFER and FIXED_DEPOSIT will have different state machines and workflow helps with segregating both.
// more workflow types to be added as the use cases grow
enum OrderWorkflow {
  ORDER_WORKFLOW_UNSPECIFIED = 0;

  // workflow for fund transfer between two users ( includes both internal and external).
  // e.g. UPI/NEFT/IMPS/RTGS
  // P2P_FUND_TRANSFER follows the following workflow
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED.
  P2P_FUND_TRANSFER = 1;

  // workflow of collect based payment between an internal epiFi user and an external user.
  // To begin with this workflow will only be supported over UPI
  // P2P_COLLECT can follows the following workflow
  //
  // Outgoing - CREATED -> COLLECT_IN_PROGRESS -> COLLECT_REGISTERED/COLLECT_FAILED ->
  // PAYMENT_IN_PROGRESS/COLLECT_DISMISSED_BY_PAYER  -> PAID / FAILED
  //
  // Incoming - COLLECT_REGISTERED ->  PAYMENT_IN_PROGRESS/ COLLECT_DISMISSED_BY_PAYER -> PAID / FAILED
  // Note- IN_PAYMENT step will be skipped in case of payer is not an epiFi user.
  P2P_COLLECT = 2;

  // workflow of collect based payment between two epiFi users.
  //
  // This is a special case when payer and payee both belong to epiFi, collect requests are short-circuited.
  // To the user it seems to be a collect request but actually when payer authenticates a transaction
  // it is treated similar to a normal pay transaction in backend.
  //
  // P2P_COLLECT_SHORT_CIRCUIT can follow the following workflow
  // CREATED -> COLLECT_REGISTERED -> COLLECT_DISMISSED_BY_PAYER/COLLECT_DISMISSED_BY_PAYEE/PAYMENT_IN_PROGRESS -> PAID/FAILED
  P2P_COLLECT_SHORT_CIRCUIT = 3;

  // workflow to be used when making entries of order already in terminal state.
  // For Ex. Inbound transactions
  NO_OP = 4;

  // workflow to be followed by URN based fund transfers between internal/external actor to an internal epiFi user.
  // A URN based transfer can be done via various methods e.g. app intents, dynamic QRs, NFC, etc.
  //
  // Eg. While adding funds to epifi account the user can choose to have an intent based payment from other app
  // URN_TRANSFER follows the following workflow
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED.
  URN_TRANSFER = 5;

  // Workflow for a deposit creation order
  // An order with this workflow goes through FULFILLMENT (in this case deposit creation)
  // CREATE_DEPOSIT follows following state machine
  // CREATED -> IN_FULFILLMENT -> FULFILLED/FULFILLMENT_FAILED
  CREATE_DEPOSIT = 6;

  // Workflow for a preclose deposit order
  // An order with this workflow goes through FULFILLMENT (in this case deposit preclosure)
  // PRECLOSE_DEPOSIT follows following state machine
  // CREATED -> IN_FULFILLMENT -> FULFILLED/FULFILLMENT_FAILED
  PRECLOSE_DEPOSIT = 7;

  // Workflow to be followed for a fund transfer from epiFi's business account to an internal user
  // savings account.
  //
  // the business logic makes sures payment is completed irrespective of transient failures at banks's end.
  // Each time a transient error is returned from vendor bank, the transaction is marked as failed and a new payment
  // request is initiated; hence a new transaction linking to the same order.
  //
  // B2C_FUND_TRANSFER goes through following states
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED
  B2C_FUND_TRANSFER = 8;

  // Workflow for a SD creation process via the rewards flow
  // An order with this workflow will have the following two legs
  // 1. PAYMENT - money transfer from business account to user's savings account, i.e. B2C_FUND_TRANSFER
  // 2. FULFILLMENT - Smart Deposit creation using the money transferred in the first leg, i.e. CREATE_DEPOSIT
  REWARDS_CREATE_SD = 9;

  // Workflow for adding funds to SD via the rewards flow
  // An order with this workflow will have the following two legs
  // 1. PAYMENT - money transfer from business account to user's savings account, i.e. B2C_FUND_TRANSFER
  // 2. FULFILLMENT - Add funds to SD using the money transferred in the first leg, i.e. P2P_FUND_TRANSFER
  // Although second leg also involves payment we'll model this as fulfilment
  // because a single workflow can't have multiple stages of same type as we need DAG map for each workflow
  REWARDS_ADD_FUNDS_SD = 10;

  // Workflow to be followed while adding funds to epifi account via intent.
  // An order with this workflow goes through settlement
  // the order will have two legs
  // 1) Payment - Money gets transferred from user's external account to epifi Pool account
  // 2) Settlement - Money is transferred from epifi pool account to user's epifi savings account
  //
  // ADD_FUNDS goes through the following states
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED -> SETTLED
  ADD_FUNDS = 11;

  // Workflow for adding funds to SD from user's savings account
  //
  // The business logic makes sures payment is completed irrespective of transient failures at banks's end.
  // Each time a transient error is returned from vendor bank, the transaction is marked as failed and a new payment
  // request is initiated; hence a new transaction linking to the same order.
  //
  // ADD_FUNDS_SD goes through following states
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED
  ADD_FUNDS_SD = 12;

  // Workflow to be followed while adding funds to epifi account via collect request.
  // An order with this workflow goes through settlement
  // the order will have two legs
  // 1) Payment - Money gets transferred from user's external account to epifi Pool account
  // 2) Settlement - Money is transferred from epifi pool account to user's epifi savings account
  //
  // ADD_FUNDS goes through the following states
  // CREATED -> COLLECT_IN_PROGRESS -> COLLECT_REGISTERED/COLLECT_FAILED -> PAID/PAYMENT_FAILED -> IN_SETTLEMENT -> SETTLED
  ADD_FUNDS_COLLECT = 13;

  // Workflow for creating a recurring payment
  // An order with this workflow goes through FULFILLMENT (in this case recurring payment creation)
  // CREATE_RECURRING_PAYMENT goes through the following states
  // CREATED -> IN_FULFILLMENT -> FULFILLED/FULFILLMENT_FAILED
  CREATE_RECURRING_PAYMENT = 14;

  // Workflow for execution/fund transfer of a recurring payment
  // EXECUTE_RECURRING_PAYMENT goes through the following states
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED
  EXECUTE_RECURRING_PAYMENT = 15;

  // Workflow for modifying a recurring payment
  // An order with this workflow goes through FULFILLMENT (in this case recurring payment modification)
  // MODIFY_RECURRING_PAYMENT goes through the following states
  // CREATED -> IN_FULFILLMENT -> FULFILLED/FULFILLMENT_FAILED
  MODIFY_RECURRING_PAYMENT = 16;

  // Workflow for revoking a recurring payment
  // An order with this workflow goes through FULFILLMENT (in this case recurring payment modification)
  // REVOKE_RECURRING_PAYMENT goes through the following states
  // CREATED -> IN_FULFILLMENT -> FULFILLED/FULFILLMENT_FAILED
  REVOKE_RECURRING_PAYMENT = 17;

  // Workflow for processing investments into mutual funds
  // An order with this workflow goes through the following states
  // CREATED -> IN_FULFILLMENT ( Order would be sent to Vendors for processing investment) -> FULFILLED/FULFILLMENT_FAILED -> IN_PAYMENT -> PAID/PAYMENT_FAILED
  MUTUAL_FUNDS_INVEST_POST_PAID = 18;

  // Workflow for processing redemption from mutual funds
  // An order with this workflow goes through the following states
  // CREATED -> IN_FULFILLMENT ( Order would be sent to Vendors for processing redemption) -> FULFILLED/FULFILLMENT_FAILED
  MUTUAL_FUNDS_REDEEM = 19;

  // Workflow for executing one time recurring payment of collect type.
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED.
  COLLECT_ONE_TIME_RECURRING_PAYMENT = 20 [deprecated = true];

  // Workflow for executing recurring payments of type collect where
  // user authorisation is required
  // // An order with this workflow goes through the following states
  // CREATED -> COLLECT_REGISTERED -> IN_PAYMENT -> PAID/PAYMENT_FAILED
  COLLECT_RECURRING_PAYMENT_WITH_AUTH = 21;

  // Workflow for executing recurring payments of type collect where
  // user authorisation is not required
  // An order with this workflow goes through the following states
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED
  COLLECT_RECURRING_PAYMENT_NO_AUTH = 22;

  // Workflow for pause/unpause of a recurring payment
  // An order with this workflow goes through FULFILLMENT
  // PAUSE_OR_UNPAUSE_RECURRING_PAYMENT goes through the following states
  // CREATED -> IN_FULFILLMENT -> FULFILLED/FULFILLMENT_FAILED
  PAUSE_OR_UNPAUSE_RECURRING_PAYMENT = 23;

  // Workflow for executing a P2P investment
  // The workflow has two legs, PAYMENT, and SETTLEMENT
  // PAYMENT stage deals with making the investment payment via a partner bank
  // SETTLEMENT stage deals with settling the payment with the investment vendor.
  // An order with this workflow goes through the following states
  // CREATED -> IN_PAYMENT -> PAID/PAYMENT_FAILED -> IN_SETTLEMENT -> SETTLED/SETTLEMENT_FAILED
  P2P_INVESTMENT = 24;

  // Workflow for executing P2P withdrawal
  // The workflow has two legs, FULFILLMENT and PAYMENT
  // The FULFILLMENT leg takes care of raising the request to the p2p vendor to withdraw money
  // The PAYMENT legs is responsible for creating the transaction at our end after confirmation from partner bank
  // An order with this workflow goes through the following states
  // CREATED -> IN_FULFILLMENT -> FULFILLED/FULFILLMENT_FAILED -> IN_PAYMENT -> PAID/PAYMENT_FAILED
  P2P_WITHDRAWAL = 25;

  // Workflow for executing International fund transfer from user's savings account
  // This workflow has 5 stages, FUNDS_POOL_TRANSFER, A2_FORM_GENERATION, LRS_CHECK, SOF_CHECK, SWIFT_TRANSFER
  // THE FUNDS_POOL_TRANSFER takes care of transferring money from user's savings account to a pool account.
  // THE LRS_CHECK eligibility on how much money one can transfer, imposed by RBI
  // THE SOF_CHECK how the user has acquired the money, imposed by RBI
  // THE A2_FORM_GENERATION generates doc needed by vendor for SOF_CHECK
  // THE SWIFT_TRANSFER where payee actually gets the money through vendor
  INTERNATIONAL_FUND_TRANSFER = 26;

  // Workflow for orchestrating enach execution via fund transfer to epifi's pool account.
  // The order created for this workflow has 2 (or 3 in reversal cases) legs,
  //  1. In first leg, the funds are transferred from source account of enach mandate to epifi's Pool Account, a txn entry is created for this transfer.
  //  2. In second leg (ONLY if first leg is successful), the funds are transferred from epifi's Pool Account to destination account of enach mandate, a new txn entry is created for this transfer.
  // If the first leg fails, then order is moved to a PAYMENT_FAILED state and workflow is halted.
  // If second leg fails, then an additional third leg (and a corresponding new txn) is initiated for reversal of funds from epifi's Pool Account to enach mandate source account and order is marked in PAYMENT_REVERSED state ince this leg is successful.
  // If the second leg is successful, then order is marked to a PAID state.
  ENACH_EXECUTION_VIA_POOL_ACCOUNT_TRANSFER = 27;

  // Workflow for identifying and marking off-app upi payments coming in via inbound notifications.
  // This will handle scenarios where the status received from the inbound notification might not exactly be the final one,
  // i.e. we wouldn't want to put that into NO_OP flow, for e.g. DEEMED scenarios.
  // Note: Orchestration is handled by "Off App UPI V2 Flow" temporal workflow.
  //
  // Orchestration details:
  // 1. Inbound notification is passed to a temporal workflow.
  // 2. Irrespective of what we get in inbound notification details, we enquire for status of the transaction with the vendor.
  // 3. Upon doing the enquiry for a short duration, we proceed for the order and txn creation (irrespective of the status we get from vendor).
  // 4. If the status is non-terminal, we proceed for long term enquiry for the transaction with the vendor.
  // 5. Eventually (once we get terminal status from vendor or retries exhaust), we update order and txn entities.
  // Please refer gamma `pay/workflow/off_app_upi_v2_flow.go` for more details on orchestration.
  OFF_APP_UPI = 28;

  // Signifies order was created from secure cc flow
  // To obtain fi secure credit card powered by federal
  // there is a prerequisite to create FD with federal
  // Workflow for orchestrating secure cc federal FD
  // fund transfer.
  SECURED_CC_FEDERAL_FD_FUND_TRANSFER = 29;
}
