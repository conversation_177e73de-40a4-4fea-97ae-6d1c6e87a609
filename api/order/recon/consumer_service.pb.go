// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/recon/consumer_service.proto

package recon

import (
	queue "github.com/epifi/be-common/api/queue"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReconcileAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// customer id of the actor to which the account belongs to
	CustomerId string `protobuf:"bytes,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// account id of the savings account to be reconciled
	SavingsAccountId string `protobuf:"bytes,3,opt,name=savings_account_id,json=savingsAccountId,proto3" json:"savings_account_id,omitempty"`
	// account number as provided by partner bank
	AccountNo string `protobuf:"bytes,4,opt,name=account_no,json=accountNo,proto3" json:"account_no,omitempty"`
	// account opening date
	AccOpeningDate *date.Date `protobuf:"bytes,5,opt,name=acc_opening_date,json=accOpeningDate,proto3" json:"acc_opening_date,omitempty"`
	// actor id of the owner of the account
	ActorId string `protobuf:"bytes,6,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *ReconcileAccountRequest) Reset() {
	*x = ReconcileAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_recon_consumer_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReconcileAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReconcileAccountRequest) ProtoMessage() {}

func (x *ReconcileAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_recon_consumer_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReconcileAccountRequest.ProtoReflect.Descriptor instead.
func (*ReconcileAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_order_recon_consumer_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReconcileAccountRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ReconcileAccountRequest) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *ReconcileAccountRequest) GetSavingsAccountId() string {
	if x != nil {
		return x.SavingsAccountId
	}
	return ""
}

func (x *ReconcileAccountRequest) GetAccountNo() string {
	if x != nil {
		return x.AccountNo
	}
	return ""
}

func (x *ReconcileAccountRequest) GetAccOpeningDate() *date.Date {
	if x != nil {
		return x.AccOpeningDate
	}
	return nil
}

func (x *ReconcileAccountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type ReconcileAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ReconcileAccountResponse) Reset() {
	*x = ReconcileAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_recon_consumer_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReconcileAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReconcileAccountResponse) ProtoMessage() {}

func (x *ReconcileAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_recon_consumer_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReconcileAccountResponse.ProtoReflect.Descriptor instead.
func (*ReconcileAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_order_recon_consumer_service_proto_rawDescGZIP(), []int{1}
}

func (x *ReconcileAccountResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_order_recon_consumer_service_proto protoreflect.FileDescriptor

var file_api_order_recon_consumer_service_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x63, 0x6f,
	0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x72, 0x65, 0x63, 0x6f, 0x6e, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xa4, 0x02, 0x0a, 0x17, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x6f, 0x12, 0x3b,
	0x0a, 0x10, 0x61, 0x63, 0x63, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0e, 0x61, 0x63, 0x63,
	0x4f, 0x70, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63,
	0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75,
	0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x32, 0x6b, 0x0a, 0x08, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x10, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63,
	0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69,
	0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x25, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x63, 0x6f, 0x6e, 0x63, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6e, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_order_recon_consumer_service_proto_rawDescOnce sync.Once
	file_api_order_recon_consumer_service_proto_rawDescData = file_api_order_recon_consumer_service_proto_rawDesc
)

func file_api_order_recon_consumer_service_proto_rawDescGZIP() []byte {
	file_api_order_recon_consumer_service_proto_rawDescOnce.Do(func() {
		file_api_order_recon_consumer_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_recon_consumer_service_proto_rawDescData)
	})
	return file_api_order_recon_consumer_service_proto_rawDescData
}

var file_api_order_recon_consumer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_order_recon_consumer_service_proto_goTypes = []interface{}{
	(*ReconcileAccountRequest)(nil),      // 0: order.recon.ReconcileAccountRequest
	(*ReconcileAccountResponse)(nil),     // 1: order.recon.ReconcileAccountResponse
	(*queue.ConsumerRequestHeader)(nil),  // 2: queue.ConsumerRequestHeader
	(*date.Date)(nil),                    // 3: google.type.Date
	(*queue.ConsumerResponseHeader)(nil), // 4: queue.ConsumerResponseHeader
}
var file_api_order_recon_consumer_service_proto_depIdxs = []int32{
	2, // 0: order.recon.ReconcileAccountRequest.request_header:type_name -> queue.ConsumerRequestHeader
	3, // 1: order.recon.ReconcileAccountRequest.acc_opening_date:type_name -> google.type.Date
	4, // 2: order.recon.ReconcileAccountResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 3: order.recon.Consumer.ReconcileAccount:input_type -> order.recon.ReconcileAccountRequest
	1, // 4: order.recon.Consumer.ReconcileAccount:output_type -> order.recon.ReconcileAccountResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_order_recon_consumer_service_proto_init() }
func file_api_order_recon_consumer_service_proto_init() {
	if File_api_order_recon_consumer_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_order_recon_consumer_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReconcileAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_recon_consumer_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReconcileAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_recon_consumer_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_order_recon_consumer_service_proto_goTypes,
		DependencyIndexes: file_api_order_recon_consumer_service_proto_depIdxs,
		MessageInfos:      file_api_order_recon_consumer_service_proto_msgTypes,
	}.Build()
	File_api_order_recon_consumer_service_proto = out.File
	file_api_order_recon_consumer_service_proto_rawDesc = nil
	file_api_order_recon_consumer_service_proto_goTypes = nil
	file_api_order_recon_consumer_service_proto_depIdxs = nil
}
