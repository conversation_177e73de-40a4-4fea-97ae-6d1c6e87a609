// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/aa/event.proto

package aa

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AATxnUpdate is the message to be published to a topic after aa txn is consumed in pay service
type AATxnUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	AaTxn         *Transaction                 `protobuf:"bytes,2,opt,name=aa_txn,json=aaTxn,proto3" json:"aa_txn,omitempty"`
}

func (x *AATxnUpdate) Reset() {
	*x = AATxnUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_aa_event_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AATxnUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AATxnUpdate) ProtoMessage() {}

func (x *AATxnUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_aa_event_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AATxnUpdate.ProtoReflect.Descriptor instead.
func (*AATxnUpdate) Descriptor() ([]byte, []int) {
	return file_api_order_aa_event_proto_rawDescGZIP(), []int{0}
}

func (x *AATxnUpdate) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *AATxnUpdate) GetAaTxn() *Transaction {
	if x != nil {
		return x.AaTxn
	}
	return nil
}

var File_api_order_aa_event_proto protoreflect.FileDescriptor

var file_api_order_aa_event_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x61, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x61, 0x61, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x61, 0x61, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x01, 0x0a, 0x0b, 0x41, 0x41, 0x54, 0x78, 0x6e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x06, 0x61,
	0x61, 0x5f, 0x74, 0x78, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x61, 0x61, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x05, 0x61, 0x61, 0x54, 0x78, 0x6e, 0x42, 0x4a, 0x0a, 0x23, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x61, 0x61,
	0x5a, 0x23, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2f, 0x61, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_order_aa_event_proto_rawDescOnce sync.Once
	file_api_order_aa_event_proto_rawDescData = file_api_order_aa_event_proto_rawDesc
)

func file_api_order_aa_event_proto_rawDescGZIP() []byte {
	file_api_order_aa_event_proto_rawDescOnce.Do(func() {
		file_api_order_aa_event_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_aa_event_proto_rawDescData)
	})
	return file_api_order_aa_event_proto_rawDescData
}

var file_api_order_aa_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_order_aa_event_proto_goTypes = []interface{}{
	(*AATxnUpdate)(nil),                 // 0: order.aa.AATxnUpdate
	(*queue.ConsumerRequestHeader)(nil), // 1: queue.ConsumerRequestHeader
	(*Transaction)(nil),                 // 2: order.aa.Transaction
}
var file_api_order_aa_event_proto_depIdxs = []int32{
	1, // 0: order.aa.AATxnUpdate.request_header:type_name -> queue.ConsumerRequestHeader
	2, // 1: order.aa.AATxnUpdate.aa_txn:type_name -> order.aa.Transaction
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_order_aa_event_proto_init() }
func file_api_order_aa_event_proto_init() {
	if File_api_order_aa_event_proto != nil {
		return
	}
	file_api_order_aa_transaction_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_order_aa_event_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AATxnUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_aa_event_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_order_aa_event_proto_goTypes,
		DependencyIndexes: file_api_order_aa_event_proto_depIdxs,
		MessageInfos:      file_api_order_aa_event_proto_msgTypes,
	}.Build()
	File_api_order_aa_event_proto = out.File
	file_api_order_aa_event_proto_rawDesc = nil
	file_api_order_aa_event_proto_goTypes = nil
	file_api_order_aa_event_proto_depIdxs = nil
}
