//go:generate gen_sql -types=LoanApplicationDetails

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/sgapplication/internal/loan_application.proto

package sgapplication

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	creditrisk "github.com/epifi/gringott/api/stockguardian/creditrisk"
	enums "github.com/epifi/gringott/api/stockguardian/sgapplication/enums"
	bre "github.com/epifi/gringott/api/stockguardian/vendors/inhouse/bre"
	date "google.golang.org/genproto/googleapis/type/date"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoanApplication struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// A unique identifier used for external references, such as vendor communications.
	ApplicantId string `protobuf:"bytes,3,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	// The unique identifier for the Loan Service Provider (LSP)
	ClientId string `protobuf:"bytes,4,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	// Identifies various loan programs offered by NBFC, which may differ based on the stages and validations required in the loan application process.
	ProductId string                         `protobuf:"bytes,5,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	Details   *LoanApplicationDetails        `protobuf:"bytes,6,opt,name=details,proto3" json:"details,omitempty"`
	Status    enums.LoanApplicationStatus    `protobuf:"varint,7,opt,name=status,proto3,enum=stockguardian.sgapplication.enums.LoanApplicationStatus" json:"status,omitempty"`
	SubStatus enums.LoanApplicationSubStatus `protobuf:"varint,8,opt,name=sub_status,json=subStatus,proto3,enum=stockguardian.sgapplication.enums.LoanApplicationSubStatus" json:"sub_status,omitempty"`
	CreatedAt *timestamppb.Timestamp         `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp         `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp         `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *LoanApplication) Reset() {
	*x = LoanApplication{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanApplication) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanApplication) ProtoMessage() {}

func (x *LoanApplication) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanApplication.ProtoReflect.Descriptor instead.
func (*LoanApplication) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{0}
}

func (x *LoanApplication) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoanApplication) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *LoanApplication) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *LoanApplication) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *LoanApplication) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *LoanApplication) GetDetails() *LoanApplicationDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *LoanApplication) GetStatus() enums.LoanApplicationStatus {
	if x != nil {
		return x.Status
	}
	return enums.LoanApplicationStatus(0)
}

func (x *LoanApplication) GetSubStatus() enums.LoanApplicationSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return enums.LoanApplicationSubStatus(0)
}

func (x *LoanApplication) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LoanApplication) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LoanApplication) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type LoanApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserSelectedDetails     *UserSelectedDetails     `protobuf:"bytes,1,opt,name=user_selected_details,json=userSelectedDetails,proto3" json:"user_selected_details,omitempty"`
	LoanOfferDetails        *LoanOfferDetails        `protobuf:"bytes,2,opt,name=loan_offer_details,json=loanOfferDetails,proto3" json:"loan_offer_details,omitempty"`
	AddressDetails          *AddressDetails          `protobuf:"bytes,4,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	EmploymentDetails       *EmploymentDetails       `protobuf:"bytes,5,opt,name=employment_details,json=employmentDetails,proto3" json:"employment_details,omitempty"`
	BankAccountDetails      *BankAccountDetails      `protobuf:"bytes,6,opt,name=bank_account_details,json=bankAccountDetails,proto3" json:"bank_account_details,omitempty"`
	ConsentDetails          *ConsentDetails          `protobuf:"bytes,7,opt,name=consent_details,json=consentDetails,proto3" json:"consent_details,omitempty"`
	PersonalDetails         *PersonalDetails         `protobuf:"bytes,8,opt,name=personal_details,json=personalDetails,proto3" json:"personal_details,omitempty"`
	RecurringPaymentDetails *RecurringPaymentDetails `protobuf:"bytes,9,opt,name=recurring_payment_details,json=recurringPaymentDetails,proto3" json:"recurring_payment_details,omitempty"`
	EsignDocumentDetails    *EsignDocumentDetails    `protobuf:"bytes,10,opt,name=esign_document_details,json=esignDocumentDetails,proto3" json:"esign_document_details,omitempty"`
	LocationDetails         *LocationDetails         `protobuf:"bytes,11,opt,name=location_details,json=locationDetails,proto3" json:"location_details,omitempty"`
}

func (x *LoanApplicationDetails) Reset() {
	*x = LoanApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanApplicationDetails) ProtoMessage() {}

func (x *LoanApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanApplicationDetails.ProtoReflect.Descriptor instead.
func (*LoanApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{1}
}

func (x *LoanApplicationDetails) GetUserSelectedDetails() *UserSelectedDetails {
	if x != nil {
		return x.UserSelectedDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetLoanOfferDetails() *LoanOfferDetails {
	if x != nil {
		return x.LoanOfferDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetAddressDetails() *AddressDetails {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetEmploymentDetails() *EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetBankAccountDetails() *BankAccountDetails {
	if x != nil {
		return x.BankAccountDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetConsentDetails() *ConsentDetails {
	if x != nil {
		return x.ConsentDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetPersonalDetails() *PersonalDetails {
	if x != nil {
		return x.PersonalDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetRecurringPaymentDetails() *RecurringPaymentDetails {
	if x != nil {
		return x.RecurringPaymentDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetEsignDocumentDetails() *EsignDocumentDetails {
	if x != nil {
		return x.EsignDocumentDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetLocationDetails() *LocationDetails {
	if x != nil {
		return x.LocationDetails
	}
	return nil
}

type BankAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankAccountDetails *common.BankAccountDetails `protobuf:"bytes,1,opt,name=bank_account_details,json=bankAccountDetails,proto3" json:"bank_account_details,omitempty"`
	RegisteredPiId     string                     `protobuf:"bytes,2,opt,name=registered_pi_id,json=registeredPiId,proto3" json:"registered_pi_id,omitempty"`
}

func (x *BankAccountDetails) Reset() {
	*x = BankAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccountDetails) ProtoMessage() {}

func (x *BankAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccountDetails.ProtoReflect.Descriptor instead.
func (*BankAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{2}
}

func (x *BankAccountDetails) GetBankAccountDetails() *common.BankAccountDetails {
	if x != nil {
		return x.BankAccountDetails
	}
	return nil
}

func (x *BankAccountDetails) GetRegisteredPiId() string {
	if x != nil {
		return x.RegisteredPiId
	}
	return ""
}

type AddressDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddressDetails *common.PostalAddress `protobuf:"bytes,1,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	AddressType    common.AddressType    `protobuf:"varint,2,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.common.AddressType" json:"address_type,omitempty"`
}

func (x *AddressDetails) Reset() {
	*x = AddressDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressDetails) ProtoMessage() {}

func (x *AddressDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressDetails.ProtoReflect.Descriptor instead.
func (*AddressDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{3}
}

func (x *AddressDetails) GetAddressDetails() *common.PostalAddress {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *AddressDetails) GetAddressType() common.AddressType {
	if x != nil {
		return x.AddressType
	}
	return common.AddressType(0)
}

type EmploymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Occupation       common.EmploymentType `protobuf:"varint,1,opt,name=occupation,proto3,enum=api.typesv2.common.EmploymentType" json:"occupation,omitempty"`
	OrganizationName string                `protobuf:"bytes,2,opt,name=organization_name,json=organizationName,proto3" json:"organization_name,omitempty"`
	MonthlyIncome    *money.Money          `protobuf:"bytes,3,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	WorkEmail        string                `protobuf:"bytes,4,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	OfficeAddress    *common.PostalAddress `protobuf:"bytes,5,opt,name=office_address,json=officeAddress,proto3" json:"office_address,omitempty"`
}

func (x *EmploymentDetails) Reset() {
	*x = EmploymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentDetails) ProtoMessage() {}

func (x *EmploymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentDetails.ProtoReflect.Descriptor instead.
func (*EmploymentDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{4}
}

func (x *EmploymentDetails) GetOccupation() common.EmploymentType {
	if x != nil {
		return x.Occupation
	}
	return common.EmploymentType(0)
}

func (x *EmploymentDetails) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *EmploymentDetails) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *EmploymentDetails) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *EmploymentDetails) GetOfficeAddress() *common.PostalAddress {
	if x != nil {
		return x.OfficeAddress
	}
	return nil
}

type ConsentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecordedConsents []*ConsentDetails_Consent `protobuf:"bytes,1,rep,name=recorded_consents,json=recordedConsents,proto3" json:"recorded_consents,omitempty"`
}

func (x *ConsentDetails) Reset() {
	*x = ConsentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsentDetails) ProtoMessage() {}

func (x *ConsentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsentDetails.ProtoReflect.Descriptor instead.
func (*ConsentDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{5}
}

func (x *ConsentDetails) GetRecordedConsents() []*ConsentDetails_Consent {
	if x != nil {
		return x.RecordedConsents
	}
	return nil
}

// All the fields should be passed when updating the user selected details in the loan application.
type UserSelectedDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanAmount     *money.Money `protobuf:"bytes,1,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
	TenureInMonths int32        `protobuf:"varint,2,opt,name=tenure_in_months,json=tenureInMonths,proto3" json:"tenure_in_months,omitempty"`
	InterestRate   float64      `protobuf:"fixed64,3,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
}

func (x *UserSelectedDetails) Reset() {
	*x = UserSelectedDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSelectedDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSelectedDetails) ProtoMessage() {}

func (x *UserSelectedDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSelectedDetails.ProtoReflect.Descriptor instead.
func (*UserSelectedDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{6}
}

func (x *UserSelectedDetails) GetLoanAmount() *money.Money {
	if x != nil {
		return x.LoanAmount
	}
	return nil
}

func (x *UserSelectedDetails) GetTenureInMonths() int32 {
	if x != nil {
		return x.TenureInMonths
	}
	return 0
}

func (x *UserSelectedDetails) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

type LoanOfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanOffer *creditrisk.LoanOfferDetails `protobuf:"bytes,1,opt,name=loan_offer,json=loanOffer,proto3" json:"loan_offer,omitempty"`
	// This denotes the request id used for locking the offer.
	LockRequestId string `protobuf:"bytes,3,opt,name=lock_request_id,json=lockRequestId,proto3" json:"lock_request_id,omitempty"`
	// open market user is someone who was not initially evaluated offline by LSP and all evaluation is happening real time
	IsOpenMarketUser bool `protobuf:"varint,4,opt,name=is_open_market_user,json=isOpenMarketUser,proto3" json:"is_open_market_user,omitempty"`
	// this is metadata sent by LSP to be sent to BRE for evaluation of offer generation
	BreParams *LoanOfferDetails_BreParams `protobuf:"bytes,5,opt,name=bre_params,json=breParams,proto3" json:"bre_params,omitempty"`
}

func (x *LoanOfferDetails) Reset() {
	*x = LoanOfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferDetails) ProtoMessage() {}

func (x *LoanOfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferDetails.ProtoReflect.Descriptor instead.
func (*LoanOfferDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{7}
}

func (x *LoanOfferDetails) GetLoanOffer() *creditrisk.LoanOfferDetails {
	if x != nil {
		return x.LoanOffer
	}
	return nil
}

func (x *LoanOfferDetails) GetLockRequestId() string {
	if x != nil {
		return x.LockRequestId
	}
	return ""
}

func (x *LoanOfferDetails) GetIsOpenMarketUser() bool {
	if x != nil {
		return x.IsOpenMarketUser
	}
	return false
}

func (x *LoanOfferDetails) GetBreParams() *LoanOfferDetails_BreParams {
	if x != nil {
		return x.BreParams
	}
	return nil
}

type PersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dob           *date.Date           `protobuf:"bytes,1,opt,name=dob,proto3" json:"dob,omitempty"`
	Email         string               `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	PanName       *common.Name         `protobuf:"bytes,3,opt,name=pan_name,json=panName,proto3" json:"pan_name,omitempty"`
	Gender        common.Gender        `protobuf:"varint,4,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
	FatherName    *common.Name         `protobuf:"bytes,5,opt,name=father_name,json=fatherName,proto3" json:"father_name,omitempty"`
	MotherName    *common.Name         `protobuf:"bytes,6,opt,name=mother_name,json=motherName,proto3" json:"mother_name,omitempty"`
	MaritalStatus common.MaritalStatus `protobuf:"varint,7,opt,name=marital_status,json=maritalStatus,proto3,enum=api.typesv2.common.MaritalStatus" json:"marital_status,omitempty"`
	Qualification string               `protobuf:"bytes,8,opt,name=qualification,proto3" json:"qualification,omitempty"`
}

func (x *PersonalDetails) Reset() {
	*x = PersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonalDetails) ProtoMessage() {}

func (x *PersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonalDetails.ProtoReflect.Descriptor instead.
func (*PersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{8}
}

func (x *PersonalDetails) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *PersonalDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PersonalDetails) GetPanName() *common.Name {
	if x != nil {
		return x.PanName
	}
	return nil
}

func (x *PersonalDetails) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *PersonalDetails) GetFatherName() *common.Name {
	if x != nil {
		return x.FatherName
	}
	return nil
}

func (x *PersonalDetails) GetMotherName() *common.Name {
	if x != nil {
		return x.MotherName
	}
	return nil
}

func (x *PersonalDetails) GetMaritalStatus() common.MaritalStatus {
	if x != nil {
		return x.MaritalStatus
	}
	return common.MaritalStatus(0)
}

func (x *PersonalDetails) GetQualification() string {
	if x != nil {
		return x.Qualification
	}
	return ""
}

type LocationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// latitude and longitude of the location
	LatitudeLongitude *latlng.LatLng `protobuf:"bytes,1,opt,name=latitude_longitude,json=latitudeLongitude,proto3" json:"latitude_longitude,omitempty"`
}

func (x *LocationDetails) Reset() {
	*x = LocationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationDetails) ProtoMessage() {}

func (x *LocationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationDetails.ProtoReflect.Descriptor instead.
func (*LocationDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{9}
}

func (x *LocationDetails) GetLatitudeLongitude() *latlng.LatLng {
	if x != nil {
		return x.LatitudeLongitude
	}
	return nil
}

type KycDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersonalData      *KYCPersonalDetails        `protobuf:"bytes,1,opt,name=personal_data,json=personalData,proto3" json:"personal_data,omitempty"`
	AadhaarXml        string                     `protobuf:"bytes,2,opt,name=aadhaar_xml,json=aadhaarXml,proto3" json:"aadhaar_xml,omitempty"`
	UserSelfieUrl     string                     `protobuf:"bytes,3,opt,name=user_selfie_url,json=userSelfieUrl,proto3" json:"user_selfie_url,omitempty"`
	AadhaarPhotoUrl   string                     `protobuf:"bytes,4,opt,name=aadhaar_photo_url,json=aadhaarPhotoUrl,proto3" json:"aadhaar_photo_url,omitempty"`
	OrchestrationFlow enums.KycOrchestrationFlow `protobuf:"varint,5,opt,name=orchestration_flow,json=orchestrationFlow,proto3,enum=stockguardian.sgapplication.enums.KycOrchestrationFlow" json:"orchestration_flow,omitempty"`
}

func (x *KycDetails) Reset() {
	*x = KycDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycDetails) ProtoMessage() {}

func (x *KycDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycDetails.ProtoReflect.Descriptor instead.
func (*KycDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{10}
}

func (x *KycDetails) GetPersonalData() *KYCPersonalDetails {
	if x != nil {
		return x.PersonalData
	}
	return nil
}

func (x *KycDetails) GetAadhaarXml() string {
	if x != nil {
		return x.AadhaarXml
	}
	return ""
}

func (x *KycDetails) GetUserSelfieUrl() string {
	if x != nil {
		return x.UserSelfieUrl
	}
	return ""
}

func (x *KycDetails) GetAadhaarPhotoUrl() string {
	if x != nil {
		return x.AadhaarPhotoUrl
	}
	return ""
}

func (x *KycDetails) GetOrchestrationFlow() enums.KycOrchestrationFlow {
	if x != nil {
		return x.OrchestrationFlow
	}
	return enums.KycOrchestrationFlow(0)
}

type KYCPersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaskedAadhaar string                `protobuf:"bytes,1,opt,name=masked_aadhaar,json=maskedAadhaar,proto3" json:"masked_aadhaar,omitempty"`
	Name          *common.Name          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Gender        common.Gender         `protobuf:"varint,3,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
	Dob           *date.Date            `protobuf:"bytes,4,opt,name=dob,proto3" json:"dob,omitempty"`
	Address       *common.PostalAddress `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *KYCPersonalDetails) Reset() {
	*x = KYCPersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KYCPersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KYCPersonalDetails) ProtoMessage() {}

func (x *KYCPersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KYCPersonalDetails.ProtoReflect.Descriptor instead.
func (*KYCPersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{11}
}

func (x *KYCPersonalDetails) GetMaskedAadhaar() string {
	if x != nil {
		return x.MaskedAadhaar
	}
	return ""
}

func (x *KYCPersonalDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *KYCPersonalDetails) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *KYCPersonalDetails) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *KYCPersonalDetails) GetAddress() *common.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

type RecurringPaymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecurringPaymentId string `protobuf:"bytes,1,opt,name=recurring_payment_id,json=recurringPaymentId,proto3" json:"recurring_payment_id,omitempty"`
}

func (x *RecurringPaymentDetails) Reset() {
	*x = RecurringPaymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecurringPaymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecurringPaymentDetails) ProtoMessage() {}

func (x *RecurringPaymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecurringPaymentDetails.ProtoReflect.Descriptor instead.
func (*RecurringPaymentDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{12}
}

func (x *RecurringPaymentDetails) GetRecurringPaymentId() string {
	if x != nil {
		return x.RecurringPaymentId
	}
	return ""
}

type EsignDocumentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *EsignDocumentDetails) Reset() {
	*x = EsignDocumentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EsignDocumentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EsignDocumentDetails) ProtoMessage() {}

func (x *EsignDocumentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EsignDocumentDetails.ProtoReflect.Descriptor instead.
func (*EsignDocumentDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{13}
}

func (x *EsignDocumentDetails) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type DownPaymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier for the transaction
	TransactionId string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// Unique Transaction Reference number for the transaction
	Utr string `protobuf:"bytes,2,opt,name=utr,proto3" json:"utr,omitempty"`
	// The amount of down payment made
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *DownPaymentDetails) Reset() {
	*x = DownPaymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownPaymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownPaymentDetails) ProtoMessage() {}

func (x *DownPaymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownPaymentDetails.ProtoReflect.Descriptor instead.
func (*DownPaymentDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{14}
}

func (x *DownPaymentDetails) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *DownPaymentDetails) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (x *DownPaymentDetails) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

// ServiceProviderDetails contains details of the service provider to which we are disbursing the loan.
// Why? Sometimes we will transfer the loan amount to some entity other than the applicant.
type ServiceProviderDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to PaymentDetails:
	//	*ServiceProviderDetails_InsurerDetails
	PaymentDetails isServiceProviderDetails_PaymentDetails `protobuf_oneof:"payment_details"`
}

func (x *ServiceProviderDetails) Reset() {
	*x = ServiceProviderDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceProviderDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceProviderDetails) ProtoMessage() {}

func (x *ServiceProviderDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceProviderDetails.ProtoReflect.Descriptor instead.
func (*ServiceProviderDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{15}
}

func (m *ServiceProviderDetails) GetPaymentDetails() isServiceProviderDetails_PaymentDetails {
	if m != nil {
		return m.PaymentDetails
	}
	return nil
}

func (x *ServiceProviderDetails) GetInsurerDetails() *InsurerDetails {
	if x, ok := x.GetPaymentDetails().(*ServiceProviderDetails_InsurerDetails); ok {
		return x.InsurerDetails
	}
	return nil
}

type isServiceProviderDetails_PaymentDetails interface {
	isServiceProviderDetails_PaymentDetails()
}

type ServiceProviderDetails_InsurerDetails struct {
	// This will contain the insurer details, eg - when disbursing the loan for health insurance
	InsurerDetails *InsurerDetails `protobuf:"bytes,1,opt,name=insurer_details,json=insurerDetails,proto3,oneof"`
}

func (*ServiceProviderDetails_InsurerDetails) isServiceProviderDetails_PaymentDetails() {}

type InsurerDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id of the insurer
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Bank account number of the insurer
	BankAccountNumber string `protobuf:"bytes,2,opt,name=bank_account_number,json=bankAccountNumber,proto3" json:"bank_account_number,omitempty"`
	// Ifsc code of the insurer
	BankAccountIfsc string `protobuf:"bytes,3,opt,name=bank_account_ifsc,json=bankAccountIfsc,proto3" json:"bank_account_ifsc,omitempty"`
	// Bank branch of the insurer
	BankAccountBranch string `protobuf:"bytes,4,opt,name=bank_account_branch,json=bankAccountBranch,proto3" json:"bank_account_branch,omitempty"`
}

func (x *InsurerDetails) Reset() {
	*x = InsurerDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InsurerDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsurerDetails) ProtoMessage() {}

func (x *InsurerDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsurerDetails.ProtoReflect.Descriptor instead.
func (*InsurerDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{16}
}

func (x *InsurerDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InsurerDetails) GetBankAccountNumber() string {
	if x != nil {
		return x.BankAccountNumber
	}
	return ""
}

func (x *InsurerDetails) GetBankAccountIfsc() string {
	if x != nil {
		return x.BankAccountIfsc
	}
	return ""
}

func (x *InsurerDetails) GetBankAccountBranch() string {
	if x != nil {
		return x.BankAccountBranch
	}
	return ""
}

type ConsentDetails_Consent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConsentType enums.ConsentType `protobuf:"varint,1,opt,name=consent_type,json=consentType,proto3,enum=stockguardian.sgapplication.enums.ConsentType" json:"consent_type,omitempty"`
	// refers the consent id from the tsp consent service.
	ReferenceId string `protobuf:"bytes,2,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// The type of consent that is being recorded.
	// The timestamp when the consent was recorded.
	RecordedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=recorded_at,json=recordedAt,proto3" json:"recorded_at,omitempty"`
	// url of the externally accepted consent document
	ConsentDocUrl string `protobuf:"bytes,4,opt,name=consent_doc_url,json=consentDocUrl,proto3" json:"consent_doc_url,omitempty"`
}

func (x *ConsentDetails_Consent) Reset() {
	*x = ConsentDetails_Consent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsentDetails_Consent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsentDetails_Consent) ProtoMessage() {}

func (x *ConsentDetails_Consent) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsentDetails_Consent.ProtoReflect.Descriptor instead.
func (*ConsentDetails_Consent) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ConsentDetails_Consent) GetConsentType() enums.ConsentType {
	if x != nil {
		return x.ConsentType
	}
	return enums.ConsentType(0)
}

func (x *ConsentDetails_Consent) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *ConsentDetails_Consent) GetRecordedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RecordedAt
	}
	return nil
}

func (x *ConsentDetails_Consent) GetConsentDocUrl() string {
	if x != nil {
		return x.ConsentDocUrl
	}
	return ""
}

type LoanOfferDetails_BreParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// policy params used to generate offer from BRE
	PolicyParams         *bre.PolicyParams `protobuf:"bytes,1,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
	DesiredLoanAmount    *money.Money      `protobuf:"bytes,2,opt,name=desired_loan_amount,json=desiredLoanAmount,proto3" json:"desired_loan_amount,omitempty"`
	Tenure               *common.Duration  `protobuf:"bytes,3,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Roi                  string            `protobuf:"bytes,4,opt,name=roi,proto3" json:"roi,omitempty"`
	PfPlusGst            *money.Money      `protobuf:"bytes,5,opt,name=pf_plus_gst,json=pfPlusGst,proto3" json:"pf_plus_gst,omitempty"`
	UpfrontPaymentAmount *money.Money      `protobuf:"bytes,6,opt,name=upfront_payment_amount,json=upfrontPaymentAmount,proto3" json:"upfront_payment_amount,omitempty"`
}

func (x *LoanOfferDetails_BreParams) Reset() {
	*x = LoanOfferDetails_BreParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferDetails_BreParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferDetails_BreParams) ProtoMessage() {}

func (x *LoanOfferDetails_BreParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferDetails_BreParams.ProtoReflect.Descriptor instead.
func (*LoanOfferDetails_BreParams) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP(), []int{7, 0}
}

func (x *LoanOfferDetails_BreParams) GetPolicyParams() *bre.PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

func (x *LoanOfferDetails_BreParams) GetDesiredLoanAmount() *money.Money {
	if x != nil {
		return x.DesiredLoanAmount
	}
	return nil
}

func (x *LoanOfferDetails_BreParams) GetTenure() *common.Duration {
	if x != nil {
		return x.Tenure
	}
	return nil
}

func (x *LoanOfferDetails_BreParams) GetRoi() string {
	if x != nil {
		return x.Roi
	}
	return ""
}

func (x *LoanOfferDetails_BreParams) GetPfPlusGst() *money.Money {
	if x != nil {
		return x.PfPlusGst
	}
	return nil
}

func (x *LoanOfferDetails_BreParams) GetUpfrontPaymentAmount() *money.Money {
	if x != nil {
		return x.UpfrontPaymentAmount
	}
	return nil
}

var File_api_stockguardian_sgapplication_internal_loan_application_proto protoreflect.FileDescriptor

var file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1b, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x2a,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x61,
	0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2f, 0x62, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6d, 0x61,
	0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcf, 0x04,
	0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x4d, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x50, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x38, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x5a, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x73, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xd6, 0x07, 0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x64, 0x0a, 0x15, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x13, 0x75, 0x73, 0x65,
	0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x5b, 0x0a, 0x12, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x6c, 0x6f, 0x61,
	0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x54, 0x0a,
	0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x5d, 0x0a, 0x12, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x61, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x42,
	0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x54, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x57, 0x0a, 0x10, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x70, 0x0a, 0x19, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x17, 0x72,
	0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x67, 0x0a, 0x16, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x5f,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x65, 0x73, 0x69, 0x67, 0x6e,
	0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x57, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x58, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x70, 0x69, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x65, 0x64, 0x50,
	0x69, 0x49, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xa8, 0x02, 0x0a, 0x11, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0a,
	0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a,
	0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f,
	0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x48, 0x0a, 0x0e, 0x6f, 0x66, 0x66, 0x69, 0x63,
	0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x0d, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0xd9, 0x02, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x64,
	0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x65, 0x64, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0xe4, 0x01, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x44, 0x6f, 0x63, 0x55, 0x72, 0x6c, 0x22, 0x99, 0x01,
	0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a,
	0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x65,
	0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x22, 0x80, 0x05, 0x0a, 0x10, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x49,
	0x0a, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x09,
	0x6c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10,
	0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x56, 0x0a, 0x0a, 0x62, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x2e, 0x42, 0x72, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x09, 0x62,
	0x72, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0xeb, 0x02, 0x0a, 0x09, 0x42, 0x72, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x54, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72,
	0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x42, 0x0a, 0x13,
	0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x64,
	0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x34, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06,
	0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0x32, 0x0a, 0x0b, 0x70, 0x66, 0x5f, 0x70,
	0x6c, 0x75, 0x73, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x09, 0x70, 0x66, 0x50, 0x6c, 0x75, 0x73, 0x47, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x16,
	0x75, 0x70, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x14, 0x75, 0x70, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x22, 0x9b, 0x03, 0x0a,
	0x0f, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a, 0x08, 0x70,
	0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x07, 0x70, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x32, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x0a, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0a,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x6d, 0x61,
	0x72, 0x69, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6d, 0x61, 0x72, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x71, 0x75, 0x61,
	0x6c, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x55, 0x0a, 0x0f, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a,
	0x12, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x11,
	0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x4c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x22, 0xbf, 0x02, 0x0a, 0x0a, 0x4b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x54, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4b, 0x59, 0x43, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61,
	0x72, 0x5f, 0x78, 0x6d, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x61, 0x64,
	0x68, 0x61, 0x61, 0x72, 0x58, 0x6d, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x73, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x66, 0x69, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x2a, 0x0a, 0x11, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x5f, 0x70, 0x68, 0x6f, 0x74, 0x6f,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x61, 0x64, 0x68,
	0x61, 0x61, 0x72, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x66, 0x0a, 0x12, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6c, 0x6f,
	0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4b, 0x79, 0x63, 0x4f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x11, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x6c, 0x6f, 0x77, 0x22, 0xff, 0x01, 0x0a, 0x12, 0x4b, 0x59, 0x43, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61,
	0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x61, 0x64, 0x68, 0x61, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x41, 0x61, 0x64, 0x68, 0x61, 0x61,
	0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x32, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x3b, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x4b, 0x0a, 0x17, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x35, 0x0a, 0x14, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x12, 0x44, 0x6f, 0x77,
	0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x74, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x74, 0x72, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x83, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x56, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x0e, 0x49,
	0x6e, 0x73, 0x75, 0x72, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2e, 0x0a,
	0x13, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x61, 0x6e, 0x6b,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a,
	0x11, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x66,
	0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x66, 0x73, 0x63, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x42, 0x76, 0x0a, 0x39, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x72,
	0x69, 0x6e, 0x67, 0x6f, 0x74, 0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74,
	0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescOnce sync.Once
	file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescData = file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDesc
)

func file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescGZIP() []byte {
	file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescData)
	})
	return file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDescData
}

var file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_stockguardian_sgapplication_internal_loan_application_proto_goTypes = []interface{}{
	(*LoanApplication)(nil),             // 0: stockguardian.sgapplication.LoanApplication
	(*LoanApplicationDetails)(nil),      // 1: stockguardian.sgapplication.LoanApplicationDetails
	(*BankAccountDetails)(nil),          // 2: stockguardian.sgapplication.BankAccountDetails
	(*AddressDetails)(nil),              // 3: stockguardian.sgapplication.AddressDetails
	(*EmploymentDetails)(nil),           // 4: stockguardian.sgapplication.EmploymentDetails
	(*ConsentDetails)(nil),              // 5: stockguardian.sgapplication.ConsentDetails
	(*UserSelectedDetails)(nil),         // 6: stockguardian.sgapplication.UserSelectedDetails
	(*LoanOfferDetails)(nil),            // 7: stockguardian.sgapplication.LoanOfferDetails
	(*PersonalDetails)(nil),             // 8: stockguardian.sgapplication.PersonalDetails
	(*LocationDetails)(nil),             // 9: stockguardian.sgapplication.LocationDetails
	(*KycDetails)(nil),                  // 10: stockguardian.sgapplication.KycDetails
	(*KYCPersonalDetails)(nil),          // 11: stockguardian.sgapplication.KYCPersonalDetails
	(*RecurringPaymentDetails)(nil),     // 12: stockguardian.sgapplication.RecurringPaymentDetails
	(*EsignDocumentDetails)(nil),        // 13: stockguardian.sgapplication.EsignDocumentDetails
	(*DownPaymentDetails)(nil),          // 14: stockguardian.sgapplication.DownPaymentDetails
	(*ServiceProviderDetails)(nil),      // 15: stockguardian.sgapplication.ServiceProviderDetails
	(*InsurerDetails)(nil),              // 16: stockguardian.sgapplication.InsurerDetails
	(*ConsentDetails_Consent)(nil),      // 17: stockguardian.sgapplication.ConsentDetails.Consent
	(*LoanOfferDetails_BreParams)(nil),  // 18: stockguardian.sgapplication.LoanOfferDetails.BreParams
	(enums.LoanApplicationStatus)(0),    // 19: stockguardian.sgapplication.enums.LoanApplicationStatus
	(enums.LoanApplicationSubStatus)(0), // 20: stockguardian.sgapplication.enums.LoanApplicationSubStatus
	(*timestamppb.Timestamp)(nil),       // 21: google.protobuf.Timestamp
	(*common.BankAccountDetails)(nil),   // 22: api.typesv2.common.BankAccountDetails
	(*common.PostalAddress)(nil),        // 23: api.typesv2.common.PostalAddress
	(common.AddressType)(0),             // 24: api.typesv2.common.AddressType
	(common.EmploymentType)(0),          // 25: api.typesv2.common.EmploymentType
	(*money.Money)(nil),                 // 26: google.type.Money
	(*creditrisk.LoanOfferDetails)(nil), // 27: stockguardian.creditrisk.LoanOfferDetails
	(*date.Date)(nil),                   // 28: google.type.Date
	(*common.Name)(nil),                 // 29: api.typesv2.common.Name
	(common.Gender)(0),                  // 30: api.typesv2.common.Gender
	(common.MaritalStatus)(0),           // 31: api.typesv2.common.MaritalStatus
	(*latlng.LatLng)(nil),               // 32: google.type.LatLng
	(enums.KycOrchestrationFlow)(0),     // 33: stockguardian.sgapplication.enums.KycOrchestrationFlow
	(enums.ConsentType)(0),              // 34: stockguardian.sgapplication.enums.ConsentType
	(*bre.PolicyParams)(nil),            // 35: stockguardian.vendors.inhouse.bre.PolicyParams
	(*common.Duration)(nil),             // 36: api.typesv2.common.Duration
}
var file_api_stockguardian_sgapplication_internal_loan_application_proto_depIdxs = []int32{
	1,  // 0: stockguardian.sgapplication.LoanApplication.details:type_name -> stockguardian.sgapplication.LoanApplicationDetails
	19, // 1: stockguardian.sgapplication.LoanApplication.status:type_name -> stockguardian.sgapplication.enums.LoanApplicationStatus
	20, // 2: stockguardian.sgapplication.LoanApplication.sub_status:type_name -> stockguardian.sgapplication.enums.LoanApplicationSubStatus
	21, // 3: stockguardian.sgapplication.LoanApplication.created_at:type_name -> google.protobuf.Timestamp
	21, // 4: stockguardian.sgapplication.LoanApplication.updated_at:type_name -> google.protobuf.Timestamp
	21, // 5: stockguardian.sgapplication.LoanApplication.deleted_at:type_name -> google.protobuf.Timestamp
	6,  // 6: stockguardian.sgapplication.LoanApplicationDetails.user_selected_details:type_name -> stockguardian.sgapplication.UserSelectedDetails
	7,  // 7: stockguardian.sgapplication.LoanApplicationDetails.loan_offer_details:type_name -> stockguardian.sgapplication.LoanOfferDetails
	3,  // 8: stockguardian.sgapplication.LoanApplicationDetails.address_details:type_name -> stockguardian.sgapplication.AddressDetails
	4,  // 9: stockguardian.sgapplication.LoanApplicationDetails.employment_details:type_name -> stockguardian.sgapplication.EmploymentDetails
	2,  // 10: stockguardian.sgapplication.LoanApplicationDetails.bank_account_details:type_name -> stockguardian.sgapplication.BankAccountDetails
	5,  // 11: stockguardian.sgapplication.LoanApplicationDetails.consent_details:type_name -> stockguardian.sgapplication.ConsentDetails
	8,  // 12: stockguardian.sgapplication.LoanApplicationDetails.personal_details:type_name -> stockguardian.sgapplication.PersonalDetails
	12, // 13: stockguardian.sgapplication.LoanApplicationDetails.recurring_payment_details:type_name -> stockguardian.sgapplication.RecurringPaymentDetails
	13, // 14: stockguardian.sgapplication.LoanApplicationDetails.esign_document_details:type_name -> stockguardian.sgapplication.EsignDocumentDetails
	9,  // 15: stockguardian.sgapplication.LoanApplicationDetails.location_details:type_name -> stockguardian.sgapplication.LocationDetails
	22, // 16: stockguardian.sgapplication.BankAccountDetails.bank_account_details:type_name -> api.typesv2.common.BankAccountDetails
	23, // 17: stockguardian.sgapplication.AddressDetails.address_details:type_name -> api.typesv2.common.PostalAddress
	24, // 18: stockguardian.sgapplication.AddressDetails.address_type:type_name -> api.typesv2.common.AddressType
	25, // 19: stockguardian.sgapplication.EmploymentDetails.occupation:type_name -> api.typesv2.common.EmploymentType
	26, // 20: stockguardian.sgapplication.EmploymentDetails.monthly_income:type_name -> google.type.Money
	23, // 21: stockguardian.sgapplication.EmploymentDetails.office_address:type_name -> api.typesv2.common.PostalAddress
	17, // 22: stockguardian.sgapplication.ConsentDetails.recorded_consents:type_name -> stockguardian.sgapplication.ConsentDetails.Consent
	26, // 23: stockguardian.sgapplication.UserSelectedDetails.loan_amount:type_name -> google.type.Money
	27, // 24: stockguardian.sgapplication.LoanOfferDetails.loan_offer:type_name -> stockguardian.creditrisk.LoanOfferDetails
	18, // 25: stockguardian.sgapplication.LoanOfferDetails.bre_params:type_name -> stockguardian.sgapplication.LoanOfferDetails.BreParams
	28, // 26: stockguardian.sgapplication.PersonalDetails.dob:type_name -> google.type.Date
	29, // 27: stockguardian.sgapplication.PersonalDetails.pan_name:type_name -> api.typesv2.common.Name
	30, // 28: stockguardian.sgapplication.PersonalDetails.gender:type_name -> api.typesv2.common.Gender
	29, // 29: stockguardian.sgapplication.PersonalDetails.father_name:type_name -> api.typesv2.common.Name
	29, // 30: stockguardian.sgapplication.PersonalDetails.mother_name:type_name -> api.typesv2.common.Name
	31, // 31: stockguardian.sgapplication.PersonalDetails.marital_status:type_name -> api.typesv2.common.MaritalStatus
	32, // 32: stockguardian.sgapplication.LocationDetails.latitude_longitude:type_name -> google.type.LatLng
	11, // 33: stockguardian.sgapplication.KycDetails.personal_data:type_name -> stockguardian.sgapplication.KYCPersonalDetails
	33, // 34: stockguardian.sgapplication.KycDetails.orchestration_flow:type_name -> stockguardian.sgapplication.enums.KycOrchestrationFlow
	29, // 35: stockguardian.sgapplication.KYCPersonalDetails.name:type_name -> api.typesv2.common.Name
	30, // 36: stockguardian.sgapplication.KYCPersonalDetails.gender:type_name -> api.typesv2.common.Gender
	28, // 37: stockguardian.sgapplication.KYCPersonalDetails.dob:type_name -> google.type.Date
	23, // 38: stockguardian.sgapplication.KYCPersonalDetails.address:type_name -> api.typesv2.common.PostalAddress
	26, // 39: stockguardian.sgapplication.DownPaymentDetails.amount:type_name -> google.type.Money
	16, // 40: stockguardian.sgapplication.ServiceProviderDetails.insurer_details:type_name -> stockguardian.sgapplication.InsurerDetails
	34, // 41: stockguardian.sgapplication.ConsentDetails.Consent.consent_type:type_name -> stockguardian.sgapplication.enums.ConsentType
	21, // 42: stockguardian.sgapplication.ConsentDetails.Consent.recorded_at:type_name -> google.protobuf.Timestamp
	35, // 43: stockguardian.sgapplication.LoanOfferDetails.BreParams.policy_params:type_name -> stockguardian.vendors.inhouse.bre.PolicyParams
	26, // 44: stockguardian.sgapplication.LoanOfferDetails.BreParams.desired_loan_amount:type_name -> google.type.Money
	36, // 45: stockguardian.sgapplication.LoanOfferDetails.BreParams.tenure:type_name -> api.typesv2.common.Duration
	26, // 46: stockguardian.sgapplication.LoanOfferDetails.BreParams.pf_plus_gst:type_name -> google.type.Money
	26, // 47: stockguardian.sgapplication.LoanOfferDetails.BreParams.upfront_payment_amount:type_name -> google.type.Money
	48, // [48:48] is the sub-list for method output_type
	48, // [48:48] is the sub-list for method input_type
	48, // [48:48] is the sub-list for extension type_name
	48, // [48:48] is the sub-list for extension extendee
	0,  // [0:48] is the sub-list for field type_name
}

func init() { file_api_stockguardian_sgapplication_internal_loan_application_proto_init() }
func file_api_stockguardian_sgapplication_internal_loan_application_proto_init() {
	if File_api_stockguardian_sgapplication_internal_loan_application_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanApplication); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSelectedDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KYCPersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecurringPaymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EsignDocumentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownPaymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceProviderDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InsurerDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsentDetails_Consent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferDetails_BreParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes[15].OneofWrappers = []interface{}{
		(*ServiceProviderDetails_InsurerDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_stockguardian_sgapplication_internal_loan_application_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_sgapplication_internal_loan_application_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_sgapplication_internal_loan_application_proto_msgTypes,
	}.Build()
	File_api_stockguardian_sgapplication_internal_loan_application_proto = out.File
	file_api_stockguardian_sgapplication_internal_loan_application_proto_rawDesc = nil
	file_api_stockguardian_sgapplication_internal_loan_application_proto_goTypes = nil
	file_api_stockguardian_sgapplication_internal_loan_application_proto_depIdxs = nil
}
