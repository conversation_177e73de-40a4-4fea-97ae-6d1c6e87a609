// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/customer/internal/transaction.proto

package customer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gringott/api/stockguardian/customer/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.TransactionSource(0)
)

// Validate checks the field values on CustomerTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerTransaction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerTransaction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerTransactionMultiError, or nil if none found.
func (m *CustomerTransaction) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerTransaction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CustomerId

	// no validation rules for Source

	// no validation rules for SourceReferenceId

	// no validation rules for TransactionType

	if all {
		switch v := interface{}(m.GetStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerTransactionValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerTransactionValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerTransactionValidationError{
				field:  "StartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerTransactionValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerTransactionValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerTransactionValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedFields()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerTransactionValidationError{
					field:  "UpdatedFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerTransactionValidationError{
					field:  "UpdatedFields",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedFields()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerTransactionValidationError{
				field:  "UpdatedFields",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomerTransactionMultiError(errors)
	}

	return nil
}

// CustomerTransactionMultiError is an error wrapping multiple validation
// errors returned by CustomerTransaction.ValidateAll() if the designated
// constraints aren't met.
type CustomerTransactionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerTransactionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerTransactionMultiError) AllErrors() []error { return m }

// CustomerTransactionValidationError is the validation error returned by
// CustomerTransaction.Validate if the designated constraints aren't met.
type CustomerTransactionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerTransactionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerTransactionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerTransactionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerTransactionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerTransactionValidationError) ErrorName() string {
	return "CustomerTransactionValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerTransactionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerTransaction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerTransactionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerTransactionValidationError{}

// Validate checks the field values on UpdatedFields with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdatedFields) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatedFields with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdatedFieldsMultiError, or
// nil if none found.
func (m *UpdatedFields) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatedFields) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdatedFieldsValidationError{
					field:  "UpdatedFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdatedFieldsValidationError{
					field:  "UpdatedFrom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdatedFieldsValidationError{
				field:  "UpdatedFrom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdatedFieldsValidationError{
					field:  "UpdatedTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdatedFieldsValidationError{
					field:  "UpdatedTo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdatedFieldsValidationError{
				field:  "UpdatedTo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdatedFieldsMultiError(errors)
	}

	return nil
}

// UpdatedFieldsMultiError is an error wrapping multiple validation errors
// returned by UpdatedFields.ValidateAll() if the designated constraints
// aren't met.
type UpdatedFieldsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatedFieldsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatedFieldsMultiError) AllErrors() []error { return m }

// UpdatedFieldsValidationError is the validation error returned by
// UpdatedFields.Validate if the designated constraints aren't met.
type UpdatedFieldsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatedFieldsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatedFieldsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatedFieldsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatedFieldsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatedFieldsValidationError) ErrorName() string { return "UpdatedFieldsValidationError" }

// Error satisfies the builtin error interface
func (e UpdatedFieldsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatedFields.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatedFieldsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatedFieldsValidationError{}
