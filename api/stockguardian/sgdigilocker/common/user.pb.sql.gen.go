// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gringott/api/stockguardian/sgdigilocker/common/user.pb.go

package common

import (
	"database/sql/driver"
	"fmt"
	"google.golang.org/protobuf/encoding/protojson"
)

// Scanner interface implementation for parsing UserProfile while reading from DB
func (a *UserProfile) Scan(src interface{}) error {
	marshalledData, ok := src.([]byte)
	if !ok {
		return fmt.Errorf("type assertion to []byte failed, src: %T", src)
	}
	return protojson.Unmarshal(marshalledData, a)
}

// Valuer interface implementation for storing the UserProfile in string format in DB
func (a *UserProfile) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return protojson.Marshal(a)
}

// Marshaler interface implementation for UserProfile
func (a *UserProfile) MarshalJSON() ([]byte, error) {
	return protojson.Marshal(a)
}

// Unmarshaler interface implementation for UserProfile
func (a *UserProfile) UnmarshalJSON(b []byte) error {
	return protojson.Unmarshal(b, a)
}
