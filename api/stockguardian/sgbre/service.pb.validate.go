// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/sgbre/service.proto

package sgbre

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Bureau(0)
)

// Validate checks the field values on GetLoanDecisioningRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningRequestMultiError, or nil if none found.
func (m *GetLoanDecisioningRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ClientId

	// no validation rules for SchemeId

	// no validation rules for BatchId

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPersonalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestValidationError{
				field:  "PersonalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestValidationError{
				field:  "EmploymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestValidationError{
				field:  "LoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntityId

	if len(errors) > 0 {
		return GetLoanDecisioningRequestMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanDecisioningRequest.ValidateAll() if the
// designated constraints aren't met.
type GetLoanDecisioningRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequestMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequestValidationError is the validation error returned by
// GetLoanDecisioningRequest.Validate if the designated constraints aren't met.
type GetLoanDecisioningRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequestValidationError) ErrorName() string {
	return "GetLoanDecisioningRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequestValidationError{}

// Validate checks the field values on GetLoanDecisioningResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningResponseMultiError, or nil if none found.
func (m *GetLoanDecisioningResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanDecision

	// no validation rules for RawBreResponse

	if all {
		switch v := interface{}(m.GetOfferDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseValidationError{
				field:  "OfferDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SchemeId

	// no validation rules for BatchId

	// no validation rules for CustId

	// no validation rules for LoanProgram

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningResponseMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanDecisioningResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDecisioningResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningResponseMultiError) AllErrors() []error { return m }

// GetLoanDecisioningResponseValidationError is the validation error returned
// by GetLoanDecisioningResponse.Validate if the designated constraints aren't met.
type GetLoanDecisioningResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningResponseValidationError) ErrorName() string {
	return "GetLoanDecisioningResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningResponseValidationError{}

// Validate checks the field values on GetLoanDecisioningRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningRequestV2MultiError, or nil if none found.
func (m *GetLoanDecisioningRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "LoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2ValidationError{
				field:  "LoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEvaluationRequestTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "EvaluationRequestTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "EvaluationRequestTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvaluationRequestTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2ValidationError{
				field:  "EvaluationRequestTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetCustomerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2ValidationError{
				field:  "CustomerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2ValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Bureau

	// no validation rules for ClientId

	if len(errors) > 0 {
		return GetLoanDecisioningRequestV2MultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequestV2MultiError is an error wrapping multiple
// validation errors returned by GetLoanDecisioningRequestV2.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDecisioningRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequestV2MultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequestV2ValidationError is the validation error returned
// by GetLoanDecisioningRequestV2.Validate if the designated constraints
// aren't met.
type GetLoanDecisioningRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequestV2ValidationError) ErrorName() string {
	return "GetLoanDecisioningRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequestV2ValidationError{}

// Validate checks the field values on CustomerDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerDetailsMultiError, or nil if none found.
func (m *CustomerDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPersonalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PersonalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "EmploymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResidentialAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "ResidentialAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "ResidentialAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResidentialAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "ResidentialAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestedLoanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "RequestedLoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "RequestedLoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestedLoanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "RequestedLoanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomerDetailsMultiError(errors)
	}

	return nil
}

// CustomerDetailsMultiError is an error wrapping multiple validation errors
// returned by CustomerDetails.ValidateAll() if the designated constraints
// aren't met.
type CustomerDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDetailsMultiError) AllErrors() []error { return m }

// CustomerDetailsValidationError is the validation error returned by
// CustomerDetails.Validate if the designated constraints aren't met.
type CustomerDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDetailsValidationError) ErrorName() string { return "CustomerDetailsValidationError" }

// Error satisfies the builtin error interface
func (e CustomerDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDetailsValidationError{}

// Validate checks the field values on PersonalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PersonalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PersonalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PersonalDetailsMultiError, or nil if none found.
func (m *PersonalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PersonalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PersonalDetailsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PersonalDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	// no validation rules for Pan

	if len(errors) > 0 {
		return PersonalDetailsMultiError(errors)
	}

	return nil
}

// PersonalDetailsMultiError is an error wrapping multiple validation errors
// returned by PersonalDetails.ValidateAll() if the designated constraints
// aren't met.
type PersonalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PersonalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PersonalDetailsMultiError) AllErrors() []error { return m }

// PersonalDetailsValidationError is the validation error returned by
// PersonalDetails.Validate if the designated constraints aren't met.
type PersonalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PersonalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PersonalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PersonalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PersonalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PersonalDetailsValidationError) ErrorName() string { return "PersonalDetailsValidationError" }

// Error satisfies the builtin error interface
func (e PersonalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPersonalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PersonalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PersonalDetailsValidationError{}

// Validate checks the field values on EmploymentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EmploymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmploymentDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmploymentDetailsMultiError, or nil if none found.
func (m *EmploymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *EmploymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmploymentType

	if all {
		switch v := interface{}(m.GetMonthlyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmploymentDetailsValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmploymentDetailsValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmploymentDetailsValidationError{
				field:  "MonthlyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmployerName

	// no validation rules for WorkEmail

	if all {
		switch v := interface{}(m.GetWorkAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmploymentDetailsValidationError{
					field:  "WorkAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmploymentDetailsValidationError{
					field:  "WorkAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorkAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmploymentDetailsValidationError{
				field:  "WorkAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EmploymentDetailsMultiError(errors)
	}

	return nil
}

// EmploymentDetailsMultiError is an error wrapping multiple validation errors
// returned by EmploymentDetails.ValidateAll() if the designated constraints
// aren't met.
type EmploymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmploymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmploymentDetailsMultiError) AllErrors() []error { return m }

// EmploymentDetailsValidationError is the validation error returned by
// EmploymentDetails.Validate if the designated constraints aren't met.
type EmploymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmploymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmploymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmploymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmploymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmploymentDetailsValidationError) ErrorName() string {
	return "EmploymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e EmploymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmploymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmploymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmploymentDetailsValidationError{}

// Validate checks the field values on OfferDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferDetailsMultiError, or
// nil if none found.
func (m *OfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsValidationError{
				field:  "MaxEmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxTenureInMonths

	// no validation rules for MinTenureInMonths

	// no validation rules for InterestPercentage

	// no validation rules for ProcessingFeePercentage

	// no validation rules for GstPercentage

	if all {
		switch v := interface{}(m.GetEmiDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "EmiDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "EmiDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsValidationError{
				field:  "EmiDueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDetailsValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDetailsValidationError{
				field:  "ValidTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OfferDetailsMultiError(errors)
	}

	return nil
}

// OfferDetailsMultiError is an error wrapping multiple validation errors
// returned by OfferDetails.ValidateAll() if the designated constraints aren't met.
type OfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferDetailsMultiError) AllErrors() []error { return m }

// OfferDetailsValidationError is the validation error returned by
// OfferDetails.Validate if the designated constraints aren't met.
type OfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferDetailsValidationError) ErrorName() string { return "OfferDetailsValidationError" }

// Error satisfies the builtin error interface
func (e OfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferDetailsValidationError{}

// Validate checks the field values on FinalBreDecision with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FinalBreDecision) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinalBreDecision with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FinalBreDecisionMultiError, or nil if none found.
func (m *FinalBreDecision) ValidateAll() error {
	return m.validate(true)
}

func (m *FinalBreDecision) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LendingProgram

	// no validation rules for Decision

	if all {
		switch v := interface{}(m.GetOfferDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinalBreDecisionValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinalBreDecisionValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinalBreDecisionValidationError{
				field:  "OfferDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinalBreDecisionValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinalBreDecisionValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinalBreDecisionValidationError{
				field:  "ValidTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FinalBreDecisionMultiError(errors)
	}

	return nil
}

// FinalBreDecisionMultiError is an error wrapping multiple validation errors
// returned by FinalBreDecision.ValidateAll() if the designated constraints
// aren't met.
type FinalBreDecisionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinalBreDecisionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinalBreDecisionMultiError) AllErrors() []error { return m }

// FinalBreDecisionValidationError is the validation error returned by
// FinalBreDecision.Validate if the designated constraints aren't met.
type FinalBreDecisionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinalBreDecisionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinalBreDecisionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinalBreDecisionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinalBreDecisionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinalBreDecisionValidationError) ErrorName() string { return "FinalBreDecisionValidationError" }

// Error satisfies the builtin error interface
func (e FinalBreDecisionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinalBreDecision.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinalBreDecisionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinalBreDecisionValidationError{}

// Validate checks the field values on GetLoanDecisioningResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningResponseV2MultiError, or nil if none found.
func (m *GetLoanDecisioningResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseV2ValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerId

	// no validation rules for ApplicationId

	if all {
		switch v := interface{}(m.GetEvaluationRequestTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "EvaluationRequestTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "EvaluationRequestTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvaluationRequestTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseV2ValidationError{
				field:  "EvaluationRequestTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseV2ValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrioritizedDecision()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PrioritizedDecision",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PrioritizedDecision",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrioritizedDecision()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseV2ValidationError{
				field:  "PrioritizedDecision",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFinalDecision() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
						field:  fmt.Sprintf("FinalDecision[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
						field:  fmt.Sprintf("FinalDecision[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanDecisioningResponseV2ValidationError{
					field:  fmt.Sprintf("FinalDecision[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RawLoanDecisionResponse

	if len(errors) > 0 {
		return GetLoanDecisioningResponseV2MultiError(errors)
	}

	return nil
}

// GetLoanDecisioningResponseV2MultiError is an error wrapping multiple
// validation errors returned by GetLoanDecisioningResponseV2.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDecisioningResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningResponseV2MultiError) AllErrors() []error { return m }

// GetLoanDecisioningResponseV2ValidationError is the validation error returned
// by GetLoanDecisioningResponseV2.Validate if the designated constraints
// aren't met.
type GetLoanDecisioningResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningResponseV2ValidationError) ErrorName() string {
	return "GetLoanDecisioningResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningResponseV2ValidationError{}

// Validate checks the field values on
// GetLoanDecisioningRequest_PersonalDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningRequest_PersonalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDecisioningRequest_PersonalDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanDecisioningRequest_PersonalDetailsMultiError, or nil if none found.
func (m *GetLoanDecisioningRequest_PersonalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequest_PersonalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_PersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_PersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequest_PersonalDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_PersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_PersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequest_PersonalDetailsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_PersonalDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_PersonalDetailsValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequest_PersonalDetailsValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningRequest_PersonalDetailsMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequest_PersonalDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanDecisioningRequest_PersonalDetails.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningRequest_PersonalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequest_PersonalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequest_PersonalDetailsMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequest_PersonalDetailsValidationError is the validation
// error returned by GetLoanDecisioningRequest_PersonalDetails.Validate if the
// designated constraints aren't met.
type GetLoanDecisioningRequest_PersonalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequest_PersonalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequest_PersonalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequest_PersonalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequest_PersonalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequest_PersonalDetailsValidationError) ErrorName() string {
	return "GetLoanDecisioningRequest_PersonalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequest_PersonalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequest_PersonalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequest_PersonalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequest_PersonalDetailsValidationError{}

// Validate checks the field values on
// GetLoanDecisioningRequest_EmploymentDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningRequest_EmploymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDecisioningRequest_EmploymentDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanDecisioningRequest_EmploymentDetailsMultiError, or nil if none found.
func (m *GetLoanDecisioningRequest_EmploymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequest_EmploymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmploymentType

	if all {
		switch v := interface{}(m.GetMonthlyIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_EmploymentDetailsValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_EmploymentDetailsValidationError{
					field:  "MonthlyIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequest_EmploymentDetailsValidationError{
				field:  "MonthlyIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmployerName

	// no validation rules for WorkEmail

	if len(errors) > 0 {
		return GetLoanDecisioningRequest_EmploymentDetailsMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequest_EmploymentDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanDecisioningRequest_EmploymentDetails.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningRequest_EmploymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequest_EmploymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequest_EmploymentDetailsMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequest_EmploymentDetailsValidationError is the validation
// error returned by GetLoanDecisioningRequest_EmploymentDetails.Validate if
// the designated constraints aren't met.
type GetLoanDecisioningRequest_EmploymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequest_EmploymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequest_EmploymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequest_EmploymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequest_EmploymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequest_EmploymentDetailsValidationError) ErrorName() string {
	return "GetLoanDecisioningRequest_EmploymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequest_EmploymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequest_EmploymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequest_EmploymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequest_EmploymentDetailsValidationError{}

// Validate checks the field values on GetLoanDecisioningResponse_OfferDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanDecisioningResponse_OfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDecisioningResponse_OfferDetails with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLoanDecisioningResponse_OfferDetailsMultiError, or nil if none found.
func (m *GetLoanDecisioningResponse_OfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningResponse_OfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponse_OfferDetailsValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponse_OfferDetailsValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "MaxEmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponse_OfferDetailsValidationError{
				field:  "MaxEmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InterestPercentage

	// no validation rules for ProcessingFeePercentage

	// no validation rules for GstPercentage

	// no validation rules for MinTenureInMonths

	// no validation rules for MaxTenureInMonths

	if all {
		switch v := interface{}(m.GetEmiDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "EmiDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "EmiDueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponse_OfferDetailsValidationError{
				field:  "EmiDueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValidTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_OfferDetailsValidationError{
					field:  "ValidTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValidTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponse_OfferDetailsValidationError{
				field:  "ValidTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningResponse_OfferDetailsMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningResponse_OfferDetailsMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanDecisioningResponse_OfferDetails.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningResponse_OfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningResponse_OfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningResponse_OfferDetailsMultiError) AllErrors() []error { return m }

// GetLoanDecisioningResponse_OfferDetailsValidationError is the validation
// error returned by GetLoanDecisioningResponse_OfferDetails.Validate if the
// designated constraints aren't met.
type GetLoanDecisioningResponse_OfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningResponse_OfferDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningResponse_OfferDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningResponse_OfferDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningResponse_OfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningResponse_OfferDetailsValidationError) ErrorName() string {
	return "GetLoanDecisioningResponse_OfferDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningResponse_OfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningResponse_OfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningResponse_OfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningResponse_OfferDetailsValidationError{}

// Validate checks the field values on CustomerDetails_RequestedLoanDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CustomerDetails_RequestedLoanDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerDetails_RequestedLoanDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CustomerDetails_RequestedLoanDetailsMultiError, or nil if none found.
func (m *CustomerDetails_RequestedLoanDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerDetails_RequestedLoanDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDesiredLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDesiredLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetails_RequestedLoanDetailsValidationError{
				field:  "DesiredLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTenure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetails_RequestedLoanDetailsValidationError{
				field:  "Tenure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Roi

	if all {
		switch v := interface{}(m.GetPfPlusGst()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "PfPlusGst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "PfPlusGst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPfPlusGst()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetails_RequestedLoanDetailsValidationError{
				field:  "PfPlusGst",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpfrontPaymentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "UpfrontPaymentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "UpfrontPaymentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpfrontPaymentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetails_RequestedLoanDetailsValidationError{
				field:  "UpfrontPaymentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomerDetails_RequestedLoanDetailsMultiError(errors)
	}

	return nil
}

// CustomerDetails_RequestedLoanDetailsMultiError is an error wrapping multiple
// validation errors returned by
// CustomerDetails_RequestedLoanDetails.ValidateAll() if the designated
// constraints aren't met.
type CustomerDetails_RequestedLoanDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDetails_RequestedLoanDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDetails_RequestedLoanDetailsMultiError) AllErrors() []error { return m }

// CustomerDetails_RequestedLoanDetailsValidationError is the validation error
// returned by CustomerDetails_RequestedLoanDetails.Validate if the designated
// constraints aren't met.
type CustomerDetails_RequestedLoanDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDetails_RequestedLoanDetailsValidationError) ErrorName() string {
	return "CustomerDetails_RequestedLoanDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerDetails_RequestedLoanDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerDetails_RequestedLoanDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDetails_RequestedLoanDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDetails_RequestedLoanDetailsValidationError{}
