package screener

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"reflect"
	"sort"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	screenerPb "github.com/epifi/gamma/api/screener"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/screener/checks"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

var (
	uanActor = "uan-actor"
	uanSaReq = &screenerPb.ScreenerAttempt{
		Id:      "SCRATT12345",
		ActorId: uanActor,
		ResultInfo: &screenerPb.ScreenerAttemptResultInfo{
			Result: screenerPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_IN_PROGRESS,
		},
	}
	uanSa = &screenerPb.ScreenerAttempt{
		Id:      "SCRATT12345",
		ActorId: uanActor,
		ResultInfo: &screenerPb.ScreenerAttemptResultInfo{
			Result: screenerPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_IN_PROGRESS,
		},
	}
	uanCheckResp = &screenerPb.RunCheckResponse{
		Status: rpc.StatusOk(),
		CheckDetails: &screenerPb.CheckDetails{
			CheckType:      screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
			CheckResult:    screenerPb.CheckResult_CHECK_RESULT_PASSED,
			RetriesLeft:    0,
			CheckAttemptId: "new passed",
		},
		NextAction: nil,
	}
	uanSaPassed = &screenerPb.ScreenerAttempt{
		ActorId: uanActor,
		ResultInfo: &screenerPb.ScreenerAttemptResultInfo{
			Result: screenerPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_PASSED,
			PassingStages: []screenerPb.CheckType{
				screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
			},
		},
	}
	passedActor = "passedActor"
	saPassed    = &screenerPb.ScreenerAttempt{
		Id:      "SCRATT09876",
		ActorId: passedActor,
		ResultInfo: &screenerPb.ScreenerAttemptResultInfo{
			Result:        screenerPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_PASSED,
			PassingStages: []screenerPb.CheckType{screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE},
		},
	}
	caResp = &checks.GetCheckAttemptsResponse{
		CheckAttempts: []*screenerPb.ScreenerCheckAttempt{
			{
				Id:                "old failed",
				ScreenerAttemptId: saPassed.GetId(),
				CheckResult:       screenerPb.CheckResult_CHECK_RESULT_FAILED,
				UpdatedAt: &timestampPb.Timestamp{
					Seconds: 200,
				},
				DeletedAtUnix: 200,
			},
			{
				Id:                "old skipped",
				ScreenerAttemptId: saPassed.GetId(),
				CheckResult:       screenerPb.CheckResult_CHECK_RESULT_SKIPPED,
				UpdatedAt: &timestampPb.Timestamp{
					Seconds: 100,
				},
				DeletedAtUnix: 100,
			},
			{
				Id:                "new passed",
				ScreenerAttemptId: saPassed.GetId(),
				CheckResult:       screenerPb.CheckResult_CHECK_RESULT_PASSED,
				UpdatedAt: &timestampPb.Timestamp{
					Seconds: 300,
				},
				DeletedAtUnix: 300,
			},
		},
	}
	passedCheckMap = []*screenerPb.CheckDetails{
		{
			CheckType:      screenerPb.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION,
			CheckResult:    screenerPb.CheckResult_CHECK_RESULT_PASSED,
			RetriesLeft:    0,
			CheckAttemptId: "new passed",
		},
		{
			CheckType:   screenerPb.CheckType_CHECK_TYPE_EPFO,
			CheckResult: screenerPb.CheckResult_CHECK_RESULT_DISABLED,
		},
		{
			CheckType:      screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
			CheckResult:    screenerPb.CheckResult_CHECK_RESULT_PASSED,
			RetriesLeft:    -2,
			CheckAttemptId: "new passed",
		},
		{
			CheckType:   screenerPb.CheckType_CHECK_TYPE_BACKGROUND_CHECKS,
			CheckResult: screenerPb.CheckResult_CHECK_RESULT_DISABLED,
		},
		{
			CheckType:   screenerPb.CheckType_CHECK_TYPE_GMAIL_INSIGHTS,
			CheckResult: screenerPb.CheckResult_CHECK_RESULT_DISABLED,
		},
		{
			CheckType:   screenerPb.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS,
			CheckResult: screenerPb.CheckResult_CHECK_RESULT_DISABLED,
		},
		{
			CheckType:   screenerPb.CheckType_CHECK_TYPE_CREDIT_REPORT,
			CheckResult: screenerPb.CheckResult_CHECK_RESULT_DISABLED,
		},
		{
			CheckType:      screenerPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE,
			CheckResult:    screenerPb.CheckResult_CHECK_RESULT_PASSED,
			RetriesLeft:    -2,
			CheckAttemptId: "new passed",
		},
		{
			CheckType:      screenerPb.CheckType_CHECK_TYPE_ITR_INTIMATION,
			CheckResult:    screenerPb.CheckResult_CHECK_RESULT_PASSED,
			CheckAttemptId: "new passed",
		},
		{
			CheckType:      screenerPb.CheckType_CHECK_TYPE_LENDABILITY,
			CheckResult:    screenerPb.CheckResult_CHECK_RESULT_PASSED,
			RetriesLeft:    -2,
			CheckAttemptId: "new passed",
		},
		{
			CheckType:   screenerPb.CheckType_CHECK_TYPE_SMS_PARSER,
			CheckResult: screenerPb.CheckResult_CHECK_RESULT_DISABLED,
		},
		{
			CheckType:      screenerPb.CheckType_CHECK_TYPE_INSTALLED_APPS,
			CheckResult:    screenerPb.CheckResult_CHECK_RESULT_PASSED,
			RetriesLeft:    -2,
			CheckAttemptId: "new passed",
		},
	}
)

type ScreenerAttemptArgMatcher struct {
	want *screenerPb.ScreenerAttempt
}

func newScreenerAttemptArgMatcher(want *screenerPb.ScreenerAttempt) *ScreenerAttemptArgMatcher {
	return &ScreenerAttemptArgMatcher{want: want}
}

func (c *ScreenerAttemptArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*screenerPb.ScreenerAttempt)
	if !ok {
		return false
	}

	c.want.Id = got.Id
	c.want.Expiry = got.Expiry
	return reflect.DeepEqual(c.want, got)
}

func (c *ScreenerAttemptArgMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

func getScreenerAttemptsByActorIdRespComparator(got, want *screenerPb.GetScreenerAttemptsByActorIdResponse) bool {
	return reflect.DeepEqual(got.GetScreenerAttempt(), want.GetScreenerAttempt()) &&
		reflect.DeepEqual(got.GetStatus(), want.GetStatus()) &&
		checkDetailsComparator(got.GetChecksMap(), want.GetChecksMap())
}

func checkDetailsComparator(got, want []*screenerPb.CheckDetails) bool {
	sort.Slice(got, func(i, j int) bool {
		return got[i].GetCheckType().Number() < got[j].GetCheckType().Number()
	})
	sort.Slice(want, func(i, j int) bool {
		return want[i].GetCheckType().Number() < want[j].GetCheckType().Number()
	})
	_ = fmt.Sprintf("%v\n%v", got, want)
	return reflect.DeepEqual(want, got)
}

func TestService_RunCheck(t *testing.T) {
	type args struct {
		req *screenerPb.RunCheckRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockedDependencies)
		want    *screenerPb.RunCheckResponse
		wantErr error
	}{
		{
			name: "successfully ran check after deleting duplicate entry",
			args: args{
				req: &screenerPb.RunCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
					BackAction: &dlPb.Deeplink{
						Screen: dlPb.Screen_ALFRED_REQUEST_CHOICE,
					},
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.saDao.EXPECT().DeleteAttemptsOfActor(gomock.Any(), uanActor).
					Return(nil)
				m.saDao.EXPECT().Create(gomock.Any(), newScreenerAttemptArgMatcher(uanSaReq)).
					Return(uanSa, nil)
				m.cf.EXPECT().RunCheck(gomock.Any(), &checks.RunCheckRequest{
					ActorId:           uanActor,
					ScreenerAttemptId: uanSa.GetId(),
					BackAction: &dlPb.Deeplink{
						Screen: dlPb.Screen_ALFRED_REQUEST_CHOICE,
					},
				}).Return(uanCheckResp, nil)
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(uanSa, nil)
				m.saDao.EXPECT().UpdateActiveAttemptForActor(gomock.Any(), uanSaPassed, []screenerPb.ScreenerAttemptFieldMask{
					screenerPb.ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_RESULT_INFO,
				}).Return(nil)
			},
			want:    uanCheckResp,
			wantErr: nil,
		},
		{
			name: "error running factory check - received error message",
			args: args{
				req: &screenerPb.RunCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(uanSa, nil)
				m.cf.EXPECT().RunCheck(gomock.Any(), &checks.RunCheckRequest{
					ActorId:           uanActor,
					ScreenerAttemptId: uanSa.GetId(),
				}).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want: &screenerPb.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg(epifierrors.ErrInvalidArgument.Error()),
			},
			wantErr: nil,
		},
		{
			name: "error running factory check - status not ok",
			args: args{
				req: &screenerPb.RunCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(uanSa, nil)
				m.cf.EXPECT().RunCheck(gomock.Any(), &checks.RunCheckRequest{
					ActorId:           uanActor,
					ScreenerAttemptId: uanSa.GetId(),
				}).Return(&screenerPb.RunCheckResponse{
					Status: rpc.StatusInvalidArgument(),
				}, nil)
			},
			want: &screenerPb.RunCheckResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: nil,
		},
		{
			name: "error while updating check result",
			args: args{
				req: &screenerPb.RunCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(uanSa, nil)
				m.cf.EXPECT().RunCheck(gomock.Any(), &checks.RunCheckRequest{
					ActorId:           uanActor,
					ScreenerAttemptId: uanSa.GetId(),
				}).Return(uanCheckResp, nil)
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(uanSa, nil)
				m.saDao.EXPECT().UpdateActiveAttemptForActor(gomock.Any(), uanSaPassed, []screenerPb.ScreenerAttemptFieldMask{
					screenerPb.ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_RESULT_INFO,
				}).Return(epifierrors.ErrPermanent)
			},
			want: &screenerPb.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg(epifierrors.ErrPermanent.Error()),
			},
			wantErr: nil,
		},
		{
			name: "error in getOrCreateScreenerAttempt - deleting attempt",
			args: args{
				req: &screenerPb.RunCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.saDao.EXPECT().DeleteAttemptsOfActor(gomock.Any(), uanActor).
					Return(epifierrors.ErrInvalidSQL)
			},
			want: &screenerPb.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in getOrCreateScreenerAttempt"),
			},
			wantErr: nil,
		},
		{
			name: "error in getOrCreateScreenerAttempt - creating attempt",
			args: args{
				req: &screenerPb.RunCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.saDao.EXPECT().DeleteAttemptsOfActor(gomock.Any(), uanActor).
					Return(nil)
				m.saDao.EXPECT().Create(gomock.Any(), newScreenerAttemptArgMatcher(uanSaReq)).
					Return(nil, epifierrors.ErrInvalidSQL)
			},
			want: &screenerPb.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in getOrCreateScreenerAttempt"),
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			got, err := svc.RunCheck(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("RunCheck value mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

func TestService_GetScreenerAttemptsByActorId(t *testing.T) {
	type args struct {
		req *screenerPb.GetScreenerAttemptsByActorIdRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockedDependencies)
		want    *screenerPb.GetScreenerAttemptsByActorIdResponse
		wantErr error
	}{
		{
			name: "successful fetch",
			args: args{
				req: &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId: passedActor,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), passedActor).
					Return(saPassed, nil)
				m.cf.EXPECT().GetCheckAttempts(gomock.Any(), &checks.GetCheckAttemptsRequest{
					ActorId:           passedActor,
					ScreenerAttemptId: saPassed.GetId(),
				}).Return(caResp, nil).AnyTimes()
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_CONNECTED_ACCOUNTS_IN_SCREENER).
					WithActorId(passedActor)).Return(false, nil)
			},
			want: &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status:          rpc.StatusOk(),
				ScreenerAttempt: saPassed,
				ChecksMap:       passedCheckMap,
			},
			wantErr: nil,
		},
		{
			name: "no active attempt",
			args: args{
				req: &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId: uanActor,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(nil, epifierrors.ErrRecordNotFound)
				m.saDao.EXPECT().DeleteAttemptsOfActor(gomock.Any(), uanActor).Return(nil)
				m.saDao.EXPECT().Create(gomock.Any(), newScreenerAttemptArgMatcher(uanSaReq)).Return(uanSa, nil)
				m.cf.EXPECT().GetCheckAttempts(gomock.Any(), &checks.GetCheckAttemptsRequest{
					ActorId:           uanActor,
					ScreenerAttemptId: uanSa.GetId(),
				}).Return(caResp, nil).AnyTimes()
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_CONNECTED_ACCOUNTS_IN_SCREENER).
					WithActorId(uanActor)).Return(false, nil)
			},
			want: &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status:          rpc.StatusOk(),
				ScreenerAttempt: uanSa,
				ChecksMap:       passedCheckMap,
			},
			wantErr: nil,
		},
		{
			name: "error while getting active attempts",
			args: args{
				req: &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId: passedActor,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), passedActor).
					Return(nil, epifierrors.ErrPermanent)
			},
			want: &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Errorf("error while getting active screener attempt: %v", epifierrors.ErrPermanent).Error()),
			},
			wantErr: nil,
		},
		{
			name: "error while getting check attempts",
			args: args{
				req: &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId: passedActor,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), passedActor).
					Return(saPassed, nil)
				m.cf.EXPECT().GetCheckAttempts(gomock.Any(), &checks.GetCheckAttemptsRequest{
					ActorId:           passedActor,
					ScreenerAttemptId: saPassed.GetId(),
				}).Return(nil, epifierrors.ErrInvalidSQL)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), release.NewCommonConstraintData(types.Feature_FEATURE_CONNECTED_ACCOUNTS_IN_SCREENER).
					WithActorId(passedActor)).Return(true, nil).AnyTimes()
			},
			want: &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while getting check details"),
			},
			wantErr: nil,
		},
		{
			name: "no cached attempt",
			args: args{
				req: &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId:    uanActor,
					CachedData: true,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetLastScreenerAttemptForActor(gomock.Any(), uanActor).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: nil,
		},
		{
			name: "error while fetching cached attempt",
			args: args{
				req: &screenerPb.GetScreenerAttemptsByActorIdRequest{
					ActorId:    uanActor,
					CachedData: true,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetLastScreenerAttemptForActor(gomock.Any(), uanActor).
					Return(nil, epifierrors.ErrInvalidSQL)
			},
			want: &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to get active attempts for actor"),
			},
			wantErr: nil,
		},
		{
			name: "no actor passed",
			args: args{
				req: &screenerPb.GetScreenerAttemptsByActorIdRequest{},
			},
			mocks: func(m *mockedDependencies) {},
			want: &screenerPb.GetScreenerAttemptsByActorIdResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("actor id is empty"),
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			got, err := svc.GetScreenerAttemptsByActorId(getContextWithPlatformValues(commontypes.Platform_ANDROID, "80"), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if !getScreenerAttemptsByActorIdRespComparator(got, tt.want) {
				t.Errorf("GetScreenerAttemptsByActorId() got = %v\n, want %v", got, tt.want)
			}
		})
	}
}

func TestService_FailScreenerCheck(t *testing.T) {
	type args struct {
		req *screenerPb.FailScreenerCheckRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockedDependencies)
		want    *screenerPb.FailScreenerCheckResponse
		wantErr error
	}{
		{
			name: "successfully failed attempt",
			args: args{
				req: &screenerPb.FailScreenerCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).Return(uanSa, nil)
				m.scaDao.EXPECT().UpdateCheckDetails(gomock.Any(), &screenerPb.ScreenerCheckAttempt{
					ScreenerAttemptId: uanSa.GetId(),
					CheckType:         screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
					CheckResult:       screenerPb.CheckResult_CHECK_RESULT_FAILED,
				}, []screenerPb.ScreenerCheckAttemptFieldMask{
					screenerPb.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
				}).Return(nil)
				m.scaDao.EXPECT().DeleteCheckAttempt(gomock.Any(), uanSa.GetId(), screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE).
					Return(nil)
			},
			want: &screenerPb.FailScreenerCheckResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: nil,
		},
		{
			name: "error while deleting old attempt",
			args: args{
				req: &screenerPb.FailScreenerCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).Return(uanSa, nil)
				m.scaDao.EXPECT().UpdateCheckDetails(gomock.Any(), &screenerPb.ScreenerCheckAttempt{
					ScreenerAttemptId: uanSa.GetId(),
					CheckType:         screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
					CheckResult:       screenerPb.CheckResult_CHECK_RESULT_FAILED,
				}, []screenerPb.ScreenerCheckAttemptFieldMask{
					screenerPb.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
				}).Return(nil)
				m.scaDao.EXPECT().DeleteCheckAttempt(gomock.Any(), uanSa.GetId(), screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE).
					Return(epifierrors.ErrInvalidSQL)
			},
			want: &screenerPb.FailScreenerCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while deleting check entry in db"),
			},
			wantErr: nil,
		},
		{
			name: "error while updating check result",
			args: args{
				req: &screenerPb.FailScreenerCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).Return(uanSa, nil)
				m.scaDao.EXPECT().UpdateCheckDetails(gomock.Any(), &screenerPb.ScreenerCheckAttempt{
					ScreenerAttemptId: uanSa.GetId(),
					CheckType:         screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
					CheckResult:       screenerPb.CheckResult_CHECK_RESULT_FAILED,
				}, []screenerPb.ScreenerCheckAttemptFieldMask{
					screenerPb.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
				}).Return(epifierrors.ErrInvalidSQL)
			},
			want: &screenerPb.FailScreenerCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while updating check attempt"),
			},
			wantErr: nil,
		},
		{
			name: "no active screener attempt found",
			args: args{
				req: &screenerPb.FailScreenerCheckRequest{
					ActorId:   uanActor,
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {
				m.saDao.EXPECT().GetActiveAttemptForActor(gomock.Any(), uanActor).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &screenerPb.FailScreenerCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to get active attempts for actor"),
			},
			wantErr: nil,
		},
		{
			name: "required params not passed",
			args: args{
				req: &screenerPb.FailScreenerCheckRequest{
					ActorId:   "",
					CheckType: screenerPb.CheckType_CHECK_TYPE_UAN_PRESENCE,
				},
			},
			mocks: func(m *mockedDependencies) {},
			want: &screenerPb.FailScreenerCheckResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("required arguments not passed"),
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, mocks := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mocks)
			}
			got, err := svc.FailScreenerCheck(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FailScreenerCheck() got = %v, want %v", got, tt.want)
			}
		})
	}
}
