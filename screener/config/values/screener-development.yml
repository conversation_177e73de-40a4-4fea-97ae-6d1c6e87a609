Application:
  Environment: "development"
  Name: "screener"

EpifiDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

ConnectedAccountsConfig:
  SqsPublisher:
    QueueName: "connected-account-enquiry-queue"

EnquiryConsumerSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  MaxMessages: 10
  PollingDuration: 20
  QueueName: "connected-account-enquiry-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 16
      TimeUnit: "Second"

FetchSMSParserDataConsumerSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "screener-sms-parser-data-fetch-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 5
      TimeUnit: "Second"

SMSParserConfig:
  SqsPublisher:
    QueueName: "screener-sms-parser-data-fetch-queue"