package stageproc

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/bankcust"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
)

type NRUpdateCustomerDetailsStage struct {
	userClient     usersPb.UsersClient
	bankCustClient bankCustPb.BankCustomerServiceClient
	onboardingDao  dao.OnboardingDao
	empClient      employmentPb.EmploymentClient
	userProc       helper.UserProcessor
	vkycClient     vkyc.VKYCClient
	passportHelper helper.PassportHelper
}

func NewNRUpdateCustomerDetailsStage(userClient usersPb.UsersClient, bankCustClient bankCustPb.BankCustomerServiceClient, onboardingDao dao.OnboardingDao,
	empClient employmentPb.EmploymentClient, userProc helper.UserProcessor, vkycClient vkyc.VKYCClient,
	passportHelper helper.PassportHelper) *NRUpdateCustomerDetailsStage {
	return &NRUpdateCustomerDetailsStage{
		userClient:     userClient,
		bankCustClient: bankCustClient,
		onboardingDao:  onboardingDao,
		empClient:      empClient,
		userProc:       userProc,
		vkycClient:     vkycClient,
		passportHelper: passportHelper,
	}
}

func (s *NRUpdateCustomerDetailsStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onbDetails = req.GetOnb()
	)
	logger.Info(ctx, "update customer details for NR user")
	userResp, err := s.userClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_Id{
			Id: onbDetails.GetUserId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to get user from user service", zap.Error(rpcErr))
		return nil, rpcErr
	}

	passportData, err := s.passportHelper.FetchPassport(ctx, onbDetails.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to get passport data", zap.Error(err))
		return nil, err
	}
	usr := userResp.GetUser()
	userFieldMasks := []usersPb.UserFieldMask{
		usersPb.UserFieldMask_KYC_NAME,
		usersPb.UserFieldMask_ADDRESSES,
		usersPb.UserFieldMask_KYC_GENDER,
	}

	usr.Profile.KycName = passportData.GetName()
	usr.Profile.KycGender = passportData.GetGender()
	if usr.GetProfile().GetAddresses() == nil {
		usr.Profile.Addresses = map[string]*postaladdress.PostalAddress{}
	}
	// we're allowed to only persist pin code of the user's addresses
	if passportData.GetAddress() != nil {
		permanentAddress := &postaladdress.PostalAddress{
			PostalCode: passportData.GetAddress().GetPostalCode(),
		}
		usr.Profile.Addresses[types.AddressType_PERMANENT.String()] = permanentAddress
	}

	if usr.GetProfile().GetDateOfBirth() == nil {
		// Use DOB from passport to update in profile as user may have opted form 60
		logger.Info(ctx, "updating DOB from passport")
		userFieldMasks = append(userFieldMasks, usersPb.UserFieldMask_DOB, usersPb.UserFieldMask_DATA_VERIFICATION_DETAILS)
		usr.Profile.DateOfBirth = types.GetBeDate(passportData.GetDateOfBirth())
		usr.DataVerificationDetails = &usersPb.DataVerificationDetails{
			DataVerificationDetails: []*usersPb.DataVerificationDetail{
				{
					DataType: usersPb.DataType_DATA_TYPE_DOB,
					DataValue: &usersPb.DataVerificationDetail_DOB{
						DOB: types.GetBeDate(passportData.GetDateOfBirth()),
					},
					VerificationEntity: commonvgpb.Vendor_IN_HOUSE,
					VerificationMethod: usersPb.VerificationMethod_VERIFICATION_METHOD_NON_RESIDENT_KYC_CROSS_VALIDATION,
					VerifiedTime:       timestamppb.Now(),
				},
			},
		}
	}

	resp, err := s.userClient.UpdateUser(ctx, &usersPb.UpdateUserRequest{
		User: &usersPb.User{
			Id:      usr.GetId(),
			Profile: usr.GetProfile(),
		},
		UpdateMask: userFieldMasks,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "failed to update user kyc level in user", zap.Error(rpcErr))
		return nil, rpcErr
	}

	// Update kyc level
	if err = s.updateKycLevel(ctx, onbDetails, kyc.KYCLevel_MIN_KYC, bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING); err != nil {
		return nil, err
	}

	return nil, NoActionError
}

// nolint
func (s *NRUpdateCustomerDetailsStage) updateKycLevel(ctx context.Context, onb *onbPb.OnboardingDetails, kycLevelNew kyc.KYCLevel, kycLevelUpdateFlow bankCustPb.KycLevelUpdateFlow) error {
	if kycLevelNew == kyc.KYCLevel_UNSPECIFIED {
		return nil
	}
	// Get user
	bankCustRes, errResp := s.bankCustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: onb.GetVendor(),
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
			ActorId: onb.GetActorId(),
		},
	})
	if err := epifigrpc.RPCError(bankCustRes, errResp); err != nil && !rpc.StatusFromError(err).IsRecordNotFound() {
		logger.Error(ctx, "error while fetching bank customer", zap.Error(err))
		return err
	}
	// update kyc level in bank customer service
	originalKycLevel := bankCustRes.GetBankCustomer().GetDedupeInfo().GetOriginalKycLevelWithVendor()
	var kycLevel kyc.KYCLevel
	switch originalKycLevel {
	// 1. Dedupe customer, full kyc - full account
	case kyc.KYCLevel_FULL_KYC:
		kycLevel = kyc.KYCLevel_FULL_KYC
	// 2. Dedupe customer, min kyc - kyc min/full
	// 3. If not dedupe customer -  kyc min/full
	case kyc.KYCLevel_MIN_KYC, kyc.KYCLevel_UNSPECIFIED:
		switch kycLevelNew {
		case kyc.KYCLevel_FULL_KYC:
			kycLevel = kyc.KYCLevel_FULL_KYC
		case kyc.KYCLevel_MIN_KYC:
			kycLevel = kyc.KYCLevel_MIN_KYC
		default:
			logger.Error(ctx, "kyc level unknown, from user kyc record", zap.String(logger.USER_ID, onb.GetUserId()))
			return fmt.Errorf("kyc level unknown, from user kyc record %v", zap.String(logger.USER_ID, onb.GetUserId()))
		}
	default:
		logger.Error(ctx, fmt.Sprintf("unknown original kyc level with vendor %v", originalKycLevel), zap.String(logger.USER_ID, onb.GetUserId()))
		return fmt.Errorf("invalid original kyc level: %v", originalKycLevel)
	}
	if kycLevel != kyc.KYCLevel_FULL_KYC {
		// Sync Updates to customer if missed by VKYC consumer. This will ensure incorrect KYC level is not present for VKYC users.
		res, errResp := s.vkycClient.GetVKYCSummary(ctx, &vkyc.GetVKYCSummaryRequest{
			ActorId: onb.GetActorId(),
		})
		if err := epifigrpc.RPCError(res, errResp); err != nil && !res.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error in fetching vkyc summary", zap.Error(err))
			return err
		}
		if res.GetVkycRecord().GetVkycSummary().GetStatus() == vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED {
			kycLevel = kyc.KYCLevel_FULL_KYC
		}
	}
	// update kyc level of user in db
	resp, err := s.bankCustClient.UpdateKycLevel(ctx, &bankcust.UpdateKycLevelRequest{
		ActorId:            onb.GetActorId(),
		Vendor:             onb.GetVendor(),
		KycLevel:           kycLevel,
		KycLevelUpdateFlow: kycLevelUpdateFlow,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to update user kyc level in bank customer", zap.Error(err), zap.String(logger.USER_ID, onb.GetUserId()))
		return err
	}
	kycLevelOld := onb.GetStageMetadata().GetKycMetadata().GetLastUpdatedKycLevel()
	// to prevent unnecessary db updates for stage metadata
	if kycLevel == kycLevelOld {
		return nil
	}
	// update kyclevel in onboarding service
	if onb.GetStageMetadata() == nil {
		onb.StageMetadata = &onbPb.StageMetadata{
			KycMetadata: &onbPb.KYCMetadata{
				LastUpdatedKycLevel: kycLevel,
			},
		}
	} else if onb.GetStageMetadata().GetKycMetadata() == nil {
		onb.StageMetadata.KycMetadata = &onbPb.KYCMetadata{
			LastUpdatedKycLevel: kycLevel,
		}
	} else {
		onb.StageMetadata.KycMetadata.LastUpdatedKycLevel = kycLevel
	}
	if err := s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	}, onb); err != nil {
		return err
	}

	logger.Info(ctx, fmt.Sprintf("kyc level updated: %v", kycLevel))
	return nil
}
