package stageproc

import (
	"context"
	"strconv"
	"testing"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"

	consentPb "github.com/epifi/gamma/api/consent"
	mockConsent "github.com/epifi/gamma/api/consent/mocks"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mockEvents "github.com/epifi/gamma/user/events/mocks"
	mockDao "github.com/epifi/gamma/user/onboarding/dao/mocks"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	onbHelperMocks "github.com/epifi/gamma/user/onboarding/helper/mocks"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

func TestSaIntroConsentStage_StageProcessor(t *testing.T) {
	minimalOnbDetails := &onbPb.OnboardingDetails{
		ActorId:      "actor-id",
		OnboardingId: "onboardingId",
		StageDetails: &onbPb.StageDetails{
			StageMapping: map[string]*onbPb.StageInfo{
				onbPb.OnboardingStage_TNC_CONSENT.String(): {
					State: onbPb.OnboardingState_SUCCESS,
				},
				onbPb.OnboardingStage_INTENT_SELECTION.String(): {
					State: onbPb.OnboardingState_SUCCESS,
				},
			},
		},
		CurrentOnboardingStage: onbPb.OnboardingStage_SAVINGS_INTRO_CONSENT,
	}
	intentSelectionDL := &dlPb.Deeplink{
		Screen: dlPb.Screen_ONBOARDING_INTENT_SELECTION,
	}
	featureEnabledAppVersion := context.WithValue(
		context.WithValue(context.Background(), epificontext.CtxAppVersionCodeKey, strconv.Itoa(10001)), epificontext.CtxAppPlatformKey, "ANDROID")
	type mocks struct {
		consentClient *mockConsent.MockConsentClient
		onbDao        *mockDao.MockOnboardingDao
		eventLogger   *mockEvents.MockEventLogger
		dlHelper      *onbHelperMocks.MockDeeplinkHelper
	}
	type args struct {
		ctx context.Context
		req *StageProcessorRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(m *mocks)
		want    *StageProcessorResponse
		wantErr error
	}{
		{
			name: "feature enable, need consent",
			args: args{
				ctx: featureEnabledAppVersion,
				req: &StageProcessorRequest{
					Onb: minimalOnbDetails,
				},
			},
			mocks: func(m *mocks) {
				m.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consentPb.CheckConsentRequirementRequest{
					ActorId: minimalOnbDetails.GetActorId(),
					ConsentTypes: []consentPb.ConsentType{
						consentPb.ConsentType_FED_TNC,
					},
					Owner: common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.CheckConsentRequirementResponse{
					IsConsentRequired: true,
				}, nil)
				m.eventLogger.EXPECT().LogLoadedSAIntroScreenV1Server(gomock.Any(), minimalOnbDetails.GetActorId())
				m.dlHelper.EXPECT().GetIntentSelectionDL(gomock.Any(), minimalOnbDetails.GetActorId(), onbPb.IntentSelectionEntryPoint_INTENT_SELECTION_ENTRY_POINT_SA_INTRO_SCREEN.String()).Return(intentSelectionDL, nil)
			},
			want: &StageProcessorResponse{
				NextAction: getSaIntroScreen(intentSelectionDL),
			},
			wantErr: nil,
		},
		{
			name: "feature enable, consent already collected",
			args: args{
				ctx: featureEnabledAppVersion,
				req: &StageProcessorRequest{
					Onb: minimalOnbDetails,
				},
			},
			mocks: func(m *mocks) {
				m.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consentPb.CheckConsentRequirementRequest{
					ActorId: minimalOnbDetails.GetActorId(),
					ConsentTypes: []consentPb.ConsentType{
						consentPb.ConsentType_FED_TNC,
					},
					Owner: common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.CheckConsentRequirementResponse{
					IsConsentRequired: false,
				}, nil)
			},
			want:    nil,
			wantErr: NoActionError,
		},
		{
			name: "feature disabled, consent not collected",
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: minimalOnbDetails,
				},
			},
			mocks: func(m *mocks) {
				m.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consentPb.CheckConsentRequirementRequest{
					ActorId: minimalOnbDetails.GetActorId(),
					ConsentTypes: []consentPb.ConsentType{
						consentPb.ConsentType_FED_TNC,
						consentPb.ConsentType_FI_PRIVACY_POLICY,
						consentPb.ConsentType_FI_TNC,
						consentPb.ConsentType_FI_WEALTH_TNC,
					},
					Owner: common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.CheckConsentRequirementResponse{
					IsConsentRequired: true,
				}, nil)
				m.onbDao.EXPECT().UpdateStatus(gomock.Any(), minimalOnbDetails.GetOnboardingId(),
					onbPb.OnboardingStage_TNC_CONSENT, onbPb.OnboardingState_RESET).Return(minimalOnbDetails, nil)
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewActionToGetNextAction(),
			},
			wantErr: nil,
		},
		{
			name: "feature disabled, consent already collected",
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: minimalOnbDetails,
				},
			},
			mocks: func(m *mocks) {
				m.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consentPb.CheckConsentRequirementRequest{
					ActorId: minimalOnbDetails.GetActorId(),
					ConsentTypes: []consentPb.ConsentType{
						consentPb.ConsentType_FED_TNC,
						consentPb.ConsentType_FI_PRIVACY_POLICY,
						consentPb.ConsentType_FI_TNC,
						consentPb.ConsentType_FI_WEALTH_TNC,
					},
					Owner: common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.CheckConsentRequirementResponse{
					IsConsentRequired: true,
				}, nil)
				m.onbDao.EXPECT().UpdateStatus(gomock.Any(), minimalOnbDetails.GetOnboardingId(),
					onbPb.OnboardingStage_TNC_CONSENT, onbPb.OnboardingState_RESET).Return(minimalOnbDetails, nil)
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewActionToGetNextAction(),
			},
			wantErr: nil,
		},
		{
			name: "feature disabled, consent already collected",
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: minimalOnbDetails,
				},
			},
			mocks: func(m *mocks) {
				m.consentClient.EXPECT().CheckConsentRequirement(gomock.Any(), &consentPb.CheckConsentRequirementRequest{
					ActorId: minimalOnbDetails.GetActorId(),
					ConsentTypes: []consentPb.ConsentType{
						consentPb.ConsentType_FED_TNC,
						consentPb.ConsentType_FI_PRIVACY_POLICY,
						consentPb.ConsentType_FI_TNC,
						consentPb.ConsentType_FI_WEALTH_TNC,
					},
					Owner: common.Owner_OWNER_EPIFI_TECH,
				}).Return(&consentPb.CheckConsentRequirementResponse{
					IsConsentRequired: false,
				}, nil)
			},
			want:    nil,
			wantErr: NoActionError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			consentClient := mockConsent.NewMockConsentClient(ctr)
			onbDao := mockDao.NewMockOnboardingDao(ctr)
			eventLogger := mockEvents.NewMockEventLogger(ctr)
			dlHelper := onbHelperMocks.NewMockDeeplinkHelper(ctr)
			if tt.mocks != nil {
				tt.mocks(&mocks{
					consentClient: consentClient,
					onbDao:        onbDao,
					eventLogger:   eventLogger,
					dlHelper:      dlHelper,
				})
			}
			s := &SaIntroConsentStage{
				conf:          ts.GenConf.Onboarding(),
				consentClient: consentClient,
				onbDao:        onbDao,
				eventLogger:   eventLogger,
				dlHelper:      dlHelper,
			}
			got, err := s.StageProcessor(tt.args.ctx, tt.args.req)
			assert.Equalf(t, tt.wantErr, err, "StageProcessor(%v, %v)", tt.args.ctx, tt.args.req)
			assert.Equalf(t, tt.want, got, "StageProcessor(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}
