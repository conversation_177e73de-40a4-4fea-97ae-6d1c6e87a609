package event

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/user/config"
	"github.com/epifi/gamma/user/test"
)

var (
	conf *config.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// nolint
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, _, teardown = test.InitTestServer()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
