package celestial_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifierrors"
	rpNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"
)

func TestProcessor_GetWorkflowByClientRequestId(t *testing.T) {
	processor, md, assertMocks := newProcessorWithMocks(t)
	defer assertMocks()

	type args struct {
		ctx             context.Context
		clientRequestId string
		client          workflowPb.Client
	}
	type mockGetWorkflowStatus struct {
		enable bool
		req    *celestialPb.GetWorkflowStatusRequest
		want   *celestialPb.GetWorkflowStatusResponse
		err    error
	}
	tests := []struct {
		name                  string
		args                  args
		mockGetWorkflowStatus mockGetWorkflowStatus
		want                  *celestialPb.WorkflowRequest
		status                *rpc.Status
	}{
		{
			name: "should return workflow successfully",
			args: args{
				clientRequestId: "client_req_id",
				client:          workflowPb.Client_USER_APP,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_USER_APP,
						},
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Id: "workflow_req_id",
					},
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			want: &celestialPb.WorkflowRequest{
				Id: "workflow_req_id",
			},
			status: rpc.StatusOk(),
		},
		{
			name: "failed to get workflow, due to some rpc issue",
			args: args{
				clientRequestId: "client_req_id",
				client:          workflowPb.Client_USER_APP,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_USER_APP,
						},
					},
				},
				want: nil,
				err:  errors.New("server down"),
			},
			want:   nil,
			status: rpc.StatusInternal(),
		},
		{
			name: "failed as workflow not found for given client req id",
			args: args{
				clientRequestId: "client_req_id",
				client:          workflowPb.Client_USER_APP,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_USER_APP,
						},
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			want:   nil,
			status: rpc.StatusRecordNotFound(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetWorkflowStatus.enable {
				md.celestialClient.EXPECT().
					GetWorkflowStatus(gomock.Any(), tt.mockGetWorkflowStatus.req).
					Return(tt.mockGetWorkflowStatus.want, tt.mockGetWorkflowStatus.err)
			}
			got, err := processor.GetWorkflowByClientRequestId(tt.args.ctx, tt.args.clientRequestId, tt.args.client)
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetWorkflowByClientRequestId() got = %v, want %v", got, tt.want)
			}
			if !proto.Equal(rpc.StatusFromError(err), tt.status) {
				t.Errorf("GetWorkflowByClientRequestId() got = %v, want %v", rpc.StatusFromError(err), tt.status)
			}
		})
	}
}

func TestProcessor_SignalWorkflowByClientReqId(t *testing.T) {
	processor, md, assertMocks := newProcessorWithMocks(t)
	defer assertMocks()

	type mockCelestialClientSignalWorkflow struct {
		enable bool
		req    *celestialPb.SignalWorkflowRequest
		msgID  string
		want   *celestialPb.SignalWorkflowResponse
		err    error
	}
	type args struct {
		ctx         context.Context
		clientReqID string
		signalID    string
		client      workflow.Client
		payload     proto.Message
	}
	tests := []struct {
		name                     string
		mockSignalWorkflowClient mockCelestialClientSignalWorkflow
		args                     args
		wantErr                  bool
	}{
		{
			name: "successfully signaled workflow",
			mockSignalWorkflowClient: mockCelestialClientSignalWorkflow{
				enable: true,
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_USER_APP,
						},
					},
					SignalId: string(rpNs.CreateRecurringPaymentAuthSignal),
				},
				want: &celestialPb.SignalWorkflowResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			args: args{
				clientReqID: "client_req_id",
				signalID:    string(rpNs.CreateRecurringPaymentAuthSignal),
				client:      workflowPb.Client_USER_APP,
			},
			wantErr: false,
		},
		{
			name: "failed to publish signal workflow requets",
			mockSignalWorkflowClient: mockCelestialClientSignalWorkflow{
				enable: true,
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_USER_APP,
						},
					},
					SignalId: string(rpNs.CreateRecurringPaymentAuthSignal),
				},
				want: &celestialPb.SignalWorkflowResponse{
					Status: rpc.StatusInternal(),
				},
				err: errors.New("SQS issue"),
			},
			args: args{
				clientReqID: "client_req_id",
				signalID:    string(rpNs.CreateRecurringPaymentAuthSignal),
				client:      workflowPb.Client_USER_APP,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockSignalWorkflowClient.enable {
				md.celestialClient.EXPECT().
					SignalWorkflow(gomock.Any(), gomock.AssignableToTypeOf(tt.mockSignalWorkflowClient.req)).
					Return(tt.mockSignalWorkflowClient.want, tt.mockSignalWorkflowClient.err)
			}
			if err := processor.SignalWorkflowByClientReqId(tt.args.ctx, tt.args.clientReqID, tt.args.signalID, tt.args.client, tt.args.payload); (err != nil) != tt.wantErr {
				t.Errorf("SignalWorkflow() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestProcessor_SignalWorkflowByWorkflowReqId(t *testing.T) {
	processor, md, assertMocks := newProcessorWithMocks(t)
	defer assertMocks()

	type mockCelestialClientSignalWorkflow struct {
		enable bool
		req    *celestialPb.SignalWorkflowRequest
		msgID  string
		want   *celestialPb.SignalWorkflowResponse
		err    error
	}

	type args struct {
		ctx           context.Context
		workflowReqID string
		signalID      string
		client        workflow.Client
		payload       proto.Message
	}
	tests := []struct {
		name                     string
		mockSignalWorkflowClient *mockCelestialClientSignalWorkflow
		args                     args
		wantErr                  bool
	}{
		{
			name: "successfully signaled workflow",
			mockSignalWorkflowClient: &mockCelestialClientSignalWorkflow{
				enable: true,
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_USER_APP,
						},
					},
					SignalId: string(rpNs.CreateRecurringPaymentAuthSignal),
				},
				want: &celestialPb.SignalWorkflowResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			args: args{
				workflowReqID: "workflow_req_id",
				signalID:      string(rpNs.CreateRecurringPaymentAuthSignal),
				client:        workflowPb.Client_USER_APP,
			},
			wantErr: false,
		},
		{
			name: "failed to publish signal workflow requets",
			mockSignalWorkflowClient: &mockCelestialClientSignalWorkflow{
				enable: true,
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_USER_APP,
						},
					},
					SignalId: string(rpNs.CreateRecurringPaymentAuthSignal),
				},
				want: &celestialPb.SignalWorkflowResponse{
					Status: rpc.StatusInternal(),
				},
				err: errors.New("SQS issue"),
			},
			args: args{
				workflowReqID: "workflow_req_id",
				signalID:      string(rpNs.CreateRecurringPaymentAuthSignal),
				client:        workflowPb.Client_USER_APP,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockSignalWorkflowClient.enable {
				md.celestialClient.EXPECT().
					SignalWorkflow(gomock.Any(), gomock.AssignableToTypeOf(tt.mockSignalWorkflowClient.req)).
					Return(tt.mockSignalWorkflowClient.want, tt.mockSignalWorkflowClient.err)
			}
			if err := processor.SignalWorkflowByWorkflowReqId(tt.args.ctx, tt.args.workflowReqID, tt.args.signalID, tt.args.payload); (err != nil) != tt.wantErr {
				t.Errorf("SignalWorkflow() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCelestialProcessor_InitiateWorkflow(t *testing.T) {
	payload, _ := protojson.Marshal(nil)
	type mockInitiateWorkflow struct {
		enable bool
		req    *celestialPb.InitiateWorkflowRequest
		res    *celestialPb.InitiateWorkflowResponse
		err    error
	}
	type args struct {
		ctx          context.Context
		clientReqId  *celestialPb.ClientReqId
		actorId      string
		payload      proto.Message
		workflowType workflowPb.Type
		version      workflowPb.Version
	}
	tests := []struct {
		name                 string
		args                 args
		mockInitiateWorkflow mockInitiateWorkflow
		want                 error
		wantErr              bool
	}{
		{
			name: "successfully initiated workflow",
			args: args{
				ctx: context.Background(),
				clientReqId: &celestialPb.ClientReqId{
					Id:     "client-req-id",
					Client: workflowPb.Client_USER_APP,
				},
				actorId:      "actorId",
				payload:      nil,
				workflowType: workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
				version:      workflowPb.Version_V0,
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				req: &celestialPb.InitiateWorkflowRequest{
					ActorId: "actorId",
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
					Payload: payload,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_USER_APP,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
				res: &celestialPb.InitiateWorkflowResponse{
					Status:            rpc.StatusOk(),
					WorkflowRequestId: "workflow-id",
				},
				err: nil,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "error while initiating workflow",
			args: args{
				ctx: context.Background(),
				clientReqId: &celestialPb.ClientReqId{
					Id:     "client-req-id",
					Client: workflowPb.Client_USER_APP,
				},
				actorId:      "actorId",
				payload:      nil,
				workflowType: workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
				version:      workflowPb.Version_V0,
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				req: &celestialPb.InitiateWorkflowRequest{
					ActorId: "actorId",
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
					Payload: payload,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_USER_APP,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
				res: nil,
				err: errors.New("error"),
			},
			want:    errors.New("error"),
			wantErr: true,
		},
		{
			name: "error while initiating workflow due to non-ok code",
			args: args{
				ctx: context.Background(),
				clientReqId: &celestialPb.ClientReqId{
					Id:     "client-req-id",
					Client: workflowPb.Client_USER_APP,
				},
				actorId:      "actorId",
				payload:      nil,
				workflowType: workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
				version:      workflowPb.Version_V0,
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				req: &celestialPb.InitiateWorkflowRequest{
					ActorId: "actorId",
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
					Payload: payload,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_USER_APP,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
				res: &celestialPb.InitiateWorkflowResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want:    errors.New("error"),
			wantErr: true,
		},
		{
			name: "error while initiating workflow due to already existing workflow",
			args: args{
				ctx: context.Background(),
				clientReqId: &celestialPb.ClientReqId{
					Id:     "client-req-id",
					Client: workflowPb.Client_USER_APP,
				},
				actorId:      "actorId",
				payload:      nil,
				workflowType: workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
				version:      workflowPb.Version_V0,
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				req: &celestialPb.InitiateWorkflowRequest{
					ActorId: "actorId",
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER,
					Payload: payload,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client-req-id",
						Client: workflowPb.Client_USER_APP,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
				res: &celestialPb.InitiateWorkflowResponse{
					Status: rpc.StatusAlreadyExists(),
				},
				err: nil,
			},
			want:    epifierrors.ErrAlreadyExists,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor, md, assertMocks := newProcessorWithMocks(t)
			if tt.mockInitiateWorkflow.enable {
				md.celestialClient.EXPECT().InitiateWorkflow(gomock.Any(), tt.mockInitiateWorkflow.req).
					Return(tt.mockInitiateWorkflow.res, tt.mockInitiateWorkflow.err)
			}
			if err := processor.InitiateWorkflow(tt.args.ctx, tt.args.clientReqId, tt.args.actorId, tt.args.payload, tt.args.workflowType, tt.args.version); (err != nil) != tt.wantErr {
				t.Errorf("InitiateWorkflow() error = %v, wantErr %v", err, tt.wantErr)
			}
			assertMocks()
		})
	}
}
