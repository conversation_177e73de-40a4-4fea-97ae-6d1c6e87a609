package notification

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	commsPb "github.com/epifi/gamma/api/comms"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	depositMocks "github.com/epifi/gamma/api/deposit/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/order/payment/notification"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	mocks3 "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	"github.com/epifi/gamma/api/savings/mocks"
	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"
	mocks2 "github.com/epifi/gamma/order/dao/mocks"
	userMocks "github.com/epifi/gamma/order/internal/user/mocks"
)

// Payment protocol: UPI
// Order Workflow: NO_OP
// fromPiType: PaymentInstrumentType_ACC
// toPiType: PaymentInstrumentType_UPI
// user: PAYEE
// possible scenario: use externally linked fi account (gPay) to pay a fi vpa via UPI
func TestService_notifyUser__PaymentProtocol_UPI__Workflow_NO_OP__ACC_VPA__PAYEE(t *testing.T) {
	ctrl := gomock.NewController(t)
	commsClient := commsMocks.NewMockCommsClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	timelineClient := timelineMocks.NewMockTimelineServiceClient(ctrl)
	depositClient := depositMocks.NewMockDepositClient(ctrl)
	savingsClient := mocks.NewMockSavingsClient(ctrl)
	txnNotificationMapDao := mocks2.NewMockTransactionNotificationMapDao(ctrl)
	accountPiRelationClient := mocks3.NewMockAccountPIRelationClient(ctrl)
	mockUserProcessor := userMocks.NewMockUserProcessor(ctrl)
	mockUserProcessor.EXPECT().GetUserGroupsByActorID(context.Background(), gomock.Any()).
		Return([]commontypes.UserGroup{commontypes.UserGroup_INTERNAL, commontypes.UserGroup_FNF}, nil).AnyTimes()
	notificationService := NewService(commsClient, actorClient, piClient, timelineClient, conf, depositClient, savingsClient, accountPiRelationClient, txnNotificationMapDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	var randomMessageId = "random-message-id"

	defer func() {
		ctrl.Finish()
		notificationService = nil
	}()

	currentTime := timestamppb.Now()
	debitedAtTime := timestamppb.Now()
	creditedAtTime := timestamppb.Now()
	type args struct {
		ctx        context.Context
		txnDetails *transactionDetails
	}
	type result struct {
		sentSMSes         []commsPb.SmsType
		sentNotifications []string
		err               error
	}

	type mockGetByTransactionIdForSms struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockGetByTransactionIdForNotification struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockUpdateForSms struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}
	type mockUpdateForNotification struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}

	tests := []struct {
		name                                  string
		args                                  args
		result                                result
		commsExpectSms                        *commsPb.SendMessageResponse
		commsExpectNotification               *commsPb.SendMessageBatchResponse
		mockGetByTransactionIdForSms          *mockGetByTransactionIdForSms
		mockGetByTransactionIdForNotification *mockGetByTransactionIdForNotification
		mockUpdateForSms                      *mockUpdateForSms
		mockUpdateForNotification             *mockUpdateForNotification
		wantErr                               bool
	}{
		{
			name: "Acc to VPA UPI order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_SMS_ID,
				},
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_PUSH_NOTIF_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_GENERIC_PI_CREDIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.CreditUPI().Title,
				},
				err: nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
		},
		{
			name: "Acc to VPA UPI order in PAID state but no notifications sent because they were already sent",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
			},
			result: result{
				err: nil,
			},
		},
		{
			name: "Acc to VPA UPI order in PAID state and comms call for SMS failed as record not found",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			result: result{
				err: fmt.Errorf("failed to send SMS for entity id : permanent failure"),
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.commsExpectSms != nil {
				commsClient.EXPECT().
					SendMessage(context.Background(), gomock.All()).
					Return(tt.commsExpectSms, nil)
			}
			if tt.commsExpectNotification != nil {
				commsClient.EXPECT().
					SendMessageBatch(context.Background(), gomock.All()).
					Return(tt.commsExpectNotification, nil)
			}

			if tt.mockGetByTransactionIdForSms != nil {
				first := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForSms.txnId).
					Return(&tt.mockGetByTransactionIdForSms.res, tt.mockGetByTransactionIdForSms.err)

				if tt.mockGetByTransactionIdForNotification != nil {
					second := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
						Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
					if tt.mockGetByTransactionIdForSms != nil {
						gomock.InOrder(
							first,
							second,
						)
					}
				}
			} else if tt.mockGetByTransactionIdForNotification != nil {
				txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
					Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
			}

			if tt.mockUpdateForSms != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForSms.txnNotifMap, tt.mockUpdateForSms.updateMask).
					Return(tt.mockUpdateForSms.err)
			}
			if tt.mockUpdateForNotification != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForNotification.txnNotifMap, tt.mockUpdateForNotification.updateMask).
					Return(tt.mockUpdateForNotification.err)
			}

			payeeSmses, payeeNotifications, err := notificationService.notifyUser(tt.args.ctx, tt.args.txnDetails, "", PAYEE, nil, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("notifyUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			if !reflect.DeepEqual(payeeSmses, tt.result.sentSMSes) {
				t.Errorf("notifyUser() SMSes got: %v, want: %v", payeeSmses, tt.result.sentSMSes)
			}
			if !assertNotifications(payeeNotifications, tt.result.sentNotifications) {
				t.Errorf("notifyUser() notifications got: %v, want: %v", payeeNotifications, tt.result.sentNotifications)
			}

		})
	}
}

// Payment protocol: UPI
// Order Workflow: NO_OP
// fromPiType: PaymentInstrumentType_ACC
// toPiType: PaymentInstrumentType_ACC
// user: PAYEE
// possible scenario: use externally linked fi account (gPay) to pay a fi account via UPI
func TestService_notifyUser__PaymentProtocol_UPI_OrderWorkflow_NO_OP__ACC_ACC__PAYEE(t *testing.T) {
	ctrl := gomock.NewController(t)
	commsClient := commsMocks.NewMockCommsClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	timelineClient := timelineMocks.NewMockTimelineServiceClient(ctrl)
	depositClient := depositMocks.NewMockDepositClient(ctrl)
	savingsClient := mocks.NewMockSavingsClient(ctrl)
	txnNotificationMapDao := mocks2.NewMockTransactionNotificationMapDao(ctrl)
	accountPiRelationClient := mocks3.NewMockAccountPIRelationClient(ctrl)
	mockUserProcessor := userMocks.NewMockUserProcessor(ctrl)
	mockUserProcessor.EXPECT().GetUserGroupsByActorID(context.Background(), gomock.Any()).
		Return([]commontypes.UserGroup{commontypes.UserGroup_INTERNAL, commontypes.UserGroup_FNF}, nil).AnyTimes()
	notificationService := NewService(commsClient, actorClient, piClient, timelineClient, conf, depositClient, savingsClient, accountPiRelationClient, txnNotificationMapDao, mockUserProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	var randomMessageId = "random-message-id"

	defer func() {
		ctrl.Finish()
		notificationService = nil
	}()

	currentTime := timestamppb.Now()
	debitedAtTime := timestamppb.Now()
	creditedAtTime := timestamppb.Now()
	type args struct {
		ctx        context.Context
		txnDetails *transactionDetails
	}
	type result struct {
		sentSMSes         []commsPb.SmsType
		sentNotifications []string
		err               error
	}

	type mockGetByTransactionIdForSms struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockGetByTransactionIdForNotification struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockUpdateForSms struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}
	type mockUpdateForNotification struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}

	tests := []struct {
		name                                  string
		args                                  args
		result                                result
		commsExpectSms                        *commsPb.SendMessageResponse
		commsExpectNotification               *commsPb.SendMessageBatchResponse
		mockGetByTransactionIdForSms          *mockGetByTransactionIdForSms
		mockGetByTransactionIdForNotification *mockGetByTransactionIdForNotification
		mockUpdateForSms                      *mockUpdateForSms
		mockUpdateForNotification             *mockUpdateForNotification
		wantErr                               bool
	}{
		{
			name: "Acc to Acc UPI order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_SMS_ID,
				},
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_PUSH_NOTIF_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_GENERIC_PI_CREDIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.CreditUPI().Title,
				},
				err: nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
		},
		{
			name: "Acc to Acc UPI order in PAID state but no notifications sent because they were already sent",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
			},
			result: result{
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.commsExpectSms != nil {
				commsClient.EXPECT().
					SendMessage(context.Background(), gomock.All()).
					Return(tt.commsExpectSms, nil)
			}
			if tt.commsExpectNotification != nil {
				commsClient.EXPECT().
					SendMessageBatch(context.Background(), gomock.All()).
					Return(tt.commsExpectNotification, nil)
			}

			if tt.mockGetByTransactionIdForSms != nil {
				first := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForSms.txnId).
					Return(&tt.mockGetByTransactionIdForSms.res, tt.mockGetByTransactionIdForSms.err)

				if tt.mockGetByTransactionIdForNotification != nil {
					second := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
						Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
					if tt.mockGetByTransactionIdForSms != nil {
						gomock.InOrder(
							first,
							second,
						)
					}
				}
			} else if tt.mockGetByTransactionIdForNotification != nil {
				txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
					Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
			}

			if tt.mockUpdateForSms != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForSms.txnNotifMap, tt.mockUpdateForSms.updateMask).
					Return(tt.mockUpdateForSms.err)
			}
			if tt.mockUpdateForNotification != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForNotification.txnNotifMap, tt.mockUpdateForNotification.updateMask).
					Return(tt.mockUpdateForNotification.err)
			}

			payeeSmses, payeeNotifications, err := notificationService.notifyUser(tt.args.ctx, tt.args.txnDetails, "", PAYEE, nil, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("notifyUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			if !reflect.DeepEqual(payeeSmses, tt.result.sentSMSes) {
				t.Errorf("notifyUser() SMSes got: %v, want: %v", payeeSmses, tt.result.sentSMSes)
			}
			if !assertNotifications(payeeNotifications, tt.result.sentNotifications) {
				t.Errorf("notifyUser() notifications got: %v, want: %v", payeeNotifications, tt.result.sentNotifications)
			}

		})
	}
}

// Payment protocol: UPI
// Order Workflow: NO_OP
// fromPiType: PaymentInstrumentType_GENERIC
// toPiType: PaymentInstrumentType_ACC
// user: PAYEE
func TestService_notifyUser__PaymentProtocol_UPI__OrderWorkflow_NO_OP__GENERIC_ACC__PAYEE(t *testing.T) {
	ctrl := gomock.NewController(t)
	commsClient := commsMocks.NewMockCommsClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	timelineClient := timelineMocks.NewMockTimelineServiceClient(ctrl)
	depositClient := depositMocks.NewMockDepositClient(ctrl)
	savingsClient := mocks.NewMockSavingsClient(ctrl)
	txnNotificationMapDao := mocks2.NewMockTransactionNotificationMapDao(ctrl)
	accountPiRelationClient := mocks3.NewMockAccountPIRelationClient(ctrl)
	mockUserProcessor := userMocks.NewMockUserProcessor(ctrl)
	mockUserProcessor.EXPECT().GetUserGroupsByActorID(context.Background(), gomock.Any()).
		Return([]commontypes.UserGroup{commontypes.UserGroup_INTERNAL, commontypes.UserGroup_FNF}, nil).AnyTimes()
	notificationService := NewService(commsClient, actorClient, piClient, timelineClient, conf, depositClient, savingsClient, accountPiRelationClient, txnNotificationMapDao, mockUserProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	var randomMessageId = "random-message-id"

	defer func() {
		ctrl.Finish()
		notificationService = nil
	}()

	currentTime := timestamppb.Now()
	debitedAtTime := timestamppb.Now()
	creditedAtTime := timestamppb.Now()
	type args struct {
		ctx        context.Context
		txnDetails *transactionDetails
	}
	type result struct {
		sentSMSes         []commsPb.SmsType
		sentNotifications []string
		err               error
	}

	type mockGetByTransactionIdForSms struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockGetByTransactionIdForNotification struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockUpdateForSms struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}
	type mockUpdateForNotification struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}

	tests := []struct {
		name                                  string
		args                                  args
		result                                result
		commsExpectSms                        *commsPb.SendMessageResponse
		commsExpectNotification               *commsPb.SendMessageBatchResponse
		mockGetByTransactionIdForSms          *mockGetByTransactionIdForSms
		mockGetByTransactionIdForNotification *mockGetByTransactionIdForNotification
		mockUpdateForSms                      *mockUpdateForSms
		mockUpdateForNotification             *mockUpdateForNotification
		wantErr                               bool
	}{
		{
			name: "Generic PI to Acc UPI order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_GENERIC,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_SMS_ID,
				},
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_PUSH_NOTIF_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_GENERIC_PI_CREDIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.CreditUPI().Title,
				},
				err: nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
		},
		{
			name: "Generic PI to Acc UPI order in PAID state but no notifications sent because they were already sent",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_GENERIC,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
			},
			result: result{
				err: nil,
			},
		},
		{
			name: "Generic PI to Acc Card txn order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_CARD,
					eventTime:             currentTime,
					debitedAt:             debitedAtTime,
					creditedAt:            creditedAtTime,
					txnLastUpdatedAt:      currentTime,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_GENERIC,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_POS,
					isCardTransaction:     true,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_SMS_ID,
				},
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:     "transaction-1",
					CreditPushNotifId: randomMessageId,
					CreditSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_CREDIT_PUSH_NOTIF_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					CreditSmsId:   randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_GENERIC_PI_CREDIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.CreditUPI().Title,
				},
				err: nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.commsExpectSms != nil {
				commsClient.EXPECT().
					SendMessage(context.Background(), gomock.All()).
					Return(tt.commsExpectSms, nil)
			}
			if tt.commsExpectNotification != nil {
				commsClient.EXPECT().
					SendMessageBatch(context.Background(), gomock.All()).
					Return(tt.commsExpectNotification, nil)
			}

			if tt.mockGetByTransactionIdForSms != nil {
				first := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForSms.txnId).
					Return(&tt.mockGetByTransactionIdForSms.res, tt.mockGetByTransactionIdForSms.err)

				if tt.mockGetByTransactionIdForNotification != nil {
					second := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
						Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
					if tt.mockGetByTransactionIdForSms != nil {
						gomock.InOrder(
							first,
							second,
						)
					}
				}
			} else if tt.mockGetByTransactionIdForNotification != nil {
				txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
					Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
			}

			if tt.mockUpdateForSms != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForSms.txnNotifMap, tt.mockUpdateForSms.updateMask).
					Return(tt.mockUpdateForSms.err)
			}
			if tt.mockUpdateForNotification != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForNotification.txnNotifMap, tt.mockUpdateForNotification.updateMask).
					Return(tt.mockUpdateForNotification.err)
			}

			payeeSmses, payeeNotifications, err := notificationService.notifyUser(tt.args.ctx, tt.args.txnDetails, "", PAYEE, nil, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("notifyUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			if !reflect.DeepEqual(payeeSmses, tt.result.sentSMSes) {
				t.Errorf("notifyUser() SMSes got: %v, want: %v", payeeSmses, tt.result.sentSMSes)
			}
			if !assertNotifications(payeeNotifications, tt.result.sentNotifications) {
				t.Errorf("notifyUser() notifications got: %v, want: %v", payeeNotifications, tt.result.sentNotifications)
			}

		})
	}
}

// Payment protocol: UPI
// Order Workflow: NO_OP
// fromPiType: PaymentInstrumentType_ACC
// toPiType: PaymentInstrumentType_UPI
// user: PAYER
// possible scenario: use externally linked fi account (gPay) to pay a fi vpa via UPI
func TestService_notifyUser__PaymentProtocol_UPI__OrderWorkflow_NO_OP__ACC_VPA__PAYER(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	ctrl := gomock.NewController(t)
	commsClient := commsMocks.NewMockCommsClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	timelineClient := timelineMocks.NewMockTimelineServiceClient(ctrl)
	depositClient := depositMocks.NewMockDepositClient(ctrl)
	savingsClient := mocks.NewMockSavingsClient(ctrl)
	txnNotificationMapDao := mocks2.NewMockTransactionNotificationMapDao(ctrl)
	accountPiRelationClient := mocks3.NewMockAccountPIRelationClient(ctrl)
	mockUserProcessor := userMocks.NewMockUserProcessor(ctrl)
	mockUserProcessor.EXPECT().GetUserGroupsByActorID(context.Background(), gomock.Any()).
		Return([]commontypes.UserGroup{commontypes.UserGroup_INTERNAL, commontypes.UserGroup_FNF}, nil).AnyTimes()
	notificationService := NewService(commsClient, actorClient, piClient, timelineClient, conf, depositClient, savingsClient, accountPiRelationClient, txnNotificationMapDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	var randomMessageId = "random-message-id"

	defer func() {
		ctrl.Finish()
		notificationService = nil
	}()

	timestampFixture := timestamppb.New(time.Now().Add(-1 * time.Hour))
	type args struct {
		ctx        context.Context
		txnDetails *transactionDetails
	}
	type result struct {
		sentSMSes         []commsPb.SmsType
		sentNotifications []string
		err               error
	}

	type mockGetByTransactionIdForSms struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockGetByTransactionIdForNotification struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockUpdateForSms struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}
	type mockUpdateForNotification struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}

	tests := []struct {
		name                                  string
		args                                  args
		result                                result
		commsExpectSms                        *commsPb.SendMessageResponse
		commsExpectNotification               *commsPb.SendMessageBatchResponse
		mockGetByTransactionIdForSms          *mockGetByTransactionIdForSms
		mockGetByTransactionIdForNotification *mockGetByTransactionIdForNotification
		mockUpdateForSms                      *mockUpdateForSms
		mockUpdateForNotification             *mockUpdateForNotification
		wantErr                               bool
	}{
		{
			name: "Acc to VPA UPI order in IN_PAYMENT state and debited at non null",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_IN_PAYMENT,
					protocol:              paymentPb.PaymentProtocol_UPI,
					expireAt:              nil,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_UPI_DEBIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.DebitUPI().Title,
				},
				err: nil,
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitPushNotifId: randomMessageId,
					DebitSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_PUSH_NOTIF_ID,
				},
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to VPA UPI order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_UPI_DEBIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.DebitUPI().Title,
				},
				err: nil,
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitPushNotifId: randomMessageId,
					DebitSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_PUSH_NOTIF_ID,
				},
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to VPA UPI order in PAID state but eventTime within push notification trigger after",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestamppb.Now(),
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_UPI_DEBIT,
				},
				sentNotifications: nil,
				err:               nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to VPA UPI order in PAID state but no notifications sent because they were already sent",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			result: result{
				err: nil,
			},
		},
		{
			name: "Acc to VPA UPI order in IN_PAYMENT state and debited at is null",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_IN_PAYMENT,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             nil,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			result: result{
				err: nil,
			},
		},
		{
			name: "Acc to VPA UPI order in IN_PAYMENT state and debited at non null, but notifications already sent",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_IN_PAYMENT,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             nil,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			result: result{
				err: nil,
			},
		},
		{
			name: "Acc to VPA UPI order in CREATED state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_CREATED,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			result: result{
				err: nil,
			},
		},
		{
			name: "Acc to VPA UPI order in PAYMENT_FAILED state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAYMENT_FAILED,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_UPI,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_FAILED_TRANSACTION,
				},
				sentNotifications: []string{
					notificationService.notificationParams.TransactionFailed().Title,
				},
				err: nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.commsExpectSms != nil {
				commsClient.EXPECT().
					SendMessage(context.Background(), gomock.All()).
					Return(tt.commsExpectSms, nil)
			}
			if tt.commsExpectNotification != nil {
				commsClient.EXPECT().
					SendMessageBatch(context.Background(), gomock.All()).
					Return(tt.commsExpectNotification, nil)
			}

			if tt.mockGetByTransactionIdForSms != nil {
				first := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForSms.txnId).
					Return(&tt.mockGetByTransactionIdForSms.res, tt.mockGetByTransactionIdForSms.err)

				if tt.mockGetByTransactionIdForNotification != nil {
					second := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
						Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
					if tt.mockGetByTransactionIdForSms != nil {
						gomock.InOrder(
							first,
							second,
						)
					}
				}
			} else if tt.mockGetByTransactionIdForNotification != nil {
				txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
					Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
			}

			if tt.mockUpdateForSms != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForSms.txnNotifMap, tt.mockUpdateForSms.updateMask).
					Return(tt.mockUpdateForSms.err)
			}
			if tt.mockUpdateForNotification != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForNotification.txnNotifMap, tt.mockUpdateForNotification.updateMask).
					Return(tt.mockUpdateForNotification.err)
			}

			payeeSmses, payeeNotifications, err := notificationService.notifyUser(tt.args.ctx, tt.args.txnDetails, "", PAYER, nil, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("notifyUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			if !reflect.DeepEqual(payeeSmses, tt.result.sentSMSes) {
				t.Errorf("notifyUser() SMSes got: %v, want: %v", payeeSmses, tt.result.sentSMSes)
			}
			if !assertNotifications(payeeNotifications, tt.result.sentNotifications) {
				t.Errorf("notifyUser() notifications got: %v, want: %v", payeeNotifications, tt.result.sentNotifications)
			}

		})
	}
}

// Payment protocol: UPI
// Order Workflow: NO_OP
// fromPiType: PaymentInstrumentType_ACC
// toPiType: PaymentInstrumentType_ACC
// user: PAYER
// possible scenario: use externally linked fi account (gPay) to pay a fi account via UPI
func TestService_notifyUser__PaymentProtocol_UPI__OrderWorkflow_NO_OP__ACC_ACC__PAYER(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	ctrl := gomock.NewController(t)
	commsClient := commsMocks.NewMockCommsClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	timelineClient := timelineMocks.NewMockTimelineServiceClient(ctrl)
	depositClient := depositMocks.NewMockDepositClient(ctrl)
	savingsClient := mocks.NewMockSavingsClient(ctrl)
	txnNotificationMapDao := mocks2.NewMockTransactionNotificationMapDao(ctrl)
	accountPiRelationClient := mocks3.NewMockAccountPIRelationClient(ctrl)
	mockUserProcessor := userMocks.NewMockUserProcessor(ctrl)
	mockUserProcessor.EXPECT().GetUserGroupsByActorID(context.Background(), gomock.Any()).
		Return([]commontypes.UserGroup{commontypes.UserGroup_INTERNAL, commontypes.UserGroup_FNF}, nil).AnyTimes()
	notificationService := NewService(commsClient, actorClient, piClient, timelineClient, conf, depositClient, savingsClient, accountPiRelationClient, txnNotificationMapDao, mockUserProcessor, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	var randomMessageId = "random-message-id"

	defer func() {
		ctrl.Finish()
		notificationService = nil
	}()

	timestampFixture := timestamppb.New(time.Now().Add(-1 * time.Hour))
	type args struct {
		ctx        context.Context
		txnDetails *transactionDetails
	}
	type result struct {
		sentSMSes         []commsPb.SmsType
		sentNotifications []string
		err               error
	}

	type mockGetByTransactionIdForSms struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockGetByTransactionIdForNotification struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockUpdateForSms struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}
	type mockUpdateForNotification struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}

	tests := []struct {
		name                                  string
		args                                  args
		result                                result
		commsExpectSms                        *commsPb.SendMessageResponse
		commsExpectNotification               *commsPb.SendMessageBatchResponse
		mockGetByTransactionIdForSms          *mockGetByTransactionIdForSms
		mockGetByTransactionIdForNotification *mockGetByTransactionIdForNotification
		mockUpdateForSms                      *mockUpdateForSms
		mockUpdateForNotification             *mockUpdateForNotification
		wantErr                               bool
	}{
		{
			name: "Acc to Acc UPI order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_UPI_DEBIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.DebitUPI().Title,
				},
				err: nil,
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitPushNotifId: randomMessageId,
					DebitSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_PUSH_NOTIF_ID,
				},
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to Acc UPI order in PAID state but eventTime within push notification trigger after",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestamppb.Now(),
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_UPI_DEBIT,
				},
				sentNotifications: nil,
				err:               nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to Acc UPI order in PAID state but no notifications sent because they were already sent",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_BANK_ACCOUNT,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			result: result{
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.commsExpectSms != nil {
				commsClient.EXPECT().
					SendMessage(context.Background(), gomock.All()).
					Return(tt.commsExpectSms, nil)
			}
			if tt.commsExpectNotification != nil {
				commsClient.EXPECT().
					SendMessageBatch(context.Background(), gomock.All()).
					Return(tt.commsExpectNotification, nil)
			}

			if tt.mockGetByTransactionIdForSms != nil {
				first := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForSms.txnId).
					Return(&tt.mockGetByTransactionIdForSms.res, tt.mockGetByTransactionIdForSms.err)

				if tt.mockGetByTransactionIdForNotification != nil {
					second := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
						Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
					if tt.mockGetByTransactionIdForSms != nil {
						gomock.InOrder(
							first,
							second,
						)
					}
				}
			} else if tt.mockGetByTransactionIdForNotification != nil {
				txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
					Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
			}

			if tt.mockUpdateForSms != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForSms.txnNotifMap, tt.mockUpdateForSms.updateMask).
					Return(tt.mockUpdateForSms.err)
			}
			if tt.mockUpdateForNotification != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForNotification.txnNotifMap, tt.mockUpdateForNotification.updateMask).
					Return(tt.mockUpdateForNotification.err)
			}

			payeeSmses, payeeNotifications, err := notificationService.notifyUser(tt.args.ctx, tt.args.txnDetails, "", PAYER, nil, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("notifyUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			if !reflect.DeepEqual(payeeSmses, tt.result.sentSMSes) {
				t.Errorf("notifyUser() SMSes got: %v, want: %v", payeeSmses, tt.result.sentSMSes)
			}
			if !assertNotifications(payeeNotifications, tt.result.sentNotifications) {
				t.Errorf("notifyUser() notifications got: %v, want: %v", payeeNotifications, tt.result.sentNotifications)
			}

		})
	}
}

// Payment protocol: UPI
// Order Workflow: NO_OP
// fromPiType: PaymentInstrumentType_ACC
// toPiType: PaymentInstrumentType_GENERIC
// user: PAYER
// possible scenario: use externally linked fi account (gPay) to external GENERIC PI
func TestService_notifyUser__PaymentProtocol_UPI__OrderWorkflow_NO_OP__ACC_GENERIC__PAYER(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	ctrl := gomock.NewController(t)
	commsClient := commsMocks.NewMockCommsClient(ctrl)
	piClient := piMocks.NewMockPiClient(ctrl)
	actorClient := actorMocks.NewMockActorClient(ctrl)
	timelineClient := timelineMocks.NewMockTimelineServiceClient(ctrl)
	depositClient := depositMocks.NewMockDepositClient(ctrl)
	savingsClient := mocks.NewMockSavingsClient(ctrl)
	txnNotificationMapDao := mocks2.NewMockTransactionNotificationMapDao(ctrl)
	accountPiRelationClient := mocks3.NewMockAccountPIRelationClient(ctrl)
	mockUserProcessor := userMocks.NewMockUserProcessor(ctrl)
	mockUserProcessor.EXPECT().GetUserGroupsByActorID(context.Background(), gomock.Any()).
		Return([]commontypes.UserGroup{commontypes.UserGroup_INTERNAL, commontypes.UserGroup_FNF}, nil).AnyTimes()
	notificationService := NewService(commsClient, actorClient, piClient, timelineClient, conf, depositClient, savingsClient, accountPiRelationClient, txnNotificationMapDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	var randomMessageId = "random-message-id"

	defer func() {
		ctrl.Finish()
		notificationService = nil
	}()

	timestampFixture := timestamppb.New(time.Now().Add(-1 * time.Hour))
	type args struct {
		ctx        context.Context
		txnDetails *transactionDetails
	}
	type result struct {
		sentSMSes         []commsPb.SmsType
		sentNotifications []string
		err               error
	}

	type mockGetByTransactionIdForSms struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockGetByTransactionIdForNotification struct {
		txnId string
		err   error
		res   notification.TransactionNotificationMap
	}

	type mockUpdateForSms struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}
	type mockUpdateForNotification struct {
		txnNotifMap notification.TransactionNotificationMap
		updateMask  []notification.TransactionNotificationMapFieldMask
		err         error
	}

	tests := []struct {
		name                                  string
		args                                  args
		result                                result
		commsExpectSms                        *commsPb.SendMessageResponse
		commsExpectNotification               *commsPb.SendMessageBatchResponse
		mockGetByTransactionIdForSms          *mockGetByTransactionIdForSms
		mockGetByTransactionIdForNotification *mockGetByTransactionIdForNotification
		mockUpdateForSms                      *mockUpdateForSms
		mockUpdateForNotification             *mockUpdateForNotification
		wantErr                               bool
	}{
		{
			name: "Acc to Generic PI UPI order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_GENERIC,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_UPI_DEBIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.DebitUPI().Title,
				},
				err: nil,
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitPushNotifId: randomMessageId,
					DebitSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_PUSH_NOTIF_ID,
				},
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to Generic PI Card order in PAID state",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_CARD,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_GENERIC,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					isCardTransaction:     true,
					provenance:            orderPb.OrderProvenance_POS,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_POS_DEBIT,
				},
				sentNotifications: []string{
					notificationService.notificationParams.DebitUPI().Title,
				},
				err: nil,
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitPushNotifId: randomMessageId,
					DebitSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_PUSH_NOTIF_ID,
				},
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to Generic PI UPI order in PAID state but eventTime within push notification trigger after",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestamppb.Now(),
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_GENERIC,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_UPI_DEBIT,
				},
				sentNotifications: nil,
				err:               nil,
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
		{
			name: "Acc to Generic PI UPI order in PAID state but no notifications sent because they were already sent",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:                    "transaction-1",
					orderId:               "order-1",
					fromAccNo:             "actor-1-acc-no",
					fromActorName:         "actor-1",
					fromUpiVpa:            "actor1@fede",
					toAccNo:               "actor-2-acc-no",
					toActorName:           "actor-2",
					toUpiVpa:              "actor2@fede",
					amount:                &money.Money{CurrencyCode: "INR", Units: 10001},
					payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_UPI,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_GENERIC,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					provenance:            orderPb.OrderProvenance_USER_APP,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitSmsId:       randomMessageId,
					DebitPushNotifId: randomMessageId,
				},
			},
			result: result{
				err: nil,
			},
		},
		{
			name: "Acc to Generic PI Card order in PAID state where payerBalance is NIL",
			args: args{
				ctx: context.Background(),
				txnDetails: &transactionDetails{
					id:            "transaction-1",
					orderId:       "order-1",
					fromAccNo:     "actor-1-acc-no",
					fromActorName: "actor-1",
					fromUpiVpa:    "actor1@fede",
					toAccNo:       "actor-2-acc-no",
					toActorName:   "actor-2",
					toUpiVpa:      "actor2@fede",
					amount:        &money.Money{CurrencyCode: "INR", Units: 10001},
					// payerBalanceAmount:    &money.Money{Units: 500},
					payeeBalanceAmount:    &money.Money{Units: 500},
					utr:                   "utr",
					timelineId:            "timeline-id",
					orderStatus:           orderPb.OrderStatus_PAID,
					protocol:              paymentPb.PaymentProtocol_CARD,
					eventTime:             timestampFixture,
					debitedAt:             timestampFixture,
					creditedAt:            timestampFixture,
					txnLastUpdatedAt:      timestampFixture,
					toActorId:             "actor-2",
					toActorProfileImgURL:  "random-url",
					savingAccountUserType: PAYER,
					fromPiType:            piPb.PaymentInstrumentType_BANK_ACCOUNT,
					toPiType:              piPb.PaymentInstrumentType_GENERIC,
					orderWorkflow:         orderPb.OrderWorkflow_NO_OP,
					isCardTransaction:     true,
					provenance:            orderPb.OrderProvenance_POS,
				},
			},
			mockUpdateForSms: &mockUpdateForSms{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_SMS_ID,
				},
			},
			mockGetByTransactionIdForSms: &mockGetByTransactionIdForSms{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
				},
			},
			mockGetByTransactionIdForNotification: &mockGetByTransactionIdForNotification{
				txnId: "transaction-1",
				res: notification.TransactionNotificationMap{
					TransactionId: "transaction-1",
					DebitSmsId:    randomMessageId,
				},
			},
			result: result{
				sentSMSes: []commsPb.SmsType{
					commsPb.SmsType_POS_DEBIT_FALLBACK,
				},
				sentNotifications: []string{
					notificationService.notificationParams.DebitUPI().Title,
				},
				err: nil,
			},
			mockUpdateForNotification: &mockUpdateForNotification{
				txnNotifMap: notification.TransactionNotificationMap{
					TransactionId:    "transaction-1",
					DebitPushNotifId: randomMessageId,
					DebitSmsId:       randomMessageId,
				},
				updateMask: []notification.TransactionNotificationMapFieldMask{
					notification.TransactionNotificationMapFieldMask_DEBIT_PUSH_NOTIF_ID,
				},
			},
			commsExpectNotification: &commsPb.SendMessageBatchResponse{
				Status: rpc.StatusOk(),
				MessageIdList: []string{
					randomMessageId,
				},
			},
			commsExpectSms: &commsPb.SendMessageResponse{
				Status:    rpc.StatusOk(),
				MessageId: randomMessageId,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.commsExpectSms != nil {
				commsClient.EXPECT().
					SendMessage(context.Background(), gomock.All()).
					Return(tt.commsExpectSms, nil)
			}
			if tt.commsExpectNotification != nil {
				commsClient.EXPECT().
					SendMessageBatch(context.Background(), gomock.All()).
					Return(tt.commsExpectNotification, nil)
			}

			if tt.mockGetByTransactionIdForSms != nil {
				first := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForSms.txnId).
					Return(&tt.mockGetByTransactionIdForSms.res, tt.mockGetByTransactionIdForSms.err)

				if tt.mockGetByTransactionIdForNotification != nil {
					second := txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
						Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
					if tt.mockGetByTransactionIdForSms != nil {
						gomock.InOrder(
							first,
							second,
						)
					}
				}
			} else if tt.mockGetByTransactionIdForNotification != nil {
				txnNotificationMapDao.EXPECT().GetByTransactionId(context.Background(), tt.mockGetByTransactionIdForNotification.txnId).
					Return(&tt.mockGetByTransactionIdForNotification.res, tt.mockGetByTransactionIdForNotification.err)
			}

			if tt.mockUpdateForSms != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForSms.txnNotifMap, tt.mockUpdateForSms.updateMask).
					Return(tt.mockUpdateForSms.err)
			}
			if tt.mockUpdateForNotification != nil {
				txnNotificationMapDao.EXPECT().Update(context.Background(), &tt.mockUpdateForNotification.txnNotifMap, tt.mockUpdateForNotification.updateMask).
					Return(tt.mockUpdateForNotification.err)
			}

			payeeSmses, payeeNotifications, err := notificationService.notifyUser(tt.args.ctx, tt.args.txnDetails, "", PAYER, nil, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("notifyUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			if !reflect.DeepEqual(payeeSmses, tt.result.sentSMSes) {
				t.Errorf("notifyUser() SMSes got: %v, want: %v", payeeSmses, tt.result.sentSMSes)
			}
			if !assertNotifications(payeeNotifications, tt.result.sentNotifications) {
				t.Errorf("notifyUser() notifications got: %v, want: %v", payeeNotifications, tt.result.sentNotifications)
			}

		})
	}
}
