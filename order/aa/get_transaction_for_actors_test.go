package aa_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	aaPb "github.com/epifi/gamma/api/order/aa"
)

func TestService_GetTransactionCountForActors(t *testing.T) {
	svc, md, assertTest := newServiceWithMocks(t)
	defer assertTest()

	type mockGetTxnCountsForActorIds struct {
		enable   bool
		actorIds []string
		want     map[string]int32
		err      error
	}

	tests := []struct {
		name                        string
		req                         *aaPb.GetTransactionCountForActorsRequest
		mockGetTxnCountsForActorIds mockGetTxnCountsForActorIds
		res                         *aaPb.GetTransactionCountForActorsResponse
		wantErr                     bool
	}{
		{
			name: "Got the txn count successfully",
			req: &aaPb.GetTransactionCountForActorsRequest{
				ActorIds: []string{
					"actor-1",
					"actor-2",
				},
			},
			mockGetTxnCountsForActorIds: mockGetTxnCountsForActorIds{
				enable: true,
				actorIds: []string{
					"actor-1",
					"actor-2",
				},
				want: map[string]int32{
					"actor-1": 5,
					"actor-2": 0,
				},
			},
			res: &aaPb.GetTransactionCountForActorsResponse{
				Status: rpc.StatusOk(),
				ActorIdToTxnCountMap: map[string]int32{
					"actor-1": 5,
					"actor-2": 0,
				},
			},
		},
		{
			name: "Failed to fetch txn counts",
			req: &aaPb.GetTransactionCountForActorsRequest{
				ActorIds: []string{
					"actor-1",
					"actor-2",
				},
			},
			mockGetTxnCountsForActorIds: mockGetTxnCountsForActorIds{
				enable: true,
				actorIds: []string{
					"actor-1",
					"actor-2",
				},
				err: errors.New("error fetching count"),
			},
			res: &aaPb.GetTransactionCountForActorsResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "Invalid Actors passed",
			req: &aaPb.GetTransactionCountForActorsRequest{
				ActorIds: []string{},
			},
			res: &aaPb.GetTransactionCountForActorsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetTxnCountsForActorIds.enable {
				md.aaTransactionDao.EXPECT().GetTxnCountsForActorIds(context.Background(), tt.mockGetTxnCountsForActorIds.actorIds).
					Return(tt.mockGetTxnCountsForActorIds.want, tt.mockGetTxnCountsForActorIds.err)
			}
			got, err := svc.GetTransactionCountForActors(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTransactionCountForActors() gotErr: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.res) {
				t.Errorf("GetTransactionCountForActors() got: %v, want: %v", got, tt.res)
				return
			}
		})
	}
}
