package model

import (
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	mPb "github.com/epifi/gamma/api/merchant"
	"github.com/epifi/be-common/pkg/nulltypes"

	gormv2 "gorm.io/gorm"
)

type ProbableKnownMerchant struct {
	Id           string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	MerchantId   nulltypes.NullString
	PiId         nulltypes.NullString
	DsMerchantId nulltypes.NullString
	CreatedAt    time.Time
	UpdatedAt    time.Time
	DeletedAt    gormv2.DeletedAt
}

func NewProbableKnownMerchant(m *mPb.ProbableKnownMerchant) (*ProbableKnownMerchant, error) {
	return &ProbableKnownMerchant{
		Id:           m.GetId(),
		MerchantId:   nulltypes.NewNullString(m.GetMerchantId()),
		PiId:         nulltypes.NewNullString(m.GetPiId()),
		DsMerchantId: nulltypes.NewNullString(m.GetDsMerchantId()),
	}, nil
}

func (m *ProbableKnownMerchant) GetProto() (*mPb.ProbableKnownMerchant, error) {
	return &mPb.ProbableKnownMerchant{
		Id:           m.Id,
		MerchantId:   m.MerchantId.GetValue(),
		PiId:         m.PiId.GetValue(),
		DsMerchantId: m.DsMerchantId.GetValue(),
		CreatedAt:    timestampPb.New(m.CreatedAt),
		UpdatedAt:    timestampPb.New(m.UpdatedAt),
	}, nil
}
