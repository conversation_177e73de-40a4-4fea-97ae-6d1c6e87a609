package steps

import (
	"context"
	"reflect"
	"testing"

	types "github.com/epifi/gamma/api/typesv2"

	"google.golang.org/genproto/googleapis/type/date"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/gamma/wealthonboarding/helper"
	mock_comms "github.com/epifi/gamma/wealthonboarding/test/mocks/comms"
	mockReview "github.com/epifi/gamma/wealthonboarding/test/mocks/manual_review"

	"github.com/golang/mock/gomock"
)

var (
	odExpiryApproved = &woPb.OnboardingDetails{
		Metadata: &woPb.OnboardingMetadata{
			ManualReviewAttempts: &woPb.ManualReviewAttempts{
				ReviewAttemptMapping: map[string]*woPb.ManualReviewAttempts_ReviewAttemptList{
					"ITEM_TYPE_DOCUMENT_EXPIRY": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random",
								Status: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
							},
						},
					},
				},
			},
		},
	}
	odRedactApproved = &woPb.OnboardingDetails{
		Metadata: &woPb.OnboardingMetadata{
			ManualReviewAttempts: &woPb.ManualReviewAttempts{
				ReviewAttemptMapping: map[string]*woPb.ManualReviewAttempts_ReviewAttemptList{
					"ITEM_TYPE_AADHAAR_REDACTION": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random",
								Status: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
							},
						},
					},
				},
			},
		},
	}
	odLivenessApproved = &woPb.OnboardingDetails{
		Metadata: &woPb.OnboardingMetadata{
			ManualReviewAttempts: &woPb.ManualReviewAttempts{
				ReviewAttemptMapping: map[string]*woPb.ManualReviewAttempts_ReviewAttemptList{
					"ITEM_TYPE_LIVENESS": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random",
								Status: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
							},
						},
					},
				},
			},
		},
	}
	odNoReviewItems   = &woPb.OnboardingDetails{}
	odLivenessPending = &woPb.OnboardingDetails{
		Metadata: &woPb.OnboardingMetadata{
			ManualReviewAttempts: &woPb.ManualReviewAttempts{
				ReviewAttemptMapping: map[string]*woPb.ManualReviewAttempts_ReviewAttemptList{
					"ITEM_TYPE_DOCUMENT_EXPIRY": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random1",
								Status: woPb.ReviewStatus_REVIEW_STATUS_PENDING,
							},
						},
					},
					"ITEM_TYPE_LIVENESS": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random2",
								Status: woPb.ReviewStatus_REVIEW_STATUS_PENDING,
							},
						},
					},
				},
			},
		},
	}
	odRejectedAndApproved = &woPb.OnboardingDetails{
		Metadata: &woPb.OnboardingMetadata{
			ManualReviewAttempts: &woPb.ManualReviewAttempts{
				ReviewAttemptMapping: map[string]*woPb.ManualReviewAttempts_ReviewAttemptList{
					"ITEM_TYPE_LIVENESS": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random1",
								Status: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
							},
						},
					},
					"ITEM_TYPE_AADHAAR_REDACTION": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random2",
								Status: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
							},
						},
					},
				},
			},
		},
	}
	odPermRejected = &woPb.OnboardingDetails{
		Metadata: &woPb.OnboardingMetadata{
			ManualReviewAttempts: &woPb.ManualReviewAttempts{
				ReviewAttemptMapping: map[string]*woPb.ManualReviewAttempts_ReviewAttemptList{
					"ITEM_TYPE_LIVENESS": {
						ReviewAttempts: []*woPb.ManualReviewAttempts_ReviewAttempt{
							{
								Id:     "random3",
								Status: woPb.ReviewStatus_REVIEW_STATUS_IN_PROGRESS,
							},
						},
					},
				},
			},
		},
	}
)

func TestConsolidatedManualReviewStep_Perform(t *testing.T) {
	ctr := gomock.NewController(t)

	mockSvc := mockReview.NewMockIManualReview(ctr)
	mockComms := mock_comms.NewMockIComms(ctr)
	brokerMock := eventMock.NewMockBroker(ctr)
	mockComms.EXPECT().SendNotificationAsync(gomock.Any(), gomock.Any(), gomock.Any()).Return().AnyTimes()
	type args struct {
		ctx   context.Context
		od    *woPb.OnboardingDetails
		mocks []interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    *StepExecutionResponse
		wantErr bool
	}{
		{
			name: "expiry review approved",
			args: args{
				ctx: context.Background(),
				od:  odExpiryApproved,
				mocks: []interface{}{
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random").Return(woPb.ReviewStatus_REVIEW_STATUS_APPROVED, nil),
					mockSvc.EXPECT().GetReviewItem(gomock.Any(), gomock.Any()).Return(&woPb.ManualReview{
						ReviewPayload: &woPb.ReviewPayload{
							Payload: &woPb.ReviewPayload_ExpiryReview{
								ExpiryReview: &woPb.ReviewPayload_OcrReview{
									ProcessedDocument: &woPb.OcrDocumentProof{
										Doc: &types.DocumentProof{
											Expiry: &date.Date{
												Year:  3025,
												Month: 1,
												Day:   1,
											},
										},
									},
								},
							},
						},
						ItemType: woPb.ItemType_ITEM_TYPE_DOCUMENT_EXPIRY,
					}, nil),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
							NoMetadata: &woPb.NoMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: false,
		},
		{
			name: "redact review approved",
			args: args{
				ctx: context.Background(),
				od:  odRedactApproved,
				mocks: []interface{}{
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random").Return(woPb.ReviewStatus_REVIEW_STATUS_APPROVED, nil),
					mockSvc.EXPECT().GetReviewItem(gomock.Any(), gomock.Any()).Return(&woPb.ManualReview{
						ReviewPayload: &woPb.ReviewPayload{
							Payload: &woPb.ReviewPayload_RedactionReview{
								RedactionReview: &woPb.ReviewPayload_OcrReview{
									ProcessedDocument: &woPb.OcrDocumentProof{
										Doc: &types.DocumentProof{
											ProofType: types.DocumentProofType_DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR,
										},
									},
								},
							},
						},
						ItemType: woPb.ItemType_ITEM_TYPE_AADHAAR_REDACTION,
					}, nil),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odRedactApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
							NoMetadata: &woPb.NoMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: false,
		},
		{
			name: "liveness review approved",
			args: args{
				ctx: context.Background(),
				od:  odLivenessApproved,
				mocks: []interface{}{
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random").Return(woPb.ReviewStatus_REVIEW_STATUS_APPROVED, nil),
					mockSvc.EXPECT().GetReviewItem(gomock.Any(), gomock.Any()).Return(&woPb.ManualReview{
						ItemType: woPb.ItemType_ITEM_TYPE_LIVENESS,
					}, nil),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
							NoMetadata: &woPb.NoMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: false,
		},
		{
			name: "no review items",
			args: args{
				ctx: context.Background(),
				od:  odNoReviewItems,
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
							NoMetadata: &woPb.NoMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				IsLastStep:       false,
			},
			wantErr: false,
		},
		{
			name: "pending review items",
			args: args{
				ctx: context.Background(),
				od:  odLivenessPending,
				mocks: []interface{}{
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random1").Return(woPb.ReviewStatus_REVIEW_STATUS_PENDING, nil),
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random2").Return(woPb.ReviewStatus_REVIEW_STATUS_PENDING, nil),
					mockSvc.EXPECT().SubmitForReview(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes(),
				},
			},
			want: &StepExecutionResponse{
				Deeplink: &deeplinkPb.Deeplink{
					// TODO(ismail): currently only error supports an illustration url, we might need to return a status screen with illustration url in it
					Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_WealthOnboardingErrorScreenOptions{
						WealthOnboardingErrorScreenOptions: &deeplinkPb.WealthOnboardingErrorScreenOptions{
							IllustrationUrl: helper.ManualVerificationIllustrationURL,
							Title:           ManualVerificationTitle,
							Description:     ManualVerificationDescription,
							Cta: &deeplinkPb.Cta{
								Text: "Awesome",
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_HOME,
								},
							},
						},
					},
				},
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_IN_PROGRESS,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
							NoMetadata: &woPb.NoMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
			},
			wantErr: false,
		},
		{
			name: "liveness approved and ocr rejected",
			args: args{
				ctx: context.Background(),
				od:  odRejectedAndApproved,
				mocks: []interface{}{
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random1").Return(woPb.ReviewStatus_REVIEW_STATUS_APPROVED, nil),
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random2").Return(woPb.ReviewStatus_REVIEW_STATUS_REJECTED, nil),
					mockSvc.EXPECT().GetReviewItem(gomock.Any(), gomock.Any()).Return(&woPb.ManualReview{
						ItemType: woPb.ItemType_ITEM_TYPE_LIVENESS,
					}, nil).Times(2),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_COMPLETED,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
							NoMetadata: &woPb.NoMetadata{},
						},
					},
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_IN_PROGRESS,
				StepsToInvalidate: []woPb.OnboardingStep{
					woPb.OnboardingStep_ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO,
					woPb.OnboardingStep_ONBOARDING_STEP_CREATE_AND_SIGN_KRA_DOCKET,
					woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
				},
			},
			wantErr: false,
		},
		{
			name: "liveness permanently rejected",
			args: args{
				ctx: context.Background(),
				od:  odPermRejected,
				mocks: []interface{}{
					mockSvc.EXPECT().CheckStatus(gomock.Any(), "random3").Return(woPb.ReviewStatus_REVIEW_STATUS_PERMANENTLY_REJECTED, nil),
				},
			},
			want: &StepExecutionResponse{
				NextOnboardingStep: woPb.OnboardingStep_ONBOARDING_STEP_UNSPECIFIED,
				CurrentStepDetails: &woPb.OnboardingStepDetails{
					OnboardingDetailsId: odExpiryApproved.GetId(),
					Step:                woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
					Status:              woPb.OnboardingStepStatus_STEP_STATUS_FUTURE_SCOPE,
					Metadata: &woPb.OnboardingStepMetadata{
						StepMetaData: &woPb.OnboardingStepMetadata_NoMetadata{
							NoMetadata: &woPb.NoMetadata{},
						},
					},
					SubStatus: woPb.OnboardingStepSubStatus_STEP_SUB_STATUS_PERMANENTLY_REJECTED,
				},
				OnboardingStatus: woPb.OnboardingStatus_ONBOARDING_STATUS_FUTURE_SCOPE,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ConsolidatedManualReviewStep{
				manualReview:      mockSvc,
				currentStep:       woPb.OnboardingStep_ONBOARDING_STEP_CONSOLIDATED_MANUAL_REVIEW,
				nextStepOnSuccess: woPb.OnboardingStep_ONBOARDING_STEP_CONFIRM_PEP_AND_CITIZENSHIP,
				conf:              conf,
				commsService:      mockComms,
				eventBroker:       brokerMock,
			}
			got, err := s.Perform(tt.args.ctx, tt.args.od)
			if (err != nil) != tt.wantErr {
				t.Errorf("Perform() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got = getComparableSer(got)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Perform() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func getComparableSer(ser *StepExecutionResponse) *StepExecutionResponse {
	if ser == nil {
		return nil
	}
	ret := &StepExecutionResponse{
		NextOnboardingStep: ser.NextOnboardingStep,
		Deeplink:           ser.Deeplink,
		OnboardingStatus:   ser.OnboardingStatus,
		StepsToInvalidate:  ser.StepsToInvalidate,
	}
	if ser.CurrentStepDetails != nil {
		ret.CurrentStepDetails = &woPb.OnboardingStepDetails{
			OnboardingDetailsId: ser.CurrentStepDetails.GetOnboardingDetailsId(),
			Step:                ser.CurrentStepDetails.GetStep(),
			Metadata:            ser.CurrentStepDetails.GetMetadata(),
			SubStatus:           ser.CurrentStepDetails.GetSubStatus(),
			Status:              ser.CurrentStepDetails.GetStatus(),
		}
	}
	return ret
}
