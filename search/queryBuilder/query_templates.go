package queryBuilder

/*
This file will contain all the queries to be done on es
*/

// query for ifsc autocomplete
const PaySearchAutoComp = `{
    "suggest": {
        "ifsc-suggest" : {
            "prefix" : "{{ .SearchText }}",
            "completion" : {
                "field" : "suggest",
				"skip_duplicates": {{ .SkipDuplicates }},
				"size": {{ .Size }},
				"fuzzy" : {
                    "fuzziness" : 2,
					"prefix_length": 3
                },
				"contexts": {
					"field_type": [{{$c := .Context}}{{range $index, $elem := .Context}} {{ if eq (inc $index) (len $c)}} "{{ . }}" {{ else }} "{{ . }}", {{end}} {{end}}]
				}
            }
        }
    },
	"_source": ["bank_name", "ifsc", "branch", "city", "district"]
}`
