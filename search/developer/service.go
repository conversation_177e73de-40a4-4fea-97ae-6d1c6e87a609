package developer

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/gamma/search/config"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/epifi/be-common/pkg/aws/v2/s3"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	developerPb "github.com/epifi/gamma/api/search/developer"
	"github.com/epifi/gamma/search/indexer"
	"github.com/epifi/gamma/search/parser"
)

const (
	// TODO(sahil): use filepath to join file paths
	s3PathIndexingPrefix = "indexing/"
	s3PathIndexingLatest = s3PathIndexingPrefix + "query_base_latest.csv"
	s3PathNluPrefix      = "NLUengine/setup_data/mpnet/queries/"
	s3PathNluLatest      = s3PathNluPrefix + "quirky_queries_latest.txt"

	// paths for query to skill map
	s3PathQueryToSkillLatest = "query_to_skill_latest.csv"
)

// nolint
type DeveloperService struct {
	developerPb.UnimplementedDevSearchServer
	s3Client           s3.S3Client
	queryToSkillConfig *config.QueryToSkillMap
	currIndexer        indexer.ESIndexer
	nluParser          parser.NLUParser
}

func NewDeveloperService(s3Client s3.S3Client, currIndexer indexer.ESIndexer, nluParser parser.NLUParser,
	queryToSkillS3Path *config.QueryToSkillMap) *DeveloperService {
	return &DeveloperService{s3Client: s3Client, currIndexer: currIndexer, nluParser: nluParser, queryToSkillConfig: queryToSkillS3Path}
}

func (s *DeveloperService) writeToS3Paths(ctx context.Context, indexCsvBytesBuf, queryCsvBytesBuf []byte) error {
	ts := time.Now().Format("20060102_150405")
	s3IndexingTsPath := fmt.Sprintf("%squery_base_%s", s3PathIndexingPrefix, ts)
	s3NluTsPath := fmt.Sprintf("%squirky_queries_%s", s3PathNluPrefix, ts)
	logger.Info(ctx, "s3 paths are", zap.String("indexing-path", s3IndexingTsPath), zap.String("nlu-path", s3NluTsPath))
	// write to s3 indexing latest path
	err := s.s3Client.Write(ctx, s3PathIndexingLatest, indexCsvBytesBuf, string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if err != nil {
		return fmt.Errorf("error in uploading file to s3 path: %s, error: %w", s3PathIndexingLatest, err)
	}
	// write to s3 indexing timestamp path
	err = s.s3Client.Write(ctx, s3IndexingTsPath, indexCsvBytesBuf, string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if err != nil {
		return fmt.Errorf("error in uploading file to s3 path: %s, error: %w", s3IndexingTsPath, err)
	}
	// write to s3 nlu latest path
	err = s.s3Client.Write(ctx, s3PathNluLatest, queryCsvBytesBuf, string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if err != nil {
		return fmt.Errorf("error in uploading file to s3 path: %s, error: %w", s3PathNluLatest, err)
	}
	// write to s3 nlu timestamp path
	err = s.s3Client.Write(ctx, s3NluTsPath, queryCsvBytesBuf, string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if err != nil {
		return fmt.Errorf("error in uploading file to s3 path: %s, error: %w", s3NluTsPath, err)
	}
	return nil
}
