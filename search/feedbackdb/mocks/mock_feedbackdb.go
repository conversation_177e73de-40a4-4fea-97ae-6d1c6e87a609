// Code generated by MockGen. DO NOT EDIT.
// Source: db.go

// Package mock_feedbackdb is a generated GoMock package.
package mock_feedbackdb

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockIdb is a mock of Idb interface.
type MockIdb struct {
	ctrl     *gomock.Controller
	recorder *MockIdbMockRecorder
}

// MockIdbMockRecorder is the mock recorder for MockIdb.
type MockIdbMockRecorder struct {
	mock *MockIdb
}

// NewMockIdb creates a new mock instance.
func NewMockIdb(ctrl *gomock.Controller) *MockIdb {
	mock := &MockIdb{ctrl: ctrl}
	mock.recorder = &MockIdbMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIdb) EXPECT() *MockIdbMockRecorder {
	return m.recorder
}

// GetCounterByKey mocks base method.
func (m *MockIdb) GetCounterByKey(ctx context.Context, key string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCounterByKey", ctx, key)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCounterByKey indicates an expected call of GetCounterByKey.
func (mr *MockIdbMockRecorder) GetCounterByKey(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCounterByKey", reflect.TypeOf((*MockIdb)(nil).GetCounterByKey), ctx, key)
}

// IncrCounterByKey mocks base method.
func (m *MockIdb) IncrCounterByKey(ctx context.Context, key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrCounterByKey", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrCounterByKey indicates an expected call of IncrCounterByKey.
func (mr *MockIdbMockRecorder) IncrCounterByKey(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrCounterByKey", reflect.TypeOf((*MockIdb)(nil).IncrCounterByKey), ctx, key)
}
