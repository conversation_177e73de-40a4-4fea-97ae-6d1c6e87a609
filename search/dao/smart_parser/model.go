package smart_parser

type SmartParserRequest struct {
	Question string `json:"answer,omitempty"`
	Model    string `json:"model,omitempty"`
}

func (req *SmartParserRequest) GetQuestion() string {
	return req.Question
}

func (req *SmartParserRequest) GetModel() string {
	return req.Model
}

type SmartParserResponse struct {
	Answer string `json:"answer,omitempty"`
	Model  string `json:"model,omitempty"`
}

func (res *SmartParserResponse) GetAnswer() string {
	return res.Answer
}

func (res *SmartParserResponse) GetModel() string {
	return res.Model
}

type InvestRecommendationsRequest struct {
	Query string `json:"query,omitempty"`
}

func (req *InvestRecommendationsRequest) GetQuery() string {
	return req.Query
}

type InvestRecommendationsResponse struct {
	Answer string `json:"answer,omitempty"`
}

func (res *InvestRecommendationsResponse) GetAnswer() string {
	return res.Answer
}
