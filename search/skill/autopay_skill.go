package skill

import (
	"context"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fittt/clientstate"
	searchPb "github.com/epifi/gamma/api/search"
	actionbarPb "github.com/epifi/gamma/api/search/actionbar"
	skillPb "github.com/epifi/gamma/api/search/skill"
	"github.com/epifi/gamma/search/common"
	"github.com/epifi/gamma/search/constant"
	"github.com/epifi/gamma/search/entity"
)

type AutoPaySkill struct {
	configFetcher      SkillConfigGet
	searchResultConfig *skillPb.SearchResultConfig
}

func NewAutoPaySkill(configFetcher SkillConfigGet) *AutoPaySkill {
	return &AutoPaySkill{
		configFetcher: configFetcher,
	}
}

func (a *AutoPaySkill) AddConfigValues(ctx context.Context, config Config) error {
	var currSkillConfig *skillPb.SearchResultConfig
	var err error
	// fetch the config for skill
	if currSkillConfig, err = a.configFetcher.FetchSkillConfig(ctx, config.SkillName); err != nil {
		return err
	}
	a.searchResultConfig = currSkillConfig
	return nil
}

func (a *AutoPaySkill) AddEntityData(ctx context.Context) {

}

func (a *AutoPaySkill) GetFinancialActivity(ctx context.Context, getFinancialActivityRequest *GetFinancialActivityRequest) (*actionbarPb.QuickInfoResponse, error) {
	return nil, nil
}

func (a *AutoPaySkill) BuildFiLiteSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	return nil, nil
}

func (a *AutoPaySkill) BuildSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	var searchResult []*searchPb.SearchResultUnit
	// Get Deep links
	if a.searchResultConfig.GetFiQuickLinkConfig().GetEnable() {
		currActionResponse := &actionbarPb.ActionResponse{
			FitCustomiseRuleScreenOption: []*deeplink.FitCustomiseRuleScreenOptions{
				{
					PageType: clientstate.SubscriptionPageType_SUBSCRIPTION_PAGE_NEW,
				},
			},
		}
		searchResult = append(searchResult, common.GetSearchResultUnitFromQl(currActionResponse, constant.FiTabName))
	}
	return searchResult, nil
}
