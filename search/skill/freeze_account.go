//nolint:dupl
package skill

import (
	"context"

	"github.com/epifi/gamma/search/constant"
	"github.com/epifi/gamma/search/entity"

	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/search/actionbar"
	skillPb "github.com/epifi/gamma/api/search/skill"
	"github.com/epifi/gamma/search/common"
)

type FreezeAccountSkill struct {
	configFetcher      SkillConfigGet
	searchResultConfig *skillPb.SearchResultConfig
}

func (c *FreezeAccountSkill) AddConfigValues(ctx context.Context, config Config) error {
	var currSkillConfig *skillPb.SearchResultConfig
	var err error
	// fetch the config for skill
	if currSkillConfig, err = c.configFetcher.FetchSkillConfig(ctx, config.SkillName); err != nil {
		return err
	}
	c.searchResultConfig = currSkillConfig
	return nil
}

func (c *FreezeAccountSkill) AddEntityData(ctx context.Context) {
}

func NewFreezeAccountSkill(configFetcher SkillConfigGet) *FreezeAccountSkill {
	return &FreezeAccountSkill{
		configFetcher: configFetcher,
	}
}

func (c *FreezeAccountSkill) BuildFiLiteSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	return nil, nil
}

func (c *FreezeAccountSkill) BuildSearchResult(ctx context.Context) ([]*searchPb.SearchResultUnit, *entity.CurrActorSearchCtxImpl) {
	return common.ReorderSearchResultUnits(c.searchResultConfig.GetResponseTypeOrder(), c.buildSearchResultForCloseAccount()), nil
}

func (c *FreezeAccountSkill) buildSearchResultForCloseAccount() []*searchPb.SearchResultUnit {
	var searchResultUnits []*searchPb.SearchResultUnit
	if c.searchResultConfig.GetFiSummaryConfig().GetEnable() {
		searchResultUnits = append(searchResultUnits, c.getFreezeAccountSummary())
	}
	return searchResultUnits
}

func (c *FreezeAccountSkill) GetFinancialActivity(ctx context.Context, getFinancialActivityRequest *GetFinancialActivityRequest) (*actionbar.QuickInfoResponse, error) {
	return nil, nil
}

func (c *FreezeAccountSkill) getFreezeAccountSummary() *searchPb.SearchResultUnit {
	return common.GetSearchResultUnitFromSummary(&actionbar.SummaryResponse{
		Summary: c.searchResultConfig.GetFiSummaryConfig().GetSummaryText(),
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}
