// nolint:testifylint
package elasticsearch_test

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/olivere/elastic/v7"
	"github.com/opensearch-project/opensearch-go/v2/opensearchapi"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/gamma/search/elasticsearch"
	mock_elasticsearch "github.com/epifi/gamma/search/test/mocks/es"
)

func mockQuery() string {
	return `
{
    "query": {
        "match" : {
            "message" : {
                "query" : "baroda"
            }
        }
    }
}
`
}

func mockSuggQuery() string {
	return `{
    "suggest": {
        "song-suggest" : {
            "prefix" : "bank of baroda",
            "completion" : {
                "field" : "suggest",
        				"skip_duplicates": true,
        				"size": 20,
        				"fuzzy": {
        				  "fuzziness": 2,
        				  "prefix_length": 3
        				}
            }
        }
    },
    "_source": ["bank_name", "ifsc", "branch", "city", "district"]
}`
}

func mockSuggResp() ([]byte, *opensearchapi.Response, error) {
	esResBody := strings.NewReader(`{
			  "took" : 44,
			  "timed_out" : false,
			  "_shards" : {
				"total" : 5,
				"successful" : 5,
				"skipped" : 0,
				"failed" : 0
			  },
			  "hits" : {
				"total" : {
				  "value" : 0,
				  "relation" : "eq"
				},
				"max_score" : null,
				"hits" : [ ]
			  },
			  "suggest" : {
				"song-suggest" : [
				  {
					"text" : "bank of baroda",
					"offset" : 0,
					"length" : 14,
					"options" : [
					  {
						"text" : "BANK OF BARODA",
						"_index" : "banks_ac",
						"_type" : "_doc",
						"_id" : "BARB0AMBHET",
						"_score" : 180.0,
						"_source" : {
						  "city" : "SURAT",
						  "district" : "AMBHETI",
						  "bank_name" : "BANK OF BARODA",
						  "ifsc" : "BARB0AMBHET",
						  "branch" : "AMBHETI, DIST. SURAT, GUJARAT"
						}
					  }
					]
				  }
				]
			  }
			}`)
	resBytes, err := ioutil.ReadAll(esResBody)
	if err != nil {
		return nil, nil, err
	}
	return resBytes, &opensearchapi.Response{
		StatusCode: 200,
		Header:     nil,
		Body:       ioutil.NopCloser(esResBody),
	}, nil
}

func mockRes() ([]byte, *opensearchapi.Response, error) {
	esResBody := strings.NewReader(`{"took" : 0,
				  "timed_out" : false,
				  "_shards" : {
					"total" : 0,
					"successful" : 0,
					"skipped" : 0,
					"failed" : 0
				  },
				  "hits" : {
					"total" : {
					  "value" : 0,
					  "relation" : "eq"
					},
					"max_score" : null,
					"hits" : null
				  }
				}`)
	resBytes, err := ioutil.ReadAll(esResBody)
	if err != nil {
		return nil, nil, err
	}
	return resBytes, &opensearchapi.Response{
		StatusCode: 200,
		Header:     nil,
		Body:       ioutil.NopCloser(esResBody),
	}, nil
}

func TestUsersRepo_ExecuteQuery(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	resBytes, esResp, err1 := mockRes()
	var mockedResp elastic.SearchResult
	if err := json.Unmarshal(resBytes, &mockedResp); err != nil {
		assert.Fail(t, "json unmarshal failed: %w", err)
	}
	es := mock_elasticsearch.NewMockES(ctrl)
	es.EXPECT().Query(context.Background(), mockQuery(), "").Return(resBytes, esResp, err1)

	// initialize indexRepo
	ur, _ := elasticsearch.NewUsersRepoSearcher("", es)
	got, err1 := ur.ExecuteQuery(context.Background(), mockQuery())

	assert.Nil(t, err1)
	assert.Equal(t, mockedResp, *got)
}

func TestBankAutoCompRepo_ExecuteQuery(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRespBytes, esResp, err1 := mockSuggResp()
	var mockedResp elastic.SearchResult
	if err := json.Unmarshal(mockRespBytes, &mockedResp); err != nil {
		assert.Fail(t, "json unmarshal failed: %w", err)
	}

	es := mock_elasticsearch.NewMockES(ctrl)
	es.EXPECT().Query(context.Background(), mockSuggQuery(), "").Return(mockRespBytes, esResp, err1)

	// initialize banksAcRepo
	bankRepo, err := elasticsearch.NewBankAutoCompRepo("", es)
	assert.Nil(t, err)
	got, err := bankRepo.ExecuteQuery(context.Background(), mockSuggQuery())
	assert.Nil(t, err)
	assert.Equal(t, mockedResp, *got)
}
