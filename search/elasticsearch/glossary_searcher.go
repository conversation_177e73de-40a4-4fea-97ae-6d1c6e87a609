package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/search/constant"
	"github.com/epifi/gamma/search/queryBuilder"
)

// type to distinguish between same ES type for wire initialization
type EsClientFinGlossary ES

// type to distinguish between same index type for wire initialization
type FinGlossaryIndexName string

type searchResp struct {
	Value   string
	Keyword string
}

type FinGlossaryIndexSearcher struct {
	index    FinGlossaryIndexName
	es       ES
	qBuilder queryBuilder.QueryBuilder
}

func NewFinGlossaryIndexSearcher(index FinGlossaryIndexName, esClient EsClientFinGlossary) (*FinGlossaryIndexSearcher, error) {
	qBuilder := queryBuilder.NewQueryBuilder()
	return &FinGlossaryIndexSearcher{
		index:    index,
		es:       esClient,
		qBuilder: qBuilder,
	}, nil
}

//nolint:dupl
func (finGlossarySearcher *FinGlossaryIndexSearcher) ExecuteQuery(ctx context.Context, input string) (*elastic.SearchResult, error) {
	respBytes, _, err := finGlossarySearcher.es.Query(ctx, input, string(finGlossarySearcher.index))
	if err != nil {
		return nil, fmt.Errorf("error in querying: %w", err)
	}
	searchResult := elastic.SearchResult{}
	err = json.Unmarshal(respBytes, &searchResult)
	if err != nil {
		return nil, fmt.Errorf("error in resp json unmarshal: %w", err)
	}
	if searchResult.Error != nil {
		var rootCause []string
		if searchResult.Error.RootCause != nil {
			for idx := range searchResult.Error.RootCause {
				rootCause = append(rootCause, searchResult.Error.RootCause[idx].Reason)
			}
		}
		zapFields := []zap.Field{zap.String("es-query-error", searchResult.Error.Reason)}
		if rootCause != nil {
			zapFields = append(zapFields, zap.Strings("rootcause", rootCause))
		}
		logger.Error(ctx, "error in es response", zapFields...)
		return nil, fmt.Errorf("error in es response: %s", searchResult.Error.Reason)
	}
	return &searchResult, nil
}

func (finGlossarySearcher *FinGlossaryIndexSearcher) QueryBuilder(ctx context.Context, keyword string) (string, error) {
	resp, err := finGlossarySearcher.qBuilder.FinGlossaryQuery(ctx, keyword)
	if err != nil {
		return "", err
	}
	return resp, nil
}

func (finGlossarySearcher *FinGlossaryIndexSearcher) GetRandomGlossaryTerm(ctx context.Context) (string, error) {
	query, err := finGlossarySearcher.qBuilder.GetAllGlossaryQuery(ctx)
	if err != nil {
		logger.Error(ctx, "Error in getting getallglossary query", zap.Error(err))
		return "", err
	}
	res, err := finGlossarySearcher.ExecuteQuery(ctx, query)
	if err != nil {
		logger.Error(ctx, "Error in executing GetRandomGlossaryTerm", zap.Error(err))
		return "", err
	}
	if res == nil || res.TotalHits() == 0 {
		logger.Error(ctx, "Zero hits found", zap.String("query", query))
		return "", fmt.Errorf("Zero hits found")
	}
	var finGlossaryDetail searchResp
	if err := json.Unmarshal(res.Hits.Hits[0].Source, &finGlossaryDetail); err != nil {
		logger.Error(ctx, "error while parsing getAllGlossaryQuery response", zap.Error(err))
		return "", err
	}
	summaryString := finGlossaryDetail.Keyword
	return summaryString, nil
}

func (finGlossarySearcher *FinGlossaryIndexSearcher) GetSuggestionForGlossary(ctx context.Context, ques string) ([]string, error) {
	query, err := finGlossarySearcher.qBuilder.GetGlossarySuggestionQuery(ctx, ques)
	if err != nil {
		logger.Error(ctx, "Error in getting GetGlossarySuggestionQuery query", zap.Error(err))
		return nil, err
	}
	res, err := finGlossarySearcher.ExecuteQuery(ctx, query)
	if err != nil {
		logger.Error(ctx, "Error in executing GetGlossarySuggestionQuery", zap.Error(err))
		return nil, err
	}
	if res == nil || res.Suggest == nil || res.Suggest["glossary-suggest"] == nil {
		logger.Error(ctx, "Zero hits found", zap.String("query", query))
		return nil, fmt.Errorf("zero hits found")
	}
	suggResp := res.Suggest["glossary-suggest"][0].Options
	var finGlossaryDetail searchResp
	var suggResponse []string
	for index := range suggResp {
		if err := json.Unmarshal(suggResp[index].Source, &finGlossaryDetail); err != nil {
			logger.Error(ctx, "error while parsing getAllGlossaryQuery response", zap.Error(err))
			return nil, err
		} else {
			suggResponse = append(suggResponse, constant.GLOSSARY_PREFIX+" "+finGlossaryDetail.Keyword)
		}
	}

	return suggResponse, nil
}
