package cc_securitygroups_security

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var owlhNodeReachRules = &securitygroups.SGIR{
	Name:        "owlh-node reach",
	Account:     cfg.Security,
	VpcId:       "vpc-014c56086a6612639",
	Description: "enables owlh-master to reach owlh node",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "enables monitored instances to reach owlh node to forward traffic",
			Protocol:    "tcp",
			FromPort:    50010,
			ToPort:      50010,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "owlh-node comms to master",
			Protocol:    "tcp",
			FromPort:    50002,
			ToPort:      50002,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "prmetheus-access ",
			Protocol:    "tcp",
			FromPort:    9100,
			ToPort:      9101,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "owlh-node comms to master",
			Protocol:    "tcp",
			FromPort:    50002,
			ToPort:      50002,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "enables monitored instances to reach owlh node to forward traffic",
			Protocol:    "tcp",
			FromPort:    50010,
			ToPort:      50010,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{},
}
