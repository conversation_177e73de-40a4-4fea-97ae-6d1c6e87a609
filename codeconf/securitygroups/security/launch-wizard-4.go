package cc_securitygroups_security

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var launchWizard4Rules = &securitygroups.SGIR{
	Name:        "launch-wizard-4",
	Account:     cfg.Security,
	VpcId:       "vpc-014c56086a6612639",
	Description: "launch-wizard-4 created 2023-07-27T11:51:41.152Z",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "MISP",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{},
}
