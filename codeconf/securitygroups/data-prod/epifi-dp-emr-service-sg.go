package cc_securitygroups_data_prod

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var epifiDpEmrServiceSgRules = &securitygroups.SGIR{
	Name:        "epifi-dp-emr-service-sg",
	Account:     cfg.DataProd,
	VpcId:       "vpc-0c1889d2ca3ceeb04",
	Description: "security group for emr service",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9443,
			ToPort:      9443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.DataProd, SecurityGroupName: "epifi-dp-emr-master-sg", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "securitygroup",
			Egress:      true,
			Description: "",
			Protocol:    "tcp",
			FromPort:    8443,
			ToPort:      8443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.DataProd, SecurityGroupName: "epifi-dp-emr-master-sg", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      true,
			Description: "",
			Protocol:    "tcp",
			FromPort:    8443,
			ToPort:      8443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.DataProd, SecurityGroupName: "epifi-dp-emr-slave-sg", PrefixList: ""},
		},
	},
	Tags: map[string]string{"Name": "epifi-dp-emr-service-sg"},
}
