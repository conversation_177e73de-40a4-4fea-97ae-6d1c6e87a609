package cc_securitygroups_staging

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var commonPromethuesSgRules = &securitygroups.SGIR{
	Name:        "common-promethues-sg",
	Account:     cfg.Staging,
	VpcId:       "vpc-04954cf7b8592de5e",
	Description: "Managed by Terraform",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9100,
			ToPort:      9100,
			SourceDest:  &securitygroups.SrvObject{Cidr: "********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9100,
			ToPort:      9100,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9999,
			ToPort:      9999,
			SourceDest:  &securitygroups.SrvObject{Cidr: "********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9999,
			ToPort:      9999,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{},
	Tags:        map[string]string{},
}
