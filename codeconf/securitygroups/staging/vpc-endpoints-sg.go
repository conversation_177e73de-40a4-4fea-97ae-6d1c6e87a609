package cc_securitygroups_staging

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var vpcEndpointsSgRules = &securitygroups.SGIR{
	Name:        "vpc-endpoints-sg",
	Account:     cfg.Staging,
	VpcId:       "vpc-04954cf7b8592de5e",
	Description: "Managed by Terraform",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{},
	Tags:        map[string]string{},
}
