package cc_securitygroups_qa

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var k8sQapublic9cb4772440Rules = &securitygroups.SGIR{
	Name:        "k8s-qapublic-9cb4772440",
	Account:     cfg.QA,
	VpcId:       "vpc-020996fb61b6f695d",
	Description: "[k8s] Managed SecurityGroup for LoadBalancer",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    443,
			ToPort:      443,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{"elbv2.k8s.aws/cluster": "epifi-qa",
		"ingress.k8s.aws/resource": "ManagedLBSecurityGroup",
		"ingress.k8s.aws/stack":    "qa-public",
	},
}
