package cc_securitygroups_qa

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var qaCockroachSgRules = &securitygroups.SGIR{
	Name:        "qa-cockroach-sg",
	Account:     cfg.QA,
	VpcId:       "vpc-020996fb61b6f695d",
	Description: "Managed by Terraform",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.QA, SecurityGroupName: "qa-cockroach-sg", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    26257,
			ToPort:      26257,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    8080,
			ToPort:      8080,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    26257,
			ToPort:      26257,
			SourceDest:  &securitygroups.SrvObject{Cidr: "********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    8080,
			ToPort:      8080,
			SourceDest:  &securitygroups.SrvObject{Cidr: "********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    9100,
			ToPort:      9100,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    22,
			ToPort:      22,
			SourceDest:  &securitygroups.SrvObject{Cidr: "********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "deploy-prometheus",
			Protocol:    "tcp",
			FromPort:    8080,
			ToPort:      8080,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    26257,
			ToPort:      26257,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    26257,
			ToPort:      26257,
			SourceDest:  &securitygroups.SrvObject{Cidr: "********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "",
			Protocol:    "tcp",
			FromPort:    22,
			ToPort:      22,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{"Name": "cockroach-sg"},
}
