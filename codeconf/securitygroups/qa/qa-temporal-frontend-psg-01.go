package cc_securitygroups_qa

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var qaTemporalFrontendPsg01Rules = &securitygroups.SGIR{
	Name:        "qa-temporal-frontend-psg-01",
	Account:     cfg.QA,
	VpcId:       "vpc-020996fb61b6f695d",
	Description: "For the front-end component in Temporal - QA env",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "Allow host EKS cluster - primary security group",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.QA, SecurityGroupName: "eks-cluster-sg-epifi-qa-*********", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "Allow internal traffic",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.QA, SecurityGroupName: "qa-temporal-frontend-psg-01", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "Allow host EKS control plane security group",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.QA, SecurityGroupName: "eksctl-epifi-qa-cluster-ControlPlaneSecurityGroup-1JMEP335BGJWV", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{"Subsystem": "Temporal"},
}
