package cc_securitygroups_qa

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var qaLocalSshRules = &securitygroups.SGIR{
	Name:        "qa-local-ssh",
	Account:     cfg.QA,
	VpcId:       "vpc-020996fb61b6f695d",
	Description: "All local ssh from VPN",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "VPN access",
			Protocol:    "tcp",
			FromPort:    22,
			ToPort:      22,
			SourceDest:  &securitygroups.SrvObject{Cidr: "10.0.0.0/8", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{},
}
