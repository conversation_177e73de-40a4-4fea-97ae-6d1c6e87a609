package cc_securitygroups_qa

import (
	"github.com/epiFi/go-infra/pkg/aws/securitygroups"
	"github.com/epiFi/go-infra/pkg/cfg"
)

var qaDocsRules = &securitygroups.SGIR{
	Name:        "qa-docs",
	Account:     cfg.QA,
	VpcId:       "vpc-020996fb61b6f695d",
	Description: "Security Group for docs Service",
	IngressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Deploy",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "docs Service App Port",
			Protocol:    "tcp",
			FromPort:    9530,
			ToPort:      9530,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Consul",
			Protocol:    "udp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Deploy",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "securitygroup",
			Egress:      false,
			Description: "Allow inbound from own security group",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "", Account: cfg.QA, SecurityGroupName: "qa-docs", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      false,
			Description: "Inbound from Consul",
			Protocol:    "tcp",
			FromPort:    8300,
			ToPort:      8301,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	EgressRules: []*securitygroups.SGR{
		{
			Type:        "cidr",
			Egress:      true,
			Description: "Outbound to Internet",
			Protocol:    "-1",
			FromPort:    -1,
			ToPort:      -1,
			SourceDest:  &securitygroups.SrvObject{Cidr: "0.0.0.0/0", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},

		{
			Type:        "cidr",
			Egress:      true,
			Description: "docs Outbound VPC",
			Protocol:    "tcp",
			FromPort:    0,
			ToPort:      65535,
			SourceDest:  &securitygroups.SrvObject{Cidr: "*********/16", Account: cfg.InvalidAccount, SecurityGroupName: "", PrefixList: ""},
		},
	},
	Tags: map[string]string{"Name": "qa-docs",
		"TFModVersion": "V3",
	},
}
