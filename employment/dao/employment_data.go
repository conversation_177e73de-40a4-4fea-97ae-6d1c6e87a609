package dao

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/employment/dao/model"
)

var (
	EmploymentDataActorIdNotPresentError = fmt.Errorf("actorId unspecified")
	EmploymentDataNotPresentError        = fmt.Errorf("employment data not present")
	EmploymentDataUpdateMaskEmptyErr     = fmt.Errorf("update mask array is empty")
	EmploymentDataIdNotPresentError      = fmt.Errorf("id is empty")
)

var EmploymentDataColumnName = map[employmentPb.EmploymentDataFieldMask]string{
	employmentPb.EmploymentDataFieldMask_ID:                                         "id",
	employmentPb.EmploymentDataFieldMask_ACTOR_ID:                                   "actor_id",
	employmentPb.EmploymentDataFieldMask_EMPLOYMENT_TYPE:                            "employment_type",
	employmentPb.EmploymentDataFieldMask_EMPLOYMENT_INFO:                            "employment_info",
	employmentPb.EmploymentDataFieldMask_VERIFICATION_PROCESS_STATUS:                "verification_process_status",
	employmentPb.EmploymentDataFieldMask_VERIFICATION_RESULT:                        "verification_result",
	employmentPb.EmploymentDataFieldMask_HOLD_REASON:                                "hold_reason",
	employmentPb.EmploymentDataFieldMask_REJECTION_REASON:                           "rejection_reason",
	employmentPb.EmploymentDataFieldMask_CREATED_AT:                                 "created_at",
	employmentPb.EmploymentDataFieldMask_UPDATED_AT:                                 "updated_at",
	employmentPb.EmploymentDataFieldMask_DELETED_AT:                                 "deleted_at",
	employmentPb.EmploymentDataFieldMask_ORG_PF_DATA:                                "org_pf_data",
	employmentPb.EmploymentDataFieldMask_ACCEPTED_REASON:                            "accepted_reason",
	employmentPb.EmploymentDataFieldMask_PROCESSING_INTENT:                          "processing_intent",
	employmentPb.EmploymentDataFieldMask_EMPLOYER_ID:                                "employer_id",
	employmentPb.EmploymentDataFieldMask_UPDATED_BY_SOURCE:                          "updated_by_source",
	employmentPb.EmploymentDataFieldMask_UPDATED_BY_SOURCE_IDENTIFIER:               "updated_by_source_identifier",
	employmentPb.EmploymentDataFieldMask_EMPLOYMENT_DATA_FIELD_MASK_OCCUPATION_TYPE: "occupation_type",
}

// Implements EmploymentDataDao using CRDB
type EmploymentDataDaoCrdb struct {
	db types.EpifiCRDB
}

func NewEmploymentDataDaoCrdb(db types.EpifiCRDB) *EmploymentDataDaoCrdb {
	return &EmploymentDataDaoCrdb{
		db: db,
	}
}

func (s *EmploymentDataDaoCrdb) CreateEmploymentData(ctx context.Context,
	empData *employmentPb.EmploymentData) (*employmentPb.EmploymentData, error) {
	defer metric_util.TrackDuration("employment/dao", "EmploymentDataDaoCrdb", "CreateEmploymentData", time.Now())
	if empData == nil {
		return nil, EmploymentDataNotPresentError
	}
	if empData.GetActorId() == "" {
		return nil, EmploymentDataActorIdNotPresentError
	}
	m := Marshal(empData)

	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection provided by the dao.
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	res := db.Create(m)
	if res.Error != nil {
		return nil, fmt.Errorf("error creating employment data entry: %w", res.Error)
	}
	return Unmarshal(res.Statement.Model.(*model.EmploymentData)), nil
}

func (s *EmploymentDataDaoCrdb) GetByActorId(ctx context.Context, actorId string) (*employmentPb.EmploymentData, error) {
	defer metric_util.TrackDuration("employment/dao", "EmploymentDataDaoCrdb", "GetByActorId", time.Now())
	if actorId == "" {
		return nil, EmploymentDataActorIdNotPresentError
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var empData model.EmploymentData
	if err := db.Where("actor_id = ?", actorId).Take(&empData).Error; err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error fetching employment data for actor: %w", err)
	}
	return Unmarshal(&empData), nil
}

// getSelectColumnsForUpdateOrder converts update mask to string slice with column
// names corresponding to field name enums
func selectedColumnsForUpdate(updateMask []employmentPb.EmploymentDataFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMask {
		selectColumns = append(selectColumns, EmploymentDataColumnName[field])
	}
	return selectColumns
}

// Update should contain update_by_source and update_by_source_identifier field masks
// in case either is mentioned in the list of fields to update
func (s *EmploymentDataDaoCrdb) Update(ctx context.Context, empData *employmentPb.EmploymentData,
	updateMask []employmentPb.EmploymentDataFieldMask) error {
	defer metric_util.TrackDuration("employment/dao", "EmploymentDataDaoCrdb", "Update", time.Now())
	if empData.GetActorId() == "" {
		return EmploymentDataActorIdNotPresentError
	}
	if len(updateMask) == 0 {
		return EmploymentDataUpdateMaskEmptyErr
	}
	updateColumns := selectedColumnsForUpdate(updateMask)
	m := Marshal(empData)
	whereClause := &model.EmploymentData{ActorId: empData.ActorId}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	return db.Model(m).Where(whereClause).Select(updateColumns).Updates(m).Error
}

func (s *EmploymentDataDaoCrdb) DeleteByActorId(ctx context.Context, actorId string) error {
	defer metric_util.TrackDuration("employment/dao", "EmploymentDataDaoCrdb", "DeleteByActorId", time.Now())
	if actorId == "" {
		return EmploymentDataIdNotPresentError
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	err := db.Where(&model.EmploymentData{ActorId: actorId}).Delete(&model.EmploymentData{}).Error
	if storagev2.IsRecordNotFoundError(err) {
		return nil
	}
	return err
}

func Marshal(o *employmentPb.EmploymentData) *model.EmploymentData {
	a := &model.EmploymentData{
		Id:                        o.GetId(),
		ActorId:                   o.GetActorId(),
		EmploymentType:            o.GetEmploymentType(),
		EmploymentInfo:            o.GetEmploymentInfo(),
		VerificationProcessStatus: o.GetVerificationProcessStatus(),
		VerificationResult:        o.GetVerificationResult(),
		HoldReason:                o.GetHoldReason(),
		RejectionReason:           o.GetRejectionReason(),
		AcceptedReason:            o.GetAcceptedReason(),
		OrgPfData:                 o.GetOrgPfData(),
		ProcessingIntent:          o.GetProcessingIntent(),
		EmployerId:                o.GetEmployerId(),
		UpdatedBySource:           o.GetUpdatedBySource(),
		UpdatedBySourceIdentifier: o.GetUpdatedBySourceIdentifier(),
		OccupationType:            o.GetOccupationType(),
	}
	return a
}

func Unmarshal(o *model.EmploymentData) *employmentPb.EmploymentData {
	a := &employmentPb.EmploymentData{
		ActorId:                   o.ActorId,
		Id:                        o.Id,
		EmploymentType:            o.EmploymentType,
		EmploymentInfo:            o.EmploymentInfo,
		VerificationProcessStatus: o.VerificationProcessStatus,
		VerificationResult:        o.VerificationResult,
		HoldReason:                o.HoldReason,
		RejectionReason:           o.RejectionReason,
		AcceptedReason:            o.AcceptedReason,
		ProcessingIntent:          o.ProcessingIntent,
		OrgPfData:                 o.OrgPfData,
		EmployerId:                o.EmployerId,
		CreatedAt:                 timestamppb.New(o.CreatedAt),
		UpdatedAt:                 timestamppb.New(o.UpdatedAt),
		UpdatedBySource:           o.UpdatedBySource,
		UpdatedBySourceIdentifier: o.UpdatedBySourceIdentifier,
		OccupationType:            o.OccupationType,
	}
	if o.DeletedAt.Valid {
		a.DeletedAt = timestamppb.New(o.DeletedAt.Time)
	}
	return a
}
