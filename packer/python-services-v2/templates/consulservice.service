[Unit]
Description=consulservice
ConditionPathExists=/home/<USER>/consulservice/scripts/start.sh
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/consulservice/
LimitNOFILE=65535
Restart=on-failure
RestartSec=10
Environment=PYTHONPATH=/home/<USER>/consulservice/
ExecStart=/home/<USER>/consulservice/scripts/start.sh

# make sure log directory exists and owned by syslog
StandardOutput=file:/var/log/$TARGETAPP/info.log
StandardError=file:/var/log/$TARGETAPP/error.log
SyslogIdentifier=$TARGETAPP

[Install]
WantedBy=default.target
