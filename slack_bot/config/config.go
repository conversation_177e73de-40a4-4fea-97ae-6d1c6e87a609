package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	_, b, _, _ = runtime.Caller(0)
	once       sync.Once
	config     *Config
	err        error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	conf := &Config{}
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.SLACK_BOT_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh config: %w", err)
	}

	err = cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}

	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

//go:generate conf_gen github.com/epifi/gamma/slack_bot/config Config
type Config struct {
	Application          *cfg.Application
	Disable              bool `dynamic:"true"`
	UseSecureLogs        bool `dynamic:"true"`
	ReviewersFilePath    map[string]string
	ChannelsDetail       map[string]*ChannelDetails `dynamic:"true"`
	Aws                  *Aws
	RetryStrategy        *RetryStrategy
	VgConnectionStrategy *VgConnectionStrategy
	MonorailConfig       *cfg.MonorailConfig
	DefaultReply         []string
	SecureClusterDetails map[string]*SecureClusterDetails
}
type ClusterSecrets struct {
	ClusterUsernamePassword string `iam:"sm-read"`
	Username                string `field:"ClusterUsernamePassword" jsonPath:"username"`
	Password                string `field:"ClusterUsernamePassword" jsonPath:"password"`
}
type SecureClusterDetails struct {
	SecureLogsCluster *cfg.ES
	RetentionDays     int
	AuthMethod        string
	RoleArn           string
	ClusterSecrets    *ClusterSecrets
	SecureIndex       string
}
type ChannelDetails struct {
	Id                 string
	MonorailLabel      string
	BUToOnCallMap      map[string]string `dynamic:"true"`
	BUToTicketOwnerMap map[string]string `dynamic:"true"`
	IssuePreface       string            `dynamic:"true"`
	PreferredBU        string
}

type Aws struct {
	Region string
}

type RetryStrategy struct {
	ExponentialBackOff *ExponentialBackOff
	MaxAttempts        int
	MaxDeadLine        time.Duration
}

type ExponentialBackOff struct {
	BaseInterval time.Duration
	MaxBackOff   time.Duration
}

type VgConnectionStrategy struct {
	VgConnectionRetryInterval  time.Duration
	VgStreamConnectionDuration time.Duration
}
