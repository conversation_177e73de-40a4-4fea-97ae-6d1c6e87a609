package worker

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	"github.com/rudderlabs/analytics-go"
	"go.temporal.io/sdk/worker"
	"go.uber.org/fx"
	"go.uber.org/zap"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	commonfx "github.com/epifi/be-common/pkg/epifitemporal/fx"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	bcPb "github.com/epifi/gamma/api/bankcust"
	userPb "github.com/epifi/gamma/api/user"
	vgBcPb "github.com/epifi/gamma/api/vendorgateway/openbanking/bank_customer"
	bcworkerconfig "github.com/epifi/gamma/bankcust/config/worker"
	bcworkergenconfig "github.com/epifi/gamma/bankcust/config/worker/genconf"
	bcWire "github.com/epifi/gamma/bankcust/wire"
	bcWorkflow "github.com/epifi/gamma/bankcust/workflow"
	epifitemporalfx "github.com/epifi/gamma/pkg/epifitemporal/fx"
)

func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	app := fx.New(
		fx.Supply(initNotifier),
		fx.Supply(cfg.BANK_CUSTOMER_WORKER_SERVER),
		fx.Supply(cfg.BANK_CUSTOMER_WORKER_SERVICE),
		fx.Supply(bcworkerconfig.Load),
		fx.Supply(bcworkergenconfig.NewConfig),
		commonfx.GetConfigProviderOption[*bcworkergenconfig.Config, *bcworkerconfig.Config](),
		epifitemporalfx.FxWorkerModule,
		fx.Decorate(initBankCustomerWorker),
	)

	app.Run()
	return nil
}

type initBankCustomerWorkerParams struct {
	fx.In

	Lc             fx.Lifecycle
	Worker         worker.Worker                             `name:"Worker"`
	Conf           *bcworkerconfig.Config                    `name:"Conf"`
	DbConnProvider *storageV2.DBResourceProvider[*gormV2.DB] `name:"DbConnProvider"`
}

type initBankCustomerWorkerResult struct {
	fx.Out

	Worker worker.Worker `name:"Worker"`
}

func initBankCustomerWorker(p initBankCustomerWorkerParams) (initBankCustomerWorkerResult, error) {
	db, err := p.DbConnProvider.GetResourceForOwnership(commontypes.Ownership_EPIFI_TECH)
	if err != nil {
		logger.Panic("failed to get epifi db conn", zap.Error(err))
	}

	storageV2.InitDefaultCRDBTransactionExecutor(db)

	closure := registerDomainActivities(p.Worker, p.Conf)
	p.Lc.Append(fx.Hook{
		OnStop: func(context.Context) error {
			closure()
			return nil
		},
	})

	// Register workflows
	p.Worker.RegisterWorkflow(bcWorkflow.ProvisionChequebook)
	p.Worker.RegisterWorkflow(bcWorkflow.ProfileUpdate)

	return initBankCustomerWorkerResult{Worker: p.Worker}, nil

}

func registerDomainActivities(w worker.Worker, conf *bcworkerconfig.Config) func() {
	bcConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	BcClient := bcPb.NewBankCustomerServiceClient(bcConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	vgBcClient := vgBcPb.NewBankCustomerClient(vgConn)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	userClient := userPb.NewUsersClient(userConn)

	// Setup rudderstack client
	broker, err := initRudderClient(conf)
	if err != nil {
		logger.ErrorNoCtx("failed to initialize rudder client")
		panic(err)
	}

	bcActivityProcessor := bcWire.InitializeActivityProcessor(conf, BcClient, vgBcClient, userClient, broker)

	// todo(vineet): register activity here
	w.RegisterActivity(bcActivityProcessor)
	return func() {
		epifigrpc.CloseConn(bcConn)
		broker.Close()
	}
}

func initRudderClient(conf *bcworkerconfig.Config) (events.Broker, error) {
	// load rudder-stack broker for sending events
	client, err := analytics.NewWithConfig(
		conf.Secrets.Ids["RudderWriteKey"], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  conf.RudderStack.IntervalInSec * time.Second,
			BatchSize: conf.RudderStack.BatchSize,
			Verbose:   conf.RudderStack.Verbose,
		})
	if err != nil {
		return nil, err
	}
	broker := events.NewRudderStackBroker(client)
	return broker, nil
}
