Databases:
  SherlockPGDB:
    StatementTimeout: 5m
    DbType: "PGDB"
    Name: "sherlock"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/postgres14"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

  InapphelpPGDB:
    StatementTimeout: 5m
    DbType: "PGDB"
    Name: "inapphelp"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/postgres14"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

  CasbinSherlockPGDBv1:
    DbType: "PGDB"
    StatementTimeout: 5m
    Name: "sherlock"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/postgres14"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

RedisRateLimiterName: "CxRedisStore"

RedisClusters:
  CxRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      # on staging we are using common redis cluster with a different db
      DB: 0

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    RedisDB: 0
    ClientName: "cx-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: true
