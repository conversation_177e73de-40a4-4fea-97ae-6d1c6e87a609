ServerPorts:
  HttpPort: 9899

GrpcRateLimiterParams:
  Disable: true

QuestSdk:
  Disable: false

Databases:
  EpifiCRDB:
    StatementTimeout: 5m
    DbType: "CRDB"
    Name: "epifi_test"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    EnableDebug: true
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 1m
      UseInsecureLog: true

  FeatureEngineeringPGDB:
    AppName: "user"
    StatementTimeout: 5m
    Name: "feature_engineering_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    DbType: "PGDB"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: false

  LoansFederalPGDB:
    StatementTimeout: 5m
    Name: "loans_federal_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    DbType: "PGDB"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 1ms
      UseInsecureLog: true

  CreditRiskPGDB:
    DbType: "PGDB"
    AppName: "epfo"
    StatementTimeout: 5s
    Name: "credit_risk_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

DBConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_federal_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  LIQUILOANS_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_liquiloans_test"
    StatementTimeout: 1m
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  IDFC_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_idfc_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  FIFTYFIN_LAMF:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_fiftyfin_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  MONEYVIEW_PL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_moneyview_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  LOANS_ABFL:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_abfl_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  LOANS_LENDEN:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_lenden_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  LOANS_STOCK_GUARDIAN_LSP:
    DbType: "PGDB"
    AppName: "preapprovedloan"
    Host: "localhost"
    Port: 5432
    Name: "loans_stock_guardian_lsp_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  EPIFI_TECH_V2:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Name: "loans_epifi_tech_test"
    EnableDebug: false
    StatementTimeout: 1m
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

RedisRateLimiterName: "FireflyRedisStore"
RedisClusters:
  FireflyRedisStore:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    ClientName: firefly
    HystrixCommand:
      CommandName: "firefly_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 200
        ExecutionTimeout: 100ms
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80

Secrets:
  Ids:
    TemporalCodecAesKey: "853bbce933313713af7f43bb8fcc1d84"

RueidisRedisClients:
  CollapserRueidisRedisStore:
    Addrs:
      - "localhost:6379"
    RedisDB: 0
    ClientName: "lending-collapser"
    Hystrix:
      CommandName: "collapser_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 2500
        ExecutionTimeout: 1s
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
    EnableSecureRedis: false
