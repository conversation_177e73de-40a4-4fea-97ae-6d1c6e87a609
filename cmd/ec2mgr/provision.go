package main

import (
	"errors"
	"fmt"

	cc_ec2mgr_crdb "github.com/epiFi/go-infra/codeconf/ec2mgr/crdb"
	cc_ec2mgr_fluentbit "github.com/epiFi/go-infra/codeconf/ec2mgr/log-aggregator"
	"github.com/epiFi/go-infra/pkg/aws/ec2server"
)

func provisionJenkins(ec2srvP *ec2server.Processor, server *ec2server.SrvTemplate) error {
	if err := ec2srvP.ConvertLaunchTemplate(server.LaunchTemplate); err != nil {
		return fmt.Errorf("error converting launch template: %w", err)
	}
	if err := ec2srvP.CreateLaunchTemplate(server); err != nil {
		return fmt.Errorf("error creating launch template: %w", err)
	}
	if err := ec2srvP.CreateEC2InstanceFromLT(server.LaunchTemplate.Name, ""); err != nil {
		return fmt.Errorf("error creating instance: %w", err)
	}
	return nil
}

func provisionFluentbit(ec2srvP *ec2server.Processor, env string) error {
	if env == "deploy" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_fluentbit.FluentBitSrvDeploy.LaunchTemplate); err != nil {
			return err
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_fluentbit.FluentBitSrvDeploy); err != nil {
			return err
		}
	} else if env == "prod" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_fluentbit.FluentBitSrvProd.LaunchTemplate); err != nil {
			return err
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_fluentbit.FluentBitSrvProd); err != nil {
			return err
		}
	} else if env == "data-prod" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_fluentbit.FluentBitSrvDataProd.LaunchTemplate); err != nil {
			return err
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_fluentbit.FluentBitSrvDataProd); err != nil {
			return err
		}
	} else {
		return errors.New("invalid environment passed")
	}
	return nil
}

func provisionSftp(ec2srvP *ec2server.Processor, server *ec2server.SrvTemplate) error {
	if err := ec2srvP.ConvertLaunchTemplate(server.LaunchTemplate); err != nil {
		return fmt.Errorf("error converting launch template: %w", err)
	}
	if err := ec2srvP.CreateLaunchTemplate(server); err != nil {
		return fmt.Errorf("error creating launch template: %w", err)
	}
	return nil
}

func provisionKeycloak(ec2srvP *ec2server.Processor, server *ec2server.SrvTemplate) error {
	if err := ec2srvP.ConvertLaunchTemplate(server.LaunchTemplate); err != nil {
		return fmt.Errorf("error converting launch template: %w", err)
	}
	if err := ec2srvP.CreateLaunchTemplate(server); err != nil {
		return fmt.Errorf("error creating launch template: %w", err)
	}
	return nil
}

func provisionCrdb(ec2srvP *ec2server.Processor, env, node string) error {
	if environment == "staging" {
		return provisionCrdbStaging(ec2srvP, env, node)
	} else if environment == "qa" {
		return provisionCrdbQa(ec2srvP, env, node)
	} else if environment == "uat" {
		return provisionCrdbUat(ec2srvP, env, node)
	} else if environment == "prod" {
		return provisionCrdbProd(ec2srvP, env, node)
	}
	return errors.New("invalid environment specified")
}

func provisionCrdbProd(ec2srvP *ec2server.Processor, env, node string) error {
	if node == "26" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb26.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb26); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "27" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb27.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb27); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "28" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb28.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb28); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "29" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb29.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb29); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "30" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb30.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb30); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "31" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb31.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdb31); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "rt1" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdbRT1.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdbRT1); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "rt2" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdbRT2.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdbRT2); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "rt3" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.ProdCrdbRT3.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.ProdCrdbRT3); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else {
		return errors.New("invalid node specified for environment")
	}
	return nil
}

func provisionCrdbQa(ec2srvP *ec2server.Processor, env, node string) error {
	if node == "7" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.QaCrdb7.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.QaCrdb7); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "3" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.QaCrdb3.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.QaCrdb3); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else if node == "4" {
		if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.QaCrdb4.LaunchTemplate); err != nil {
			return fmt.Errorf("error converting launch template: %w", err)
		}
		if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.QaCrdb4); err != nil {
			return fmt.Errorf("error creating launch template: %w", err)
		}
	} else {
		return errors.New("invalid node specified for environment")
	}
	return nil
}

func provisionCrdbStaging(ec2srvP *ec2server.Processor, _, _ string) error {
	if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.StagingCrdb1.LaunchTemplate); err != nil {
		return fmt.Errorf("error converting launch template: %w", err)
	}
	if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.StagingCrdb1); err != nil {
		return fmt.Errorf("error creating launch template: %w", err)
	}
	return nil
}

func provisionCrdbUat(ec2srvP *ec2server.Processor, _, _ string) error {
	if err := ec2srvP.ConvertLaunchTemplate(cc_ec2mgr_crdb.UatCrdb1.LaunchTemplate); err != nil {
		return fmt.Errorf("error converting launch template: %w", err)
	}
	if err := ec2srvP.CreateLaunchTemplate(cc_ec2mgr_crdb.UatCrdb1); err != nil {
		return fmt.Errorf("error creating launch template: %w", err)
	}
	return nil
}
