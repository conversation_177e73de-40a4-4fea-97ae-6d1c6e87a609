package vendornotification

import (
	"github.com/epifi/be-common/tools/servergen/meta"

	leadsExternalPb "github.com/epifi/gamma/api/leads/external"
	aa "github.com/epifi/gamma/api/vendornotification/aa"
	igVnPb "github.com/epifi/gamma/api/vendornotification/aa/analytics/ignosis"
	tss "github.com/epifi/gamma/api/vendornotification/aml/tss"
	authPb "github.com/epifi/gamma/api/vendornotification/auth"
	shipwaycardpb "github.com/epifi/gamma/api/vendornotification/card/shipway"
	"github.com/epifi/gamma/api/vendornotification/comms/unsubscribe"
	ccpb "github.com/epifi/gamma/api/vendornotification/creditcard"
	"github.com/epifi/gamma/api/vendornotification/creditcard/paisabazaar"
	savenVnPb "github.com/epifi/gamma/api/vendornotification/creditcard/saven"
	chatbotauthpb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/auth/senseforth"
	freshchat2 "github.com/epifi/gamma/api/vendornotification/cx/chatbot/inapphelp/freshchat"
	senseforth "github.com/epifi/gamma/api/vendornotification/cx/chatbot/livechatfallback/senseforth"
	vnchatbotworkflowpb "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth"
	cxEscalationPb "github.com/epifi/gamma/api/vendornotification/cx/federal"
	freshchat "github.com/epifi/gamma/api/vendornotification/cx/freshchat"
	ozonetel "github.com/epifi/gamma/api/vendornotification/cx/ozonetel"
	sprinklrPb "github.com/epifi/gamma/api/vendornotification/cx/sprinklr"
	email "github.com/epifi/gamma/api/vendornotification/email"
	epanVnPb "github.com/epifi/gamma/api/vendornotification/epan/karza"
	fiCoinsAccVnPb "github.com/epifi/gamma/api/vendornotification/fi_coins_accounting"
	riskcovryvnpb "github.com/epifi/gamma/api/vendornotification/healthinsurance/riskcovry"
	"github.com/epifi/gamma/api/vendornotification/investment/smallcase"
	axisekyc "github.com/epifi/gamma/api/vendornotification/kyc/axis"
	vnIdfcPb "github.com/epifi/gamma/api/vendornotification/kyc/idfc"
	inhouseBreVnPb "github.com/epifi/gamma/api/vendornotification/lending/bre/inhouse"
	credgenicsVnPb "github.com/epifi/gamma/api/vendornotification/lending/credgenics"
	"github.com/epifi/gamma/api/vendornotification/lending/loans/abfl"
	lendingFederalPb "github.com/epifi/gamma/api/vendornotification/lending/loans/federal"
	fiftyfinVnPb "github.com/epifi/gamma/api/vendornotification/lending/loans/fiftyfin"
	"github.com/epifi/gamma/api/vendornotification/lending/loans/moneyview"
	"github.com/epifi/gamma/api/vendornotification/lending/setu"
	karza "github.com/epifi/gamma/api/vendornotification/liveness/karza"
	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	offersVnPb "github.com/epifi/gamma/api/vendornotification/offers/externalredemptions"
	axisaccounts "github.com/epifi/gamma/api/vendornotification/openbanking/accounts/axis"
	federalaccountspb "github.com/epifi/gamma/api/vendornotification/openbanking/accounts/federal"
	federalauthpb "github.com/epifi/gamma/api/vendornotification/openbanking/auth/federal"
	federalcardpb "github.com/epifi/gamma/api/vendornotification/openbanking/card/federal"
	federaldepositpb "github.com/epifi/gamma/api/vendornotification/openbanking/deposit/federal"
	federalDmpPb "github.com/epifi/gamma/api/vendornotification/openbanking/dispute"
	federalkyctypechangepb "github.com/epifi/gamma/api/vendornotification/openbanking/kyctypechange/federal"
	axispayment "github.com/epifi/gamma/api/vendornotification/openbanking/payment/axis"
	federalpaymentpb "github.com/epifi/gamma/api/vendornotification/openbanking/payment/federal"
	rpFederalVnPb "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	federalshippingaddressupdatepb "github.com/epifi/gamma/api/vendornotification/openbanking/shipping_preference/federal"
	razorpayVnPb "github.com/epifi/gamma/api/vendornotification/paymentgateway/razorpay"
	gupshupRcsVnPb "github.com/epifi/gamma/api/vendornotification/rcs/gupshup"
	rewardPb "github.com/epifi/gamma/api/vendornotification/reward"
	aclpb "github.com/epifi/gamma/api/vendornotification/sms/acl"
	airtelVnPb "github.com/epifi/gamma/api/vendornotification/sms/airtel"
	kaleyrapb "github.com/epifi/gamma/api/vendornotification/sms/kaleyra"
	netcoreVnPb "github.com/epifi/gamma/api/vendornotification/sms/netcore"
	"github.com/epifi/gamma/api/vendornotification/videocall/videosdk"
	karza2 "github.com/epifi/gamma/api/vendornotification/vkyc/karza"
	aclwapb "github.com/epifi/gamma/api/vendornotification/whatsapp/acl"
	airtelWpVnPb "github.com/epifi/gamma/api/vendornotification/whatsapp/airtel"
	gupshupVnPb "github.com/epifi/gamma/api/vendornotification/whatsapp/gupshup"
	leadsExternalWire "github.com/epifi/gamma/leads/external/wire"
	wire "github.com/epifi/gamma/vendornotification/wire"
)

var shouldInitQuest = true

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeFederalPayService,
		GRPCRegisterMethods: []any{federalpaymentpb.RegisterPaymentServer},
	},
	{
		WireInitializer:     wire.InitializeFederalCardService,
		GRPCRegisterMethods: []any{federalcardpb.RegisterCardServer},
	},
	{
		WireInitializer:     wire.InitializeKarzaLivenessService,
		GRPCRegisterMethods: []any{karza.RegisterLivenessServer},
	},
	{
		WireInitializer:     wire.InitializeSmsAclCallbackService,
		GRPCRegisterMethods: []any{aclpb.RegisterSmsCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeKaleyraCallbackService,
		GRPCRegisterMethods: []any{kaleyrapb.RegisterKaleyraCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeWhatsappAclCallbackService,
		GRPCRegisterMethods: []any{aclwapb.RegisterWhatsappCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeVKYCKarzaCallbackService,
		GRPCRegisterMethods: []any{karza2.RegisterVKYCKarzaServer},
	},
	{
		WireInitializer:     wire.InitializeShippingAddressUpdateCallbackService,
		GRPCRegisterMethods: []any{federalshippingaddressupdatepb.RegisterShippingPreferenceServer},
	},
	{
		WireInitializer:     wire.InitializeFederalDepositService,
		GRPCRegisterMethods: []any{federaldepositpb.RegisterDepositServer},
	},
	{
		WireInitializer:     wire.InitializeKycTypeChangeCallbackService,
		GRPCRegisterMethods: []any{federalkyctypechangepb.RegisterKycTypeChangeServer},
	},
	{
		WireInitializer:     wire.InitializeAccountsCallbackService,
		GRPCRegisterMethods: []any{federalaccountspb.RegisterAccountsServer},
	},
	{
		WireInitializer:     wire.InitializeFederalAuthService,
		GRPCRegisterMethods: []any{federalauthpb.RegisterAuthServer},
	},
	{
		WireInitializer:     wire.InitializeEmailCallbackService,
		GRPCRegisterMethods: []any{email.RegisterEmailCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeAANotificationService,
		GRPCRegisterMethods: []any{aa.RegisterAANotificationServer},
	},
	{
		WireInitializer:     wire.InitialiseShipwayCardService,
		GRPCRegisterMethods: []any{shipwaycardpb.RegisterShipwayServer},
	},
	{
		WireInitializer:     wire.InitializeOzonetelCallRoutingService,
		GRPCRegisterMethods: []any{ozonetel.RegisterCallRoutingServer},
	},
	{
		WireInitializer:     wire.InitializeAxisAccountsCallBackService,
		GRPCRegisterMethods: []any{axisaccounts.RegisterAccountsServer},
	},
	{
		WireInitializer:     wire.InitializeAxisPaymentCallBackService,
		GRPCRegisterMethods: []any{axispayment.RegisterPaymentServer},
	},
	{
		WireInitializer:     wire.InitializeAxisEKycCallBackService,
		GRPCRegisterMethods: []any{axisekyc.RegisterEkycServer},
	},
	{
		WireInitializer:     wire.InitializeSenseforthChatBotAuthService,
		GRPCRegisterMethods: []any{chatbotauthpb.RegisterChatBotAuthServer},
	},
	{
		WireInitializer:     wire.InitializeSenseforthLiveChatFallbackService,
		GRPCRegisterMethods: []any{senseforth.RegisterLiveChatFallbackServer},
	},
	{
		WireInitializer:     wire.InitializeFreshchatCallbackService,
		GRPCRegisterMethods: []any{freshchat.RegisterFreshchatCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeM2PCreditCardService,
		GRPCRegisterMethods: []any{ccpb.RegisterCreditCardServer},
	},
	{
		WireInitializer:     wire.InitializeChatbotWorkflowService,
		GRPCRegisterMethods: []any{vnchatbotworkflowpb.RegisterChatBotWorkflowServer},
	},
	{
		WireInitializer:     wire.InitializeTssWebhookCallBackService,
		GRPCRegisterMethods: []any{tss.RegisterTSSServer},
	},
	{
		WireInitializer:     wire.InitializeRiskcovryCallbackService,
		GRPCRegisterMethods: []any{riskcovryvnpb.RegisterRiskcovryCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeSmallcaseWebhookService,
		GRPCRegisterMethods: []any{smallcase.RegisterSmallcaseServer},
	},
	{
		WireInitializer:     wire.InitializeFederalDmpService,
		GRPCRegisterMethods: []any{federalDmpPb.RegisterDmpServer},
	},
	{
		WireInitializer:     wire.InitializeInhouseBreService,
		GRPCRegisterMethods: []any{inhouseBreVnPb.RegisterBreServer},
	},
	{
		WireInitializer:     wire.InitializeEPANCallbackService,
		GRPCRegisterMethods: []any{epanVnPb.RegisterEPANServer},
	},
	{
		WireInitializer:     wire.InitializeFiCoinsAccountingService,
		GRPCRegisterMethods: []any{fiCoinsAccVnPb.RegisterFiCoinsAccountingServer},
	},
	{
		WireInitializer:     wire.InitializeFiftyfinNotificationService,
		GRPCRegisterMethods: []any{fiftyfinVnPb.RegisterFiftyFinCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeIdfcKycCallbackService,
		GRPCRegisterMethods: []any{vnIdfcPb.RegisterIdfcServer},
	},
	{
		WireInitializer:     wire.InitializeLendingFederalInboundService,
		GRPCRegisterMethods: []any{lendingFederalPb.RegisterLoansServer},
	},
	{
		WireInitializer:     wire.InitializeMoengageService,
		GRPCRegisterMethods: []any{moengageVnPb.RegisterMoengageServer},
	},
	{
		WireInitializer:     wire.InitialiseRecurringPaymentFederalService,
		GRPCRegisterMethods: []any{rpFederalVnPb.RegisterRecurringPaymentServer},
	},
	{
		WireInitializer:     wire.InitializeSprinklrEventHandlingService,
		GRPCRegisterMethods: []any{sprinklrPb.RegisterSprinklrEventHandlingServer},
	},
	{
		WireInitializer:     wire.InitializeAbflNotificationService,
		GRPCRegisterMethods: []any{abfl.RegisterAbflCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeSetuNotificationService,
		GRPCRegisterMethods: []any{setu.RegisterSetuCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeMoneyviewCallbackService,
		GRPCRegisterMethods: []any{moneyview.RegisterMoneyviewCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeExternalOfferRedemptionsService,
		GRPCRegisterMethods: []any{offersVnPb.RegisterExternalRedemptionsServer},
	},
	{
		WireInitializer:     wire.InitializeCommsUnsubscribeService,
		GRPCRegisterMethods: []any{unsubscribe.RegisterUnsubscribeServer},
	},
	{
		WireInitializer:     wire.InitializeCredgenicsWebhookService,
		GRPCRegisterMethods: []any{credgenicsVnPb.RegisterCredgenicsWebhookServer},
	},
	{
		WireInitializer:     wire.InitializeWhatsappGupshupCallbackService,
		GRPCRegisterMethods: []any{gupshupVnPb.RegisterGupshupCallbackServer},
	},
	{
		WireInitializer:     wire.InitializePaisabazaarCallBackService,
		GRPCRegisterMethods: []any{paisabazaar.RegisterPaisabazaarCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeRcsGupshupCallbackService,
		GRPCRegisterMethods: []any{gupshupRcsVnPb.RegisterGupshupRcsCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeVideoSdkService,
		GRPCRegisterMethods: []any{videosdk.RegisterVideoSdkCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeNetCoreCallbackService,
		GRPCRegisterMethods: []any{netcoreVnPb.RegisterNetCoreCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeRazorpayInboundEventService,
		GRPCRegisterMethods: []any{razorpayVnPb.RegisterRazorpayPaymentGatewayServer},
	},
	{
		WireInitializer:     wire.InitializeAirtelCallbackService,
		GRPCRegisterMethods: []any{airtelVnPb.RegisterAirtelSMSCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeAirtelWhatsappCallbackService,
		GRPCRegisterMethods: []any{airtelWpVnPb.RegisterAirtelCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeIgnosisAaService,
		GRPCRegisterMethods: []any{igVnPb.RegisterIgnosisAaAnalyticsNotificationServer},
	},
	{
		WireInitializer:     wire.InitializeFederalEventHandlingService,
		GRPCRegisterMethods: []any{cxEscalationPb.RegisterFederalEscalationHandlingServer},
	},
	{
		WireInitializer:     wire.InitializeSavenCallBackService,
		GRPCRegisterMethods: []any{savenVnPb.RegisterSavenCallbackServer},
	},
	{
		WireInitializer:     wire.InitializeAuthService,
		GRPCRegisterMethods: []any{authPb.RegisterAuthServer},
	},
	{
		WireInitializer:     wire.InitializeRewardService,
		GRPCRegisterMethods: []any{rewardPb.RegisterRewardServer},
	},
	{
		WireInitializer:     leadsExternalWire.InitialiseExternalLeadsService,
		GRPCRegisterMethods: []any{leadsExternalPb.RegisterLeadServer},
	},
	{
		WireInitializer:     wire.InitializeFreshchatAIBotService,
		GRPCRegisterMethods: []any{freshchat2.RegisterFreshchatAIBotServiceServer},
	},
}
