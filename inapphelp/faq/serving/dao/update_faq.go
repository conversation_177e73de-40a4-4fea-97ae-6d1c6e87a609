package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	sPb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	solPb "github.com/epifi/gamma/api/vendorgateway/cx/solutions"
	"github.com/epifi/gamma/inapphelp/faq/processor/dao/model"
)

var CategoryColumns = map[sPb.CategoryFieldMask]string{
	sPb.CategoryFieldMask_CATEGORY_ICON_URL:   "icon_url",
	sPb.CategoryFieldMask_CATEGORY_RANK:       "rank",
	sPb.CategoryFieldMask_CATEGORY_VISIBILITY: "visibility",
	sPb.CategoryFieldMask_CATEGORY_GROUP:      "group_identifier",
}

type IUpdateFAQ interface {
	UpdateCategory(ctx context.Context, catId int64, catProto *solPb.Category, updateFieldMask []sPb.CategoryFieldMask) error
	UpdateFolder(ctx context.Context, folID int64, visibility solPb.CategoryVisibility) error
	UpdateArticle(ctx context.Context, artID int64, visibility solPb.CategoryVisibility) error
}

type UpdateFAQData struct {
	DB *gormv2.DB
}

var _ IUpdateFAQ = &UpdateFAQData{}

func NewUpdateFAQData(db types.InapphelpPGDB) *UpdateFAQData {
	return &UpdateFAQData{
		DB: db,
	}
}

//nolint:dupl
func (u *UpdateFAQData) UpdateCategory(ctx context.Context, catId int64, catProto *solPb.Category, updateFieldMask []sPb.CategoryFieldMask) error {
	defer metric_util.TrackDuration("inapphelp/faq/serving/dao", "UpdateFAQData", "UpdateCategory", time.Now())
	if u == nil {
		return fmt.Errorf("update FAQ is not initialized")
	}

	if catId == 0 {
		return fmt.Errorf("invalid folder id")
	}

	if len(updateFieldMask) == 0 {
		return fmt.Errorf("category update mask cannot be empty")
	}

	updateFields, err := getCategoryColumnsForUpdate(updateFieldMask)
	if err != nil {
		return err
	}

	versionId, err := u.getLatestVersion(ctx)
	if err != nil {
		return nil
	}

	categoryModel := model.Category{
		Visibility:      catProto.GetCategoryVisibility(),
		Rank:            catProto.GetCategoryRank(),
		IconUrl:         catProto.GetIconUrl(),
		GroupIdentifier: catProto.GetGroupIdentifier(),
	}

	db := gormctxv2.FromContextOrDefault(ctx, u.DB)
	query := db.Where("id = ?", catId)
	query = query.Where("version_id = ?", versionId)
	if err := query.Model(categoryModel).Select(updateFields).Updates(categoryModel).Error; err != nil {
		return err
	}

	return nil
}

//nolint:dupl
func (u *UpdateFAQData) UpdateFolder(ctx context.Context, folID int64, visibility solPb.CategoryVisibility) error {
	defer metric_util.TrackDuration("inapphelp/faq/serving/dao", "UpdateFAQData", "UpdateFolder", time.Now())
	if u == nil {
		return fmt.Errorf("update FAQ is not initialized")
	}

	if folID == 0 {
		return fmt.Errorf("invalid folder id")
	}

	if visibility == solPb.CategoryVisibility_CATEGORY_VISIBILITY_UNSPECIFIED {
		return fmt.Errorf("folder visibility cannot be unpspecified")
	}

	versionId, err := u.getLatestVersion(ctx)
	if err != nil {
		return nil
	}

	folderModel := model.Folder{
		FolderVisibility: visibility,
	}
	mask := []string{"folder_visibility"}

	db := gormctxv2.FromContextOrDefault(ctx, u.DB)
	query := db.Where("id = ?", folID)
	query = query.Where("version_id = ?", versionId)
	if err := query.Model(folderModel).Select(mask).Updates(folderModel).Error; err != nil {
		return err
	}

	return nil
}

//nolint:dupl
func (u *UpdateFAQData) UpdateArticle(ctx context.Context, artID int64, visibility solPb.CategoryVisibility) error {
	defer metric_util.TrackDuration("inapphelp/faq/serving/dao", "UpdateFAQData", "UpdateArticle", time.Now())

	if u == nil {
		return fmt.Errorf("update FAQ is not initialized")
	}

	if artID == 0 {
		return fmt.Errorf("invalid article id")
	}

	if visibility == solPb.CategoryVisibility_CATEGORY_VISIBILITY_UNSPECIFIED {
		return fmt.Errorf("article visibility cannot be unpspecified")
	}

	versionId, err := u.getLatestVersion(ctx)
	if err != nil {
		return nil
	}

	articleModel := model.Article{
		ArticleVisibility: visibility,
	}
	mask := []string{"article_visibility"}

	db := gormctxv2.FromContextOrDefault(ctx, u.DB)
	query := db.Where("id = ?", artID)
	query = query.Where("version_id = ?", versionId)
	if err := query.Model(articleModel).Select(mask).Updates(articleModel).Error; err != nil {
		return err
	}

	return nil
}

//nolint:dupl
func (u *UpdateFAQData) getLatestVersion(ctx context.Context) (int64, error) {
	if u == nil {
		return 0, fmt.Errorf("update FAQ is not initialized")
	}
	faqVersionModel := model.FaqVersion{}
	db := gormctxv2.FromContextOrDefault(ctx, u.DB)
	if err := db.Order("updated_at desc").Select("id").First(&faqVersionModel).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return 0, epifierrors.ErrRecordNotFound
		}
		return 0, err
	}
	return faqVersionModel.Id, nil
}

func getCategoryColumnsForUpdate(updateFieldMask []sPb.CategoryFieldMask) ([]string, error) {
	var columns []string
	for _, field := range updateFieldMask {
		chk, ok := CategoryColumns[field]
		if !ok {
			return nil, fmt.Errorf("column not found for field: %s", field.String())
		}
		columns = append(columns, chk)
	}
	return columns, nil
}
