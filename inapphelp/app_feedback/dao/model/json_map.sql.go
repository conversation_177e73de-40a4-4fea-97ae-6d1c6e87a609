package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

type JSONMap map[string]string

// Value implements driver.Valuer interface
// It stores data as string in DB
func (x JSONMap) Value() (driver.Value, error) {
	if x == nil {
		return nil, nil
	}
	return json.Marshal(x)
}

// <PERSON>an implements sql.Scanner interface
// It parses the data from DB and converts to required type
func (x *JSONMap) Scan(src interface{}) error {
	val, ok := src.([]byte)
	if !ok {
		err := fmt.Errorf("expected []byte, got %T", src)
		return err
	}
	return json.Unmarshal(val, x)
}
