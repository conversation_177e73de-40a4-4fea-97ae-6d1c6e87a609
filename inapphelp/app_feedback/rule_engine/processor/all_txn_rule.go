package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	"github.com/pkg/errors"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/inapphelp/app_feedback"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/inapphelp/config"
	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

var (
	FailedTransactionCountAboveThresholdErr  = errors.New("failed transaction count above threshold")
	SuccessfulOrderCountLessThanThresholdErr = errors.New("successful order count less than threshold")
)

type AllTxnRuleProcessor struct {
	orderClient orderPb.OrderServiceClient
}

func NewAllTxnRuleProcessor(orderClient orderPb.OrderServiceClient) *AllTxnRuleProcessor {
	return &AllTxnRuleProcessor{
		orderClient: orderClient,
	}
}

var _ RuleProcessor = &AllTxnRuleProcessor{}

func (a *AllTxnRuleProcessor) IsUserEligible(ctx context.Context, actorId string, platform commontypes.Platform, ruleEngineConfig *config.FeedbackRuleEngineConfig) (commontypes.BooleanEnum, app_feedback.AppRatingNudgeIneligibilityReason, error) {
	err := a.CheckFailedTxn(ctx, actorId, ruleEngineConfig)
	if err != nil {
		if errors.Is(err, FailedTransactionCountAboveThresholdErr) {
			return commontypes.BooleanEnum_FALSE, getReasonForIneligibility(err), nil
		}
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, getReasonForIneligibility(nil), errors.Wrap(err, "error while checking failed txn")
	}
	err = a.CheckSuccessfulTxn(ctx, actorId, ruleEngineConfig)
	if err != nil {
		if errors.Is(err, SuccessfulOrderCountLessThanThresholdErr) {
			return commontypes.BooleanEnum_FALSE, getReasonForIneligibility(err), nil
		}
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, getReasonForIneligibility(nil), errors.Wrap(err, "error while checking successful txn")
	}
	return commontypes.BooleanEnum_TRUE, getReasonForIneligibility(nil), nil
}

func (a *AllTxnRuleProcessor) CheckFailedTxn(ctx context.Context, actorId string, ruleEngineConfig *config.FeedbackRuleEngineConfig) error {
	duration, err := time.ParseDuration(ruleEngineConfig.AllTxnRuleConfig.DurationForTxnEvaluation)
	if err != nil {
		return errors.Wrap(err, "error while parsing duration from config")
	}
	fromTime := timestampPb.New(time.Now().Add(-1 * duration))
	failedOrderResp, err := a.orderClient.GetOrdersForActor(ctx, &orderPb.GetOrdersForActorRequest{
		ActorId:         actorId,
		TransactionType: orderPb.GetOrdersForActorRequest_BOTH,
		FieldMask: []orderPb.OrderFieldMask{orderPb.OrderFieldMask_AMOUNT, orderPb.OrderFieldMask_FROM_ACTOR_ID,
			orderPb.OrderFieldMask_STATUS},
		StatusFilters: []orderPb.GetOrdersForActorRequest_OrderStatusFilter{orderPb.GetOrdersForActorRequest_FAILED,
			orderPb.GetOrdersForActorRequest_MANUAL_INTERVENTION},
		FromTime: fromTime,
		ToTime:   timestampPb.New(time.Now()),
		PageSize: 30,
	})
	if err = epifigrpc.RPCError(failedOrderResp, err); err != nil {
		return errors.Wrap(err, "error while fetching failed txns")
	}
	// check if failed txn count is above threshold
	if len(failedOrderResp.GetOrders()) >= ruleEngineConfig.AllTxnRuleConfig.MaxFailedCountThreshold {
		return FailedTransactionCountAboveThresholdErr
	}
	return nil
}

func (a *AllTxnRuleProcessor) CheckSuccessfulTxn(ctx context.Context, actorId string, ruleEngineConfig *config.FeedbackRuleEngineConfig) error {
	duration, err := time.ParseDuration(ruleEngineConfig.AllTxnRuleConfig.DurationForTxnEvaluation)
	if err != nil {
		return errors.Wrap(err, "error while parsing duration from config")
	}
	fromTime := timestampPb.New(time.Now().Add(-1 * duration))
	successfulOrderCount := 0
	successfulDebitOrderCount := 0
	thresholdAmount := moneyPb.AmountINR(ruleEngineConfig.AllTxnRuleConfig.MinDebitAmount).GetPb()
	isLastPage := false
	offset := int32(0)
	pageSize := ruleEngineConfig.AllTxnRuleConfig.PageSizeForOrders
	// check all txns till we reach last page or conditions are satisfied
	for !isLastPage {
		successOrderResp, err := a.orderClient.GetOrdersForActor(ctx, &orderPb.GetOrdersForActorRequest{
			ActorId:         actorId,
			TransactionType: orderPb.GetOrdersForActorRequest_BOTH,
			FieldMask: []orderPb.OrderFieldMask{orderPb.OrderFieldMask_AMOUNT, orderPb.OrderFieldMask_FROM_ACTOR_ID,
				orderPb.OrderFieldMask_STATUS},
			StatusFilters: []orderPb.GetOrdersForActorRequest_OrderStatusFilter{orderPb.GetOrdersForActorRequest_SUCCESS},
			FromTime:      fromTime,
			ToTime:        timestampPb.New(time.Now()),
			PageSize:      pageSize,
			Offset:        offset,
		})
		if err = epifigrpc.RPCError(successOrderResp, err); err != nil {
			return errors.Wrap(err, "error while fetching failed txns")
		}
		// if order length is less than 30 we have reached the last page
		isLastPage = len(successOrderResp.GetOrders()) < int(pageSize)

		for _, order := range successOrderResp.GetOrders() {
			// check if txn is debit and amount is greater than threshold amount
			if order.GetFromActorId() == actorId && moneyPb.Compare(order.GetAmount(), thresholdAmount) > -1 {
				successfulDebitOrderCount++
			}
		}
		// check if successful txn count is above threshold
		if successfulOrderCount >= ruleEngineConfig.AllTxnRuleConfig.MinSuccessCountThreshold ||
			successfulDebitOrderCount >= ruleEngineConfig.AllTxnRuleConfig.MinSuccessfulDebitCountThreshold {
			return nil
		}
		offset += pageSize
	}
	return SuccessfulOrderCountLessThanThresholdErr
}
