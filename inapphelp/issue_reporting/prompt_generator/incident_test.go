package prompt_generator

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	json "google.golang.org/protobuf/encoding/protojson"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epifierrors"
	mock_events "github.com/epifi/be-common/pkg/events/mocks"

	issuePb "github.com/epifi/gamma/api/cx/issue_category"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/cx/watson/mocks"
	mock_manager "github.com/epifi/gamma/cx/test/mocks/issue_category/manager"
)

var (
	issueCategoryId	= "6507c8cd-30ee-464c-b279-af1322a0d46b"
	incident1	= &watsonPb.IncidentDetailsForClient{
		ActorId:		actorId,
		IdentifiedAt:		timestamp.New(time.Unix(*********, 0)),
		IssueCategoryId:	issueCategoryId,
		TicketId:		1234,
	}
	incident3	= &watsonPb.IncidentDetailsForClient{
		ActorId:		actorId,
		IdentifiedAt:		timestamp.New(time.Unix(*********, 0)),
		IssueCategoryId:	issueCategoryId,
		TicketId:		6780,
	}
	incident2	= &watsonPb.IncidentDetailsForClient{
		ActorId:		actorId,
		IdentifiedAt:		timestamp.New(time.Unix(*********, 0)),
		IssueCategoryId:	issueCategoryId,
		TicketId:		0,
	}
	issueCategory	= &issuePb.IssueCategory{
		ProductCategory:	"Account",
		ProductCategoryDetails:	"Tier change",
		SubCategory:		"Upgrade failed",
	}
)

func TestIncidentPromptGenerator_GenerateContextualPrompt(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockWatsonClient := mocks.NewMockWatsonClient(ctr)
	mockIssueCategoryManager := mock_manager.NewMockIssueCategoryManager(ctr)
	mockBroker := mock_events.NewMockBroker(ctr)

	type args struct {
		actorId string
	}
	tests := []struct {
		name	string
		args	args
		mocks	[]interface{}
		want	string
		wantErr	error
	}{
		{
			name:	"no incidents found",
			args:	args{actorId: actorId},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{Status: rpcPb.StatusRecordNotFound()}, nil),
			},
			wantErr:	epifierrors.ErrRecordNotFound,
		},
		{
			name:	"failed to fetch incidents",
			args:	args{actorId: actorId},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(nil, mockErr),
			},
			wantErr:	mockErr,
		},
		{
			name:	"failed to fetch issue category id",
			args:	args{actorId: actorId},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{
						Status:	rpcPb.StatusOk(), Incidents: []*watsonPb.IncidentDetailsForClient{incident1},
					}, nil),
				mockIssueCategoryManager.EXPECT().GetValueById(gomock.Any(), issueCategoryId).Return(nil, mockErr),
				mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()),
			},
			wantErr:	epifierrors.ErrRecordNotFound,
		},
		{
			name:	"success but ignore one incident due to no ticket found",
			args:	args{actorId: actorId},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{
						Status:	rpcPb.StatusOk(), Incidents: []*watsonPb.IncidentDetailsForClient{incident1, incident2},
					}, nil),
				mockIssueCategoryManager.EXPECT().GetValueById(gomock.Any(), issueCategoryId).Return(issueCategory, nil),
				mockIssueCategoryManager.EXPECT().GetValueById(gomock.Any(), issueCategoryId).Return(issueCategory, nil),
				mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()),
			},
			want:	"{\"recent_incidents\":{\"incidents\":[{\"details\":\"Account Tier change Upgrade failed\", \"incident_id\":\"1234\", \"reported_at\":\"Saturday, 03-Mar-73 15:16:40 IST\"}], \"source\":\"realtime_incident_stream_fetch\"}}",
		},
		{
			name:	"success prompt max item threshold breached",
			args:	args{actorId: actorId},
			mocks: []interface{}{
				mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), gomock.Any()).
					Return(&watsonPb.GetIncidentsForClientResponse{
						Status:	rpcPb.StatusOk(), Incidents: []*watsonPb.IncidentDetailsForClient{incident1, incident3, incident2},
					}, nil),
				mockIssueCategoryManager.EXPECT().GetValueById(gomock.Any(), issueCategoryId).Return(issueCategory, nil),
				mockIssueCategoryManager.EXPECT().GetValueById(gomock.Any(), issueCategoryId).Return(issueCategory, nil),
				mockBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes(),
			},
			want:	"{\"recent_incidents\":{\"incidents\":[{\"details\":\"Account Tier change Upgrade failed\", \"incident_id\":\"1234\", \"reported_at\":\"Saturday, 03-Mar-73 15:16:40 IST\"}, {\"details\":\"Account Tier change Upgrade failed\", \"incident_id\":\"6780\", \"reported_at\":\"Saturday, 03-Mar-73 15:16:40 IST\"}], \"source\":\"realtime_incident_stream_fetch\"}}",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := NewIncidentPromptGenerator(mockWatsonClient, promptGeneratorTS.config, mockIssueCategoryManager, mockBroker)
			got, err := i.GenerateContextualPrompt(context.Background(), tt.args.actorId, "", "")
			if (err != nil) != (tt.wantErr != nil) || !errors.Is(err, tt.wantErr) {
				t.Errorf("GenerateContextualPrompt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			contextJson, jsonErr := json.Marshal(got)
			if jsonErr != nil {
				t.Errorf("json.Marshal() error = %v", jsonErr)
				return
			}
			if err == nil {
				require.JSONEq(t, tt.want, string(contextJson))
			}
		})
	}
}
