package model

import (
	"time"

	"gorm.io/gorm"

	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"

	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

// ReferralsSegmentedComponentFieldMask is to be used for deciding which fields to fetch from DB
type ReferralsSegmentedComponentFieldMask uint32

const (
	UNSPECIFIED_FIELD_MASK ReferralsSegmentedComponentFieldMask = 0
	SEGMENT_ID             ReferralsSegmentedComponentFieldMask = 1
	COMPONENT              ReferralsSegmentedComponentFieldMask = 2
	ID                     ReferralsSegmentedComponentFieldMask = 3
)

var FieldMaskToColumnNameMapping = map[ReferralsSegmentedComponentFieldMask]string{
	SEGMENT_ID: "segment_id",
	COMPONENT:  "component",
	ID:         "id",
}

type FetchReferralsSegmentedComponentsFilters struct {
	Ids        []string
	SegmentIds []string
	Components []inAppReferralEnumPb.Component
	AppFeature inAppReferralEnumPb.AppFeature
	// Variant filter will be applied only if ApplyVariantFilter is true
	// added this because we can't do zero value check for variant since it is not mandatory field in DB
	ApplyVariantFilter bool
	// will be applied only if ApplyVariantFilter is true
	Variant        inAppReferralEnumPb.Variant
	ActiveSince    time.Time
	ActiveTill     time.Time
	CreatedAtSince time.Time
	CreatedAtTill  time.Time
}

type ReferralsSegmentedComponent struct {
	// unique id of the row
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	// id of segment for which the details are created
	SegmentId string
	// enum corresponding to the component
	Component inAppReferralEnumPb.Component
	// details related to the component
	ComponentDetails *inAppReferralPb.ComponentDetails
	// app feature for which the component is enabled
	AppFeature inAppReferralEnumPb.AppFeature
	// variant of the component, represents another dimension to support multiple versions of the same (component, segment_id, app_feature) tuple.
	Variant inAppReferralEnumPb.Variant
	// timestamps since/till the component details will be active
	ActiveSince time.Time
	ActiveTill  time.Time

	CreatedAt time.Time
	UpdatedAt time.Time
	// signifies the date of soft deletion of the record
	DeletedAt gorm.DeletedAt
}

func NewReferralsSegmentedComponent(referralsSegmentedComponent *inAppReferralPb.ReferralsSegmentedComponent) *ReferralsSegmentedComponent {
	return &ReferralsSegmentedComponent{
		Id:               referralsSegmentedComponent.GetId(),
		SegmentId:        referralsSegmentedComponent.GetSegmentId(),
		Component:        referralsSegmentedComponent.GetComponent(),
		ComponentDetails: referralsSegmentedComponent.GetComponentDetails(),
		AppFeature:       referralsSegmentedComponent.GetAppFeature(),
		Variant:          referralsSegmentedComponent.GetVariant(),
		ActiveSince:      referralsSegmentedComponent.GetActiveSince().AsTime(),
		ActiveTill:       referralsSegmentedComponent.GetActiveTill().AsTime(),
	}
}

func (r *ReferralsSegmentedComponent) GetProto() *inAppReferralPb.ReferralsSegmentedComponent {
	var deletedAtTimestamp *timestampPb.Timestamp
	if r.DeletedAt.Valid {
		deletedAtTimestamp = timestampPb.New(r.DeletedAt.Time)
	}

	return &inAppReferralPb.ReferralsSegmentedComponent{
		Id:               r.Id,
		SegmentId:        r.SegmentId,
		Component:        r.Component,
		ComponentDetails: r.ComponentDetails,
		AppFeature:       r.AppFeature,
		Variant:          r.Variant,
		ActiveSince:      timestampPb.New(r.ActiveSince),
		ActiveTill:       timestampPb.New(r.ActiveTill),
		CreatedAt:        timestampPb.New(r.CreatedAt),
		UpdatedAt:        timestampPb.New(r.UpdatedAt),
		DeletedAt:        deletedAtTimestamp,
	}
}
