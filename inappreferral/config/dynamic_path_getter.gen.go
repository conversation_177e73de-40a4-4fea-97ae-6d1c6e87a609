// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "timestampjitterfortxncompletedeventinseconds":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TimestampJitterForTxnCompletedEventInSeconds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TimestampJitterForTxnCompletedEventInSeconds, nil
	case "inmemorycachettlinminutes":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InMemoryCacheTtlInMinutes\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InMemoryCacheTtlInMinutes, nil
	case "consideracquisitiongoldenticketasnonreferee":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConsiderAcquisitionGoldenTicketAsNonReferee\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConsiderAcquisitionGoldenTicketAsNonReferee, nil
	case "useorderupdatedatacollectorconsumerfortxncompletedevent":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseOrderUpdateDataCollectorConsumerForTxnCompletedEvent\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseOrderUpdateDataCollectorConsumerForTxnCompletedEvent, nil
	case "useorderupdatedatacollectorconsumerforfirstaddfundsevent":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseOrderUpdateDataCollectorConsumerForFirstAddFundsEvent\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseOrderUpdateDataCollectorConsumerForFirstAddFundsEvent, nil
	case "referralscollecteddatapublishdelayduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReferralsCollectedDataPublishDelayDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReferralsCollectedDataPublishDelayDuration, nil
	case "referraleligibilitysegmentexpressionsmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ReferralEligibilitySegmentExpressionsMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.ReferralEligibilitySegmentExpressionsMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.ReferralEligibilitySegmentExpressionsMap, nil
	case "claimedfinitecodenudgestoexit":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.ClaimedFiniteCodeNudgesToExit, nil
		case len(dynamicFieldPath) > 1:

			return obj.ClaimedFiniteCodeNudgesToExit[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.ClaimedFiniteCodeNudgesToExit, nil
	case "userapplicablereferralconstructvariant":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UserApplicableReferralConstructVariant\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UserApplicableReferralConstructVariant, nil
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "accountstateupdatesubscriber":
		return obj.AccountStateUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "inappreferralnotificationssqscustomdelaysubscriber":
		return obj.InAppReferralNotificationsSqsCustomDelaySubscriber.Get(dynamicFieldPath[1:])
	case "processrewardgenerationeventforseasonsubscriber":
		return obj.ProcessRewardGenerationEventForSeasonSubscriber.Get(dynamicFieldPath[1:])
	case "referralseligibilitycollecteddatasqssubscriber":
		return obj.ReferralsEligibilityCollectedDataSqsSubscriber.Get(dynamicFieldPath[1:])
	case "referralsorderupdatesqssubscriber":
		return obj.ReferralsOrderUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "referralssavingsaccountupdatesqssubscriber":
		return obj.ReferralsSavingsAccountUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "referralsorderupdatefirstaddfundssqssubscriber":
		return obj.ReferralsOrderUpdateFirstAddFundsSqsSubscriber.Get(dynamicFieldPath[1:])
	case "referralsnotificationsqssubscriber":
		return obj.ReferralsNotificationSqsSubscriber.Get(dynamicFieldPath[1:])
	case "referralnotificationtriggersinfo":
		return obj.ReferralNotificationTriggersInfo.Get(dynamicFieldPath[1:])
	case "abfeaturereleaseconfig":
		return obj.ABFeatureReleaseConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablephonenumberasreferralcode":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnablePhoneNumberAsReferralCode\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnablePhoneNumberAsReferralCode, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReferralEligibilitySegmentExpression) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isactive":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsActive\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsActive, nil
	case "expression":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Expression\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Expression, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReferralEligibilitySegmentExpression", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *NudgeToExit) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isactive":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsActive\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsActive, nil
	case "nudgeid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NudgeId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NudgeId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for NudgeToExit", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReferralNotificationTriggersInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "triggersinfo":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.TriggersInfo, nil
		case len(dynamicFieldPath) > 1:

			return obj.TriggersInfo[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.TriggersInfo, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReferralNotificationTriggersInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReferralNotificationTrigger) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "delayduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DelayDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DelayDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReferralNotificationTrigger", strings.Join(dynamicFieldPath, "."))
	}
}
