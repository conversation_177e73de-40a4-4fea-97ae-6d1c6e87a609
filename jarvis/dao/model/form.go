package model

import (
	"time"

	"gorm.io/gorm"

	"github.com/lib/pq"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/jarvis/types"
	pbtypes "github.com/epifi/gamma/api/jarvis/types"
)

type Form struct {
	Id                 string             `gorm:"type:uuid;default:gen_random_uuid();primary_key"`
	Title              string             `gorm:"not null"`
	Description        string             `gorm:"not null"`
	Status             pbtypes.FormStatus `gorm:"not null"`
	FormType           pbtypes.FormType   `gorm:"not null"`
	FormFields         *types.FormFields
	ApproverRole       string         `gorm:"not null"`
	Labels             pq.StringArray `gorm:"type:text[]"`
	Email              string         `gorm:"not null"`
	Cc                 pq.StringArray `gorm:"type:text[]"`
	ReportingManager   string         `gorm:"not null"`
	Bu                 string         `gorm:"not null"`
	RequestedReviewers pq.StringArray `gorm:"type:text[]"`
	CreatedBy          string         `gorm:"not null"`
	LastUpdatedBy      string
	DeletedBy          string
	ProcessedBy        string
	CreatedAt          time.Time
	UpdatedAt          time.Time
	DeletedAt          gorm.DeletedAt
	ProcessedAt        time.Time
	Comments           string
}

func NewForm(e *pbtypes.Form) *Form {
	return &Form{
		Id:                 e.GetId(),
		Title:              e.GetTitle(),
		Description:        e.GetDescription(),
		Status:             e.GetStatus(),
		FormType:           e.GetFormType(),
		FormFields:         e.GetFormFields(),
		ApproverRole:       e.GetApproverRole(),
		Labels:             e.GetLabels(),
		Email:              e.GetEmail(),
		Cc:                 e.GetCc(),
		ReportingManager:   e.GetReportingManager(),
		Bu:                 e.GetBu(),
		RequestedReviewers: e.GetRequestedReviewers(),
		CreatedBy:          e.GetCreatedBy(),
		LastUpdatedBy:      e.GetLastUpdatedBy(),
		DeletedBy:          e.GetDeletedBy(),
		ProcessedBy:        e.GetProcessedBy(),
		CreatedAt:          e.GetCreatedAt().AsTime(),
		UpdatedAt:          e.GetUpdatedAt().AsTime(),
		DeletedAt: gorm.DeletedAt{
			Time:  e.GetDeletedAt().AsTime(),
			Valid: e.GetDeletedAt().IsValid(),
		},
		ProcessedAt: e.GetProcessedAt().AsTime(),
		Comments:    e.GetComments(),
	}
}

func (e *Form) GetProto() *pbtypes.Form {
	var deletedAt *timestamppb.Timestamp
	if e.DeletedAt.Valid {
		deletedAt = timestamppb.New(e.DeletedAt.Time)
	}
	return &pbtypes.Form{
		Id:                 e.Id,
		Title:              e.Title,
		Description:        e.Description,
		Status:             e.Status,
		FormType:           e.FormType,
		FormFields:         e.FormFields,
		ApproverRole:       e.ApproverRole,
		Labels:             e.Labels,
		Email:              e.Email,
		Cc:                 e.Cc,
		ReportingManager:   e.ReportingManager,
		Bu:                 e.Bu,
		RequestedReviewers: e.RequestedReviewers,
		CreatedBy:          e.CreatedBy,
		LastUpdatedBy:      e.LastUpdatedBy,
		DeletedBy:          e.DeletedBy,
		ProcessedBy:        e.ProcessedBy,
		CreatedAt:          timestamppb.New(e.CreatedAt),
		UpdatedAt:          timestamppb.New(e.UpdatedAt),
		DeletedAt:          deletedAt,
		ProcessedAt:        timestamppb.New(e.ProcessedAt),
		Comments:           e.Comments,
	}
}
