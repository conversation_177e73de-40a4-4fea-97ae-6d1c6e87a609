# Ref: https://weberc2.bitbucket.io/posts/golang-docker-scratch-app.html

# This is the first stage, for building things that will be required by the
# final stage (notably the binary)
FROM 632884248997.dkr.ecr.ap-south-1.amazonaws.com/gamma-dependencies:latest AS builder
# Copy the source code
COPY . /go/src/github.com/epifi/gamma

# Accept service_name as build-arg
ARG service_name
# Accept the env as build-arg
ARG env
# Set GOPROXY environment variable
ENV GOPROXY="https://goproxy.pointz.in"
# Build the Go app with CGO_ENABLED=0 so we use the pure-Go implementations for
# things like DNS resolution (so we don't build a binary that depends on system
# libraries)
RUN make build target=${service_name} env=${env}

# The second and final stage
FROM scratch

# Accept service_name as build-arg
ARG service_name

# Copy the binary and config from the builder stage
COPY --from=builder /go/src/github.com/epifi/gamma/output/${service_name}/${service_name}_bin /my_bin
COPY --from=builder /go/src/github.com/epifi/gamma/output/${service_name}/config /config

# Copy the certs from the builder stage,
# this is needed if applications makes HTTPS requests
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the /etc_passwd file we created in the builder stage into /etc/passwd in
# the target stage. This creates a new non-root user as a security best
# practice.
COPY --from=builder /etc_passwd /etc/passwd

# Run as the new non-root by default
USER nobody

CMD ["/my_bin"]
