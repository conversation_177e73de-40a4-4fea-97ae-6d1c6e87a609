package internationalfundtransfer

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/forex"
)

func (s *Service) RevertForexRateAmountInUse(ctx context.Context, req *iftPb.RevertForexRateAmountInUseRequest) (*iftPb.RevertForexRateAmountInUseResponse, error) {
	res, err := s.forexClient.UpdateForexDealUsage(ctx, &forex.UpdateForexDealUsageRequest{
		ClientReqId:      req.GetClientRequestId(),
		UpdateType:       forex.ForexDealUsageUpdateType_FOREX_DEAL_USAGE_UPDATE_TYPE_REVERT,
		ForexDealId:      req.GetForexRateId(),
		RemittanceAmount: req.GetTransactionAmount(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "error reverting forex rate consumption", zap.Error(err),
			zap.String(logger.FOREX_RATE_ID, req.GetForexRateId()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()))
		return &iftPb.RevertForexRateAmountInUseResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &iftPb.RevertForexRateAmountInUseResponse{Status: rpcPb.StatusOk()}, nil
}
