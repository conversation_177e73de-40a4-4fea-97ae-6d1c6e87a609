package internationalfundtransfer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	storage "github.com/epifi/be-common/pkg/storage/v2"
	mocks2 "github.com/epifi/be-common/pkg/storage/v2/mocks"

	"github.com/epifi/be-common/api/rpc"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	vgIftPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	vgIftMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer/mocks"
	"github.com/epifi/gamma/pay/dao"
	"github.com/epifi/gamma/pay/dao/mocks"
	doOnceMock "github.com/epifi/be-common/pkg/counter/once/v2/mocks"
	"github.com/epifi/be-common/pkg/datetime"
	dateTimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

func TestService_GetForexRate(t *testing.T) {
	timeFix1 := time.Date(2022, 1, 1, 2, 0, 0, 0, datetime.IST)
	type fields struct {
		forexRateDao    *mocks.MockForexRateDao
		vgIft           *vgIftMocks.MockInternationalFundTransferClient
		redisClient     *redis.Client
		timeClient      *dateTimeMocks.MockTime
		doOnce          *doOnceMock.MockDoOnce
		mockTxnExecutor *mocks2.MockIdempotentTxnExecutor
	}
	type args struct {
		ctx context.Context
		req *iftPb.GetForexRateRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *iftPb.GetForexRateResponse
		wantErr bool
		prepare func(*args, *fields)
	}{
		{
			name: "should successfully get forex rate from DB",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
					Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        0,
				},
				Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
				ForexRateId: "forex-rate-id-1",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id:           "forex-rate-id-1",
						CurrencyCode: "USD",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
						MaxAmount: &moneyPb.Money{
							CurrencyCode: "USD",
							Units:        6789,
							Nanos:        0,
						},
						AmountInUse: &moneyPb.Money{
							CurrencyCode: "USD",
							Units:        56,
							Nanos:        0,
						},
						State:      iftPb.ForexRateState_FOREX_RATE_STATE_ACTIVE,
						Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil).Times(1)
			},
		},
		{
			name: "should successfully get forex rate from VG",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					Provenance:     iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API,
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        0,
				},
				ForexRateId: "forex_rate_id_1",
				Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API,
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.timeClient.EXPECT().Now().Return(timeFix1).Times(2)
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id: "forex_rate_id_1",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
				f.vgIft.EXPECT().GetForexRate(a.ctx, &vgIftPb.GetForexRateRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					CurrencyCode:   "USD",
					RemittanceType: vgIftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
				}).Return(&vgIftPb.GetForexRateResponse{
					Status: rpc.StatusOk(),
					RateDetails: &vgIftPb.ForexRateDetails{
						Rate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
			},
		},
		{
			name: "should successfully get forex rate from DB when provenance is not specified",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
					Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_UNSPECIFIED,
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        0,
				},
				Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
				ForexRateId: "forex-rate-id-1",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id:           "forex-rate-id-1",
						CurrencyCode: "USD",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
						MaxAmount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        6789,
							Nanos:        0,
						},
						AmountInUse: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        56,
							Nanos:        0,
						},
						State:      iftPb.ForexRateState_FOREX_RATE_STATE_ACTIVE,
						Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil).Times(1)
			},
		},
		{
			name: "should successfully get forex rate from VG as DB have no active forex rate",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        0,
				},
				Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API,
				ForexRateId: "forex_rate_id",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.doOnce.EXPECT().DoOnceFn(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, taskString string, function func() error) error {
					return function()
				})
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{}, nil).Times(2)
				f.timeClient.EXPECT().Now().Return(timeFix1).Times(3)
				f.vgIft.EXPECT().GetForexRate(a.ctx, &vgIftPb.GetForexRateRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					CurrencyCode:   "USD",
					RemittanceType: vgIftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
				}).Return(&vgIftPb.GetForexRateResponse{
					Status: rpc.StatusOk(),
					RateDetails: &vgIftPb.ForexRateDetails{
						Rate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
				f.mockTxnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, executor storage.InTransaction) error {
					return executor(ctx)
				})
				f.forexRateDao.EXPECT().Create(a.ctx, gomock.Any()).Return(&iftPb.ForexRate{
					Id: "forex_rate_id",
					ExchangeRate: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        82,
						Nanos:        0,
					},
				}, nil)
			},
		},
		{
			name: "should successfully get forex rate from DB for VG provenance",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        83,
					Nanos:        0,
				},
				Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API,
				ForexRateId: "forex_rate_id_1",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{}, nil).Times(1)
				f.timeClient.EXPECT().Now().Return(timeFix1).Times(2)
				f.vgIft.EXPECT().GetForexRate(a.ctx, &vgIftPb.GetForexRateRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					CurrencyCode:   "USD",
					RemittanceType: vgIftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
				}).Return(&vgIftPb.GetForexRateResponse{
					Status: rpc.StatusOk(),
					RateDetails: &vgIftPb.ForexRateDetails{
						Rate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        83,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id: "forex_rate_id_1",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        83,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
			},
		},
		{
			name: "forex rate from vendor and forex rate in DB for VG provenance is not same",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{}, nil).Times(1)
				f.timeClient.EXPECT().Now().Return(timeFix1).Times(2)
				f.vgIft.EXPECT().GetForexRate(a.ctx, &vgIftPb.GetForexRateRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					CurrencyCode:   "USD",
					RemittanceType: vgIftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
				}).Return(&vgIftPb.GetForexRateResponse{
					Status: rpc.StatusOk(),
					RateDetails: &vgIftPb.ForexRateDetails{
						Rate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        83,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id: "forex_rate_id_1",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        5,
						},
					},
				}, nil).Times(1)
			},
		},
		{
			name: "multiple forex rate entry present in DB for VG provenance is not same",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{}, nil).Times(1)
				f.timeClient.EXPECT().Now().Return(timeFix1).Times(2)
				f.vgIft.EXPECT().GetForexRate(a.ctx, &vgIftPb.GetForexRateRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					CurrencyCode:   "USD",
					RemittanceType: vgIftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
				}).Return(&vgIftPb.GetForexRateResponse{
					Status: rpc.StatusOk(),
					RateDetails: &vgIftPb.ForexRateDetails{
						Rate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        83,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id: "forex_rate_id_1",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        83,
							Nanos:        0,
						},
					},
					{
						Id: "forex_rate_id_2",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        83,
							Nanos:        5,
						},
					},
				}, nil).Times(1)
			},
		},
		{
			name: "should fail to get forex rate when provenance is not specified and no forex rate is found in DB" +
				" and vg is down",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
					Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_UNSPECIFIED,
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{}, nil).Times(1)
				f.timeClient.EXPECT().Now().Return(timeFix1).Times(1)
				f.vgIft.EXPECT().GetForexRate(a.ctx, gomock.Any()).Return(&vgIftPb.GetForexRateResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)
			},
		},
		// UNCOMMENT THIS BLOCK if we want to use forex rate from config if vendor api fails
		// {
		//	name: "should successfully get forex rate from config as DB have no active forex rate and VG is giving error",
		//	args: args{
		//		ctx: context.Background(),
		//		req: &iftPb.GetForexRateRequest{
		//			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		//			RequiredAmount: &moneyPb.Money{
		//				CurrencyCode: "USD",
		//				Units:        100,
		//				Nanos:        0,
		//			},
		//		},
		//	},
		//	want: &iftPb.GetForexRateResponse{
		//		Status: rpc.StatusOk(),
		//		ExchangeRate: &moneyPb.Money{
		//			CurrencyCode: "INR",
		//			Units:        81,
		//			Nanos:        *********,
		//		},
		//		Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_CONFIG,
		//	},
		//	wantErr: false,
		//	prepare: func(a *args, f *fields) {
		//		f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{}, nil).Times(1)
		//
		//		f.vgIft.EXPECT().GetForexRate(a.ctx, &vgIftPb.GetForexRateRequest{
		//			Header: &commonvgpb.RequestHeader{
		//				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		//			},
		//			CurrencyCode: "USD",
		//		}).Return(&vgIftPb.GetForexRateResponse{
		//			Status: rpc.StatusInternal(),
		//		}, nil).Times(1)
		//	},
		// },
		{
			name: "should fail as DB is giving error",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{},
					fmt.Errorf("random error")).Times(1)
			},
		},
		{
			name: "should successfully get forex rate from DB with amount_in_use as nil",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
					Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        0,
				},
				Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
				ForexRateId: "forex-rate-id-1",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id:           "forex-rate-id-1",
						CurrencyCode: "USD",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
						MaxAmount: &moneyPb.Money{
							CurrencyCode: "USD",
							Units:        6789,
							Nanos:        0,
						},
						AmountInUse: nil,
						State:       iftPb.ForexRateState_FOREX_RATE_STATE_ACTIVE,
						Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
						Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil).Times(1)
			},
		},
		{
			name: "should successfully get forex rate from VG for INWARD remittance",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_INWARD,
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        0,
				},
				Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API,
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.vgIft.EXPECT().GetForexRate(a.ctx, &vgIftPb.GetForexRateRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: vgIftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_INWARD,
				}).Return(&vgIftPb.GetForexRateResponse{
					Status: rpc.StatusOk(),
					RateDetails: &vgIftPb.ForexRateDetails{
						Rate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
					},
				}, nil).Times(1)
			},
		},
		{
			name: "getting forex rate from cache for INWARD remittance",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_INWARD,
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        83,
					Nanos:        50,
				},
				Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API,
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.redisClient.Set(context.Background(),
					getAPIForexRateRedisKey(a.req.GetVendor(), a.req.GetCurrencyCode(), a.req.GetRemittanceType()),
					&ForexRateDetailsCache{
						ForexRateDetails: &vgIftPb.ForexRateDetails{
							Rate: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        83,
								Nanos:        50,
							}},
						FetchedAt: time.Now(),
					}, 0)
			},
		},
		{
			name: "should successfully get forex rate from Cache as DB have no active forex rate",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status:      rpc.StatusOk(),
				ForexRateId: "forex_rate_id",
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        22,
				},
				Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API,
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.redisClient.Set(context.Background(),
					getAPIForexRateRedisKey(a.req.GetVendor(), a.req.GetCurrencyCode(), a.req.GetRemittanceType()),
					&ForexRateDetailsCache{
						ForexRateDetails: &vgIftPb.ForexRateDetails{
							Rate: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        82,
								Nanos:        22,
							}},
						FetchedAt: time.Now(),
					}, 0)
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{}, nil).Times(2)
				f.timeClient.EXPECT().Now().Return(timeFix1).Times(3)
				f.doOnce.EXPECT().DoOnceFn(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, taskString string, function func() error) error {
					return function()
				})
				f.mockTxnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, executor storage.InTransaction) error {
					return executor(ctx)
				})
				f.forexRateDao.EXPECT().Create(a.ctx, gomock.Any()).Return(&iftPb.ForexRate{
					Id: "forex_rate_id",
					ExchangeRate: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        82,
						Nanos:        22,
					},
				}, nil)
			},
		},
		{
			name: "get forex rate from DB with filters",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetForexRateRequest{
					Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
					CurrencyCode:   moneyPkg.USDCurrencyCode,
					RemittanceType: iftPb.ForexRateRemittanceType_FOREX_RATE_REMITTANCE_TYPE_OUTWARD,
					RequiredAmount: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
						Nanos:        0,
					},
					Provenance:      iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
					ForexRateStates: []iftPb.ForexRateState{iftPb.ForexRateState_FOREX_RATE_STATE_INACTIVE},
				},
			},
			want: &iftPb.GetForexRateResponse{
				Status: rpc.StatusOk(),
				ExchangeRate: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        82,
					Nanos:        0,
				},
				Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
				ForexRateId: "forex-rate-id-1",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.forexRateDao.EXPECT().GetByFilters(a.ctx, gomock.Any()).Return([]*iftPb.ForexRate{
					{
						Id:           "forex-rate-id-1",
						CurrencyCode: "USD",
						ExchangeRate: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        82,
							Nanos:        0,
						},
						MaxAmount: &moneyPb.Money{
							CurrencyCode: "USD",
							Units:        6789,
							Nanos:        0,
						},
						AmountInUse: &moneyPb.Money{
							CurrencyCode: "USD",
							Units:        56,
							Nanos:        0,
						},
						State:      iftPb.ForexRateState_FOREX_RATE_STATE_INACTIVE,
						Provenance: iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
						Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					},
				}, nil).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			forexRateDao := mocks.NewMockForexRateDao(ctr)
			vgIftMocksClient := vgIftMocks.NewMockInternationalFundTransferClient(ctr)
			mockTimeClient := dateTimeMocks.NewMockTime(ctr)
			mockdoOnceClient := doOnceMock.NewMockDoOnce(ctr)
			mockTxnExecutor := mocks2.NewMockIdempotentTxnExecutor(ctr)
			f := fields{
				forexRateDao:    forexRateDao,
				vgIft:           vgIftMocksClient,
				timeClient:      mockTimeClient,
				redisClient:     redisClient,
				doOnce:          mockdoOnceClient,
				mockTxnExecutor: mockTxnExecutor,
			}
			if tt.prepare != nil {
				tt.prepare(&tt.args, &f)
			}
			s := &Service{
				forexRateDao:          forexRateDao,
				vgIft:                 vgIftMocksClient,
				config:                conf,
				timeClient:            mockTimeClient,
				redisClient:           redisClient,
				doOnce:                mockdoOnceClient,
				idempotentTxnExecutor: mockTxnExecutor,
			}
			got, err := s.GetForexRate(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetForexRate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			redisClient.Del(context.Background(), getAPIForexRateRedisKey(tt.args.req.GetVendor(), tt.args.req.GetCurrencyCode(),
				tt.args.req.GetRemittanceType()))
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetForexRate() diff=%v", diff)
			}
		})
	}
}

func TestService_GetForexRateDetails(t *testing.T) {
	ctr := gomock.NewController(t)
	forexRateDao := mocks.NewMockForexRateDao(ctr)
	forexRate := &iftPb.ForexRate{
		Id:           "forex-rate-id-1",
		CurrencyCode: "USD",
		ExchangeRate: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        82,
			Nanos:        0,
		},
		MaxAmount: &moneyPb.Money{
			CurrencyCode: "USD",
			Units:        6789,
			Nanos:        0,
		},
		AmountInUse: nil,
		State:       iftPb.ForexRateState_FOREX_RATE_STATE_ACTIVE,
		Provenance:  iftPb.ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
	}

	type fields struct {
		forexRateDao dao.ForexRateDao
	}
	type args struct {
		ctx     context.Context
		request *iftPb.GetForexRateDetailsRequest
	}
	tests := []struct {
		name      string
		fields    fields
		args      args
		setupMock func()
		want      *iftPb.GetForexRateDetailsResponse
		wantErr   bool
	}{
		{
			name: "#1 invalid field mask provided",
			fields: fields{
				forexRateDao: forexRateDao,
			},
			args: args{
				ctx: context.Background(),
				request: &iftPb.GetForexRateDetailsRequest{
					Vendor:       commonvgpb.Vendor_ALPACA,
					CurrencyCode: "USD",
					ForexRateDetailsFieldMask: []iftPb.ForexRateDetailsFieldMask{
						iftPb.ForexRateDetailsFieldMask_FOREX_RATE_DETAILS_FIELD_MASK_UNSPECIFIED,
					},
				},
			},
			want:    &iftPb.GetForexRateDetailsResponse{Status: rpc.StatusInvalidArgument()},
			wantErr: false,
		},
		{
			name: "#2 error in dao func",
			fields: fields{
				forexRateDao: forexRateDao,
			},
			args: args{
				ctx: context.Background(),
				request: &iftPb.GetForexRateDetailsRequest{
					Vendor:       commonvgpb.Vendor_ALPACA,
					CurrencyCode: "USD",
					ForexRateDetailsFieldMask: []iftPb.ForexRateDetailsFieldMask{
						iftPb.ForexRateDetailsFieldMask_FOREX_RATE_DETAILS_FIELD_MASK_MAX_AVAILABLE_DEAL_AMOUNT,
					},
				},
			},
			setupMock: func() {
				forexRateDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any()).Return(nil, errors.New("error in GetByFilters"))
			},
			want:    &iftPb.GetForexRateDetailsResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "#3 no forex rates present",
			fields: fields{
				forexRateDao: forexRateDao,
			},
			args: args{
				ctx: context.Background(),
				request: &iftPb.GetForexRateDetailsRequest{
					Vendor:       commonvgpb.Vendor_ALPACA,
					CurrencyCode: "USD",
					ForexRateDetailsFieldMask: []iftPb.ForexRateDetailsFieldMask{
						iftPb.ForexRateDetailsFieldMask_FOREX_RATE_DETAILS_FIELD_MASK_MAX_AVAILABLE_DEAL_AMOUNT,
					},
				},
			},
			setupMock: func() {
				forexRateDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any()).Return([]*iftPb.ForexRate{}, nil)
			},
			want:    &iftPb.GetForexRateDetailsResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "#5 forex rates present",
			fields: fields{
				forexRateDao: forexRateDao,
			},
			args: args{
				ctx: context.Background(),
				request: &iftPb.GetForexRateDetailsRequest{
					Vendor:       commonvgpb.Vendor_ALPACA,
					CurrencyCode: "USD",
					ForexRateDetailsFieldMask: []iftPb.ForexRateDetailsFieldMask{
						iftPb.ForexRateDetailsFieldMask_FOREX_RATE_DETAILS_FIELD_MASK_MAX_AVAILABLE_DEAL_AMOUNT,
					},
				},
			},
			setupMock: func() {
				forexRateDao.EXPECT().GetByFilters(gomock.Any(), gomock.Any()).Return([]*iftPb.ForexRate{forexRate}, nil)
			},
			want: &iftPb.GetForexRateDetailsResponse{
				Status: rpc.StatusOk(),
				ForexRateDetailsMap: map[string]*iftPb.ForexRate{
					iftPb.ForexRateDetailsFieldMask_FOREX_RATE_DETAILS_FIELD_MASK_MAX_AVAILABLE_DEAL_AMOUNT.String(): forexRate,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				forexRateDao: tt.fields.forexRateDao,
			}
			if tt.setupMock != nil {
				tt.setupMock()
			}
			got, err := s.GetForexRateDetails(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetForexRateDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetForexRateDetails() diff=%v", diff)
			}
		})
	}
}
