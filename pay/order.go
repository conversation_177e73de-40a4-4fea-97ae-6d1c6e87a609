package pay

import (
	"context"
	"errors"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"google.golang.org/protobuf/types/known/durationpb"
	durationPb "google.golang.org/protobuf/types/known/durationpb"

	types "github.com/epifi/gamma/api/typesv2"
	config "github.com/epifi/gamma/pay/config/server"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"

	orderPb "github.com/epifi/gamma/api/order"
	orderEnumsPb "github.com/epifi/gamma/api/order/enums"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/payload"
	"github.com/epifi/gamma/api/recurringpayment"
	vgPgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"
	"github.com/epifi/gamma/pkg/vendorstore"
)

var (
	vendorsRequiringOrderCreation = map[commonvgpb.Vendor]bool{
		commonvgpb.Vendor_RAZORPAY: true,
	}

	amountIsNotMatchingErr        = fmt.Errorf("order amount is not matching with domain amount")
	clientExpiryTimeIsNotValidErr = fmt.Errorf("client expiry time is not valid to process")
)

// CreateOrder creates an entry in the order table
// It is idempotent wrt client request id. If an order already exists, it will return the existing order
// as well as the vendor side details. If the order does not exist, it will create the order and also create
// an order on the vendor's end. It will also initiate a workflow to poll the order status at the vendor's end
// and update the order status in the internal entities.
// NOTE: This RPC was created to handle the order creation flow for an external vendor as well if required. For eg.
// in the payment gateway scenario, the order creation is done at the vendor's end as well. This RPC will handle
// the order creation at the vendor's end as well as the internal order creation.
func (s *Service) CreateOrder(ctx context.Context, req *payPb.CreateOrderRequest) (*payPb.CreateOrderResponse, error) {
	var (
		res = &payPb.CreateOrderResponse{}
	)
	vendorOrderCreationRequired := vendorsRequiringOrderCreation[req.GetVendor()]
	ctx = epificontext.WithOwnership(ctx, req.GetOwnership())
	dbOrder, err := s.checkIfOrderExists(ctx, req.GetOrder().GetClientReqId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		// normal flow continues
	case err != nil:
		logger.Error(ctx, "error in validating order created", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		res.Order = dbOrder
		if !vendorOrderCreationRequired {
			res.Status = rpc.StatusOk()
			return res, nil
		}
		// since the internal order already exists, the vendor order will also exist
		ovomRes, ovomErr := s.orderVendorOrderMapDao.GetOrderVendorOrderMap(ctx, &orderPb.OrderVendorOrderMap{
			OrderId: dbOrder.GetId(),
		}, []orderEnumsPb.OrderVendorOrderMapFieldMask{
			orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_ORDER_ID,
		})
		if ovomErr != nil && !errors.Is(ovomErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in fetching ovom for given order", zap.Error(err), zap.String(logger.ORDER_ID, dbOrder.GetId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		if len(ovomRes) != 0 {
			res.VendorOrderId = ovomRes[0].GetVendorOrderId()
		}
		authParams, authErr := pgauthkeys.GetAuthParamsForPgProgram(&types.PaymentGatewayProgram{
			PgVendor:        req.GetVendor(),
			PiId:            req.GetDomainOrderData().GetToPi(),
			EntityOwnership: req.GetOwnership(),
		})
		if authErr != nil {
			logger.Error(ctx, "error in fetching auth params for the given pg program", zap.Error(authErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		authKey, authErr := pgauthkeys.GetAuthKeyForAuthParams(authParams)
		if authErr != nil {
			logger.Error(ctx, "error in fetching auth keys for auth params", zap.Error(authErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.VendorAuthKey = authKey
		res.Status = rpc.StatusOk()
		return res, nil
	}

	err = s.validateAmountAndClientExpiryTime(req.GetDomainOrderData())
	switch {
	case errors.Is(err, clientExpiryTimeIsNotValidErr):
		logger.Error(ctx, "order creation time expired", zap.Error(err))
		res.Status = rpc.StatusFailedPreconditionWithDebugMsg(err.Error())
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in validating domain order data", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	req.GetOrder().ToActorId = req.GetDomainOrderData().GetToActor()
	if req.GetDomainOrderData().GetNextAction() != nil {
		nextActionAny, marshalErr := anyPb.New(req.GetDomainOrderData().GetNextAction())
		if marshalErr != nil {
			logger.Error(ctx, "marshalling error in domain next action", zap.Error(marshalErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		req.GetOrder().PostPaymentDeeplink = nextActionAny
	}
	// not proceeding with vendor order creation if vendor does not require it,
	// hence returning
	if !vendorOrderCreationRequired {
		// Note: This order is not created outside this because in case the vendor side order has
		// to be created, we need to create the vendor order first to maintain idempotency of the API
		createdOrder, createErr := s.createOrder(ctx, req.GetOrder())
		if createErr != nil {
			logger.Error(ctx, "order creation failed", zap.Error(createErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		res.Order = createdOrder
		return res, nil
	}
	// initialising pg program to be used to propagate to workflows and also fetch the
	// api keys to the vendor
	pgProgram := &types.PaymentGatewayProgram{
		PgVendor:        req.GetVendor(),
		PiId:            req.GetDomainOrderData().GetToPi(),
		EntityOwnership: req.GetOwnership(),
	}
	vendorOrderRes, authParams, err := s.createOrderAtVendorSide(ctx, req.GetOrder(), req.GetVendor(), req.GetDomainOrderData(), req.GetOwnership(), req.GetAutoCaptureTimeoutOverride())
	if err != nil {
		logger.Error(ctx, "error in order creation at vendor side", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetOrder().GetClientReqId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	// creating internal order after vendor order to avoid the scenario that internal order is created
	// but vendor order creation fails. This case would then hamper the idempotency since we dont have
	// any internal identifier to see if the order was created at vendor's end or not and any retry will indicate
	// that the order creation has been done on vendor's end as well. Doing it this way will ensure full idempotency.
	vendorOrderPayload := &payPb.VendorOrderDetailsPayload{
		PgProgram: pgProgram,
		PiFrom:    req.GetDomainOrderData().GetFromPi(),
	}
	vendorOrderPayloadBytes, marshalErr := protojson.Marshal(vendorOrderPayload)
	if marshalErr != nil {
		logger.Error(ctx, "error in marshalling vendor order payload", zap.Error(marshalErr), zap.String(logger.CLIENT_ORDER_ID, req.GetOrder().GetClientReqId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	// in case of payments which require a vendor order creation, a vendor order payload will also
	// be added in order so that it can be accessed when needed
	req.GetOrder().OrderPayload = vendorOrderPayloadBytes
	createdOrder, createErr := s.createOrder(ctx, req.GetOrder())
	if createErr != nil {
		logger.Error(ctx, "internal order creation failed", zap.Error(createErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.VendorOrderId = vendorOrderRes.GetVendorOrderId()
	authKey, err := pgauthkeys.GetAuthKeyForAuthParams(authParams)
	if err != nil {
		logger.Error(ctx, "error in fetching auth keys for auth params", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	// setting auth key in response so that it can be used by the client using vendor SDK/API
	res.VendorAuthKey = authKey
	// after order has been created both on vendor's end and internally, we create a mapping between the two to order
	// ids for lookup purposes
	_, createOrderVendorOrderErr := s.createOrderVendorOrderMap(ctx, &orderPb.OrderVendorOrderMap{
		OrderId:           createdOrder.GetId(),
		VendorOrderId:     vendorOrderRes.GetVendorOrderId(),
		Vendor:            req.GetVendor(),
		OrderDirection:    orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
		EntityOwnership:   req.GetOwnership(),
		DomainReferenceId: req.GetOrder().GetClientReqId(),
	})
	if createOrderVendorOrderErr != nil {
		// Note: In case of failures in this stage, the order creation will have to be retried with a new client request id.
		// A permanent fix for this would be to keep the order creation and ovom creation in a single txn. Currently that is not
		// done here because order entity will be entity-segregated and currently ovom is not. When ovom is also entity segregated,
		// the order and ovom creation wll be moved in a single txn
		// TODO(abhishekprakashfi) create order and ovom in a single txn once ovom is also entity-segregated
		logger.Error(ctx, "vendor order map creation failed", zap.Error(createOrderVendorOrderErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	goroutine.RunWithCtx(epificontext.CloneCtx(ctx), func(ctx context.Context) {
		s.createVendorResponse(ctx, vendorOrderRes, createdOrder.GetFromActorId(), req.GetVendor())
	})

	recurringPaymentId := req.GetRecurringPaymentDetails().GetRecurringPaymentId()
	fundTransferStatusWaitSignalTimeout := s.dynConf.PgParams().OneTimeFundTransferStatusWaitSignalTimeout()
	if recurringPaymentId != "" {
		fundTransferStatusWaitSignalTimeout = s.dynConf.PgParams().RpExecutionFundTransferStatusWaitSignalTimeout()
	}

	// initiating pg order status polling workflow so that our order status is constantly updated in
	// the background wrt vendor order
	initErr := s.initiatePgOrderPollingWorkflow(ctx, req.GetOrder().GetFromActorId(), req.GetOrder().GetClientReqId(), createdOrder.GetId(), pgProgram, fundTransferStatusWaitSignalTimeout)
	if initErr != nil {
		// not returning error here since order has been created already on vendor's . Failure will mean that
		// only sync polling will be available at our end.
		logger.Error(ctx, "error in initiating polling workflow",
			zap.Error(initErr),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetOrder().GetClientReqId()),
			zap.String(logger.ACTOR_ID_V2, req.GetOrder().GetFromActorId()))
	}

	res.Status = rpc.StatusOk()
	res.Order = createdOrder
	return res, nil
}

func (s *Service) initiatePgOrderPollingWorkflow(ctx context.Context, actorId, clientReqId, orderId string, pgProgram *types.PaymentGatewayProgram, fundTransferStatusWaitSignalTimeout time.Duration) error {
	wfPayload, err := protojson.Marshal(&payload.CheckPgFundTransferStatusWfPayload{
		OrderId:                             orderId,
		Vendor:                              pgProgram.GetPgVendor(),
		PgProgram:                           pgProgram,
		FundTransferStatusWaitSignalTimeout: durationPb.New(fundTransferStatusWaitSignalTimeout),
	})
	if err != nil {
		return fmt.Errorf("error in marshalling wf payload %w", err)
	}
	initRes, initErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: actorId,
			Version: workflow.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(payNs.CheckAndUpdatePgFundTransferStatus),
			Payload: wfPayload,
			ClientReqId: &workflow.ClientReqId{
				Id:     clientReqId,
				Client: workflow.Client_PAY,
			},
			Ownership:        pgProgram.GetEntityOwnership(),
			UseCase:          config.OwnershipToUseCase[pgProgram.GetEntityOwnership()],
			NextAction:       nil,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initRes, initErr); te != nil {
		return fmt.Errorf("error in initiating pg order polling workflow %w", te)
	}
	return nil
}

// createVendorResponse will store the raw response, rpc status, api, etc corresponding to vendor_order_Id
func (s *Service) createVendorResponse(ctx context.Context, pgOrder *vgPgPb.CreateOrderResponse, actorId string, vendor commonvgpb.Vendor) {
	rawResponse, err := proto.Marshal(pgOrder)
	if err != nil {
		logger.Error(ctx, "error in marshal pg order response", zap.Error(err))
		return
	}

	err = s.vendorStore.Insert(ctx, actorId, vendor, pgOrder.GetVendorStatus(), pgOrder.GetStatus(), vendorstore.API_PAYMENT_GATEWAY_CREATE_ORDER, pgOrder.GetVendorOrderId(), string(rawResponse))
	if err != nil {
		logger.Error(ctx, "error in inserting data in vendor response table", zap.Error(err))
	}

	return
}

// createOrderVendorOrderMap will create entry in order_vendor_order_map table
// it will store orderId, vendor order id and vendor
func (s *Service) createOrderVendorOrderMap(ctx context.Context, ovom *orderPb.OrderVendorOrderMap) (*orderPb.OrderVendorOrderMap, error) {
	ovoms, err := s.orderVendorOrderMapDao.GetOrderVendorOrderMap(ctx, &orderPb.OrderVendorOrderMap{
		OrderId: ovom.GetOrderId(),
	}, []orderEnumsPb.OrderVendorOrderMapFieldMask{
		orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_ORDER_ID,
	})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound),
		err == nil && len(ovoms) == 0:
		createdOvom, createErr := s.orderVendorOrderMapDao.Create(ctx, ovom)
		if createErr != nil {
			return nil, fmt.Errorf("error in creating ovom for given order id : [%w]", createErr)
		}
		return createdOvom, nil
	case err != nil:
		return nil, fmt.Errorf("error in fetching ovom for the given order id : [%w]", err)
	default:
		return ovoms[0], nil
	}
}

func (s *Service) createOrder(ctx context.Context, order *orderPb.Order) (*orderPb.Order, error) {
	createdOrder, err := s.orderDao.Create(ctx, order, epificontext.OwnershipFromContext(ctx))
	if err != nil {
		return nil, err
	}
	return createdOrder, nil
}

// validateAmountAndClientExpiryTime will validate order amount with domain amount
// and also validate the client_req_id_expiry time with (time.Now + buffer) time for the workflow
func (s *Service) validateAmountAndClientExpiryTime(domainData *payPb.DomainOrderData) error {
	if domainData.GetClientRequestIdExpiry() != nil && !dateTimePkg.IsAfter(domainData.GetClientRequestIdExpiry(), timestamp.Now()) {
		return clientExpiryTimeIsNotValidErr
	}

	return nil
}

// checkIfOrderExists is doing the Idempotency check that handles
func (s *Service) checkIfOrderExists(ctx context.Context, clientReqId string) (*orderPb.Order, error) {
	if clientReqId == "" {
		return nil, nil
	}

	order, err := s.orderDao.GetByClientReqId(ctx, clientReqId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, fmt.Errorf("no order with the given client request id %w", epifierrors.ErrRecordNotFound)
	case err != nil:
		return nil, fmt.Errorf("error in fetching order with the given client req id : %w", err)
	default:
		return order, nil
	}
}

// createOrderAtVendorSide will create order at vendor side(in case of PG, eg: Razorpay)
func (s *Service) createOrderAtVendorSide(
	ctx context.Context,
	order *orderPb.Order,
	vendor commonvgpb.Vendor,
	domainData *payPb.DomainOrderData,
	ownership commontypes.Ownership,
	autoCaptureTimeoutOverride *durationpb.Duration) (*vgPgPb.CreateOrderResponse, *vgPgPb.AuthenticationParams, error) {

	var (
		accountDetails                  *vgPgPb.AccountDetails
		enforcePaymentAccountValidation bool
		err                             error
	)
	if len(domainData.GetAccountsEligibleForPaymentFulfillment()) > 0 {
		accountDetails = &vgPgPb.AccountDetails{
			AccountNumber:     domainData.GetAccountsEligibleForPaymentFulfillment()[0].GetAccountNumber(),
			IfscCode:          domainData.GetAccountsEligibleForPaymentFulfillment()[0].GetIfscCode(),
			AccountHolderName: domainData.GetAccountsEligibleForPaymentFulfillment()[0].GetName(),
		}
		enforcePaymentAccountValidation = true
	} else {
		enforcePaymentAccountValidation = false
	}

	authParams, authErr := pgauthkeys.GetAuthParamsForPgProgram(&types.PaymentGatewayProgram{
		PgVendor:        vendor,
		PiId:            domainData.GetToPi(),
		EntityOwnership: ownership,
	})
	if authErr != nil {
		return nil, nil, fmt.Errorf("error in fetching auth params for the given pg program : %w", authErr)
	}
	var paymentCaptureOptions *vgPgPb.PaymentCaptureOptions
	if autoCaptureTimeoutOverride != nil {
		paymentCaptureOptions = &vgPgPb.PaymentCaptureOptions{
			CaptureType:                vgPgPb.PaymentCaptureOptions_CAPTURE_TYPE_AUTOMATIC,
			AutomaticCapturePeriodMins: autoCaptureTimeoutOverride,
			RefundSpeed:                vgPgPb.PaymentCaptureOptions_REFUND_SPEED_NORMAL,
		}
	}
	vgReq := &vgPgPb.CreateOrderRequest{
		Header:                             &commonvgpb.RequestHeader{Vendor: vendor},
		Amount:                             types.GetFromBeMoney(order.GetAmount()),
		ReferenceId:                        order.GetClientReqId(),
		RequireAccountValidationForPayment: enforcePaymentAccountValidation,
		BankAccount:                        accountDetails,
		RecurringPaymentParameters:         nil,
		IsCreateOrderForChargingCustomer:   commontypes.BooleanEnum_TRUE,
		AuthenticationParams:               authParams,
		PaymentCaptureOptions:              paymentCaptureOptions,
	}
	pgOrderRes, err := s.pgClient.CreateOrder(ctx, vgReq)
	if te := epifigrpc.RPCError(pgOrderRes, err); te != nil {
		return nil, nil, fmt.Errorf("error in order creation at vendor side: %v", te)
	}

	return pgOrderRes, authParams, nil
}

func (s *Service) getRecurringPaymentDetailsForVendor(ctx context.Context, recurringPaymentId, customerId string) (*vgPgPb.RecurringPaymentParameters, error) {
	recurringPayment, err := s.recurringPaymentClient.GetRecurringPaymentById(ctx, &recurringpayment.GetRecurringPaymentByIdRequest{
		Id: recurringPaymentId,
	})
	if te := epifigrpc.RPCError(recurringPayment, err); te != nil {
		return nil, fmt.Errorf("error in fetching recurring payment details for vendor : %w", te)
	}
	var authorisationMethod vgPgPb.AuthorisationMethod
	switch recurringPayment.GetRecurringPayment().GetType() {
	case recurringpayment.RecurringPaymentType_UPI_MANDATES:
		authorisationMethod = vgPgPb.AuthorisationMethod_AUTHORISATION_METHOD_UPI
	case recurringpayment.RecurringPaymentType_ENACH_MANDATES:
		authorisationMethod = vgPgPb.AuthorisationMethod_AUTHORISATION_METHOD_EMANDATE
	}
	return &vgPgPb.RecurringPaymentParameters{
		AuthorisationMethod: authorisationMethod,
		PaymentCapture:      true,
		CustomerId:          customerId,
		Token: &vgPgPb.Token{
			MaxAmount: types.GetFromBeMoney(recurringPayment.GetRecurringPayment().GetAmount()),
			ExpireAt:  recurringPayment.GetRecurringPayment().GetInterval().GetEndTime(),
		},
	}, nil
}

// getDomainData will fetch domain data using processor
// which is implemented by other domain services
// based on workflow we get the processor
// and based on the processor we get the data
func (s *Service) getDomainData(ctx context.Context, workflow orderPb.OrderWorkflow, clientReqId, actorId string) (*payPb.DomainOrderData, error) {
	domainDataMethod, ok := s.orderWorkflowToDomainDataMethod[workflow]
	if !ok {
		return nil, fmt.Errorf("no domain data provider method mapping present for %s workflow", workflow.String())
	}
	res, err := domainDataMethod(ctx, &payPb.GetPaymentsDomainOrderDataRequest{
		ActorId:     actorId,
		ClientReqId: clientReqId,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error in fetching domain order data for workflow : %s : %w", workflow.String(), err)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("non success response while fetching domain order data for workflow : %s : %s", workflow.String(), res.GetStatus().String())
	default:
		return res.GetDomainOrderData(), nil
	}
}

// CreateOrderVendorOrderMap creates a mapping between an order and a vendor order.
// This is used to link the (internal order ID / recurring payment ID) with the vendor's order ID, facilitating tracking and managing orders across different systems.
func (s *Service) CreateOrderVendorOrderMap(ctx context.Context, req *payPb.CreateOrderVendorOrderMapRequest) (*payPb.CreateOrderVendorOrderMapResponse, error) {
	orderId := req.GetOrderId()
	vendorOrderId := req.GetVendorOrderId()
	vendor := req.GetVendor()
	ovoms, err := s.orderVendorOrderMapDao.GetOrderVendorOrderMap(ctx, &orderPb.OrderVendorOrderMap{
		OrderId:       orderId,
		VendorOrderId: vendorOrderId,
		Vendor:        vendor,
	}, []orderEnumsPb.OrderVendorOrderMapFieldMask{
		orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_ORDER_ID,
		orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_VENDOR_ORDER_ID,
		orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_VENDOR,
	})
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound),
		err == nil && len(ovoms) == 0:
		ovom, createErr := s.createOrderVendorOrderMap(ctx, &orderPb.OrderVendorOrderMap{
			OrderId:           orderId,
			VendorOrderId:     vendorOrderId,
			Vendor:            vendor,
			OrderDirection:    orderEnumsPb.OrderDirection_ORDER_DIRECTION_FORWARD,
			EntityOwnership:   req.GetEntityOwnership(),
			DomainReferenceId: req.GetDomainReferenceId(),
		})
		if createErr != nil {
			logger.Error(ctx, "error creating orderVendorOrderMap", zap.Error(createErr))
			res := &payPb.CreateOrderVendorOrderMapResponse{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		}
		res := &payPb.CreateOrderVendorOrderMapResponse{
			Status:              rpc.StatusOk(),
			OrderVendorOrderMap: ovom,
		}
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching ovom", zap.Error(err))
		res := &payPb.CreateOrderVendorOrderMapResponse{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	default:
		res := &payPb.CreateOrderVendorOrderMapResponse{
			Status:              rpc.StatusOk(),
			OrderVendorOrderMap: ovoms[0],
		}
		return res, nil
	}
}

// GetOrderVendorOrderMap fetches the mapping between an order and a vendor order.
// This is used to retrieve the OrderVendorOrderMap associated with a given (internal order ID / recurring payment ID) and vendor name.
func (s *Service) GetOrderVendorOrderMap(ctx context.Context, req *payPb.GetOrderVendorOrderMapRequest) (*payPb.GetOrderVendorOrderMapResponse, error) {
	var (
		res = &payPb.GetOrderVendorOrderMapResponse{}
		err error
	)
	orderId := req.GetOrderId()
	vendor := req.GetVendor()
	voMaps, err := s.orderVendorOrderMapDao.GetOrderVendorOrderMap(ctx, &orderPb.OrderVendorOrderMap{
		OrderId: orderId,
		Vendor:  vendor,
	}, []orderEnumsPb.OrderVendorOrderMapFieldMask{
		orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_ORDER_ID,
		orderEnumsPb.OrderVendorOrderMapFieldMask_ORDER_VENDOR_ORDER_MAP_FIELD_MASK_VENDOR,
	})
	if err != nil {
		res.Status = rpc.StatusInternal()
		if errors.Is(err, epifierrors.ErrRecordNotFound) || len(voMaps) == 0 {
			logger.Error(ctx, fmt.Sprintf("order not found in vendor mapping for orderId : %s", orderId), zap.Error(err))
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error getting orderVendorOrderMap by id ", zap.Error(err))
		return res, nil
	}
	res.Status = rpc.StatusOk()
	res.VendorOrderMaps = voMaps
	return res, nil
}
