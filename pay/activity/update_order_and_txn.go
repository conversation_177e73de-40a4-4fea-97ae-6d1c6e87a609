package activity

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	merchantPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay/activity/offappupiv2flow"
	timelinePb "github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/epifitemporal"
)

var (
	// offAppUpiFlowTxnStatusToOrderStatusMap: map of possible txn statuses in off-app-upi flow with order status
	offAppUpiFlowTxnStatusToOrderStatusMap = map[paymentPb.TransactionStatus]orderPb.OrderStatus{
		// terminal
		paymentPb.TransactionStatus_SUCCESS:  orderPb.OrderStatus_PAID,
		paymentPb.TransactionStatus_FAILED:   orderPb.OrderStatus_PAYMENT_FAILED,
		paymentPb.TransactionStatus_REVERSED: orderPb.OrderStatus_PAYMENT_REVERSED,

		// pending
		paymentPb.TransactionStatus_IN_PROGRESS: orderPb.OrderStatus_IN_PAYMENT,
		paymentPb.TransactionStatus_UNKNOWN:     orderPb.OrderStatus_IN_PAYMENT,

		// stuck
		paymentPb.TransactionStatus_MANUAL_INTERVENTION: orderPb.OrderStatus_MANUAL_INTERVENTION,
	}

	// offAppUpiFlowNewToAllowedCurrentTxnStatusesMap: map of allowed current txn statuses for moving to new txn status in off-app-upi flow.
	// Note:
	//	1. MANUAL_INTERVENTION is kept out of current-statuses so that we can first validate what's wrong with the txn and then re-initiate the workflow if required.
	offAppUpiFlowNewToAllowedCurrentTxnStatusesMap = map[paymentPb.TransactionStatus][]paymentPb.TransactionStatus{
		paymentPb.TransactionStatus_SUCCESS: {
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_UNKNOWN,
			paymentPb.TransactionStatus_SUCCESS,
		},
		paymentPb.TransactionStatus_FAILED: {
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_UNKNOWN,
			paymentPb.TransactionStatus_FAILED,
		},
		paymentPb.TransactionStatus_REVERSED: {
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_UNKNOWN,
			paymentPb.TransactionStatus_FAILED,
			paymentPb.TransactionStatus_REVERSED,
			// if a reversal (let's say credit txn comes) for a debit, we can let it move to REVERSED from MANUAL_INTERVENTION
			paymentPb.TransactionStatus_MANUAL_INTERVENTION,
		},
		paymentPb.TransactionStatus_IN_PROGRESS: {
			paymentPb.TransactionStatus_CREATED,
			paymentPb.TransactionStatus_INITIATED,
			paymentPb.TransactionStatus_UNKNOWN,
			// exception in case we want to move to IN_PROGRESS from MANUAL_INTERVENTION
			paymentPb.TransactionStatus_MANUAL_INTERVENTION,
			paymentPb.TransactionStatus_IN_PROGRESS,
		},
		paymentPb.TransactionStatus_UNKNOWN: {
			paymentPb.TransactionStatus_CREATED,
			paymentPb.TransactionStatus_INITIATED,
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_UNKNOWN,
		},
		paymentPb.TransactionStatus_MANUAL_INTERVENTION: {
			paymentPb.TransactionStatus_CREATED,
			paymentPb.TransactionStatus_INITIATED,
			paymentPb.TransactionStatus_IN_PROGRESS,
			paymentPb.TransactionStatus_UNKNOWN,
			paymentPb.TransactionStatus_MANUAL_INTERVENTION,
		},
	}

	// offAppUpiIrrevocableTxnStatuses : list of txn statuses which are irrevocable in off-app-upi flow, i.e. once a txn is in this status, it can't move to any other status.
	// Why is FAILED not considered as irrevocable?
	//	- FAILED can be updated to REVERSED. For e.g., if the txn was marked as FAILED & a corresponding credit txn came, we can mark it as REVERSED.
	offAppUpiIrrevocableTxnStatuses = []paymentPb.TransactionStatus{
		paymentPb.TransactionStatus_SUCCESS,
		paymentPb.TransactionStatus_REVERSED,
	}
)

// UpdateOrderAndTxn : Activity helps in updating the Txn and Order entities with the following:
// 1. New txn status, and corresponding Order status
// 2. Other Actor and PI entities (optional)
// 3. Order Tags (if applicable)
//
// Note: Currently it caters / assumes the flow to be for off-app-upi workflow
//
// nolint:funlen
func (p *Processor) UpdateOrderAndTxn(ctx context.Context, req *offappupiv2flow.UpdateOrderAndTxnRequest) (*offappupiv2flow.UpdateOrderAndTxnResponse, error) {
	var (
		lg             = activity.GetLogger(ctx)
		txnId, orderId = req.GetTransactionId(), req.GetOrderId()
		timelineId     = req.GetTimelineId()
		otherActorPiId = req.GetOtherActorPiId()

		// data to be pulled concurrently
		txn          *paymentPb.Transaction
		order        *orderPb.Order
		isMerchantPi bool
		timeline     *timelinePb.Timeline
	)

	// ========================================= FETCH DATA CONCURRENTLY ===============================================

	errGrp, gctx := errgroup.WithContext(ctx)
	// fetch the txn and order
	errGrp.Go(func() error {
		fetchedTxn, fetchedOrder, err := p.getTxnAndOrderById(gctx, txnId, orderId)
		if err != nil {
			return err
		}

		txn = fetchedTxn
		order = fetchedOrder
		return nil
	})
	// fetch the timeline if passed by the caller
	if timelineId != "" {
		// if no timeline-id is provided, we won't fetch any.
		// though, absence of timeline-id isn't a failure scenario. The caller didn't intend to get the actor-ids updated (via timeline).
		errGrp.Go(func() error {
			timelineRes, err := p.timelineClient.GetById(gctx, &timelinePb.GetByIdRequest{Id: timelineId})
			if rpcErr := epifigrpc.RPCError(timelineRes, err); rpcErr != nil {
				lg.Error("failed to get timeline details by id", zap.String(logger.TIMELINE_ID, timelineId), zap.Error(rpcErr))
				return epifitemporal.NewTransientError(fmt.Errorf("failed to get timeline by id: %s", timelineId))
			}

			timeline = timelineRes.GetTimeline()
			return nil
		})
	}
	// check if other-pi belongs to a merchant or not (if passed by the caller).
	// absence of other-pi-id is not a failure scenario. The caller didn't intend to update the other-pi-id.
	if otherActorPiId != "" {
		errGrp.Go(func() error {
			// check if the new pi belongs to a merchant or not
			piBelongsToMerchant, err := p.doesPiBelongToMerchant(gctx, otherActorPiId)
			if err != nil {
				return err
			}

			isMerchantPi = piBelongsToMerchant
			return nil
		})
	}

	if waitErr := errGrp.Wait(); waitErr != nil {
		lg.Error("error while fetching data for performing update of txn and order", zap.Error(waitErr))
		return nil, waitErr
	}

	// =================== SECTION TO PREPARE THE FIELD MASKS FOR UPDATING THE TXN AND ORDER ===========================

	var (
		currentTxnStatus   = txn.GetStatus()
		currentOrderStatus = order.GetStatus()

		txnUpdateMask                        []paymentPb.TransactionFieldMask
		orderUpdateMask                      []orderPb.OrderFieldMask
		otherActorIdFieldMask                orderPb.OrderFieldMask
		otherActorPiIdFieldMask              paymentPb.TransactionFieldMask
		primaryActorId                       string
		currentOtherActorId, newOtherActorId string
	)

	// figure out the current primary and current other-actor entities with update-masks
	if req.GetTransactionTransferType() == types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_CREDIT {
		primaryActorId = order.GetToActorId()
		currentOtherActorId = order.GetFromActorId()
		otherActorIdFieldMask = orderPb.OrderFieldMask_FROM_ACTOR_ID
		otherActorPiIdFieldMask = paymentPb.TransactionFieldMask_PI_FROM
	} else {
		primaryActorId = order.GetFromActorId()
		currentOtherActorId = order.GetToActorId()
		otherActorIdFieldMask = orderPb.OrderFieldMask_TO_ACTOR_ID
		otherActorPiIdFieldMask = paymentPb.TransactionFieldMask_PI_TO
	}

	// Finding other actor id from timeline response.
	// Since secondary actor from timeline need not correspond to other actor, below logic helps in finding the actual other-actor.
	// Note: Absence of timeline-id isn't a failure scenario. The caller didn't intend to get the actor-ids updated (via timeline).
	if timelineId != "" {
		newOtherActorId = lo.Ternary(primaryActorId == timeline.GetPrimaryActorId(), timeline.GetSecondaryActorId(), timeline.GetPrimaryActorId())
	}

	// update the other-actor-id in the order and prepare the update-mask (if new other-actor-id is available)
	if newOtherActorId != "" && newOtherActorId != currentOtherActorId {
		orderUpdateMask = append(orderUpdateMask, otherActorIdFieldMask)
		switch otherActorIdFieldMask {
		case orderPb.OrderFieldMask_FROM_ACTOR_ID:
			order.FromActorId = newOtherActorId
		case orderPb.OrderFieldMask_TO_ACTOR_ID:
			order.ToActorId = newOtherActorId
		}
	}

	txnUpdateMask = txn.SafeUpdateField(otherActorPiId, otherActorPiIdFieldMask, txnUpdateMask)
	txnUpdateMask = txn.SafeUpdateField(req.GetFinalStatusOfTransaction(), paymentPb.TransactionFieldMask_STATUS, txnUpdateMask)

	// update the order status if it has changed.
	// note: assuming off-app-upi flow for now.
	if newStatus := offAppUpiFlowTxnStatusToOrderStatusMap[txn.GetStatus()]; newStatus != order.GetStatus() {
		order.Status = newStatus
		orderUpdateMask = append(orderUpdateMask, orderPb.OrderFieldMask_STATUS)
	}

	// add the merchant tag if applicable
	if isMerchantPi && !lo.Contains(order.GetTags(), orderPb.OrderTag_MERCHANT) {
		order.Tags = append(order.GetTags(), orderPb.OrderTag_MERCHANT)
		orderUpdateMask = append(orderUpdateMask, orderPb.OrderFieldMask_TAGS)
	}

	// guardrail to ensure we are updating the txn status correctly based on the current txn status.
	// note: assuming off-app-upi flow for now.
	if allowedStatuses := offAppUpiFlowNewToAllowedCurrentTxnStatusesMap[txn.GetStatus()]; !lo.Contains(allowedStatuses, currentTxnStatus) {
		// If the txn status is irrevocable, we can return without updating the txn and order.
		// For e.g., if the current txn status is already in SUCCESS / REVERSED, then there is no point attempting to update
		// the txn to, let's say, FAILED status.
		if lo.Contains(offAppUpiIrrevocableTxnStatuses, currentTxnStatus) {
			lg.Warn("txn status is irrevocable, thus, returning gracefully.", zap.String(logger.TXN_ID, txn.GetId()),
				zap.String("currentTxnStatus", currentTxnStatus.String()), zap.String("newTxnStatus", txn.GetStatus().String()),
			)
			return &offappupiv2flow.UpdateOrderAndTxnResponse{}, nil
		}

		lg.Error("current txn status doesn't allow moving to new txn status", zap.String(logger.TXN_ID, txn.GetId()),
			zap.String("currentTxnStatus", currentTxnStatus.String()), zap.String("newTxnStatus", txn.GetStatus().String()),
		)

		// Returning transient error in the hope that the current-txn status was updated concurrently and we can retry.
		// Though, it is possible that we exhaust the retries altogether. In that case, we can manually check such workflows for the exact issue.
		return nil, epifitemporal.NewTransientError(fmt.Errorf("current txn status doesn't allow moving to new txn status: %s -> %s", currentTxnStatus.String(), txn.GetStatus().String()))
	}

	// =============================== UPDATE THE ORDER AND TXN IN A TXN BLOCK =========================================
	if updateErr := p.updateTxnAndOrderInTxnBlock(ctx, txn, order, txnUpdateMask, orderUpdateMask, currentTxnStatus, currentOrderStatus); updateErr != nil {
		return nil, updateErr
	}

	return &offappupiv2flow.UpdateOrderAndTxnResponse{}, nil
}

// doesPiBelongToMerchant : checks if the given PI ID belongs to a merchant or not.
//
// It also caters for returning the activity specific errors in case of error scenarios.
func (p *Processor) doesPiBelongToMerchant(ctx context.Context, piId string) (bool, error) {
	var (
		lg = activity.GetLogger(ctx)
	)

	// check if the new pi belongs to a merchant or not
	merchantRes, err := p.merchantClient.GetMerchant(ctx, &merchantPb.GetMerchantRequest{
		Identifier: &merchantPb.GetMerchantRequest_PiId{
			PiId: piId,
		},
	})
	switch {
	case err != nil:
		lg.Error("error while calling GetMerchant", zap.Error(err), zap.String(logger.PI_ID, piId))
		return false, epifitemporal.NewTransientError(fmt.Errorf("error while calling GetMerchant: %w", err))
	case merchantRes.GetStatus().IsRecordNotFound():
		return false, nil
	case merchantRes.GetStatus().IsSuccess():
		return true, nil
	default:
		lg.Error("unexpected status received from GetMerchant", zap.String(logger.RPC_STATUS, merchantRes.GetStatus().String()), zap.String(logger.PI_ID, piId))
		return false, epifitemporal.NewTransientError(fmt.Errorf("unknown status received from GetMerchant: %s", merchantRes.GetStatus().String()))
	}
}

// updateTxnAndOrderInTxnBlock : updates the txn and order in a txn block
//
// It also caters for returning the activity specific errors in case of error scenarios.
//
// Note: If no rows are updated, it's not considered as error
func (p *Processor) updateTxnAndOrderInTxnBlock(
	ctx context.Context,
	txn *paymentPb.Transaction, order *orderPb.Order,
	txnUpdateMask []paymentPb.TransactionFieldMask, orderUpdateMask []orderPb.OrderFieldMask,
	currentTxnStatus paymentPb.TransactionStatus, currentOrderStatus orderPb.OrderStatus,
) error {
	var (
		lg = activity.GetLogger(ctx)
	)

	txnErr := storageV2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		if err := p.txnDao.UpdateAndChangeStatus(txnCtx, txn, nil, txnUpdateMask, currentTxnStatus, txn.GetStatus()); err != nil {
			if !errors.Is(err, epifierrors.ErrRowNotUpdated) {
				return err
			}

			lg.Warn("no rows updated for txn", zap.String(logger.TXN_ID, txn.GetId()), zap.Any(logger.FIELD_MASKS, txnUpdateMask))

			// Checks to validate if the txn status was updated concurrently, i.e. current status isn't what we know as of now.
			//	1. If yes, we can return transient error so that the same is retried.
			if currentTxnStatus != txn.GetStatus() {
				lg.Error("txn didn't update even though the new and current status are different", zap.String(logger.TXN_ID, txn.GetId()),
					zap.String("currentTxnStatus", currentTxnStatus.String()), zap.String("newTxnStatus", txn.GetStatus().String()),
				)

				return fmt.Errorf("txn didn't update even though the new and current status are different: %s -> %s", currentTxnStatus.String(), txn.GetStatus().String())
			}
		}

		if err := p.orderDao.UpdateAndChangeStatus(ctx, order, orderUpdateMask, currentOrderStatus); err != nil {
			if !errors.Is(err, epifierrors.ErrRowNotUpdated) {
				return err
			}

			lg.Warn("no rows updated for order", zap.String(logger.ORDER_ID, order.GetId()), zap.Any(logger.FIELD_MASKS, orderUpdateMask))

			// Checks to validate if the order status was updated concurrently, i.e. current status isn't what we know as of now.
			//	1. If yes, we can return transient error so that the same is retried.
			if currentTxnStatus != txn.GetStatus() {
				lg.Error("order didn't update even though the new and current status are different", zap.String(logger.ORDER_ID, order.GetId()),
					zap.String("currentOrderStatus", currentOrderStatus.String()), zap.String("newOrderStatus", order.GetStatus().String()),
				)

				return fmt.Errorf("order didn't update even though the new and current status are different: %s -> %s", currentOrderStatus.String(), order.GetStatus().String())
			}
		}

		return nil
	})
	if txnErr != nil {
		lg.Error("error updating txn and order", zap.Error(txnErr), zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.ORDER_ID, order.GetId()))
		return epifitemporal.NewTransientError(fmt.Errorf("error updating txn and order: %w", txnErr))
	}

	// logging MANUAL_INTERVENTION cases so that they can be looked into by ops
	// Why "potentially"?
	//	- It's possible that the txn is already in some other terminal status (or was updated concurrently etc).
	if txn.GetStatus() == paymentPb.TransactionStatus_MANUAL_INTERVENTION {
		lg.Warn("txn potentially moved to MANUAL_INTERVENTION status", zap.String(logger.TXN_ID, txn.GetId()), zap.String(logger.ORDER_ID, order.GetId()))
	}

	return nil
}

// getTxnAndOrderById : returns the txn and order by the respective IDs passed.
//
// Note: It assumes permanent failure for any activity using this in case the expected entities are not found.
func (p *Processor) getTxnAndOrderById(ctx context.Context, txnId, orderId string) (*paymentPb.Transaction, *orderPb.Order, error) {
	var (
		lg = activity.GetLogger(ctx)
	)

	txn, err := p.txnDao.GetById(ctx, txnId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, nil, epifitemporal.NewPermanentError(fmt.Errorf("no txn found for id: %s", txnId))
		}

		lg.Error("unknown error while fetching txn by id", zap.String(logger.TXN_ID, txnId), zap.Error(err))
		return nil, nil, epifitemporal.NewTransientError(fmt.Errorf("unknown error while fetching txn by id: %s, err: %w", txnId, err))
	}

	order, err := p.orderDao.GetById(ctx, orderId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, nil, epifitemporal.NewPermanentError(fmt.Errorf("no order found for id: %s", orderId))
		}

		lg.Error("unknown error while fetching order by id", zap.String(logger.ORDER_ID, orderId), zap.Error(err))
		return nil, nil, epifitemporal.NewTransientError(fmt.Errorf("unknown error while fetching order by id: %s, err: %w", orderId, err))
	}

	return txn, order, nil
}
