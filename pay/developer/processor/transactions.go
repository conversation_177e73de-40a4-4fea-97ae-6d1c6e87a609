package processor

import (
	"context"
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/proto/json"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay/developer"
	"github.com/epifi/gamma/pay/dao"
)

type TransactionProcessor struct {
	transactionDao dao.TransactionDao
}

func NewTransactionProcessor(transactionDao dao.TransactionDao) *TransactionProcessor {
	return &TransactionProcessor{
		transactionDao: transactionDao,
	}
}

func (d *TransactionProcessor) FetchParamList(ctx context.Context, entity developer.PayEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            PartnerRefIdDebit,
			Label:           "Partner Transaction Id Debit",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            PartnerRefIdCredit,
			Label:           "Partner Transaction Id Credit",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ownership,
			Label:           "Ownership",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getOwnershipList(),
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *TransactionProcessor) FetchData(ctx context.Context, entity developer.PayEntity, filters []*db_state.Filter) (string, error) {
	var (
		transaction          *paymentPb.Transaction
		err                  error
		transactionJsonBytes []byte
		inputOwnership       commontypes.Ownership
	)
	if len(filters) == 0 {
		return "", epifierrors.ErrInvalidArgument
	}

	// Find the passed inputOwnership and set it in ctx. The default inputOwnership is assumed to be EPIFI_TECH.
	// If the inputOwnership is encoded in the input ID, then that inputOwnership will be honored, instead of this.
	for _, filter := range filters {
		if filter.GetParameterName() == ownership {
			inputOwnership = commontypes.Ownership(commontypes.Ownership_value[filter.GetDropdownValue()])
		}
	}
	ctx = epificontext.WithOwnership(ctx, inputOwnership)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case PartnerRefIdCredit:
			transaction, err = d.transactionDao.GetByPartnerRefIdCredit(ctx, filter.GetStringValue())
		case PartnerRefIdDebit:
			transaction, err = d.transactionDao.GetByPartnerRefIdDebit(ctx, filter.GetStringValue())
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetStringValue())

		}
	}
	if err != nil {
		return "", fmt.Errorf("failed to fetch the transaction %w", err)
	}
	transaction = redactTransaction(transaction)
	transactionJsonBytes, err = json.Marshal(transaction)
	if err != nil {
		return "", fmt.Errorf("failed to marshal the proto message to json %w", err)
	}
	return string(transactionJsonBytes), nil
}

func redactTransaction(txn *paymentPb.Transaction) *paymentPb.Transaction {
	txn.Amount = nil
	txn.Remarks = ""
	return txn
}

func getOwnershipList() []string {
	ownershipList := make([]string, 0)
	for _, ownership := range commontypes.Ownership_name {
		ownershipList = append(ownershipList, ownership)
	}
	return ownershipList
}
