package upi

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/rulerover/rule"
	"github.com/epifi/be-common/pkg/rulerover/types"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/pay/internal/decisionengine/constants"
	"github.com/epifi/gamma/pay/internal/decisionengine/factengine/facts"
)

type PreferredUPI struct {
}

var _ rule.Rule = &PreferredUPI{}

func NewPreferredUPI() *PreferredUPI {
	return &PreferredUPI{}
}

func (p *PreferredUPI) Name() rule.RuleName { return constants.RulePreferredUPI }
func (p *PreferredUPI) Area() string        { return "PAY" }
func (p *PreferredUPI) IsShadow() bool      { return false }
func (p *PreferredUPI) GroupName() string   { return "UPI" }
func (p *PreferredUPI) Priority() int       { return 0 }
func (p *PreferredUPI) Version() int        { return 0 }

func (p *PreferredUPI) Execute(ctx context.Context, execInput *rule.ExecutionInput) (*rule.ExecutionResult, error) {
	var (
		paymentInitPayload *facts.PaymentInitPayload
	)

	// load facts
	fact1, ok := execInput.FactMap.Load(constants.FactPaymentInit)
	if ok {
		pd, err := fact1.Get(ctx)
		if err != nil {
			return nil, fmt.Errorf("error fetching Fact %s: %w", fact1.Name(), err)
		}

		paymentInitPayload = pd.(*facts.PaymentInitPayload)
	}

	if paymentInitPayload == nil {
		return nil, fmt.Errorf("fact PAYMENT_INIT not found")
	}

	// rule logic
	if len(paymentInitPayload.PreferredPaymentProtocol) != 0 && !lo.Contains(paymentInitPayload.PreferredPaymentProtocol, paymentPb.PaymentProtocol_UPI) {
		return &rule.ExecutionResult{
			Result: types.RuleResultFailed,
		}, nil
	}

	return &rule.ExecutionResult{
		Result: types.RuleResultPassed,
	}, nil
}
