package order

import (
	"context"
	"fmt"
	"time"

	orderPb "github.com/epifi/gamma/api/order"
	payFilterPb "github.com/epifi/gamma/api/pay/workflow/filter"
	"github.com/epifi/gamma/pay/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
)

type Processor struct {
	orderDao dao.OrderDao
}

func NewProcessor(orderDao dao.OrderDao) *Processor {
	return &Processor{orderDao: orderDao}
}

// getOrdersByStageAndSlaFilter - filters orders for the given stage and SLA
func (p *Processor) getOrdersByStageAndSlaFilter(ctx context.Context, filterOptions *payFilterPb.StageWithSlaOption,
	fromTime time.Time, toTime time.Time, workflow orderPb.OrderWorkflow) (int64, error) {
	var (
		c   int64
		err error
	)
	statusList := filterOptions.GetStageFilterOption().GetStatusFilterOption().GetStatusList()
	if len(statusList) > 0 {
		c, err = p.orderDao.GetOrderCountByStage(ctx, fromTime, toTime, filterOptions.GetStageFilterOption().GetStage(), workflow, dao.WithOrderStatus(statusList), dao.WithSla(filterOptions.GetSla()))
	} else {
		c, err = p.orderDao.GetOrderCountByStage(ctx, fromTime, toTime, filterOptions.GetStageFilterOption().GetStage(), workflow, dao.WithSla(filterOptions.GetSla()))
	}
	if err != nil {
		return 0, fmt.Errorf("error while filtering orders %w", err)
	}
	return c, nil
}

// getOrdersByStageFilter - filters orders for stage option
func (p *Processor) getOrdersByStageFilter(ctx context.Context, filterOptions *payFilterPb.StageOption,
	fromTime time.Time, toTime time.Time, workflow orderPb.OrderWorkflow) (int64, error) {
	var (
		c   int64
		err error
	)
	statusList := filterOptions.GetStatusFilterOption().GetStatusList()
	if len(statusList) > 0 {
		c, err = p.orderDao.GetOrderCountByStage(ctx, fromTime, toTime, filterOptions.GetStage(), workflow, dao.WithOrderStatus(statusList))
	} else {
		c, err = p.orderDao.GetOrderCountByStage(ctx, fromTime, toTime, filterOptions.GetStage(), workflow)
	}
	if err != nil {
		return 0, fmt.Errorf("error while filtering orders %w", err)
	}
	return c, nil
}

// GetOrderByStage - enables filtering of workflow in a given stage and status
func (p *Processor) GetOrderByStage(ctx context.Context, fromTime time.Time, toTime time.Time, stageOption interface{}, workflow orderPb.OrderWorkflow) (*payFilterPb.FilterWithCount, error) {
	var (
		res = &payFilterPb.FilterWithCount{}
		c   int64
		err error
	)
	switch val := stageOption.(type) {
	case *payFilterPb.Filter_StageFilterOption:
		c, err = p.getOrdersByStageFilter(ctx, val.StageFilterOption, fromTime, toTime, workflow)
		if err != nil {
			return nil, err
		}

		res.Count = c
		res.Filter = &payFilterPb.Filter{
			Option: val,
		}

	case *payFilterPb.Filter_StageWithSlaFilterOption:
		c, err = p.getOrdersByStageAndSlaFilter(ctx, val.StageWithSlaFilterOption, fromTime, toTime, workflow)
		if err != nil {
			return nil, err
		}

		res.Count = c
		res.Filter = &payFilterPb.Filter{
			Option: val,
		}

	default:
		return nil, fmt.Errorf("failed to fetch orders for given stageOption: %T: %w", stageOption, epifierrors.ErrPermanent)
	}
	return res, nil
}

// GetOrderByStatus - enables filtering workflow stuck in a set of workflow statuses irrespective of the workflow stage
func (p *Processor) GetOrderByStatus(ctx context.Context, fromTime time.Time, toTime time.Time, statusOption interface{}, workflow orderPb.OrderWorkflow) (*payFilterPb.FilterWithCount, error) {
	var (
		res = &payFilterPb.FilterWithCount{}
		c   int64
		err error
	)
	switch val := statusOption.(type) {
	case *payFilterPb.Filter_StatusFilterOption:
		c, err = p.orderDao.GetOrderCountByStatus(ctx, fromTime, toTime, val.StatusFilterOption.GetStatusList(), workflow)
		if err != nil {
			return nil, fmt.Errorf("error while filtering orders  %w", err)
		}

		res.Count = c
		res.Filter = &payFilterPb.Filter{
			Option: val,
		}

	case *payFilterPb.Filter_StatusWithSlaFilterOption:
		c, err = p.orderDao.GetOrderCountByStatus(ctx, fromTime, toTime, val.StatusWithSlaFilterOption.GetStatusFilterOption().GetStatusList(), workflow, dao.WithSla(val.StatusWithSlaFilterOption.GetSla()))
		if err != nil {
			return nil, fmt.Errorf("error while filtering orders %w", err)
		}

		res.Count = c
		res.Filter = &payFilterPb.Filter{
			Option: val,
		}

	default:
		return nil, fmt.Errorf("failed to fetch orders for given statusOption: %T: %w", statusOption, epifierrors.ErrPermanent)
	}
	return res, nil
}
