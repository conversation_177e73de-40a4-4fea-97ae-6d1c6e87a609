package consumer

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	actorPb "github.com/epifi/gamma/api/actor"
	pb "github.com/epifi/gamma/api/comms"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	fcmPb "github.com/epifi/gamma/api/vendorgateway/fcm"
	"github.com/epifi/gamma/comms/dao"
	"github.com/epifi/gamma/comms/helper"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
)

type WhatsappServiceGD struct {
	fcmClient      fcmPb.FCMClient
	dao            dao.CommsStorage
	userClient     userPb.UsersClient
	deviceTokenDao dao.DeviceTokenStorage
	actorClient    actorPb.ActorClient
	helperSvc      helper.IHelperService
}

func NewWhatsappServiceGD(fcmClient fcmPb.FCMClient, dao dao.CommsStorage, userClient userPb.UsersClient,
	deviceTokenDao dao.DeviceTokenStorage, actorClient actorPb.ActorClient, helperSvc helper.IHelperService) *WhatsappServiceGD {
	return &WhatsappServiceGD{
		fcmClient:      fcmClient,
		dao:            dao,
		userClient:     userClient,
		deviceTokenDao: deviceTokenDao,
		actorClient:    actorClient,
		helperSvc:      helperSvc,
	}
}

var (
	WaTransFailure = &pb.ProcessWhatsappResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	WaSuccess = &pb.ProcessWhatsappResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	WaPermFailure = &pb.ProcessWhatsappResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
)

func (w *WhatsappServiceGD) ProcessWhatsappEvent(ctx context.Context, req *pb.ProcessWhatsappRequest) (*pb.ProcessWhatsappResponse, error) {
	isLastAttempt := req.GetRequestHeader().GetIsLastAttempt()
	actorId := epificontext.ActorIdFromContext(ctx)
	msg, err := w.helperSvc.GetOrCreateCommsMessage(ctx, req.GetSendMessageRequest(), actorId, req.GetCommsMessageId())
	if err != nil {
		logger.Error(ctx, "failed to create or get comms message", zap.Error(err))
		return WaTransFailure, errors.Wrap(err, "failure in create or get comms message")
	}

	// No status API present for vendor message ID, hence we rely only on db states and callbacks and retry if not marked success
	if msg.Status == pb.MessageState_DELIVERED.String() || msg.Status == pb.MessageState_FAILED_UNSUBSCRIBED.String() {
		return WaSuccess, nil
	}
	// Do not retry if status of message is sent to vendor
	if msg.Status == pb.MessageState_SENT_TO_VENDOR.String() {
		return WaTransFailure, nil
	}
	actor, phoneNumber, response, err := w.getActorAndPhone(ctx, req)
	// In case of whatsapp comms, actor fetch is mandatory since we need to check user preference for this actor
	// we will skip this check on in case of whatsapp bot replies
	if response != nil || err != nil {
		if req.GetClientId() != string(cfg.WHATSAPP_BOT_SERVICE) {
			w.helperSvc.UpdateStatusInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_TRANSIENT_FAILURE, isLastAttempt)
			return response, err
		}
	}
	// skip preference check if call is made by whatsapp bot service
	// we will allow bot to reply to users who has opted out since opt-out is for whatsapp updates initiated by us
	// user can opt out for updates but still use whatsapp bot features
	if req.GetClientId() != string(cfg.WHATSAPP_BOT_SERVICE) {
		if checkErr := w.helperSvc.CheckOrCreatePreferenceForWaAndSendWelcome(ctx, actor.GetId(), phoneNumber, req.GetWhatsapp()); checkErr != nil {
			if errors.Is(checkErr, helper.ErrUnsubscribed) {
				w.helperSvc.UpdateStatusInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_FAILED_UNSUBSCRIBED, isLastAttempt)
				return WaSuccess, nil
			}
			w.helperSvc.UpdateStatusInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_TRANSIENT_FAILURE, isLastAttempt)
			return WaTransFailure, checkErr
		}
	}

	vendorMsgId, vendor, sendErr := w.helperSvc.SendWhatsapp(ctx, req.GetWhatsapp(), phoneNumber)
	if sendErr != nil {
		w.helperSvc.UpdateVendorAttemptInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_TRANSIENT_FAILURE, msg.Retries+1,
			vendorMsgId, vendor, isLastAttempt, "", pb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_UNSPECIFIED)
		return WaTransFailure, sendErr
	}
	w.helperSvc.UpdateVendorAttemptInCommsTable(ctx, req.GetCommsMessageId(), pb.MessageState_SENT_TO_VENDOR, msg.Retries+1,
		vendorMsgId, vendor, isLastAttempt, "", pb.CommsMessageSubStatus_COMMS_MESSAGE_SUB_STATUS_UNSPECIFIED)
	return WaSuccess, nil
}

func (w *WhatsappServiceGD) getActorAndPhone(ctx context.Context, req *pb.ProcessWhatsappRequest) (*types.Actor, string, *pb.ProcessWhatsappResponse, error) {
	var actor *types.Actor
	var actorErr error
	var phoneNumber string
	switch req.GetUserIdentifier().(type) {
	case *pb.ProcessWhatsappRequest_UserId:
		user, userErr := w.helperSvc.GetUser(ctx, &pb.ActorIdentifier{Identifier: &pb.ActorIdentifier_UserId{UserId: req.GetUserId()}})
		if userErr != nil {
			return nil, "", WaTransFailure, userErr
		}
		phoneNumber = user.GetProfile().GetPhoneNumber().ToString()
		if phoneNumber == "" {
			return nil, "", WaPermFailure, errors.New("no phone number found in profile of user")
		}
	case *pb.ProcessWhatsappRequest_PhoneNumber:
		phoneNumber = req.GetPhoneNumber()
	default:
		return nil, "", WaPermFailure, errors.New("request does not have valid inputs to get phone")
	}

	actor, actorErr = w.helperSvc.GetActor(ctx, &pb.ActorIdentifier{Identifier: &pb.ActorIdentifier_Phone{Phone: phoneNumber}})

	return actor, phoneNumber, nil, actorErr
}
