package model

import "time"

type FCMDeviceToken struct {
	ID          string `gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	ActorId     string
	DeviceToken string
	Status      string
	// Standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time
}

type DeviceTokenMask int

const (
	UNSPECIFIED_DEVICE_TOKEN DeviceTokenMask = iota
	ACTOR_ID
	DEVICE_TOKEN
	DEVICE_TOKEN_STATUS
)

var DeviceTokenColumnNameMap = map[DeviceTokenMask]string{
	ACTOR_ID:            "actor_id",
	DEVICE_TOKEN:        "device_token",
	DEVICE_TOKEN_STATUS: "status",
}
