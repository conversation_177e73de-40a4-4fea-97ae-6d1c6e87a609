package model

import (
	"database/sql"
	"time"

	"github.com/epifi/be-common/pkg/nulltypes"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/leads"
)

type UserLead struct {
	Id                string
	ActorId           nulltypes.NullString
	ClientRequestId   string
	ProductType       leads.ProductType
	ClientId          string
	Pan               nulltypes.NullString
	MobileNumber      nulltypes.NullString
	Email             nulltypes.NullString
	PersonalDetails   *leads.PersonalDetails
	AdditionalDetails *leads.AdditionalDetails
	LeadStatus        leads.UserLeadStatus
	CompletedAt       sql.NullTime
	ExpiredAt         sql.NullTime
	CreatedAt         time.Time
	UpdatedAt         time.Time
}

// TableName overrides the table name
func (*UserLead) TableName() string {
	return "user_leads"
}

func NewUserLead(details *leads.UserLead) *UserLead {
	res := &UserLead{
		Id:                details.GetId(),
		ActorId:           nulltypes.NewNullString(details.GetActorId()),
		ClientRequestId:   details.GetClientRequestId(),
		ProductType:       details.GetProductType(),
		ClientId:          details.GetClientId(),
		Pan:               nulltypes.NewNullString(details.GetPan()),
		MobileNumber:      nulltypes.NewNullString(details.GetMobileNumber()),
		Email:             nulltypes.NewNullString(details.GetEmail()),
		PersonalDetails:   details.GetPersonalDetails(),
		AdditionalDetails: details.GetAdditionalDetails(),
		LeadStatus:        details.GetLeadStatus(),
	}

	if details.GetExpiredAt().IsValid() {
		res.ExpiredAt = sql.NullTime{
			Time:  details.GetExpiredAt().AsTime(),
			Valid: true,
		}
	}
	if details.GetCompletedAt().IsValid() {
		res.CompletedAt = sql.NullTime{
			Time:  details.GetCompletedAt().AsTime(),
			Valid: true,
		}
	}
	return res
}

func (l *UserLead) GetProto() *leads.UserLead {
	userLeadProto := &leads.UserLead{
		Id:                l.Id,
		ActorId:           l.ActorId.GetValue(),
		ClientRequestId:   l.ClientRequestId,
		ProductType:       l.ProductType,
		ClientId:          l.ClientId,
		Pan:               l.Pan.GetValue(),
		MobileNumber:      l.MobileNumber.GetValue(),
		Email:             l.Email.GetValue(),
		PersonalDetails:   l.PersonalDetails,
		AdditionalDetails: l.AdditionalDetails,
		LeadStatus:        l.LeadStatus,
		CreatedAt:         timestampPb.New(l.CreatedAt),
		UpdatedAt:         timestampPb.New(l.UpdatedAt),
	}

	if l.CompletedAt.Valid {
		userLeadProto.CompletedAt = timestampPb.New(l.CompletedAt.Time)
	}
	if l.ExpiredAt.Valid {
		userLeadProto.ExpiredAt = timestampPb.New(l.ExpiredAt.Time)
	}
	return userLeadProto
}
