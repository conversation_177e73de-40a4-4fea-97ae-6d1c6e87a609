// nolint: dupl
package dao

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	pb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/card/dao/model"
)

// Implements CardActivationRequestDao using CRDB
type CardActivationRequestDaoCRDB struct {
	db *gorm.DB
}

// Ensure that CardActivationRequestDaoCRDB implements CardActivationRequestDao interface.
var _ CardActivationRequestDao = &CardActivationRequestDaoCRDB{}

// Factory method for creating an instance of CardActivationRequest dao. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewCardActivationRequestDaoCRDB(db DebitCardPGDB) *CardActivationRequestDaoCRDB {
	return &CardActivationRequestDaoCRDB{db: db}
}

func (cad *CardActivationRequestDaoCRDB) Create(ctx context.Context, caReq *pb.CardActivationRequest) (*pb.CardActivationRequest, error) {
	defer metric_util.TrackDuration("card/dao", "CardActivationRequestDaoCRDB", "Create", time.Now())
	caModel := cad.convertToModel(caReq)
	res := cad.db.Create(caModel)
	if res.Error != nil {
		logger.Error(ctx, fmt.Sprintf("error in creating card model: %v: %v", res.Error, caModel))
		return nil, res.Error
	}
	resp := cad.convertToProto(res.Statement.Model.(*model.CardActivationRequest))
	return resp, nil
}

func (cad *CardActivationRequestDaoCRDB) GetByID(_ context.Context, id string) (*pb.CardActivationRequest, error) {
	defer metric_util.TrackDuration("card/dao", "CardActivationRequestDaoCRDB", "GetByID", time.Now())
	var mdl model.CardActivationRequest
	res := cad.db.Where(&model.CardActivationRequest{
		ID: id,
	}).Take(&mdl)

	if res.Error != nil {
		return nil, res.Error
	}
	return cad.convertToProto(&mdl), nil
}

func (cad *CardActivationRequestDaoCRDB) convertToModel(proto *pb.CardActivationRequest) *model.CardActivationRequest {
	caModel := &model.CardActivationRequest{
		ID:         proto.Id,
		CardId:     proto.CardId,
		State:      proto.State,
		NumRetries: strconv.Itoa(int(proto.Retries)),
	}
	return caModel
}

func (cad *CardActivationRequestDaoCRDB) convertToProto(caReq *model.CardActivationRequest) *pb.CardActivationRequest {
	NumRetries, _ := strconv.ParseInt(caReq.NumRetries, 10, 32)
	caProto := &pb.CardActivationRequest{
		Id:      caReq.ID,
		CardId:  caReq.CardId,
		State:   caReq.State,
		Retries: uint32(NumRetries),
	}
	return caProto
}
