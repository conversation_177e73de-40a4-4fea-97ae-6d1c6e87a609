groups:
- name: popular-faqs.rules
  rules:
    ## High CPU usage alert
    - alert: PopularFAQsHighCPU
      expr: sum(rate(container_cpu_usage_seconds_total{image!="",container!="POD", container!="", pod=~"data-dev-popular-faqs-.*"}[5m])) by (pod)/topk(1, sum(kube_pod_container_resource_limits{unit="core",pod=~"data-dev-popular-faqs-.*"}) by (pod)) > 0.4
      for: 15m
      labels:
        severity: "p1"
        service: "ds-popular-faqs"
        team: "datascience"
      annotations:
        summary: "Popular FAQs service high CPU"
        description: "CPU usage on popular faqs has been breaching limit"
    ## High Memory usage alert
    - alert: PopularFAQsHighMemory
      expr: sum(container_memory_working_set_bytes{pod=~"data-dev-popular-faqs-.*", container!~"POD", container!~""})/sum(kube_pod_container_resource_limits{pod=~"data-dev-popular-faqs-.*", container!~"POD", container!~""}) * 100 > 60
      for: 15m
      labels:
        severity: "p1"
        service: "ds-popular-faqs"
        team: "datascience"
      annotations:
        summary: "Popular FAQs service high memory"
        description: "Memory usage on popular faqs has been breaching limit"
    ## High API response time
    - alert: PopularFAQsHighAPIResponseTime
      expr: histogram_quantile(0.95, sum by (le) (rate(popular_faqs_requests_processing_time_seconds_bucket[5m]))) > 0.01
      for: 15m
      labels:
        severity: "p1"
        service: "ds-popular-faqs"
        team: "datascience"
      annotations:
        summary: "Popular FAQs service high response time"
        description: "Response time on popular faqs has been breaching limit"
    ## Service not available
    - alert: PopularFAQsServiceDown
      expr: absent(sum(popular_faqs_requests_total{path_template="/_health"})) == 1
      for: 10m
      labels:
        severity: "p1"
        service: "ds-popular-faqs"
        team: "datascience"
      annotations:
        summary: "Popular FAQs service is down"
        description: "Popular FAQs service API response is down"
