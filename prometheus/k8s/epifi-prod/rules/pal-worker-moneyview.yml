groups:
  - name: pal-worker.rules
    rules:
      - alert: MoneyviewLeadCreationStageDropOffSpike
        expr: (sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD", status=~"LOAN_STEP_EXECUTION_STATUS_SUCCESS"}[6h])) by (vendor, loan_program, flow_name, step_name) or on() vector(0))/ sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD", status=~"LOAN_STEP_EXECUTION_STATUS_CREATED"}[6h])) by (vendor, loan_program, flow_name, step_name) * 100 < 40
        for: 15m
        labels:
          severity: "p1"
          team: "lending"
          service: "preapprovedloan-worker"
          category: "biz-alert"
        annotations:
          summary: "Spike in drop offs in Moneyview lead creation stage in loan application flow"

      - alert: MoneyviewLeadCreationStageStuckInManualIntervention
        expr: sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD", status=~"LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION"}[15m])) by (vendor, loan_program, flow_name, step_name) >= 1
        for: 5m
        labels:
          severity: "p1"
          team: "lending"
          service: "preapprovedloan-worker"
        annotations:
          summary: "Moneyview Lead Creation Stage stuck in Manual Intervention state"

      - alert: MoneyviewLoanAccountCreationStageFailure
        expr: sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION", status=~"LOAN_STEP_EXECUTION_STATUS_FAILED"}[15m])) by (vendor, loan_program, flow_name, step_name) >= 1
        for: 5m
        labels:
          severity: "p1"
          team: "lending"
          service: "preapprovedloan-worker"
        annotations:
          summary: "Moneyview Loan Account Creation Stage Failed"

      - alert: MoneyviewLoanAccountCreationStageStuckInManualIntervention
        expr: sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION", status=~"LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION"}[15m])) by (vendor, loan_program, flow_name, step_name) >= 1
        for: 5m
        labels:
          severity: "p1"
          team: "lending"
          service: "preapprovedloan-worker"
        annotations:
          summary: "Moneyview Loan Account Creation Stage stuck in Manual Intervention state"

      - alert: MoneyviewFetchOfferStageDropOffSpike
        expr: (sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER", status=~"LOAN_STEP_EXECUTION_STATUS_SUCCESS"}[3h])) by (vendor, loan_program, flow_name, step_name) or on() vector(0))/ sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER", status=~"LOAN_STEP_EXECUTION_STATUS_CREATED"}[3h])) by (vendor, loan_program, flow_name, step_name) * 100 < 50
        for: 5m
        labels:
          severity: "p1"
          team: "lending"
          service: "preapprovedloan-worker"
          category: "biz-alert"
        annotations:
          summary: "Spike in drop offs in Moneyview offer fetch stage in loan application flow"

      - alert: MoneyviewFetchOfferStageStuckInManualIntervention
        expr: sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER", status=~"LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION"}[15m])) by (vendor, loan_program, flow_name, step_name)  >= 1
        for: 5m
        labels:
          severity: "p1"
          team: "lending"
          service: "preapprovedloan-worker"
        annotations:
          summary: "Moneyview Fetch Offer Stage stuck in Manual Intervention state"

      - alert: MoneyviewVendorPwaStageStuckInManualIntervention
        expr: sum(increase(loan_step_execution_status_update_total{vendor="MONEYVIEW", loan_program="LOAN_PROGRAM_PRE_APPROVED_LOAN", flow_name="LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION", step_name="LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES", status=~"LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION"}[15m])) by (vendor, loan_program, flow_name, step_name) >= 1
        for: 5m
        labels:
          severity: "p1"
          team: "lending"
          service: "preapprovedloan-worker"
        annotations:
          summary: "Moneyview Vendor PWA Stage stuck in Manual Intervention state"
