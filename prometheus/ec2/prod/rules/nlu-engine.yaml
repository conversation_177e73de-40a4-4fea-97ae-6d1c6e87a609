groups:
- name: nlu-engine.rules
  rules:
  - alert: IntentClassifierConnectionError
    expr: increase(nlu_conn_error_counter_total{port="9001"}[1m]) > 0
    for: 1m
    labels:
     severity: "p0"
     team: "datascience"
    annotations:
      summary: "NLU Intent Classifier ConnectionError Alert"
      description: "NLU Intent Classifier ConnectionError occurrence is more than usual. Error count is {{ humanize $value}} in past 1 minute on port 9001."
  - alert: NERTaggerConnectionError
    expr: increase(nlu_conn_error_counter_total{port="9002"}[1m]) > 0
    for: 1m
    labels:
     severity: "p0"
     team: "datascience"
    annotations:
      summary: "NLU NER Tagger ConnectionError Alert"
      description: "NLU NER Tagger ConnectionError occurrence is more than usual. Error count is {{ humanize $value}} in past 1 minute on port 9002."
  - alert: NLUTotalResponseTimeHigh
    expr: histogram_quantile(0.95, sum by (le) (rate(nlu_prediction_time_seconds_bucket{model="nlu"}[5m]))) > 0.055
    for: 1m
    labels:
      severity: "p1"
      team: "datascience"
    annotations:
      summary: "NLU High Response Time Alert"
      description: "NLU total response time is more than usual. 95th percentile of response time is {{ humanize $value }} in past 1 minute."
