groups:
  - name: search-api.rules
    rules:
      - alert: Search
        expr: |
          sum(rate(grpc_server_handled_total{grpc_type="unary", grpc_code!~"OK|Canceled|NotFound|InvalidArgument", grpc_method="Search", grpc_service="search.ActionBar"}[15m])) by (environment, grpc_service, grpc_method) / sum(rate(grpc_server_started_total{grpc_type="unary", grpc_method="Search", grpc_service="search.ActionBar"}[15m])) by (environment, grpc_service, grpc_method) * 100 > 1 and sum(rate(grpc_server_handled_total{grpc_type="unary", grpc_code!~"OK|Canceled|NotFound|InvalidArgument", grpc_method="Search", grpc_service="search.ActionBar"}[15m])) by (environment, grpc_service, grpc_method) > 5
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: SearchClient
        expr: |
          sum(rate(grpc_client_handled_total{grpc_type="unary", grpc_code!~"OK|Canceled|NotFound|InvalidArgument", grpc_method="Search", grpc_service="search.ActionBar"}[15m])) by (environment, grpc_service, grpc_method) / sum(rate(grpc_client_handled_total{grpc_type="unary", grpc_method="Search", grpc_service="search.ActionBar"}[15m])) by (environment, grpc_service, grpc_method) * 100 > 1 and sum(rate(grpc_client_handled_total{grpc_type="unary", grpc_code!~"OK|Canceled|NotFound|InvalidArgument", grpc_method="Search", grpc_service="search.ActionBar"}[15m])) by (environment, grpc_service, grpc_method) > 5
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "client alert - search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "client alert - search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: SearchHelp
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|NotFound|Canceled",grpc_method="SearchHelp",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="SearchHelp",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method) * 100 > 5 and ON() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|NotFound|Canceled",grpc_method="SearchHelp",grpc_service="search.ActionBar"}[15m]))) > 3
        for: 1m
        labels:
          severity: "p1"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: SearchHelpClient
        expr: sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_code!~"OK|NotFound|Canceled",grpc_method="SearchHelp",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_method="SearchHelp",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 1 and ON() round(sum(increase(grpc_client_handled_total{grpc_type="unary",grpc_code!~"OK|NotFound|Canceled",grpc_method="SearchHelp",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
          severity: "p1"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetTransactionAggregates
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_method="GetTransactionAggregates",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetTransactionAggregates",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetTransactionAggregatesClient
        expr: sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_method="GetTransactionAggregates",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_method="GetTransactionAggregates",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetTrending
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_method="GetTrending",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetTrending",grpc_service="search.ActionBar"}[1h]))by (environment,grpc_service,grpc_method) * 100 > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetTrendingClient
        expr: sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_method="GetTrending",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_method="GetTrending",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: PayAutocomplete
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_method="PayAutocomplete",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="PayAutocomplete",grpc_service="search.ActionBar"}[1h]))by (environment,grpc_service,grpc_method) *100 > 5
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: PayAutocompleteClient
        expr: sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled",grpc_method="PayAutocomplete",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_method="PayAutocomplete",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) *100 > 5
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      #    - alert: CompanyNameAutoComplete
      #      expr: sum(rate(grpc_server_handled_total{service="search",grpc_type="unary",grpc_code!~"OK|Canceled|NotFound",grpc_method="CompanyNameAutoComplete",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_server_started_total{service="search",grpc_type="unary",grpc_method="CompanyNameAutoComplete",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1
      #      for: 1m
      #      labels:
      #        severity: "p0"
      #      annotations:
      #        summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
      #        description: "search API {{ $labels.grpc_method }} has been down for more than 1m."
      #
      #    - alert: CompanyNameAutoCompleteClient
      #      expr: sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound",grpc_method="CompanyNameAutoComplete",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method, service)/sum(rate(grpc_client_handled_total{grpc_type="unary",grpc_method="CompanyNameAutoComplete",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method, service) * 100 > 1
      #      for: 1m
      #      labels:
      #        severity: "p0"
      #      annotations:
      #        summary: "search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
      #        description: "search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: NluServerError
        expr: sum(rate(nlu_response{error="nlu-server-error"}[15m])) by (environment) > 1
        for: 1m
        labels:
          severity: "p1"
          team: "wealth"
        annotations:
          summary: "Nlu server is down in {{ $labels.environment }}."
          description: "Nlu server has been down for more than 1m."

      - alert: RelatedQueries
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetRelatedQueries",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetRelatedQueries",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5  and on() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetRelatedQueries",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
          severity: "p1"
          team: "wealth"
        annotations:
          summary: "RelatedQueries API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "RelatedQueries API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: HomeSearchBarWidget
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetHomeSearchBarWidget",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetHomeSearchBarWidget",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5  and on() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetHomeSearchBarWidget",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "GetHomeSearchBarWidget API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "GetHomeSearchBarWidget API {{ $labels.grpc_method }} has been down for more than 1m."


      - alert: GetTxnAggregateFitRuleIds
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetTxnAggregateFitRuleIds",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetTxnAggregateFitRuleIds",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5  and on() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetTxnAggregateFitRuleIds",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "GetTxnAggregateFitRuleIds API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "GetTxnAggregateFitRuleIds API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetTxnAggregateFit
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetTxnAggregateFit",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetTxnAggregateFit",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5  and on() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetTxnAggregateFit",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "GetTxnAggregateFit API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "GetTxnAggregateFit API {{ $labels.grpc_method }} has been down for more than 1m."


      - alert: HomeTrendingQueries
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetHomeTrendingQueries",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetHomeTrendingQueries",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5  and on() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetHomeTrendingQueries",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "HomeTrendingQueries API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "HomeTrendingQueries API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetTrendingWidgets
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetTrendingWidgets",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetTrendingWidgets",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5  and on() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetTrendingWidgets",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
          severity: "p0"
          team: "wealth"
        annotations:
          summary: "GetTrendingWidgets API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "GetTrendingWidgets API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: PreviewPage
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetPreviewPage",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetPreviewPage",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 5 and ON() round(sum(increase(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|Canceled|NotFound|InvalidArgument",grpc_method="GetPreviewPage",grpc_service="search.ActionBar"}[15m]))) > 1
        for: 1m
        labels:
         severity: "p0"
         team: "wealth"
        annotations:
         summary: "PreviewPage API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
         description: "PreviewPage API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: SearchUserIssue
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|NotFound",grpc_method="SearchUserIssue",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="SearchUserIssue",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 10
        for: 15m
        labels:
          severity: "p0"
          team: "growth-experience"
        annotations:
          summary: "Search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "Search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: IndexUserIssue
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK",grpc_method="IndexUserIssue",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="IndexUserIssue",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 1
        for: 15m
        labels:
          severity: "p0"
          team: "growth-experience"
        annotations:
          summary: "Search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "Search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetSherlockSopSearchResults
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|NotFound",grpc_method="GetSherlockSopSearchResults",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetSherlockSopSearchResults",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 1
        for: 15m
        labels:
          severity: "p0"
          team: "growth-experience"
        annotations:
          summary: "Search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "Search API {{ $labels.grpc_method }} has been down for more than 1m."

      - alert: GetSherlockSopSearchResultsMeta
        expr: sum(rate(grpc_server_handled_total{grpc_type="unary",grpc_code!~"OK|NotFound",grpc_method="GetSherlockSopSearchResultsMeta",grpc_service="search.ActionBar"}[15m])) by (environment,grpc_service,grpc_method)/sum(rate(grpc_server_started_total{grpc_type="unary",grpc_method="GetSherlockSopSearchResultsMeta",grpc_service="search.ActionBar"}[15m]))by (environment,grpc_service,grpc_method) * 100 > 1
        for: 15m
        labels:
          severity: "p0"
          team: "growth-experience"
        annotations:
          summary: "Search API {{ $labels.grpc_method }} is down in {{ $labels.environment }}."
          description: "Search API {{ $labels.grpc_method }} has been down for more than 1m."








