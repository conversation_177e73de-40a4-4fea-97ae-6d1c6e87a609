
groups:
  - name: latency
    rules:
      - alert: "[Redis Cloud] Average Latency Warning"
        expr: round(bdb_avg_latency{bdb_name=~"central-growth"} * 1000) > 1
        for: 30s
        labels:
          severity: "p1"
          team: "central-growth"
        annotations:
          summary: "High Latency - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Latency: {{$value}} ms"
          description: "High Latency - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Latency: {{$value}} ms"
          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/latency.md#average-latency
      - alert: "[Redis Cloud] Average Latency Critical"
        expr: round(bdb_avg_latency{bdb_name=~"central-growth"} * 1000) > 4
        for: 30s
        labels:
          severity: "p0"
          team: "central-growth"
        annotations:
          summary: "High Latency - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Latency: {{$value}} ms"
          description: "High Latency - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Latency: {{$value}} ms"
          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/latency.md#average-latency

  - name: connections
    rules:
# TODO(Sundeep): Evaluate the usefulness and re-enable.
#      - alert: No Redis Connections
#        expr: bdb_conns{bdb_name=~"central-growth"} < 1
#        for: 30s
#        labels:
#          severity: "p1"
#          team: "central-growth"
#        annotations:
#          summary: "No Redis Connections"
#          description: "No Connections - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Connections: {{$value}}"
#          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/connections.md#no-redis-connections
      - alert: "[Redis Cloud] Excessive Connections"
        expr: bdb_conns{bdb_name=~"central-growth"} > 64000
        for: 30s
        labels:
          severity: "p0"
          team: "central-growth"
        annotations:
          summary: "Too Many Connections - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Connections: {{$value}}"
          description: "Too Many Connections - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Connections: {{$value}}"
          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/connections.md#excessive-connections

  - name: throughput
    rules:
# TODO(Sundeep): Evaluate the usefulness and re-enable.
#      - alert: No Redis Requests
#        expr: bdb_total_req{bdb_name=~"central-growth"} < 1
#        for: 30s
#        labels:
#          severity: "p1"
#          team: "central-growth"
#        annotations:
#          summary: No Redis Requests
#          description: "Too few Redis operations - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}}  {{$value}} (ops/sec)"
#          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/throughput.md#no-redis-requests
      - alert: "[Redis Cloud] Excessive Redis Requests"
        expr: bdb_total_req{bdb_name=~"central-growth"} > 1000000
        for: 30s
        labels:
          severity: "p0"
          team: "central-growth"
        annotations:
          summary: "Too Many Redis Operations - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} {{$value}} (ops/sec)"
          description: "Too Many Redis Operations - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} {{$value}} (ops/sec)"
          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/throughput.md#excessive-redis-requests

  - name: capacity
    rules:
      - alert: "[Redis Cloud] DB full"
        expr: round((bdb_used_memory{bdb_name=~"central-growth"}/bdb_memory_limit{bdb_name=~"central-growth"}) * 100) > 98
        for: 30s
        labels:
          severity: "p0"
          team: "central-growth"
        annotations:
          summary: "DB Usage - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Usage: {{$value}}% full "
          description: "DB Usage - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Usage: {{$value}}% full "
          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/capacity.md#db-full
      - alert: "[Redis Cloud] DB full in 2 hours"
        expr: round((bdb_used_memory{bdb_name=~"central-growth"}/bdb_memory_limit{bdb_name=~"central-growth"}) * 100) < 98 and (predict_linear(bdb_used_memory{bdb_name=~"central-growth"}[15m], 2 * 3600) / bdb_memory_limit{bdb_name=~"central-growth"}) > 0.3 and round(predict_linear(bdb_used_memory{bdb_name=~"central-growth"}[15m], 2 * 3600)/bdb_memory_limit{bdb_name=~"central-growth"}) > 0.98
        for: 30s
        labels:
          severity: "p1"
          team: "central-growth"
        annotations:
          summary: "DB Usage - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Usage: {{$value}}% in 2 hours "
          description: "DB Usage - Cluster: {{ $labels.cluster }} DB: {{$labels.bdb}} Name: {{$labels.bdb_name}} Usage: {{$value}}% in 2 hours "
          runbook: https://github.com/Redislabs-Solution-Architects/RedisEnterprisePrometheus/blob/master/runbooks/capacity.md#db-full-in-2-hours
