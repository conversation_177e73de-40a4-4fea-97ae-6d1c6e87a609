// Code generated by MockGen. DO NOT EDIT.
// Source: history.go

// Package mock_history is a generated GoMock package.
package mock_history

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	money "google.golang.org/genproto/googleapis/type/money"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockNetworth is a mock of Networth interface.
type MockNetworth struct {
	ctrl     *gomock.Controller
	recorder *MockNetworthMockRecorder
}

// MockNetworthMockRecorder is the mock recorder for MockNetworth.
type MockNetworthMockRecorder struct {
	mock *MockNetworth
}

// NewMockNetworth creates a new mock instance.
func NewMockNetworth(ctrl *gomock.Controller) *MockNetworth {
	mock := &MockNetworth{ctrl: ctrl}
	mock.recorder = &MockNetworthMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNetworth) EXPECT() *MockNetworthMockRecorder {
	return m.recorder
}

// GetAssetHistoryValue mocks base method.
func (m *MockNetworth) GetAssetHistoryValue(ctx context.Context, actorId string, historyDate *timestamppb.Timestamp) (*money.Money, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetHistoryValue", ctx, actorId, historyDate)
	ret0, _ := ret[0].(*money.Money)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetHistoryValue indicates an expected call of GetAssetHistoryValue.
func (mr *MockNetworthMockRecorder) GetAssetHistoryValue(ctx, actorId, historyDate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetHistoryValue", reflect.TypeOf((*MockNetworth)(nil).GetAssetHistoryValue), ctx, actorId, historyDate)
}

// GetNetWorth mocks base method.
func (m *MockNetworth) GetNetWorth(ctx context.Context, actorId string) (*money.Money, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNetWorth", ctx, actorId)
	ret0, _ := ret[0].(*money.Money)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNetWorth indicates an expected call of GetNetWorth.
func (mr *MockNetworthMockRecorder) GetNetWorth(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNetWorth", reflect.TypeOf((*MockNetworth)(nil).GetNetWorth), ctx, actorId)
}

// StoreSnapshot mocks base method.
func (m *MockNetworth) StoreSnapshot(ctx context.Context, actorId string, historyDate *timestamppb.Timestamp) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StoreSnapshot", ctx, actorId, historyDate)
	ret0, _ := ret[0].(error)
	return ret0
}

// StoreSnapshot indicates an expected call of StoreSnapshot.
func (mr *MockNetworthMockRecorder) StoreSnapshot(ctx, actorId, historyDate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreSnapshot", reflect.TypeOf((*MockNetworth)(nil).StoreSnapshot), ctx, actorId, historyDate)
}
