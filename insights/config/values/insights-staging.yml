Application:
  Environment: "staging"
  Name: "insights"
  GmailDataEncrKeyKMSId: "2f116882-3f3d-4e29-8555-76301c0edb5c"
  EmailIdRegex: "[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*"
  MailFetchDateRangeInMonths: 3

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9999
    HttpPProfPort: 9990

InsightsDb:
  AppName: "insights"
  StatementTimeout: 1s
  Name: "insights"
  EnableDebug: false
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "staging/rds/postgres14"

ActorInsightsDb:
  AppName: "insights"
  StatementTimeout: 1s
  Name: "actor_insights"
  EnableDebug: false
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "staging/rds/postgres14"

GmailBatchGetApiParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 15000
  BatchReqUrl: "https://www.googleapis.com/batch/gmail/v1"
  GetMessageApi: "GET /gmail/v1/users/me/messages/"

GmailListApiParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 2000

EmailParserParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1000
  ParseReqUrl: "http://email-parser.staging.pointz.in/v1/parse"

GoogleOAuthParams:
  ClientMaxIdleConns: 10
  MaxIdleConnsPerHost: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1500
  RevokeTokenUrl: "https://oauth2.googleapis.com/revoke"

MailFetchConcurrencyParams:
  MaxGoroutinesPerUser: 5
  MessagePerGoroutine: 10

UserEmailAccessPublisher:
  QueueName: "staging-new-user-email-access-queue"

UserEmailAccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-new-user-email-access-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed for next 200 mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 10

PeriodicEmaiSyncPublisher:
  QueueName: "staging-periodic-mail-sync-queue"
  QueueOwnerAccountId: "************"

PeriodicEmailSyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 6
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-periodic-mail-sync-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"

MailDataParserPublisher:
  QueueName: "staging-mail-data-parser-queue"

MailDataParserSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-mail-data-parser-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed for next 200 mins
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 20
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 10

GmailUserSpendsPublisher:
  TopicName: "staging-gmail-user-spends-topic"

EpfPassbookImportEventPublisher:
  TopicName: "staging-epf-passbook-import-topic"

CreateOrUpdateGenerationStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-insight-generation-script-run-status"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "insights"

StoreGeneratedActorInsightsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-generated-actor-insights"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 1m
    Namespace: "insights"

OnboardingStageUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-insights-onb-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "insights"

GenerateInsightsSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "dev-generate-insights"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "insights"

ProcessCaNewDataFetchEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "staging-insights-ca-data-new-data-fetch-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "insights"

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    GoogleOAuthCredentials: "staging/insights-web/gmail-oauth-cred"
    MailDataEncryptionKey: "staging/insights/mail-data-encr"
    EmailParserInsightsDbUsernamePassword: "staging/rds/postgres14"
    ActorInsightsDbUsernamePassword: "staging/rds/postgres14"

Flags:
  TrimDebugMessageFromStatus: false

AddGmailAccountBannerParams:
  ShowAddAccountBanner: true
  AddAccountRedirectUrl: "https://web.staging.pointz.in/insights"
  AddAccountWebViewTitle: "Insights"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

RealTimeInsightParams:
  SegmentCoolDownPeriod: "240h" # 10 days
  RealTimeInsightProb: 100 # prob on scale of [1, 100] that real time insight flow is chosen to generate insight

EpfPassbookDataFlatteningSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-epf-passbook-data-flattening-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

UANAccountsCacheConfig:
  CacheTTL: "10m"
  IsCachingEnabled: false

NetworthParams:
  UseSchemeAnalyticsApiForMfAggVal: true
  DebugActorIdsForDailyReport:
    - "AC3N7kkBRMEm250423": true # Sainath's actorId for debugging purposes

DbConfigMap:
  EPIFI_TECH:
    DbType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "staging/cockroach/ca.crt"
    SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "insights"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FEDERAL_BANK:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 5s
    Name: "insights_federal"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/epifimetis/insights_federal_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_WEALTH:
    DbType: "PGDB"
    AppName: "insights"
    StatementTimeout: 5s
    Name: "epifi_wealth_analytics"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    SecretName: "staging/rds/postgres14"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

GeminiConf:
  GoogleCloudCredentialsSecretPath: "staging/magic-import/gemini-secret"
