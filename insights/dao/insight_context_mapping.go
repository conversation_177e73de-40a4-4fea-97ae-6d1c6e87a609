package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/frontend/deeplink"
	modelpb "github.com/epifi/gamma/api/insights/model"
	"github.com/epifi/gamma/insights/dao/model"
)

var InsightContextMappingColumnName = map[modelpb.InsightContextMappingFieldMask]string{
	modelpb.InsightContextMappingFieldMask_INSIGHT_CONTEXT_MAPPING_ID:           "id",
	modelpb.InsightContextMappingFieldMask_INSIGHT_CONTEXT_MAPPING_FRAMEWORK_ID: "framework_id",
	modelpb.InsightContextMappingFieldMask_INSIGHT_CONTEXT_MAPPING_SCREEN_NAME:  "screen_name",
	modelpb.InsightContextMappingFieldMask_INSIGHT_CONTEXT_MAPPING_CREATED_AT:   "created_at",
	modelpb.InsightContextMappingFieldMask_INSIGHT_CONTEXT_MAPPING_UPDATED_AT:   "updated_at",
	modelpb.InsightContextMappingFieldMask_INSIGHT_CONTEXT_MAPPING_DELETED_AT:   "deleted_at",
}

var (
	InsightContextMappingEmptyError = fmt.Errorf("InsightContextMapping entry is empty")
)

type InsightContextMappingDaoPgdb struct {
	db *gorm.DB
}

func NewInsightContextMappingDaoPgdb(db cmdTypes.ActorInsightsPGDB) *InsightContextMappingDaoPgdb {
	return &InsightContextMappingDaoPgdb{
		db: db,
	}
}

var _ InsightContextMappingDao = &InsightContextMappingDaoPgdb{}

func (s *InsightContextMappingDaoPgdb) Create(ctx context.Context,
	mapping *modelpb.InsightContextMapping) (*modelpb.InsightContextMapping, error) {
	defer metric_util.TrackDuration("insights/dao", "InsightContextMappingDaoPgdb", "Create", time.Now())
	if mapping == nil {
		return nil, InsightContextMappingEmptyError
	}
	if mapping.GetFrameworkId() == "" {
		return nil, FrameworkIdUnspecifiedErr
	}
	if mapping.GetScreenName() == deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED {
		return nil, ScreenNameUnspecifiedErr
	}
	m := model.NewInsightContextMapping(mapping)
	// If ctx is carrying the gorm transaction connection object, use it.
	// if not, use the gorm.DB connection provided by the dao.
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	res := db.Create(m)
	if res.Error != nil {
		return nil, fmt.Errorf("error creating insightScreenNapping data entry: %w", res.Error)
	}
	return m.ToProto(), nil
}

// GetByFrameworkId returns a random content template for a given frameworkId
// DB Indexes to optimise the query: insight_context_mappings_framework_id
func (s *InsightContextMappingDaoPgdb) GetByFrameworkId(ctx context.Context, frameworkId string) ([]*modelpb.InsightContextMapping, error) {
	defer metric_util.TrackDuration("insights/dao", "InsightContextMappingDaoPgdb", "GetByFrameworkId", time.Now())
	if frameworkId == "" {
		return nil, FrameworkIdUnspecifiedErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var insightContextMappingData []*model.InsightContextMapping
	if err := db.Where(&model.InsightContextMapping{
		FrameworkId: frameworkId,
	}).Find(&insightContextMappingData).Error; err != nil {
		return nil, fmt.Errorf("error fetching InsightContextMapping data for frameworkId %s: %w", frameworkId, err)
	}
	var insightContextMappingDataInProto []*modelpb.InsightContextMapping
	for _, data := range insightContextMappingData {
		insightContextMappingDataInProto = append(insightContextMappingDataInProto, data.ToProto())
	}
	return insightContextMappingDataInProto, nil
}

func (s *InsightContextMappingDaoPgdb) GetByScreen(ctx context.Context, screen deeplink.Screen) ([]*modelpb.InsightContextMapping, error) {
	defer metric_util.TrackDuration("insights/dao", "InsightContextMappingDaoPgdb", "GetByScreen", time.Now())
	if screen == deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED {
		return nil, ScreenNameUnspecifiedErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var insightContextMappingData []*model.InsightContextMapping
	if err := db.Where(&model.InsightContextMapping{
		ScreenName: screen,
	}).Find(&insightContextMappingData).Error; err != nil {
		return nil, fmt.Errorf("error fetching InsightContextMapping data for screen %s: %w", screen.String(), err)
	}
	var insightContextMappingDataInProto []*modelpb.InsightContextMapping
	for _, data := range insightContextMappingData {
		insightContextMappingDataInProto = append(insightContextMappingDataInProto, data.ToProto())
	}
	return insightContextMappingDataInProto, nil
}

// nolint:dupl
func (s *InsightContextMappingDaoPgdb) Update(ctx context.Context, insightContextMappingData *modelpb.InsightContextMapping,
	updateMask []modelpb.InsightContextMappingFieldMask) error {
	defer metric_util.TrackDuration("insights/dao", "InsightContextMappingDaoPgdb", "Update", time.Now())
	if insightContextMappingData == nil {
		return InsightContextMappingEmptyError
	}
	if insightContextMappingData.GetId() == "" {
		return IdUnspecifiedError
	}
	if len(updateMask) == 0 {
		return UpdateMaskEmptyErr
	}
	updateColumns := getInsightContextMappingColumnsForUpdate(updateMask)
	m := model.NewInsightContextMapping(insightContextMappingData)
	whereClause := &model.InsightContextMapping{Id: insightContextMappingData.GetId()}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	return db.Model(m).Where(whereClause).Select(updateColumns).Updates(m).Error
}

// getInsightContextMappingColumnsForUpdate returns a string slice of column names present in updateMask
// using InsightContextMappingColumnName map
func getInsightContextMappingColumnsForUpdate(updateMask []modelpb.InsightContextMappingFieldMask) []string {
	var requiredColumns []string
	for _, field := range updateMask {
		requiredColumns = append(requiredColumns, InsightContextMappingColumnName[field])
	}
	return requiredColumns
}
