package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/thoas/go-funk"
	"gorm.io/gorm"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	modelPb "github.com/epifi/gamma/api/insights/model"
	"github.com/epifi/gamma/insights/dao/model"
)

type ContentTemplateDaoPgdb struct {
	db *gorm.DB
}

func NewContentTemplateDaoPgdb(db cmdTypes.ActorInsightsPGDB) *ContentTemplateDaoPgdb {
	return &ContentTemplateDaoPgdb{
		db: db,
	}
}

var _ ContentTemplateDao = &ContentTemplateDaoPgdb{}

var ContentTemplateColNameMap = map[modelPb.ContentTemplateFieldMask]string{
	modelPb.ContentTemplateFieldMask_CONTENT_TEMPLATE_FIELD_MASK_ID:              "id",
	modelPb.ContentTemplateFieldMask_CONTENT_TEMPLATE_FIELD_MASK_FRAMEWORK_ID:    "framework_id",
	modelPb.ContentTemplateFieldMask_CONTENT_TEMPLATE_FIELD_MASK_PLATFORM:        "platform",
	modelPb.ContentTemplateFieldMask_CONTENT_TEMPLATE_FIELD_MASK_CONTENT_DETAILS: "content_details",
	modelPb.ContentTemplateFieldMask_CONTENT_TEMPLATE_FIELD_MASK_CREATED_AT:      "created_at",
	modelPb.ContentTemplateFieldMask_CONTENT_TEMPLATE_FIELD_MASK_UPDATED_AT:      "updated_at",
	modelPb.ContentTemplateFieldMask_CONTENT_TEMPLATE_FIELD_MASK_DELETED_AT:      "deleted_at",
}

var (
	FrameworkIdUnspecifiedErr = fmt.Errorf("framework id not specified")
	ContentTemplateEmptyErr   = fmt.Errorf("content template can't be empty")
)

func (s *ContentTemplateDaoPgdb) Create(ctx context.Context, template *modelPb.ContentTemplate) (*modelPb.ContentTemplate, error) {
	defer metric_util.TrackDuration("insights/dao", "ContentTemplateDaoPgdb", "Create", time.Now())
	if template == nil {
		return nil, ContentTemplateEmptyErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	modelTemplate := model.NewContentTemplate(template)
	if err := db.Create(modelTemplate).Error; err != nil {
		return nil, fmt.Errorf("unable create DB entry for content template : %w", err)
	}
	return modelTemplate.ToProto(), nil
}

func (s *ContentTemplateDaoPgdb) GetById(ctx context.Context, id string) (*modelPb.ContentTemplate, error) {
	defer metric_util.TrackDuration("insights/dao", "ContentTemplateDaoPgdb", "GetById", time.Now())
	if id == "" {
		return nil, fmt.Errorf("error feching content template : %w", IdUnspecifiedError)
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var modelTemplate model.ContentTemplate
	if err := db.Where(&model.ContentTemplate{
		Id: id,
	}).First(&modelTemplate).Error; err != nil {
		return nil, fmt.Errorf("error feching content template : %w", err)
	}
	return modelTemplate.ToProto(), nil
}

//nolint:dupl
func (s *ContentTemplateDaoPgdb) GetByFrameworkId(ctx context.Context, frameworkId string) ([]*modelPb.ContentTemplate, error) {
	defer metric_util.TrackDuration("insights/dao", "ContentTemplateDaoPgdb", "GetByFrameworkId", time.Now())
	if frameworkId == "" {
		return nil, FrameworkIdUnspecifiedErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var contentTemplatesData []*model.ContentTemplate
	if err := db.Where(
		&model.ContentTemplate{
			FrameworkId: frameworkId,
		},
	).Find(&contentTemplatesData).Error; err != nil {
		return nil, fmt.Errorf("error fetching contentTemplates for frameworkId %s: %w", frameworkId, err)
	}
	var contentTemplatesInProto []*modelPb.ContentTemplate
	for _, data := range contentTemplatesData {
		contentTemplatesInProto = append(contentTemplatesInProto, data.ToProto())
	}
	return contentTemplatesInProto, nil
}

// GetRandomTemplateForFramework returns a random content template for a given frameworkId
// DB Indexes to optimise the query: insight_context_mappings_framework_id
func (s *ContentTemplateDaoPgdb) GetRandomTemplateForFramework(ctx context.Context, frameworkId string) (*modelPb.ContentTemplate, error) {
	defer metric_util.TrackDuration("insights/dao", "ContentTemplateDaoPgdb", "GetRandomTemplateForFramework", time.Now())
	if frameworkId == "" {
		return nil, fmt.Errorf("error feching content template : %w", FrameworkIdUnspecifiedErr)
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var modelTemplate model.ContentTemplate

	if err := db.Where(&model.ContentTemplate{FrameworkId: frameworkId}).Order("random()").First(&modelTemplate).Error; err != nil {
		return nil, fmt.Errorf("error feching content template : %w", err)
	}
	return modelTemplate.ToProto(), nil
}

// nolint:dupl
func (s *ContentTemplateDaoPgdb) Update(ctx context.Context, template *modelPb.ContentTemplate, updateMask []modelPb.ContentTemplateFieldMask) error {
	defer metric_util.TrackDuration("insights/dao", "ContentTemplateDaoPgdb", "Update", time.Now())
	if template.GetId() == "" {
		return fmt.Errorf("error updating content template : %w", IdUnspecifiedError)
	}
	if len(updateMask) == 0 {
		return UpdateMaskEmptyErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	updateCols := getSelectColumnsForContentTemplate(updateMask)
	modelTemplate := model.NewContentTemplate(template)
	if err := db.Model(modelTemplate).Select(updateCols).Updates(modelTemplate).Error; err != nil {
		return fmt.Errorf("unable to update content template: %s : %w", template.Id, err)
	}
	return nil
}

// GetByFrameworkAndSegmentId - returns all templates that have the given framework id and segment id as given or NULL
// for a given (framework, segment) pair we find all templates which belong to (framework, ”) or (framework, segment)
// nolint:dupl
func (s *ContentTemplateDaoPgdb) GetByFrameworkAndSegmentId(ctx context.Context, frameworkId string, segmentId string) ([]*modelPb.ContentTemplate, error) {
	defer metric_util.TrackDuration("insights/dao", "ContentTemplateDaoPgdb", "GetByFrameworkAndSegmentId", time.Now())
	if frameworkId == "" {
		return nil, FrameworkIdUnspecifiedErr
	}
	if segmentId == "" {
		return nil, SegmentIdUnspecifiedErr
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var contentTemplatesData []*model.ContentTemplate
	if err := db.Where("framework_id = ? AND (segment_id = ? OR segment_id IS NULL)", frameworkId, segmentId).Find(&contentTemplatesData).Error; err != nil {
		return nil, fmt.Errorf("error fetching contentTemplates for frameworkId %s segmentId %s : %w", frameworkId, segmentId, err)
	}
	var contentTemplatesInProto []*modelPb.ContentTemplate
	for _, data := range contentTemplatesData {
		contentTemplatesInProto = append(contentTemplatesInProto, data.ToProto())
	}
	return contentTemplatesInProto, nil
}

// GetRandomTemplateForFrameworkAndSegment returns a random content template for a given frameworkId and segmentId
// for a given (framework, segment) pair we find all templates which belong to (framework, ”) or (framework, segment)
// DB Indexes to optimise the query: insight_context_mappings_framework_id
// nolint:dupl
func (s *ContentTemplateDaoPgdb) GetRandomTemplateForFrameworkAndSegment(ctx context.Context, frameworkId string, segmentId string) (*modelPb.ContentTemplate, error) {
	defer metric_util.TrackDuration("insights/dao", "ContentTemplateDaoPgdb", "GetRandomTemplateForFrameworkAndSegment", time.Now())
	if frameworkId == "" {
		return nil, fmt.Errorf("error feching content template : %w", FrameworkIdUnspecifiedErr)
	}
	if segmentId == "" {
		return nil, fmt.Errorf("error feching content template : %w", SegmentIdUnspecifiedErr)
	}
	db := gormctxv2.FromContextOrDefault(ctx, s.db)
	var modelTemplate model.ContentTemplate

	if err := db.Where("framework_id = ? AND (segment_id = ? OR segment_id IS NULL)", frameworkId, segmentId).Order("random()").First(&modelTemplate).Error; err != nil {
		return nil, fmt.Errorf("error feching content template : %w", err)
	}
	return modelTemplate.ToProto(), nil
}

// getSelectColumnsForContentTemplate converts field mask to string slice with column name corresponding to field name enums
func getSelectColumnsForContentTemplate(fieldMasks []modelPb.ContentTemplateFieldMask) []string {
	var selectColumns []string

	for _, field := range fieldMasks {
		selectColumns = append(selectColumns, ContentTemplateColNameMap[field])
	}
	return funk.UniqString(selectColumns)
}
