package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/datetime"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	pb "github.com/epifi/gamma/api/insights/consumer"
	modelPb "github.com/epifi/gamma/api/insights/model"
	mockDao "github.com/epifi/gamma/insights/dao/mocks"
)

var (
	group10 = "group-10"
	group11 = "group-11"
	req1    = &pb.StoreGeneratedActorInsightsRequest{
		ConsumerRequestHeader: &queue.ConsumerRequestHeader{
			IsLastAttempt: false,
		},
		RunId:       "f9a8648a-6cfc-4cba-8d80-848910d847fc",
		FrameworkId: "f9a8649a-6cfc-4cba-8d80-848910d837fc",
		ActorInsightData: []*pb.ActorInsightData{
			{
				TargetEntityId: group10,
				RelevanceScore: 100,
				Values: []*modelPb.InsightVariableValuePair{
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						VariableValue: "1200",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						VariableValue: "Flipkart",
					},
				},
			},
			{
				TargetEntityId: group11,
				RelevanceScore: 100,
				Values: []*modelPb.InsightVariableValuePair{
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						VariableValue: "150",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						VariableValue: "Myntra",
					},
				},
			},
		},
		ValidFrom: timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
		ValidTill: timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
	}

	req2 = &pb.StoreGeneratedActorInsightsRequest{
		ConsumerRequestHeader: &queue.ConsumerRequestHeader{
			IsLastAttempt: false,
		},
		RunId:       "344c5674-93d3-11ec-b909-0242ac120002",
		FrameworkId: "88c847e6-93d1-11ec-b909-0242ac120002",
		ActorInsightData: []*pb.ActorInsightData{
			{
				TargetEntityId: group10,
				RelevanceScore: 100,
				Values: []*modelPb.InsightVariableValuePair{
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_PERCENT,
						VariableValue: "50",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						VariableValue: "1200",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						VariableValue: "Swiggy",
					},
				},
			},
			{
				TargetEntityId: group11,
				RelevanceScore: 100,
				Values: []*modelPb.InsightVariableValuePair{
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						VariableValue: "Zomato",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						VariableValue: "1500",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_PERCENT,
						VariableValue: "80",
					},
				},
			},
		},
		ValidFrom: timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
		ValidTill: timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
	}
	req3 = &pb.StoreGeneratedActorInsightsRequest{
		ConsumerRequestHeader: &queue.ConsumerRequestHeader{
			IsLastAttempt: false,
		},
		RunId:       "f9a8648a-6cfc-4cba-8d80-848910d847fc",
		FrameworkId: "f9a8649a-6cfc-4cba-8d80-848910d837fc",
		ActorInsightData: []*pb.ActorInsightData{
			{
				TargetEntityId: group10,
				RelevanceScore: 100,
				Values: []*modelPb.InsightVariableValuePair{
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						VariableValue: "Flipkart",
					},
				},
			},
		},
		ValidFrom: timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
		ValidTill: timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
	}

	reqWithoutValidity = &pb.StoreGeneratedActorInsightsRequest{
		ConsumerRequestHeader: &queue.ConsumerRequestHeader{
			IsLastAttempt: false,
		},
		RunId:       "f9a8648a-6cfc-4cba-8d80-848910d847fc",
		FrameworkId: "f9a8649a-6cfc-4cba-8d80-848910d837fc",
		ActorInsightData: []*pb.ActorInsightData{
			{
				TargetEntityId: group10,
				RelevanceScore: 100,
				Values: []*modelPb.InsightVariableValuePair{
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						VariableValue: "1200",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						VariableValue: "Flipkart",
					},
				},
			},
			{
				TargetEntityId: group11,
				RelevanceScore: 100,
				Values: []*modelPb.InsightVariableValuePair{
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						VariableValue: "150",
					},
					{
						Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						VariableValue: "Flipkart",
					},
				},
			},
		},
		ValidFrom: nil,
		ValidTill: nil,
	}
	frameworkFixtureId1 = "f9a8649a-6cfc-4cba-8d80-848910d837fc"
	frameworkFixtureId3 = "88c847e6-93d1-11ec-b909-0242ac120002"

	segmentFixture1 = &modelPb.InsightSegment{
		Id:           "f9a8649a-6cfc-4cba-8d80-848910d847fc",
		FrameworkId:  frameworkFixtureId1,
		ReleaseGroup: commontypes.UserGroup_FNF,
		SegmentDetails: []*modelPb.InsightVariableValuePair{
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				VariableValue: "Flipkart",
			},
		},
		DefaultValues: []*modelPb.InsightVariableValuePair{
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
				VariableValue: "1200",
			},
		},
	}

	segmentFixture2 = &modelPb.InsightSegment{
		Id:           "f9a8649a-6cfc-4cba-8d80-848910d847fd",
		FrameworkId:  frameworkFixtureId1,
		ReleaseGroup: commontypes.UserGroup_FNF,
		SegmentDetails: []*modelPb.InsightVariableValuePair{
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				VariableValue: "Zomato",
			},
		},
	}

	segmentFixture3 = &modelPb.InsightSegment{
		Id:           "f9a8649a-6cfc-4cba-8d80-848910d847fe",
		FrameworkId:  frameworkFixtureId1,
		ReleaseGroup: commontypes.UserGroup_INTERNAL,
		SegmentDetails: []*modelPb.InsightVariableValuePair{
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				VariableValue: "Myntra",
			},
		},
	}

	segmentFixture4 = &modelPb.InsightSegment{
		Id:           "f9a8649a-6cfc-4cba-8d80-848910d847f2",
		FrameworkId:  frameworkFixtureId1,
		ReleaseGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
		SegmentDetails: []*modelPb.InsightVariableValuePair{
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				VariableValue: "Bigbasket",
			},
		},
	}
	segmentFixture5 = &modelPb.InsightSegment{
		Id:           "1135fc5e-93d2-11ec-b909-0242ac120002",
		FrameworkId:  frameworkFixtureId3,
		ReleaseGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
		SegmentDetails: []*modelPb.InsightVariableValuePair{
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				VariableValue: "Swiggy",
			},
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_PERCENT,
				VariableValue: "50",
			},
		},
	}
	segmentFixture6 = &modelPb.InsightSegment{
		Id:           "15835270-93d2-11ec-b909-0242ac120002",
		FrameworkId:  frameworkFixtureId3,
		ReleaseGroup: commontypes.UserGroup_USER_GROUP_UNSPECIFIED,
		SegmentDetails: []*modelPb.InsightVariableValuePair{
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_PERCENT,
				VariableValue: "80",
			},
			{
				Variable:      modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
				VariableValue: "Zomato",
			},
		},
	}
)

func TestService_StoreGeneratedActorInsights_Validations(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, actorInsightDB, conf.ActorInsightsDb.GetName(), insightsTables)

	ctrl := gomock.NewController(t)
	defer func() {
		ctrl.Finish()
	}()

	gormTxnExecutor := storagev2.NewGormTxnExecutor(actorInsightDB)
	mockFrameworkDao := mockDao.NewMockInsightFrameworkDao(ctrl)
	mockSegmentDao := mockDao.NewMockInsightSegmentDao(ctrl)
	mockGenerationScriptRunDao := mockDao.NewMockGenerationScriptRunDao(ctrl)
	mockActorInsightDao := mockDao.NewMockActorInsightDao(ctrl)

	s := NewService(gormTxnExecutor, mockFrameworkDao, mockSegmentDao, mockGenerationScriptRunDao, mockActorInsightDao, nil, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx context.Context
		req *pb.StoreGeneratedActorInsightsRequest
	}

	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *pb.StoreGeneratedActorInsightsResponse
	}{
		{
			name: "RunId is empty",
			args: args{
				ctx: context.Background(),
				req: &pb.StoreGeneratedActorInsightsRequest{
					RunId:       "",
					FrameworkId: "f9a8649a-6cfc-4cba-8d80-848910d837fd",
				},
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
		},
		{
			name: "FrameworkId is empty",
			args: args{
				ctx: context.Background(),
				req: &pb.StoreGeneratedActorInsightsRequest{
					RunId:       "f9a8648a-6cfc-4cba-8d80-848910d847ab",
					FrameworkId: "",
				},
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
		},
		{
			name: "ActorInsightData is empty",
			args: args{
				ctx: context.Background(),
				req: &pb.StoreGeneratedActorInsightsRequest{
					RunId:            "f9a8648a-6cfc-4cba-8d80-848910d847ab",
					FrameworkId:      "f9a8648a-6cfc-4cba-8d80-848910d847ab",
					ActorInsightData: []*pb.ActorInsightData{},
				},
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := s.StoreGeneratedActorInsights(tt.args.ctx, tt.args.req)
			if err != nil {
				t.Errorf("StoreGeneratedActorInsights() error = %v", err)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StoreGeneratedActorInsights() gotStoreGeneratedActorInsightsResponse = %v, wantStoreGeneratedActorInsightsResponse %v", got, tt.want)
			}
		})
	}
}

func TestService_StoreGeneratedActorInsights(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, actorInsightDB, conf.ActorInsightsDb.GetName(), insightsTables)

	ctrl := gomock.NewController(t)
	defer func() {
		ctrl.Finish()
	}()

	gormTxnExecutor := storagev2.NewGormTxnExecutor(actorInsightDB)
	mockFrameworkDao := mockDao.NewMockInsightFrameworkDao(ctrl)
	mockSegmentDao := mockDao.NewMockInsightSegmentDao(ctrl)
	mockGenerationScriptRunDao := mockDao.NewMockGenerationScriptRunDao(ctrl)
	mockActorInsightDao := mockDao.NewMockActorInsightDao(ctrl)

	s := NewService(gormTxnExecutor, mockFrameworkDao, mockSegmentDao, mockGenerationScriptRunDao, mockActorInsightDao, nil, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx context.Context
		req *pb.StoreGeneratedActorInsightsRequest
	}

	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *pb.StoreGeneratedActorInsightsResponse
	}{
		{
			name: "Corresponding run_id not present in generationScriptRun table (foreign key dependency not met)",
			args: args{
				ctx: context.Background(),
				req: &pb.StoreGeneratedActorInsightsRequest{
					ConsumerRequestHeader: &queue.ConsumerRequestHeader{
						IsLastAttempt: false,
					},
					RunId:            "6da2fd32-7ff7-11ec-a8a3-0242ac120002",
					FrameworkId:      "78dcc796-7ff7-11ec-a8a3-0242ac120002",
					ActorInsightData: req1.GetActorInsightData(),
				},
			},
			setupMockCalls: func() {
				mockGenerationScriptRunDao.EXPECT().GetById(gomock.Any(), "6da2fd32-7ff7-11ec-a8a3-0242ac120002").Return(&modelPb.GenerationScriptRun{}, gorm.ErrRecordNotFound)
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
		},
		{
			name: "Corresponding run_id not present in generationScriptRun table (foreign key dependency not met) - Last attempt",
			args: args{
				ctx: context.Background(),
				req: &pb.StoreGeneratedActorInsightsRequest{
					ConsumerRequestHeader: &queue.ConsumerRequestHeader{
						IsLastAttempt: true,
					},
					RunId:            "6da2fd32-7ff7-11ec-a8a3-0242ac120002",
					FrameworkId:      "78dcc796-7ff7-11ec-a8a3-0242ac120002",
					ActorInsightData: req1.GetActorInsightData(),
				},
			},
			setupMockCalls: func() {
				mockGenerationScriptRunDao.EXPECT().GetById(gomock.Any(), "6da2fd32-7ff7-11ec-a8a3-0242ac120002").Return(&modelPb.GenerationScriptRun{}, gorm.ErrRecordNotFound)
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
		},
		{
			name: "Corresponding framework_id not present in framework table (foreign key dependency not met)",
			args: args{
				ctx: context.Background(),
				req: &pb.StoreGeneratedActorInsightsRequest{
					ConsumerRequestHeader: &queue.ConsumerRequestHeader{
						IsLastAttempt: false,
					},
					RunId:            "f9a8648a-6cfc-4cba-8d80-848910d847ab",
					FrameworkId:      "78dcc796-7ff7-11ec-a8a3-0242ac120002",
					ActorInsightData: req1.GetActorInsightData(),
				},
			},
			setupMockCalls: func() {
				mockGenerationScriptRunDao.EXPECT().GetById(gomock.Any(), "f9a8648a-6cfc-4cba-8d80-848910d847ab").Return(&modelPb.GenerationScriptRun{}, nil)
				mockFrameworkDao.EXPECT().GetById(gomock.Any(), "78dcc796-7ff7-11ec-a8a3-0242ac120002").Return(&modelPb.InsightFramework{}, gorm.ErrRecordNotFound)
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
		},
		{
			name: "Successfully inserted actor insights data",
			args: args{
				ctx: context.Background(),
				req: req1,
			},
			setupMockCalls: func() {
				mockGenerationScriptRunDao.EXPECT().GetById(gomock.Any(), "f9a8648a-6cfc-4cba-8d80-848910d847fc").Return(&modelPb.GenerationScriptRun{}, nil)
				mockFrameworkDao.EXPECT().GetById(gomock.Any(), "f9a8649a-6cfc-4cba-8d80-848910d837fc").Return(&modelPb.InsightFramework{
					Id:            "f9a8649a-6cfc-4cba-8d80-848910d837fc",
					FrameworkName: "INS_MERCH01",
					FrameworkVariables: []modelPb.InsightVariable{
						modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
					},
					SegmentVariables: []modelPb.InsightVariable{
						modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
					},
					ContentType:      modelPb.ContentType_CONTENT_TYPE_PERSONAL,
					TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
					InsightState:     modelPb.InsightState_INSIGHT_STATE_ACTIVE,
					Deeplink:         "{}",
					ExpiryConfig:     &modelPb.ExpiryConfig{},
				}, nil)
				mockSegmentDao.EXPECT().GetSegmentsInFramaworkIds(gomock.Any(), []string{"f9a8649a-6cfc-4cba-8d80-848910d837fc"}).Return([]*modelPb.InsightSegment{
					segmentFixture1,
					segmentFixture2,
					segmentFixture3,
					segmentFixture4,
				}, nil)
				mockActorInsightDao.EXPECT().SoftDeleteBatch(gomock.Any(), []*modelPb.ActorInsight{
					{
						FrameworkId:      req1.GetFrameworkId(),
						SegmentId:        "f9a8649a-6cfc-4cba-8d80-848910d847fc",
						RunId:            req1.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
						TargetEntityId:   req1.ActorInsightData[0].TargetEntityId,
						Values:           req1.ActorInsightData[0].Values,
						RelevanceScore:   req1.ActorInsightData[0].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
					{
						FrameworkId:      req1.GetFrameworkId(),
						SegmentId:        "f9a8649a-6cfc-4cba-8d80-848910d847fe",
						RunId:            req1.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
						TargetEntityId:   req1.ActorInsightData[1].TargetEntityId,
						Values:           req1.ActorInsightData[1].Values,
						RelevanceScore:   req1.ActorInsightData[1].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
				}).Return(nil)
				mockActorInsightDao.EXPECT().InsertBatch(gomock.Any(), []*modelPb.ActorInsight{
					{
						FrameworkId:      req1.GetFrameworkId(),
						SegmentId:        "f9a8649a-6cfc-4cba-8d80-848910d847fc",
						RunId:            req1.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
						TargetEntityId:   req1.ActorInsightData[0].TargetEntityId,
						Values:           req1.ActorInsightData[0].Values,
						RelevanceScore:   req1.ActorInsightData[0].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
					{
						FrameworkId:      req1.GetFrameworkId(),
						SegmentId:        "f9a8649a-6cfc-4cba-8d80-848910d847fe",
						RunId:            req1.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
						TargetEntityId:   req1.ActorInsightData[1].TargetEntityId,
						Values:           req1.ActorInsightData[1].Values,
						RelevanceScore:   req1.ActorInsightData[1].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
				}).Return(nil)
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
		},
		{
			name: "Successfully inserted actor insights data - (default value from segment used)",
			args: args{
				ctx: context.Background(),
				req: req3,
			},
			setupMockCalls: func() {
				mockGenerationScriptRunDao.EXPECT().GetById(gomock.Any(), "f9a8648a-6cfc-4cba-8d80-848910d847fc").Return(&modelPb.GenerationScriptRun{}, nil)
				mockFrameworkDao.EXPECT().GetById(gomock.Any(), "f9a8649a-6cfc-4cba-8d80-848910d837fc").Return(&modelPb.InsightFramework{
					Id:            "f9a8649a-6cfc-4cba-8d80-848910d837fc",
					FrameworkName: "INS_MERCH01",
					FrameworkVariables: []modelPb.InsightVariable{
						modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
					},
					SegmentVariables: []modelPb.InsightVariable{
						modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
					},
					ContentType:      modelPb.ContentType_CONTENT_TYPE_PERSONAL,
					TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
					InsightState:     modelPb.InsightState_INSIGHT_STATE_ACTIVE,
					Deeplink:         "{}",
					ExpiryConfig:     &modelPb.ExpiryConfig{},
				}, nil)
				mockSegmentDao.EXPECT().GetSegmentsInFramaworkIds(gomock.Any(), []string{"f9a8649a-6cfc-4cba-8d80-848910d837fc"}).Return([]*modelPb.InsightSegment{
					segmentFixture1,
					segmentFixture2,
					segmentFixture3,
					segmentFixture4,
				}, nil)
				mockActorInsightDao.EXPECT().SoftDeleteBatch(gomock.Any(), []*modelPb.ActorInsight{
					{
						FrameworkId:      req3.GetFrameworkId(),
						SegmentId:        "f9a8649a-6cfc-4cba-8d80-848910d847fc",
						RunId:            req3.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
						TargetEntityId:   req3.ActorInsightData[0].TargetEntityId,
						Values:           req3.ActorInsightData[0].Values,
						RelevanceScore:   req3.ActorInsightData[0].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
				}).Return(nil)
				mockActorInsightDao.EXPECT().InsertBatch(gomock.Any(), []*modelPb.ActorInsight{
					{
						FrameworkId:      req3.GetFrameworkId(),
						SegmentId:        "f9a8649a-6cfc-4cba-8d80-848910d847fc",
						RunId:            req3.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR,
						TargetEntityId:   req3.ActorInsightData[0].TargetEntityId,
						Values:           req3.ActorInsightData[0].Values,
						RelevanceScore:   req3.ActorInsightData[0].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
				}).Return(nil)
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
		},
		{
			name: "Successfully inserted actor insights data - (2 segment variables)",
			args: args{
				ctx: context.Background(),
				req: req2,
			},
			setupMockCalls: func() {
				mockGenerationScriptRunDao.EXPECT().GetById(gomock.Any(), req2.GetRunId()).Return(&modelPb.GenerationScriptRun{}, nil)
				mockFrameworkDao.EXPECT().GetById(gomock.Any(), req2.GetFrameworkId()).Return(&modelPb.InsightFramework{
					Id:            req2.GetFrameworkId(),
					FrameworkName: "INS_MERCH03",
					FrameworkVariables: []modelPb.InsightVariable{
						modelPb.InsightVariable_INSIGHT_VARIABLE_AMOUNT,
						modelPb.InsightVariable_INSIGHT_VARIABLE_PERCENT,
						modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
					},
					SegmentVariables: []modelPb.InsightVariable{
						modelPb.InsightVariable_INSIGHT_VARIABLE_MERCHANT,
						modelPb.InsightVariable_INSIGHT_VARIABLE_PERCENT,
					},
					ContentType:      modelPb.ContentType_CONTENT_TYPE_GENERIC,
					TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_GROUP,
					InsightState:     modelPb.InsightState_INSIGHT_STATE_ACTIVE,
					Deeplink:         "{}",
					ExpiryConfig:     &modelPb.ExpiryConfig{},
				}, nil)
				mockSegmentDao.EXPECT().GetSegmentsInFramaworkIds(gomock.Any(), []string{req2.GetFrameworkId()}).Return([]*modelPb.InsightSegment{
					segmentFixture5,
					segmentFixture6,
				}, nil)
				mockActorInsightDao.EXPECT().SoftDeleteBatch(gomock.Any(), []*modelPb.ActorInsight{
					{
						FrameworkId:      req2.GetFrameworkId(),
						SegmentId:        segmentFixture5.GetId(),
						RunId:            req2.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_GROUP,
						TargetEntityId:   req2.ActorInsightData[0].TargetEntityId,
						Values:           req2.ActorInsightData[0].Values,
						RelevanceScore:   req2.ActorInsightData[0].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
					{
						FrameworkId:      req2.GetFrameworkId(),
						SegmentId:        segmentFixture6.GetId(),
						RunId:            req2.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_GROUP,
						TargetEntityId:   req2.ActorInsightData[1].TargetEntityId,
						Values:           req2.ActorInsightData[1].Values,
						RelevanceScore:   req2.ActorInsightData[1].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
				}).Return(nil)
				mockActorInsightDao.EXPECT().InsertBatch(gomock.Any(), []*modelPb.ActorInsight{
					{
						FrameworkId:      req2.GetFrameworkId(),
						SegmentId:        segmentFixture5.GetId(),
						RunId:            req2.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_GROUP,
						TargetEntityId:   req2.ActorInsightData[0].TargetEntityId,
						Values:           req2.ActorInsightData[0].Values,
						RelevanceScore:   req2.ActorInsightData[0].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
					{
						FrameworkId:      req2.GetFrameworkId(),
						SegmentId:        segmentFixture6.Id,
						RunId:            req2.GetRunId(),
						TargetEntityType: modelPb.TargetEntityType_TARGET_ENTITY_TYPE_GROUP,
						TargetEntityId:   req2.ActorInsightData[1].TargetEntityId,
						Values:           req2.ActorInsightData[1].Values,
						RelevanceScore:   req2.ActorInsightData[1].GetRelevanceScore(),
						ValidFrom:        timestamppb.New(time.Date(2022, 04, 01, 12, 0, 0, 0, datetime.IST)),
						ValidTill:        timestamppb.New(time.Date(2023, 04, 01, 12, 0, 0, 0, datetime.IST)),
					},
				}).Return(nil)
			},
			want: &pb.StoreGeneratedActorInsightsResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := s.StoreGeneratedActorInsights(tt.args.ctx, tt.args.req)
			if err != nil {
				t.Errorf("StoreGeneratedActorInsights() error = %v", err)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("StoreGeneratedActorInsights() gotStoreGeneratedActorInsightsResponse = %v, wantStoreGeneratedActorInsightsResponse %v", got, tt.want)
			}
		})
	}
}

func TestService_StoreGeneratedActorInsights_GetValidTill(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, actorInsightDB, conf.ActorInsightsDb.GetName(), insightsTables)

	ctrl := gomock.NewController(t)
	defer func() {
		ctrl.Finish()
	}()

	gormTxnExecutor := storagev2.NewGormTxnExecutor(actorInsightDB)
	mockFrameworkDao := mockDao.NewMockInsightFrameworkDao(ctrl)
	mockSegmentDao := mockDao.NewMockInsightSegmentDao(ctrl)
	mockGenerationScriptRunDao := mockDao.NewMockGenerationScriptRunDao(ctrl)
	mockActorInsightDao := mockDao.NewMockActorInsightDao(ctrl)

	s := NewService(gormTxnExecutor, mockFrameworkDao, mockSegmentDao, mockGenerationScriptRunDao, mockActorInsightDao, nil, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx           context.Context
		dateTime      time.Time
		frameworkResp *modelPb.InsightFramework
		req           *pb.StoreGeneratedActorInsightsRequest
	}

	req2 := req1
	req2.ValidTill = timestamppb.New(time.Date(2021, 2, 1, 0, 0, 0, 0, datetime.IST))

	tests := []struct {
		name string
		args args
		want *timestamppb.Timestamp
	}{
		{
			name: "validTill already present in request",
			args: args{
				ctx:           context.Background(),
				dateTime:      time.Now().In(datetime.IST),
				frameworkResp: &modelPb.InsightFramework{},
				req:           req2,
			},
			want: req2.GetValidTill(),
		},
		{
			name: "validTill using expiry config - Absolute Date Time Params",
			args: args{
				ctx:      context.Background(),
				dateTime: time.Date(2022, 2, 2, 12, 10, 10, 0, datetime.IST),
				frameworkResp: &modelPb.InsightFramework{
					ExpiryConfig: &modelPb.ExpiryConfig{
						Method: modelPb.ExpiryMethod_EXPIRY_METHOD_ABSOLUTE_DATE_TIME,
						ExpiryValue: &modelPb.ExpiryConfig_AbsoluteDateTimeParams{
							AbsoluteDateTimeParams: &modelPb.AbsoluteDateTimeParams{
								DateTime: timestamppb.New(time.Date(2022, 2, 10, 11, 30, 0, 0, datetime.IST)),
							},
						},
					},
				},
				req: reqWithoutValidity,
			},
			want: timestamppb.New(time.Date(2022, 2, 10, 11, 30, 0, 0, datetime.IST)),
		},
		{
			name: "validTill using expiry config - Relative Duration from gen in days",
			args: args{
				ctx:      context.Background(),
				dateTime: time.Date(2022, 2, 2, 12, 10, 10, 0, datetime.IST),
				frameworkResp: &modelPb.InsightFramework{
					ExpiryConfig: &modelPb.ExpiryConfig{
						Method: modelPb.ExpiryMethod_EXPIRY_METHOD_RELATIVE_DURATION_FROM_GENERATION_IN_DAYS,
						ExpiryValue: &modelPb.ExpiryConfig_RelativeDurationFromGenerationInDays{
							RelativeDurationFromGenerationInDays: 30,
						},
					},
				},
				req: reqWithoutValidity,
			},
			want: timestamppb.New(time.Date(2022, 3, 4, 12, 10, 10, 0, datetime.IST)),
		},
		{
			name: "validTill using expiry config - EndOfCalenderUnit from generation in weeks",
			args: args{
				ctx:      context.Background(),
				dateTime: time.Date(2022, 2, 3, 12, 10, 10, 0, datetime.IST),
				frameworkResp: &modelPb.InsightFramework{
					ExpiryConfig: &modelPb.ExpiryConfig{
						Method: modelPb.ExpiryMethod_EXPIRY_METHOD_END_OF_CALENDER_UNIT_FROM_GENERATION,
						ExpiryValue: &modelPb.ExpiryConfig_EndOfCalenderUnitParams{
							EndOfCalenderUnitParams: &modelPb.EndOfCalenderUnitParams{
								Unit:  modelPb.TimeUnit_TIME_UNIT_WEEK,
								Count: 2,
							},
						},
					},
				},
				req: reqWithoutValidity,
			},
			want: timestamppb.New(time.Date(2022, 2, 13, 0, 0, 0, 0, datetime.IST)),
		},
		{
			name: "validTill using expiry config - EndOfCalenderUnit from generation in Months",
			args: args{
				ctx:      context.Background(),
				dateTime: time.Date(2022, 10, 3, 12, 10, 10, 0, datetime.IST),
				frameworkResp: &modelPb.InsightFramework{
					ExpiryConfig: &modelPb.ExpiryConfig{
						Method: modelPb.ExpiryMethod_EXPIRY_METHOD_END_OF_CALENDER_UNIT_FROM_GENERATION,
						ExpiryValue: &modelPb.ExpiryConfig_EndOfCalenderUnitParams{
							EndOfCalenderUnitParams: &modelPb.EndOfCalenderUnitParams{
								Unit:  modelPb.TimeUnit_TIME_UNIT_MONTH,
								Count: 4,
							},
						},
					},
				},
				req: reqWithoutValidity,
			},
			want: timestamppb.New(time.Date(2023, 2, 1, 0, 0, 0, 0, datetime.IST)),
		},
		{
			name: "validTill using expiry config - EndOfCalenderUnit from generation in Months - 2",
			args: args{
				ctx:      context.Background(),
				dateTime: time.Date(2022, 1, 13, 12, 10, 10, 0, datetime.IST),
				frameworkResp: &modelPb.InsightFramework{
					ExpiryConfig: &modelPb.ExpiryConfig{
						Method: modelPb.ExpiryMethod_EXPIRY_METHOD_END_OF_CALENDER_UNIT_FROM_GENERATION,
						ExpiryValue: &modelPb.ExpiryConfig_EndOfCalenderUnitParams{
							EndOfCalenderUnitParams: &modelPb.EndOfCalenderUnitParams{
								Unit:  modelPb.TimeUnit_TIME_UNIT_MONTH,
								Count: 2,
							},
						},
					},
				},
				req: reqWithoutValidity,
			},
			want: timestamppb.New(time.Date(2022, 3, 1, 0, 0, 0, 0, datetime.IST)),
		},
		{
			name: "validTill using expiry config - EndOfCalenderUnit from generation in Months - 3",
			args: args{
				ctx:      context.Background(),
				dateTime: time.Date(2021, 12, 31, 12, 10, 10, 0, datetime.IST),
				frameworkResp: &modelPb.InsightFramework{
					ExpiryConfig: &modelPb.ExpiryConfig{
						Method: modelPb.ExpiryMethod_EXPIRY_METHOD_END_OF_CALENDER_UNIT_FROM_GENERATION,
						ExpiryValue: &modelPb.ExpiryConfig_EndOfCalenderUnitParams{
							EndOfCalenderUnitParams: &modelPb.EndOfCalenderUnitParams{
								Unit:  modelPb.TimeUnit_TIME_UNIT_MONTH,
								Count: 2,
							},
						},
					},
				},
				req: reqWithoutValidity,
			},
			want: timestamppb.New(time.Date(2022, 2, 1, 0, 0, 0, 0, datetime.IST)),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := s.GetValidTill(tt.args.ctx, tt.args.dateTime, tt.args.frameworkResp, tt.args.req)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetValidTill() gotTime = %v, wantTime %v", got, tt.want)
			}
		})
	}
}
