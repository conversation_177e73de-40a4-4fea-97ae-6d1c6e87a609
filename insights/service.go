package insights

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	errors2 "github.com/epifi/gamma/insights/errors"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/frontend/deeplink"
	pb "github.com/epifi/gamma/api/insights"
	modelPb "github.com/epifi/gamma/api/insights/model"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/insights/config/genconf"
	"github.com/epifi/gamma/insights/dao"
	"github.com/epifi/gamma/insights/realtime"
	"github.com/epifi/gamma/insights/utils"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

const (
	GenericInsightPeerGroupId = "PeerGroup-Generic"
	DefaultSessionId          = "default-session-id"
)

type Service struct {
	pb.UnimplementedInsightsServer

	Gconf *genconf.Config

	TxnExecutor            storagev2.TxnExecutor
	InsightFrameworkDao    dao.InsightFrameworkDao
	InsightSegmentDao      dao.InsightSegmentDao
	ActorInsightDao        dao.ActorInsightDao
	ContentTemplateDao     dao.ContentTemplateDao
	InsightEngagementDao   dao.InsightEngagementDao
	GenerationScriptRunDao dao.GenerationScriptRunDao
	FeedbackDao            dao.InsightFeedbackDao

	ActorClient          actorPb.ActorClient
	UserGroupClient      userGroupPb.GroupClient
	UserClient           user.UsersClient
	InsightsProcessor    realtime.InsightsProcessor
	GenerateRandomNumber utils.GenerateRandomNumber
}

func NewService(gconf *genconf.Config, txnExecutor storagev2.TxnExecutor, frameworkDao dao.InsightFrameworkDao, segmentDao dao.InsightSegmentDao,
	actorInsightDao dao.ActorInsightDao, contentTemplateDao dao.ContentTemplateDao, insightEngagementDao dao.InsightEngagementDao,
	generationScriptRunDao dao.GenerationScriptRunDao, feedbackDao dao.InsightFeedbackDao, actorClient actorPb.ActorClient, userGrpClient userGroupPb.GroupClient,
	usersClient user.UsersClient, insightsProcessor realtime.InsightsProcessor, generateRandomNumber utils.GenerateRandomNumber) *Service {
	return &Service{
		Gconf:                  gconf,
		TxnExecutor:            txnExecutor,
		InsightFrameworkDao:    frameworkDao,
		InsightSegmentDao:      segmentDao,
		ActorInsightDao:        actorInsightDao,
		ContentTemplateDao:     contentTemplateDao,
		InsightEngagementDao:   insightEngagementDao,
		GenerationScriptRunDao: generationScriptRunDao,
		FeedbackDao:            feedbackDao,
		ActorClient:            actorClient,
		UserGroupClient:        userGrpClient,
		UserClient:             usersClient,
		InsightsProcessor:      insightsProcessor,
		GenerateRandomNumber:   generateRandomNumber,
	}
}

// UserGroupsUsedForInsights contains all release groups that are used to control release of a insight segment.
var UserGroupsUsedForInsights = map[commontypes.UserGroup]bool{
	commontypes.UserGroup_INTERNAL: true,
	commontypes.UserGroup_FNF:      true,
}

// GetInsight takes actorId and insight context as input and returns an insight that can be shown to the user.
// It first queries actor_insights table with actorId and insight context details to fetch a personal insight for
// the user, if not found it tries to fetch a peer-group insight.
// If an actor insight is found, it fetches a template for the framework from content_templates table and
// replaces all the variables in the template with their values and returns the insight.
//
// To fast track testing of new insight segments, such segments are preferred for users who belong
// to such internal release groups i.e.,
// Users belonging to CUGs will be served new segments if available
// If not available, regular insights are served
func (s *Service) GetInsight(ctx context.Context, req *pb.GetInsightRequest) (res *pb.GetInsightResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.GetInsightResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.GetInsightResponse{
			Status: status,
		}, nil
	}
	res = &pb.GetInsightResponse{}

	// extract sessionId based on insight channel
	sessionId, err := getSessionId(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to get session id", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	actorId := req.GetActorId()

	// check if user has already seen max number of insights allowed in a session
	if req.GetDestination() != modelPb.InsightDestination_INSIGHT_DESTINATION_NOTIFICATION_CENTER {
		maxInsightsSeen, seenErr := s.userAlreadySeenMaxNumOfInsights(ctx, actorId, sessionId)
		if seenErr != nil {
			return errRes(rpc.StatusInternal(), seenErr.Error())
		}
		if maxInsightsSeen {
			return errRes(rpc.StatusResourceExhausted(), "")
		}
	}
	insightContext := &modelPb.InsightContext{
		ScreenName: req.GetInsightContext().GetScreenName(),
	}
	// TODO(mohit): Remove this condition once client side fix is released.
	if insightContext.GetScreenName() == deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED {
		insightContext.ScreenName = deeplink.Screen_PAY_LANDING_SCREEN
	}
	peerGroups := s.getPeerGroupsForActor(ctx, actorId)
	// fetch an active actor insight for the user
	actorInsight, err := s.getActorInsight(ctx, actorId, peerGroups, insightContext)
	if err != nil {
		return errRes(rpc.StatusInternal(), err.Error())
	}
	if actorInsight == nil {
		return errRes(rpc.StatusRecordNotFound(), "")
	}
	frameworkId := actorInsight.GetFrameworkId()
	framework, err := s.InsightFrameworkDao.GetById(ctx, frameworkId)
	if err != nil {
		logger.Error(ctx, "error fetching insight framework", zap.String(logger.FRAMEWORK_ID, frameworkId), zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	// check if framework is in active state
	if !isFrameworkActive(framework) {
		// if framework is currently not in active state then soft delete the actor insight entry and return.
		// no insight will be shown to the user in this case. We're soft deleting the actor-insight to ensure that
		// the same entry won't be picked again to be shown to the user.
		err = s.ActorInsightDao.SoftDelete(ctx, actorInsight.GetId())
		if err != nil {
			// not returning an error response as this is not a critical operation.
			logger.Error(ctx, "error while soft deleting actor insight", zap.Error(err))
		}
		res.Status = rpc.StatusOk()
		return res, nil
	}
	segment, err := s.InsightSegmentDao.GetById(ctx, actorInsight.GetSegmentId())
	if err != nil {
		logger.Error(ctx, "error fetching insight segment", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	// add default values for variables whose value is not present.
	actorInsight.Values = AddDefaultValuesForAbsentVars(actorInsight.GetValues(), segment.GetDefaultValues())
	template, err := s.ContentTemplateDao.GetRandomTemplateForFrameworkAndSegment(ctx, frameworkId, actorInsight.GetSegmentId())
	if err != nil {
		logger.Error(ctx, "error fetching content template for framework", zap.String(logger.FRAMEWORK_ID, frameworkId), zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	insightData, err := s.generateInsightData(ctx, actorInsight, framework, template)
	if err != nil {
		logger.Error(ctx, "error generating insight data", zap.Error(err), zap.String(logger.FRAMEWORK_ID, framework.GetId()),
			zap.String(logger.TEMPLATE_ID, template.GetId()), zap.String(logger.ACTOR_INSIGHT_ID, actorInsight.GetId()))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	err = validateInsightData(insightData)
	if err != nil {
		logger.Error(ctx, "validate insight data failed", zap.Error(err), zap.String(logger.FRAMEWORK_ID, framework.GetId()),
			zap.String(logger.TEMPLATE_ID, template.GetId()), zap.String(logger.ACTOR_INSIGHT_ID, actorInsight.GetId()))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	engagementEntry, err := s.InsightEngagementDao.Create(ctx, &modelPb.InsightEngagement{
		SessionId:        sessionId,
		ActorId:          actorId,
		ActorInsightId:   actorInsight.GetId(),
		SegmentId:        actorInsight.GetSegmentId(),
		TemplateId:       template.GetId(),
		EngagementAction: modelPb.EngagementAction_ENGAGEMENT_ACTION_SERVED,
		Destination:      getDestination(req),
	})
	if err != nil {
		logger.Error(ctx, "error creating insight engagement entry", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	return &pb.GetInsightResponse{
		Status:       rpc.StatusOk(),
		InsightData:  insightData,
		EngagementId: engagementEntry.GetId(),
	}, nil
}

func getSessionId(ctx context.Context, req *pb.GetInsightRequest) (string, error) {
	switch req.GetDestination() {
	case modelPb.InsightDestination_INSIGHT_DESTINATION_NOTIFICATION_CENTER:
		return DefaultSessionId, nil
	default:
		sessionId := epificontext.SessionIdFromContext(ctx)
		if sessionId == epificontext.UnknownId || sessionId == "" {
			return "", fmt.Errorf("session-id not present in context")
		}
		return sessionId, nil
	}
}

func getDestination(req *pb.GetInsightRequest) string {
	switch req.GetDestination() {
	case modelPb.InsightDestination_INSIGHT_DESTINATION_NOTIFICATION_CENTER:
		return req.GetDestination().String()
	default:
		return req.GetInsightContext().GetScreenName().String()
	}
}

// UpdateInsightEngagementDetails - Used to update insight engagementsAction and Rating for insight served to client
// When an insight is served to user, an insight engagement entry is created with engagementAction SERVED
// After the insight is served to user after a duration of x seconds, a UpdateInsightEngagementDetails request is sent from client
// to update engagementAction from SERVED to NOTICED.
// User also gets an option to provide Rating (before or after engagementAction is updated to NOTICED) and sends a
// UpdateInsightEngagementDetails req with Rating,
// This updates the engagementAction to ACTED_ON from SERVED/NOTICED and also sets/(updates the existing Rating) Rating in InsightFeedback table.
// Note: This rpc also supports Rating updates
func (s *Service) UpdateInsightEngagementDetails(ctx context.Context, req *pb.UpdateInsightEngagementDetailsRequest) (res *pb.UpdateInsightEngagementDetailsResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.UpdateInsightEngagementDetailsResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.UpdateInsightEngagementDetailsResponse{
			Status: status,
		}, nil
	}
	res = &pb.UpdateInsightEngagementDetailsResponse{}

	debugMsg := ValidateUpdateInsightEngagementDetailsRequest(ctx, req)
	if debugMsg != "" {
		return errRes(rpc.StatusInvalidArgument(), debugMsg)
	}

	engagementResp, err := s.InsightEngagementDao.GetById(ctx, req.GetEngagementId())
	if err != nil {
		logger.Error(ctx, "failed to get engagement by id", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}

	switch {
	// if rating is present in the request, we update rating and also update engagementAction to ACTED_ON
	case req.GetRating() != modelPb.Rating_RATING_UNSPECIFIED:
		err = s.UpdateEngagementAndRating(ctx, req.GetEngagementId(), req.GetActorId(), req.GetRating())
		if err != nil {
			logger.Error(ctx, "failed transaction to update EngagementEntry and Feedback Entry", zap.Error(err))
			return errRes(rpc.StatusInternal(), fmt.Sprintf("failed transaction to update EngagementEntry and Feedback Entry: %s", err.Error()))
		}
		err = s.updateActorInsightConsumedAtForPersonalised(ctx, engagementResp.GetActorInsightId())
		if err != nil {
			return errRes(rpc.StatusInternal(), "failed to update ActorInsight ConsumedAt For Personalised")
		}
	case req.GetMarkNoticed():
		// i.e. rating is not provided in req but mark_noticed is true, we update engagementAction to NOTICED

		// engagement entry should be in SERVED state, if not we return success without performing any action
		if engagementResp.GetEngagementAction() == modelPb.EngagementAction_ENGAGEMENT_ACTION_SERVED {
			engagementEntry := &modelPb.InsightEngagement{
				Id:               req.GetEngagementId(),
				EngagementAction: modelPb.EngagementAction_ENGAGEMENT_ACTION_NOTICED,
			}
			err = s.InsightEngagementDao.Update(ctx, engagementEntry, []modelPb.InsightEngagementFieldMask{
				modelPb.InsightEngagementFieldMask_INSIGHT_ENGAGEMENT_ENGAGEMENT_ACTION,
			})
			if err != nil {
				logger.Error(ctx, "failed to update engagement action to NOTICED", zap.Error(err))
				return errRes(rpc.StatusInternal(), err.Error())
			}
		}
		err = s.updateActorInsightConsumedAtForPersonalised(ctx, engagementResp.GetActorInsightId())
		if err != nil {
			return errRes(rpc.StatusInternal(), "failed to update ActorInsight ConsumedAt For Personalised")
		}
	default:
		logger.Error(ctx, "No rating is provided in req and mark_noticed is also set to false")
		return errRes(rpc.StatusInvalidArgument(), "No rating is provided in req and mark_noticed is also set to false")
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) updateActorInsightConsumedAtForPersonalised(ctx context.Context, actorInsightId string) error {
	actorInsightResp, err := s.ActorInsightDao.GetById(ctx, actorInsightId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to fetch corresponding actorInsight %s", actorInsightId), zap.Error(err))
		return err
	}
	if actorInsightResp.GetTargetEntityType() == modelPb.TargetEntityType_TARGET_ENTITY_TYPE_ACTOR {
		err = s.ActorInsightDao.Update(ctx, &modelPb.ActorInsight{
			Id:         actorInsightId,
			ConsumedAt: timestamppb.Now(),
		}, []modelPb.ActorInsightFieldMask{
			modelPb.ActorInsightFieldMask_ACTOR_INSIGHT_FIELD_MASK_CONSUMED_AT,
		})
		if err != nil {
			logger.Error(ctx, "failed to update personalised actorInsight consumed_at", zap.Error(err))
		}
	}
	return err
}

func ValidateUpdateInsightEngagementDetailsRequest(ctx context.Context, req *pb.UpdateInsightEngagementDetailsRequest) string {
	debugMsg := ""
	if req == nil {
		logger.Error(ctx, "req is nil")
		debugMsg = "req is nil"
	}
	if req.GetEngagementId() == "" {
		logger.Error(ctx, "engagementId is empty in request")
		debugMsg = "engagementId is empty in request"
	}

	return debugMsg
}

// UpdateEngagementAndRating - Given a engagementId, actorId and valid rating, this method update the rating
// and also set engagementAction to ACTED_ON
// Both operations are performed in a transaction to make them atomic
func (s *Service) UpdateEngagementAndRating(ctx context.Context, engagementId string, actorId string, rating modelPb.Rating) error {
	txnErr := s.TxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		feedbackEntry := &modelPb.InsightFeedback{
			EngagementId: engagementId,
			ActorId:      actorId,
			Rating:       rating,
		}
		engagementEntry := &modelPb.InsightEngagement{
			Id:               engagementId,
			EngagementAction: modelPb.EngagementAction_ENGAGEMENT_ACTION_ACTED_ON,
		}
		err := s.InsightEngagementDao.Update(ctx, engagementEntry, []modelPb.InsightEngagementFieldMask{
			modelPb.InsightEngagementFieldMask_INSIGHT_ENGAGEMENT_ENGAGEMENT_ACTION,
		})
		if err != nil {
			return err
		}

		feedbackResp, err := s.FeedbackDao.GetByEngagementId(ctx, engagementId)

		switch {
		case errors.Is(err, gorm.ErrRecordNotFound):
			// No feedback(rating) entry exists i.e. received feedback for first time for given engagement
			// create a new feedback entry with given rating
			_, err = s.FeedbackDao.Create(ctx, feedbackEntry)
			if err != nil {
				return err
			}
		case err != nil:
			return err
		default:
			// Feedback entry already exists, update the rating
			feedbackEntry.Id = feedbackResp.Id
			// update the rating to a new rating in feedback dao
			err := s.FeedbackDao.Update(ctx, feedbackEntry, []modelPb.InsightFeedbackFieldMask{
				modelPb.InsightFeedbackFieldMask_INSIGHT_FEEDBACK_RATING,
			})
			if err != nil {
				return err
			}
		}
		return nil
	})
	return txnErr
}

// getActorInsight returns an actor insight that is eligible to be shown to the user.
// Based on some probability defined in cfg, the real-time insights flow is invoked which try to generate an insight from predefined frameworks
// if no real-time insight is available, we fallback to precomputed insights
// Insight frameworks rolled out to internal release groups are given a priority to fast track testing
// So, the method first checks if there is some insight present in release groups associated with the user
// If there is no insight found in release group, it returns an insight based on the insight context.
func (s *Service) getActorInsight(ctx context.Context, actorId string, peerGroupIds []string, insightCtx *modelPb.InsightContext) (actorInsight *modelPb.ActorInsight, err error) {
	// if no real time insight is found, proceed with offline store insights approach
	releaseGrps, _ := s.getFilteredUserGroups(ctx, actorId)
	actorInsight, err = s.getRealTimeInsight(ctx, actorId, releaseGrps)
	if err != nil && !errors.Is(err, errors2.NoInsightsDataGeneratedErr) {
		logger.Error(ctx, "failed to get real time actor insight", zap.Error(err))
	}
	if actorInsight != nil {
		logger.Info(ctx, "serving real time insight", zap.String(logger.FRAMEWORK_ID, actorInsight.GetFrameworkId()),
			zap.String(logger.SEGMENT_ID, actorInsight.GetSegmentId()))
		return actorInsight, nil
	}
	fetchInsightForReleaseGrp := len(releaseGrps) != 0
	if fetchInsightForReleaseGrp {
		logger.Info(ctx, "fetching insight in release group")
		actorInsight, _ = s.fetchActorInsightForReleaseGroup(ctx, actorId, insightCtx, peerGroupIds, releaseGrps)
	}
	if actorInsight != nil {
		return actorInsight, nil
	}
	logger.Info(ctx, "fetching insight for context")
	actorInsight, err = s.fetchActorInsightWithContext(ctx, actorId, peerGroupIds, insightCtx)
	if err != nil {
		return nil, err
	}
	return actorInsight, nil
}

// fetchActorInsightWithContext returns an insight that is eligible to be shown to the user based on insight context.
// It first tries to fetch a personal insight, if not found it returns a peer-group insight.
func (s *Service) fetchActorInsightWithContext(ctx context.Context, actorId string, peerGroupIds []string, insightCtx *modelPb.InsightContext) (actorInsight *modelPb.ActorInsight, err error) {
	actorInsight, err = s.ActorInsightDao.GetPersonalInsightForActorWithContext(ctx, insightCtx, actorId, nil)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching personal insight for context", zap.Error(err), zap.String(logger.SCREEN, insightCtx.String()))
		return nil, err
	}
	if actorInsight != nil {
		logger.Info(ctx, "personal insight found for user")
		return actorInsight, nil
	}
	logger.Info(ctx, "personal insight not found for actor")
	if len(peerGroupIds) == 0 {
		logger.Info(ctx, "not checking for peer group insights as no peerGroupIds array is empty")
		return nil, nil
	}
	excludeInsightShownAfter := time.Now().Add(-1 * s.Gconf.InsightServing().ActorInsightRecurrenceMinDuration())
	actorInsight, err = s.ActorInsightDao.GetPeerGroupInsightForActorWithContext(ctx, insightCtx, actorId, peerGroupIds, nil, excludeInsightShownAfter)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching peer-group insight for context", zap.Error(err), zap.String(logger.SCREEN, insightCtx.String()))
		return nil, err
	}
	if actorInsight != nil {
		logger.Info(ctx, "peer-group insight found for user")
	} else {
		logger.Info(ctx, "peer-group insight not found for user")
	}
	return actorInsight, nil
}

// fetchActorInsightForReleaseGroup returns an actor insight for a user which is currently only active for users part of `releaseGroups`
// It first tries to fetch a personal insight, if not found it returns a peer-group insight.
func (s *Service) fetchActorInsightForReleaseGroup(ctx context.Context, actorId string, insightCtx *modelPb.InsightContext, peerGroupIds []string,
	releaseGroups []commontypes.UserGroup) (actorInsight *modelPb.ActorInsight, err error) {
	segments, err := s.getSegmentsInReleaseGroup(ctx, releaseGroups)
	if err != nil {
		logger.Error(ctx, "error while fetching segments in release group", zap.Error(err))
		return nil, err
	}
	if len(segments) == 0 {
		logger.Info(ctx, "no segments in release group", zap.Any(logger.USER_GROUP, releaseGroups))
		return nil, nil
	}
	segmentIds := make([]string, 0)
	for _, segment := range segments {
		segmentIds = append(segmentIds, segment.Id)
	}
	actorInsight, err = s.ActorInsightDao.GetPersonalInsightForActorWithContext(ctx, insightCtx, actorId, segmentIds)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching personal insight in segments", zap.Error(err))
		return nil, err
	}
	if actorInsight != nil {
		logger.Info(ctx, "personal insight found for user")
		return actorInsight, nil
	}
	logger.Info(ctx, "personal insight not found for actor")
	if len(peerGroupIds) == 0 {
		logger.Info(ctx, "not checking for peer group insights as peerGroupIds array is empty")
		return nil, nil
	}
	excludeInsightShownAfter := time.Now().Add(-1 * s.Gconf.InsightServing().ActorInsightRecurrenceMinDuration())
	actorInsight, err = s.ActorInsightDao.GetPeerGroupInsightForActorWithContext(ctx, insightCtx, actorId, peerGroupIds, segmentIds, excludeInsightShownAfter)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching peer-group insight in segments", zap.Error(err))
		return nil, err
	}
	if actorInsight != nil {
		logger.Info(ctx, "peer-group insight found for user")
	} else {
		logger.Info(ctx, "peer-group insight not found for user")
	}
	return actorInsight, nil
}

// getSegmentsInReleaseGroup checks for frameworks and segments that are associated with releaseGroups and return them.
// For frameworks that are associated with a release group, it returns all the segments present for that framework.
func (s *Service) getSegmentsInReleaseGroup(ctx context.Context, releaseGroups []commontypes.UserGroup) ([]*modelPb.InsightSegment, error) {
	frameworks, err := s.InsightFrameworkDao.GetFrameworksInReleaseGroups(ctx, releaseGroups)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching frameworks for release groups", zap.Error(err))
		return nil, err
	}
	frameworkIds := make([]string, 0)
	for _, framework := range frameworks {
		frameworkIds = append(frameworkIds, framework.GetId())
	}
	allSegments := make([]*modelPb.InsightSegment, 0)
	if len(frameworks) != 0 {
		segments, err1 := s.InsightSegmentDao.GetSegmentsInFramaworkIds(ctx, frameworkIds)
		if err1 != nil {
			logger.Error(ctx, "error while fetching segments for frameworks in release group", zap.Error(err1))
			return nil, err1
		}
		if len(segments) != 0 {
			allSegments = append(allSegments, segments...)
		}
	}
	segments, err := s.InsightSegmentDao.GetSegmentsInReleaseGroups(ctx, releaseGroups)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching segments for release groups", zap.Error(err))
		return nil, err
	}
	if len(segments) != 0 {
		allSegments = append(allSegments, segments...)
	}
	return allSegments, nil
}

func (s *Service) generateInsightData(ctx context.Context, actorInsight *modelPb.ActorInsight,
	framework *modelPb.InsightFramework, template *modelPb.ContentTemplate) (*pb.InsightData, error) {
	contentDetails := template.GetContentDetails()
	deeplinkStr := framework.GetDeeplink()

	insightData := &pb.InsightData{}

	stringReplacer := createStringReplacerForActorInsight(ctx, actorInsight)
	populateContentDetailsInInsightData(ctx, insightData, contentDetails, stringReplacer)
	err := populateDeeplinkInInsightsData(ctx, insightData, deeplinkStr, stringReplacer)
	if err != nil {
		return nil, err
	}
	return insightData, nil
}

func createStringReplacerForActorInsight(_ context.Context, actorInsight *modelPb.ActorInsight) *strings.Replacer {
	oldNewArr := make([]string, 0)
	for _, valueObj := range actorInsight.GetValues() {
		oldNewArr = append(oldNewArr, getIdentifierForVariable(valueObj.GetVariable().String()), valueObj.GetVariableValue())
	}
	return strings.NewReplacer(oldNewArr...)
}

func populateContentDetailsInInsightData(_ context.Context, insightData *pb.InsightData, contentDetails *modelPb.ContentDetails,
	replacer *strings.Replacer) {
	insightData.InsightCardType = contentDetails.GetCardType()
	insightData.IconUrl = replacer.Replace(contentDetails.GetIconUrl())
	insightData.Title = newTextElement(replacer.Replace(contentDetails.GetTitle().GetText()))

	insightData.SourceInfo = newTextElement(replacer.Replace(contentDetails.GetSourceInfo().GetText()))
	insightData.Body = newTextElement(replacer.Replace(contentDetails.GetRichText().GetText()))
	if insightData.Cta == nil {
		insightData.Cta = &pb.Cta{}
	}
	insightData.Cta.CtaText = newTextElement(replacer.Replace(contentDetails.GetCtaText().GetText()))
}

func populateDeeplinkInInsightsData(_ context.Context, insightsData *pb.InsightData, deeplinkText string,
	replacer *strings.Replacer) error {
	deeplinkText = replacer.Replace(deeplinkText)
	var deeplinkObj deeplink.Deeplink
	err := protojson.Unmarshal([]byte(deeplinkText), &deeplinkObj)
	if err != nil {
		return fmt.Errorf("error unmarshaling deeplink text: %w", err)
	}
	if insightsData.Cta == nil {
		insightsData.Cta = &pb.Cta{}
	}
	insightsData.Cta.Deeplink = &deeplinkObj
	return nil
}

// userAlreadySeenMaxNumOfInsights checks if user has already seen max number of insights allowed in the day/session.
func (s *Service) userAlreadySeenMaxNumOfInsights(ctx context.Context, actorId, sessionId string) (bool, error) {
	maxInsights := s.Gconf.InsightServing().InsightCapPerSession()
	insightShown, err := s.InsightEngagementDao.GetBySessionIdAndEngagementActionIn(ctx, actorId, sessionId, []modelPb.EngagementAction{
		modelPb.EngagementAction_ENGAGEMENT_ACTION_ACTED_ON,
		modelPb.EngagementAction_ENGAGEMENT_ACTION_NOTICED,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching engagement logs for session", zap.String(logger.SESSION_ID, sessionId))
		return false, fmt.Errorf("error fetching engagement records by sessionId: %w", err)
	}
	numInsightsShown := len(insightShown)
	return numInsightsShown >= maxInsights, nil
}

// getFilteredUserGroups returns list of release groups to which a given actor is linked and the groups are also supported by insight segment
func (s *Service) getFilteredUserGroups(ctx context.Context, actorId string) ([]commontypes.UserGroup, error) {
	userGrps, err := s.getReleaseGroups(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while fetching user groups", zap.Error(err))
		return nil, err
	}
	return filterInsightSegmentReleaseGroups(userGrps), nil
}

// getReleaseGroups returns release groups to which a given actor is linked
// in case user doesn't belong to any grp then empty slice is returned
func (s *Service) getReleaseGroups(ctx context.Context, actorId string) ([]commontypes.UserGroup, error) {
	actorRes, err := s.ActorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})
	if err1 := epifigrpc.RPCError(actorRes, err); err1 != nil {
		return nil, fmt.Errorf("failed to fetch actor details: %w", err1)
	}
	if actorRes.GetActor().GetType() != types.Actor_USER {
		return []commontypes.UserGroup{}, nil
	}
	userId := actorRes.GetActor().GetEntityId()
	userRes, err := s.UserClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{Id: userId},
	})
	if err1 := epifigrpc.RPCError(userRes, err); err1 != nil {
		return nil, fmt.Errorf("failed to fetch user: %s: %w", userId, err1)
	}

	userGrpRes, err := s.UserGroupClient.GetGroupsMappedToEmail(ctx,
		&userGroupPb.GetGroupsMappedToEmailRequest{Email: userRes.GetUser().GetProfile().GetEmail()})
	if err1 := epifigrpc.RPCError(userGrpRes, err); err1 != nil {
		return nil, fmt.Errorf("failed to fetch user group: %s: %w", userId, err1)
	}

	return userGrpRes.GetGroups(), nil
}

// getPeerGroupsForUser returns list of peer groups Ids that an actor is associated with.
func (s *Service) getPeerGroupsForActor(_ context.Context, _ string) []string {
	// only returning generic peer group id as mapping between actors and peer groups is not yet defined
	return []string{
		GenericInsightPeerGroupId,
	}
}

// Only a small set of release groups are being used by insight segments. This function filters the input array
// and returns the release groups which are being used by insight segments.
func filterInsightSegmentReleaseGroups(userGroups []commontypes.UserGroup) []commontypes.UserGroup {
	filteredList := make([]commontypes.UserGroup, 0)
	for _, grp := range userGroups {
		if _, ok := UserGroupsUsedForInsights[grp]; ok {
			filteredList = append(filteredList, grp)
		}
	}
	return filteredList
}

func getIdentifierForVariable(variableName string) string {
	return fmt.Sprintf("{#%s#}", variableName)
}

func isFrameworkActive(framework *modelPb.InsightFramework) bool {
	return framework.GetInsightState() == modelPb.InsightState_INSIGHT_STATE_ACTIVE
}

func newTextElement(text string) *modelPb.TextElement {
	return &modelPb.TextElement{
		Text: text,
	}
}

func AddDefaultValuesForAbsentVars(values, defaultValues []*modelPb.InsightVariableValuePair) []*modelPb.InsightVariableValuePair {
	valsPresentMap := make(map[modelPb.InsightVariable]bool)
	for _, pair := range values {
		valsPresentMap[pair.GetVariable()] = true
	}
	for _, pair := range defaultValues {
		if ok := valsPresentMap[pair.GetVariable()]; !ok {
			values = append(values, pair)
		}
	}
	return values
}

//nolint:dupl
func (s *Service) GetFramework(ctx context.Context, req *pb.GetFrameworkRequest) (res *pb.GetFrameworkResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.GetFrameworkResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.GetFrameworkResponse{
			Status: status,
		}, nil
	}

	res = &pb.GetFrameworkResponse{}

	switch req.GetIdentifier().(type) {
	case *pb.GetFrameworkRequest_FrameworkId:
		daoRes, err := s.InsightFrameworkDao.GetById(ctx, req.GetFrameworkId())
		if err != nil {
			logger.Error(ctx, "failed to fetch insight framework ById", zap.Error(err), zap.String(logger.FRAMEWORK_ID, req.GetFrameworkId()))
			return errRes(rpc.StatusInternal(), err.Error())
		}
		res.Framework = daoRes

	case *pb.GetFrameworkRequest_FrameworkName:
		daoRes, err := s.InsightFrameworkDao.GetByName(ctx, req.GetFrameworkName())
		if err != nil {
			logger.Error(ctx, "failed to fetch insight framework ByName", zap.Error(err), zap.String(logger.FRAMEWORK_NAME, req.GetFrameworkName()))
			return errRes(rpc.StatusInternal(), err.Error())
		}
		res.Framework = daoRes
	default:
		return errRes(rpc.StatusInvalidArgument(), "invalid identifier")
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

//nolint:dupl
func (s *Service) GetSegment(ctx context.Context, req *pb.GetSegmentRequest) (res *pb.GetSegmentResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.GetSegmentResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.GetSegmentResponse{
			Status: status,
		}, nil
	}

	res = &pb.GetSegmentResponse{}

	switch req.GetIdentifier().(type) {
	case *pb.GetSegmentRequest_SegmentId:
		daoRes, err := s.InsightSegmentDao.GetById(ctx, req.GetSegmentId())
		if err != nil {
			logger.Error(ctx, "failed to fetch insight segment byId()", zap.Error(err), zap.String(logger.SEGMENT_ID, req.GetSegmentId()))
			return errRes(rpc.StatusInternal(), err.Error())
		}
		res.Segment = daoRes

	default:
		return errRes(rpc.StatusInvalidArgument(), "invalid identifier")
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

//nolint:dupl
func (s *Service) GetContentTemplate(ctx context.Context, req *pb.GetContentTemplateRequest) (res *pb.GetContentTemplateResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.GetContentTemplateResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.GetContentTemplateResponse{
			Status: status,
		}, nil
	}

	res = &pb.GetContentTemplateResponse{}

	switch req.GetIdentifier().(type) {
	case *pb.GetContentTemplateRequest_ContentTemplateId:
		daoRes, err := s.ContentTemplateDao.GetById(ctx, req.GetContentTemplateId())
		if err != nil {
			logger.Error(ctx, "failed to fetch content template byId()", zap.Error(err), zap.String(logger.TEMPLATE_ID, req.GetContentTemplateId()))
			return errRes(rpc.StatusInternal(), err.Error())
		}
		res.ContentTemplate = daoRes

	default:
		return errRes(rpc.StatusInvalidArgument(), "invalid identifier")
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

//nolint:dupl
func (s *Service) UpdateInsightFramework(ctx context.Context, req *pb.UpdateInsightFrameworkRequest) (res *pb.UpdateInsightFrameworkResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.UpdateInsightFrameworkResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.UpdateInsightFrameworkResponse{
			Status: status,
		}, nil
	}

	res = &pb.UpdateInsightFrameworkResponse{}

	if len(req.GetMask()) == 0 {
		logger.Error(ctx, "update mask is empty")
		return errRes(rpc.StatusInvalidArgument(), "update mask is empty")
	}
	err = s.InsightFrameworkDao.Update(ctx, req.GetFramework(), req.GetMask())
	if err != nil {
		logger.Error(ctx, "failed to update framework", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

//nolint:dupl
func (s *Service) UpdateInsightSegment(ctx context.Context, req *pb.UpdateInsightSegmentRequest) (res *pb.UpdateInsightSegmentResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.UpdateInsightSegmentResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.UpdateInsightSegmentResponse{
			Status: status,
		}, nil
	}
	res = &pb.UpdateInsightSegmentResponse{}

	if len(req.GetMask()) == 0 {
		logger.Error(ctx, "update mask is empty")
		return errRes(rpc.StatusInvalidArgument(), "update mask is empty")
	}
	err = s.InsightSegmentDao.Update(ctx, req.GetSegment(), req.GetMask())
	if err != nil {
		logger.Error(ctx, "failed to update segment", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

//nolint:dupl
func (s *Service) UpdateContentTemplate(ctx context.Context, req *pb.UpdateContentTemplateRequest) (res *pb.UpdateContentTemplateResponse, err error) {
	errRes := func(status *rpc.Status, debug string) (*pb.UpdateContentTemplateResponse, error) {
		status.SetDebugMessage(debug)
		return &pb.UpdateContentTemplateResponse{
			Status: status,
		}, nil
	}
	res = &pb.UpdateContentTemplateResponse{}

	if len(req.GetMask()) == 0 {
		logger.Error(ctx, "update mask is empty")
		return errRes(rpc.StatusInvalidArgument(), "update mask is empty")
	}
	err = s.ContentTemplateDao.Update(ctx, req.GetContentTemplate(), req.GetMask())
	if err != nil {
		logger.Error(ctx, "failed to update content template", zap.Error(err))
		return errRes(rpc.StatusInternal(), err.Error())
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func validateInsightData(data *pb.InsightData) error {
	switch {
	case containsUnPopulatedVariable(data.GetBody().GetText()):
		return fmt.Errorf("insightData body contains unpoulated variables, failure")
	case containsUnPopulatedVariable(data.GetTitle().GetText()):
		return fmt.Errorf("insightData title contains unpoulated variables, failure")
	case containsUnPopulatedVariable(data.GetSourceInfo().GetText()):
		return fmt.Errorf("insightData source_info contains unpoulated variables, failure")
	case containsUnPopulatedVariable(data.GetIconUrl()):
		return fmt.Errorf("insightData icon_url contains unpoulated variables, failure")
	case containsUnPopulatedVariable(data.GetCta().GetCtaText().GetText()):
		return fmt.Errorf("insightData cta_text contains unpoulated variables, failure")
	}
	return nil
}

func containsUnPopulatedVariable(s string) bool {
	if strings.Contains(s, "{#") || strings.Contains(s, "#}") {
		return true
	}
	return false
}
