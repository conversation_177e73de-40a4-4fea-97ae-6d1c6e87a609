// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	model "github.com/epifi/gamma/api/insights/epf/model"
	gomock "github.com/golang/mock/gomock"
)

// MockEPFPassbookRequestDao is a mock of EPFPassbookRequestDao interface.
type MockEPFPassbookRequestDao struct {
	ctrl     *gomock.Controller
	recorder *MockEPFPassbookRequestDaoMockRecorder
}

// MockEPFPassbookRequestDaoMockRecorder is the mock recorder for MockEPFPassbookRequestDao.
type MockEPFPassbookRequestDaoMockRecorder struct {
	mock *MockEPFPassbookRequestDao
}

// NewMockEPFPassbookRequestDao creates a new mock instance.
func NewMockEPFPassbookRequestDao(ctrl *gomock.Controller) *MockEPFPassbookRequestDao {
	mock := &MockEPFPassbookRequestDao{ctrl: ctrl}
	mock.recorder = &MockEPFPassbookRequestDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEPFPassbookRequestDao) EXPECT() *MockEPFPassbookRequestDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEPFPassbookRequestDao) Create(ctx context.Context, request *model.EPFPassbookRequest) (*model.EPFPassbookRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, request)
	ret0, _ := ret[0].(*model.EPFPassbookRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEPFPassbookRequestDaoMockRecorder) Create(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEPFPassbookRequestDao)(nil).Create), ctx, request)
}

// CreateOrUpdate mocks base method.
func (m *MockEPFPassbookRequestDao) CreateOrUpdate(ctx context.Context, epfPassbookRequest *model.EPFPassbookRequest, updateMask []model.EPFPassbookRequestFieldMask) (*model.EPFPassbookRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdate", ctx, epfPassbookRequest, updateMask)
	ret0, _ := ret[0].(*model.EPFPassbookRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdate indicates an expected call of CreateOrUpdate.
func (mr *MockEPFPassbookRequestDaoMockRecorder) CreateOrUpdate(ctx, epfPassbookRequest, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdate", reflect.TypeOf((*MockEPFPassbookRequestDao)(nil).CreateOrUpdate), ctx, epfPassbookRequest, updateMask)
}

// GetByClientRequestIdAndActorId mocks base method.
func (m *MockEPFPassbookRequestDao) GetByClientRequestIdAndActorId(ctx context.Context, clientRequestId, actorId string) (*model.EPFPassbookRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientRequestIdAndActorId", ctx, clientRequestId, actorId)
	ret0, _ := ret[0].(*model.EPFPassbookRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestIdAndActorId indicates an expected call of GetByClientRequestIdAndActorId.
func (mr *MockEPFPassbookRequestDaoMockRecorder) GetByClientRequestIdAndActorId(ctx, clientRequestId, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestIdAndActorId", reflect.TypeOf((*MockEPFPassbookRequestDao)(nil).GetByClientRequestIdAndActorId), ctx, clientRequestId, actorId)
}

// GetLatestRequestByActorIdAndUanNumber mocks base method.
func (m *MockEPFPassbookRequestDao) GetLatestRequestByActorIdAndUanNumber(ctx context.Context, actorId, uanNumber string) (*model.EPFPassbookRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestRequestByActorIdAndUanNumber", ctx, actorId, uanNumber)
	ret0, _ := ret[0].(*model.EPFPassbookRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestRequestByActorIdAndUanNumber indicates an expected call of GetLatestRequestByActorIdAndUanNumber.
func (mr *MockEPFPassbookRequestDaoMockRecorder) GetLatestRequestByActorIdAndUanNumber(ctx, actorId, uanNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestRequestByActorIdAndUanNumber", reflect.TypeOf((*MockEPFPassbookRequestDao)(nil).GetLatestRequestByActorIdAndUanNumber), ctx, actorId, uanNumber)
}

// GetRequestsByActorId mocks base method.
func (m *MockEPFPassbookRequestDao) GetRequestsByActorId(ctx context.Context, actorId string, limit int, filters ...storagev2.FilterOption) ([]*model.EPFPassbookRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, limit}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRequestsByActorId", varargs...)
	ret0, _ := ret[0].([]*model.EPFPassbookRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestsByActorId indicates an expected call of GetRequestsByActorId.
func (mr *MockEPFPassbookRequestDaoMockRecorder) GetRequestsByActorId(ctx, actorId, limit interface{}, filters ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, limit}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestsByActorId", reflect.TypeOf((*MockEPFPassbookRequestDao)(nil).GetRequestsByActorId), varargs...)
}

// Update mocks base method.
func (m *MockEPFPassbookRequestDao) Update(ctx context.Context, request *model.EPFPassbookRequest, updateMask []model.EPFPassbookRequestFieldMask) (*model.EPFPassbookRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, request, updateMask)
	ret0, _ := ret[0].(*model.EPFPassbookRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockEPFPassbookRequestDaoMockRecorder) Update(ctx, request, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockEPFPassbookRequestDao)(nil).Update), ctx, request, updateMask)
}

// MockUANAccountDao is a mock of UANAccountDao interface.
type MockUANAccountDao struct {
	ctrl     *gomock.Controller
	recorder *MockUANAccountDaoMockRecorder
}

// MockUANAccountDaoMockRecorder is the mock recorder for MockUANAccountDao.
type MockUANAccountDaoMockRecorder struct {
	mock *MockUANAccountDao
}

// NewMockUANAccountDao creates a new mock instance.
func NewMockUANAccountDao(ctrl *gomock.Controller) *MockUANAccountDao {
	mock := &MockUANAccountDao{ctrl: ctrl}
	mock.recorder = &MockUANAccountDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUANAccountDao) EXPECT() *MockUANAccountDaoMockRecorder {
	return m.recorder
}

// BatchUpsertByActorIdAndUAN mocks base method.
func (m *MockUANAccountDao) BatchUpsertByActorIdAndUAN(ctx context.Context, accounts []*model.UANAccount, updateMask []model.UANAccountFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpsertByActorIdAndUAN", ctx, accounts, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpsertByActorIdAndUAN indicates an expected call of BatchUpsertByActorIdAndUAN.
func (mr *MockUANAccountDaoMockRecorder) BatchUpsertByActorIdAndUAN(ctx, accounts, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsertByActorIdAndUAN", reflect.TypeOf((*MockUANAccountDao)(nil).BatchUpsertByActorIdAndUAN), ctx, accounts, updateMask)
}

// DeleteByActorId mocks base method.
func (m *MockUANAccountDao) DeleteByActorId(ctx context.Context, actorId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorId", ctx, actorId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorId indicates an expected call of DeleteByActorId.
func (mr *MockUANAccountDaoMockRecorder) DeleteByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorId", reflect.TypeOf((*MockUANAccountDao)(nil).DeleteByActorId), ctx, actorId)
}

// DeleteByActorIdAndUanNumber mocks base method.
func (m *MockUANAccountDao) DeleteByActorIdAndUanNumber(ctx context.Context, actorId, uanNumber string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorIdAndUanNumber", ctx, actorId, uanNumber)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorIdAndUanNumber indicates an expected call of DeleteByActorIdAndUanNumber.
func (mr *MockUANAccountDaoMockRecorder) DeleteByActorIdAndUanNumber(ctx, actorId, uanNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorIdAndUanNumber", reflect.TypeOf((*MockUANAccountDao)(nil).DeleteByActorIdAndUanNumber), ctx, actorId, uanNumber)
}

// GetByActorId mocks base method.
func (m *MockUANAccountDao) GetByActorId(ctx context.Context, actorId string) ([]*model.UANAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId)
	ret0, _ := ret[0].([]*model.UANAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockUANAccountDaoMockRecorder) GetByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockUANAccountDao)(nil).GetByActorId), ctx, actorId)
}

// GetByActorIdAndUAN mocks base method.
func (m *MockUANAccountDao) GetByActorIdAndUAN(ctx context.Context, actorId, uanNumber string) (*model.UANAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndUAN", ctx, actorId, uanNumber)
	ret0, _ := ret[0].(*model.UANAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndUAN indicates an expected call of GetByActorIdAndUAN.
func (mr *MockUANAccountDaoMockRecorder) GetByActorIdAndUAN(ctx, actorId, uanNumber interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndUAN", reflect.TypeOf((*MockUANAccountDao)(nil).GetByActorIdAndUAN), ctx, actorId, uanNumber)
}

// GetById mocks base method.
func (m *MockUANAccountDao) GetById(ctx context.Context, id string) (*model.UANAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*model.UANAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockUANAccountDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockUANAccountDao)(nil).GetById), ctx, id)
}

// Update mocks base method.
func (m *MockUANAccountDao) Update(ctx context.Context, account *model.UANAccount, updateMask []model.UANAccountFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, account, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockUANAccountDaoMockRecorder) Update(ctx, account, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockUANAccountDao)(nil).Update), ctx, account, updateMask)
}

// MockEPFImportSessionDao is a mock of EPFImportSessionDao interface.
type MockEPFImportSessionDao struct {
	ctrl     *gomock.Controller
	recorder *MockEPFImportSessionDaoMockRecorder
}

// MockEPFImportSessionDaoMockRecorder is the mock recorder for MockEPFImportSessionDao.
type MockEPFImportSessionDaoMockRecorder struct {
	mock *MockEPFImportSessionDao
}

// NewMockEPFImportSessionDao creates a new mock instance.
func NewMockEPFImportSessionDao(ctrl *gomock.Controller) *MockEPFImportSessionDao {
	mock := &MockEPFImportSessionDao{ctrl: ctrl}
	mock.recorder = &MockEPFImportSessionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEPFImportSessionDao) EXPECT() *MockEPFImportSessionDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEPFImportSessionDao) Create(ctx context.Context, request *model.EPFImportSession) (*model.EPFImportSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, request)
	ret0, _ := ret[0].(*model.EPFImportSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEPFImportSessionDaoMockRecorder) Create(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEPFImportSessionDao)(nil).Create), ctx, request)
}

// GetById mocks base method.
func (m *MockEPFImportSessionDao) GetById(ctx context.Context, externalId string, filters ...storagev2.FilterOption) (*model.EPFImportSession, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, externalId}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetById", varargs...)
	ret0, _ := ret[0].(*model.EPFImportSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockEPFImportSessionDaoMockRecorder) GetById(ctx, externalId interface{}, filters ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, externalId}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockEPFImportSessionDao)(nil).GetById), varargs...)
}

// UpdateById mocks base method.
func (m *MockEPFImportSessionDao) UpdateById(ctx context.Context, request *model.EPFImportSession, updateMask []model.EPFImportSessionFieldMask) (*model.EPFImportSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateById", ctx, request, updateMask)
	ret0, _ := ret[0].(*model.EPFImportSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateById indicates an expected call of UpdateById.
func (mr *MockEPFImportSessionDaoMockRecorder) UpdateById(ctx, request, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateById", reflect.TypeOf((*MockEPFImportSessionDao)(nil).UpdateById), ctx, request, updateMask)
}

// MockEpfPassbookEmployeeDetailsDao is a mock of EpfPassbookEmployeeDetailsDao interface.
type MockEpfPassbookEmployeeDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockEpfPassbookEmployeeDetailsDaoMockRecorder
}

// MockEpfPassbookEmployeeDetailsDaoMockRecorder is the mock recorder for MockEpfPassbookEmployeeDetailsDao.
type MockEpfPassbookEmployeeDetailsDaoMockRecorder struct {
	mock *MockEpfPassbookEmployeeDetailsDao
}

// NewMockEpfPassbookEmployeeDetailsDao creates a new mock instance.
func NewMockEpfPassbookEmployeeDetailsDao(ctrl *gomock.Controller) *MockEpfPassbookEmployeeDetailsDao {
	mock := &MockEpfPassbookEmployeeDetailsDao{ctrl: ctrl}
	mock.recorder = &MockEpfPassbookEmployeeDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpfPassbookEmployeeDetailsDao) EXPECT() *MockEpfPassbookEmployeeDetailsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEpfPassbookEmployeeDetailsDao) Create(ctx context.Context, data *model.EpfPassbookEmployeeDetails) (*model.EpfPassbookEmployeeDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, data)
	ret0, _ := ret[0].(*model.EpfPassbookEmployeeDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEpfPassbookEmployeeDetailsDaoMockRecorder) Create(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEpfPassbookEmployeeDetailsDao)(nil).Create), ctx, data)
}

// DeleteByActorId mocks base method.
func (m *MockEpfPassbookEmployeeDetailsDao) DeleteByActorId(ctx context.Context, actorId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorId", ctx, actorId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorId indicates an expected call of DeleteByActorId.
func (mr *MockEpfPassbookEmployeeDetailsDaoMockRecorder) DeleteByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorId", reflect.TypeOf((*MockEpfPassbookEmployeeDetailsDao)(nil).DeleteByActorId), ctx, actorId)
}

// MockEpfPassbookOverallPfBalanceDao is a mock of EpfPassbookOverallPfBalanceDao interface.
type MockEpfPassbookOverallPfBalanceDao struct {
	ctrl     *gomock.Controller
	recorder *MockEpfPassbookOverallPfBalanceDaoMockRecorder
}

// MockEpfPassbookOverallPfBalanceDaoMockRecorder is the mock recorder for MockEpfPassbookOverallPfBalanceDao.
type MockEpfPassbookOverallPfBalanceDaoMockRecorder struct {
	mock *MockEpfPassbookOverallPfBalanceDao
}

// NewMockEpfPassbookOverallPfBalanceDao creates a new mock instance.
func NewMockEpfPassbookOverallPfBalanceDao(ctrl *gomock.Controller) *MockEpfPassbookOverallPfBalanceDao {
	mock := &MockEpfPassbookOverallPfBalanceDao{ctrl: ctrl}
	mock.recorder = &MockEpfPassbookOverallPfBalanceDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpfPassbookOverallPfBalanceDao) EXPECT() *MockEpfPassbookOverallPfBalanceDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEpfPassbookOverallPfBalanceDao) Create(ctx context.Context, data *model.EpfPassbookOverallPfBalance) (*model.EpfPassbookOverallPfBalance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, data)
	ret0, _ := ret[0].(*model.EpfPassbookOverallPfBalance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEpfPassbookOverallPfBalanceDaoMockRecorder) Create(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEpfPassbookOverallPfBalanceDao)(nil).Create), ctx, data)
}

// DeleteByActorId mocks base method.
func (m *MockEpfPassbookOverallPfBalanceDao) DeleteByActorId(ctx context.Context, actorId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorId", ctx, actorId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorId indicates an expected call of DeleteByActorId.
func (mr *MockEpfPassbookOverallPfBalanceDaoMockRecorder) DeleteByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorId", reflect.TypeOf((*MockEpfPassbookOverallPfBalanceDao)(nil).DeleteByActorId), ctx, actorId)
}

// MockEpfPassbookEstDetailsDao is a mock of EpfPassbookEstDetailsDao interface.
type MockEpfPassbookEstDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockEpfPassbookEstDetailsDaoMockRecorder
}

// MockEpfPassbookEstDetailsDaoMockRecorder is the mock recorder for MockEpfPassbookEstDetailsDao.
type MockEpfPassbookEstDetailsDaoMockRecorder struct {
	mock *MockEpfPassbookEstDetailsDao
}

// NewMockEpfPassbookEstDetailsDao creates a new mock instance.
func NewMockEpfPassbookEstDetailsDao(ctrl *gomock.Controller) *MockEpfPassbookEstDetailsDao {
	mock := &MockEpfPassbookEstDetailsDao{ctrl: ctrl}
	mock.recorder = &MockEpfPassbookEstDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpfPassbookEstDetailsDao) EXPECT() *MockEpfPassbookEstDetailsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEpfPassbookEstDetailsDao) Create(ctx context.Context, data *model.EpfPassbookEstDetails) (*model.EpfPassbookEstDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, data)
	ret0, _ := ret[0].(*model.EpfPassbookEstDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEpfPassbookEstDetailsDaoMockRecorder) Create(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEpfPassbookEstDetailsDao)(nil).Create), ctx, data)
}

// DeleteByActorId mocks base method.
func (m *MockEpfPassbookEstDetailsDao) DeleteByActorId(ctx context.Context, actorId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorId", ctx, actorId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorId indicates an expected call of DeleteByActorId.
func (mr *MockEpfPassbookEstDetailsDaoMockRecorder) DeleteByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorId", reflect.TypeOf((*MockEpfPassbookEstDetailsDao)(nil).DeleteByActorId), ctx, actorId)
}

// MockEpfPassbookTransactionsDao is a mock of EpfPassbookTransactionsDao interface.
type MockEpfPassbookTransactionsDao struct {
	ctrl     *gomock.Controller
	recorder *MockEpfPassbookTransactionsDaoMockRecorder
}

// MockEpfPassbookTransactionsDaoMockRecorder is the mock recorder for MockEpfPassbookTransactionsDao.
type MockEpfPassbookTransactionsDaoMockRecorder struct {
	mock *MockEpfPassbookTransactionsDao
}

// NewMockEpfPassbookTransactionsDao creates a new mock instance.
func NewMockEpfPassbookTransactionsDao(ctrl *gomock.Controller) *MockEpfPassbookTransactionsDao {
	mock := &MockEpfPassbookTransactionsDao{ctrl: ctrl}
	mock.recorder = &MockEpfPassbookTransactionsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpfPassbookTransactionsDao) EXPECT() *MockEpfPassbookTransactionsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEpfPassbookTransactionsDao) Create(ctx context.Context, data *model.EpfPassbookTransactions) (*model.EpfPassbookTransactions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, data)
	ret0, _ := ret[0].(*model.EpfPassbookTransactions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEpfPassbookTransactionsDaoMockRecorder) Create(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEpfPassbookTransactionsDao)(nil).Create), ctx, data)
}

// DeleteByActorId mocks base method.
func (m *MockEpfPassbookTransactionsDao) DeleteByActorId(ctx context.Context, actorId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorId", ctx, actorId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorId indicates an expected call of DeleteByActorId.
func (mr *MockEpfPassbookTransactionsDaoMockRecorder) DeleteByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorId", reflect.TypeOf((*MockEpfPassbookTransactionsDao)(nil).DeleteByActorId), ctx, actorId)
}

// MockEmployerPfHistoryDetailsDao is a mock of EmployerPfHistoryDetailsDao interface.
type MockEmployerPfHistoryDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockEmployerPfHistoryDetailsDaoMockRecorder
}

// MockEmployerPfHistoryDetailsDaoMockRecorder is the mock recorder for MockEmployerPfHistoryDetailsDao.
type MockEmployerPfHistoryDetailsDaoMockRecorder struct {
	mock *MockEmployerPfHistoryDetailsDao
}

// NewMockEmployerPfHistoryDetailsDao creates a new mock instance.
func NewMockEmployerPfHistoryDetailsDao(ctrl *gomock.Controller) *MockEmployerPfHistoryDetailsDao {
	mock := &MockEmployerPfHistoryDetailsDao{ctrl: ctrl}
	mock.recorder = &MockEmployerPfHistoryDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEmployerPfHistoryDetailsDao) EXPECT() *MockEmployerPfHistoryDetailsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEmployerPfHistoryDetailsDao) Create(ctx context.Context, req *model.EmployerPfHistoryDetails) (*model.EmployerPfHistoryDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, req)
	ret0, _ := ret[0].(*model.EmployerPfHistoryDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEmployerPfHistoryDetailsDaoMockRecorder) Create(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEmployerPfHistoryDetailsDao)(nil).Create), ctx, req)
}

// GetByCompanyId mocks base method.
func (m *MockEmployerPfHistoryDetailsDao) GetByCompanyId(ctx context.Context, companyId string) (*model.EmployerPfHistoryDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCompanyId", ctx, companyId)
	ret0, _ := ret[0].(*model.EmployerPfHistoryDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCompanyId indicates an expected call of GetByCompanyId.
func (mr *MockEmployerPfHistoryDetailsDaoMockRecorder) GetByCompanyId(ctx, companyId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCompanyId", reflect.TypeOf((*MockEmployerPfHistoryDetailsDao)(nil).GetByCompanyId), ctx, companyId)
}

// UpdateByCompanyId mocks base method.
func (m *MockEmployerPfHistoryDetailsDao) UpdateByCompanyId(ctx context.Context, req *model.EmployerPfHistoryDetails, updateMask []model.EmployerPfHistoryDetailsFieldMask) (*model.EmployerPfHistoryDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateByCompanyId", ctx, req, updateMask)
	ret0, _ := ret[0].(*model.EmployerPfHistoryDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateByCompanyId indicates an expected call of UpdateByCompanyId.
func (mr *MockEmployerPfHistoryDetailsDaoMockRecorder) UpdateByCompanyId(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateByCompanyId", reflect.TypeOf((*MockEmployerPfHistoryDetailsDao)(nil).UpdateByCompanyId), ctx, req, updateMask)
}

// Upsert mocks base method.
func (m *MockEmployerPfHistoryDetailsDao) Upsert(ctx context.Context, req *model.EmployerPfHistoryDetails, updateMask []model.EmployerPfHistoryDetailsFieldMask) (*model.EmployerPfHistoryDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, req, updateMask)
	ret0, _ := ret[0].(*model.EmployerPfHistoryDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upsert indicates an expected call of Upsert.
func (mr *MockEmployerPfHistoryDetailsDaoMockRecorder) Upsert(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockEmployerPfHistoryDetailsDao)(nil).Upsert), ctx, req, updateMask)
}

// MockEpfSmsDataDao is a mock of EpfSmsDataDao interface.
type MockEpfSmsDataDao struct {
	ctrl     *gomock.Controller
	recorder *MockEpfSmsDataDaoMockRecorder
}

// MockEpfSmsDataDaoMockRecorder is the mock recorder for MockEpfSmsDataDao.
type MockEpfSmsDataDaoMockRecorder struct {
	mock *MockEpfSmsDataDao
}

// NewMockEpfSmsDataDao creates a new mock instance.
func NewMockEpfSmsDataDao(ctrl *gomock.Controller) *MockEpfSmsDataDao {
	mock := &MockEpfSmsDataDao{ctrl: ctrl}
	mock.recorder = &MockEpfSmsDataDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEpfSmsDataDao) EXPECT() *MockEpfSmsDataDaoMockRecorder {
	return m.recorder
}

// CreateOrUpdateBulk mocks base method.
func (m *MockEpfSmsDataDao) CreateOrUpdateBulk(ctx context.Context, req []*model.EpfSmsData, updateMask []model.EpfSmsDataFieldMask) ([]*model.EpfSmsData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateBulk", ctx, req, updateMask)
	ret0, _ := ret[0].([]*model.EpfSmsData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdateBulk indicates an expected call of CreateOrUpdateBulk.
func (mr *MockEpfSmsDataDaoMockRecorder) CreateOrUpdateBulk(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateBulk", reflect.TypeOf((*MockEpfSmsDataDao)(nil).CreateOrUpdateBulk), ctx, req, updateMask)
}

// DeleteByActorId mocks base method.
func (m *MockEpfSmsDataDao) DeleteByActorId(ctx context.Context, actorId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorId", ctx, actorId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorId indicates an expected call of DeleteByActorId.
func (mr *MockEpfSmsDataDaoMockRecorder) DeleteByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorId", reflect.TypeOf((*MockEpfSmsDataDao)(nil).DeleteByActorId), ctx, actorId)
}

// GetAllPassbooksLatestDataByActorId mocks base method.
func (m *MockEpfSmsDataDao) GetAllPassbooksLatestDataByActorId(ctx context.Context, actorId string) ([]*model.EpfSmsData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllPassbooksLatestDataByActorId", ctx, actorId)
	ret0, _ := ret[0].([]*model.EpfSmsData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllPassbooksLatestDataByActorId indicates an expected call of GetAllPassbooksLatestDataByActorId.
func (mr *MockEpfSmsDataDaoMockRecorder) GetAllPassbooksLatestDataByActorId(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllPassbooksLatestDataByActorId", reflect.TypeOf((*MockEpfSmsDataDao)(nil).GetAllPassbooksLatestDataByActorId), ctx, actorId)
}
