package dao

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"

	"go.uber.org/zap"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	epfModel "github.com/epifi/gamma/api/insights/epf/model"
	"github.com/epifi/gamma/insights/epf/dao/model"
)

type EpfSmsDataDaoPGDB struct {
	db *gorm.DB
}

var epfSmsDataFieldMaskToColumnMap = map[epfModel.EpfSmsDataFieldMask]string{
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_ID:                "id",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_ACTOR_ID:          "actor_id",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_MASKED_UAN_NUMBER: "masked_uan_number",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_PASSBOOK_NUMBER:   "passbook_number",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_CREDIT_AMOUNT:     "credit_amount",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_PASSBOOK_BALANCE:  "passbook_balance",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_CREDIT_MONTH:      "credit_month",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_CREATED_AT:        "created_at",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_UPDATED_AT:        "updated_at",
	epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_DELETED_AT:        "deleted_at_unix",
}

func NewEpfSmsDataDaoPGDB(db cmdTypes.InsightsPGDB) *EpfSmsDataDaoPGDB {
	return &EpfSmsDataDaoPGDB{db: db}
}

func (e *EpfSmsDataDaoPGDB) CreateOrUpdateBulk(ctx context.Context, req []*epfModel.EpfSmsData, updateMask []epfModel.EpfSmsDataFieldMask) ([]*epfModel.EpfSmsData, error) {
	defer metric_util.TrackDuration("insights/epf/dao", "EpfSmsDataDaoPGDB", "CreateOrUpdateBulk", time.Now())
	if len(req) == 0 || len(req) > 1000 {
		return nil, fmt.Errorf("invalid request length %v", len(req))
	}

	epfSmsDataModels := make([]*model.EpfSmsData, 0, len(req))
	for _, protoModel := range req {
		if err := e.validateRequest(protoModel); err != nil {
			return nil, fmt.Errorf("failed to validate epf sms data: %w", err)
		}
		epfSmsDataModels = append(epfSmsDataModels, model.NewEpfSmsData(protoModel))
	}

	cols, err := e.convertFieldMaskToColumns(updateMask)
	if err != nil {
		return nil, fmt.Errorf("failed to get col names from field mask: %w", err)
	}
	db := gormctxv2.FromContextOrDefault(ctx, e.db)
	resp := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: epfSmsDataFieldMaskToColumnMap[epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_ACTOR_ID]},
			{Name: epfSmsDataFieldMaskToColumnMap[epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_PASSBOOK_NUMBER]},
			{Name: epfSmsDataFieldMaskToColumnMap[epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_CREDIT_MONTH]},
			{Name: epfSmsDataFieldMaskToColumnMap[epfModel.EpfSmsDataFieldMask_EPF_SMS_DATA_FIELD_MASK_DELETED_AT]},
		},
		TargetWhere: clause.Where{
			Exprs: []clause.Expression{
				clause.Expr{
					SQL: "deleted_at_unix = 0",
				},
			},
		},
		DoUpdates: clause.AssignmentColumns(cols),
	}).Create(epfSmsDataModels)

	if resp.Error != nil {
		return nil, fmt.Errorf("unable to bulk upsert DB entries for epfSmsDataModels: %w", resp.Error)
	}

	result := make([]*epfModel.EpfSmsData, 0, len(epfSmsDataModels))
	for _, dbModel := range epfSmsDataModels {
		result = append(result, dbModel.ToProto())
	}

	return result, nil
}

func (e *EpfSmsDataDaoPGDB) GetAllPassbooksLatestDataByActorId(ctx context.Context, actorId string) ([]*epfModel.EpfSmsData, error) {
	defer metric_util.TrackDuration("insights/epf/dao", "EpfSmsDataDaoPGDB", "GetAllPassbooksLatestDataByActorId", time.Now())
	if actorId == "" {
		return nil, fmt.Errorf("actorId cannot be empty for get operation")
	}
	db := gormctxv2.FromContextOrDefault(ctx, e.db)

	// Step 1: Get all distinct passbook numbers for this actor
	var passbookNumbers []string
	if err := db.Model(&model.EpfSmsData{}).Distinct("passbook_number").Where("actor_id = ? AND deleted_at_unix = 0", actorId).
		Pluck("passbook_number", &passbookNumbers).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch distinct passbook numbers for actor: %w", err)
	}

	if len(passbookNumbers) == 0 {
		return nil, fmt.Errorf("no epf sms data found for given actor id: %w", epifierrors.ErrRecordNotFound)
	}

	result := make([]*epfModel.EpfSmsData, 0, len(passbookNumbers))

	// Step 2: For each passbook number, get the latest record by credit_month
	for _, passbookNumber := range passbookNumbers {
		epfSmsDataModel := &model.EpfSmsData{}
		if err := db.Where("actor_id = ? AND passbook_number = ? AND deleted_at_unix = 0", actorId, passbookNumber).
			Order("credit_month DESC, created_at DESC").Limit(1).First(epfSmsDataModel).Error; err != nil {
			return nil, fmt.Errorf("unable to get DB entry for actorId and passbookNumber: %w", err)
		}
		result = append(result, epfSmsDataModel.ToProto())
	}
	return result, nil
}

func (e *EpfSmsDataDaoPGDB) validateRequest(req *epfModel.EpfSmsData) error {
	if req.GetActorId() == "" {
		return fmt.Errorf("actor id cannot be empty")
	}
	if req.GetMaskedUanNumber() == "" {
		return fmt.Errorf("masked UAN number cannot be empty")
	}
	if req.GetPassbookNumber() == "" {
		return fmt.Errorf("passbook number cannot be empty")
	}
	if req.GetCreditMonth() == nil {
		return fmt.Errorf("credit month cannot be empty")
	}
	if req.GetCreditAmount() == nil {
		return fmt.Errorf("credit amount cannot be empty")
	}
	if req.GetPassbookBalance() == nil {
		return fmt.Errorf("passbook balance cannot be empty")
	}
	return nil
}

func (e *EpfSmsDataDaoPGDB) convertFieldMaskToColumns(fieldMask []epfModel.EpfSmsDataFieldMask) ([]string, error) {
	if len(fieldMask) == 0 {
		return nil, fmt.Errorf("field mask cannot be empty")
	}
	var cols []string
	for _, field := range lo.Uniq(fieldMask) {
		col, found := epfSmsDataFieldMaskToColumnMap[field]
		if !found {
			return nil, fmt.Errorf("%s mapping not found in fieldMask to col name map", field.String())
		}
		cols = append(cols, col)
	}
	return lo.Uniq(cols), nil
}

func (e *EpfSmsDataDaoPGDB) DeleteByActorId(ctx context.Context, actorId string) error {
	defer metric_util.TrackDuration("insights/epf/dao", "EpfSmsDataDaoPGDB", "DeleteByActorId", time.Now())

	curTime := time.Now()
	db := gormctxv2.FromContextOrDefault(ctx, e.db)
	result := db.Model(&model.EpfSmsData{}).
		Where("actor_id = ? AND deleted_at_unix = 0", actorId).
		Updates(map[string]interface{}{
			"deleted_at_unix": curTime.Unix(),
			"updated_at":      curTime,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to soft delete epf sms data: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		logger.Info(ctx, "no epf sms data found for given actor id", zap.String("actor_id", actorId))
		return epifierrors.ErrRecordNotFound
	}

	return nil
}
