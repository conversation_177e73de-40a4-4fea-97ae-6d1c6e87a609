#!/bin/bash

binarymaker()
{
    make docker-build target="$target"
    #aws ecr get-login-password --region ap-south-1 | docker login --password-stdin 240673475153.dkr.ecr.ap-south-1.amazonaws.com --username AWS
    #docker tag $target:latest 240673475153.dkr.ecr.ap-south-1.amazonaws.com/$target:latest
    #docker push 240673475153.dkr.ecr.ap-south-1.amazonaws.com/$target:latest
    docker create -it --name copy_artifact_"$target" "$target":latest
    mkdir -p /var/lib/jenkins/artifacts/staging/"$target"
    docker cp copy_artifact_"$target":/my_bin /var/lib/jenkins/artifacts/staging/"$target"/"$target"_bin
    docker cp copy_artifact_"$target":/config /var/lib/jenkins/artifacts/staging/"$target"/
    docker rm -f copy_artifact_"$target"
    export ARTIFACT_PATH=/var/lib/jenkins/artifacts/staging/"$target"
    ls -l /var/lib/jenkins/artifacts/staging/"$target"
}

#Mainfunction
#TODO(muskaan): Change this for loop to parse servers list from gamma repo cmd folder or so, instead of hard coding list here..(will get back once we have finalised changes in gamma/cmd folder).
#TODO(muskaan): make this for loop run parallel
declare -a servers=("actor" "auth" "card" "comms" "frontend" "kyc" "order" "paymentinstrument" "rewards" "savings" "search" "simulator" "timeline" "user" "vendorgateway" "vendornotification")
for target in "${servers[@]}"
do
   binarymaker "$target"
done
#wait for all processes to finish up
wait
