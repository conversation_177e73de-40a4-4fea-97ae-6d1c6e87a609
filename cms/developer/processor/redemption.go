package processor

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/api/cms/developer"

	"github.com/epifi/gamma/cms/dao"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/be-common/pkg/logger"
)

type RedemptionProcessor struct {
	redemptionDao dao.RedemptionDao
}

func NewRedemptionProcessor(redemptionDao dao.RedemptionDao) *RedemptionProcessor {
	return &RedemptionProcessor{redemptionDao: redemptionDao}
}

func (r *RedemptionProcessor) FetchParamList(ctx context.Context, entity developer.CmsEntity) ([]*db_state.ParameterMeta, error) {

	paramList := []*db_state.ParameterMeta{
		{
			Name:            REFERENCE_ID,
			Label:           "Reference Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (r *RedemptionProcessor) FetchData(ctx context.Context, entity developer.CmsEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", ErrNilFilter
	}

	var referenceId string

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case REFERENCE_ID:
			referenceId = filter.GetStringValue()

		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	// fetch redemption by id
	redemption, err := r.redemptionDao.GetRedemptionByReferenceId(ctx, referenceId)
	if err != nil {
		logger.Error(ctx, "error in GetRedemptionByReferenceId", zap.String("referenceId", referenceId))
		return "", fmt.Errorf("error in GetRedemptionByReferenceId, err: %w", err)
	}

	marshalledRes, err := protojson.Marshal(redemption)
	if err != nil {
		logger.Error(ctx, "error marshalling redemption proto", zap.Any("redemption", redemption))
		return "", fmt.Errorf("error marshalling redemption proto, err: %w", err)
	}
	return string(marshalledRes), nil
}
