package esign

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	esignPb "github.com/epifi/gamma/api/docs/esign"
	eSignTypesPb "github.com/epifi/gamma/api/typesv2/esign"
	eSignVgPb "github.com/epifi/gamma/api/vendorgateway/esign"
	"github.com/epifi/gamma/docs/config"
	"github.com/epifi/gamma/docs/esign/dao"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

type Service struct {
	esignPb.UnimplementedESignServer
	eSignRequestsDao dao.ESignRequestsDao
	esignVgClient    eSignVgPb.ESignClient
	conf             *config.Config
	s3Client         s3.S3Client
}

func NewService(
	eSignRequestsDao dao.ESignRequestsDao,
	esignVgClient eSignVgPb.ESignClient,
	conf *config.Config,
	s3client s3.S3Client,
) *Service {
	return &Service{
		eSignRequestsDao: eSignRequestsDao,
		esignVgClient:    esignVgClient,
		conf:             conf,
		s3Client:         s3client,
	}
}

func (s *Service) InitiateESign(ctx context.Context, req *esignPb.InitiateESignRequest) (*esignPb.InitiateESignResponse, error) {
	res := &esignPb.InitiateESignResponse{}
	irn := req.GetInternalReferenceNumber()
	if irn == "" {
		irn = uuid.New().String()
	}

	eSignRequest, err := s.eSignRequestsDao.GetByClientReqIdAndClient(ctx, req.GetClientRequestId(), req.GetClient())
	if err != nil {
		if err == epifierrors.ErrRecordNotFound {
			eSignRequest, err = s.eSignRequestsDao.Create(ctx, &esignPb.EsignRequest{
				ActorId:         req.GetActorId(),
				ClientRequestId: req.GetClientRequestId(),
				Client:          req.GetClient(),
				Irn:             irn,
				Status:          esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_CREATED,
				Vendor:          req.GetVendor(),
				TemplateType:    req.GetTemplateType(),
				TemplateOption:  req.GetTemplateOption(),
			})
			if err != nil {
				logger.Error(ctx, "Unable to create entry in esign_requests table", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		} else {
			logger.Error(ctx, "Unable to GetByClientReqIdAndClient from esign_requests table", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	var vgRes *eSignVgPb.CreateESignResponse
	if eSignRequest.GetSignUrl() != "" {
		res.Status = rpcPb.StatusOk()
		res.SignUrl = eSignRequest.GetSignUrl()
		res.ExpiryAt = eSignRequest.GetExpiryAt()
		return res, nil
	}

	vgRes, err = s.initiateEsignWithVendorGateway(ctx, eSignRequest)
	if err != nil {
		logger.Error(ctx, "Unable to send request to VG to Create ESign", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	eSignRequest.VendorDocumentId = vgRes.GetData().GetDocumentId()
	eSignRequest.SignUrl = vgRes.GetData().GetInvitee().GetSignUrl()
	eSignRequest.ExpiryAt = vgRes.GetData().GetInvitee().GetExpiryTimestamp()

	err = s.eSignRequestsDao.Update(ctx, eSignRequest, []esignPb.EsignRequestFieldMask{
		esignPb.EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_VENDOR_DOCUMENT_ID,
		esignPb.EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_SIGN_URL,
		esignPb.EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_EXPIRY_AT,
	})
	if err != nil {
		logger.Error(ctx, "Unable to update esign_request in DB", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.Status = rpcPb.StatusOk()
	res.SignUrl = eSignRequest.GetSignUrl()
	res.ExpiryAt = eSignRequest.GetExpiryAt()
	return res, nil
}

func (s *Service) initiateEsignWithVendorGateway(ctx context.Context, req *esignPb.EsignRequest) (*eSignVgPb.CreateESignResponse, error) {
	client, ok := eSignClientToVgClientMapping[req.GetClient()]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("didnt find mapping for given client %v", client))
		return nil, fmt.Errorf("mapping missing")
	}
	vgRes, err := s.esignVgClient.CreateESign(ctx, &eSignVgPb.CreateESignRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: eSignVendorToVgVendorMapping[req.GetVendor()],
		},
		InternalReferenceNumber: req.GetIrn(),
		Template: &eSignTypesPb.Template{
			Type:    req.GetTemplateType(),
			Options: req.GetTemplateOption(),
		},
		Client: client,
	})
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		return nil, err
	}
	return vgRes, nil
}

func (s *Service) CheckESignStatus(ctx context.Context, req *esignPb.CheckESignStatusRequest) (*esignPb.CheckESignStatusResponse, error) {
	res := &esignPb.CheckESignStatusResponse{}

	eSignRequest, err := s.eSignRequestsDao.GetByClientReqIdAndClient(ctx, req.GetClientRequestId(), req.GetClient())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "Record Not Found", zap.Error(err))
			// TODO(@prasoon): Move logic from rpc status to defining new status as param for check response
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "Unable to Get ESign Request By ClientReqId And Client from DB", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if eSignRequest.GetStatus() == esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_FAILED ||
		eSignRequest.GetStatus() == esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_SUCCESS {
		res.SignUrl = eSignRequest.GetSignUrl()
		res.SignedAt = eSignRequest.GetSignedAt()
		res.ESignStatus = eSignRequest.GetStatus()
		res.Status = rpcPb.StatusOk()
		return res, nil
	}

	client, ok := eSignClientToVgClientMapping[req.GetClient()]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("didnt find mapping for given client %v", client))
		return &esignPb.CheckESignStatusResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("mapping missing"),
		}, nil
	}

	if eSignRequest.GetVendorDocumentId() == "" {
		logger.Error(ctx, "document id is empty in db")
		return &esignPb.CheckESignStatusResponse{
			Status: rpcPb.StatusFailedPrecondition(),
		}, nil
	}

	vgRes, err := s.esignVgClient.CheckESignStatus(ctx, &eSignVgPb.CheckESignStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: eSignVendorToVgVendorMapping[eSignRequest.GetVendor()],
		},
		DocumentId: eSignRequest.GetVendorDocumentId(),
		Client:     client,
	})
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		if isDocumentNotFoundWithVendor(vgRes.GetMessages()) {
			// marking e-sign request status as FAILED as this can happen when the sign document is
			// expired and deleted at the vendor's end but the request entry is present at our db.
			res.Status = rpcPb.StatusOk()
			res.ESignStatus = esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_FAILED
			return res, nil
		}

		logger.Error(ctx, "Unable to send request to VG to Check ESign Status", zap.Error(te))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	updateMask := []esignPb.EsignRequestFieldMask{
		esignPb.EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_STATUS,
	}
	switch vgRes.GetData().GetEsignDetails().GetStatus() {
	case eSignVgPb.EsignStatus_ESIGN_STATUS_EXPIRED,
		eSignVgPb.EsignStatus_ESIGN_STATUS_REJECTED:
		eSignRequest.Status = esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_FAILED
	case eSignVgPb.EsignStatus_ESIGN_STATUS_SIGNED:
		eSignRequest.Status = esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_SUCCESS
		eSignRequest.SignedAt = timestampPb.New(time.Now().In(datetime.IST))
		updateMask = append(updateMask, esignPb.EsignRequestFieldMask_ESIGN_REQUEST_FIELD_MASK_SIGNED_AT)
	case eSignVgPb.EsignStatus_ESIGN_STATUS_PENDING_BANK:
		eSignRequest.Status = esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING_AT_BANK
	default:
		eSignRequest.Status = esignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING
	}

	err = s.eSignRequestsDao.Update(ctx, eSignRequest, updateMask)
	if err != nil {
		logger.Error(ctx, "Unable to update esign_request in DB", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.SignUrl = eSignRequest.GetSignUrl()
	res.ESignStatus = eSignRequest.GetStatus()
	res.SignedAt = eSignRequest.GetSignedAt()
	res.Status = rpcPb.StatusOk()
	return res, nil
}

var eSignVendorToVgVendorMapping = map[esignPb.EsignRequestVendor]commonvgpb.Vendor{
	esignPb.EsignRequestVendor_ESIGN_REQUEST_VENDOR_UNSPECIFIED: commonvgpb.Vendor_VENDOR_UNSPECIFIED,
	esignPb.EsignRequestVendor_ESIGN_REQUEST_VENDOR_LEEGALITY:   commonvgpb.Vendor_LEEGALITY,
}

var eSignClientToVgClientMapping = map[esignPb.EsignRequestClient]eSignVgPb.EsignRequestClient{
	esignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_UNSPECIFIED:          eSignVgPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_UNSPECIFIED,
	esignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN:    eSignVgPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN,
	esignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT:      eSignVgPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT,
	esignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2: eSignVgPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2,
}

func isDocumentNotFoundWithVendor(messages []*eSignVgPb.CheckESignStatusResponse_Messages) bool {
	for _, message := range messages {
		if message.GetCode() == eSignVgPb.CheckESignStatusResponse_CODE_DOCUMENT_NOT_FOUND {
			return true
		}
	}

	return false
}
