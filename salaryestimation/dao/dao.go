package dao

import (
	"context"

	"github.com/epifi/gamma/api/salaryestimation"
)

//go:generate mockgen -source=dao.go -destination=./mocks/mock_dao.go -package=mocks
//go:generate dao_metrics_gen .

// SalaryEstimationAttemptDao defines all the DAO operations for salary estimation attempts
type SalaryEstimationAttemptDao interface {
	// Create creates a new salary estimation attempt record
	Create(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.SalaryEstimationAttempt, error)

	// Update updates an existing salary estimation attempt record
	Update(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, fieldMasks []salaryestimation.SalaryEstimationAttemptFieldMask) error

	// GetByID retrieves a salary estimation attempt by its ID
	GetByID(ctx context.Context, id string) (*salaryestimation.SalaryEstimationAttempt, error)

	// GetByClientReqID retrieves a salary estimation attempt by client request ID
	GetByClientReqID(ctx context.Context, clientReqID string) (*salaryestimation.SalaryEstimationAttempt, error)
}
