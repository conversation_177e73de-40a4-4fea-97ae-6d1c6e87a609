// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	salaryestimation "github.com/epifi/gamma/api/salaryestimation"
	gomock "github.com/golang/mock/gomock"
)

// MockSalaryEstimationAttemptDao is a mock of SalaryEstimationAttemptDao interface.
type MockSalaryEstimationAttemptDao struct {
	ctrl     *gomock.Controller
	recorder *MockSalaryEstimationAttemptDaoMockRecorder
}

// MockSalaryEstimationAttemptDaoMockRecorder is the mock recorder for MockSalaryEstimationAttemptDao.
type MockSalaryEstimationAttemptDaoMockRecorder struct {
	mock *MockSalaryEstimationAttemptDao
}

// NewMockSalaryEstimationAttemptDao creates a new mock instance.
func NewMockSalaryEstimationAttemptDao(ctrl *gomock.Controller) *MockSalaryEstimationAttemptDao {
	mock := &MockSalaryEstimationAttemptDao{ctrl: ctrl}
	mock.recorder = &MockSalaryEstimationAttemptDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSalaryEstimationAttemptDao) EXPECT() *MockSalaryEstimationAttemptDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockSalaryEstimationAttemptDao) Create(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.SalaryEstimationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, attempt)
	ret0, _ := ret[0].(*salaryestimation.SalaryEstimationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockSalaryEstimationAttemptDaoMockRecorder) Create(ctx, attempt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockSalaryEstimationAttemptDao)(nil).Create), ctx, attempt)
}

// GetByClientReqID mocks base method.
func (m *MockSalaryEstimationAttemptDao) GetByClientReqID(ctx context.Context, clientReqID string) (*salaryestimation.SalaryEstimationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientReqID", ctx, clientReqID)
	ret0, _ := ret[0].(*salaryestimation.SalaryEstimationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientReqID indicates an expected call of GetByClientReqID.
func (mr *MockSalaryEstimationAttemptDaoMockRecorder) GetByClientReqID(ctx, clientReqID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientReqID", reflect.TypeOf((*MockSalaryEstimationAttemptDao)(nil).GetByClientReqID), ctx, clientReqID)
}

// GetByID mocks base method.
func (m *MockSalaryEstimationAttemptDao) GetByID(ctx context.Context, id string) (*salaryestimation.SalaryEstimationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*salaryestimation.SalaryEstimationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockSalaryEstimationAttemptDaoMockRecorder) GetByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockSalaryEstimationAttemptDao)(nil).GetByID), ctx, id)
}

// Update mocks base method.
func (m *MockSalaryEstimationAttemptDao) Update(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt, fieldMasks []salaryestimation.SalaryEstimationAttemptFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, attempt, fieldMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockSalaryEstimationAttemptDaoMockRecorder) Update(ctx, attempt, fieldMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockSalaryEstimationAttemptDao)(nil).Update), ctx, attempt, fieldMasks)
}
