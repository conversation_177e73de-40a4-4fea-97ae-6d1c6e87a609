package impl

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/be-common/pkg/logger"
	testPkg "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/salaryestimation/config/genconf"
	"github.com/epifi/gamma/salaryestimation/test"
)

var dbInstancePool testPkg.DbInstancePool
var conf *genconf.Config

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	_, conf, teardown = test.InitTestServer()
	testLogger := testPkg.NewZapLogger(logger.Log)
	dbInstancePool = testPkg.NewPgdbDbInstancePool(testLogger, conf.FeatureEngineeringDb(), 1)
	exitCode := m.Run()
	dbInstancePool.Cleanup(testLogger)
	teardown()
	os.Exit(exitCode)
}
