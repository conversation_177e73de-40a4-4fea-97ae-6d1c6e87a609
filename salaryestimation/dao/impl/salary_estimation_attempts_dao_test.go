package impl

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/salaryestimation/dao"
)

var (
	affectedTestTables = []string{"salary_estimation_attempts"}
)

func getSalaryEstimationAttemptDao(t *testing.T) (dao.SalaryEstimationAttemptDao, func()) {
	dbInstance, dbInstanceRelease := dbInstancePool.GetDbInstance(t)
	salaryEstimationAttemptDao := NewSalaryEstimationAttemptDao(dbInstance.GetConnection())
	return salaryEstimationAttemptDao, func() { dbInstanceRelease(affectedTestTables) }
}

func TestSalaryEstimationAttemptDao_Create(t *testing.T) {
	t.<PERSON>l()
	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx     context.Context
		attempt *salaryestimation.SalaryEstimationAttempt
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
	}{
		{
			name: "successful creation",
			args: args{
				ctx: context.Background(),
				attempt: &salaryestimation.SalaryEstimationAttempt{
					ClientReqId: "test-client-req-id-1",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     "test-actor-id-1",
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
				},
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "test-client-req-id-1",
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     "test-actor-id-1",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := salaryEstimationAttemptDao.Create(tt.args.ctx, tt.args.attempt)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(salaryestimation.SalaryEstimationAttempt{}, "Id", "CreatedAt", "UpdatedAt"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}

func TestSalaryEstimationAttemptDao_GetByClientReqID(t *testing.T) {
	t.Parallel()

	salaryEstimationAttemptDao, releaseDao := getSalaryEstimationAttemptDao(t)
	defer releaseDao()

	type args struct {
		ctx         context.Context
		clientReqID string
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryestimation.SalaryEstimationAttempt
		wantErr bool
		setup   func() *salaryestimation.SalaryEstimationAttempt
	}{
		{
			name: "successful retrieval",
			args: args{
				ctx:         context.Background(),
				clientReqID: "unique-client-req-id",
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "unique-client-req-id",
				Source:      salaryestimation.Source_SOURCE_AA,
				ActorId:     "test-actor-id",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
			},
			wantErr: false,
			setup: func() *salaryestimation.SalaryEstimationAttempt {
				attempt := &salaryestimation.SalaryEstimationAttempt{
					ClientReqId: "unique-client-req-id",
					Source:      salaryestimation.Source_SOURCE_AA,
					ActorId:     "test-actor-id",
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_INITIATED,
				}
				created, err := salaryEstimationAttemptDao.Create(context.Background(), attempt)
				require.NoError(t, err)
				return created
			},
		},
		{
			name: "not found",
			args: args{
				ctx:         context.Background(),
				clientReqID: "non-existent-client-req-id",
			},
			want:    nil,
			wantErr: true,
			setup:   func() *salaryestimation.SalaryEstimationAttempt { return nil },
		},
		{
			name: "successful retrieval with unspecified source",
			args: args{
				ctx:         context.Background(),
				clientReqID: "unspecified-client-req-id",
			},
			want: &salaryestimation.SalaryEstimationAttempt{
				ClientReqId: "unspecified-client-req-id",
				Source:      salaryestimation.Source_SOURCE_UNSPECIFIED,
				ActorId:     "test-actor-id-2",
				Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED,
			},
			wantErr: false,
			setup: func() *salaryestimation.SalaryEstimationAttempt {
				attempt := &salaryestimation.SalaryEstimationAttempt{
					ClientReqId: "unspecified-client-req-id",
					Source:      salaryestimation.Source_SOURCE_UNSPECIFIED,
					ActorId:     "test-actor-id-2",
					Status:      salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED,
				}
				created, err := salaryEstimationAttemptDao.Create(context.Background(), attempt)
				require.NoError(t, err)
				return created
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_ = tt.setup()
			got, err := salaryEstimationAttemptDao.GetByClientReqID(tt.args.ctx, tt.args.clientReqID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientReqID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				cmpopts.IgnoreFields(salaryestimation.SalaryEstimationAttempt{}, "Id", "CreatedAt", "UpdatedAt"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetByClientReqID() got = %v,\n want %v", got, tt.want)
			}
		})
	}
}
