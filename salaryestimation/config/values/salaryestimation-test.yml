Application:
  Environment: "test"
  Name: "salaryestimation"
  ServerName: "central-growth"

FeatureEngineeringDb:
  DbType: "PGDB"
  AppName: "salaryestimation"
  StatementTimeout: 5m
  Name: "feature_engineering_test"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"
