env: prod
app: cx-worker
image: 854002675954.dkr.ecr.ap-south-1.amazonaws.com/cx-worker
replicas: 1
autoPromotionEnabled: false
memoryLimits: 2000M
iamRole: arn:aws:iam::854002675954:role/prod-cx-worker
appPort: 9090
prometheus: true
metricsPort: 9090

resources:
  requests:
    memory: 500M
    cpu: 0.2
  limits:
    memory: 1Gi
    cpu: 0.5

autoscaling:
  cpu: 70
  memory: 70
  minReplicas: 2
  maxReplicas: 10

svc:  
  green:
    port: 9090
    type: NodePort
    annotations:
      service.beta.kubernetes.io/aws-load-balancer-type: "internal"
  blue:
    port: 9090
    type: ClusterIP

probes:
  liveness:
    url: /_health/liveness
    delays:
      initialDelaySeconds: 45
      periodSeconds: 20
  readiness:
    url: /_health/readiness
    delays:
      initialDelaySeconds: 45
      periodSeconds: 20

podEnv:
  ENVIRONMENT: prod
  TEMPORAL_HOST: "temporal-frontend-headless.temporal-v1-18-5.svc.cluster.local"
  TEMPORAL_SECURE_CONNECTION: "false"
  TEMPORAL_PORT: 7233
