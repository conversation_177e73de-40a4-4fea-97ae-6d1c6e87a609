package reminder_builder

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	mocks2 "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	txnaggregatesMocks "github.com/epifi/gamma/api/analyser/txnaggregates/mocks"
	"github.com/epifi/gamma/api/budgeting/reminder"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/budgeting/reminder/meta"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	managerPb "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/budgeting/reminder/dao"
	"github.com/epifi/gamma/budgeting/reminder/utils"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
)

type mockGetTopCategories struct {
	resp *txnaggregates.GetTopCategoriesResponse
	err  error
}

type mockGetAccount struct {
	resp *savings.GetAccountResponse
	err  error
}

type mockGetActorById struct {
	resp *actor.GetActorByIdResponse
	err  error
}

func TestConstructTopSectionSubHeading(t *testing.T) {
	logger.Init("test")
	testCases := []struct {
		name           string
		triggeredCount int32
		isErrResp      bool
		expected       string
	}{
		{
			name:           "No trigger count and no error response",
			triggeredCount: 0,
			isErrResp:      false,
			expected:       "NOT YET INITIATED THIS MONTH",
		},
		{
			name:           "Has trigger count and no error response",
			triggeredCount: 5,
			isErrResp:      false,
			expected:       "INITIATED 5 TIMES THIS MONTH",
		},
		{
			name:           "Has 1 trigger count and no error response",
			triggeredCount: 1,
			isErrResp:      false,
			expected:       "INITIATED 1 TIME THIS MONTH",
		},
		{
			name:           "No trigger count and has error response",
			triggeredCount: 0,
			isErrResp:      true,
			expected:       "",
		},
		{
			name:           "Has trigger count and has error response",
			triggeredCount: 10,
			isErrResp:      true,
			expected:       "",
		},
	}

	for _, tc := range testCases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			result := constructTopSectionSubHeading(tc.triggeredCount, tc.isErrResp)
			if result != tc.expected {
				t.Errorf("Unexpected result; expected: %v, got: %v", tc.expected, result)
			}
		})
	}
}

func TestCategoryReminder_BuildParams(t *testing.T) {
	logger.Init("test")
	type fields struct {
		config *reminder.CategoryReminderConfig
	}
	type args struct {
		request *BuildParamsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *managerPb.RuleParamValues
		wantErr bool
	}{
		{
			name: "params found",
			fields: fields{
				config: &reminder.CategoryReminderConfig{
					Amount: &types.Money{
						Units: 100,
					},
					Frequency: reminder.ReminderFrequency_MONTH,
					Category:  categorizer.DisplayCategory_BONUS,
				},
			},
			args: args{
				request: &BuildParamsRequest{
					CommMediums: []comms.Medium{
						comms.Medium_SMS,
					},
				},
			},
			want: &managerPb.RuleParamValues{
				RuleParamValues: map[string]*managerPb.Value{
					utils.ConfiguredDuration: {
						Value: &managerPb.Value_TimePeriod{
							TimePeriod: managerPb.TimePeriod_MONTH,
						},
					},
					utils.ConfiguredAmount: {
						Value: &managerPb.Value_MoneyVal{
							MoneyVal: &types.Money{
								Units: 100,
							},
						},
					},
					utils.ConfiguredCategory: {
						Value: &managerPb.Value_StrVal{
							StrVal: "bonus",
						},
					},
					utils.CommsMedium: {
						Value: &managerPb.Value_CommsMediums{
							CommsMediums: &managerPb.CommsMedium{
								MediumList: []comms.Medium{
									comms.Medium_SMS,
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			a := &CategoryReminder{
				config: tt.fields.config,
			}
			got, err := a.BuildParams(tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildParams() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BuildParams() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCategoryReminder_BuildReminderConditionCorrectScreen(t *testing.T) {
	logger.Init("test")
	type fields struct {
		config            *reminder.CategoryReminderConfig
		txnAggregates     txnaggregates.TxnAggregatesClient
		actorClient       actor.ActorClient
		savingsClient     savings.SavingsClient
		ruleManagerClient managerPb.RuleManagerClient
		reminderLogDao    dao.ReminderLogDao
	}
	type args struct {
		ctx     context.Context
		request *BuildReminderConditionCorrectScreenRequest
	}
	tests := []struct {
		name                 string
		fields               fields
		args                 args
		want                 *BuildReminderConditionCorrectScreenResponse
		wantErr              bool
		mockGetTopCategories *mockGetTopCategories
		mockGetAccount       *mockGetAccount
		mockGetActorById     *mockGetActorById
	}{
		{
			name: "configured amount is greater than agg amount",
			args: args{
				ctx: context.Background(),
				request: &BuildReminderConditionCorrectScreenRequest{
					ActorId:        "actor-id-1",
					SubscriptionId: "sub-id-1",
					RuleParams: &managerPb.RuleParamValues{
						RuleParamValues: map[string]*managerPb.Value{
							utils.ConfiguredCategory: &managerPb.Value{
								Value: &managerPb.Value_StrVal{
									StrVal: "SHOPPING",
								},
							},
							utils.ConfiguredAmount: &managerPb.Value{
								Value: &managerPb.Value_MoneyVal{
									MoneyVal: &types.Money{
										CurrencyCode: "INR",
										Units:        1500,
									},
								},
							},
						},
					},
				},
			},
			mockGetTopCategories: &mockGetTopCategories{
				resp: &txnaggregates.GetTopCategoriesResponse{
					Status: rpc.StatusOk(),
					CategoryAggregates: []*txnaggregates.Aggregate{
						{
							Category: "SHOPPING",
							SumAmount: &types.Money{
								CurrencyCode: "INR",
								Units:        1459,
							},
						},
					},
				},
			},
			mockGetAccount: &mockGetAccount{
				resp: &savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-id-1",
					},
				},
			},
			mockGetActorById: &mockGetActorById{
				resp: &actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actor-id-1",
						EntityId: "user-id-1",
					},
				},
			},
		},
		{
			name: "configured amount is less than agg amount",
			args: args{
				ctx: context.Background(),
				request: &BuildReminderConditionCorrectScreenRequest{
					ActorId:        "actor-id-1",
					SubscriptionId: "sub-id-1",
					RuleParams: &managerPb.RuleParamValues{
						RuleParamValues: map[string]*managerPb.Value{
							utils.ConfiguredCategory: &managerPb.Value{
								Value: &managerPb.Value_StrVal{
									StrVal: "SHOPPING",
								},
							},
							utils.ConfiguredAmount: &managerPb.Value{
								Value: &managerPb.Value_MoneyVal{
									MoneyVal: &types.Money{
										CurrencyCode: "INR",
										Units:        1200,
									},
								},
							},
						},
					},
				},
			},
			mockGetTopCategories: &mockGetTopCategories{
				resp: &txnaggregates.GetTopCategoriesResponse{
					Status: rpc.StatusOk(),
					CategoryAggregates: []*txnaggregates.Aggregate{
						{
							Category: "SHOPPING",
							SumAmount: &types.Money{
								CurrencyCode: "INR",
								Units:        1459,
							},
						},
					},
				},
			},
			mockGetAccount: &mockGetAccount{
				resp: &savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-id-1",
					},
				},
			},
			mockGetActorById: &mockGetActorById{
				resp: &actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &typesPb.Actor{
						Id:       "actor-id-1",
						EntityId: "user-id-1",
					},
				},
			},
			want: &BuildReminderConditionCorrectScreenResponse{
				Screen: &reminder.PostReminderScreen{
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: ExceedSpendsTitle,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
						},
					},
					PrimaryCta: &reminder.PostReminderScreenCta{
						CtaText: &commontypes.Text{
							DisplayValue: &commontypes.Text_Html{
								Html: "<font color='#FFFFFF'>Done</font>",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						BgColor: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#00B899",
							},
						},
					},
					SecondaryCta: &reminder.PostReminderScreenCta{
						CtaText: &commontypes.Text{
							DisplayValue: &commontypes.Text_Html{
								Html: "<font color='#00B899'>Edit Amount</font>",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_REMINDERS_CONFIG_SCREEN,
							ScreenOptions: &deeplinkPb.Deeplink_RemindersConfigScreenOptions{
								RemindersConfigScreenOptions: &deeplinkPb.RemindersConfigScreenOptions{
									ConfigType:   meta.ReminderConfigType_CONFIG_UPDATE,
									ReminderType: meta.ReminderType_CATEGORY_SPENDS,
									ConfigFlow: &deeplinkPb.RemindersConfigScreenOptions_UpdateConfig{
										UpdateConfig: &deeplinkPb.UpdateReminderConfig{
											ReminderSubscriptionId: "sub-id-1",
										},
									},
								},
							},
						},
						BgColor: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#F7F9FA",
							},
						},
					},
					PrimaryImg: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/quick-link-icons/reminder_done.png",
					},
					ScreenType: reminder.PostReminderScreenType_EXCEED_REMINDER,
					Rows: []*reminder.PostReminderScreenRows{
						{
							Row: &reminder.PostReminderScreenRows_TextRow{
								TextRow: &commontypes.Text{
									DisplayValue: &commontypes.Text_Html{
										Html: fmt.Sprintf("<font color='#646464'>You’ve already spent ₹1,459 on Shopping in %s,<br> which is more than your reminder of ₹1,200.</font>", time.Now().In(datetime.IST).Month().String()),
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockTxnAggregatesClient := txnaggregatesMocks.NewMockTxnAggregatesClient(ctrl)
			mockTxnAggregatesClient.EXPECT().GetTopCategories(gomock.Any(), gomock.Any()).Return(tt.mockGetTopCategories.resp, tt.mockGetTopCategories.err)
			mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
			mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(tt.mockGetAccount.resp, tt.mockGetAccount.err)
			mockActorClient := mocks2.NewMockActorClient(ctrl)
			mockActorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(tt.mockGetActorById.resp, tt.mockGetActorById.err)
			c := &CategoryReminder{
				config:            tt.fields.config,
				txnAggregates:     mockTxnAggregatesClient,
				actorClient:       mockActorClient,
				savingsClient:     mockSavingsClient,
				ruleManagerClient: tt.fields.ruleManagerClient,
				reminderLogDao:    tt.fields.reminderLogDao,
			}
			got, err := c.BuildReminderConditionCorrectScreen(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildReminderConditionCorrectScreen() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BuildReminderConditionCorrectScreen() got = %v, want %v", got, tt.want)
			}
		})
	}
}
