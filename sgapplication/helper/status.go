package helper

import "github.com/epifi/gringott/api/stockguardian/sgapplication/enums"

func GetTerminalApplicationStatuses() []enums.LoanApplicationStatus {
	terminalStatuses := []enums.LoanApplicationStatus{
		enums.LoanApplicationStatus_LOAN_APPLICATION_STATUS_CANCELLED,
		enums.LoanApplicationStatus_LOAN_APPLICATION_STATUS_SUCCESS,
		enums.LoanApplicationStatus_LOAN_APPLICATION_STATUS_FAILED,
	}
	return terminalStatuses
}

func GetTerminalStageStatuses() []enums.LoanApplicationStageStatus {
	terminalStatuses := []enums.LoanApplicationStageStatus{
		enums.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_SUCCESS,
		enums.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_FAILED,
		enums.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CANCELLED,
		enums.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_EXPIRED,
	}
	return terminalStatuses
}
