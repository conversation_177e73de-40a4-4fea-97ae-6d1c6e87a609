package alpaca

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/date"
	moneypb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"google.golang.org/protobuf/types/known/wrapperspb"

	types "github.com/epifi/gamma/api/typesv2"
	stocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	alpacaPb "github.com/epifi/gamma/api/vendors/alpaca"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"
)

const (
	PREFIX_COUNTRY_CODE = "COUNTRY_CODE_"

	// Represent precision to display after point
	AMOUNT_PRECISION = 9
)

func getUsdMoneyFromStr(data string) (*moneypb.Money, error) {
	if data == "" {
		return nil, nil
	}
	return money.ParseString(data, money.USDCurrencyCode)
}

func getStrFromMoneyWithNilAllowed(data *moneypb.Money, precision int32) (string, error) {
	if data == nil {
		return "", nil
	}
	if precision != 0 {
		return money.ToString(data, precision)
	}
	return money.ToString(data, AMOUNT_PRECISION)
}

func getAccountObject(data *alpacaPb.Account) (*stocksPb.Account, error) {
	lastEquity, err := getUsdMoneyFromStr(data.GetLastEquity())
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse last Equity in getAccount")
	}
	stockStatus, err := getAccountStatusFromStr(data.GetStatus())
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse stock Status in getAccount")
	}
	createdAtTimeStamp, err := getTimeStampFromStr(data.GetCreatedAt())
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse createAt TimeStamp in getAccount")
	}
	accountType, err := getAccountTypeFromStr(data.GetAccountType())
	dataParse := stocksPb.Account{
		AccountId:          data.GetId(),
		AccountNumber:      data.GetAccountNumber(),
		AccountStatus:      stockStatus,
		LastEquity:         lastEquity,
		CreatedAt:          createdAtTimeStamp,
		AccountType:        accountType,
		TradeConfiguration: getTradingConfigrationObject(data.GetTradeConfiguration()),
	}
	return &dataParse, nil
}

func getDayTradingMarginCallCheckTypeFromStr(data string) stocksPb.DayTradingMarginCallCheckType {
	if data == "" {
		return stocksPb.DayTradingMarginCallCheckType_DAY_TRADING_MARGIN_CALL_CHECK_UNSPECIFIED
	}
	switch data {
	case "both":
		return stocksPb.DayTradingMarginCallCheckType_DAY_TRADING_MARGIN_CALL_CHECK_BOTH
	case "entry":
		return stocksPb.DayTradingMarginCallCheckType_DAY_TRADING_MARGIN_CALL_CHECK_ENTRY
	case "exit":
		return stocksPb.DayTradingMarginCallCheckType_DAY_TRADING_MARGIN_CALL_CHECK_EXIT
	}
	return stocksPb.DayTradingMarginCallCheckType_DAY_TRADING_MARGIN_CALL_CHECK_UNSPECIFIED
}

func getTradingConfigrationObject(tradeConfig *alpacaPb.TradeConfigurations) *stocksPb.TradeConfigurations {
	if tradeConfig == nil {
		return nil
	}
	return &stocksPb.TradeConfigurations{
		DayTradingMarginCallCheck: getDayTradingMarginCallCheckTypeFromStr(tradeConfig.GetDayTradingMarginCallCheck()),
		EnableFractionTrading:     getBooleanEnumFromBooleanProto(tradeConfig.GetEnableFractionTrading()),
		MaxMarginMultipler:        tradeConfig.GetMaxMarginMultipler(),
		NoShortSelling:            getBooleanEnumFromBooleanProto(tradeConfig.GetNoShortSelling()),
		PatternDayTradeCheck:      tradeConfig.GetPatternDayTradeCheck(),
		SuspendTrades:             getBooleanEnumFromBooleanProto(tradeConfig.GetSuspendTrades()),
		EnableEmailUpdate:         getEnableEmailUpdateFromStr(tradeConfig.GetEnableEmailUpdate()),
	}
}

func getAccountTypeFromStr(data string) (stocksPb.AccountType, error) {
	switch data {
	case "trading":
		return stocksPb.AccountType_ACCOUNT_TYPE_TRADING, nil
	case "custodial":
		return stocksPb.AccountType_ACCOUNT_TYPE_CUSTODIAL, nil
	case "donor_advised":
		return stocksPb.AccountType_ACCOUNT_TYPE_DONOR_ADVISED, nil
	default:
		return stocksPb.AccountType_ACCOUNT_TYPE_UNSPECIFIED, fmt.Errorf("unable to parse account type %s form Alpaca", data)
	}
}

func getAccountStatusFromStr(data string) (stocksPb.AccountStatus, error) {
	switch strings.ToLower(data) {
	case "inactive":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_INACTIVE, nil
	case "onboarding":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_INITIATED, nil
	case "submitted":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_KYC_VERIFICATION_INITIATED, nil
	case "action_required":
	case "edited":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_MANUAL_INTERVENTION, nil
	case "approval_pending":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_KYC_VERIFICATION_IN_PROCESS, nil
	case "approved":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_KYC_VERIFICATION_COMPLETED, nil
	case "rejected":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_REJECTED, nil
	case "active":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_ACTIVE, nil
	case "submission_failed":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_FAILED_TO_INITIATE, nil
	case "disabled":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_DISABLED, nil
	case "account_closed":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_CLOSED, nil
	case "account_updated":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_UPDATED, nil
	case "":
		return stocksPb.AccountStatus_ACCOUNT_STATUS_UNSPECIFIED, nil
	}
	return stocksPb.AccountStatus_ACCOUNT_STATUS_UNSPECIFIED, fmt.Errorf("unable to convert AccountStatus for %s", data)
}

func getApprovalStatusFromStr(status stocksPb.KYCStatus) (string, error) {
	switch status {
	case stocksPb.KYCStatus_KYC_STATUS_APPROVED:
		return "approved", nil
	case stocksPb.KYCStatus_KYC_STATUS_REJECTED:
		return "rejected", nil
	default:
		return "", fmt.Errorf("unable to convert ApprovalStatus for %s", status)
	}
}

func getStrFromDate(date *date.Date) string {
	if date == nil {
		return ""
	}
	return fmt.Sprintf("%04d-%02d-%02d", date.Year, date.Month, date.Day)
}

func getStrFromTimestamp(timestamp *timestampPb.Timestamp) string {
	if timestamp == nil {
		return ""
	}
	return timestamp.AsTime().Format(time.RFC3339)
}

func getTimeStampFromStr(data string) (*timestampPb.Timestamp, error) {
	if data == "" {
		return nil, nil
	}
	return datetime.ParseStringTimeStampProto(time.RFC3339, data)

}

// Error validation is not introduced since BE might skip some fields according to KYC
// So there might case to use an unspecified type
func getStrFromVerificationResult(data stocksPb.VerificationResult) string {
	switch data {
	case stocksPb.VerificationResult_VERIFICATION_RESULT_MANUAL_INTERVENTION:
		return "consider"
	case stocksPb.VerificationResult_VERIFICATION_RESULT_CLEAR:
		return "clear"
	default:
		return ""
	}
}

// Error validation is not introduced since BE might skip some fields according to KYC
// So there might case to use an unspecified type
func getStrFromVerificationStatus(data stocksPb.VerificationStatus) string {
	switch data {
	case stocksPb.VerificationStatus_VERIFICATION_STATUS_CANCELLED:
		return "withdrawn"
	case stocksPb.VerificationStatus_VERIFICATION_STATUS_COMPLETE:
		return "complete"
	default:
		return ""
	}
}

func getStrFromEmploymentStatus(data stocksPb.EmploymentStatus) (string, error) {
	switch data {
	case stocksPb.EmploymentStatus_EMPLOYMENT_STATUS_EMPLOYED:
		return "employed", nil
	case stocksPb.EmploymentStatus_EMPLOYMENT_STATUS_RETIRED:
		return "retired", nil
	case stocksPb.EmploymentStatus_EMPLOYMENT_STATUS_STUDENT:
		return "student", nil
	case stocksPb.EmploymentStatus_EMPLOYMENT_STATUS_UNEMPLOYED:
		return "unemployed", nil
	default:
		return "", nil
	}
}

func getStrFromIncomeSource(data stocksPb.IncomeSource) (string, error) {
	switch data {
	case stocksPb.IncomeSource_INCOME_SOURCE_BUSINESS_INCOME:
		return "business_income", nil
	case stocksPb.IncomeSource_INCOME_SOURCE_EMPLOYMENT_INCOME:
		return "employment_income", nil
	case stocksPb.IncomeSource_INCOME_SOURCE_FAMILY:
		return "family", nil
	case stocksPb.IncomeSource_INCOME_SOURCE_INVESTMENTS:
		return "investments", nil
	case stocksPb.IncomeSource_INCOME_SOURCE_INHERITANCE:
		return "inheritance", nil
	case stocksPb.IncomeSource_INCOME_SOURCE_SAVINGS:
		return "savings", nil
	default:
		return "", fmt.Errorf("unable to convert IncomeSource String for %s", data)
	}
}

func getStrFromTaxIdType(data stocksPb.TaxIdType) (string, error) {
	switch data {
	case stocksPb.TaxIdType_TAX_ID_TYPE_IND_PAN:
		return "IND_PAN", nil
	default:
		return "", fmt.Errorf("unable to convert TaxIdType String for %s", data)
	}
}

func getStrFromDocumentType(data stocksPb.DocumentType) (string, error) {
	switch data {
	case stocksPb.DocumentType_DOCUMENT_TYPE_W8BEN:
		return "w8ben", nil
	case stocksPb.DocumentType_DOCUMENT_TYPE_ACCOUNT_APPROVAL_LETTER:
		return "account_approval_letter", nil
	case stocksPb.DocumentType_DOCUMENT_TYPE_ADDRESS_VERIFICATION:
		return "address_verification", nil
	case stocksPb.DocumentType_DOCUMENT_TYPE_DATE_OF_BIRTH_VERIFICATION:
		return "date_of_birth_verification", nil
	case stocksPb.DocumentType_DOCUMENT_TYPE_IDENTITY_VERIFICATION:
		return "identity_verification", nil
	case stocksPb.DocumentType_DOCUMENT_TYPE_TAX_ID_VERIFICATION:
		return "tax_id_verification", nil
	case stocksPb.DocumentType_DOCUMENT_TYPE_KYC_RESULT:
		return "kyc_result", nil
	default:
		return "", fmt.Errorf("unable to convert DocumentType String for %s", data)
	}
}

func getStrFromAgreementType(data stocksPb.AgreementType) (string, error) {
	switch data {
	case stocksPb.AgreementType_AGREEMENT_TYPE_MARGIN_AGREEMENT:
		return "margin_agreement", nil
	case stocksPb.AgreementType_AGREEMENT_TYPE_CUSTOMER_AGREEMENT:
		return "customer_agreement", nil
	case stocksPb.AgreementType_AGREEMENT_TYPE_ACCOUNT_AGREEMENT:
		return "account_agreement", nil
	default:
		return "", fmt.Errorf("unable to convert AgreementType String for %s", data)
	}
}

func getEnableEmailUpdateFromStr(data string) stocksPb.EnableEmailUpdate {
	switch data {
	case "all":
		return stocksPb.EnableEmailUpdate_ENABLE_EMAIL_UPDATE_ENABLE
	case "none":
		return stocksPb.EnableEmailUpdate_ENABLE_EMAIL_UPDATE_DISABLE
	default:
		return stocksPb.EnableEmailUpdate_ENABLE_EMAIL_UPDATE_UNSPECIFIED
	}
}

func getBooleanProtoFromDisclosureStatus(data stocksPb.DisclosureStatus) *wrapperspb.BoolValue {
	switch {
	case data == stocksPb.DisclosureStatus_DISCLOSURE_STATUS_DENIED:
		return &wrapperspb.BoolValue{
			Value: false,
		}
	case data == stocksPb.DisclosureStatus_DISCLOSURE_STATUS_ACCEPTED:
		return &wrapperspb.BoolValue{
			Value: true,
		}
	default:
		return nil
	}
}

func getBooleanEnumFromBooleanProto(data *wrapperspb.BoolValue) commontypes.BooleanEnum {
	if data == nil {
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
	}
	if data.Value {
		return commontypes.BooleanEnum_TRUE
	}
	return commontypes.BooleanEnum_FALSE
}

func getStrFromCountryCode(data types.CountryCode) string {
	if data == types.CountryCode_COUNTRY_CODE_UNSPECIFIED {
		return ""
	}
	countryName := data.String()
	countryName = strings.ReplaceAll(countryName, PREFIX_COUNTRY_CODE, "")
	return countryName
}

func getStrFromNationality(data types.Nationality) (string, error) {
	switch data {
	case types.Nationality_NATIONALITY_INDIAN:
		return "indian", nil
	default:
		return "", fmt.Errorf("unable to convert Nationality String for %s", data)
	}
}

func getStrFromDocumentSubType(data stocksPb.DocumentSubType) string {
	switch data {
	case stocksPb.DocumentSubType_DOCUMENT_SUB_TYPE_PAN_CARD:
		return "pan"
	case stocksPb.DocumentSubType_DOCUMENT_SUB_TYPE_ACCOUNT_STATEMENT:
		return "bank_account_statement"
	default:
		// ignoring because some document type doesnt require subtype
		return ""
	}
}

func getLegalName(name *commontypes.Name) string {
	return name.ToString()
}

// Trusted contact is an optional paramter
// but follows validation
// Ref:https://alpaca.markets/docs/api-references/broker-api/accounts/accounts/#trusted-contact
func validateTrustedContact(trustedContact *stocksPb.TrustedContact) error {
	if trustedContact == nil {
		return nil
	}
	if trustedContact.GetName() == nil {
		return fmt.Errorf("trusted contact require first name and last name")
	}
	if trustedContact.GetContactDetails() != nil {
		// if their is any contact information then it is valid
		if trustedContact.GetContactDetails().GetEmailAddress() != "" || trustedContact.GetContactDetails().GetPhoneNumber() != "" || trustedContact.GetContactDetails().GetStreetAddress() != "" || trustedContact.GetContactDetails().GetPostalCode() != "" || trustedContact.GetContactDetails().GetCity() != "" || trustedContact.GetContactDetails().GetState() != "" {
			return nil
		}
	}
	if trustedContact.GetCountry() != types.CountryCode_COUNTRY_CODE_UNSPECIFIED {
		return nil
	}
	return fmt.Errorf("Need additional information for a trusted contact")
}

func getMatchData(matchData []*stocksPb.MatchData) []*alpacaPb.MatchData {
	vgMatchData := make([]*alpacaPb.MatchData, len(matchData))
	for i, data := range matchData {
		vgMatchData[i] = &alpacaPb.MatchData{
			MatchingWatchlistCategory:    getVgWatchlistCategory(data.GetMatchingWatchlistCategory()),
			MatchingWatchlist:            data.GetMatchingWatchlist(),
			MatchingRecordName:           data.GetMatchingRecordName(),
			MatchingParameter:            getVgMatchParameters(data.GetMatchingParameter()),
			MatchingPercentage:           data.GetMatchingPercentage(),
			MatchType:                    data.GetMatchType().String(),
			Remarks:                      data.GetRemarks(),
			IsMatch:                      data.GetIsMatch(),
			WatchlistAliases:             data.GetWatchlistAliases(),
			NoticeDetails:                data.GetNoticeDetails(),
			WatchlistAddress:             data.GetWatchlistAddress(),
			WatchlistAssociatedCountries: data.GetWatchlistAssociatedCountries(),
			WatchlistDob:                 data.GetWatchlistDob(),
			ExternalSourceLinks:          data.GetExternalSourceLinks(),
			WatchlistIdentityNumbers:     data.GetWatchlistIdentityNumbers(),
			AdditionalInfo:               data.GetAdditionalInfo(),
		}
	}
	return vgMatchData
}

func getVgMatchParameters(parameter stocksPb.AmlParameter) string {
	switch parameter {
	case stocksPb.AmlParameter_AML_PARAMETER_UNSPECIFIED:
		return ""
	case stocksPb.AmlParameter_AML_PARAMETER_PAN:
		return "pan"
	case stocksPb.AmlParameter_AML_PARAMETER_PASSPORT:
		return "passport"
	case stocksPb.AmlParameter_AML_PARAMETER_DRIVING_LICENSE:
		return "driving_license"
	case stocksPb.AmlParameter_AML_PARAMETER_NAME:
		return "name"
	}
	return ""
}

func getVgWatchlistCategory(category stocksPb.AmlWatchlistCategory) string {
	switch category {
	case stocksPb.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_UNSPECIFIED:
		return ""
	case stocksPb.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_SANCTIONS:
		return "sanctions"
	case stocksPb.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_LAW_ENFORCEMENT:
		return "law_enforcement"
	case stocksPb.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_REGULATORY_ENFORCEMENT:
		return "regulatory_enforcement"
	case stocksPb.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_PEP:
		return "pep"
	case stocksPb.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_ADVERSE_MEDIA:
		return "adverse_media"
	case stocksPb.AmlWatchlistCategory_AML_WATCHLIST_CATEGORY_OTHERS:
		return "others"
	}
	return ""
}

func getAccountUpdateObject(data *alpacaPb.AccountUpdate) (*stocksPb.AccountUpdate, error) {
	accountStatusFrom, err := getAccountStatusFromStr(data.GetStatusFrom())
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse accountStatusFrom from AccountUpdate data")
	}
	accountStatusTo, err := getAccountStatusFromStr(data.GetStatusTo())
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse accountStatusTo from AccountUpdate data")
	}
	dataParse := &stocksPb.AccountUpdate{
		AccountId:        data.GetAccountId(),
		AccountNumber:    data.GetAccountNumber(),
		EventTime:        data.GetEventTime(),
		EventId:          data.GetEventId(),
		StatusFrom:       accountStatusFrom,
		StatusTo:         accountStatusTo,
		Reason:           data.GetReason(),
		PatternDayTrader: data.GetPatternDayTrader(),
		AccountBlocked:   data.GetAccountBlocked(),
		TradingBlocked:   data.GetTradingBlocked(),
		AdminConfigurations: &stocksPb.AdminConfigurations{
			OutgoingTransfersBlocked: data.GetAdminConfigurations().GetOutgoingTransfersBlocked(),
			IncomingTransfersBlocked: data.GetAdminConfigurations().GetIncomingTransfersBlocked(),
			DisableShorting:          data.GetAdminConfigurations().GetDisableShorting(),
			DisableFractional:        data.GetAdminConfigurations().GetDisableFractional(),
			DisableCrypto:            data.GetAdminConfigurations().GetDisableCrypto(),
			DisableDayTrading:        data.GetAdminConfigurations().GetDisableDayTrading(),
			MaxMarginMultiplier:      data.GetAdminConfigurations().GetMaxMarginMultiplier(),
			AcctDailyTransferLimit:   data.GetAdminConfigurations().GetAcctDailyTransferLimit(),
			RestrictToLiquidationReasons: &stocksPb.RestrictToLiquidationReasons{
				PatternDayTrading:     data.GetAdminConfigurations().GetRestrictToLiquidationReasons().GetPatternDayTrading(),
				AchReturn:             data.GetAdminConfigurations().GetRestrictToLiquidationReasons().GetAchReturn(),
				PositionToEquityRatio: data.GetAdminConfigurations().GetRestrictToLiquidationReasons().GetPositionToEquityRatio(),
				Unspecified:           data.GetAdminConfigurations().GetRestrictToLiquidationReasons().GetUnspecified(),
			},
		},
	}

	return dataParse, nil
}
