package alpaca

import (
	"context"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	stocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/api/vendors/alpaca"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vendorgateway/config"
)

type GetJournalSSERequest struct {
	conf *config.Config
	// responseChannel is used for sending message to corresponding RPC client
	responseChannel chan any
	// errChannel is used to get notified of errors happening in stream consumption thread in SSE ConnectionHandler
	errChannel chan error
}

func NewJournalSSERequest(conf *config.Config) (*GetJournalSSERequest, error) {
	errChannel := make(chan error)
	responseChannel := make(chan any)
	return &GetJournalSSERequest{
		conf:            conf,
		errChannel:      errChannel,
		responseChannel: responseChannel,
	}, nil
}

func (r *GetJournalSSERequest) GetRequestHeader() http.Header {
	req := http.Request{}
	req.Header = make(map[string][]string, 0)
	req.SetBasicAuth(r.conf.Application.Alpaca.Secret.BrokerApiKey, r.conf.Application.Alpaca.Secret.BrokerApiSecret)
	return req.Header
}

func (r *GetJournalSSERequest) GetConnURL() url.URL {
	return url.URL{Scheme: r.conf.Application.Alpaca.BrokerEventsApiScheme, Host: r.conf.Application.Alpaca.BrokerEventsApiHost, Path: r.conf.Application.Alpaca.JournalEventsPath}
}

// nolint:dupl
func (r *GetJournalSSERequest) HandleIncomingMessage(ctx context.Context, msgBytes []byte) error {
	event := string(msgBytes)

	// if journal update event data is not received, no operation required
	if !strings.Contains(event, "data:") {
		// logger.Debug(ctx, "non journal update message received, so dropping the message", zap.Any(logger.PAYLOAD, event))
		return nil
	}

	// to remove suffix `data:` from payload
	event = event[5:]
	journalUpdate := &alpaca.JournalUpdate{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(event), journalUpdate)
	if err != nil {
		logger.Error(ctx, "Error Unmarshalling the event byte stream", zap.Error(err))
		return err
	}

	logger.Debug(ctx, "journal update message received for journal Id ", zap.Any(logger.PAYLOAD, journalUpdate.GetJournalId()))

	journalUpdateObj, err := getJournalUpdateObj(journalUpdate)
	if err != nil {
		logger.Error(ctx, "Error in parsing the journal update response from journal update", zap.Error(err))
		return err
	}
	r.GetResponseChannel() <- journalUpdateObj
	return nil
}

func getJournalUpdateObj(journalUpdate *alpaca.JournalUpdate) (*stocksPb.JournalUpdate, error) {
	journalType, err := getJournalTypeFromStr(journalUpdate.GetJournalType())
	if err != nil {
		return nil, err
	}
	statusFrom := stocksPb.JournalStatus_JOURNAL_STATUS_UNSPECIFIED
	// status can be empty in first update event
	if len(journalUpdate.GetStatusFrom()) > 0 {
		statusFrom, err = getJournalStatusFromString(journalUpdate.GetStatusFrom())
		if err != nil {
			return nil, err
		}
	}
	statusTo, err := getJournalStatusFromString(journalUpdate.GetStatusTo())
	if err != nil {
		return nil, err
	}
	return &stocksPb.JournalUpdate{
		EventTime:   journalUpdate.GetEventTime(),
		JournalType: journalType,
		EventId:     strconv.FormatInt(journalUpdate.GetEventId(), 10),
		JournalId:   journalUpdate.GetJournalId(),
		StatusFrom:  statusFrom,
		StatusTo:    statusTo,
	}, nil
}

func (r *GetJournalSSERequest) GetErrorChannel() chan error {
	return r.errChannel
}

func (r *GetJournalSSERequest) GetResponseChannel() chan any {
	return r.responseChannel
}

func (r *GetJournalSSERequest) ShouldUseSimulatedEnvironment() bool {
	return r.conf.Application.Alpaca.ShouldUseSimulatedEnvForEvents
}
