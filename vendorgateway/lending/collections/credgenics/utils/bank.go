// nolint: gosec
package utils

import (
	"fmt"
	"strings"

	vgCredgenicsPb "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
)

// getBankAccountTypeStringToEnum converts the string to vgCredgenicsPb.BankAccountType
// if string is empty, it'll return vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_UNSPECIFIED
// e.g: "savings" -> vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_SAVINGS
func getBankAccountTypeStringToEnum(str string) (vgCredgenicsPb.BankAccountType, error) {
	if len(str) == 0 {
		return vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_UNSPECIFIED, nil
	}
	str = strings.ToLower(str)
	switch str {
	case "savings":
		return vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_SAVINGS, nil
	case "current":
		return vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_CURRENT, nil
	default:
		return vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_UNSPECIFIED, fmt.Errorf("invalid bank account type %s", str)
	}
}

// getBankAccountTypeEnumToString converts the vgCredgenicsPb.BankAccountType to string
// e.g: vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_SAVINGS -> "savings"
func getBankAccountTypeEnumToString(bankAccountType vgCredgenicsPb.BankAccountType) string {
	switch bankAccountType {
	case vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_SAVINGS:
		return "Savings"
	case vgCredgenicsPb.BankAccountType_BANK_ACCOUNT_TYPE_CURRENT:
		return "Current"
	default:
		return ""
	}
}
