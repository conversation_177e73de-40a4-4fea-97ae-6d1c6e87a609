package creditcard

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	vgCreditCard "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	federalReq "github.com/epifi/gamma/vendorgateway/lending/creditcard/federal"
	"github.com/epifi/gamma/vendorgateway/lending/creditcard/m2p"
	m2pVg "github.com/epifi/gamma/vendorgateway/m2p"
	m2pCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/m2p"
)

func (s *Service) NewM2PRequest(req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *vgCreditCard.RegisterCustomerRequest:
		return &m2p.RegisterCustomerRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.SetPreferencesRequest:
		return &m2p.SetPreferencesRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.RequestPhysicalCardRequest:
		return &m2p.RequestPhysicalCardRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.UpgradeLimitRequest:
		return &m2p.UpgradeLimitRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchLimitRequest:
		return &m2p.FetchLimitRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchPreferenceRequest:
		return &m2p.FetchPreferenceRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.GetCardListRequest:
		return &m2p.GetCardListRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchBalanceRequest:
		return &m2p.FetchBalanceRequest{
			Method: http.MethodGet,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.ManageLockRequest:
		return &m2p.ManageLockingRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchTransactionsRequest:
		return &m2p.FetchTransactionsRequest{
			Method: http.MethodGet,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.SetCreditLimitRequest:
		return &m2p.SetCreditLimitRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchDueAmountRequest:
		return &m2p.FetchDueRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.GenerateCVVRqeuest:
		return &m2p.GenerateCVVRequest{
			Method:  http.MethodPost,
			Req:     v,
			Conf:    s.conf,
			PciConf: s.piConf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.UpdateCustomerRequest:
		return &m2p.UpdateCustomerRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.GetUnmaskedCardDetailsRequest:
		return &m2p.GetUnmaskedCardDetailsRequest{
			Method:  http.MethodPost,
			Req:     v,
			Conf:    s.conf,
			PciConf: s.piConf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchEncodedStatementRequest:
		return &m2p.FetchEncodedStatementRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchMonthlyStatementRequest:
		return &m2p.FetchMonthlyStatementRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.ReplaceCardRequest:
		return &m2p.CardReplacementRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchTransactionStatusRequest:
		return &m2p.FetchTransactionStatusRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.SetPinRequest:
		return &m2p.SetPinRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchUnbilledTransactionsRequest:
		return &m2p.FetchUnbilledTransactionsRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.RepayLoanAmountRequest:
		return &m2p.RepayLoanAmountRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchStatementRequest:
		return &m2p.FetchStatementListRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.UpdateStatementDateRequest:
		return &m2p.UpdateStatementDateRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.MarkDisputeRequest:
		return &m2p.MarkDisputeRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.GetAllDisputesRequest:
		return &m2p.GetAllDisputesRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchEligibleTransactionsForEmiConversionRequest:
		return &m2p.FetchEligibleTransactionsForEmiConversionRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchTransactionLoanOffersRequest:
		return &m2p.FetchTransactionLoanOffersRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.CreateLoanRequest:
		return &m2p.CreateLoanRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchLoanByIdRequest:
		return &m2p.FetchLoanByIdRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FetchLoanByStatusRequest:
		return &m2p.FetchLoanByStatusRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.SetPinPartnerSdkRequest:
		return &m2p.SetPinPartnerSdkRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.PreviewPreCloseLoanRequest:
		return &m2p.PreviewPreCloseLoanRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.PreCloseLoanRequest:
		return &m2p.PreCloseLoanRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.CancelLoanRequest:
		return &m2p.CancelLoanRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.RegisterCustomerV2Request:
		return &m2p.RegisterCustomerV2Request{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.NonPreApprovedUnsecuredRegisterCustomerRequest:
		return &m2p.NonPreApprovedUnsecuredRegisterCustomerRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.SecuredCardRegisterCustomerRequest:
		return &m2p.SecuredCardRegisterCustomerRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	case *vgCreditCard.FeeReversalRequest:
		return &m2p.FeeReversalRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard,
			DefaultM2PSecuredExchange: &m2pCryptor.DefaultM2PSecuredExchange{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
			DefaultHeaderAdder: &m2pVg.DefaultHeaderAdder{
				VendorRequestHeader: v.GetCcVendorRequestHeader(),
			},
		}
	default:
		logger.ErrorNoCtx("Unsupported request type", zap.Any(logger.REQUEST_TYPE, v))
		return nil
	}
}
func (s *Service) NewFederalRequest(req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *vgCreditCard.UpdateCreditCardDetailsRequest:
		return &federalReq.UpdateCreditCardDetailsRequest{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application.Lending.CreditCard.Federal,
		}
	default:
		logger.ErrorNoCtx("Unsupported request type", zap.Any(logger.REQUEST_TYPE, v))
		return nil
	}
}

func (s *Service) getRequestFactoryMap() map[commonvgpb.Vendor]vendorapi.SyncRequestFactory {
	return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
		commonvgpb.Vendor_M2P:          s.NewM2PRequest,
		commonvgpb.Vendor_FEDERAL_BANK: s.NewFederalRequest,
	}
}
