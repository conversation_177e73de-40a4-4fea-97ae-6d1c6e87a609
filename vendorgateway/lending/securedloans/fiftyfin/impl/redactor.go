package impl

import (
	"context"

	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
)

var (
	// commonMaskingStrategies is a map of field name to masking strategy applied to that field
	// we use this map to redact body of requests and responses
	commonMaskingStrategies = map[string]mask.MaskingStrategy{
		"mother_last_name":                mask.MaskToStaticValue,
		"occupation":                      mask.MaskToStaticValue,
		"father_spouse_first_name":        mask.MaskToStaticValue,
		"father_spouse_last_name":         mask.MaskToStaticValue,
		"mother_first_name":               mask.MaskToStaticValue,
		"device_ip":                       mask.MaskToStaticValue,
		"latitude":                        mask.MaskToStaticValue,
		"longitude":                       mask.MaskToStaticValue,
		"document":                        mask.MaskToStaticValue,
		"pan":                             mask.MaskToStaticValue,
		"father_name":                     mask.MaskToStaticValue,
		"name_provided":                   mask.MaskToStaticValue,
		"registered_name":                 mask.MaskToStaticValue,
		"field":                           mask.MaskToStaticValue,
		"email":                           mask.MaskToStaticValue,
		"phone_number":                    mask.MaskToStaticValue,
		"first_name":                      mask.MaskToStaticValue,
		"last_name":                       mask.MaskToStaticValue,
		"date_of_birth":                   mask.MaskToStaticValue,
		"karvy_otp":                       mask.MaskAllChars,
		"cams_otp":                        mask.MaskAllChars,
		"LienUnits":                       mask.MaskToStaticValue,
		"FolioNo":                         mask.MaskToStaticValue,
		"lienunit":                        mask.MaskToStaticValue,
		"total_portfolio_value":           mask.MaskToStaticValue,
		"total_approved_value":            mask.MaskToStaticValue,
		"max_loan_amount":                 mask.MaskToStaticValue,
		"quantity":                        mask.MaskToStaticValue,
		"total_amount":                    mask.MaskToStaticValue,
		"folio_number":                    mask.MaskToStaticValue,
		"interest_account_number":         mask.MaskToStaticValue,
		"principal_account_number":        mask.MaskToStaticValue,
		"IFSC_code_kotak_to_kotak":        mask.MaskToStaticValue,
		"IFSC_code_non_kotak_to_kotak":    mask.MaskToStaticValue,
		"beneficiary_name":                mask.MaskToStaticValue,
		"amount":                          mask.MaskToStaticValue,
		"payment_amount":                  mask.MaskToStaticValue,
		"remaining_amount":                mask.MaskToStaticValue,
		"disbursement_amount":             mask.MaskToStaticValue,
		"interest_timeline":               mask.MaskToStaticValue,
		"HOLDING_STATEMENT":               mask.MaskToStaticValue,
		"FORECLOSURE_STATEMENT":           mask.MaskToStaticValue,
		"principal":                       mask.MaskToStaticValue,
		"outstanding_interest":            mask.MaskToStaticValue,
		"outstanding_charges":             mask.MaskToStaticValue,
		"interest_accrued_not_due":        mask.MaskToStaticValue,
		"penal_interest_receivable":       mask.MaskToStaticValue,
		"penal_interest_accrued_not_due":  mask.MaskToStaticValue,
		"bounce_charge":                   mask.MaskToStaticValue,
		"interest_on_closure_date":        mask.MaskToStaticValue,
		"penal_interest_on_closure_date":  mask.MaskToStaticValue,
		"total_due":                       mask.MaskToStaticValue,
		"advance_interest":                mask.MaskToStaticValue,
		"excess_margin":                   mask.MaskToStaticValue,
		"total_credit":                    mask.MaskToStaticValue,
		"net_payable":                     mask.MaskToStaticValue,
		"SOA_STATEMENT":                   mask.MaskToStaticValue,
		"contract_start_date":             mask.MaskToStaticValue,
		"contract_end_date":               mask.MaskToStaticValue,
		"tenure":                          mask.MaskToStaticValue,
		"sanctioned_limit":                mask.MaskToStaticValue,
		"loan_amount":                     mask.MaskToStaticValue,
		"principal_outstanding":           mask.MaskToStaticValue,
		"interest_rate":                   mask.MaskToStaticValue,
		"penal_interest":                  mask.MaskToStaticValue,
		"bounce_charges":                  mask.MaskToStaticValue,
		"dp_charges":                      mask.MaskToStaticValue,
		"excess_amount":                   mask.MaskToStaticValue,
		"first_interest_due_date":         mask.MaskToStaticValue,
		"next_interest_due_date":          mask.MaskToStaticValue,
		"interest_outstanding":            mask.MaskToStaticValue,
		"charges_outstanding":             mask.MaskToStaticValue,
		"TransactionDetails":              mask.MaskToStaticValue,
		"InterestSlabDetail":              mask.MaskToStaticValue,
		"OtherContractLevelSummary":       mask.MaskToStaticValue,
		"ValueDate":                       mask.MaskToStaticValue,
		"Particulars":                     mask.MaskToStaticValue,
		"DebitAmount":                     mask.MaskToStaticValue,
		"CreditAmount":                    mask.MaskToStaticValue,
		"LoanContractNo":                  mask.MaskToStaticValue,
		"SanctionedLimit":                 mask.MaskToStaticValue,
		"LoanAmount":                      mask.MaskToStaticValue,
		"AnnualizedInterestRate":          mask.MaskToStaticValue,
		"LoanMaturityDate":                mask.MaskToStaticValue,
		"InterestOutstanding":             mask.MaskToStaticValue,
		"ChargesOutstanding":              mask.MaskToStaticValue,
		"loan_account_no":                 mask.MaskToStaticValue,
		"lan_no":                          mask.MaskToStaticValue,
		"loan_term":                       mask.MaskToStaticValue,
		"loan_type":                       mask.MaskToStaticValue,
		"loan_frequency":                  mask.MaskToStaticValue,
		"loan_tenure":                     mask.MaskToStaticValue,
		"start_date":                      mask.MaskToStaticValue,
		"end_date":                        mask.MaskToStaticValue,
		"interest_amount":                 mask.MaskToStaticValue,
		"processing_fees":                 mask.MaskToStaticValue,
		"next_payment_date":               mask.MaskToStaticValue,
		"daily_interest_amount":           mask.MaskToStaticValue,
		"interest_till_date":              mask.MaskToStaticValue,
		"portfolio_id":                    mask.MaskToStaticValue,
		"is_top_up":                       mask.MaskToStaticValue,
		"old_loan_id":                     mask.MaskToStaticValue,
		"status":                          mask.MaskToStaticValue,
		"bajaj_doc_upload":                mask.MaskToStaticValue,
		"ckyc":                            mask.MaskToStaticValue,
		"payload":                         mask.MaskToStaticValue,
		"MasterData":                      mask.MaskToStaticValue,
		"Account":                         mask.MaskToStaticValue,
		"Opportunity":                     mask.MaskToStaticValue,
		"Contact":                         mask.MaskToStaticValue,
		"ScripDetails":                    mask.MaskToStaticValue,
		"BankDetails":                     mask.MaskToStaticValue,
		"Applicant":                       mask.MaskToStaticValue,
		"RepaymentModeDetails":            mask.MaskToStaticValue,
		"CamDetails":                      mask.MaskToStaticValue,
		"SourcingChannelName":             mask.MaskToStaticValue,
		"Name":                            mask.MaskToStaticValue,
		"Last_Name__c":                    mask.MaskToStaticValue,
		"First_Name__c":                   mask.MaskToStaticValue,
		"Middle_Name__c":                  mask.MaskToStaticValue,
		"Address_1st_Line__c":             mask.MaskToStaticValue,
		"Address_2nd_Line__c":             mask.MaskToStaticValue,
		"Address_3rd_Line__c":             mask.MaskToStaticValue,
		"AccCity__c":                      mask.MaskToStaticValue,
		"Date_of_Birth__c":                mask.MaskToStaticValue,
		"PANNumber__c":                    mask.MaskToStaticValue,
		"Mobile__c":                       mask.MaskToStaticValue,
		"Current_Email_Id__c":             mask.MaskToStaticValue,
		"PinCode__c":                      mask.MaskToStaticValue,
		"Gender__c":                       mask.MaskToStaticValue,
		"Permanent_Residence_Address1__c": mask.MaskToStaticValue,
		"Permanent_Residence_Address2__c": mask.MaskToStaticValue,
		"Permanent_Residence_Address3__c": mask.MaskToStaticValue,
		"Permanent_City__c":               mask.MaskToStaticValue,
		"Permanent_PinCode__c":            mask.MaskToStaticValue,
		"Permanent_State__c":              mask.MaskToStaticValue,
		"Residence_TypeAcc__c":            mask.MaskToStaticValue,
		"Permanent_Address_as_above__c":   mask.MaskToStaticValue,
		"Approved_Rate__c":                mask.MaskToStaticValue,
		"Approved_Tenor__c":               mask.MaskToStaticValue,
		"Existing_Customer__c":            mask.MaskToStaticValue,
		"Loan_Type__c":                    mask.MaskToStaticValue,
		"End_Use__c":                      mask.MaskToStaticValue,
		"Existing_Loan_Remarks__c":        mask.MaskToStaticValue,
		"Program_Type__c":                 mask.MaskToStaticValue,
		"Type_of_Constitution__c":         mask.MaskToStaticValue,
		"CloseDate":                       mask.MaskToStaticValue,
		"StageName":                       mask.MaskToStaticValue,
		"Product__c":                      mask.MaskToStaticValue,
		"Loan_Amount__c":                  mask.MaskToStaticValue,
		"Amount_Rs__c":                    mask.MaskToStaticValue,
		"Sales_Notes__c":                  mask.MaskToStaticValue,
		"Processing_Fees__c":              mask.MaskToStaticValue,
		"Scheme_Master__c":                mask.MaskToStaticValue,
		"Tenor__c":                        mask.MaskToStaticValue,
		"Location__c":                     mask.MaskToStaticValue,
		"Branch__c":                       mask.MaskToStaticValue,
		"Branch_Name__c":                  mask.MaskToStaticValue,
		"Sourcing_Channel__c":             mask.MaskToStaticValue,
		"Customer_reference_number__c":    mask.MaskToStaticValue,
		"Address_1__c":                    mask.MaskToStaticValue,
		"Address_2__c":                    mask.MaskToStaticValue,
		"Address_3__c":                    mask.MaskToStaticValue,
		"AppCity__c":                      mask.MaskToStaticValue,
		"ApplicantType__c":                mask.MaskToStaticValue,
		"Occupation_CKYC__c":              mask.MaskToStaticValue,
		"Marital_Status__c":               mask.MaskToStaticValue,
		"Father_Spouse__c":                mask.MaskToStaticValue,
		"Father_Spouse_First_Name__c":     mask.MaskToStaticValue,
		"Father_Spouse_Last_Name__c":      mask.MaskToStaticValue,
		"Father_Spouse_Salutation__c":     mask.MaskToStaticValue,
		"Mother_Salutation__c":            mask.MaskToStaticValue,
		"Mother_First_Name__c":            mask.MaskToStaticValue,
		"Mother_Last_Name__c":             mask.MaskToStaticValue,
		"Branch_Address__c":               mask.MaskToStaticValue,
		"Customer_Consents_with_date_and_time__c":     mask.MaskToStaticValue,
		"Customer_Device_IP__c":                       mask.MaskToStaticValue,
		"Customer_Device_Type__c":                     mask.MaskToStaticValue,
		"Customer_Type__c":                            mask.MaskToStaticValue,
		"District__c":                                 mask.MaskToStaticValue,
		"Email__c":                                    mask.MaskToStaticValue,
		"FirstName":                                   mask.MaskToStaticValue,
		"LastName":                                    mask.MaskToStaticValue,
		"PAN_Number__c":                               mask.MaskToStaticValue,
		"Partner_Website__c":                          mask.MaskToStaticValue,
		"Permanant_Address_Line_1__c":                 mask.MaskToStaticValue,
		"Permanant_Address_Line_2__c":                 mask.MaskToStaticValue,
		"Permanant_Address_Line_3__c":                 mask.MaskToStaticValue,
		"Permanant_City__c":                           mask.MaskToStaticValue,
		"Permanent_Address_same_as_Residence__c":      mask.MaskToStaticValue,
		"Permanent_Pin_Code__c":                       mask.MaskToStaticValue,
		"Pin_Code__c":                                 mask.MaskToStaticValue,
		"residence_type__c":                           mask.MaskToStaticValue,
		"Salutation":                                  mask.MaskToStaticValue,
		"State__c":                                    mask.MaskToStaticValue,
		"Website_IP_Address__c":                       mask.MaskToStaticValue,
		"CIF_Id__c":                                   mask.MaskToStaticValue,
		"DNC_Response__c":                             mask.MaskToStaticValue,
		"LAN_No_s__c":                                 mask.MaskToStaticValue,
		"Digital_Scrip_Name__c":                       mask.MaskToStaticValue,
		"Digital_Scrip_Type__c":                       mask.MaskToStaticValue,
		"Number_of_Shares__c":                         mask.MaskToStaticValue,
		"Digital_Scrip_market_price__c":               mask.MaskToStaticValue,
		"Digital_Scrips_Eligibility__c":               mask.MaskToStaticValue,
		"ISIN__c":                                     mask.MaskToStaticValue,
		"Bank_Name__c":                                mask.MaskToStaticValue,
		"Bank_Branch__c":                              mask.MaskToStaticValue,
		"IFSC_Code__c":                                mask.MaskToStaticValue,
		"Bank_Acct_Number__c":                         mask.MaskToStaticValue,
		"Account_Type__c":                             mask.MaskToStaticValue,
		"Applicant_Name__c":                           mask.MaskToStaticValue,
		"CKYC_No__c":                                  mask.MaskToStaticValue,
		"Identity_Document_Expiry_Date__c":            mask.MaskToStaticValue,
		"Identity_Document_No__c":                     mask.MaskToStaticValue,
		"Permanent_Address_Doc_No__c":                 mask.MaskToStaticValue,
		"Permanent_Address_Expiry_Date__c":            mask.MaskToStaticValue,
		"Proof_of_Address_Submitted_for_Permanent__c": mask.MaskToStaticValue,
		"Proof_of_Identity__c":                        mask.MaskToStaticValue,
		"Proof_of_Residence_Address_Submitted__c":     mask.MaskToStaticValue,
		"ReKYC__c":                                    mask.MaskToStaticValue,
		"Residence_Address_Doc_No__c":                 mask.MaskToStaticValue,
		"Residence_Address_Expiry_Date__c":            mask.MaskToStaticValue,
		"Open_ECS_Max_Limit__c":                       mask.MaskToStaticValue,
		"ECS_End_Date__c":                             mask.MaskToStaticValue,
		"ECS_Start_Date__c":                           mask.MaskToStaticValue,
		"ECS_Amount__c":                               mask.MaskToStaticValue,
		"Open_ECS_Facility__c":                        mask.MaskToStaticValue,
		"ROI__c":                                      mask.MaskToStaticValue,
		"CurrencyIsoCode":                             mask.MaskToStaticValue,
		"CY_Tenor__c":                                 mask.MaskToStaticValue,
		"e_agreement":                                 mask.MaskToStaticValue,
		"e_mandate":                                   mask.MaskToStaticValue,
		"kfs":                                         mask.MaskToStaticValue,
		"loan_status":                                 mask.MaskToStaticValue,
		"due_amount":                                  mask.MaskToStaticValue,
		"is_active":                                   mask.MaskToStaticValue,
		"repayment_timeline":                          mask.MaskToStaticValue,
		"application_number":                          mask.MaskToStaticValue,
		"account_number":                              mask.MaskToStaticValue,
		"id_number":                                   mask.MaskToStaticValue,
		"customer_identifier":                         mask.MaskToStaticValue,
		"reference_id":                                mask.MaskToStaticValue,
		"transaction_id":                              mask.MaskToStaticValue,
		"entities":                                    mask.MaskToStaticValue,
		"access_token":                                mask.MaskToStaticValue,
		"emailid":                                     mask.MaskToStaticValue,
		"user_name":                                   mask.MaskToStaticValue,
		"profile_picture_url":                         mask.MaskToStaticValue,
	}
)

type RequestBodyRedactor struct {
}

func (r *RequestBodyRedactor) RedactRequestBody(ctx context.Context, requestBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, requestBody, contentType, commonMaskingStrategies)
}

type ResponseBodyRedactor struct {
}

func (r *ResponseBodyRedactor) RedactResponseBody(ctx context.Context, responseBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, responseBody, contentType, commonMaskingStrategies)
}
