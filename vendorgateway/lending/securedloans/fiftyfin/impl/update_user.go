// nolint:dupl
package impl

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	vgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	vendorPb "github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/fiftyfin"
)

var updateUserStatusMapping = map[string]rpc.StatusFactory{
	"Invalid Field Type": rpc.StatusInvalidArgument,
	"User not found":     rpc.StatusRecordNotFound,
	"Field being updated is the same as existing field":      rpc.StatusOk,
	"Filed being updated is the same as existing field":      rpc.StatusOk,
	"User with this email already exists":                    newStatusFactory(uint32(vgPb.UpdateUserResponse_EMAIL_ALREADY_EXISTS)),
	"Phone number already exists, please try another number": newStatusFactory(uint32(vgPb.UpdateUserResponse_PHONE_NUMBER_ALREADY_EXISTS)),
	"User has been updated successfully":                     rpc.StatusOk,
}

type UpdateUserRequest struct {
	Method string
	Conf   *config.SecuredLoans
	Req    *vgPb.UpdateUserRequest
	*fiftyfin.DefaultHeaderSetter
	*fiftyfin.HeaderContentSetter
	*RequestBodyRedactor
}

type UpdateUserResponse struct {
	*ResponseBodyRedactor
}

func (u *UpdateUserRequest) Marshal() ([]byte, error) {
	ctx := context.Background()
	user := &vendorPb.UpdateUserRequest{
		UserId: u.Req.GetUserId(),
	}
	u.PopulateFieldForUser(user)
	if user.GetFieldType() == "" || user.GetField() == "" {
		logger.Error(ctx, "no field type or value passed for update user", zap.String("type", user.GetFieldType()), zap.String("value", user.GetField()))
		return nil, rpc.StatusAsError(rpc.StatusInvalidArgument())
	}
	res, err := protojson.Marshal(user)
	if err != nil {
		return nil, fmt.Errorf("error in marshalling update user request. err: %v", zap.Error(err))
	}
	return res, nil
}

func (u *UpdateUserRequest) GetResponse() vendorapi.Response {
	return &UpdateUserResponse{}
}

func (u *UpdateUserRequest) HTTPMethod() string {
	return u.Method
}

func (u *UpdateUserRequest) URL() string {
	return u.Conf.Url + "/auth/api/v1/update_user/"
}

func (u *UpdateUserResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := vendorPb.UpdateUserResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, fmt.Errorf("unable to unmarshal byte array to proto vendor response. err: %v", zap.Error(err))
	}
	if res.GetCode() != 200 && res.GetCode() != 201 {
		return &vgPb.AddAdditionalKycDetailsResponse{
			Status: getStatusForMsg(context.Background(), res.GetCode(), res.GetDetail(), updateUserStatusMapping, fiftyfinStatusForMsgReqParams("UpdateUser")),
		}, nil
	}
	vgResponse := &vgPb.UpdateUserResponse{
		Status: rpc.StatusOkWithDebugMsg(res.GetDetail()),
	}
	return vgResponse, nil
}

func (u *UpdateUserResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	res := vendorPb.UpdateUserResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to proto vendor response")
	}
	return &vgPb.UpdateUserResponse{
		Status: getStatusForMsg(ctx, res.GetCode(), res.GetDetail(), updateUserStatusMapping, fiftyfinStatusForMsgReqParams("UpdateUser")),
	}, nil
}

func (u *UpdateUserRequest) PopulateFieldForUser(user *vendorPb.UpdateUserRequest) {
	field := vgPb.FieldType_name[int32(u.Req.GetFieldType())]
	switch {
	case field == "FIELD_TYPE_NAME":
		user.FieldType = "name"
		user.Field = u.Req.GetName()
	case field == "FIELD_TYPE_EMAIL":
		user.FieldType = "email"
		user.Field = u.Req.GetEmail()
	case field == "FIELD_TYPE_PHN_NUMBER":
		user.FieldType = "phone_number"
		user.Field = u.Req.GetPhoneNumber().ToStringNationalNumber()
	}
}
