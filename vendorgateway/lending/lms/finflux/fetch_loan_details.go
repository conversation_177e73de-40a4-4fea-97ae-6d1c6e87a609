package finflux

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	vgFinfluxPb "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	vFinfluxPb "github.com/epifi/gamma/api/vendors/finflux"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/lending/lms/finflux/converters"
	"github.com/epifi/gamma/vendorgateway/lending/lms/finflux/header"
	"github.com/epifi/gamma/vendorgateway/lending/lms/finflux/redactor"
)

type FetchLoanDetailsReq struct {
	*header.Header
	*redactor.RequestBodyRedactor
	req *vgFinfluxPb.FetchLoanDetailsRequest
}

func (r *FetchLoanDetailsReq) Marshal() ([]byte, error) {
	return nil, nil
}

func (r *FetchLoanDetailsReq) URL() string {
	switch r.req.GetIdentifier().(type) {
	case *vgFinfluxPb.FetchLoanDetailsRequest_LoanId:
		return fmt.Sprintf("%s/fineract-provider/api/v1/loans/%s", r.Conf.BaseUrl, r.req.GetLoanId())
	case *vgFinfluxPb.FetchLoanDetailsRequest_ExternalId:
		return fmt.Sprintf("%s/fineract-provider/api/v1/loans/external-id/%s", r.Conf.BaseUrl, r.req.GetExternalId())
	default:
		return ""
	}
}

func (r *FetchLoanDetailsReq) GetResponse() vendorapi.Response {
	return &FetchLoanDetailsRes{
		conf: r.Header.Conf,
	}
}

type FetchLoanDetailsRes struct {
	*redactor.ResponseBodyRedactor
	conf *config.Finflux
}

func (r *FetchLoanDetailsRes) Unmarshal(b []byte) (proto.Message, error) {
	vendorRes := &vFinfluxPb.FetchLoanResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, vendorRes)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	loanDetails, err := converters.ConvertLoanDetailsVendorToVg(vendorRes, r.conf)
	if err != nil {
		return nil, fmt.Errorf("failed to convert loan details: %w", err)
	}

	return &vgFinfluxPb.FetchLoanDetailsResponse{
		Status: rpc.StatusOk(),
		Loan:   loanDetails,
	}, nil

}

func (r *FetchLoanDetailsRes) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "finflux fetch loan details http error",
		zap.Int(logger.HTTP_STATUS, httpStatus),
		zap.String(logger.RESPONSE, string(b)),
	)
	return &vgFinfluxPb.FetchLoanDetailsResponse{
		Status: vendorapi.GetRpcStatusFromHttpCodeWithDebugMsg(
			ctx,
			httpStatus,
			fmt.Sprintf("Error response: %s", string(b)),
		),
	}, nil
}
