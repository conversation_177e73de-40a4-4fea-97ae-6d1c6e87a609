package auth

import (
	"context"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	cachePkg "github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	lockPkg "github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
)

const (
	// TokenKey is the key to store the auth token in cache
	// nolint: gosec
	TokenKey = "finflux:vendor_auth_token"
	// TokenLockKey is the key to store the lock for generating auth token to prevent multiple requests generating
	// new token at the same time
	// nolint: gosec
	TokenLockKey = "finflux:vendor_auth_token_lock"
)

var WireSet = wire.NewSet(NewProcessorImpl, wire.Bind(new(Processor), new(*ProcessorImpl)))

type Processor interface {
	// GetAuthToken returns the auth token from cache if already present there or otherwise generates a new one
	// from vendor and caches it. It'll prefix the token with "Bearer" before returning.
	// e.g. "Bearer <auth_token>"
	GetAuthToken(ctx context.Context) (string, error)
}

type ProcessorImpl struct {
	Handler                *vendorapi.HTTPRequestHandler
	conf                   *config.Finflux
	kvStore                cachePkg.CacheStorage
	distributedLockManager lockPkg.ILockManager
}

func NewProcessorImpl(
	handler *vendorapi.HTTPRequestHandler,
	conf *config.Config,
	kvStore cachePkg.CacheStorage,
	distributedLockManager lockPkg.ILockManager,
) *ProcessorImpl {
	return &ProcessorImpl{
		Handler:                handler,
		conf:                   conf.Application.Lending.PreApprovedLoan.Finflux,
		kvStore:                kvStore,
		distributedLockManager: distributedLockManager,
	}
}

func (p *ProcessorImpl) GetAuthToken(ctx context.Context) (string, error) {
	val, err := p.kvStore.Get(ctx, TokenKey)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return "", errors.Wrap(err, "failed to get auth token from kv store")
	}
	if val != "" {
		return val, nil
	}
	// if auth token is not present in cache then generate a new one and cache it.
	return p.checkAndRefreshAuthToken(ctx)
}

// checkAndRefreshAuthToken returns auth token from cache if already present there or otherwise generates a new one
// and caches it.
func (p *ProcessorImpl) checkAndRefreshAuthToken(ctx context.Context) (string, error) {
	// acquire lock to prevent multiple requests from generating new auth token at the same time
	lock, err := p.distributedLockManager.GetLock(ctx, TokenLockKey, 2*time.Minute)
	if err != nil {
		return "", errors.Wrap(err, "error acquiring lock for refreshing auth token")
	}
	defer func() {
		// not passing the original ctx as if that is cancelled by client after lock is taken, then release lock will fail.
		if err = lock.Release(epificontext.CloneCtx(ctx)); err != nil {
			logger.Error(ctx, "error releasing lock", zap.Error(err))
		}
	}()

	// we are again checking the cache as it might have been updated by another request which acquired the lock
	// before this request and current request was waiting for the lock to be released.
	val, err := p.kvStore.Get(ctx, TokenKey)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return "", errors.Wrap(err, "error fetching auth token from cache")
	}
	if val != "" {
		return val, nil
	}

	logger.Debug(ctx, "generating new auth token")

	authTokenResponse, err := p.generateAuthTokenFromVendor(ctx)
	if err != nil {
		return "", errors.Wrap(err, "error generating auth token from vendor")
	}

	authToken := authTokenResponse.GetAccessToken()
	// prefix with "Bearer"
	authToken = "Bearer " + authToken

	// cache the auth token
	err = p.kvStore.Set(ctx, TokenKey, authToken, p.conf.Auth.TokenValidityDuration)
	if err != nil {
		// intentionally muting this error as this shouldn't fail the flow
		// the subsequent requests can still generate a new auth token if this fails
		logger.Error(ctx, "error caching auth token in token store", zap.Error(err))
	}

	return authToken, nil
}
