package digilocker

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"encoding/base64"
	"errors"
	"time"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"

	types "github.com/epifi/gamma/api/typesv2"
	wealthDigilockerPb "github.com/epifi/gamma/api/vendorgateway/wealth/digilocker"
	digilockerVendorPb "github.com/epifi/gamma/api/vendors/wealth/digilocker"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

func GetExpireTime(t int32) *types.Interval {
	startTime := timestampPb.New(time.Now())
	endTime := timestampPb.New(time.Now().Add(time.Second * time.Duration(t)))
	return &types.Interval{StartTime: startTime, EndTime: endTime}
}

func GetName(name string) *commontypes.Name {
	return names.ParseString(name)
}

func GetDateOfBirth(dob string) *types.Date {
	if dob == "" {
		return nil
	}
	layout := "02012006"
	tm, err := time.Parse(layout, dob)
	if err != nil {
		logger.ErrorNoCtx("error while parsing date of birth from digilocker", zap.Error(err))
		return nil
	}
	return &types.Date{Day: int32(tm.Day()), Month: int32(tm.Month()), Year: int32(tm.Year())}
}

func GetGender(g string) types.Gender {
	switch {
	case g == "M":
		return types.Gender_MALE
	case g == "F":
		return types.Gender_FEMALE
	case g == "T":
		return types.Gender_TRANSGENDER
	default:
		return types.Gender_OTHER
	}
}

func GetAadhaarStatus(status string) commontypes.BooleanEnum {
	if status == "Y" {
		return commontypes.BooleanEnum_TRUE
	} else {
		return commontypes.BooleanEnum_FALSE
	}
}

func GetMobileNumber(number string) *commontypes.PhoneNumber {
	num, err := commontypes.ParsePhoneNumber(number)
	if err != nil {
		logger.ErrorNoCtx("error while parsing mobile number from digilocker", zap.Error(err))
		return nil
	}
	num.CountryCode = 91
	return num
}

func ConvertDateFormat(date string) *types.Date {
	if date == "" {
		return nil
	}
	layout := "02-01-2006"
	tm, err := time.Parse(layout, date)
	if err != nil {
		logger.ErrorNoCtx("error while parsing issued date of documents from digilocker", zap.Error(err))
		return nil
	}
	return &types.Date{Day: int32(tm.Day()), Month: int32(tm.Month()), Year: int32(tm.Year())}
}

func GetIssuedDocuments(documents []*digilockerVendorPb.GetListOfIssuedDocumentsResponse_Documents) []*wealthDigilockerPb.Documents {
	var res []*wealthDigilockerPb.Documents
	for _, doc := range documents {
		res = append(res, &wealthDigilockerPb.Documents{
			DocumentName:    doc.GetDocumentName(),
			IssuedDate:      ConvertDateFormat(doc.GetIssuedDate()),
			FormatAvailable: doc.GetFormatAvailable(),
			Uri:             doc.GetUri(),
			Doctype:         doc.GetDoctype(),
			Description:     doc.GetDescription(),
			Issuer:          doc.GetIssuer(),
			IssuerId:        doc.GetIssuerId(),
		})
	}
	return res
}

func GetFile(data string) *commontypes.Image {
	return &commontypes.Image{
		ImageType:       commontypes.ImageType_PDF,
		ImageDataBase64: data,
		ImageUrl:        "",
	}
}

func basicAuth(username, password string) string {
	auth := username + ":" + password
	return base64.StdEncoding.EncodeToString([]byte(auth))
}

func GetAddress(add *Poa) *types.PostalAddress {
	if add == nil {
		return nil
	}
	res := &types.PostalAddress{
		RegionCode:         "IN",
		Locality:           add.Vtc,
		Sublocality:        add.Dist,
		PostalCode:         add.PinCode,
		AdministrativeArea: add.State,
	}
	addressLines := []string{add.House, add.Street, add.Loc}
	for _, address := range addressLines {
		if address != "" {
			res.AddressLines = append(res.AddressLines, address)
		}
	}
	// for some users house, street, loc value is empty so in this condition we'll also add vtc, dist into address lines.
	if len(res.AddressLines) == 0 {
		if add.Vtc != "" {
			res.AddressLines = append(res.AddressLines, add.Vtc)
			return res
		}
		if add.Dist != "" {
			res.AddressLines = append(res.AddressLines, add.Dist)
		}
	}
	return res
}

func GetImage(data string) *commontypes.Image {
	return &commontypes.Image{
		ImageType:       commontypes.ImageType_PNG,
		ImageDataBase64: data,
	}
}

func GetTtl(t string) *timestampPb.Timestamp {
	if t == "" {
		return nil
	}
	layout := "2006-01-02T15:04:05"
	tm, err := time.Parse(layout, t)
	if err != nil {
		logger.ErrorNoCtx("error while parsing Ttl of aadhaarCard from digilocker", zap.Error(err))
		return nil
	}
	return &timestampPb.Timestamp{
		Seconds: tm.Unix(),
	}
}

func validateUserData(aadhaar *Certificate) error {
	if aadhaar == nil {
		return errors.New("empty Certificate in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData == nil {
		return errors.New("empty Certificate Data in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData.KycRes == nil {
		return errors.New("empty KycRes in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData.KycRes.UidData == nil {
		return errors.New("empty UidData in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData.KycRes.UidData.Poi == nil {
		return errors.New("empty Poi in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData.KycRes.UidData.Poi.Name == "" {
		return errors.New("empty User Name in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData.KycRes.UidData.Poi.Dob == "" {
		return errors.New("empty User Dob in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData.KycRes.UidData.Poi.Gender == "" {
		return errors.New("empty User Gender in AadhaarCardXMLFormat")
	}
	if aadhaar.CertificateData.KycRes.UidData.Pht == "" {
		return errors.New("empty User Image in AadhaarCardXMLFormat")
	}
	return nil
}

func GetTs(t string) *timestampPb.Timestamp {
	if t == "" {
		return nil
	}
	ts, err := time.Parse(time.RFC3339, t)
	if err != nil {
		logger.ErrorNoCtx("error while parsing Tl of aadhaarCard from digilocker", zap.Error(err))
		return nil
	}
	return &timestampPb.Timestamp{
		Seconds: ts.Unix(),
	}
}
