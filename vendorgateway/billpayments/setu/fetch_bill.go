package setu

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/vendorapi"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	billPaymentsPb "github.com/epifi/gamma/api/vendorgateway/billpayments/setu"
	"github.com/epifi/gamma/api/vendors/setu/billpayments"
)

type FetchRequestRequest struct {
	PartnerId   string
	BaseUrl     string
	EndpointUri string
	Method      string
	Req         *billPaymentsPb.FetchRequestRequest
	ctx         context.Context
}

func (p *FetchRequestRequest) Marshal() ([]byte, error) {
	req := &billpayments.CouAgentBillFetchRequest{
		Agent:    p.Req.GetCouAgentBillFetchRequest().GetAgent(),
		Biller:   p.Req.GetCouAgentBillFetchRequest().GetBiller(),
		Customer: p.Req.GetCouAgentBillFetchRequest().GetCustomer(),
	}
	return protojson.Marshal(req)
}

func (p *FetchRequestRequest) HTTPMethod() string {
	return p.Method
}

func (p *FetchRequestRequest) URL() string {
	return fmt.Sprintf("%s/%s", p.BaseUrl, p.EndpointUri)
}

func (p *FetchRequestRequest) Add(req *http.Request) *http.Request {
	req.Header.Add("X-PARTNER-ID", p.PartnerId)
	return req
}

func (p *FetchRequestRequest) GetResponse() vendorapi.Response {
	return &FetchRequestResponse{ctx: p.ctx}
}

type FetchRequestResponse struct {
	ctx context.Context
}

//nolint:dupl
func (p *FetchRequestResponse) Unmarshal(b []byte) (proto.Message, error) {
	resp := &billpayments.CouAgentBillFetchResponse{}
	unmarshaller := protojson.UnmarshalOptions{
		DiscardUnknown: true,
	}
	if errUnmarshal := unmarshaller.Unmarshal(b, resp); errUnmarshal != nil {
		return nil, fmt.Errorf("error in unmarshalling cou agent bill fetch response : %v", errUnmarshal)
	}

	if resp.GetSuccess() != true {
		return nil, fmt.Errorf("ErrorCode: %s, ErrorMessage: %s", resp.GetError().GetCode(), resp.GetError().GetMessage())
	}

	return &billPaymentsPb.FetchRequestResponse{
		Status: rpc.StatusOk(),
		Data: &billpayments.WrappedRefId{
			RefId: resp.GetData().GetRefId(),
		},
		TraceId: resp.GetTraceId(),
	}, nil
}

func (p *FetchRequestResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
