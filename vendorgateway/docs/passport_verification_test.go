package docs

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"net/http"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	types "github.com/epifi/gamma/api/typesv2"
	docVgPb "github.com/epifi/gamma/api/vendorgateway/docs"
	"github.com/epifi/gamma/vendorgateway/config"
	federalCommon "github.com/epifi/gamma/vendorgateway/openbanking/federal"
)

var (
	fieldSanitizer *federalCommon.FieldSanitiser
)

func TestPassportVerificationRequest_Marshal(t *testing.T) {
	initialiseFieldSanitizer()
	t.<PERSON>()

	var (
		DateOfBirth = &types.Date{
			Year:  1987,
			Month: 8,
			Day:   17,
		}
		DateOfIssue = &types.Date{
			Year:  2018,
			Month: 05,
			Day:   14,
		}
		Name = &commontypes.Name{
			FirstName:  "OMKAR",
			MiddleName: "MILIND",
			LastName:   "SHIRHATTI",
			Honorific:  "",
		}
		PassportVerificationRequestJson = `
			{
			  "consent": "y",
			  "fileNo": "BO3072344560818",
			  "dob": "17/08/1987",
			  "passportNo": "********",
			  "doi": "14/05/2018",
			  "name": "OMKAR MILIND SHIRHATTI",
			  "clientData": {
				"caseId": "123456"
			  }
			}
`
	)
	type fields struct {
		Method string
		Req    *docVgPb.PassportVerificationRequest
		Conf   *config.Karza
	}
	tests := []struct {
		name    string
		fields  fields
		want    []byte
		wantErr bool
	}{
		{
			name: "Happy Flow",
			fields: fields{
				Method: http.MethodPost,
				Req: &docVgPb.PassportVerificationRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_KARZA,
					},
					CaseId:           "123456",
					FileNumber:       "BO3072344560818",
					Dob:              DateOfBirth,
					PassportNumber:   "********",
					DateOfIssue:      DateOfIssue,
					UserPassportName: Name,
				},
			},

			want:    []byte(PassportVerificationRequestJson),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &PassportVerificationRequest{
				Method:         tt.fields.Method,
				Req:            tt.fields.Req,
				FieldSanitizer: fieldSanitizer,
			}
			got, err := r.Marshal()
			if (err != nil) != tt.wantErr {
				t.Errorf("Marshal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				return
			}
			require.JSONEq(t, string(tt.want), string(got))
		})
	}
}

func initialiseFieldSanitizer() {
	var err error
	fieldSanitizer, err = federalCommon.NewFieldSanitiser()
	if err != nil {
		panic("failed to initialise filed sanitizer")
	}
}

func TestPassportVerificationResponse_Unmarshal(t *testing.T) {
	type args struct {
		b []byte
	}
	var (
		PassportVerificationResult = `
					{
					  "result": {
						"passportNumber": {
						  "passportNumberFromSource": "********",
						  "passportNumberMatch": true
						},
						"applicationDate": "14/05/2018",
						"typeOfApplication": "Tatkaal",
						"dateOfIssue": {
						  "dispatchedOnFromSource": "14/05/2018",
						  "dateOfIssueMatch": true
						},
						"name": {
						  "nameScore": 1,
						  "nameMatch": true,
						  "surnameFromPassport": "SHIRHATTI",
						  "nameFromPassport": "OMKAR MILIND"
						}
					  },
					  "requestId": "f3de6c55-6c0f-11e9-bf8e-610d4b51e956",
					  "statusCode": 101,
					  "clientData": {
						"caseId": "123456"
					  }
					}
		`
		PassportVerificationResultWithDataLoss = `
					{
					  "result": {
						"passportNumber": {
						  "passportNumberFromSource": "********",
						  "passportNumberMatch": null
						},
						"applicationDate": "14/05/2018",
						"typeOfApplication": "Tatkaal",
						"dateOfIssue": {
						  "dispatchedOnFromSource": "14/05/2018",
						  "dateOfIssueMatch": true
						},
						"name": {
						  "nameScore": 1,
						  "nameMatch": true,
						  "surnameFromPassport": "SHIRHATTI",
						  "nameFromPassport": "OMKAR MILIND"
						}
					  },
					  "requestId": "f3de6c55-6c0f-11e9-bf8e-610d4b51e956",
					  "statusCode": 101,
					  "clientData": {
						"caseId": "123456"
					  }
					}
		`
	)
	tests := []struct {
		name    string
		args    args
		want    proto.Message
		wantErr bool
	}{
		{
			name: "Happy Flow",
			args: args{b: []byte(PassportVerificationResult)},

			want: &docVgPb.PassportVerificationResponse{
				Status:    rpc.StatusOk(),
				RequestId: "f3de6c55-6c0f-11e9-bf8e-610d4b51e956",
				CaseId:    "123456",
				VerificationResult: &docVgPb.VerificationResult{
					PassportNumberMatchResult: &docVgPb.PassportNumberMatchResult{
						PassportNumberFromSource: "********",
						PassportNumberMatch:      types.Verdict_VERDICT_PASS,
					},
					ApplicationDate:   "14/05/2018",
					TypeOfApplication: "Tatkaal",
					DateOfIssueMatchResult: &docVgPb.DateOfIssueMatchResult{
						DispatchedOnFromSource: "14/05/2018",
						DateOfIssueMatch:       types.Verdict_VERDICT_PASS,
					},
					NameMatchResult: &docVgPb.NameMatchResult{
						NameScore:                      1,
						NameMatch:                      types.Verdict_VERDICT_PASS,
						SurnameFromPassport:            "SHIRHATTI",
						FirstAndMiddleNameFromPassport: "OMKAR MILIND",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "Null value in passport number match result",
			args: args{b: []byte(PassportVerificationResultWithDataLoss)},

			want: &docVgPb.PassportVerificationResponse{
				Status: rpc.StatusDataLoss(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &PassportVerificationResponse{}
			got, err := a.Unmarshal(tt.args.b)
			if (err != nil) != tt.wantErr {
				t.Errorf("Unmarshal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("PassportVerificationResponse Unmarshal mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
