package idvalidate

import (
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendorgateway/idvalidate"
	"github.com/epifi/gamma/api/vendors/karza"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
)

// to map karza's status code with doc validity
var karzaStatusMap = map[int64]idvalidate.ValidateVoterIDStatus{
	101: idvalidate.ValidateVoterIDStatus_VALID,
	103: idvalidate.ValidateVoterIDStatus_INVALID,
}

// NewKarzaRequest creates a new request for karza Voter validation api
func (s *Service) NewKarzaRequest(req proto.Message) vendorapi.SyncRequest {
	return &validateVoterIdRequest{
		req: req.(*idvalidate.ValidateVoterIdRequest),
		url: s.conf.Application.VoterIdValidationUrl,
		key: s.conf.Secrets.Ids[config.KarzaKey],
	}
}

// VoterIdValidation provides functionality for adapting to Karza's Voterid Validation API.
type validateVoterIdRequest struct {
	req      *idvalidate.ValidateVoterIdRequest
	url, key string
}

// Adding Header
func (r *validateVoterIdRequest) Add(req *http.Request) *http.Request {
	req.Header.Add("x-karza-key", r.key)
	return req
}

// Marshal provides the json for VoterIDValidation request call.
func (r *validateVoterIdRequest) Marshal() ([]byte, error) {
	req := &karza.ValidateVoterIdRequest{
		VoterId: r.req.GetVoterId(),
		Consent: "Y",
	}
	return protojson.Marshal(req)
}

// URL provides the URL to send the request to
func (r *validateVoterIdRequest) URL() string {
	return r.url
}

// HTTPMethod returns the http method to use for the API call.
func (r *validateVoterIdRequest) HTTPMethod() string {
	return http.MethodPost
}

// GetResponse returns Response struct that can deserialize the vendor response
func (r *validateVoterIdRequest) GetResponse() vendorapi.Response {
	return &validateVoterIdResponse{}
}

type validateVoterIdResponse struct {
}

// Unmarshal converts the response received from Karza's VoterId validation to
// VoterIdResponse proto.
func (r *validateVoterIdResponse) Unmarshal(b []byte) (proto.Message, error) {

	m := &karza.ValidateVoterIdResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, m)
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("Could not parse Voter Id  request response '%s'", string(b)), zap.Error(err))
		return nil, err
	}

	result, ok := karzaStatusMap[m.GetStatusCode()]

	if !ok {
		return nil, fmt.Errorf("invalid voter id status from vendor")
	}
	return &idvalidate.ValidateVoterIdResponse{
		Status:    rpc.StatusOk(),
		RequestId: m.GetRequestId(),
		Result:    result,
	}, nil
}
