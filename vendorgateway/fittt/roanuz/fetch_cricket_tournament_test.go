package roanuz

import (
	"fmt"
	"reflect"
	"sort"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	pb "github.com/epifi/gamma/api/vendorgateway/fittt"
)

const sample_resp_cricket_tournament = `{"data":{"tournament":{"key":"cplt20_2020","name":"Caribbean Premier League T20 2020","short_name":"CPL T20 2020","countries":[{"short_code":"WI","code":"WIN","name":"West Indies","official_name":null,"is_region":true}],"start_date":1.597779E9,"gender":"male","point_system":"tournament_based","competition":{"key":"c__competition__caribbean_premier_league__0b23f","code":"CPLT20","name":"Caribbean Premier League"},"association_key":"c__board__cwi__b3ce9","metric_group":"MG100","sport":"cricket","is_date_confirmed":true,"is_venue_confirmed":true,"last_scheduled_match_date":1.599696E9,"formats":["t20"]},"teams":{"bt":{"key":"bt","code":"BT","name":"Barbados Tridents"},"jt":{"key":"jt","code":"JT","name":"Jamaica Tallawahs"},"snp":{"key":"snp","code":"SNP","name":"St Kitts and Nevis Patriots"}},"rounds":[{"key":"cplt20_2020_k","name":"Knockout","have_points":false,"groups":[{"key":"cplt20_2020_sf","name":"Semi Finals","team_keys":["tbc","tbc"],"match_keys":["cplt20_2020_sf1","cplt20_2020_sf2"]},{"key":"cplt20_2020_final","name":"Finals","team_keys":["tbc","tbc"],"match_keys":["cplt20_2020_final"]}],"format":"t20"}]},"cache":{"key":"RS:CK:Tournament:cplt20_2020:5.0","expires":1.614088086408714E9,"etag":"ET:RS:CK:Tournament:cplt20_2020:5.0:1614087698.652206:0","max_age":300},"schema":{"major_version":"5.0","minor_version":"1"},"error":null,"http_status_code":200}`

type TeamsById []*pb.Team

func (a TeamsById) Len() int           { return len(a) }
func (a TeamsById) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a TeamsById) Less(i, j int) bool { return a[i].TeamId < a[j].TeamId }

func TestFetchCricketTournament(t *testing.T) {
	a := require.New(t)
	r := &FetchCricketTournamentReq{}
	m, err := r.Unmarshal([]byte(sample_resp_cricket_tournament))
	a.Nil(err, "unmarshall returned error", err)
	resp, ok := m.(*pb.FetchCricketTournamentResponse)
	a.Equal(true, ok, "doesn't return FetchCricketTournamentResponse", reflect.TypeOf(m))
	sort.Sort(TeamsById(resp.Tournament.Teams))
	fmt.Println(resp)
	expectedResp := &pb.FetchCricketTournamentResponse{
		Status: rpc.StatusOk(),
		Tournament: &pb.Tournament{
			Key:       "cplt20_2020",
			Name:      "Caribbean Premier League T20 2020",
			ShortName: "CPL T20 2020",
			StartDate: timestamppb.New(time.Unix(1.597779e9, 0)),
			EndDate:   timestamppb.New(time.Unix(1.599696e9, 0)),
			Teams: []*pb.Team{
				{
					TeamId: "bt",
					Code:   "BT",
					Name:   "Barbados Tridents",
				},
				{
					TeamId: "jt",
					Code:   "JT",
					Name:   "Jamaica Tallawahs",
				},
				{
					TeamId: "snp",
					Code:   "SNP",
					Name:   "St Kitts and Nevis Patriots",
				},
			},
		},
	}
	diff := cmp.Diff(resp, expectedResp, protocmp.Transform())
	a.Equal("", diff, "wrong response")
}
