package videosdk

import (
	"time"

	"github.com/golang-jwt/jwt/v4"

	vkyccallpb "github.com/epifi/gamma/api/vendorgateway/vkyccall"
	"github.com/epifi/gamma/vendorgateway/config"
)

const (
	apiKeyStr        string = "apikey"
	permissionsStr   string = "permissions"
	versionStr       string = "version"
	rolesStr         string = "roles"
	roomIdStr        string = "roomId"
	participantIdStr string = "participantId"
	iatStr           string = "iat"
	expiryStr        string = "exp"
)

func getDeafultJwtClaims() jwt.MapClaims {
	return jwt.MapClaims{
		versionStr: 2,
		iatStr:     time.Now().Unix(),
	}
}

func generateServerJwt(apiKey string, secretKey string) (string, error) {
	claims := jwt.MapClaims{
		versionStr:     2,
		apiKeyStr:      apiKey,
		iatStr:         time.Now().Unix(),
		rolesStr:       [1]string{"crawler"},
		permissionsStr: [1]string{"allow_join"},
		expiryStr:      time.Now().Add(1 * time.Minute).Unix(),
	}
	authToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := authToken.SignedString([]byte(secretKey))
	if err != nil {
		return "", err
	}
	return token, nil
}

func generateJwt(claims jwt.MapClaims, secretKey string) (string, error) {
	authToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	token, err := authToken.SignedString([]byte(secretKey))
	if err != nil {
		return "", err
	}
	return token, err
}

type GenerateJwtRequest struct {
	req  *vkyccallpb.GenerateJwtRequest
	conf *config.VideoSdk
}

func NewGenerateJwtRequest(conf *config.VideoSdk, req *vkyccallpb.GenerateJwtRequest) *GenerateJwtRequest {
	return &GenerateJwtRequest{
		req:  req,
		conf: conf,
	}
}

func (g *GenerateJwtRequest) GenerateToken() (string, error) {
	authTokenClaims := getDeafultJwtClaims()
	authTokenClaims[versionStr] = 2
	authTokenClaims[apiKeyStr] = g.conf.Secrets.ApiKey
	authTokenClaims[iatStr] = time.Now().Unix()
	authTokenClaims[expiryStr] = time.Now().Add(g.conf.JwtExpiryDuration).Unix()

	if g.req.GetRoomId() != "" {
		authTokenClaims[roomIdStr] = g.req.GetRoomId()
	}
	if g.req.GetParticipantId() != "" {
		authTokenClaims[participantIdStr] = g.req.GetParticipantId()
	}
	switch g.req.GetRole() {
	case vkyccallpb.ParticipantRole_PARTICIPANT_ROLE_AGENT:
		authTokenClaims[permissionsStr] = [2]string{"allow_join", "allow_mod"}
		authTokenClaims[rolesStr] = [1]string{"rtc"}
	case vkyccallpb.ParticipantRole_PARTICIPANT_ROLE_USER:
		authTokenClaims[permissionsStr] = [1]string{"allow_join"}
		authTokenClaims[rolesStr] = [1]string{"rtc"}
	case vkyccallpb.ParticipantRole_PARTICIPANT_ROLE_ROOT:
		authTokenClaims[permissionsStr] = [2]string{"allow_join", "allow_mod"}
		authTokenClaims[rolesStr] = [2]string{"crawler", "rtc"}
	case vkyccallpb.ParticipantRole_PARTICIPANT_ROLE_UNSPECIFIED:
	default:
	}
	return generateJwt(authTokenClaims, g.conf.Secrets.SecretKey)
}
