package finvu

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"time"

	aaPb "github.com/epifi/gamma/api/vendors/aa"

	"github.com/golang/protobuf/jsonpb"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendorgateway/aa"
	"github.com/epifi/gamma/api/vendors/aa/finvu"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/aa/common"
)

type GenerateFinvuJwtTokenReq struct {
	Method      string
	Req         *aa.GenerateFinvuJwtTokenRequest
	EndpointUri string
	TxnId       string
	EntityId    string
	EntityKey   string
	common.CommonAttribute
	Version aaPb.RebitApiVersion
}

func (r *GenerateFinvuJwtTokenReq) CreateMarshalled() ([]byte, error) {
	apiVersion := common.GetApiVersion(r.Version)
	req := &finvu.GenerateFinvuJwtTokenRequest{
		Ver:       apiVersion,
		Timestamp: time.Now().In(time.UTC).Format(common.TimestampFormat),
		TxnId:     r.TxnId,
		EntityId:  r.EntityId,
		EntityKey: r.EntityKey,
	}
	b := &bytes.Buffer{}
	m := jsonpb.Marshaler{}
	if err := m.Marshal(b, req); err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}

func (r *GenerateFinvuJwtTokenReq) Marshal() ([]byte, error) {
	return common.GetMarshalledPayload(r)
}

func (r *GenerateFinvuJwtTokenReq) URL() string {
	u, _ := url.Parse(r.BaseUrl)
	u.Path = path.Join(u.Path, r.EndpointUri)
	return u.String()
}

func (r *GenerateFinvuJwtTokenReq) HTTPMethod() string {
	return r.Method
}

func (r *GenerateFinvuJwtTokenReq) Add(req *http.Request) *http.Request {
	return r.AddHeaders(req, r)
}

func (r *GenerateFinvuJwtTokenReq) GetResponse() vendorapi.Response {
	return &GenerateFinvuJwtTokenResp{
		TxnId: r.TxnId,
	}
}

type GenerateFinvuJwtTokenResp struct {
	TxnId string
}

func (g *GenerateFinvuJwtTokenResp) Unmarshal(b []byte) (proto.Message, error) {
	m := finvu.GenerateFinvuJwtTokenResponse{}
	um := jsonpb.Unmarshaler{AllowUnknownFields: true}
	umErr := um.Unmarshal(bytes.NewBuffer(b), &m)
	if umErr != nil {
		return &aa.GenerateFinvuJwtTokenResponse{Status: rpc.StatusInternalWithDebugMsg("error in unmarshal response")}, umErr
	}
	return &aa.GenerateFinvuJwtTokenResponse{Status: rpc.StatusOk(), Token: m.GetToken()}, nil
}

func (g *GenerateFinvuJwtTokenResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "error in generating finvu jwt token", zap.Int("httpStatus", httpStatus), zap.String("response", string(b)))
	m := finvu.GenerateFinvuJwtTokenResponse{}
	um := jsonpb.Unmarshaler{AllowUnknownFields: true}
	umErr := um.Unmarshal(bytes.NewBuffer(b), &m)
	if umErr != nil {
		return &aa.GenerateFinvuJwtTokenResponse{Status: rpc.StatusInternalWithDebugMsg("error in unmarshal response")}, umErr
	}
	return &aa.GenerateFinvuJwtTokenResponse{
		Status: vendorapi.GetRpcStatusFromHttpCodeWithDebugMsg(
			ctx,
			httpStatus,
			fmt.Sprintf("httpStatus: %d, ShortMessage: %s, DebugMessage: %s", httpStatus, m.GetErrorCode(), m.GetErrorMessage()),
		),
	}, fmt.Errorf("httpStatus: %d, ErrorCode: %s, ErrorMessage: %s", httpStatus, m.GetErrorCode(), m.GetErrorMessage())
}

func (g *GenerateFinvuJwtTokenResp) ResponseWithHeader(ctx context.Context, respHeaders http.Header, responseBody []byte, signer vendorapi.ISigner) (proto.Message, error) {
	if !signer.IsSignatureNeeded() {
		return nil, nil
	}
	signature := respHeaders.Get(caPkg.JwsSignatureHttpHeaderKey)
	if err := signer.GetSigner().VerifyDetachedJwsSignature(signature, responseBody, signer.GetPublicKey()); err != nil {
		return &aa.GenerateAccessTokenResponse{Status: rpc.StatusInternalWithDebugMsg("error in verifying response signature")}, err
	}
	// In case of successful verification of signature, unmarshal will be called and response will be returned from there
	return nil, nil
}
