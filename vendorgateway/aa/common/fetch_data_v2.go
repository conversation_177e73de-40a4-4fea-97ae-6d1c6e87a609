//nolint:depguard
package common

import (
	"bytes"
	"net/http"
	"net/url"
	"path"
	"time"

	"github.com/golang/protobuf/jsonpb"

	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/vendorgateway/aa"
	venAaPb "github.com/epifi/gamma/api/vendors/aa"
	"github.com/epifi/gamma/api/vendors/aa/onemoney"
)

type FetchDataV2Req struct {
	Method      string
	Req         *aa.FetchDataRequest
	EndpointUri string
	CommonAttribute
}

func (r *FetchDataV2Req) CreateMarshalled() ([]byte, error) {
	linkRefNumbers := make([]*onemoney.LinkRefNumber, 0)
	for _, lrf := range r.Req.GetLinkRefNumber() {
		linkRefNumbers = append(linkRefNumbers, &onemoney.LinkRefNumber{
			Id: lrf,
		})
	}
	req := &onemoney.FIFetchV2Request{
		Ver:            venAaPb.ApiVersionV2,
		Timestamp:      time.Now().In(time.UTC).Format(TimestampFormat),
		Txnid:          uuid.New().String(),
		SessionId:      r.Req.GetSessionId(),
		FipId:          r.Req.GetFipId(),
		LinkRefNumbers: linkRefNumbers,
	}
	b := &bytes.Buffer{}
	m := jsonpb.Marshaler{}
	if err := m.Marshal(b, req); err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}

func (r *FetchDataV2Req) Marshal() ([]byte, error) {
	return GetMarshalledPayload(r)
}

func (r *FetchDataV2Req) URL() string {
	u, _ := url.Parse(r.BaseUrl)
	u.Path = path.Join(u.Path, r.EndpointUri)
	return u.String()
}

// HTTPMethod returns the http method to use for the API call.
func (r *FetchDataV2Req) HTTPMethod() string {
	return r.Method
}

func (r *FetchDataV2Req) Add(req *http.Request) *http.Request {
	return r.AddHeaders(req, r)
}

// NewResponse returns Response struct that can deserialize the vendor response
func (r *FetchDataV2Req) GetResponse() vendorapi.Response {
	return &FetchDataResp{}
}

// For all vendor APIs which require exact vendor times instrumentation we will implement this method
func (g *FetchDataV2Req) RecordVendorApiResponseTime(dur time.Duration, statusCode int) {
}
