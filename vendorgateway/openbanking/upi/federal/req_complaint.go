package fedral

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/xml"
	"errors"
	"fmt"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/upi/complaint"
	upiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/api/vendors"
	federalUPI "github.com/epifi/gamma/api/vendors/federal/upi"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/federal"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const txnTypeComplaint = "COMPLAINT"

var (
	reqComplaintAckToStatusMap = map[string]rpc.StatusFactory{
		"00": rpc.StatusOk,
	}

	requestComplaintActionMap = map[complaint.RequestComplaintAction]string{
		complaint.RequestComplaintAction_COMPLAINT_RAISED: "PBRB",
	}

	// TODO (Rahul) Add all the Reason
	requestComplaintReasonMap = map[complaint.RequestComplaintReason]string{
		complaint.RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_SUCCESSFUL_TRANSACTION: "U010",
		complaint.RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_PENDING_TRANSACTION:    "U010",
		complaint.RequestComplaintReason_GOODS_NOT_PROVIDED:                              "U008",
		complaint.RequestComplaintReason_CREDIT_NOT_PROCESSED_FOR_CANCELLED_GOODS:        "U021",
		complaint.RequestComplaintReason_TRANSACTION_CONFIRMATION_NOT_RECEIVED:           "U022",
		complaint.RequestComplaintReason_DUPLICATE_PAYMENT:                               "U023",
		complaint.RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_DECLINED_TRANSACTION:   "U009",
		complaint.RequestComplaintReason_ACCOUNT_NOT_REVERSED_FOR_DECLINED_TRANSACTION:   "U005",
		complaint.RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_MERCHANT_TRANSACTION:   "U022",
		complaint.RequestComplaintReason_ACCOUNT_NOT_CREDITED_FOR_P2P_TRANSACTION:        "U010",
	}
)

type reqComplaintAckResponse struct{}

type ReqComplaintRequest struct {
	defaultSigningTechnique
	*federal.DefaultHeaderAdder

	Method string
	Req    *upiPb.RaiseComplaintRequest
	Url    string
	MsgId  string
}

func (r *ReqComplaintRequest) Marshal() ([]byte, error) {
	var req federalUPI.ReqComplaintRequest

	err := PopulateMerchantHeaders(&req.MerchantHeader, vendors.ApiTypeReqComplaint, r.Req.GetEpifiCustomerVpa())
	if err != nil {
		return nil, err
	}
	err = PopulateMerchantBodyHead(&req.MerchantBody.ReqComplaint.Head, defaultUPIVersion, r.Req.GetEpifiCustomerVpa())
	if err != nil {
		return nil, err
	}
	req.MerchantBody.ReqComplaint.Head.ProdType = "UPI"
	err = PopulateTransaction(&req.MerchantBody.ReqComplaint.Txn, r.Req.TxnHeader, txnTypeComplaint)
	if err != nil {
		return nil, err
	}
	req.MerchantBody.ReqComplaint.Txn.SubType = "PAYER"
	val, ok := requestComplaintReasonMap[r.Req.Complaint.ComplaintReason]
	if !ok {
		return nil, fmt.Errorf("%v: ComplaintReason is unknown", r.Req.Complaint.ComplaintReason)
	}
	req.MerchantBody.ReqComplaint.Complaint.RequestComplaintReason = val

	val, ok = requestComplaintActionMap[r.Req.Complaint.ComplaintAction]
	if !ok {
		return nil, fmt.Errorf("%v: ComplaintAction is unknown", r.Req.Complaint.ComplaintAction)
	}
	req.MerchantBody.ReqComplaint.Complaint.RequestComplaintAction = val

	amount := "0.00"
	if r.Req.GetComplaint().GetComplaintAmount() != nil {
		amount, err = pkgMoney.ToString(r.Req.GetComplaint().GetComplaintAmount(), 2)
		if err != nil {
			logger.ErrorNoCtx("Error in Parsing Amount", zap.Error(err))
			return nil, err
		}
	}

	req.MerchantBody.ReqComplaint.Complaint.Amount = amount
	req.MerchantBody.ReqComplaint.Txn.OrgTxnId = r.Req.GetTxnHeader().GetOriginalTxnId()
	req.MerchantBody.ReqComplaint.Txn.OrgTxnDate = r.Req.GetOrgTxnDate().AsTime().In(federal.Timezone).Format(vendors.UPINPCITimestampLayout)
	r.MsgId = req.MerchantBody.ReqComplaint.Head.Msg

	PopulateCommonNamespaceAttr(&req.XmlnsFed, &req.XmlnsUpi, &req.XmlnsXsi)
	req.XsiSchemalocation = federalSchemaLocation
	req.MerchantBody.ReqComplaint.XmlnsNs2 = xmlNs2Attr
	req.MerchantBody.ReqComplaint.XmlnsNs3 = xmlNs3Attr

	return xml.Marshal(req)
}

func (r *ReqComplaintRequest) HTTPMethod() string {
	return r.Method
}

func (r *ReqComplaintRequest) URL() string {
	return r.Url
}

func (r *ReqComplaintRequest) RequestId() string {
	return r.MsgId
}

func (r *ReqComplaintRequest) GetFallbackResponse(ctx context.Context, err error) (proto.Message, error) {
	status := rpc.StatusUnavailable()

	if errors.Is(err, epifierrors.ErrVendorApiTimeout) {
		status = rpc.StatusDeadlineExceeded()
	}

	return &upiPb.RaiseComplaintResponse{
		Status: status,
	}, nil
}

// return the content type of the request
func (r *ReqComplaintRequest) ContentTypeString() string {
	return vendorapi.ContentTypeXML
}

func (r *ReqComplaintRequest) Api() commonvgpb.SyncWrappedApi {
	return commonvgpb.SyncWrappedApi_OB_UPI_REQ_COMPLAINT
}

func (r *reqComplaintAckResponse) Unmarshal(b []byte) (proto.Message, error) {
	var res federalUPI.AckResponse
	err := xml.Unmarshal(b, &res)
	if err != nil {
		return nil, fmt.Errorf("could not parse XML response: %w", err)
	}

	ackStatus := getAckStatus(&res, reqComplaintAckToStatusMap)

	return &upiPb.RaiseComplaintResponse{
		Status: ackStatus(),
	}, nil
}

func (r *ReqComplaintRequest) GetResponse() vendorapi.Response {
	return &reqComplaintAckResponse{}
}
