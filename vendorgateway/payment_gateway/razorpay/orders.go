package razorpay

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"google.golang.org/protobuf/types/known/durationpb"

	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	pgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	razorpayPb "github.com/epifi/gamma/api/vendors/razorpay"
	"github.com/epifi/gamma/pkg/vendors/paymentgateway/razorpay"
)

var (
	errCodeMap = map[string]*rpc.Status{
		// currently, only single error code provided
		BadRequestError: rpc.StatusInvalidArgument(),
	}

	// 5 days
	razorpayMaxManualCaptureTimeout = durationpb.New(5 * 24 * 60 * time.Minute)
)

type CreateOrderRequest struct {
	Method     string
	Req        *pgPb.CreateOrderRequest
	Url        string
	ClientName string
	*AuthHeaderSetter
}
type CreateOrderResponse struct{}

func (c *CreateOrderRequest) Marshal() ([]byte, error) {
	var bankAccount *razorpayPb.BankAccount
	// RequireAccountValidationForPayment flag doesn't percolate to Razorpay.
	// It is just used to populate bank account details in request.
	if c.Req.GetRequireAccountValidationForPayment() {
		bankAccount = &razorpayPb.BankAccount{
			AccountNumber: c.Req.GetBankAccount().GetAccountNumber(),
			Name:          c.Req.GetBankAccount().GetAccountHolderName(),
			Ifsc:          c.Req.GetBankAccount().GetIfscCode(),
		}
	}

	var paymentCaptureOptions *razorpayPb.PaymentCaptureOptions
	if c.Req.GetPaymentCaptureOptions() != nil {
		paymentCaptureOptions = &razorpayPb.PaymentCaptureOptions{
			Capture: razorpay.ConverCaptureTypeToRazorpayString(c.Req.GetPaymentCaptureOptions().GetCaptureType()),
			CaptureOptions: &razorpayPb.PaymentCaptureOptions_CaptureOptions{
				AutomaticExpiryPeriod: toRazorpayCaptureDuration(c.Req.GetPaymentCaptureOptions().GetAutomaticCapturePeriodMins()),
				// Setting manual capture timeout to
				// max value, so that if for some reason the payment isn't auto captured and OPS team has to manually
				// capture the payment, then there is some time for them to do it.
				ManualExpiryPeriod: toRazorpayCaptureDuration(razorpayMaxManualCaptureTimeout),
				RefundSpeed:        razorpay.ConvertRefundSpeedToRazorpayString(c.Req.GetPaymentCaptureOptions().GetRefundSpeed()),
			},
		}
	}

	payload := &razorpayPb.CreateOrderRequest{
		Amount:                toRazorpayAmount(c.Req.GetAmount()),
		Currency:              c.Req.GetAmount().GetCurrencyCode(),
		BankAccount:           bankAccount,
		Receipt:               c.Req.GetReferenceId(),
		PaymentCapture:        c.Req.GetIsCreateOrderForChargingCustomer(),
		PaymentCaptureOptions: paymentCaptureOptions,
	}
	return protojson.Marshal(payload)
}

// URL provides the URL to send the request to
func (c *CreateOrderRequest) URL() string {
	return fmt.Sprintf("%s/v1/orders", c.Url)
}

// HTTPMethod returns the http method to use for the API call.
func (c *CreateOrderRequest) HTTPMethod() string {
	return c.Method
}

func (c *CreateOrderRequest) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

// GetResponse returns Response struct that can deserialize the vendor response
func (c *CreateOrderRequest) GetResponse() vendorapi.Response {
	return &CreateOrderResponse{}
}

func (c *CreateOrderResponse) Unmarshal(b []byte) (proto.Message, error) {
	payload := &razorpayPb.CreateOrderResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, payload)
	if err != nil {
		logger.ErrorNoCtx("error is", zap.Error(err))
		return &pgPb.CreateOrderResponse{Status: rpc.StatusInternalWithDebugMsg("error unmarshalling response")}, err
	}
	return getResponse(payload), nil
}

func getRPCStatus(err *razorpayPb.Error) *rpc.Status {
	if err == nil {
		// return ok status if no error present
		return rpc.StatusOk()
	}
	res, ok := errCodeMap[err.GetCode()]
	if !ok {
		res = rpc.StatusInternal()
	}
	return res
}

func getResponse(razorpayResp *razorpayPb.CreateOrderResponse) *pgPb.CreateOrderResponse {
	resp := &pgPb.CreateOrderResponse{}
	if razorpayResp.GetError() != nil {
		resp.VendorStatus = &commonvgpb.VendorStatus{
			Code:        razorpayResp.GetError().GetCode(),
			Description: getErrorDescription(razorpayResp),
		}
	}
	resp.Status = getRPCStatus(razorpayResp.GetError())
	if !resp.GetStatus().IsSuccess() {
		logger.ErrorNoCtx(fmt.Sprintf("received error resp from vendor: %s: %s",
			razorpayResp.GetError().GetCode(), getErrorDescription(razorpayResp)))
		return resp
	}
	resp.VendorOrderId = razorpayResp.GetId()
	resp.OrderStatus = razorpay.GetVendorOrderStatusEnum(razorpayResp.GetStatus())
	if resp.GetOrderStatus() == pgPb.VendorOrderStatus_VENDOR_ORDER_STATUS_UNSPECIFIED {
		logger.ErrorNoCtx(
			fmt.Sprintf("unexpected order status in response: %s", razorpayResp.GetStatus()),
			zap.String(logger.VENDOR_ORDER_ID, razorpayResp.GetId()))
	}
	resp.ReferenceId = razorpayResp.GetReceipt()
	resp.OrderMetadata = &pgPb.OrderMetadata{
		Attempts: razorpayResp.GetAttempts(),
	}
	resp.CreatedAt = timestamp.New(time.Unix(razorpayResp.GetCreatedAt(), 0))
	return resp
}

func getErrorDescription(razorpayResp *razorpayPb.CreateOrderResponse) string {
	err := razorpayResp.GetError()
	if err == nil {
		return ""
	}
	return fmt.Sprintf("src: %s, step: %v, reason: %s, field: %s, desc: %s", err.GetSource(), err.GetStep(), err.GetReason(), err.GetField(), err.GetDescription())
}

func getConsolidatedErrorMessage(err *razorpayPb.Error) string {
	if err == nil {
		return ""
	}
	return fmt.Sprintf("src: %s, step: %v, reason: %s, field: %s, desc: %s", err.GetSource(), err.GetStep(), err.GetReason(), err.GetField(), err.GetDescription())
}

func (c *CreateOrderResponse) HandleHttpError(ctx context.Context, httpStatusCode int,
	responseBody []byte) (proto.Message, error) {
	var m razorpayPb.CreateOrderResponse
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(responseBody, &m)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshall not ok razorpay response", zap.Error(err))
		return &pgPb.CreateOrderResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to unmarshall not ok razorpay response"),
		}, nil
	}
	return getResponse(&m), nil
}

type CreateOrderForRecurringPaymentAuthorisationRequest struct {
	Method     string
	Req        *pgPb.CreateOrderRequest
	Url        string
	ClientName string
	*AuthHeaderSetter
}
type CreateOrderForRecurringPaymentAuthorisationResponse struct{}

func (c *CreateOrderForRecurringPaymentAuthorisationRequest) HTTPMethod() string {
	return c.Method
}

func (c *CreateOrderForRecurringPaymentAuthorisationRequest) URL() string {
	return fmt.Sprintf("%s/v1/orders", c.Url)
}

func (c *CreateOrderForRecurringPaymentAuthorisationRequest) GetResponse() vendorapi.Response {
	return &CreateOrderForRecurringPaymentAuthorisationResponse{}
}

func (c *CreateOrderForRecurringPaymentAuthorisationResponse) Unmarshal(b []byte) (proto.Message, error) {
	payload := &razorpayPb.CreateOrderForRecurringPaymentAuthorisationResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(b, payload)
	if err != nil {
		logger.ErrorNoCtx("error is", zap.Error(err))
		return &pgPb.CreateOrderResponse{Status: rpc.StatusInternalWithDebugMsg("error unmarshalling response")}, err
	}
	return getRecurringPaymentAuthorisationResponse(payload), nil
}

func (c *CreateOrderForRecurringPaymentAuthorisationRequest) Marshal() ([]byte, error) {
	var bankAccount *razorpayPb.BankAccountV1
	recurringPaymentParams := c.Req.GetRecurringPaymentParameters()
	// RequireAccountValidationForPayment flag doesn't percolate to Razorpay.
	// It is just used to populate bank account details in request.
	if c.Req.GetRequireAccountValidationForPayment() {
		bankAccount = &razorpayPb.BankAccountV1{
			AccountNumber:   c.Req.GetBankAccount().GetAccountNumber(),
			BeneficiaryName: c.Req.GetBankAccount().GetAccountHolderName(),
			AccountType:     strings.ToLower(c.Req.GetBankAccount().GetAccountType()),
			IfscCode:        c.Req.GetBankAccount().GetIfscCode(),
		}
	}
	payload := &razorpayPb.CreateOrderForRecurringPaymentAuthorisationRequest{
		Amount:         toRazorpayAmount(c.Req.GetAmount()),
		Currency:       c.Req.GetAmount().GetCurrencyCode(),
		PaymentCapture: recurringPaymentParams.GetPaymentCapture(),
		Method:         vendorGatewayAuthorisationMethodToVendorMethod[recurringPaymentParams.GetAuthorisationMethod()],
		CustomerId:     recurringPaymentParams.GetCustomerId(),
		Receipt:        c.Req.GetReferenceId(),
		Token: &razorpayPb.Token{
			AuthType:    recurringPaymentParams.GetToken().GetAuthType(),
			MaxAmount:   toRazorpayAmount(recurringPaymentParams.GetToken().GetMaxAmount()),
			ExpireAt:    recurringPaymentParams.GetToken().GetExpireAt().AsTime().Unix(),
			BankAccount: bankAccount,
		},
	}
	// using EmitUnpopulated as true because amount will be 0 in case of emandate registration
	// Marshalling without this field removes the 0 values.
	res, err := protojson.MarshalOptions{EmitUnpopulated: false}.Marshal(payload)
	if err != nil {
		return nil, err
	}
	if payload.GetAmount() == 0 {
		payloadJsonMap := make(map[string]interface{})
		unmErr := json.Unmarshal(res, &payloadJsonMap)
		if unmErr != nil {
			return nil, unmErr
		}
		payloadJsonMap["amount"] = 0
		res, err = json.Marshal(payloadJsonMap)
		if err != nil {
			return nil, err
		}
	}
	return res, nil

}
func getRecurringPaymentAuthorisationResponse(razorpayResp *razorpayPb.CreateOrderForRecurringPaymentAuthorisationResponse) *pgPb.CreateOrderResponse {
	resp := &pgPb.CreateOrderResponse{}
	if razorpayResp.GetError() != nil {
		resp.VendorStatus = &commonvgpb.VendorStatus{
			Code:        razorpayResp.GetError().GetCode(),
			Description: getConsolidatedErrorMessage(razorpayResp.GetError()),
		}
	}
	resp.Status = getRPCStatus(razorpayResp.GetError())
	if !resp.GetStatus().IsSuccess() {
		logger.ErrorNoCtx(fmt.Sprintf("received error resp from vendor: %s: %s",
			razorpayResp.GetError().GetCode(), getConsolidatedErrorMessage(razorpayResp.GetError())),
			zap.String(logger.VENDOR_ORDER_ID, razorpayResp.GetId()))
		return resp
	}
	resp.VendorOrderId = razorpayResp.GetId()
	resp.OrderStatus = razorpay.GetVendorOrderStatusEnum(razorpayResp.GetStatus())
	if resp.GetOrderStatus() == pgPb.VendorOrderStatus_VENDOR_ORDER_STATUS_UNSPECIFIED {
		logger.ErrorNoCtx(fmt.Sprintf("unexpected order status in response: %s", razorpayResp.GetStatus()), zap.String(logger.VENDOR_ORDER_ID, razorpayResp.GetId()))
	}
	resp.ReferenceId = razorpayResp.GetReceipt()
	resp.OrderMetadata = &pgPb.OrderMetadata{
		Attempts: razorpayResp.GetAttempts(),
	}
	resp.CreatedAt = timestamp.New(time.Unix(razorpayResp.GetCreatedAt(), 0))
	resp.RecurringPaymentDetails = getRecurringPaymentDetails(razorpayResp)
	return resp
}
func getRecurringPaymentDetails(razorpayResp *razorpayPb.CreateOrderForRecurringPaymentAuthorisationResponse) *pgPb.RecurringPaymentDetails {
	details := &pgPb.RecurringPaymentDetails{}
	tokenResponse := razorpayResp.GetToken()
	details.FirstPaymentAmount = toTypesMoney(tokenResponse.GetFirstPaymentAmount(), tokenResponse.GetCurrency())
	return details
}

func (c *CreateOrderForRecurringPaymentAuthorisationResponse) HandleHttpError(ctx context.Context, httpStatusCode int,
	responseBody []byte) (proto.Message, error) {
	var m razorpayPb.CreateOrderResponse
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(responseBody, &m)
	if err != nil {
		logger.ErrorNoCtx("failed to unmarshall not ok razorpay response", zap.Error(err))
		return &pgPb.CreateOrderResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to unmarshall not ok razorpay response"),
		}, nil
	}
	return getResponse(&m), nil
}
