package abfl

import (
	"net/http"

	"github.com/epifi/be-common/pkg/vendorapi"
)

const (
	authorizationString = "X-AUTH-TOKEN"
)

type HeaderContentSetter struct{}

func (d *HeaderContentSetter) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

type DefaultHeaderSetter struct {
	AuthToken string
}

func (dh *DefaultHeaderSetter) Add(req *http.Request) *http.Request {
	if dh == nil {
		return req
	}
	req.Header.Set(authorizationString, dh.AuthToken)
	return req
}
