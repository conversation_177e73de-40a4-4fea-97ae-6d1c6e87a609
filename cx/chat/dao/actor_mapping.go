package dao

import (
	"context"
	"fmt"
	"time"

	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/cx/chat/dao/model"
)

type RefIdToActorMappingDAO struct {
	db *gormV2.DB
}

func NewRefIdToActorMappingDAO(db types.SherlockPGDB) *RefIdToActorMappingDAO {
	return &RefIdToActorMappingDAO{
		db: db,
	}
}

func (r *RefIdToActorMappingDAO) Create(ctx context.Context, refId string, actorId string) (*model.ReferenceIdToActorMapping, error) {
	defer metric_util.TrackDuration("cx/chat/dao", "RefIdToActorMappingDAO", "Create", time.Now())
	if actorId == "" || refId == "" {
		return nil, fmt.Errorf("reference id and actor id both are mandatory to create mapping")
	}
	msg := &model.ReferenceIdToActorMapping{
		ReferenceId: refId,
		ActorId:     actorId,
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if err := db.Create(msg).Error; err != nil {
		return nil, err
	}
	return msg, nil
}

func (r *RefIdToActorMappingDAO) GetByActorId(ctx context.Context, actorId string) (*model.ReferenceIdToActorMapping, error) {
	defer metric_util.TrackDuration("cx/chat/dao", "RefIdToActorMappingDAO", "GetByActorId", time.Now())
	if actorId == "" {
		return nil, fmt.Errorf("actor id is mandatory to get mapping")
	}
	msg := &model.ReferenceIdToActorMapping{}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if err := db.Model(&model.ReferenceIdToActorMapping{}).Where("actor_id = ?", actorId).First(msg).Error; err != nil {
		return nil, err
	}
	return msg, nil
}

func (r *RefIdToActorMappingDAO) GetByRefId(ctx context.Context, refId string) (*model.ReferenceIdToActorMapping, error) {
	defer metric_util.TrackDuration("cx/chat/dao", "RefIdToActorMappingDAO", "GetByRefId", time.Now())
	if refId == "" {
		return nil, fmt.Errorf("reference id is mandatory to get mapping")
	}
	msg := &model.ReferenceIdToActorMapping{}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if err := db.Model(&model.ReferenceIdToActorMapping{}).Where("reference_id = ?", refId).First(msg).Error; err != nil {
		return nil, err
	}
	return msg, nil
}
