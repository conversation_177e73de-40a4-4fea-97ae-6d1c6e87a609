package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"

	priorityPb "github.com/epifi/gamma/api/cx/priority_routing"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	mock_routing_engine "github.com/epifi/gamma/cx/test/mocks/priority_routing/routing_engine"

	"github.com/epifi/be-common/api/rpc"
	chatPb "github.com/epifi/gamma/api/cx/chat"
	vgFcPb "github.com/epifi/gamma/api/vendorgateway/cx/freshchat"
	mockVgFc "github.com/epifi/gamma/api/vendorgateway/cx/freshchat/mocks"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	mockVm "github.com/epifi/gamma/api/vendormapping/mocks"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	mockDao "github.com/epifi/gamma/cx/test/mocks/chat/dao"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
)

type FreshchatUserMappingTestSuite struct {
	conf *cxGenConf.Config
}

const (
	actorId1		= "actor-id-1"
	freshchatUserId1	= "fc-user-id-1"
	freshdeskId1		= "fd-id-1"
)

var (
	fumTs			FreshchatUserMappingTestSuite
	freshchatVendorHeader	= &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FRESHCHAT}
	freshchatUser1Req	= &vgFcPb.User{
		ReferenceId:	freshdeskId1,
		Properties: []*vgFcPb.Property{
			{Name: "priority", Value: "High"},
			{Name: freshchatUserCreationRoutePropKey, Value: freshchatUserCreationRoutePropValChatbot},
		},
		FirstName:	"FirstName",
		LastName:	"LastName",
		Email:		fmt.Sprintf("%<EMAIL>", freshdeskId1),	// from config fumTs.conf.FreshChatConfig.FreshChatCustomUserEmailFormat
	}
	freshchatUser1Resp	= &vgFcPb.User{
		Id:		freshchatUserId1,
		ReferenceId:	freshdeskId1,
		Email:		fmt.Sprintf("%<EMAIL>", freshdeskId1),	// from config fumTs.conf.FreshChatConfig.FreshChatCustomUserEmailFormat
	}
	freshchatUserMapping1	= &chatPb.FreshchatUserMapping{
		ActorId:		actorId1,
		FreshchatUserId:	freshchatUserId1,
	}
	user1	= &userPb.User{
		Profile: &userPb.Profile{
			KycName: &commontypes.Name{FirstName: "FirstName", LastName: "LastName"},
		},
	}
)

func TestFreshchatUserMappingHelper_GetFreshchatUserByActorId(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	mockFcDao := mockDao.NewMockIFreshchatUserMappingDao(ctr)
	mockVM := mockVm.NewMockVendorMappingServiceClient(ctr)
	mockVgFcClient := mockVgFc.NewMockFreshchatClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)
	mockRoutingEngine := mock_routing_engine.NewMockIRoutingEngine(ctr)

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	*chatPb.FreshchatUserMapping
		wantErr	bool
	}{
		{
			name:	"user mapping found in Db",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).
						Return(&chatPb.FreshchatUserMapping{ActorId: actorId1, FreshchatUserId: freshchatUserId1}, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		&chatPb.FreshchatUserMapping{ActorId: actorId1, FreshchatUserId: freshchatUserId1},
			wantErr:	false,
		},
		{
			name:	"unknown error in getByActorId",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, errors.New("test error")),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"error in vendor mapping GetBEMappingById",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, epifierrors.ErrRecordNotFound),
					mockVM.EXPECT().GetBEMappingById(context.Background(), &vmPb.GetBEMappingByIdRequest{Id: actorId1}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpc.StatusRecordNotFound()}, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"freshdesk id is empty",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, epifierrors.ErrRecordNotFound),
					mockVM.EXPECT().GetBEMappingById(context.Background(), &vmPb.GetBEMappingByIdRequest{Id: actorId1}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpc.StatusOk(), FreshdeskId: ""}, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"error in getting user",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, epifierrors.ErrRecordNotFound),
					mockVM.EXPECT().GetBEMappingById(context.Background(), &vmPb.GetBEMappingByIdRequest{Id: actorId1}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpc.StatusOk(), FreshdeskId: freshdeskId1}, nil),
					mockUserClient.EXPECT().GetUser(context.Background(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: actorId1,
						},
					}).Return(&userPb.GetUserResponse{Status: rpc.StatusInternal()}, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"error in vg freshchat CreateUser",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, epifierrors.ErrRecordNotFound),
					mockVM.EXPECT().GetBEMappingById(context.Background(), &vmPb.GetBEMappingByIdRequest{Id: actorId1}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpc.StatusOk(), FreshdeskId: freshdeskId1}, nil),
					mockUserClient.EXPECT().GetUser(context.Background(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: actorId1,
						},
					}).Return(&userPb.GetUserResponse{Status: rpc.StatusOk(), User: user1}, nil),
					mockRoutingEngine.EXPECT().GetUserPriority(context.Background(), gomock.Any()).Return(priorityPb.UserCategory_HIGH_PRIORITY_ACCOUNT_HOLDERS, nil),
					mockVgFcClient.EXPECT().CreateUser(context.Background(), &vgFcPb.CreateUserRequest{Header: freshchatVendorHeader, User: freshchatUser1Req}).
						Return(&vgFcPb.CreateUserResponse{Status: rpc.StatusInternal()}, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"error in creating entry in db",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, epifierrors.ErrRecordNotFound),
					mockVM.EXPECT().GetBEMappingById(context.Background(), &vmPb.GetBEMappingByIdRequest{Id: actorId1}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpc.StatusOk(), FreshdeskId: freshdeskId1}, nil),
					mockUserClient.EXPECT().GetUser(context.Background(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: actorId1,
						},
					}).Return(&userPb.GetUserResponse{Status: rpc.StatusOk(), User: user1}, nil),
					mockRoutingEngine.EXPECT().GetUserPriority(context.Background(), gomock.Any()).Return(priorityPb.UserCategory_HIGH_PRIORITY_ACCOUNT_HOLDERS, nil),
					mockVgFcClient.EXPECT().CreateUser(context.Background(), &vgFcPb.CreateUserRequest{Header: freshchatVendorHeader, User: freshchatUser1Req}).
						Return(&vgFcPb.CreateUserResponse{Status: rpc.StatusOk(), User: freshchatUser1Resp}, nil),
					mockFcDao.EXPECT().Create(context.Background(), freshchatUserMapping1).Return(nil, errors.New("test err")),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"success: for create new freshchat user",
			args: args{
				mocks: []interface{}{
					mockFcDao.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, epifierrors.ErrRecordNotFound),
					mockVM.EXPECT().GetBEMappingById(context.Background(), &vmPb.GetBEMappingByIdRequest{Id: actorId1}).
						Return(&vmPb.GetBEMappingByIdResponse{Status: rpc.StatusOk(), FreshdeskId: freshdeskId1}, nil),
					mockUserClient.EXPECT().GetUser(context.Background(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: actorId1,
						},
					}).Return(&userPb.GetUserResponse{Status: rpc.StatusOk(), User: user1}, nil),
					mockRoutingEngine.EXPECT().GetUserPriority(context.Background(), gomock.Any()).Return(priorityPb.UserCategory_HIGH_PRIORITY_ACCOUNT_HOLDERS, nil),
					mockVgFcClient.EXPECT().CreateUser(context.Background(), &vgFcPb.CreateUserRequest{Header: freshchatVendorHeader, User: freshchatUser1Req}).
						Return(&vgFcPb.CreateUserResponse{Status: rpc.StatusOk(), User: freshchatUser1Resp}, nil),
					mockFcDao.EXPECT().Create(context.Background(), freshchatUserMapping1).Return(freshchatUserMapping1, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:		freshchatUserMapping1,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshchatUserMappingHelper(fumTs.conf, mockRoutingEngine, mockFcDao, mockUserClient, mockVgFcClient, mockVM)
			got, err := s.GetFreshchatUserByActorId(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFreshchatUserByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetFreshchatUserByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFreshchatUserMappingHelper_GetCustomUserProperties(t *testing.T) {
	t.Parallel()

	ctr := gomock.NewController(t)
	mockVM := mockVm.NewMockVendorMappingServiceClient(ctr)
	mockRoutingEngine := mock_routing_engine.NewMockIRoutingEngine(ctr)

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		actorId	string
	}
	tests := []struct {
		name	string
		args	args
		want	map[string]string
	}{
		{
			name:	"HighPriority User Category",
			args: args{
				mocks: []interface{}{mockRoutingEngine.EXPECT().GetUserPriority(context.Background(), actorId1).
					Return(priorityPb.UserCategory_HIGH_PRIORITY_ACCOUNT_HOLDERS, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:	map[string]string{"priority": "High"},
		},
		{
			name:	"Unspecified User Category",
			args: args{
				mocks: []interface{}{mockRoutingEngine.EXPECT().GetUserPriority(context.Background(), actorId1).
					Return(priorityPb.UserCategory_CATEGORY_UNSPECIFIED, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:	map[string]string{"priority": "UnIdentified"},
		},
		{
			name:	"Low Priority User Category",
			args: args{
				mocks: []interface{}{mockRoutingEngine.EXPECT().GetUserPriority(context.Background(), actorId1).
					Return(priorityPb.UserCategory_LOW_PRIORITY_ACCOUNT_HOLDERS, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:	map[string]string{"priority": "Low"},
		},
		{
			name:	"Currently Onboarding User Category",
			args: args{
				mocks: []interface{}{mockRoutingEngine.EXPECT().GetUserPriority(context.Background(), actorId1).
					Return(priorityPb.UserCategory_CURRENTLY_ONBOARDING_USERS, nil),
				},
				ctx:		context.Background(),
				actorId:	actorId1,
			},
			want:	map[string]string{"priority": "CurrentlyOnboarding"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewFreshchatUserMappingHelper(fumTs.conf, mockRoutingEngine, nil, nil, nil, mockVM)
			got := s.GetCustomUserPropertiesMap(tt.args.ctx, tt.args.actorId)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCustomUserPropertiesMap() got = %v, want %v", got, tt.want)
			}
		})
	}
}
