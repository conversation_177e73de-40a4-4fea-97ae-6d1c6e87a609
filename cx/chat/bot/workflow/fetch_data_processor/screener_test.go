package fetch_data_processor

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"
	chatbotWorkflowPb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/screener/mocks"
)

func TestScreenerAttemptsDataProcessor_FetchData(t *testing.T) {
	t.Parallel()
	var mockScreenerClient *mocks.MockScreenerClient
	type args struct {
		workflowEntity	chatbotWorkflowPb.WorkflowEntity
		params		*chatbotWorkflowPb.FetchDataParameters
	}

	tests := []struct {
		name		string
		args		args
		want		*chatbotWorkflowPb.WorkflowData
		wantErr		error
		mockFunc	func(mockScreenerClient *mocks.MockScreenerClient)
	}{
		{
			name:	"invalid workflow entity",
			args: args{
				workflowEntity:	0,
				params:		nil,
			},
			wantErr:	errors.New("entity must be WORKFLOW_ENTITY_SCREENER_ATTEMPTS"),
		},
		{
			name:	"error from screener service",
			args: args{
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_SCREENER_ATTEMPTS,
				params: &chatbotWorkflowPb.FetchDataParameters{
					EntityParameters: &chatbotWorkflowPb.FetchDataParameters_ScreenerAttemptsDataParameters{
						ScreenerAttemptsDataParameters: &chatbotWorkflowPb.ScreenerAttemptsDataParameters{
							ActorId: actorId,
						},
					},
				},
			},
			mockFunc: func(mocScreenerClient *mocks.MockScreenerClient) {
				mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
					ActorId:	actorId,
					CachedData:	true,
				}).Return(nil, errors.New("some error"))
			},
			wantErr:	errors.New("some error"),
		},
		{
			name:	"successfully get",
			args: args{
				workflowEntity:	chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_SCREENER_ATTEMPTS,
				params: &chatbotWorkflowPb.FetchDataParameters{
					EntityParameters: &chatbotWorkflowPb.FetchDataParameters_ScreenerAttemptsDataParameters{
						ScreenerAttemptsDataParameters: &chatbotWorkflowPb.ScreenerAttemptsDataParameters{
							ActorId: actorId,
						},
					},
				},
			},
			mockFunc: func(mocScreenerClient *mocks.MockScreenerClient) {
				mockScreenerClient.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screener.GetScreenerAttemptsByActorIdRequest{
					ActorId:	actorId,
					CachedData:	true,
				}).Return(&screener.GetScreenerAttemptsByActorIdResponse{
					Status:			rpc.StatusOk(),
					ScreenerAttempt:	nil,
					ChecksMap: []*screener.CheckDetails{
						{
							CheckResult:	screener.CheckResult_CHECK_RESULT_PASSED,
							CheckType:	screener.CheckType_CHECK_TYPE_GMAIL_INSIGHTS,
							RetriesLeft:	1,
						},
						{
							CheckResult:	screener.CheckResult_CHECK_RESULT_PASSED,
							CheckType:	screener.CheckType_CHECK_TYPE_EPFO,
							RetriesLeft:	1,
						},
					},
				}, nil)
			},
			wantErr:	nil,
			want: &chatbotWorkflowPb.WorkflowData{
				Data: &chatbotWorkflowPb.WorkflowData_ScreenerAttemptsData{
					ScreenerAttemptsData: &chatbotWorkflowPb.ScreenerAttemptsData{
						CheckDetails: []*screener.CheckDetails{
							{
								CheckResult:	screener.CheckResult_CHECK_RESULT_PASSED,
								CheckType:	screener.CheckType_CHECK_TYPE_GMAIL_INSIGHTS,
								RetriesLeft:	1,
							},
							{
								CheckResult:	screener.CheckResult_CHECK_RESULT_PASSED,
								CheckType:	screener.CheckType_CHECK_TYPE_EPFO,
								RetriesLeft:	1,
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockScreenerClient = mocks.NewMockScreenerClient(ctr)
			if tt.mockFunc != nil {
				tt.mockFunc(mockScreenerClient)
			}

			s := NewScreenerAttemptsProcessor(mockScreenerClient)
			got, err := s.FetchData(context.Background(), tt.args.workflowEntity, tt.args.params)
			assert.Equal(t, tt.want, got)
			if tt.wantErr == nil {
				assert.Nil(t, err)
			} else {
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			}
		})
	}
}
