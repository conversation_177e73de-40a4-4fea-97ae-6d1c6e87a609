//nolint:all
package processor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	actorPb "github.com/epifi/gamma/api/actor"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	reconPb "github.com/epifi/gamma/api/order/recon"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
)

type ReconProcessor struct {
	savingsClient savingsPb.SavingsClient
	reconClient   reconPb.LedgerReconciliationClient
	actorClient   actorPb.ActorClient
}

func NewReconProcessor(
	savingsClient savingsPb.SavingsClient,
	reconClient reconPb.LedgerReconciliationClient,
	actorClient actorPb.ActorClient,
) *ReconProcessor {
	return &ReconProcessor{
		savingsClient: savingsClient,
		reconClient:   reconClient,
		actorClient:   actorClient,
	}
}

func (r *ReconProcessor) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	var paramList []*db_state.ParameterMeta

	switch action {
	case actionPb.DeveloperActions_FORCE_TRIGGER_RECON:
		paramList = []*dsPb.ParameterMeta{
			{
				Name:            savingsAccountId,
				Label:           savingsAccountIdLabel,
				Type:            dsPb.ParameterDataType_STRING,
				ParameterOption: dsPb.ParameterOption_OPTIONAL,
			},
			{
				Name:            savingsAccountNo,
				Label:           savingsAccountNoLabel,
				Type:            dsPb.ParameterDataType_STRING,
				ParameterOption: dsPb.ParameterOption_OPTIONAL,
			},
			{
				Name:            reconStartTimestamp,
				Label:           reconStartTimestampLabel,
				Type:            dsPb.ParameterDataType_TIMESTAMP,
				ParameterOption: dsPb.ParameterOption_MANDATORY,
			},
		}
	default:
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid action: %s", action.String()))
	}

	return paramList, nil
}

func (r *ReconProcessor) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", status.Error(codes.InvalidArgument, "filter can't be null")
	}

	switch action {
	case actionPb.DeveloperActions_FORCE_TRIGGER_RECON:
		return r.forceTriggerReconProcessor(ctx, filters)
	default:
		return "", status.Error(codes.InvalidArgument, fmt.Sprintf("invalid action: %s", action.String()))
	}
}

func (r *ReconProcessor) forceTriggerReconProcessor(ctx context.Context, filters []*db_state.Filter) (string, error) {
	var (
		accountId    string
		accountNo    string
		reconStartTs *timestampPb.Timestamp
		getAcctReq   *savingsPb.GetAccountRequest
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case savingsAccountId:
			accountId = filter.GetStringValue()
		case savingsAccountNo:
			accountNo = filter.GetStringValue()
		case reconStartTimestamp:
			reconStartTs = filter.GetTimestamp()
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	switch {
	case accountId != "":
		getAcctReq = &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_Id{
				Id: accountId,
			},
		}
	case accountNo != "":
		getAcctReq = &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_AccountNumBankFilter{
				AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
					AccountNumber: accountNo,
					PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		}
	}

	savingsRes, err := r.savingsClient.GetAccount(ctx, getAcctReq)
	if err != nil {
		logger.Error(ctx, "error in fetching saving account", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return jsonErrResponse(err)
	}

	savings := savingsRes.GetAccount()
	openingTime := savings.GetCreatedAt().AsTime()

	reconResetRes, err := r.reconClient.SetStartTime(ctx, &reconPb.SetStartTimeRequest{
		SavingsAccountId: savings.GetId(),
		ReconStartTime:   reconStartTs,
	})
	if err != nil {
		logger.Error(ctx, "failed to reset recon timestamp", logger.AccountId(savings.GetId()), zap.Error(err))
		return jsonErrResponse(err)
	}
	if !reconResetRes.GetStatus().IsSuccess() && !reconResetRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "ResetReconStartTime returned non-ok", logger.AccountId(savings.GetId()))
		b, err := protojson.Marshal(reconResetRes)
		return string(b), err
	}

	actorRes, err := r.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{EntityId: savings.GetPrimaryAccountHolder(), Type: types.Actor_USER})
	if err != nil {
		logger.Error(ctx, "failed to fetch actor", zap.String(logger.ENTITY_ID, savings.GetPrimaryAccountHolder()), zap.Error(err))
		return jsonErrResponse(err)
	}
	if !actorRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "failed to fetch actor", zap.String(logger.ENTITY_ID, savings.GetPrimaryAccountHolder()))
		b, err := protojson.Marshal(actorRes)
		return string(b), err
	}

	reconRes, err := r.reconClient.ReconcileAccountLedger(ctx, &reconPb.ReconcileAccountLedgerRequest{
		SavingsAccountId:     savings.GetId(),
		PartnerBank:          savings.GetPartnerBank(),
		AccountNo:            savings.GetAccountNo(),
		PrimaryAccountHolder: savings.GetPrimaryAccountHolder(),
		AccOpeningDate:       datetime.TimeToDate(&openingTime),
		ActorId:              actorRes.GetActor().GetId(),
	})
	if err != nil {
		logger.Error(ctx, "failed to trigger recon", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return jsonErrResponse(err)
	}

	b, err := protojson.Marshal(reconRes)
	return string(b), err
}

func jsonErrResponse(err error) (string, error) {
	if jsonErrRes, err2 := json.Marshal(map[string]string{
		"error": err.Error(),
	}); err2 == nil {
		return string(jsonErrRes), nil
	}
	return "", err
}
