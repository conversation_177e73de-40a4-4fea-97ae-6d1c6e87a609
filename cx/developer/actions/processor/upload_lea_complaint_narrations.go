//nolint:all
package processor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/gocarina/gocsv"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	depositPb "github.com/epifi/gamma/api/deposit"
	riskPb "github.com/epifi/gamma/api/risk"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	savingsPb "github.com/epifi/gamma/api/savings"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/risk_ops/review/annotations"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	leaComplaintNarrationsCSVFile    = "lea_complaint_narrations_file"
	maxLEAComplaintNarrationsAllowed = 1000
)

type UploadLEAComplaintNarrations struct {
	riskClient        riskPb.RiskClient
	savingsClient     savingsPb.SavingsClient
	annotationsHelper annotations.IAnnotationsHelper
}

func NewUploadLEAComplaintNarrations(riskClient riskPb.RiskClient, savingsClient savingsPb.SavingsClient,
	annotationsHelper annotations.IAnnotationsHelper) *UploadLEAComplaintNarrations {
	return &UploadLEAComplaintNarrations{
		riskClient:        riskClient,
		savingsClient:     savingsClient,
		annotationsHelper: annotationsHelper,
	}
}

type failedLeaComplaintNarration struct {
	Row int
	Err error
}

type LeaComplaintNarration struct {
	Narration string `csv:"Complaint Narration"`
	MO        string `csv:"MO"`
	SubMO     string `csv:"Sub MO"`
	AcctNum   string `csv:"Account_Number"`
}

func (u *UploadLEAComplaintNarrations) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            leaComplaintNarrationsCSVFile,
			Label:           "Narrations File",
			Type:            dsPb.ParameterDataType_FILE,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (u *UploadLEAComplaintNarrations) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	var (
		leaNarrationsFile *dsPb.File
		analystEmail      string
	)
	analystEmail = epificontext.AgentEmailFromContext(ctx)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case leaComplaintNarrationsCSVFile:
			leaNarrationsFile = filter.GetFile()
		default:
			cxLogger.Error(ctx, "invalid filter", zap.String(logger.DEV_ACTION_FILTER, filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	narrationsCSVRows, err := readLEANarrationsCSV(leaNarrationsFile.GetContent())
	switch {
	case err != nil:
		cxLogger.Error(ctx, "error while reading csv file", zap.Error(err))
		return fmt.Sprintf("Complete Failure: Failed to parse CSV file %s", err), fmt.Errorf("failed to parse CSV file %w", err)
	case len(narrationsCSVRows) > maxLEAComplaintNarrationsAllowed:
		return fmt.Sprintf("Complete Failure: can't upload more than %d narrations", maxLEAComplaintNarrationsAllowed),
			fmt.Errorf("can't upload more than %d narrations", maxLEAComplaintNarrationsAllowed)
	}

	var failedLeaComplaintNarrations []*failedLeaComplaintNarration
	narrationsDetails, err := u.convertCSVToProto(ctx, narrationsCSVRows)
	if err != nil {
		return fmt.Sprintf("Complete Failure: Failed to parse csv %s", err),
			fmt.Errorf("complete Failure: failed to parse csv %w", err)
	}

	errGrp, grpCtx := errgroup.WithContext(ctx)
	failuresListMux := sync.Mutex{}
	for i, narrationDetails := range narrationsDetails {
		func(i int, narrationDetails *riskPb.LEAComplaintNarrationDetails) {
			errGrp.Go(func() error {
				resp, processErr := u.riskClient.ProcessLEAComplaintNarration(grpCtx, &riskPb.ProcessLEAComplaintNarrationRequest{
					LeaComplaintNarrationDetails: narrationDetails,
					AnalystEmail:                 analystEmail,
				})
				if processErr = epifigrpc.RPCError(resp, processErr); processErr != nil {
					failuresListMux.Lock()
					failedLeaComplaintNarrations = append(failedLeaComplaintNarrations, &failedLeaComplaintNarration{
						Row: i + 2,
						Err: processErr,
					})
					failuresListMux.Unlock()
				}
				return nil
			})
		}(i, narrationDetails)
	}
	_ = errGrp.Wait()

	switch {
	case len(failedLeaComplaintNarrations) == 0:
		return "{\"msg\": \" Success \"}", nil
	default:
		cxLogger.Error(ctx, "Failed narrations", zap.Any(logger.FAILURES, failedLeaComplaintNarrations))
		resStr, err := json.Marshal(failedLeaComplaintNarrations)
		if err != nil {
			return fmt.Sprintf("Partial failure, check logs for failures. "+
				"failed to marshal failed narrations %s", err.Error()), fmt.Errorf("failed to marshal")
		}
		return fmt.Sprintf("Partial failure, please re-upload only failed narrations. Failed narrations: %s", string(resStr)), nil
	}
}

func (u *UploadLEAComplaintNarrations) convertCSVToProto(ctx context.Context, narrationCSVRows []*LeaComplaintNarration) ([]*riskPb.LEAComplaintNarrationDetails, error) {
	var leaComplaintNarrationsDetails []*riskPb.LEAComplaintNarrationDetails

	errGrp, grpCtx := errgroup.WithContext(ctx)
	narrationsListMux := sync.Mutex{}
	for row, narrationCSVRow := range narrationCSVRows {
		func(row int, narrationCSVRow *LeaComplaintNarration) {
			errGrp.Go(func() error {
				if len(narrationCSVRow.MO) == 0 || len(narrationCSVRow.Narration) == 0 || len(narrationCSVRow.AcctNum) == 0 {
					return fmt.Errorf("mandatory params are missing for row %d", row+1)
				}
				account, err := getSavingsAccount(grpCtx, u.savingsClient, narrationCSVRow.AcctNum)
				if err != nil {
					return fmt.Errorf("failed to fetch savings account for row: %d %w", row+1, err)
				}

				allowedAnnotations, err := u.getAllowedAnnotationsForMOs(grpCtx, narrationCSVRow.MO, narrationCSVRow.SubMO)
				if err != nil {
					return fmt.Errorf("failed to allowed annotations for row: %d %w", row+1, err)
				}
				narrationsListMux.Lock()
				leaComplaintNarrationsDetails = append(leaComplaintNarrationsDetails, &riskPb.LEAComplaintNarrationDetails{
					Narration: &riskPb.LEAComplaintNarration{
						ActorId:   account.GetActorId(),
						AccountId: account.GetId(),
						Narration: narrationCSVRow.Narration,
					},
					MOs: allowedAnnotations,
				})
				narrationsListMux.Unlock()
				return nil
			})
		}(row, narrationCSVRow)
	}
	grpErr := errGrp.Wait()
	if grpErr != nil {
		return nil, grpErr
	}
	return leaComplaintNarrationsDetails, nil
}

func (u *UploadLEAComplaintNarrations) getAllowedAnnotationsForMOs(ctx context.Context, mo, subMO string) ([]*reviewPb.AllowedAnnotation, error) {
	MOs := []string{mo}
	if len(subMO) != 0 {
		MOs = append(MOs, subMO)
	}
	allowedAnnotations, err := u.annotationsHelper.GetAllowedAnnotationsByValues(ctx, reviewPb.ReviewEntityType_REVIEW_ENTITY_TYPE_LEA_COMPLAINT,
		&reviewPb.AnnotationType{AnnotationType: &reviewPb.AnnotationType_LeaComplaintAnnotationType{
			LeaComplaintAnnotationType: reviewPb.LEAComplaintAnnotationType_LEA_COMPLAINT_ANNOTATION_TYPE_NARRATION_MO,
		}}, MOs)
	if err != nil {
		return nil, fmt.Errorf("failed to allowed annotations for values: %v", MOs)
	}
	return allowedAnnotations, nil
}

func getSavingsAccount(ctx context.Context, savingsClient savingsPb.SavingsClient, accountNum string) (*savingsPb.SavingsAccountEssentials, error) {
	resp, err := savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_AccountNumBankFilter{
			AccountNumBankFilter: &savingsPb.AccountNumberBankFilter{
				AccountNumber: accountNum,
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("savings account does not exist %w", epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("failed to fetch savings account")
	}
	return resp.GetAccount(), nil
}

func getDepositAccount(ctx context.Context, depositClient depositPb.DepositClient, accountNum string,
	soldId string) (*depositPb.DepositAccount, error) {
	ifscCode, ok := solIdToIFSCCodeMap[soldId]
	if !ok {
		return nil, fmt.Errorf("ifsc code not found for sol id %s", soldId)
	}
	depositResp, err := depositClient.GetByAccountNumberAndIfsc(ctx, &depositPb.GetByAccountNumberAndIfscRequest{
		AccountNumber: accountNum,
		IfscCode:      ifscCode,
	})
	if err = epifigrpc.RPCError(depositResp, err); err != nil {
		return nil, fmt.Errorf("failed to fetch deposit account for account no: %s %w", accountNum, err)
	}
	return depositResp.GetAccount(), nil
}

func readLEANarrationsCSV(file []byte) ([]*LeaComplaintNarration, error) {
	if len(file) == 0 {
		return nil, errors.New("file is empty")
	}

	var leaComplaintNarrations []*LeaComplaintNarration
	err := gocsv.Unmarshal(bytes.NewReader(file), &leaComplaintNarrations)
	if err != nil {
		return nil, errors.Wrap(err, "error while unmarshalling csv file into struct")
	}

	return leaComplaintNarrations, nil
}
