//nolint:all
package processor

import (
	"context"
	"encoding/json"
	"errors"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	palDevPb "github.com/epifi/gamma/api/preapprovedloan/developer"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const (
	loanProgram = "loan_program"
	vendorTypes = "vendor"
)

type PreApprovedLoanCreateOffer struct {
	preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient
	paldevClient          palDevPb.DevPreApprovedLoanClient
}

func NewPreApprovedLoanCreateOffer(preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient, paldevClient palDevPb.DevPreApprovedLoanClient) *PreApprovedLoanCreateOffer {
	return &PreApprovedLoanCreateOffer{
		preApprovedLoanClient: preApprovedLoanClient,
		paldevClient:          paldevClient,
	}
}

func (p *PreApprovedLoanCreateOffer) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	var loanPrograms, vendors []string
	for _, val := range preApprovedLoanPb.LoanProgram_name {
		loanPrograms = append(loanPrograms, val)
	}
	for _, val := range preApprovedLoanPb.Vendor_name {
		vendors = append(vendors, val)
	}

	paramList := []*dsPb.ParameterMeta{
		{
			Name:            ActorId,
			Label:           "Actor ID",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            loanProgram,
			Label:           "Loan Program",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         loanPrograms,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            vendorTypes,
			Label:           "Vendor",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         vendors,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (p *PreApprovedLoanCreateOffer) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var userActorId string
	var offerLoanProgram preApprovedLoanPb.LoanProgram
	var vendorType preApprovedLoanPb.Vendor

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			userActorId = filter.GetStringValue()
			ctx = epificontext.CtxWithActorId(ctx, userActorId)
		case loanProgram:
			offerLoanProgram = preApprovedLoanPb.LoanProgram(preApprovedLoanPb.LoanProgram_value[filter.GetDropdownValue()])
		case vendorTypes:
			vendorType = preApprovedLoanPb.Vendor(preApprovedLoanPb.Vendor_value[filter.GetDropdownValue()])
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	logger.Info(ctx, "create offer constraints received", zap.String("vendor", vendorType.String()), zap.String("loan_program", offerLoanProgram.String()), zap.String(logger.ACTOR_ID_V2, userActorId))

	res, err := p.paldevClient.CreateLoanOffer(ctx, &palDevPb.CreateLoanOfferRequest{
		ActorId: userActorId,
		LoanHeader: &preApprovedLoanPb.LoanHeader{
			LoanProgram: offerLoanProgram,
			Vendor:      vendorType,
		},
	})
	if er := epifigrpc.RPCError(res, err); er != nil {
		logger.Error(ctx, "error creating a loan offer", zap.Error(er), zap.String("vendor", vendorType.String()))
		return "creating loan offer erred out", nil
	}
	resp := res

	// returned marshalled response
	marshalledRes, err := json.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "error while marshalling API response", zap.Error(err))
		return "marshalling response error", nil
	}
	return string(marshalledRes), nil
}
