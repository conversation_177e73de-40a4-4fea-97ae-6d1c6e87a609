//nolint:all
package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"go.uber.org/zap"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	mediaPb "github.com/epifi/gamma/api/inapphelp/media"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type AddMediaPlaylist struct {
	inAppHelpMediaClient mediaPb.InAppHelpMediaClient
}

func NewAddMediaPlaylist(inAppHelpMediaClient mediaPb.InAppHelpMediaClient) *AddMediaPlaylist {
	return &AddMediaPlaylist{
		inAppHelpMediaClient: inAppHelpMediaClient,
	}
}

const (
	PlaylistTitle       = "playlist_title"
	PlaylistDescription = "playlist_description"
	PlaylistIcon        = "playlist_icon"
	PlaylistThumbnail   = "playlist_thumbnail"
	PlaylistVisibility  = "playlist_visibility"
)

func (s *AddMediaPlaylist) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {

	var visibilityOptions []string
	for key := range mediaPb.ContentVisibility_value {
		visibilityOptions = append(visibilityOptions, key)
	}

	paramList := []*dsPb.ParameterMeta{
		{
			Name:            PlaylistTitle,
			Label:           "Playlist Title",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            PlaylistDescription,
			Label:           "Playlist Description",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            PlaylistIcon,
			Label:           "Playlist Icon",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            PlaylistThumbnail,
			Label:           "Playlist Thumbnail",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            PlaylistVisibility,
			Label:           "Playlist Visibility",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         visibilityOptions,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (s *AddMediaPlaylist) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {

	res := struct {
		Message    string
		PlaylistId string
	}{}

	var playlistTitle, playlistDescription, playlistIcon, playlistThumbnail string
	playlistVisibility := mediaPb.ContentVisibility_CONTENT_VISIBILITY_UNSPECIFIED

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case PlaylistTitle:
			playlistTitle = filter.GetStringValue()
		case PlaylistDescription:
			playlistDescription = filter.GetStringValue()
		case PlaylistIcon:
			playlistIcon = filter.GetStringValue()
		case PlaylistThumbnail:
			playlistThumbnail = filter.GetStringValue()
		case PlaylistVisibility:
			v := filter.GetDropdownValue()
			val, ok := mediaPb.ContentVisibility_value[v]
			if !ok {
				return "", fmt.Errorf("invalid content visibility value passed in filters")
			}
			playlistVisibility = mediaPb.ContentVisibility(val)
		}
	}

	mediaPlaylist := &mediaPb.MediaPlaylist{
		Title:                     playlistTitle,
		Description:               playlistDescription,
		Icon:                      playlistIcon,
		Thumbnail:                 playlistThumbnail,
		PlaylistContentVisibility: playlistVisibility,
	}

	resp, err := s.inAppHelpMediaClient.AddPlaylist(ctx, &mediaPb.AddPlaylistRequest{
		MediaPlaylist: mediaPlaylist,
	})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "error while adding playlist", zap.Error(err))
		return "", err
	}
	res.Message = "Successfully added playlist"
	res.PlaylistId = resp.GetPlaylistId()
	resStr, err := json.Marshal(&res)
	if err != nil {
		return "", err
	}
	return string(resStr), nil
}
