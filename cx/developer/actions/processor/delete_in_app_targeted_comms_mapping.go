//nolint:dupl
//nolint:all
package processor

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"strings"

	dePb "github.com/epifi/gamma/api/dynamic_elements"

	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/inapphelp/faq/enums"
	faqPb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
)

type DeleteInAppTargetedCommsMapping struct {
	inAppTcClient          tcPb.InAppTargetedCommsClient
	inAppHelpServingClient faqPb.ServeFAQClient
}

func NewDeleteInAppTargetedCommsMapping(inAppTcClient tcPb.InAppTargetedCommsClient, inAppHelpServingClient faqPb.ServeFAQClient) *DeleteInAppTargetedCommsMapping {
	return &DeleteInAppTargetedCommsMapping{
		inAppTcClient:          inAppTcClient,
		inAppHelpServingClient: inAppHelpServingClient,
	}
}

const (
	TcMappingRowIds    = "mapping_row_ids"
	TcElementIds       = "element_ids"
	TcAppDetailsRowIds = "app_details_row_ids"
	UserTag            = "user_tag"
	Screen             = "Screen"
)

func (d *DeleteInAppTargetedCommsMapping) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*db_state.ParameterMeta, error) {
	var userTagOptions []string
	for key := range tcPb.UserTag_value {
		userTagOptions = append(userTagOptions, key)
	}
	var screenOptions []string
	for key := range deeplinkPb.Screen_value {
		// for the screens having meta value
		if containsStr(tcMetaScreensList, key) {
			screenOptions = append(screenOptions, d.getScreenWithMetaOptionsList(ctx, key)...)
		} else {
			screenOptions = append(screenOptions, key)
		}
	}
	var appPlatformOptions []string
	for key := range commontypes.Platform_value {
		appPlatformOptions = append(appPlatformOptions, key)
	}
	paramList := []*db_state.ParameterMeta{
		{
			Name:            TcMappingRowIds,
			Label:           "comma separated Row IDs of the mappings to be deleted",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            TcElementIds,
			Label:           "comma separated Element IDs: deletes mappings from both mapping tables",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            TcAppDetailsRowIds,
			Label:           "comma separated row Ids for app details mapping table:",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            UserTag,
			Label:           "User Tag:",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			Options:         userTagOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Screen,
			Label:           "Screens:",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			Options:         screenOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},

		{
			Name:            TcPlatforms,
			Label:           "App Platforms:",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			Options:         appPlatformOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

//nolint:gocritic,funlen
func (d *DeleteInAppTargetedCommsMapping) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*db_state.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	var (
		mappingRowIds, elementIds, appDetailsRowIds []string
		mappingDetailsList                          []*tcPb.MappingDetails
		appPlatformList                             []commontypes.Platform
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case TcMappingRowIds:
			idsString := filter.GetStringValue()
			mappingRowIds = strings.Split(idsString, ",")
			for idx := range mappingRowIds {
				mappingRowIds[idx] = strings.TrimSpace(mappingRowIds[idx])
			}
		case TcElementIds:
			idsString := filter.GetStringValue()
			elementIds = strings.Split(idsString, ",")
			for idx := range elementIds {
				elementIds[idx] = strings.TrimSpace(elementIds[idx])
			}
		case TcAppDetailsRowIds:
			idsString := filter.GetStringValue()
			appDetailsRowIds = strings.Split(idsString, ",")
			for idx := range appDetailsRowIds {
				appDetailsRowIds[idx] = strings.TrimSpace(appDetailsRowIds[idx])
			}
		case UserTag:
			for _, v := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				val, ok := tcPb.UserTag_value[v]
				if !ok {
					return "", errors.New("invalid user tag passed in filters")
				}
				if val != 0 {
					mappingDetailsList = append(mappingDetailsList,
						&tcPb.MappingDetails{MappingType: tcPb.MappingType_MAPPING_TYPE_USER_TAG, MappedValue: v})
				}
			}
		case Screen:
			for _, v := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				screen, metaStr, err := parseScreenOptionString(v)
				if err != nil {
					cxLogger.Error(ctx, "error while parsing screen option string", zap.Error(err))
					return "", err
				}
				mappingDetailsList = append(mappingDetailsList, &tcPb.MappingDetails{
					MappingType:     tcPb.MappingType_MAPPING_TYPE_SCREEN,
					MappedValue:     screen.String(),
					MappedValueMeta: metaStr,
				})
				// For backward compatibility, add HOME without meta when HOME|SECTION_BODY is selected
				// TODO: can be removed once the client implements querying on meta for HOME|SECTION_BODY
				if screen == deeplinkPb.Screen_HOME && metaStr == dePb.HomeScreenAdditionalInfo_SECTION_BODY.String() {
					mappingDetailsList = append(mappingDetailsList, &tcPb.MappingDetails{
						MappingType: tcPb.MappingType_MAPPING_TYPE_SCREEN,
						MappedValue: screen.String(),
					})
				}
			}
		case TcPlatforms:
			for _, v := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				val, ok := commontypes.Platform_value[v]
				if !ok {
					return "", status.Error(codes.InvalidArgument, "invalid app platform")
				}
				appPlatformList = append(appPlatformList, commontypes.Platform(val))
			}
		}
	}
	resp, err := d.inAppTcClient.DeleteTargetedCommsMapping(ctx, &tcPb.DeleteTargetedCommsMappingRequest{
		MappingRowIdList:    mappingRowIds,
		ElementIdList:       elementIds,
		MappingDetailsList:  mappingDetailsList,
		AppDetailsRowIdList: appDetailsRowIds,
		AppPlatformsList:    appPlatformList,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "error while deleting targeted comms mapping", zap.Error(err))
		return "", err
	}
	marshalOptions := protojson.MarshalOptions{UseEnumNumbers: false, EmitUnpopulated: true, Multiline: true}
	resStr, err := marshalOptions.Marshal(resp)
	if err != nil {
		// since action is done, do not return error
		return "", nil
	}
	return string(resStr), nil
}

func (d *DeleteInAppTargetedCommsMapping) getScreenWithMetaOptionsList(ctx context.Context, screen string) []string {
	switch screen {
	case deeplinkPb.Screen_HOME.String():
		return d.getHomeScreenMetaOptions()
	case deeplinkPb.Screen_FAQ_CATEGORY.String():
		return d.getFAQCategoryMetaOptions(ctx)
	default:
		return nil
	}
}

func (d *DeleteInAppTargetedCommsMapping) getFAQCategoryMetaOptions(ctx context.Context) []string {
	faqResp, err := d.inAppHelpServingClient.GetAllCategories(ctx, &faqPb.GetAllCategoriesRequest{FaqFetchSource: enums.FaqFetchSource_APP})
	if te := epifigrpc.RPCError(faqResp, err); te != nil {
		cxLogger.Error(ctx, "error calling backend FAQ service", zap.Error(te))
	}
	var metaOptions []string
	for _, category := range faqResp.GetCategories() {
		metaOptions = append(metaOptions, getFAQCategoryOptionString(category.GetId(), category.GetName()))
	}
	return metaOptions
}

func (d *DeleteInAppTargetedCommsMapping) getHomeScreenMetaOptions() []string {
	var metaOptions []string
	for section := range dePb.HomeScreenAdditionalInfo_Section_value {
		if dePb.HomeScreenAdditionalInfo_Section_value[section] == 0 {
			continue
		}
		for version := range dePb.HomeScreenAdditionalInfo_Version_value {
			if dePb.HomeScreenAdditionalInfo_Version_value[version] == 0 {
				continue
			}
			metaOptions = append(metaOptions, getHomeMetaOptionString(version, section))
		}
	}
	// Add HOME option for the existing HOME version V1 (backward compatibility)
	metaOptions = append(metaOptions, deeplinkPb.Screen_HOME.String())
	return metaOptions
}
