//nolint:all
package processor

import (
	"context"
	"encoding/json"
	"errors"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	ccCxPb "github.com/epifi/gamma/api/firefly/cx"
	ccEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type CreditCardUpdateCardRequestStageStatus struct {
	ccCxClient ccCxPb.CxClient
}

func NewCreditCardUpdateCardRequestStageStatus(
	ccCxClient ccCxPb.CxClient,
) *CreditCardUpdateCardRequestStageStatus {
	return &CreditCardUpdateCardRequestStageStatus{
		ccCxClient: ccCxClient,
	}
}

const (
	CardRequestStageId            = "card_request_stage_id"
	UpdatedCardRequestStageStatus = "updated_card_request_stage_status"
)

func (c *CreditCardUpdateCardRequestStageStatus) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	cardReqStageStatuses := getCardRequestStageStatuses()

	paramList := []*dsPb.ParameterMeta{
		{
			Name:            CardRequestStageId,
			Label:           "Card request stage id",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            UpdatedCardRequestStageStatus,
			Label:           "Updated Card Request stage status",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         cardReqStageStatuses,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (c *CreditCardUpdateCardRequestStageStatus) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var (
		cardRequestStageId            string
		updatedCardRequestStageStatus ccEnumsPb.CardRequestStageStatus
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case CardRequestStageId:
			cardRequestStageId = filter.GetStringValue()
		case UpdatedCardRequestStageStatus:
			updatedCardRequestStageStatus = ccEnumsPb.CardRequestStageStatus(ccEnumsPb.CardRequestStageStatus_value[filter.GetDropdownValue()])
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	req := &ccCxPb.UpdateCardRequestStageStatusRequest{
		CardRequestStageId:            cardRequestStageId,
		UpdatedCardRequestStageStatus: updatedCardRequestStageStatus,
	}

	updateResponse, updateError := c.ccCxClient.UpdateCardRequestStageStatus(ctx, req)
	if err := epifigrpc.RPCError(updateResponse, updateError); err != nil {
		logger.Error(ctx, "error updating card request stage status", zap.Error(err))
		return "Error updating card request stage status", nil
	}

	// returned marshalled response
	marshalledRes, err := json.Marshal(updateResponse)
	if err != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err))
		return "Error while marshalling response", nil
	}
	return string(marshalledRes), nil
}

func getCardRequestStageStatuses() []string {
	var cardReqStageStatuses []string
	for _, cardReqStageStatus := range ccEnumsPb.CardRequestStageStatus_name {
		if cardReqStageStatus == ccEnumsPb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_UNSPECIFIED.String() {
			continue
		}
		cardReqStageStatuses = append(cardReqStageStatuses, cardReqStageStatus)
	}
	return cardReqStageStatuses
}
