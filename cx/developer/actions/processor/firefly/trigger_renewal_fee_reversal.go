package processor

import (
	"context"
	"errors"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/firefly"
)

type TriggerUnsecuredCCRenewalFeeReversal struct {
	fireflyClient firefly.FireflyClient
}

func NewTriggerUnsecuredCCRenewalFeeReversal(
	fireflyClient firefly.FireflyClient,
) *TriggerUnsecuredCCRenewalFeeReversal {
	return &TriggerUnsecuredCCRenewalFeeReversal{
		fireflyClient: fireflyClient,
	}
}

const (
	ActorId = "actor_id"
	Reason  = "Reason"
)

func (c *TriggerUnsecuredCCRenewalFeeReversal) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            ActorId,
			Label:           "Actor Id",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            Reason,
			Label:           "Reason",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (c *TriggerUnsecuredCCRenewalFeeReversal) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	return "", errors.New("not implemented")
}
