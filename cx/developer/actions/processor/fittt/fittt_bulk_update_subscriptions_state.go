package fittt

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type BulkUpdateSubscriptionsState struct {
	rmsClient manager.RuleManagerClient
}

var (
	stateChangeReasons      []string
	targetSubscriptionState []string
	filterSubscriptionState []string
)

const (
	stateChangeReasonIdentifier = "state_change_reason"
	targetStateIdentifier       = "target_state"
	stateChangeReasonFilter     = "state_change_reason_filter"
	stateFilter                 = "StateFilter"
)

func NewBulkUpdateSubscriptionsState(rmsClient manager.RuleManagerClient) *BulkUpdateSubscriptionsState {
	for reason := range manager.SubscriptionStateChangeReason_value {
		if reason == manager.SubscriptionStateChangeReason_UNSPECIFIED.String() {
			continue
		}
		stateChangeReasons = append(stateChangeReasons, reason)
	}

	for state := range manager.RuleSubscriptionState_value {
		if state == manager.RuleSubscriptionState_RULE_SUBSCRIPTION_STATE_UNSPECIFIED.String() {
			continue
		}
		targetSubscriptionState = append(targetSubscriptionState, state)
	}

	for state := range manager.RuleSubscriptionState_value {
		if state == manager.RuleSubscriptionState_RULE_SUBSCRIPTION_STATE_UNSPECIFIED.String() || state == manager.RuleSubscriptionState_CLOSED.String() {
			continue
		}
		filterSubscriptionState = append(filterSubscriptionState, state)
	}

	return &BulkUpdateSubscriptionsState{rmsClient: rmsClient}
}

func (c *BulkUpdateSubscriptionsState) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {

	paramList := []*dsPb.ParameterMeta{
		{
			Name:            ruleIdIdentifier,
			Label:           "Rule Id",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
		{
			Name:            stateChangeReasonIdentifier,
			Label:           "State Change Reason",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         stateChangeReasons,
		},
		{
			Name:            targetStateIdentifier,
			Label:           "Target State",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
			Options:         targetSubscriptionState,
		},
		{
			Name:            stateFilter,
			Label:           "Subscription with existing state to be updated",
			Type:            dsPb.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options:         filterSubscriptionState,
		},
		{
			Name:            stateChangeReasonFilter,
			Label:           "Subscription with existing state change reason to be updated",
			Type:            dsPb.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
			Options:         stateChangeReasons,
		},
	}
	return paramList, nil
}

// nolint: funlen
func (c *BulkUpdateSubscriptionsState) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	updateRequest := &manager.UpdateSubscriptionsStateForRuleRequest{}
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ruleIdIdentifier:
			updateRequest.RuleId = filter.GetStringValue()
		case stateChangeReasonIdentifier:
			reason := manager.SubscriptionStateChangeReason(manager.SubscriptionStateChangeReason_value[filter.GetDropdownValue()])
			if reason == manager.SubscriptionStateChangeReason_UNSPECIFIED {
				logger.Error(ctx, "invalid subscription state change reason", zap.String(logger.REASON, filter.GetDropdownValue()))
				return "{\"error_reason\":\"invalid subscription state change reason\"}", nil
			}
			updateRequest.Reason = reason
		case targetStateIdentifier:
			targetState := manager.RuleSubscriptionState(manager.RuleSubscriptionState_value[filter.GetDropdownValue()])
			if targetState == manager.RuleSubscriptionState_RULE_SUBSCRIPTION_STATE_UNSPECIFIED {
				logger.Error(ctx, "invalid target subscription state", zap.String(logger.TARGET_STATE, filter.GetDropdownValue()))
				return "{\"error_reason\":\"invalid subscription state\"}", nil
			}
			updateRequest.TargetState = targetState
		case stateFilter:
			var filterStates []manager.RuleSubscriptionState
			for _, f := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				state := manager.RuleSubscriptionState(manager.RuleSubscriptionState_value[f])
				if state == manager.RuleSubscriptionState_RULE_SUBSCRIPTION_STATE_UNSPECIFIED {
					logger.Error(ctx, "invalid filter subscription state", zap.String(logger.TARGET_STATE, f))
					return "{\"error_reason\":\"invalid subscription state\"}", nil
				}
				filterStates = append(filterStates, state)
			}
			updateRequest.StateFilter = filterStates
		case stateChangeReasonFilter:
			var stateChangeReasonFilter []manager.SubscriptionStateChangeReason
			for _, f := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				reason := manager.SubscriptionStateChangeReason(manager.SubscriptionStateChangeReason_value[f])
				if reason == manager.SubscriptionStateChangeReason_UNSPECIFIED {
					logger.Error(ctx, "invalid filter subscription state change reason", zap.String(logger.REASON, f))
					return "{\"error_reason\":\"invalid subscription state change reason\"}", nil
				}
				stateChangeReasonFilter = append(stateChangeReasonFilter, reason)
			}
			updateRequest.StateChangeReasonFilter = stateChangeReasonFilter
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}
	updateRequest.Provenance = manager.SubscriptionStateChangeProvenance_SHERLOCK
	resp, err := c.rmsClient.UpdateSubscriptionsStateForRule(ctx, updateRequest)
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error while updating subscriptions state for rule", zap.Error(err2))
		return fmt.Sprintf("{\"error at RMS\":\"%v\"}", err.Error()), nil
	}
	// returned marshalled response
	marshalledRes, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "error marshalling UpdateSubscriptionsStateForRule response", zap.Error(err))
		return fmt.Sprintf("{\"error in marshalling\":\"%v\"}", err.Error()), nil
	}
	return string(marshalledRes), nil
}
