package internationalfundtransfer

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	actionsPb "github.com/epifi/gamma/api/cx/developer/actions"
	dbStatePb "github.com/epifi/gamma/api/cx/developer/db_state"
	fgPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type AggrTaxReport struct {
	fileGenClient fgPb.FileGeneratorClient
}

func NewAggrTaxReport(
	fileGenClient fgPb.FileGeneratorClient,
) *AggrTaxReport {
	return &AggrTaxReport{
		fileGenClient: fileGenClient,
	}
}

func (s *AggrTaxReport) FetchParamList(_ context.Context, _ actionsPb.DeveloperActions, _ *actionsPb.DevActionMeta) ([]*dbStatePb.ParameterMeta, error) {
	params := []*dbStatePb.ParameterMeta{
		{
			Name:            "fileType",
			Label:           "File Type",
			Type:            dbStatePb.ParameterDataType_DROPDOWN,
			ParameterOption: dbStatePb.ParameterOption_MANDATORY,
			Options: []string{
				fgPb.FileType_FILE_TYPE_AGGREGATED_OUTWARD_GST_REPORT.String(),
				fgPb.FileType_FILE_TYPE_AGGREGATED_OUTWARD_TCS_REPORT.String(),
				fgPb.FileType_FILE_TYPE_AGGREGATED_INWARD_GST_REPORT.String(),
			},
		},
		{
			Name:            "startDate",
			Label:           "Start Date",
			Type:            dbStatePb.ParameterDataType_TIMESTAMP,
			ParameterOption: dbStatePb.ParameterOption_MANDATORY,
		},
		{
			Name:            "endDate",
			Label:           "End Date",
			Type:            dbStatePb.ParameterDataType_TIMESTAMP,
			ParameterOption: dbStatePb.ParameterOption_MANDATORY,
		},
	}
	return params, nil
}

func (s *AggrTaxReport) ExecuteAction(ctx context.Context, _ actionsPb.DeveloperActions, filters []*dbStatePb.Filter, _ *actionsPb.DevActionMeta) (string, error) {
	var (
		fileType       fgPb.FileType
		startTs, endTs *timestampPb.Timestamp
	)
	var err error
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case "startDate":
			startTs = filter.GetTimestamp()
		case "endDate":
			endTs = filter.GetTimestamp()
		case "fileType":
			fileTypeName, ok := fgPb.FileType_value[filter.GetDropdownValue()]
			if !ok {
				return "unsupported file type name", nil
			}
			fileType = fgPb.FileType(fileTypeName)
		default:
			return fmt.Sprintf("unhandled filter param: %s", filter.GetParameterName()), nil
		}
	}

	if !startTs.IsValid() || !endTs.IsValid() || fileType == fgPb.FileType_FILE_TYPE_UNSPECIFIED {
		return "invalid inputs", nil
	}
	// Web currently converts the end date entered by Sherlock user to the start of the end date timestamp (12 midnight).
	// Hence, adding the 24 hours to include taxes transferred on the end date in the tax report generated below.
	endTs = timestampPb.New(endTs.AsTime().Add(24 * time.Hour))
	res, err := s.fileGenClient.GenerateAggregatedTaxReport(ctx, &fgPb.GenerateAggregatedTaxReportRequest{
		FileType: fileType,
		StartTs:  startTs,
		EndTs:    endTs,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error generating aggregated tax report", zap.Error(te))
		return te.Error(), nil
	}
	marshalledFileGenAttempt, err := protojson.MarshalOptions{}.Marshal(res.FileGenerationAttempt)
	if err != nil {
		logger.Error(ctx, "cannot marshal file gen attempt to json", zap.Error(err))
		return "", nil
	}
	return string(marshalledFileGenAttempt), nil
}
