//nolint:all
package processor

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"strings"

	vendorPkg "github.com/epifi/gamma/pkg/vendors/federal"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type ReopenClosedSavingsAccount struct {
	savingsClient savingsPb.SavingsClient
}

func NewReopenClosedSavingsAccount(savingsClient savingsPb.SavingsClient) *ReopenClosedSavingsAccount {
	return &ReopenClosedSavingsAccount{savingsClient: savingsClient}
}

func (s *ReopenClosedSavingsAccount) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            AccountNumber,
			Label:           "Account Number",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

//nolint:dupl
func (s *ReopenClosedSavingsAccount) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var (
		accountNumber string
		marshalledRes []byte
		ifscCode      string
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case AccountNumber:
			accountNumber = strings.TrimSpace(filter.GetStringValue())
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}
	ifscCode, err := vendorPkg.GetIfscCodeForVendor(commonvgpb.Vendor_FEDERAL_BANK)
	if err != nil {
		logger.Error(ctx, "Unable to fetch IFSC code")
		return "", status.Error(codes.Internal, "Unable to fetch IFSC code")
	}

	resp, err := s.savingsClient.ReopenAccount(ctx, &savingsPb.ReopenAccountRequest{
		Identifier: &savingsPb.ReopenAccountRequest_ExternalId{
			ExternalId: &savingsPb.BankAccountIdentifier{
				AccountNo: accountNumber,
				IfscCode:  ifscCode,
			},
		},
	})

	respAccount := resp.GetAccount()
	accountSummary := &savingsPb.Account{
		AccountNo: respAccount.GetAccountNo(),
		IfscCode:  respAccount.GetIfscCode(),
		State:     respAccount.GetState(),
	}
	resp.Account = accountSummary

	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error reopening closed account", zap.String(logger.ACCOUNT_NUMBER, accountNumber), zap.Error(err))
		marshalledRes = []byte(te.Error())
		return string(marshalledRes), nil
	}

	logger.Info(ctx, "Successfully marked account as reopened ", zap.String(logger.ACCOUNT_NUMBER, accountNumber))
	marshalledRes, err = json.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "error marshalling ReopenClosedAccount response", zap.Error(err))
		marshalledRes = []byte(err.Error())
	}
	return string(marshalledRes), nil
}
