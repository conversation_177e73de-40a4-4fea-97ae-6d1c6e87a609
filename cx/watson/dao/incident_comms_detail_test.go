package dao

import (
	"context"
	"reflect"
	"testing"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"
	gormV2 "gorm.io/gorm"

	commsPb "github.com/epifi/gamma/api/comms"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/test"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"
)

type IncidentCommsDetailDaoTestSuite struct {
	db                     *gormV2.DB
	conf                   *config.Config
	IncidentCommsDetailDao *IncidentCommsDetailDao
}

var (
	IncidentCommsDetailDTS   IncidentCommsDetailDaoTestSuite
	IncidentCommsDetailTest1 = &watsonPb.IncidentCommsDetail{
		Id:              "de34d771-2c76-44e3-81f9-48c730feef7d",
		IncidentId:      "de34d771-2c76-44e3-81f9-48c730feef7d",
		CommsMsgId:      "de34d771-2c76-44e3-81f9-48c730feef7d",
		TicketStatus:    ticketPb.Status_STATUS_OPEN,
		CommsType:       watsonPb.CommsType_COMMS_TYPE_INCIDENT_RESOLUTION,
		IssueCategoryId: "de34d771-2c76-44e3-81f9-48c730feef7c",
		CommsBatchId:    "de34d771-2c76-44e3-81f9-48c730feef7d",
		CommsMedium:     commsPb.Medium_SMS,
	}
	IncidentCommsDetailTest2 = &watsonPb.IncidentCommsDetail{
		Id:              "de34d771-2c76-44e3-81f9-48c730feef7a",
		IncidentId:      "de34d771-2c76-44e3-81f9-48c730feef7a",
		CommsMsgId:      "de34d771-2c76-44e3-81f9-48c730feef7a",
		TicketStatus:    ticketPb.Status_STATUS_PENDING,
		CommsType:       watsonPb.CommsType_COMMS_TYPE_INCIDENT_CREATION,
		IssueCategoryId: "de34d771-2c76-44e3-81f9-48c730feef7b",
		CommsBatchId:    uuid.New().String(),
		CommsMedium:     commsPb.Medium_NOTIFICATION,
	}
	IncidentCommsDetailTest3 = &watsonPb.IncidentCommsDetail{
		Id:              "de34d771-2c76-44e3-81f9-48c730feef7b",
		IncidentId:      "de34d771-2c76-44e3-81f9-48c730feef7e",
		CommsMsgId:      "de34d771-2c76-44e3-81f9-48c730feef7d",
		TicketStatus:    ticketPb.Status_STATUS_OPEN,
		CommsType:       watsonPb.CommsType_COMMS_TYPE_INCIDENT_RESOLUTION,
		IssueCategoryId: "de34d771-2c76-44e3-81f9-48c730feef7c",
		CommsBatchId:    "de34d771-2c76-44e3-81f9-48c730feef7d",
		CommsMedium:     commsPb.Medium_EMAIL,
	}
)

func TestIncidentCommsDetailDao_Create(t *testing.T) {

	type args struct {
		ctx context.Context
		req *watsonPb.IncidentCommsDetail
	}
	tests := []struct {
		name    string
		args    args
		want    *watsonPb.IncidentCommsDetail
		wantErr bool
	}{
		{
			name: "error because incident id not passed",
			args: args{
				ctx: context.Background(),
				req: &watsonPb.IncidentCommsDetail{
					CommsMsgId: "de34d771-2c76-44e3-81f9-48c730feef7d",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error because comms msg id not passed",
			args: args{
				ctx: context.Background(),
				req: &watsonPb.IncidentCommsDetail{
					IncidentId: "de34d771-2c76-44e3-81f9-48c730feef7d",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error because record already exists",
			args: args{
				ctx: context.Background(),
				req: IncidentCommsDetailTest1,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "create successful",
			args: args{
				ctx: context.Background(),
				req: IncidentCommsDetailTest2,
			},
			want:    IncidentCommsDetailTest2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, IncidentCommsDetailDTS.db, IncidentCommsDetailDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := IncidentCommsDetailDTS.IncidentCommsDetailDao.Create(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIncidentCommsDetailDao_GetByIncidentId(t *testing.T) {
	type args struct {
		ctx        context.Context
		incidentId string
	}
	tests := []struct {
		name    string
		args    args
		want    []*watsonPb.IncidentCommsDetail
		wantErr bool
	}{
		{
			name: "incident id not passed",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "not found",
			args: args{
				ctx:        context.Background(),
				incidentId: "de34d771-2c76-44e3-81f9-48c730feef0b",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:        context.Background(),
				incidentId: "de34d771-2c76-44e3-81f9-48c730feef7d",
			},
			want:    []*watsonPb.IncidentCommsDetail{IncidentCommsDetailTest1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, IncidentCommsDetailDTS.db, IncidentCommsDetailDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := IncidentCommsDetailDTS.IncidentCommsDetailDao.GetByIncidentId(tt.args.ctx, tt.args.incidentId)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isListOfIncidentCommsDetailEqual(got, tt.want) {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isListOfIncidentCommsDetailEqual(got []*watsonPb.IncidentCommsDetail, want []*watsonPb.IncidentCommsDetail) bool {
	if len(want) != len(got) {
		return false
	}
	if got == nil {
		return want == nil
	}
	for index, commsDetail := range got {
		want[index].UpdatedAt = commsDetail.GetUpdatedAt()
		want[index].CreatedAt = commsDetail.GetCreatedAt()
		if !proto.Equal(commsDetail, want[index]) {
			return false
		}
	}
	return true
}

func TestIncidentCommsDetailDao_GetByCommsMsgId(t *testing.T) {
	type args struct {
		ctx        context.Context
		commsMsgId string
	}
	tests := []struct {
		name    string
		args    args
		want    []*watsonPb.IncidentCommsDetail
		wantErr bool
	}{
		{
			name: "comms msg id not passed",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "not found",
			args: args{
				ctx:        context.Background(),
				commsMsgId: "de34d771-2c76-44e3-81f9-48c730feef0b",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:        context.Background(),
				commsMsgId: "de34d771-2c76-44e3-81f9-48c730feef7d",
			},
			want:    []*watsonPb.IncidentCommsDetail{IncidentCommsDetailTest1, IncidentCommsDetailTest3},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTestV2.TruncateAndPopulateRdsFixtures(t, IncidentCommsDetailDTS.db, IncidentCommsDetailDTS.conf.EpifiDb.GetName(), test.AffectedTestTables)
			got, err := IncidentCommsDetailDTS.IncidentCommsDetailDao.GetByCommsMsgId(tt.args.ctx, tt.args.commsMsgId)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isListOfIncidentCommsDetailEqual(got, tt.want) {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
		})
	}
}
