package helper

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"flag"
	"os"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	mockVgFd "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk/mocks"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, _, teardown := test.InitTestServer(true)
	fahTs = FreshdeskAutomationHelperTestSuite{
		conf: genConf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type FreshdeskAutomationHelperTestSuite struct {
	conf *cxGenConf.Config
}

const (
	primaryContactId   = 11111111
	secondaryContactId = 22222222
)

var (
	fahTs           FreshdeskAutomationHelperTestSuite
	fdRequestHeader = &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FRESHDESK}
	contact1        = &freshdeskPb.TicketContact{
		Id:          primaryContactId,
		Email:       "<EMAIL>",
		Phone:       "911234567890",
		OtherEmails: []string{"<EMAIL>"},
	}
	contact2 = &freshdeskPb.TicketContact{
		Id:    secondaryContactId,
		Phone: "1234567890",
	}
	contact3 = &freshdeskPb.TicketContact{
		Id:          secondaryContactId,
		Email:       "<EMAIL>",
		Phone:       "1234567890",
		OtherEmails: []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"},
	}
	contact4 = &freshdeskPb.TicketContact{
		Id:          secondaryContactId,
		Email:       "<EMAIL>",
		Phone:       "9876543210",
		OtherEmails: []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"},
	}
	contact5 = &freshdeskPb.TicketContact{
		Id:    secondaryContactId,
		Phone: "1234567890",
		Email: "<EMAIL>",
	}
)

func TestFreshdeskAutomationHelper_MergeContacts(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockVgFdClient := mockVgFd.NewMockFreshdeskClient(ctr)
	type args struct {
		mocks                                []interface{}
		ctx                                  context.Context
		primaryContactId, secondaryContactId int64
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "failed to get primary contact details",
			args: args{
				mocks: []interface{}{
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: primaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusRecordNotFound()}, nil),
				},
				ctx:                context.Background(),
				primaryContactId:   primaryContactId,
				secondaryContactId: secondaryContactId,
			},
			wantErr: errors.New("failed to get primary contact details: rpc status code:5 short_message:\"Not found\""),
		},
		{
			name: "failed to get secondary contact details",
			args: args{
				mocks: []interface{}{
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: primaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact1}, nil),
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: secondaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusInternal()}, nil),
				},
				ctx:                context.Background(),
				primaryContactId:   primaryContactId,
				secondaryContactId: secondaryContactId,
			},
			wantErr: errors.New("failed to get secondary contact details: rpc status code:13 short_message:\"Internal Server Error\""),
		},
		{
			name: "failed: trying to merge two invalid contacts",
			args: args{
				mocks: []interface{}{
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: primaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact1}, nil),
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: secondaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact5}, nil),
				},
				ctx:                context.Background(),
				primaryContactId:   primaryContactId,
				secondaryContactId: secondaryContactId,
			},
			wantErr: errors.New("trying to merge contact with multiple personal emails: invalid argument passed by the client"),
		},
		{
			name: "failed to get other emails for merging contacts",
			args: args{
				mocks: []interface{}{
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: primaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact1}, nil),
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: secondaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact3}, nil),
				},
				ctx:                context.Background(),
				primaryContactId:   primaryContactId,
				secondaryContactId: secondaryContactId,
			},
			wantErr: errors.New("failed to get other emails for merging contacts: length of non-freshchat emails (10) exceeds max allowed other emails (9)"),
		},
		{
			name: "error calling freshdesk API to merge contacts",
			args: args{
				mocks: []interface{}{
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: primaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact1}, nil),
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: secondaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact4}, nil),
					mockVgFdClient.EXPECT().MergeContacts(context.Background(), &freshdeskPb.MergeContactsRequest{
						Header:              fdRequestHeader,
						PrimaryContactId:    primaryContactId,
						SecondaryContactIds: []int64{secondaryContactId},
						TicketContact: &freshdeskPb.TicketContact{
							Phone:       contact1.GetPhone(),
							OtherEmails: []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"},
						},
					}).Return(&freshdeskPb.MergeContactsResponse{Status: rpc.StatusInvalidArgument()}, nil),
				},
				ctx:                context.Background(),
				primaryContactId:   primaryContactId,
				secondaryContactId: secondaryContactId,
			},
			wantErr: errors.New("error calling freshdesk API to merge contacts: rpc status code:3 short_message:\"Bad request\""),
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: primaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact1}, nil),
					mockVgFdClient.EXPECT().GetContactById(context.Background(), &freshdeskPb.GetContactByIdRequest{
						Header:    fdRequestHeader,
						ContactId: secondaryContactId,
					}).Return(&freshdeskPb.GetContactByIdResponse{Status: rpc.StatusOk(), Contact: contact2}, nil),
					mockVgFdClient.EXPECT().MergeContacts(context.Background(), &freshdeskPb.MergeContactsRequest{
						Header:              fdRequestHeader,
						PrimaryContactId:    primaryContactId,
						SecondaryContactIds: []int64{secondaryContactId},
						TicketContact: &freshdeskPb.TicketContact{
							Phone:       contact1.GetPhone(),
							OtherEmails: contact1.GetOtherEmails(),
						},
					}).Return(&freshdeskPb.MergeContactsResponse{Status: rpc.StatusOk()}, nil),
				},
				ctx:                context.Background(),
				primaryContactId:   primaryContactId,
				secondaryContactId: secondaryContactId,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := NewFreshdeskAutomationHelper(fahTs.conf, mockVgFdClient)
			err := f.MergeContacts(tt.args.ctx, tt.args.primaryContactId, tt.args.secondaryContactId)
			if !isErrorEqual(err, tt.wantErr) {
				t.Errorf("MergeContacts() error = %v, wantErr = %v", err.Error(), tt.wantErr.Error())
			}
		})
	}
}

func isErrorEqual(gotErr error, wantErr error) bool {
	if gotErr == nil || wantErr == nil {
		return errors.Is(gotErr, wantErr)
	}
	// Number of white spaces in the .Error() is varying. Hence removing all whitespaces for comparison
	return strings.ReplaceAll(gotErr.Error(), " ", "") == strings.ReplaceAll(wantErr.Error(), " ", "")
}
