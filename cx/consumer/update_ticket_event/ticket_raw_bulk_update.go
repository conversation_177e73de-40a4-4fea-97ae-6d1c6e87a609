package update_ticket_event

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	rpcPb "github.com/epifi/be-common/api/rpc"

	cxLogger "github.com/epifi/gamma/cx/logger"

	"github.com/epifi/be-common/api/queue"
	consumerPb "github.com/epifi/gamma/api/cx/consumer"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/cx/ticket/dao"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type TicketRawBulkUpdateProcessor struct {
	fdClient            freshdeskPb.FreshdeskClient
	bulkTicketJobDao    dao.IBulkTicketJobDao
	ticketFailureLogDao dao.ITicketFailureLogDao
}

func NewTicketRawBulkUpdateProcessor(fdClient freshdeskPb.FreshdeskClient, bulkTicketJobDao dao.IBulkTicketJobDao,
	ticketFailureLogDao dao.ITicketFailureLogDao) *TicketRawBulkUpdateProcessor {
	return &TicketRawBulkUpdateProcessor{
		fdClient:            fdClient,
		bulkTicketJobDao:    bulkTicketJobDao,
		ticketFailureLogDao: ticketFailureLogDao,
	}
}

const (
	FailureReasonMandatoryParamsMissing = "mandatory params missing in consumer request"
	FailureReasonVendorError            = "error while updating ticket in freshdesk, status: %s"
	FailureReasonDBError                = "error while fetching job details from db"
	FailureReasonJobKilled              = "not updating ticket because job is killed"
	FailureReasonAppendPrivateNoteError = "update ticket is success but failed to add private note in the ticket, status: %s"
)

//nolint:funlen
func (u *TicketRawBulkUpdateProcessor) ProcessUpdateTicketEvent(ctx context.Context, req *consumerPb.UpdateTicketEventRequest) (*consumerPb.UpdateTicketEventResponse, error) {
	jobId := req.GetUpdateTicketPayload().GetBulkRawTicketUpdatePayload().GetJobId()
	ticketId := req.GetUpdateTicketPayload().GetBulkRawTicketUpdatePayload().GetTicketId()
	ticket := req.GetUpdateTicketPayload().GetBulkRawTicketUpdatePayload().GetTicket()
	commonLogs := []zap.Field{zap.Int64("jobId", jobId), zap.Int64(logger.TICKET_ID, ticketId)}

	// mandatory params missing in request
	if jobId == 0 || ticketId == 0 || ticket == nil {
		cxLogger.Error(ctx, "job id or ticket id or ticket object is missing in request", commonLogs...)
		err := u.updateTicketFailureInDB(ctx, jobId, ticketId, FailureReasonMandatoryParamsMissing)
		// if db update failed return transient error, so we can try to update again in retry
		if err != nil {
			cxLogger.Error(ctx, "error while updating failure in db", append(commonLogs, zap.Error(err))...)
			return getTransientErrorResponse(), nil
		}
		return getPermanentErrorResponse(), nil
	}

	// fetch job details from db to check if job is killed
	// we will return transient error and retry if this step fails
	// if it's last attempt then we will update the failure for ticket in db
	job, err := u.bulkTicketJobDao.Get(ctx, jobId)
	if err != nil {
		logger.Error(ctx, "error while fetching job request from db", zap.Error(err))
		if req.GetRequestHeader().GetIsLastAttempt() {
			updateErr := u.updateTicketFailureInDB(ctx, jobId, ticketId, FailureReasonDBError)
			if updateErr != nil {
				// since this is last attempt we will just log and mask the error
				cxLogger.Error(ctx, "error while updating failure in db", append(commonLogs, zap.Error(updateErr))...)
			}
		}
		return getTransientErrorResponse(), nil
	}

	// return permanent error if job is killed
	if job.GetIsKilled() == commontypes.BooleanEnum_TRUE {
		updateErr := u.updateTicketFailureInDB(ctx, jobId, ticketId, FailureReasonJobKilled)
		if updateErr != nil {
			// since this is last attempt we will just log and mask the error
			cxLogger.Error(ctx, "error while updating failure in db", append(commonLogs, zap.Error(updateErr))...)
		}
		return getPermanentErrorResponse(), nil
	}

	// call vg api to update the ticket details
	// we will return transient error and retry if this step fails
	// if it's last attempt then we will update the failure for ticket in db
	resp, err := u.fdClient.UpdateTicketRaw(ctx, &freshdeskPb.UpdateTicketRawRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		Ticket: ticket,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		cxLogger.Error(ctx, "error while calling vg to update ticket details", append(commonLogs, zap.Error(err))...)
		// this is transient error but if it's last queue attempt we need to update the failure in db
		if req.GetRequestHeader().GetIsLastAttempt() {
			updateErr := u.updateTicketFailureInDB(ctx, jobId, ticketId, fmt.Sprintf(FailureReasonVendorError, resp.GetStatus().String()))
			if updateErr != nil {
				// since this is last attempt we will just log and mask the error
				cxLogger.Error(ctx, "error while updating failure in db", append(commonLogs, zap.Error(updateErr))...)
			}
		}
		// return transient error so this can be retried from queue
		return getTransientErrorResponse(), nil
	}

	// append private note in the ticket if private note field is present
	// we will return transient error and retry if this step fails since update ticket step is idempotent and can be done multiple times
	// if it's last attempt then we will update the failure for ticket in db
	if req.GetUpdateTicketPayload().GetBulkRawTicketUpdatePayload().GetPrivateNote() != "" {
		addPrivateNoteResp, addErr := u.fdClient.AddPrivateNoteInTicket(ctx, &freshdeskPb.AddPrivateNoteInTicketRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_FRESHDESK,
			},
			TicketId: ticket.GetId(),
			Body:     req.GetUpdateTicketPayload().GetBulkRawTicketUpdatePayload().GetPrivateNote(),
		})
		if err = epifigrpc.RPCError(addPrivateNoteResp, addErr); err != nil {
			cxLogger.Error(ctx, "error while calling vg to update ticket details", append(commonLogs, zap.Error(err))...)
			// this is transient error but if it's last queue attempt we need to update the failure in db
			if req.GetRequestHeader().GetIsLastAttempt() {
				updateErr := u.updateTicketFailureInDB(ctx, jobId, ticketId, fmt.Sprintf(FailureReasonAppendPrivateNoteError, addPrivateNoteResp.GetStatus().String()))
				if updateErr != nil {
					// since this is last attempt we will just log and mask the error
					cxLogger.Error(ctx, "error while updating failure in db", append(commonLogs, zap.Error(updateErr))...)
				}
			}
			// return transient error so this can be retried from queue
			return getTransientErrorResponse(), nil
		}
	}
	// update is successful, update the success attempt in db
	cxLogger.Info(ctx, "updated ticket details successfully in freshdesk", commonLogs...)
	err = u.bulkTicketJobDao.UpdateTicketProcessingSuccess(ctx, jobId)
	// if db update failed we should mask the error since ticket update is already done
	if err != nil {
		cxLogger.Error(ctx, "error while updating success attempt in db", append(commonLogs, zap.Error(err))...)
	}
	return getSuccessResponse(), nil
}

func (u *TicketRawBulkUpdateProcessor) updateTicketFailureInDB(ctx context.Context, jobId int64, ticketId int64, errorReason string) error {
	// if job id is missing then we can't update the failure in db
	// we will log the failure and mask the error here
	if jobId == 0 {
		logger.Info(ctx, "not updating failure in db since job id is missing")
		return nil
	}
	// update the failure count in jobs table
	err := u.bulkTicketJobDao.UpdateTicketProcessingFailure(ctx, jobId)
	if err != nil {
		return errors.Wrap(err, "error while updating failure in job table")
	}
	// if ticket id is missing then we can't update the ticket failure in db
	// we will log the failure and mask the error here
	if ticketId == 0 {
		logger.Info(ctx, "not updating ticket failure in db since ticket id is missing")
		return nil
	}
	// if jobs table is updated, we will try to log error in failure log table on best effort basis
	_, err = u.ticketFailureLogDao.AddTicketFailureLog(ctx, &ticketPb.TicketFailureLog{JobId: jobId, TicketId: ticketId, FailureReason: errorReason})
	if err != nil {
		// we are doing this on best effort basis so mask the error here if received
		cxLogger.Error(ctx, "error while adding ticket failure log in db", zap.Int64("jobId", jobId),
			zap.Int64(logger.TICKET_ID, ticketId), zap.Error(err))
	}
	return nil
}

func getPermanentErrorResponse() *consumerPb.UpdateTicketEventResponse {
	return &consumerPb.UpdateTicketEventResponse{
		ResponseHeader: &queue.ConsumerResponseHeader{
			Status:         queue.MessageConsumptionStatus_PERMANENT_FAILURE,
			GrpcStatusCode: rpcPb.StatusInvalidArgument(),
		},
	}
}

func getTransientErrorResponse() *consumerPb.UpdateTicketEventResponse {
	return &consumerPb.UpdateTicketEventResponse{
		ResponseHeader: &queue.ConsumerResponseHeader{
			Status:         queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
			GrpcStatusCode: rpcPb.StatusInternal(),
		},
	}
}

func getSuccessResponse() *consumerPb.UpdateTicketEventResponse {
	return &consumerPb.UpdateTicketEventResponse{
		ResponseHeader: &queue.ConsumerResponseHeader{
			Status:         queue.MessageConsumptionStatus_SUCCESS,
			GrpcStatusCode: rpcPb.StatusOk(),
		},
	}
}
