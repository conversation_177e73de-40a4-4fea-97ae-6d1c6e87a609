package s3_event

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
)

const (
	TicketId = "ticket_id"
	Summary  = "concatenated_summaries"
)

var SummarizationOutputProcessorWireSet = wire.NewSet(NewCallSummarizationOutputFileProcessor, wire.Bind(new(S3FileProcessor), new(*CallSummarizationOutputFileProcessor)))

type CallSummarizationOutputFileProcessor struct {
	s3Client     s3.S3Client
	ticketClient ticketPb.TicketClient
	genConf      *cxGenConf.Config
}

var _ S3FileProcessor = &CallSummarizationOutputFileProcessor{}

func NewCallSummarizationOutputFileProcessor(s3Client s3.S3Client, ticketClient ticketPb.TicketClient, genConf *cxGenConf.Config) *CallSummarizationOutputFileProcessor {
	return &CallSummarizationOutputFileProcessor{
		s3Client:     s3Client,
		ticketClient: ticketClient,
		genConf:      genConf,
	}
}

func (c *CallSummarizationOutputFileProcessor) ProcessS3File(ctx context.Context, filePath string) error {
	if !c.genConf.S3EventConsumerConfig().IsCallSummarizationProcessingEnabled() {
		logger.Info(ctx, "not processing call summarization output file based on config", zap.String(logger.FILE_NAME, filePath))
		return nil
	}

	ticketSummaryList, parseErr := c.readAndParseSummarizationModelOutput(ctx, filePath)
	if parseErr != nil {
		return errors.Wrap(parseErr, "failed to read and parse output file")
	}

	var wg sync.WaitGroup
	wg.Add(len(ticketSummaryList))

	for _, ticketCallSummary := range ticketSummaryList {
		// creating a new tempTicketCallSummary variable because by the time a goroutine starts, the ticketCallSummary variable may have a different value
		tempTicketCallSummary := ticketCallSummary
		goroutine.RunWithCtx(ctx, func(gCtx context.Context) {
			defer wg.Done()
			err := c.addSummaryInTicket(ctx, tempTicketCallSummary)
			if err != nil {
				logger.Error(ctx, "failed to add summary in ticket", zap.Error(err), zap.String(logger.TICKET_ID, tempTicketCallSummary.TicketId))
			}
		})
	}
	wg.Wait()
	return nil
}

func (c *CallSummarizationOutputFileProcessor) addSummaryInTicket(ctx context.Context, ticketCallSummary *CallSummarizationModelOutputFileEntry) error {
	ticketId, err := strconv.ParseInt(ticketCallSummary.TicketId, 10, 64)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("failed to convert ticket id %v to int", ticketId))
	}
	if ticketId == 0 || ticketCallSummary.Summary == "" {
		return fmt.Errorf("ticket id: %v and summary: %v not populated", ticketId, ticketCallSummary.Summary)
	}

	privateNoteResp, privateNoteErr := c.ticketClient.AddPrivateNoteAsync(ctx, &ticketPb.AddPrivateNoteAsyncRequest{
		TicketId: ticketId,
		Body:     ticketCallSummary.Summary,
	})
	if te := epifigrpc.RPCError(privateNoteResp, privateNoteErr); te != nil {
		return errors.Wrap(te, fmt.Sprintf("failed to update ticket: %v with private note", ticketId))
	}
	updateResp, updateErr := c.ticketClient.UpdateTicketAsync(ctx, &ticketPb.UpdateTicketAsyncRequest{
		Ticket: &ticketPb.Ticket{
			Id:           ticketId,
			CustomFields: &ticketPb.CustomFields{IsCallSummaryAdded: commontypes.BooleanEnum_TRUE},
		},
	})
	if te := epifigrpc.RPCError(updateResp, updateErr); te != nil {
		return errors.Wrap(te, fmt.Sprintf("failed to update ticket: %v", ticketId))
	}
	return nil
}

func (c *CallSummarizationOutputFileProcessor) readAndParseSummarizationModelOutput(ctx context.Context, filePath string) ([]*CallSummarizationModelOutputFileEntry, error) {
	readRes, rErr := c.s3Client.Read(ctx, filePath)
	if rErr != nil {
		return nil, errors.Wrap(rErr, "failed to read file from S3")
	}
	reader := csv.NewReader(bytes.NewReader(readRes))
	// we are using '~' as delimiter for csv, as discussed with DS team to avoid any data corruption if call summary contains comma or inverted commas
	reader.Comma = '~'
	reader.LazyQuotes = true
	entries, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(ErrInvalidFile, err.Error())
	}
	// trim white-spaces from each and every cell of csv
	for i, row := range entries {
		for j, value := range row {
			row[j] = formatCsvFileCell(value)
		}
		entries[i] = row
	}

	var list []*CallSummarizationModelOutputFileEntry
	columnNumberToTitle := make(map[int]string)
	for rowNum, entry := range entries {
		if rowNum == 0 {
			for columnNumber, columnTitle := range entry {
				columnNumberToTitle[columnNumber] = columnTitle
			}
			continue
		}
		singleEntry := &CallSummarizationModelOutputFileEntry{}
		for columnNum, cellValue := range entry {
			columnTitle, found := columnNumberToTitle[columnNum]
			if !found {
				return nil, errors.Wrap(ErrInvalidFile, fmt.Sprintf("invalid column in column number in %v", columnNum))
			}
			err := c.populateFieldInSummarizationOutputFileEntry(singleEntry, strings.TrimSpace(cellValue), columnTitle)
			if err != nil {
				logger.WarnWithCtx(ctx, "failed to populate column into output struct", zap.Error(err), zap.String("title", columnTitle), zap.String("value", cellValue))
			}
		}
		list = append(list, singleEntry)
	}
	if len(list) == 0 {
		return nil, errors.Wrap(ErrInvalidFile, "no ticket summary entry found in file")
	}
	return list, nil
}

func formatCsvFileCell(value string) string {
	value = strings.TrimSpace(value)
	// Model will populate <NL> when new line is expected, hence replacing it with new line character
	value = strings.ReplaceAll(value, "<NL>", "\n")
	// Csv reader does not remove the escape character, hence removing the escape character before quotation mark
	value = strings.ReplaceAll(value, `\"`, `"`)
	return value
}

func (c *CallSummarizationOutputFileProcessor) populateFieldInSummarizationOutputFileEntry(output *CallSummarizationModelOutputFileEntry, value string, columnTitle string) error {
	if output == nil {
		return errors.New("cannot populate into nil object")
	}
	switch columnTitle {
	case TicketId:
		output.TicketId = value
	case Summary:
		output.Summary = value
	default:
		return fmt.Errorf("invalid column title provided: %v", columnTitle)
	}
	return nil
}
