Application:
  Environment: "uat"
  Name: "cx"
  IsSecureRedis: true

Server:
  Ports:
    GrpcPort: 8095
    GrpcSecurePort: 9508
    HttpPort: 9999
    HttpPProfPort: 9990
    HttpSecurePort: 9785

EpifiDb:
  AppName: "cx"
  StatementTimeout: 5s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Cognito:
  UserPoolId: "ap-south-1_PuPQfko25"
  ClientId: "2f29r98to1sukqs5tu4ontka2v"

EmailVerification:
  VerificationUrl: "https://web.uat.pointz.in/customer-auth/email-verification"
  FromEmail: "<EMAIL>"
  FromEmailName: "Fi support"

MobilePromptVerification:
  NotificationTitle: "Verify Yourself"
  NotificationBody: "Please choose yes if the request was initiated by you"
  Validity: 300 #validity of mobile prompt in seconds

AuthFactorRetryLimit:
  DOB: 3
  MobilePrompt: 3
  EmailVerification: 3
  TransactionAmount: 3
  LastFivePanCharacters: 3
  PermanentAddressPinCode: 3
  FathersName: 3
  MothersName: 3
  Default: 3

CustomerAuth:
  AuthValidityDuration: "10m"
  EmailValidityDuration: "5m"
  MobilePromptValidityDuration: "2m"
  MaxResetCount: 1
  IsInAppNotificationEnabled: true

FreshdeskTicketPublisher:
  QueueName: "uat-freshdesk-ticket-queue"

FreshdeskTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-freshdesk-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskContactPublisher:
  QueueName: "uat-freshdesk-contact-queue"

FreshdeskContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-freshdesk-contact-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputePublisher:
  QueueName: "uat-cx-dispute-queue"

DisputeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-dispute-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeCreateTicketPublisher:
  QueueName: "uat-cx-dispute-create-ticket-queue"

DisputeCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-dispute-create-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeUpdateTicketPublisher:
  QueueName: "uat-cx-dispute-update-ticket-queue"

DisputeUpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-dispute-update-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1     # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 6s  # 10 per minute
    Namespace: "cx"

DisputeAddNoteTicketPublisher:
  QueueName: "uat-cx-dispute-add-note-ticket-queue"

DisputeAddNoteTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-dispute-add-note-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

WatsonIncidentReportingPublisher:
  QueueName: "uat-cx-watson-incident-reporting-queue"

WatsonIncidentReportingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-watson-incident-reporting-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

WatsonIncidentResolutionPublisher:
  QueueName: "uat-cx-watson-incident-resolution-queue"

WatsonIncidentResolutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-watson-incident-resolution-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1      # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 3s   # 20 per minute
    Namespace: "cx"

WatsonTicketEventPublisher:
  QueueName: "uat-cx-watson-ticket-event-queue"

WatsonTicketEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-watson-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

WatsonCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-watson-create-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

CrmIssueTrackerIntegrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-crm-issue-tracker-integration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

CrmIssueTrackerIntegrationPublisher:
  QueueName: "uat-cx-crm-issue-tracker-integration-queue"

DisputeExternalPublisher:
  QueueName: "uat-cx-dispute-events-external-queue"

RMSEventPublisher:
  QueueName: "uat-rms-event-queue"

RewardsManualGiveawayEventPublisher:
  QueueName: "uat-rewards-manual-giveaway-event-queue"

DevActionPublisher:
  QueueName: "uat-dev-action-delay-queue"

IFTFileProcessorEventPublisher:
  QueueName: "uat-pay-international-fund-transfer-process-file-queue"

DevActionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-dev-action-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 60
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriberFifo:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-fd-events-cx-queue.fifo"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-cx-freshdesk-events"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

AuditLog:
  DefaultLimit: 6

Sherlock:
  SherlockCallbackURL: "https://uat-sherlock.pointz.in/external-api/v1/customer-auth"
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30

Secrets:
  Ids:
    SherlockApiKey: "uat/cx-sherlock/internal-api-key"
    DbUsernamePassword: "uat/rds/postgres"
    FreshchatAppId: "uat/cx/freshchat-sdk-app-id"
    FreshchatAppKey: "uat/cx/freshchat-sdk-app-key"
    RudderWriteKey: "uat/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "uat/gcloud/profiling-service-account-key"
    FederalPoolAccountNo: "uat/cx/federal-pool-acc-no"
    MonorailServiceAccountKey: "uat/monorail/service-account-private-key"
    AirflowUsernamePassword: "uat/data/airflow"
    KafkaCredentials: "uat/kafka/cx"
    KafkaCaCertificate: "uat/kafka/cert"
    IFTReportsSlackBotOauthToken: "uat/ift/slack-bot-oauth-token"
    StrapiApiKey: "uat/cx/strapi"


Transaction:
  ToleranceValue: 10
  NumTxnToFetch: 3
  PageSize: 20

Comms:
  PageSize: 20

Dispute:
  DefaultDisputeConfigVersion: "DISPUTE_CONFIG_VERSION_V6"
  IsRestrictedReleaseEnabledForConfigVersion: false
  MaxThresholdDurationForEscalation: "2h"
  MaxAttemptCountForReverseProcessing: 5
  IsGetNextQuestionsForAppV2Enabled: true
  IsUpiExternalProvenanceEvaluationEnabled: true
  S3BucketName: "epifi-federal-disputes"
  DisputeJobConfig:
    ReverseProcessingJobTimeout: 60m
  DisputeConfigVersionToSherlockReleaseConfigMap:
    DISPUTE_CONFIG_VERSION_V6:
      IsGroupCheckEnabled: false
    # Enable UDIR for internal users on sherlock
    DISPUTE_CONFIG_VERSION_V4:
      IsGroupCheckEnabled: true
      EnabledGroupMap:
        INTERNAL: true
  DisputeConfigVersionAndPlatformToReleaseConfMap:
    # Enabling V6(UDIR + DMP)
    DISPUTE_CONFIG_VERSION_V6:
      ANDROID:
        IsGroupCheckEnabled: false
        MinAppVersion: 240
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: false
        MinAppVersion: 335
        MaxAppVersion: 2147483647
    # Enabling V5(UDIR + UPI External going to non-UDIR flow + retry attempt on UDIR failure going to non-UDIR flow)
    DISPUTE_CONFIG_VERSION_V5:
      ANDROID:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 240
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 335
        MaxAppVersion: 2147483647
    # Enabling V4(UDIR + New questionnaire flow for internal users on android + IOS)
    DISPUTE_CONFIG_VERSION_V4:
      ANDROID:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 185
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: true
        EnabledGroupMap:
          INTERNAL: true
        MinAppVersion: 275
        MaxAppVersion: 2147483647
    # Enabling V3(New questionnaire flow for all users on prod)
    DISPUTE_CONFIG_VERSION_V3:
      ANDROID:
        IsGroupCheckEnabled: false
        MinAppVersion: 185
        MaxAppVersion: 2147483647
      IOS:
        IsGroupCheckEnabled: false
        MinAppVersion: 275
        MaxAppVersion: 2147483647

SalaryOpsConfig:
  SalaryProgramS3BucketName: "epifi-uat-salaryprogram"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    # on uat we are using common redis cluster with a different db
    DB: 0

AppLog:
  LogTTL: "168h" # 7 days
  MaxLogCountPerUser: 5
  # RPC size limit is 4 MB. So using 3.5MB as chunk size
  LogChunkSize: 3500000

Flags:
  TrimDebugMessageFromStatus: false

Payout:
  StatusCheckDelay: "30m"
PayoutStatusCheckPublisher:
  QueueName: "uat-cx-payout-status-check-event-queue"
PayoutStatusCheckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-payout-status-check-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

WaitlistSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-freelancer-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

RateLimit:
  MaxRequestsPerMinPerUser: 1000
  MaxRequestPerMinPerUserPerApiDefault: 30
  # replace all occurrences of . and / with _ in the full method name before adding it here
  # need to do this because . and / are interpreted differently by config loader and should not be used in key
  MaxRequestPerMinPerUserPerApiMap:
    _cx_app_log_AppLog_GetLogsData: 10000
    _cx_developer_ticket_summary_TicketSummary_GetTicketDetails: 20
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanDetails: 10
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanUserDetails: 10

RlConfig:
  ResourceMap:
    sherlock_user:
      Rate: 1000
      Period: 1m
    _cx_app_log_AppLog_GetLogsData:
      Rate: 1000
      Period: 1m
    _cx_developer_ticket_summary_TicketSummary_GetTicketDetails:
      Rate: 20
      Period: 1m
    api_default:
      Rate: 30
      Period: 1m
  Namespace: "cx"

UsePkgRateLimiter: true

CallRecording:
  CallRecordingBucketName: "epifi-data-prod-ozonetel-call-recs"
  CallTranscriptionBucketName: "epifi-ozonetel-transcription"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

OrderConfig:
  TxnCountForLastNTxns: 5

LivenessVideoConfig:
  S3BucketName: "epifi-uat-liveness"

ProcessTicketJobConfig:
  MaxTicketsThresholdMap:
    ONBOARDING: 10
    RE_ONBOARDING: 10
    UPI_PINSET: 10
    DEBIT_CARD: 10
  JobStatsEmailParam:
    FromEmailId: "<EMAIL>"
    FromEmailName: "Process Ticket Automation non-prod"
    ReceiverMailIdList:
      ONBOARDING:
        ReceiverMailInfo1:
          EmailName: "Diparth"
          EmailId: "<EMAIL>"
        ReceiverMailInfo2:
          EmailName: "Sachin"
          EmailId: "<EMAIL>"
        ReceiverMailInfo3:
          EmailName: "Hardik"
          EmailId: "<EMAIL>"
    EmailMsg:
      ONBOARDING: "Please find troubleshooting details in attachment."
  NumberOfDays: 89

MaxCountThresholdForFetchingBulkUserInfo: 500

BulkUserInfoViaEmailConfig:
  MaxCountThreshold: 10000

UpiDisputeAutoUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-upi-dispute-auto-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

AuthValidation:
  MethodListForSkippingAccessControlValidation: [
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
  ]

UpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-update-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80    # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 1m
    Namespace: "cx"

UpdateTicketPublisher:
  QueueName: "uat-cx-update-ticket-queue"

UploadCreditMISToVendorPublisher:
  QueueName: "uat-mf-upload-credit-mis-non-prod-queue"

CreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-create-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

CreateTicketPublisher:
  QueueName: "uat-cx-create-ticket-queue"

BulkTicketJobConfig:
  MaxTicketThreshold: 1000

Tracing:
  Enable: true

OzonetelCallEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-vn-ozonetel-call-details-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

TicketReconciliationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-ticket-data-reconciliation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"

FreshchatEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-vn-freshchat-action-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CelestialSignalWorkflowPublisher:
  QueueName: "uat-celestial-signal-workflow-queue"

AccountFreezeStatusConfig:
  CreditFreezeBannerElementId: "533da8e7-b8bb-448c-8539-e1109b8ad12e"

SherlockFeedbackDetailsConfig:
  PageSize: 20

RiskCasePublisher:
  QueueName: "uat-risk-cases-ingestion-queue"

RiskDisputePublisher:
  QueueName: "uat-risk-dispute-upload-queue"

RiskS3Config:
  BucketName: "epifi-uat-risk"

DataS3Config:
  BucketName: "epifi-raw-dev"
  S3Prefix: "uat/data/vendor/segmentation_service"
  StaticSegmentSrcFolderPath: "manual_dump/static_segment"
  StaticSegmentDestFolderPath: "static_segments"

InternationalFundTransfer:
  DocumentsBucketName: "epifi-uat-pay-international-fund-transfer"

AirflowConfig:
  TriggerDagUrl: "http://10.6.35.53:8080/api/v1/dags/%s/dagRuns"

RewardsOrderUpdateEventQueuePublisher:
  QueueName : "uat-rewards-order-update-queue"

RewardsCreditCardTxnEventQueuePublisher:
  QueueName: "uat-rewards-credit-card-txn-event-queue"

CallRoutingEventPublisher:
  TopicName: "uat-cx-call-routing-event-topic"

TicketUpdateEventPublisher:
  TopicName: "uat-cx-ticket-update-event-topic"

CreateTicketEventPublisher:
  TopicName: "uat-cx-ticket-create-event-topic"

FederalEscalationCreateEventPublisher:
  QueueName: "uat-cx-escalation-creation-queue"

FederalEscalationUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-escalation-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FederalEscalationCreationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-escalation-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

CasperItcDownloadFileQueuePublisher:
  QueueName: "uat-casper-itc-download-file-queue"

OrderUpdateEventForTxnCategorizationPublisher:
  QueueName: "uat-categorizer-update-order-queue"


AATxnCategorizationPublisher:
  QueueName: "uat-categorizer-aa-txn-queue"

CCTxnCategorizationPublisher:
  QueueName: "uat-categorizer-cc-transaction-event-queue"


RudderEventKafkaConsumerGroup:
  StartOnServerStart: false
  GroupID: "cx_activity_uat"
  # Kafka only has one non-prod env so same brokers and topic across all non-prod env.
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  Topic: "qa.events.tech"
  RateLimitConfig:
    RedisOptions:
      IsSecureRedis: true
      Options:
        Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
        Password: ""
        DB: 0
    ResourceMap:
      consumer_group:
        Rate: 100
        Period: 1s
    Namespace: "cx"

ErrorActivityConfig:
  IsPipingErrorEventToWatsonEnabled: false

S3EventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cx-s3-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

StrapiConfig:
  BaseURL: "https://strapi.uat.pointz.in/api/"
  ApiKey: "staging/cx/strapi"
  HttpClientConfig:
    Transport:
      DialContext:
        Timeout: 30s
        KeepAlive: 30s
      TLSHandshakeTimeout: 10s
      MaxIdleConns: 100
      IdleConnTimeout: 90s
    Timeout: 10s

CXFreshdeskTicketBaseURL: "https://ficaretesting.freshdesk.com/a/tickets/%s"

S3BucketNameForFileGenerator:
  CamsS3Bucket: "epifi-uat-mutualfund"
  KarvyS3Bucket: "epifi-uat-mutualfund-karvy"

SalaryProgramLeadManagementConfig:
  SalaryProgramS3BucketName: "epifi-uat-salaryprogram"
  SalaryProgramB2BS3BucketName: "epifi-uat-salaryprogram-b2b"
  LeadDetailsExcelSheetPathB2B: "LeadDetails.xlsx"

FederalEscalationConfig:
  FederalEscalationAttachmentBucketName: "epifi-uat-cx-ticket-attachments"
  QueueId: "300000412618875"
  QPHRateLimit: 500

EpifiIconS3Config:
  BucketName: "epifi-icons"
