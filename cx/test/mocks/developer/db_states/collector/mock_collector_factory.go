// Code generated by MockGen. DO NOT EDIT.
// Source: cx/developer/db_states/collector/collector_factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	collector "github.com/epifi/gamma/cx/developer/db_states/collector"
	gomock "github.com/golang/mock/gomock"
)

// MockICollectorFactory is a mock of ICollectorFactory interface.
type MockICollectorFactory struct {
	ctrl     *gomock.Controller
	recorder *MockICollectorFactoryMockRecorder
}

// MockICollectorFactoryMockRecorder is the mock recorder for MockICollectorFactory.
type MockICollectorFactoryMockRecorder struct {
	mock *MockICollectorFactory
}

// NewMockICollectorFactory creates a new mock instance.
func NewMockICollectorFactory(ctrl *gomock.Controller) *MockICollectorFactory {
	mock := &MockICollectorFactory{ctrl: ctrl}
	mock.recorder = &MockICollectorFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICollectorFactory) EXPECT() *MockICollectorFactoryMockRecorder {
	return m.recorder
}

// GetCollector mocks base method.
func (m *MockICollectorFactory) GetCollector(serviceName db_state.Service) (collector.Collector, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCollector", serviceName)
	ret0, _ := ret[0].(collector.Collector)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCollector indicates an expected call of GetCollector.
func (mr *MockICollectorFactoryMockRecorder) GetCollector(serviceName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCollector", reflect.TypeOf((*MockICollectorFactory)(nil).GetCollector), serviceName)
}

// RegisterCollector mocks base method.
func (m *MockICollectorFactory) RegisterCollector(serviceName db_state.Service, collector collector.Collector) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RegisterCollector", serviceName, collector)
}

// RegisterCollector indicates an expected call of RegisterCollector.
func (mr *MockICollectorFactoryMockRecorder) RegisterCollector(serviceName, collector interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RegisterCollector", reflect.TypeOf((*MockICollectorFactory)(nil).RegisterCollector), serviceName, collector)
}
