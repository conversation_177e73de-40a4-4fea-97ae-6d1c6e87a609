// Code generated by MockGen. DO NOT EDIT.
// Source: cx/call_routing/dao/dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	call_routing "github.com/epifi/gamma/api/cx/call_routing"
	gomock "github.com/golang/mock/gomock"
)

// MockICallRoutingManualMappingsDao is a mock of ICallRoutingManualMappingsDao interface
type MockICallRoutingManualMappingsDao struct {
	ctrl     *gomock.Controller
	recorder *MockICallRoutingManualMappingsDaoMockRecorder
}

// MockICallRoutingManualMappingsDaoMockRecorder is the mock recorder for MockICallRoutingManualMappingsDao
type MockICallRoutingManualMappingsDaoMockRecorder struct {
	mock *MockICallRoutingManualMappingsDao
}

// NewMockICallRoutingManualMappingsDao creates a new mock instance
func NewMockICallRoutingManualMappingsDao(ctrl *gomock.Controller) *MockICallRoutingManualMappingsDao {
	mock := &MockICallRoutingManualMappingsDao{ctrl: ctrl}
	mock.recorder = &MockICallRoutingManualMappingsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockICallRoutingManualMappingsDao) EXPECT() *MockICallRoutingManualMappingsDaoMockRecorder {
	return m.recorder
}

// BulkCreateMappings mocks base method
func (m *MockICallRoutingManualMappingsDao) BulkCreateMappings(ctx context.Context, mappings []*call_routing.CallRoutingManualMapping) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkCreateMappings", ctx, mappings)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkCreateMappings indicates an expected call of BulkCreateMappings
func (mr *MockICallRoutingManualMappingsDaoMockRecorder) BulkCreateMappings(ctx, mappings interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCreateMappings", reflect.TypeOf((*MockICallRoutingManualMappingsDao)(nil).BulkCreateMappings), ctx, mappings)
}

// GetByIdentifier mocks base method
func (m *MockICallRoutingManualMappingsDao) GetByIdentifier(ctx context.Context, identifierType call_routing.IdentifierType, identifierValue string) (*call_routing.CallRoutingManualMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIdentifier", ctx, identifierType, identifierValue)
	ret0, _ := ret[0].(*call_routing.CallRoutingManualMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIdentifier indicates an expected call of GetByIdentifier
func (mr *MockICallRoutingManualMappingsDaoMockRecorder) GetByIdentifier(ctx, identifierType, identifierValue interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIdentifier", reflect.TypeOf((*MockICallRoutingManualMappingsDao)(nil).GetByIdentifier), ctx, identifierType, identifierValue)
}

// BulkDeleteMappings mocks base method
func (m *MockICallRoutingManualMappingsDao) BulkDeleteMappings(ctx context.Context, mappings []*call_routing.CallRoutingManualMapping) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkDeleteMappings", ctx, mappings)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkDeleteMappings indicates an expected call of BulkDeleteMappings
func (mr *MockICallRoutingManualMappingsDaoMockRecorder) BulkDeleteMappings(ctx, mappings interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkDeleteMappings", reflect.TypeOf((*MockICallRoutingManualMappingsDao)(nil).BulkDeleteMappings), ctx, mappings)
}
