// Code generated by MockGen. DO NOT EDIT.
// Source: cx/sherlock_user/dao/dao.go

// Package mock_sherlock_user is a generated GoMock package.
package mock_sherlock_user

import (
	context "context"
	reflect "reflect"

	casbin "github.com/epifi/gamma/api/casbin"
	sherlock_user "github.com/epifi/gamma/api/cx/sherlock_user"
	dao "github.com/epifi/gamma/cx/payout/dao"
	model "github.com/epifi/gamma/cx/sherlock_user/dao/model"
	gomock "github.com/golang/mock/gomock"
)

// MockSherlockUserInfoDao is a mock of SherlockUserInfoDao interface.
type MockSherlockUserInfoDao struct {
	ctrl     *gomock.Controller
	recorder *MockSherlockUserInfoDaoMockRecorder
}

// MockSherlockUserInfoDaoMockRecorder is the mock recorder for MockSherlockUserInfoDao.
type MockSherlockUserInfoDaoMockRecorder struct {
	mock *MockSherlockUserInfoDao
}

// NewMockSherlockUserInfoDao creates a new mock instance.
func NewMockSherlockUserInfoDao(ctrl *gomock.Controller) *MockSherlockUserInfoDao {
	mock := &MockSherlockUserInfoDao{ctrl: ctrl}
	mock.recorder = &MockSherlockUserInfoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSherlockUserInfoDao) EXPECT() *MockSherlockUserInfoDaoMockRecorder {
	return m.recorder
}

// CreateSherlockUser mocks base method.
func (m *MockSherlockUserInfoDao) CreateSherlockUser(ctx context.Context, sherlockUser *sherlock_user.SherlockUserInfo) (*sherlock_user.SherlockUserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSherlockUser", ctx, sherlockUser)
	ret0, _ := ret[0].(*sherlock_user.SherlockUserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSherlockUser indicates an expected call of CreateSherlockUser.
func (mr *MockSherlockUserInfoDaoMockRecorder) CreateSherlockUser(ctx, sherlockUser interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSherlockUser", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).CreateSherlockUser), ctx, sherlockUser)
}

// CreateUserInfo mocks base method.
func (m *MockSherlockUserInfoDao) CreateUserInfo(ctx context.Context, sherlockUser *sherlock_user.SherlockUserInfo) (*model.SherlockUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUserInfo", ctx, sherlockUser)
	ret0, _ := ret[0].(*model.SherlockUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUserInfo indicates an expected call of CreateUserInfo.
func (mr *MockSherlockUserInfoDaoMockRecorder) CreateUserInfo(ctx, sherlockUser interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUserInfo", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).CreateUserInfo), ctx, sherlockUser)
}

// CreateUserRole mocks base method.
func (m *MockSherlockUserInfoDao) CreateUserRole(ctx context.Context, userId string, role casbin.AccessLevel) (*model.SherlockUserRole, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUserRole", ctx, userId, role)
	ret0, _ := ret[0].(*model.SherlockUserRole)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUserRole indicates an expected call of CreateUserRole.
func (mr *MockSherlockUserInfoDaoMockRecorder) CreateUserRole(ctx, userId, role interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUserRole", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).CreateUserRole), ctx, userId, role)
}

// DeleteUserRoles mocks base method.
func (m *MockSherlockUserInfoDao) DeleteUserRoles(ctx context.Context, userId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserRoles", ctx, userId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserRoles indicates an expected call of DeleteUserRoles.
func (mr *MockSherlockUserInfoDaoMockRecorder) DeleteUserRoles(ctx, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserRoles", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).DeleteUserRoles), ctx, userId)
}

// GetBulkUserInfo mocks base method.
func (m *MockSherlockUserInfoDao) GetBulkUserInfo(ctx context.Context, pageToken *dao.PageToken, pageSize int64) ([]*sherlock_user.SherlockUserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBulkUserInfo", ctx, pageToken, pageSize)
	ret0, _ := ret[0].([]*sherlock_user.SherlockUserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBulkUserInfo indicates an expected call of GetBulkUserInfo.
func (mr *MockSherlockUserInfoDaoMockRecorder) GetBulkUserInfo(ctx, pageToken, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBulkUserInfo", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).GetBulkUserInfo), ctx, pageToken, pageSize)
}

// GetSherlockUser mocks base method.
func (m *MockSherlockUserInfoDao) GetSherlockUser(ctx context.Context, emailId string) (*sherlock_user.SherlockUserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSherlockUser", ctx, emailId)
	ret0, _ := ret[0].(*sherlock_user.SherlockUserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSherlockUser indicates an expected call of GetSherlockUser.
func (mr *MockSherlockUserInfoDaoMockRecorder) GetSherlockUser(ctx, emailId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSherlockUser", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).GetSherlockUser), ctx, emailId)
}

// GetUserInfo mocks base method.
func (m *MockSherlockUserInfoDao) GetUserInfo(ctx context.Context, emailId string) (*model.SherlockUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfo", ctx, emailId)
	ret0, _ := ret[0].(*model.SherlockUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockSherlockUserInfoDaoMockRecorder) GetUserInfo(ctx, emailId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).GetUserInfo), ctx, emailId)
}

// GetUserRoles mocks base method.
func (m *MockSherlockUserInfoDao) GetUserRoles(ctx context.Context, userId string) ([]*model.SherlockUserRole, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRoles", ctx, userId)
	ret0, _ := ret[0].([]*model.SherlockUserRole)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserRoles indicates an expected call of GetUserRoles.
func (mr *MockSherlockUserInfoDaoMockRecorder) GetUserRoles(ctx, userId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRoles", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).GetUserRoles), ctx, userId)
}

// UpdateSherlockUser mocks base method.
func (m *MockSherlockUserInfoDao) UpdateSherlockUser(ctx context.Context, sherlockUser *sherlock_user.SherlockUserInfo, updateMask []sherlock_user.SherlockUserFieldMask) (*sherlock_user.SherlockUserInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSherlockUser", ctx, sherlockUser, updateMask)
	ret0, _ := ret[0].(*sherlock_user.SherlockUserInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSherlockUser indicates an expected call of UpdateSherlockUser.
func (mr *MockSherlockUserInfoDaoMockRecorder) UpdateSherlockUser(ctx, sherlockUser, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSherlockUser", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).UpdateSherlockUser), ctx, sherlockUser, updateMask)
}

// UpdateUserInfo mocks base method.
func (m *MockSherlockUserInfoDao) UpdateUserInfo(ctx context.Context, sherlockUser *sherlock_user.SherlockUserInfo, updateMask []string) (*model.SherlockUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserInfo", ctx, sherlockUser, updateMask)
	ret0, _ := ret[0].(*model.SherlockUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserInfo indicates an expected call of UpdateUserInfo.
func (mr *MockSherlockUserInfoDaoMockRecorder) UpdateUserInfo(ctx, sherlockUser, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserInfo", reflect.TypeOf((*MockSherlockUserInfoDao)(nil).UpdateUserInfo), ctx, sherlockUser, updateMask)
}
