package kyc

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	cxSgKycPb "github.com/epifi/gamma/api/cx/stockguardian/kyc"
	sgApiGwKycPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
)

func (s *Service) GetS3PreSignedURL(ctx context.Context, req *cxSgKycPb.GetS3PreSignedURLRequest) (*cxSgKycPb.GetS3PreSignedURLResponse, error) {
	logger.Debug(ctx, fmt.Sprintf("GetS3PreSignedURL request map : %+v", req.GetFileNameS3URLMap()))
	signResp, errSign := s.sgApiGwKycClient.GetS3PreSignedURL(ctx, &sgApiGwKycPb.GetS3PreSignedURLRequest{
		FileNameS3URLMap: req.GetFileNameS3URLMap(),
	})
	if rpcErr := epifigrpc.RPCError(signResp, errSign); rpcErr != nil {
		logger.Error(ctx, "error while getting pre signed url", zap.Error(rpcErr))
		return &cxSgKycPb.GetS3PreSignedURLResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	return &cxSgKycPb.GetS3PreSignedURLResponse{
		Status:                    rpc.StatusOk(),
		FileNameS3PreSignedURLMap: signResp.GetFileNameS3PreSignedURLMap(),
	}, nil
}
