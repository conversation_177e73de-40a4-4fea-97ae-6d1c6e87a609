package kyc

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/file"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cxSgKycPb "github.com/epifi/gamma/api/cx/stockguardian/kyc"
	sgApiGwKycPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
)

func (s *Service) UploadManuallyReviewedDocuments(ctx context.Context, req *cxSgKycPb.UploadManuallyReviewedDocumentsRequest) (*cxSgKycPb.UploadManuallyReviewedDocumentsResponse, error) {
	if len(req.GetFiles()) == 0 || req.GetApplicationId() == "" || req.GetCkycReferenceId() == "" {
		return &cxSgKycPb.UploadManuallyReviewedDocumentsResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}
	var (
		redactedFiles []*file.File
		redactionMap  = make(map[string]common.BooleanEnum)
	)

	for _, redactedFile := range req.GetFiles() {
		f := redactedFile.GetFile()
		if f.GetFileName() == "" {
			logger.Error(ctx, "file name is empty")
			return &cxSgKycPb.UploadManuallyReviewedDocumentsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("file name is empty"),
			}, nil
		}
		if redactedFile.GetIsRedacted().ToBool() {
			redactedFiles = append(redactedFiles, f)
			// image must not be empty if redacted
			if f.GetBase64Data() == "" {
				logger.Error(ctx, "image base64 data is empty", zap.String("file_name", f.GetFileName()))
				return &cxSgKycPb.UploadManuallyReviewedDocumentsResponse{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("filimage base64 data is empty"),
				}, nil
			}
		}
		redactionMap[f.GetFileName()] = redactedFile.GetIsRedacted()
	}

	uploadResp, errUpload := s.sgApiGwKycClient.UploadRedactedCKYCDocuments(ctx, &sgApiGwKycPb.UploadRedactedCKYCDocumentsRequest{
		CkycReferenceId:        req.GetCkycReferenceId(),
		ApplicationId:          req.GetApplicationId(),
		RedactedDocuments:      redactedFiles,
		ReviewedByEmail:        req.GetHeader().GetAgentEmail(),
		FileNameToRedactionMap: redactionMap,
	})
	if rpcErr := epifigrpc.RPCError(uploadResp, errUpload); rpcErr != nil {
		logger.Error(ctx, "Error while uploading redacted documents", zap.Error(rpcErr))
		return &cxSgKycPb.UploadManuallyReviewedDocumentsResponse{
			Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
		}, nil
	}

	return &cxSgKycPb.UploadManuallyReviewedDocumentsResponse{
		Status: rpc.StatusOk(),
	}, nil
}
