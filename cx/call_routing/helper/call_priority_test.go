package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"

	callRoutingPb "github.com/epifi/gamma/api/cx/call_routing"
	fireflyMocks "github.com/epifi/gamma/api/firefly/mocks"
	salaryProgramMock "github.com/epifi/gamma/api/salaryprogram/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user/mocks"
	onbMock "github.com/epifi/gamma/api/user/onboarding/mocks"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/call_routing/dao"
	mock_priority_helper "github.com/epifi/gamma/cx/test/mocks/call_routing/helper/priority_helper"
	mock_helper "github.com/epifi/gamma/cx/test/mocks/data_collector/helper"
	mock_helper2 "github.com/epifi/gamma/cx/test/mocks/helper"
)

func TestCallRoutingHelper_GetCallPriorityByRoutingChannelAndPhoneNumber(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()
	onbClient := onbMock.NewMockOnboardingClient(ctr)
	salaryProgramClient := salaryProgramMock.NewMockSalaryProgramClient(ctr)
	mockCallRoutingManualMappingsDao := mock_dao.NewMockICallRoutingManualMappingsDao(ctr)
	mockDataCollectorHelper := mock_helper.NewMockIDataCollectorHelper(ctr)
	mockCustomerIdentifier := mock_helper2.NewMockICustomerIdentifier(ctr)
	mockUserClient := mocks.NewMockUsersClient(ctr)
	mockPriorityHelper := mock_priority_helper.NewMockIPriorityHelper(ctr)
	mockFireflyClient := fireflyMocks.NewMockFireflyClient(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)

	type args struct {
		mocks		[]interface{}
		ctx		context.Context
		routingChannel	callRoutingPb.RoutingChannel
		phoneNumber	*commontypes.PhoneNumber
	}
	tests := []struct {
		name	string
		args	args
		want	int
		wantErr	bool
	}{
		{
			name:	"actor call failed",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("internal error")),
				},
				ctx:		context.Background(),
				routingChannel:	callRoutingPb.RoutingChannel_ROUTING_CHANNEL_BLOCKED_USER_LOW_PRIORITY,
				phoneNumber: &commontypes.PhoneNumber{
					CountryCode:	91,
					NationalNumber:	********,
				},
			},
			want:		rcts.conf.CallRoutingConfig().DefaultPriorityValue(),
			wantErr:	true,
		},
		{
			name:	"call to priority helper failed",
			args: args{
				mocks: []interface{}{
					mockCustomerIdentifier.EXPECT().GetActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(&typesPb.Actor{Id: "act-101"}, nil),
					mockPriorityHelper.EXPECT().GetPriorityByRoutingChannelAndActorId(gomock.Any(), gomock.Any(), gomock.Any()).Return(0, errors.New("priority error")),
				},
				ctx:		context.Background(),
				routingChannel:	callRoutingPb.RoutingChannel_ROUTING_CHANNEL_BLOCKED_USER_LOW_PRIORITY,
				phoneNumber: &commontypes.PhoneNumber{
					CountryCode:	91,
					NationalNumber:	********,
				},
			},
			want:		rcts.conf.CallRoutingConfig().DefaultPriorityValue(),
			wantErr:	true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewCallRoutingHelper(onbClient, rcts.conf, mockCallRoutingManualMappingsDao, salaryProgramClient, mockDataCollectorHelper,
				mockCustomerIdentifier, mockUserClient, mockPriorityHelper, mockFireflyClient, nil, nil, nil, nil,
				mockReleaseEvaluator, nil, nil)
			got, err := s.GetCallPriorityByRoutingChannelAndPhoneNumber(tt.args.ctx, tt.args.routingChannel, tt.args.phoneNumber)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCallPriorityByRoutingChannelAndPhoneNumber() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCallPriorityByRoutingChannelAndPhoneNumber() got = %v, want %v", got, tt.want)
			}
		})
	}
}
