package firefly

import (
	"context"
	"time"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	accEnumPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	"github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/firefly/pinot"
	rewardPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/typesv2/webui"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

// getUserSpendsForMilestoneRewardsAndRenewalFeesWaiver returns user spends eligible for
// 1 -user spends eligible for milestone rewards
// 2- user spends eligible for renewal fees waiver on fi's logic
// 3- user spends eligible for renewal fees waiver on M2P's logic
// 4- error
// in the same order as above
func (s *Service) getUserSpendsForMilestoneRewardsAndRenewalFeesWaiver(ctx context.Context, actorId string, account *accounting.CreditAccount) (*money.Money, *money.Money, *money.Money, error) {
	startTime := account.GetCreatedAt()
	endTime := timestamp.New(startTime.AsTime().AddDate(1, 0, 0))

	waiverSpendsFiLogic, err := s.getEffectiveUserSpendsByOntologyIds(ctx, actorId, startTime, endTime, ffPkg.GetOntologyIdsToExcludeForRenewalFeesWaiver())
	if err != nil {
		logger.Error(ctx, "error in getting renewal fee waiver eligible spends", zap.Error(err))
		return nil, nil, nil, err
	}

	waiverSpendsM2pLogic, err := s.getEffectiveUserSpendsByOntologyIds(ctx, actorId, startTime, endTime, nil)
	if err != nil {
		logger.Error(ctx, "error in getting renewal fee waiver eligible spends", zap.Error(err))
		return nil, nil, nil, err
	}

	milestoneSpends, err := s.getEffectiveUserSpendsByOntologyIds(ctx, actorId, startTime, endTime, ffPkg.GetOntologyIdsToExcludeForMilestoneRewards())
	if err != nil {
		logger.Error(ctx, "error in getting renewal fee waiver eligible spends", zap.Error(err))
		return nil, nil, nil, err
	}
	return milestoneSpends, waiverSpendsFiLogic, waiverSpendsM2pLogic, err
}

func (s *Service) getEffectiveUserSpendsByOntologyIds(ctx context.Context, actorId string, startTime, endTime *timestamp.Timestamp, ontIdsToExclude []string) (*money.Money, error) {
	totalSpends, err := s.txnAggClient.GetTransactionAggregates(ctx, &pinot.GetTransactionAggregatesRequest{
		ActorId:              actorId,
		FromExecutedTime:     startTime,
		ToExecutedTime:       endTime,
		TransactionStatus:    []accEnumPb.TransactionStatus{accEnumPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS},
		ExcludeDsOntologyIds: ontIdsToExclude,
		TransactionType:      accEnumPb.TransactionType_TRANSACTION_TYPE_DEBIT,
	})
	if grpcErr := epifigrpc.RPCError(totalSpends, err); grpcErr != nil {
		logger.Error(ctx, "error in GetTransactionAggregates", zap.Error(grpcErr))
		return nil, grpcErr
	}

	totalRefunds, err := s.txnAggClient.GetRefundTransactionAggregates(ctx, &pinot.GetRefundTransactionAggregatesRequest{
		ActorId:                    actorId,
		FromParentExecutedTime:     startTime,
		ToParentExecutedTime:       endTime,
		ExcludeParentDsOntologyIds: ontIdsToExclude,
		TransactionStatus:          []accEnumPb.TransactionStatus{accEnumPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS},
	})
	if grpcErr := epifigrpc.RPCError(totalRefunds, err); grpcErr != nil {
		logger.Error(ctx, "error in GetRefundTransactionAggregates", zap.Error(grpcErr))
		return nil, grpcErr
	}

	effectiveSpends, err := money.Subtract(totalSpends.GetTransactionAggregates().GetAmount(), totalRefunds.GetTransactionAggregates().GetAmount())
	if err != nil {
		logger.Error(ctx, "error in subtracting refund from total spend", zap.Error(err))
		return nil, err
	}

	return money.NewMoney(effectiveSpends), nil
}

func (s *Service) getMilestoneRewardsByActorIdAndRewardType(ctx context.Context, actorId string, rewardOfferType rewardPb.RewardOfferType, accCreationTime *timestamp.Timestamp) ([]*rewardPb.Reward, error) {
	rewardResp, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardPb.RewardsByActorIdRequest{
		ActorId: actorId,
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
		FiltersV2: &rewardPb.RewardsByActorIdRequest_FiltersV2{
			AndFilter: &rewardPb.RewardsByActorIdRequest_Filter{
				RewardOfferType: rewardOfferType,
				StartDate:       accCreationTime,
				EndDate:         timestamp.Now(),
			},
		},
	})

	if grpcErr := epifigrpc.RPCError(rewardResp, err); grpcErr != nil && !rewardResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in GetRewardsByActorId", zap.Error(grpcErr), zap.String(logger.REWARD_TYPE, rewardOfferType.String()))
		return nil, grpcErr
	}

	return rewardResp.GetRewards(), nil
}

func (s *Service) getRenewalBillAndDueDate(ctx context.Context, actorId string, creditAccountCreationDate *timestamp.Timestamp, nthYear int) (*time.Time, *time.Time, error) {
	var firstDueDate time.Time
	cardResp, err := s.ffClient.GetCreditCard(ctx, &firefly.GetCreditCardRequest{
		GetBy: &firefly.GetCreditCardRequest_ActorId{
			ActorId: actorId,
		},
		SelectFieldMasks: []enums.CreditCardFieldMask{
			enums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
		},
	})

	if grpcErr := epifigrpc.RPCError(cardResp, err); grpcErr != nil {
		logger.Error(ctx, "error in GetCreditCard", zap.Error(grpcErr))
		return nil, nil, grpcErr
	}

	billGenDate := cardResp.GetCreditCard().GetBasicInfo().GetBillGenDate()
	creationYear, creationMonth, creationDate := creditAccountCreationDate.AsTime().In(dateTimePkg.IST).Date()

	switch {
	case int(billGenDate) > creationDate:
		firstDueDate = time.Date(creationYear, creationMonth, int(billGenDate), 0, 0, 0, 0, dateTimePkg.IST)
	default:
		firstDueDate = time.Date(creationYear, creationMonth+1, int(billGenDate), 0, 0, 0, 0, dateTimePkg.IST)
	}

	renewalFeesBillDate := dateTimePkg.AddNMonths(&firstDueDate, nthYear*12)
	renewalFeesBillDueDate := renewalFeesBillDate.AddDate(0, 0, 18)

	return renewalFeesBillDate, &renewalFeesBillDueDate, nil
}

func getRenewalFeesTableRowsForWaivedFees(spendCriteria string) []*webui.TableRow {
	return []*webui.TableRow{
		{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				"UserSpendDetails": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: spendCriteria,
					},
				},
				"RenewalFees": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: "Not applicable",
					},
				},
				"RenewalFeesPaymentStatus": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: "Not applicable",
					},
				},
				"AddedToBill": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: "Not applicable",
					},
				},
				"DueDate": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: "Not applicable",
					},
				},
			},
		},
	}
}

func getRenewalFeesTableRowsForFeesNotWaived(spendCriteria, paymentStatus, addToBill, dueDate string) []*webui.TableRow {
	return []*webui.TableRow{
		{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				"UserSpendDetails": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: spendCriteria,
					},
				},
				"RenewalFees": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: "2360 INR",
					},
				},
				"RenewalFeesPaymentStatus": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: paymentStatus,
					},
				},
				"AddedToBill": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: addToBill,
					},
				},
				"DueDate": &webui.TableCell{
					ValueV2: &webui.TableCell_StringValue{
						StringValue: dueDate,
					},
				},
			},
		},
	}
}
