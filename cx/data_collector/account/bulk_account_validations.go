package account

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"io/ioutil"
	"strconv"
	"time"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bcPb "github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	"github.com/epifi/gamma/cx/config"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/gocarina/gocsv"
	timestampPb "github.com/golang/protobuf/ptypes/timestamp"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
)

// Validation is the csv format in which the sherlock agent uploads the data
type Validation struct {
	EmailId       string `csv:"Email ID"`
	AccountNumber string `csv:"Account Number"`
	Ifsc          string `csv:"Ifsc Code"`
	UserGivenName string `csv:"Name"`
}

// VerificationResult is the csv format in which we write results of all the account validations performed
type VerificationResult struct {
	EmailId       string `csv:"Email ID"`
	AccountNumber string `csv:"Account Number"`
	Ifsc          string `csv:"Ifsc Code"`
	UserGivenName string `csv:"Name"`
	Status        string `csv:"Overall Status"`
	FailureReason string `csv:"Failure Reason"`
}

// FederalCloseAccountRequestFormat is the csv format in which Federal Bank has requested data for account closures
type FederalCloseAccountRequestFormat struct {
	SLNo               string `csv:"SL No"`                      // Bank fills
	NEFT               string `csv:"NEFT"`                       // NEFT for all
	FederalBank        string `csv:"NEFT"`                       // Federal Bank for all
	PoolAccountNo      string `csv:"DEBIT ACCNT NO"`             // Federal's Pool Account for closed accounts
	Amnt               string `csv:"AMNT"`                       // Bank fills
	Date               string `csv:"DATE"`                       // Today's date in DDMMYYYY format
	AccHoldersName     string `csv:"ACCTNAME"`                   // Account holder's name of user given account
	AccIfsc            string `csv:"IFSC"`                       // IFSC Code of external account
	AccIfsc2           string `csv:"IFSC"`                       // Account holder's name of user given account
	NEFT2              string `csv:"NEFT"`                       // NEFT for all
	Code               string `csv:"CODE"`                       // 10 for all
	AccNumber          string `csv:"Account No"`                 // Account number of user given account
	Remarks            string `csv:"Remarks"`                    // "FiAccountNumber CLOSURE" format ex. "555501XXXXXXXX CLOSURE"
	AccountStatus      string `csv:"Account Status"`             // AccountStatus denotes if account is closed/not from federals end
	AccountBalance     string `csv:"Last Known Account Balance"` // AccountBalance is the last known account balance
	BalanceCaptureTime string `csv:"Balance Captured At"`        // BalanceCaptureTime is the time at which AccountBalance was last captured
}

func convertToExtAcctRequest(val *Validation, agentEmailId, actorId string) *extacct.AddBankAccountRequest {
	return &extacct.AddBankAccountRequest{
		ActorId:       actorId,
		Ifsc:          val.Ifsc,
		AccountNumber: val.AccountNumber,
		UserGivenName: val.UserGivenName,
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_SHERLOCK,
			Email:  agentEmailId,
		},
	}
}

func convertToAccVerificationResultCsvFormat(val *Validation, status, customFailureReason string) *VerificationResult {
	return &VerificationResult{
		EmailId:       val.EmailId,
		AccountNumber: val.AccountNumber,
		Ifsc:          val.Ifsc,
		UserGivenName: val.UserGivenName,
		Status:        status,
		FailureReason: customFailureReason,
	}
}

func convertToFederalCloseAccFormat(verifiedAccount *extacct.BankAccount, fiAcctNo, federalPoolNo, accountStatus, balance, balanceTime string) *FederalCloseAccountRequestFormat {
	return &FederalCloseAccountRequestFormat{
		SLNo:               "",
		NEFT:               "NEFT",
		FederalBank:        "Federal Bank",
		PoolAccountNo:      federalPoolNo,
		Amnt:               "",
		Date:               fmt.Sprintf("%v%v%v", time.Now().Day(), time.Now().Month(), time.Now().Year()),
		AccHoldersName:     verifiedAccount.GetName(),
		AccIfsc:            verifiedAccount.GetIfsc(),
		AccIfsc2:           verifiedAccount.GetIfsc(),
		NEFT2:              "NEFT",
		Code:               "10",
		AccNumber:          verifiedAccount.GetAccountNumber(),
		Remarks:            fiAcctNo + " CLOSURE",
		AccountStatus:      accountStatus,
		AccountBalance:     balance,
		BalanceCaptureTime: balanceTime,
	}
}

func (s *Service) getUserAndActorFromEmailId(ctx context.Context, emailId string) (string, string, error) {
	getUserResp, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_EmailId{
			EmailId: emailId,
		},
	})
	if grpcErr := epifigrpc.RPCError(getUserResp, err); grpcErr != nil {
		cxLogger.Error(ctx, "error getting user from email", zap.Error(err))
		return "", "", err
	}
	getActorResp, err := s.actorClient.GetActorByEntityId(ctx, &actor.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: getUserResp.GetUser().GetId(),
	})
	if grpcErr := epifigrpc.RPCError(getActorResp, err); grpcErr != nil {
		cxLogger.Error(ctx, "error getting actor from user id", zap.Error(err))
		return "", "", err
	}
	return getUserResp.GetUser().GetId(), getActorResp.GetActor().GetId(), nil
}

func (s *Service) getSavingsAccountFromUserId(ctx context.Context, userId string) (*savings.Account, error) {
	getSavingsAccResp, err := s.savingsClient.GetAccount(ctx, &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: userId,
		},
	})
	if err != nil {
		cxLogger.Error(ctx, "error getting savings account from user id", zap.Error(err))
		return nil, err
	}
	return getSavingsAccResp.GetAccount(), nil
}

// getAccountBalanceDuringClosure returns list of (balance, balance captured at) from closed accounts balance transfer table
func (s *Service) getAccountBalanceDuringClosure(ctx context.Context, savingsId string) (string, string) {
	resp, err := s.savingsClient.GetClosedAccountBalTransferData(ctx, &savings.GetClosedAccountBalTransferDataRequest{SavingsAccountId: savingsId})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil && !resp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while getting closed account balance", zap.Error(grpcErr))
		return "", ""
	}
	var balances, times string
	for _, cbt := range resp.GetEntries() {
		if cbt.GetLastKnownBalance().GetAvailableBalance() == nil || cbt.GetLastKnownBalance().GetLastUpdatedAt() == nil {
			continue
		}
		balances, times = appendBalanceDtls(cbt.GetLastKnownBalance().GetAvailableBalance(), cbt.GetLastKnownBalance().GetLastUpdatedAt(), balances, times)
	}
	return balances, times
}

func appendBalanceDtls(bal *gmoney.Money, capturedAt *timestampPb.Timestamp, balances, times string) (string, string) {
	balance := float64(bal.GetUnits()) + float64(bal.GetNanos())/1e9
	if balances == "" {
		balances = strconv.FormatFloat(balance, 'G', -1, 64)
		times = capturedAt.AsTime().String()
		return balances, times
	}
	balances = balances + ", " + strconv.FormatFloat(balance, 'G', -1, 64)
	times = times + ", " + capturedAt.AsTime().String()
	return balances, times
}

// validateAccounts does the bank account verification of all the records present in requestVals array and returns aggregated result arrays
// for all the verifications performed and successful verifications that needs to be shared with the partner bank
func (s *Service) validateAccounts(ctx context.Context, requestVals []*Validation, agentEmailId string) ([]*VerificationResult, []*FederalCloseAccountRequestFormat) {
	var accVeriResults []*VerificationResult
	var federalCloseAccDetails []*FederalCloseAccountRequestFormat
	for _, val := range requestVals {
		userId, actorId, err := s.getUserAndActorFromEmailId(ctx, val.EmailId)
		if err != nil {
			cxLogger.Error(ctx, "error fetching userId and actorId from email", zap.Error(err))
			accVeriResults = append(accVeriResults,
				convertToAccVerificationResultCsvFormat(val, extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(), "Internal Failure - Get User/Actor"))
			// we don't  want to return from rpc since there are multiple validations happening
			continue
		}
		newCtx := epificontext.CtxWithActorId(ctx, actorId)
		addAccRes, err := s.extAcctClient.AddBankAccount(newCtx, convertToExtAcctRequest(val, agentEmailId, actorId))
		if grpcErr := epifigrpc.RPCError(addAccRes, err); grpcErr != nil {
			cxLogger.Error(newCtx, "error verifying external bank account", zap.Error(err))
			if addAccRes.GetStatus().IsAlreadyExists() {
				accVeriResults = append(accVeriResults,
					convertToAccVerificationResultCsvFormat(val, extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(), "Bank Account already processed/in-process"))
				continue
			}
			accVeriResults = append(accVeriResults,
				convertToAccVerificationResultCsvFormat(val, extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(), "Internal Failure - Performing Validation"))
			// we don't  want to return from rpc since there are multiple validations happening
			continue
		}

		savingsAcct, err := s.getSavingsAccountFromUserId(newCtx, userId)
		if err != nil {
			cxLogger.Error(newCtx, "error fetching savings accounts of the user", zap.Error(err))
			accVeriResults = append(accVeriResults,
				convertToAccVerificationResultCsvFormat(val, extacct.OverallStatus_OVERALL_STATUS_FAILURE.String(), "Internal Failure - Error getting Fi Account Number"))
			// we don't  want to return from rpc since there are multiple validations happening
			continue
		}

		bal, balTime := s.getAccountBalanceDuringClosure(newCtx, savingsAcct.GetId())

		accVeriResults = append(accVeriResults,
			convertToAccVerificationResultCsvFormat(val, addAccRes.GetOverallStatus().String(), addAccRes.GetFailureReason().String()))
		if addAccRes.GetOverallStatus() == extacct.OverallStatus_OVERALL_STATUS_SUCCESS {
			verifiedAccts, err := s.extAcctClient.GetBankAccounts(newCtx, &extacct.GetBankAccountsRequest{
				ActorId: actorId,
			})
			if grpcErr := epifigrpc.RPCError(verifiedAccts, err); grpcErr != nil {
				cxLogger.Error(newCtx, "error fetching successfully verified external account of the user", zap.Error(grpcErr))
				// we don't  want to return from rpc since there are multiple validations happening
				continue
			}
			// verifiedAccts are in descending order of update time, hence first record is the latest verified account details
			if len(verifiedAccts.GetBankAccounts()) > 0 {
				federalCloseAccDetails = append(federalCloseAccDetails, convertToFederalCloseAccFormat(verifiedAccts.GetBankAccounts()[0], savingsAcct.GetAccountNo(), s.cxConf.Secrets.Ids[config.FederalPoolAccountNo], s.getClosureStatusFromVendor(ctx, actorId, savingsAcct), bal, balTime))
			}
		}
	}
	return accVeriResults, federalCloseAccDetails
}

func createAndConvertToBytesCsvFile(ctx context.Context, list interface{}, fileName string) (string, []byte, error) {
	csvFile, err := ioutil.TempFile("", fileName+time.Now().Format("**************")+".csv")
	if err != nil {
		cxLogger.Error(ctx, "error creating a csv file", zap.Error(err))
		return "", nil, err
	}
	defer func() {
		if csvErr := csvFile.Close(); csvErr != nil {
			cxLogger.Error(ctx, "error while closing the csv file", zap.Error(csvErr))
		}
	}()

	switch ls := list.(type) {
	case []*FederalCloseAccountRequestFormat:
		if err = gocsv.MarshalFile(ls, csvFile); err != nil {
			cxLogger.Error(ctx, "failed to write contents to a csv file in federal format", zap.Error(err))
			return "", nil, err
		}
	case []*VerificationResult:
		if err = gocsv.MarshalFile(ls, csvFile); err != nil {
			cxLogger.Error(ctx, "failed to write contents to a csv file in verication result format", zap.Error(err))
			return "", nil, err
		}
	default:
		return "", nil, epifierrors.ErrInvalidArgument
	}
	cxLogger.Info(ctx, "csv file generated successfully")
	csvBytes, err := ioutil.ReadFile(csvFile.Name())
	if err != nil {
		cxLogger.Error(ctx, "failed to read contents from csv file", zap.Error(err))
		return "", nil, err
	}
	return csvFile.Name(), csvBytes, nil
}

func (s *Service) populateAndSendEmail(ctx context.Context, allVerificationsFile, federalFile string, allVerificationsBytes, federalBytes []byte,
	totalValidations, successfulValidations int64) error {
	additionalMsg := "PFA Bulk Account Verifications details file and Successful Verification Account Details file (to share with bank)."
	attachments := []*commsPb.EmailMessage_Attachment{
		{
			FileContent:    allVerificationsBytes,
			FileName:       allVerificationsFile,
			Disposition:    commsPb.Disposition_ATTACHMENT,
			AttachmentType: "text/comma-separated-values",
		},
		{
			FileContent:    federalBytes,
			FileName:       federalFile,
			Disposition:    commsPb.Disposition_ATTACHMENT,
			AttachmentType: "text/comma-separated-values",
		},
	}
	// send email
	resp, err := s.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_EMAIL,
		UserIdentifier: &commsPb.SendMessageRequest_EmailId{
			EmailId: s.cxConf.BulkAccValidationViaEmailConfig.ToEmailId,
		},
		Message: &commsPb.SendMessageRequest_Email{
			Email: &commsPb.EmailMessage{
				FromEmailId:   s.cxConf.BulkAccValidationViaEmailConfig.FromEmailId,
				FromEmailName: s.cxConf.BulkAccValidationViaEmailConfig.FromEmailName,
				ToEmailName:   s.cxConf.BulkAccValidationViaEmailConfig.ToEmailName,
				EmailOption: &commsPb.EmailOption{
					Option: &commsPb.EmailOption_CxBulkAccountValidationsEmailOption{
						CxBulkAccountValidationsEmailOption: &commsPb.CxBulkAccountValidationsEmailOption{
							EmailType: commsPb.EmailType_CX_BULK_ACCOUNT_VALIDATIONS_EMAIL,
							Option: &commsPb.CxBulkAccountValidationsEmailOption_CxBulkAccountValidationsEmailOptionV1{
								CxBulkAccountValidationsEmailOptionV1: &commsPb.CxBulkAccountValidationsEmailOptionV1{
									TotalValidations:      totalValidations,
									SuccessfulValidations: successfulValidations,
									TemplateVersion:       commsPb.TemplateVersion_VERSION_V1,
									AdditionalMsg:         additionalMsg,
								},
							},
						},
					},
				},
				Attachment: attachments,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		cxLogger.Error(ctx, "error while emailing bulk user details CSV", zap.Error(grpcErr))
		return grpcErr
	}
	return nil
}

func (s *Service) getClosureStatusFromVendor(ctx context.Context, actorId string, account *savings.Account) string {
	deviceAuthResp, err := s.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(deviceAuthResp, err); te != nil {
		cxLogger.Error(ctx, "error while getting device auth", zap.Error(te))
		return ""
	}

	getUserRes, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(getUserRes, err); te != nil {
		cxLogger.Error(ctx, "error while getting user by actorId", zap.Error(te))
		return ""
	}

	bankCustomerInfo, errResp := s.bcCLient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if er := epifigrpc.RPCError(bankCustomerInfo, errResp); er != nil {
		cxLogger.Error(ctx, "error while getting vendor customer info", zap.Error(err))
		return ""
	}
	accountDetailsRes, err := s.vgDepositClient.GetAccountDetail(ctx, &vgDepositPb.GetAccountDetailRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: account.GetPartnerBank(),
		},
		Auth: &header.Auth{
			DeviceId:      deviceAuthResp.GetDevice().GetDeviceId(),
			DeviceToken:   deviceAuthResp.GetDeviceToken(),
			UserProfileId: deviceAuthResp.GetUserProfileId(),
		},
		RequestId:            idgen.FederalRandomSequence(idgen.FederalGetDepositAccountDetailsPrefix, 5),
		CustomerId:           bankCustomerInfo.GetBankCustomer().GetVendorCustomerId(),
		AccountType:          accounts.Type_SAVINGS,
		DepositAccountNumber: account.GetAccountNo(),
		PhoneNumber:          getUserRes.GetUser().GetProfile().GetPhoneNumber(),
	})
	if te := epifigrpc.RPCError(accountDetailsRes, err); te != nil {
		cxLogger.Error(ctx, "error while getting accounts details from vg deposit client", zap.Error(te))
		return ""
	}

	var accountStatusMap = map[bool]string{
		true:  "Account Closed",
		false: "Account Open",
	}
	return accountStatusMap[accountDetailsRes.GetAccountDetails().GetIsClosed()]
}
