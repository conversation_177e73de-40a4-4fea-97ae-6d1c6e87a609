package ticket_translator

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/cx/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	_, genConf, _, teardown := test.InitTestServer(false)
	fmtTS = &FreshdeskMonorailTranslatorTestSuite{
		genConf: genConf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
