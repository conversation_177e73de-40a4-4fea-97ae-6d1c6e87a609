package processor

// import (
//	"context"
//	"fmt"
//	"testing"
//	"time"
//
//	disputePb "github.com/epifi/gamma/api/cx/dispute"
//
//	mock_helper "github.com/epifi/gamma/cx/test/mocks/dispute/processor/helper"
//
//	"github.com/golang/mock/gomock"
//
//	paymentPb "github.com/epifi/gamma/api/order/payment"
//	"github.com/epifi/gamma/cx/dispute/dao/model"
//)
//
// func TestRaiseDisputeNpciProcessor_ProcessDispute(t *testing.T) {
//	ctr := gomock.NewController(t)
//
//	mockUdirHelper := mock_helper.NewMockIUDIRHelper(ctr)
//
//	defer func() {
//		ctr.Finish()
//	}()
//	type args struct {
//		mocks           []interface{}
//		ctx             context.Context
//		dispute         *disputePb.DisputeData
//		disputeCfg      *model.DisputeConfig
//		txn             *paymentPb.Transaction
//		orderExternalId string
//		now             time.Time
//	}
//	tests := []struct {
//		name    string
//		args    args
//		wantErr bool
//	}{
//		{
//			name: "error because user not found",
//			args: args{
//				mocks: []interface{}{
//					mockUdirHelper.EXPECT().RaiseDisputeToNPCI(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
//						fmt.Errorf("failed")),
//				},
//				ctx: context.Background(),
//				dispute: &disputePb.DisputeData{
//					ActorId: "test-actor",
//				},
//				disputeCfg:      &model.DisputeConfig{},
//				txn:             &paymentPb.Transaction{},
//				orderExternalId: "test-id",
//				now:             time.Now(),
//			},
//			wantErr: true,
//		},
//		{
//			name: "success",
//			args: args{
//				mocks: []interface{}{
//					mockUdirHelper.EXPECT().RaiseDisputeToNPCI(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
//						nil),
//				},
//				ctx: context.Background(),
//				dispute: &disputePb.DisputeData{
//					ActorId: "test-actor",
//				},
//				disputeCfg:      &model.DisputeConfig{},
//				txn:             &paymentPb.Transaction{},
//				orderExternalId: "test-id",
//				now:             time.Now(),
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			a := NewEscalateDisputeToNPCI(mockUdirHelper)
//			if err := a.ProcessDispute(tt.args.ctx, tt.args.dispute, tt.args.disputeCfg, tt.args.txn, tt.args.orderExternalId, tt.args.now, nil); (err != nil) != tt.wantErr {
//				t.Errorf("ProcessDispute() error = %v, wantErr %v", err, tt.wantErr)
//			}
//		})
//	}
//}
