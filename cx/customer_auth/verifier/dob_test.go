package verifier

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"

	cxPb "github.com/epifi/gamma/api/cx"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/dao/model"
	mock_auth_engine "github.com/epifi/gamma/cx/test/mocks/customer_auth/auth_engine"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/customer_auth/dao"
)

type DobTestSuite struct {
	conf *config.Config
}

var (
	dTS			DobTestSuite
	testAuthIdDob		= "testAuthIdDob"
	headerWithUserDOB	= &cxPb.Header{
		User: &user.User{
			Id:	"test-user-1",
			Profile: &user.Profile{
				DateOfBirth: &date.Date{
					Year:	1995,
					Month:	6,
					Day:	4,
				},
			},
		},
	}
	headerWithoutUserDOB	= &cxPb.Header{
		User: &user.User{
			Id:		"test-user-1",
			Profile:	&user.Profile{},
		},
	}
)

func TestDOBVerifier_Verify(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockUserClient := mocks.NewMockUsersClient(ctr)
	mockAFStateDAO := mock_dao.NewMockICustomerAuthenticationFactorsStateDAO(ctr)
	mockAFRetryLogs := mock_dao.NewMockIAuthFactorRetryLogsDao(ctr)
	mockAuthEngine := mock_auth_engine.NewMockIAuthEngine(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		AFStateDaoMock		[]interface{}
		AFRetryLogDaoMock	interface{}
		AuthEngineMock		interface{}
		ctx			context.Context
		req			*caPb.VerifyCustomerRequest
		authId			string
	}
	tests := []struct {
		name	string
		args	args
		want	*caPb.AuthFactorStatusResponse
		wantErr	bool
	}{
		{
			name:	"dob not found in system",
			args: args{
				AFStateDaoMock: []interface{}{
					mockAFStateDAO.EXPECT().GetByPrimaryKey(context.Background(), gomock.Any(), gomock.Any()).
						Return(&model.CustomerAuthenticationFactorsState{VerificationTriesLimit: dTS.conf.AuthFactorRetryLimit.DOB}, nil),
					mockAFStateDAO.EXPECT().Update(context.Background(), gomock.Any(), gomock.Any()).Return(&model.CustomerAuthenticationFactorsState{}, nil),
				},
				AFRetryLogDaoMock: mockAFRetryLogs.EXPECT().Create(context.Background(), gomock.Any()).Return(
					&model.CustomerAuthenticationFactorsRetryLogs{}, nil),
				ctx:	context.Background(),
				req: &caPb.VerifyCustomerRequest{
					Header:		headerWithoutUserDOB,
					AuthFactor:	caPb.AuthFactor_DOB,
					Value: &caPb.VerifyCustomerRequest_DateOfBirth{DateOfBirth: &date.Date{
						Year:	1995,
						Month:	6,
						Day:	4,
					}},
				},
				authId:	testAuthIdDob,
			},
			want:		dobInternal,
			wantErr:	true,
		},
		{
			name:	"dob not passed by client",
			args: args{
				ctx:	context.Background(),
				req: &caPb.VerifyCustomerRequest{
					Header:		headerWithUserDOB,
					AuthFactor:	caPb.AuthFactor_DOB,
					Value:		&caPb.VerifyCustomerRequest_DateOfBirth{},
				},
				authId:	testAuthIdDob,
			},
			want:		dobInvalidInfo,
			wantErr:	true,
		},
		{
			name:	"dob do not match",
			args: args{
				AFStateDaoMock: []interface{}{
					mockAFStateDAO.EXPECT().GetByPrimaryKey(context.Background(), gomock.Any(), gomock.Any()).
						Return(&model.CustomerAuthenticationFactorsState{VerificationTriesLimit: dTS.conf.AuthFactorRetryLimit.DOB}, nil),
					mockAFStateDAO.EXPECT().Update(context.Background(), gomock.Any(), gomock.Any()).Return(&model.CustomerAuthenticationFactorsState{}, nil),
				},
				AFRetryLogDaoMock: mockAFRetryLogs.EXPECT().Create(context.Background(), gomock.Any()).Return(
					&model.CustomerAuthenticationFactorsRetryLogs{}, nil),
				ctx:	context.Background(),
				req: &caPb.VerifyCustomerRequest{
					Header:		headerWithUserDOB,
					AuthFactor:	caPb.AuthFactor_DOB,
					Value: &caPb.VerifyCustomerRequest_DateOfBirth{DateOfBirth: &date.Date{
						Year:	1995,
						Month:	4,
						Day:	4,
					}},
				},
				authId:	testAuthIdDob,
			},
			want:		dobInvalidInfo,
			wantErr:	false,
		},
		{
			name:	"dob match but db update failed hence throw error",
			args: args{
				AFStateDaoMock: []interface{}{
					mockAFStateDAO.EXPECT().GetByPrimaryKey(context.Background(), gomock.Any(), gomock.Any()).
						Return(&model.CustomerAuthenticationFactorsState{VerificationTriesLimit: dTS.conf.AuthFactorRetryLimit.DOB}, nil),
					mockAFStateDAO.EXPECT().Update(context.Background(), gomock.Any(), gomock.Any()).
						Return(&model.CustomerAuthenticationFactorsState{}, fmt.Errorf("error in db update")),
				},
				AFRetryLogDaoMock: mockAFRetryLogs.EXPECT().Create(context.Background(), gomock.Any()).Return(
					&model.CustomerAuthenticationFactorsRetryLogs{}, nil),
				ctx:	context.Background(),
				req: &caPb.VerifyCustomerRequest{
					Header:		headerWithUserDOB,
					AuthFactor:	caPb.AuthFactor_DOB,
					Value: &caPb.VerifyCustomerRequest_DateOfBirth{DateOfBirth: &date.Date{
						Year:	1995,
						Month:	6,
						Day:	4,
					}},
				},
				authId:	testAuthIdDob,
			},
			want:		dobInternal,
			wantErr:	true,
		},
		{
			name:	"dob match",
			args: args{
				AFStateDaoMock: []interface{}{
					mockAFStateDAO.EXPECT().GetByPrimaryKey(context.Background(), gomock.Any(), gomock.Any()).
						Return(&model.CustomerAuthenticationFactorsState{VerificationTriesLimit: dTS.conf.AuthFactorRetryLimit.DOB}, nil),
					mockAFStateDAO.EXPECT().Update(context.Background(), gomock.Any(), gomock.Any()).Return(&model.CustomerAuthenticationFactorsState{}, nil),
				},
				AFRetryLogDaoMock: mockAFRetryLogs.EXPECT().Create(context.Background(), gomock.Any()).Return(
					&model.CustomerAuthenticationFactorsRetryLogs{}, nil),
				ctx:	context.Background(),
				req: &caPb.VerifyCustomerRequest{
					Header:		headerWithUserDOB,
					AuthFactor:	caPb.AuthFactor_DOB,
					Value: &caPb.VerifyCustomerRequest_DateOfBirth{DateOfBirth: &date.Date{
						Year:	1995,
						Month:	6,
						Day:	4,
					}},
				},
				authId:	testAuthIdDob,
			},
			want:		dobSuccess,
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &DOBVerifier{
				usersClient:		mockUserClient,
				authFactorDAO:		mockAFStateDAO,
				authFactorRetryLogsDAO:	mockAFRetryLogs,
				authEngine:		mockAuthEngine,
				authFactorRetryLimit:	dTS.conf.AuthFactorRetryLimit,
			}
			got, err := d.Verify(tt.args.ctx, tt.args.req, tt.args.authId)
			if (err != nil) != tt.wantErr {
				t.Errorf("Verify() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Verify() got = %v, want %v", got, tt.want)
			}
		})
	}
}
