package customer_auth

import (
	"context"
	b64 "encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"

	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/be-common/pkg/cache"

	cxPb "github.com/epifi/gamma/api/cx"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/be-common/api/rpc"
	caPb "github.com/epifi/gamma/api/cx/customer_auth"
	fcaPb "github.com/epifi/gamma/api/frontend/cx/customer_auth"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/customer_auth/dao"
	"github.com/epifi/gamma/cx/customer_auth/verifier"
	"github.com/epifi/gamma/cx/validation"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	CxExternalAuthIdentifierPrefix       = "cx_auth_"
	CxExternalAuthRandomHashSuffixLength = 16
	ActorIdAndCreatedAtSeparator         = "###"
)

type AuthCallback struct {
	cAuthDAO               dao.ICustomerAuthenticationDAO
	callBackDAO            dao.ICustomerAuthenticationCallbackResponsesDAO
	authFactorDAO          dao.ICustomerAuthenticationFactorsStateDAO
	authEngine             auth_engine.IAuthEngine
	authFactorRetryLogsDAO dao.IAuthFactorRetryLogsDao
	authFactorRetryLimit   *config.AuthFactorRetryLimit
	ticketValidator        validation.ITicketValidation
	httpClient             IHttpClient
	sherlockConfig         *config.Sherlock
	secrets                *cfg.Secrets
	customerAuthConfig     *config.CustomerAuth
	usersClient            user.UsersClient
	cacheStorage           cache.CacheStorage
}

func NewAuthCallback(cAuthDAO dao.ICustomerAuthenticationDAO, callBackDAO dao.ICustomerAuthenticationCallbackResponsesDAO,
	authFactorDAO dao.ICustomerAuthenticationFactorsStateDAO, authEngine auth_engine.IAuthEngine,
	authFactorRetryLogsDAO dao.IAuthFactorRetryLogsDao, authFactorRetryLimit *config.AuthFactorRetryLimit,
	ticketValidator validation.ITicketValidation, httpClient IHttpClient, sherlockConfig *config.Sherlock,
	secrets *cfg.Secrets, customerAuthConfig *config.CustomerAuth, usersClient user.UsersClient, cacheStorage cache.CacheStorage,
) *AuthCallback {
	return &AuthCallback{
		cAuthDAO:               cAuthDAO,
		callBackDAO:            callBackDAO,
		authFactorDAO:          authFactorDAO,
		authEngine:             authEngine,
		authFactorRetryLogsDAO: authFactorRetryLogsDAO,
		authFactorRetryLimit:   authFactorRetryLimit,
		ticketValidator:        ticketValidator,
		httpClient:             httpClient,
		sherlockConfig:         sherlockConfig,
		secrets:                secrets,
		customerAuthConfig:     customerAuthConfig,
		usersClient:            usersClient,
		cacheStorage:           cacheStorage,
	}
}

func (c *AuthCallback) VerifyEmailCallback(ctx context.Context, req *caPb.VerifyEmailCallbackRequest) (*caPb.VerifyEmailCallbackResponse, error) {
	if req.GetRequestId() == "" {
		return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("request id not specified")}, nil
	}
	if req.GetHash() == "" {
		return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("hash for email not passed")}, nil
	}
	// if the request id prefix indicates that the customer authentication email was triggered as part of an external flow
	// for example IVR flow then we will skip creating auth factor here because we don't have a valid request id/auth id here
	// as the authentication flow was started from outside sherlock/customer authentication service,
	// the auth factor that needs to be created will be stored to a cache here, the cache will be accessed as part of GetNextAction RPC
	// whenever an agent is trying to access a ticket related to this user, a valid auth factor will be created
	// based on the values available in cache
	decodedStr, decodeErr := c.getDecodedString(req.GetHash())
	if decodeErr == nil {
		logger.Debug(ctx, "external auth based email callback recieved", zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.String("hash", decodedStr))
		if strings.HasPrefix(decodedStr, CxExternalAuthIdentifierPrefix) {
			cacheEmailAuthFactorErr := c.setActorIdAndAuthDetailsInCacheForEmail(ctx, decodedStr, caPb.AuthFactor_EMAIL_VERIFICATION, caPb.AuthFactorStatus_VERIFICATION_SUCCESS, req.GetRequestId())
			if cacheEmailAuthFactorErr != nil {
				logger.Error(ctx, "failed to set email auth factor in cache", zap.Error(cacheEmailAuthFactorErr),
					zap.String("emailHash", req.GetHash()))
				if errors.Is(cacheEmailAuthFactorErr, epifierrors.ErrUnauthorized) {
					return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusUnauthenticatedWithDebugMsg("email external auth validity expired")}, nil
				}
				return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusInternalWithDebugMsg("failed to set external auth details in cache")}, nil
			}
			return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusOk()}, nil
		}
	}
	callBackResponseRecord, err := c.callBackDAO.GetByPrimaryKey(ctx, req.GetRequestId(), caPb.AuthFactor_EMAIL_VERIFICATION)
	if err != nil {
		cxLogger.Error(ctx, "Failed to fetch callback entry from db for id: %s", zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.Error(err))
		return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusInternalWithDebugMsg("Failed to fetch callback entry from db")}, nil
	}
	// check for validity
	expired, err := isValidityExpired(callBackResponseRecord.UpdatedAt, c.customerAuthConfig.EmailValidityDuration)
	if err != nil {
		cxLogger.Error(ctx, "Failed to check auth factor validity", zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.Error(err))
		return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	if expired {
		cxLogger.Error(ctx, "Auth factor validity expired", zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.Error(err))
		return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusUnauthenticatedWithDebugMsg("auth factory validity expired")}, nil
	}
	verificationStatus := caPb.AuthFactorStatus_VERIFICATION_FAILED_REJECTED
	// verify hash
	if callBackResponseRecord.EmailHashKey == req.GetHash() {
		verificationStatus = caPb.AuthFactorStatus_VERIFICATION_SUCCESS
	}
	retryLimit := c.authFactorRetryLimit.EmailVerification
	if retryLimit == 0 {
		retryLimit = c.authFactorRetryLimit.Default
	}
	err = verifier.CreateOrUpdateAF(ctx, req.GetRequestId(), caPb.AuthFactor_EMAIL_VERIFICATION, verificationStatus,
		c.authFactorDAO, c.authFactorRetryLogsDAO, c.authEngine, retryLimit, false)

	if err != nil {
		cxLogger.Error(ctx, "unable to update auth factor state in table", zap.String(logger.REQUEST_ID, req.GetRequestId()), zap.Error(err))
		return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusInternalWithDebugMsg("unable to update auth factor state in table")}, nil
	}

	if verificationStatus == caPb.AuthFactorStatus_VERIFICATION_FAILED_REJECTED {
		cxLogger.Error(ctx, "failed to match email verification hash", zap.Any("callback_hash", req.GetHash()),
			zap.Any("hash_in_db", callBackResponseRecord.EmailHashKey), zap.Error(err))
		return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusUnauthenticatedWithDebugMsg("callback hash verification failed")}, nil
	}

	return &caPb.VerifyEmailCallbackResponse{Status: rpc.StatusOk()}, nil
}

func (c *AuthCallback) VerifyMobilePrompt(ctx context.Context, request *caPb.VerifyMobilePromptRequest) (*caPb.VerifyMobilePromptResponse, error) {
	if request.MobilePromptResponse == fcaPb.MobilePromptResponse_UNSPECIFIED {
		return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("mobile prompt response not specified")}, nil
	}
	if request.PromptId == "" {
		return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("prompt id not passed")}, nil
	}
	// if the request id prefix indicates that the authentication notification was triggered as part of an external flow
	// for example IVR flow then we will skip creating auth factor here because we don't have a valid request id/auth id here
	// as the authentication flow was started from outside sherlock/customer authentication service,
	// the auth factor that needs to be created will be stored to a cache here, the cache will be accessed as part of GetNextAction RPC
	// whenever an agent is trying to access a ticket related to this user, a valid auth factor will be created
	// based on the values available in cache
	// encoded string is of the formal : CxExternalAuthIdentifierPrefix + actor_id + random suffix of length determined by CxExternalAuthRandomHashSuffixLength
	decodedStr, decodeErr := c.getDecodedString(request.GetPromptId())
	if decodeErr == nil {
		// if the string is successfully decoded, check if this callback was triggered from some external auth
		// for example : IVR
		if strings.HasPrefix(decodedStr, CxExternalAuthIdentifierPrefix) {
			// if the user has agreed for the auth, cache the details to create a auth factor later
			// otherwise just return OK status
			if request.GetMobilePromptResponse() == fcaPb.MobilePromptResponse_YES {
				cacheMobilePromptAuthFactorErr := c.setActorIdAndAuthDetailsInCacheForMobilePrompt(ctx, decodedStr, caPb.AuthFactor_MOBILE_PROMPT, caPb.AuthFactorStatus_VERIFICATION_SUCCESS)
				if cacheMobilePromptAuthFactorErr != nil {
					logger.Error(ctx, "failed to set accepted mobile prompt auth factor in cache", zap.Error(cacheMobilePromptAuthFactorErr),
						zap.String("promptId", request.GetPromptId()))
					if errors.Is(cacheMobilePromptAuthFactorErr, epifierrors.ErrUnauthorized) {
						return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusUnauthenticatedWithDebugMsg("mobile prompt auth factor validity expired")}, nil
					}
					return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusInternalWithDebugMsg("Error while persisting external auth")}, nil
				}
			}
			return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusOk()}, nil
		}
	}
	callBackResponseRecord, err := c.callBackDAO.GetByPrimaryKey(ctx, request.GetPromptId(), caPb.AuthFactor_MOBILE_PROMPT)
	if err != nil {
		cxLogger.Error(ctx, "Failed to fetch callback entry from db for id: %s", zap.String(logger.REQUEST_ID, request.GetPromptId()), zap.Error(err))
		return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusInternalWithDebugMsg("Failed to fetch callback entry from db")}, nil
	}

	// check for validity
	expired, err := isValidityExpired(callBackResponseRecord.UpdatedAt, c.customerAuthConfig.MobilePromptValidityDuration)
	if err != nil {
		cxLogger.Error(ctx, "Failed to check auth factor validity", zap.String(logger.REQUEST_ID, request.GetPromptId()), zap.Error(err))
		return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	if expired {
		cxLogger.Error(ctx, "Failed to check auth factor validity", zap.String(logger.REQUEST_ID, request.GetPromptId()), zap.Error(err))
		return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusUnauthenticatedWithDebugMsg("auth factory validity expired")}, nil
	}

	verificationStatus := caPb.AuthFactorStatus_VERIFICATION_FAILED_REJECTED
	if request.MobilePromptResponse == fcaPb.MobilePromptResponse_YES {
		verificationStatus = caPb.AuthFactorStatus_VERIFICATION_SUCCESS
	}
	retryLimit := c.authFactorRetryLimit.MobilePrompt
	if retryLimit == 0 {
		retryLimit = c.authFactorRetryLimit.Default
	}
	err = verifier.CreateOrUpdateAF(ctx, request.GetPromptId(), caPb.AuthFactor_MOBILE_PROMPT, verificationStatus, c.authFactorDAO, c.authFactorRetryLogsDAO, c.authEngine, retryLimit, false)

	if err != nil {
		cxLogger.Error(ctx, "unable to update auth factor state in table", zap.Error(err))
		return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusInternalWithDebugMsg("unable to update auth factor state in table")}, nil
	}
	return &caPb.VerifyMobilePromptResponse{Status: rpc.StatusOk()}, nil
}

func (c *AuthCallback) getDecodedString(encodedStr string) (string, error) {
	decryptedStr, err := b64.StdEncoding.DecodeString(encodedStr)
	// if there is an error while decryption we will not proceed ahead
	if err != nil {
		return "", errors.Wrap(err, "error while decryption to get actor id")
	}
	return string(decryptedStr), nil
}

func (c *AuthCallback) setActorIdAndAuthDetailsInCacheForEmail(ctx context.Context, decodedStr string, authFactor caPb.AuthFactor, authFactorStatus caPb.AuthFactorStatus, requestId string) error {
	decodedCreatedAt, decodedCreatedAtErr := c.getDecodedString(requestId)
	if decodedCreatedAtErr != nil {
		return fmt.Errorf("failed to decode created at from external auth, requestId : %v and error : %v", requestId, decodedCreatedAtErr)
	}
	createdAtUnix, parsedCreatedAtErr := strconv.ParseInt(decodedCreatedAt, 10, 64)
	if parsedCreatedAtErr != nil {
		return fmt.Errorf("failed to parse created at from decoded string, decoded string : %v and error : %v", decodedCreatedAt, parsedCreatedAtErr)
	}
	parsedCreatedAt := time.Unix(createdAtUnix, 0)
	isEmailValidityExpired, validityErr := isValidityExpired(parsedCreatedAt, c.customerAuthConfig.EmailValidityDuration)
	if validityErr != nil {
		return errors.Wrap(validityErr, "error while checking external auth email validity")
	}
	if isEmailValidityExpired {
		return errors.Wrap(epifierrors.ErrUnauthorized, fmt.Sprintf("external auth email validity is expired, created at : %v", parsedCreatedAt))
	}
	if len(decodedStr) < CxExternalAuthRandomHashSuffixLength {
		return fmt.Errorf("length of decrypted hash to get actor id is less than expected, decrypted hash : %v", decodedStr)
	}
	// trim away CxExternalAuthIdentifierPrefix which helped us determine that this was externally triggered auth
	decodedStr = strings.TrimPrefix(decodedStr, CxExternalAuthIdentifierPrefix)
	// get actor id from hash, by trimming away the random suffix from decoded string
	lengthToKeep := len(decodedStr) - CxExternalAuthRandomHashSuffixLength
	actorId := decodedStr[:lengthToKeep]
	authDetailsBytes, marshalErr := protojson.Marshal(&caPb.CachedAuthDetails{
		AuthFactor:       authFactor,
		AuthFactorStatus: authFactorStatus,
	})
	if marshalErr != nil {
		return errors.Wrap(marshalErr, fmt.Sprintf("error while marshalling auth details, actor id : %v", actorId))
	}
	cacheEmailAuthFactorErr := c.cacheStorage.Set(ctx, c.getCacheKeyForExternalAuthFactor(actorId), string(authDetailsBytes),
		c.customerAuthConfig.AuthFactorCacheValidityDuration)
	return cacheEmailAuthFactorErr
}

func (c *AuthCallback) setActorIdAndAuthDetailsInCacheForMobilePrompt(ctx context.Context, decodedStr string, authFactor caPb.AuthFactor, authFactorStatus caPb.AuthFactorStatus) error {
	if len(decodedStr) < CxExternalAuthRandomHashSuffixLength {
		return fmt.Errorf("length of decrypted hash to get actor id is less than expected, decrypted hash : %v", decodedStr)
	}
	// trim away CxExternalAuthIdentifierPrefix which helped us determine that this was externally triggered auth
	decodedStr = strings.TrimPrefix(decodedStr, CxExternalAuthIdentifierPrefix)
	// get actor id and created at from hash, by trimming away the random suffix from decoded string
	lengthToKeep := len(decodedStr) - CxExternalAuthRandomHashSuffixLength
	remainingStr := decodedStr[:lengthToKeep]
	splittedStr := strings.Split(remainingStr, ActorIdAndCreatedAtSeparator)
	if len(splittedStr) != 2 {
		return fmt.Errorf("remaining string : %v, actor id and created at is not available", splittedStr)
	}
	actorId := splittedStr[0]
	createdAtStr := splittedStr[1]
	createdAtUnix, parsedCreatedAtErr := strconv.ParseInt(createdAtStr, 10, 64)
	if parsedCreatedAtErr != nil {
		return fmt.Errorf("given : %v , error while parsing created at", createdAtStr)
	}
	parsedCreatedAt := time.Unix(createdAtUnix, 0)
	hasValidityExpired, validityErr := isValidityExpired(parsedCreatedAt, c.customerAuthConfig.MobilePromptValidityDuration)
	if validityErr != nil {
		return errors.Wrap(validityErr, "error while checking mobile prompt validity")
	}
	if hasValidityExpired {
		return errors.Wrap(epifierrors.ErrUnauthorized, fmt.Sprintf("external auth mobile prompt validity is expired, created at : %v", parsedCreatedAt))
	}
	authDetailsBytes, marshalErr := protojson.Marshal(&caPb.CachedAuthDetails{
		AuthFactor:       authFactor,
		AuthFactorStatus: authFactorStatus,
	})
	if marshalErr != nil {
		return errors.Wrap(marshalErr, "error while marshalling auth details")
	}
	cacheEmailAuthFactorErr := c.cacheStorage.Set(ctx, c.getCacheKeyForExternalAuthFactor(actorId), string(authDetailsBytes),
		c.customerAuthConfig.AuthFactorCacheValidityDuration)
	return cacheEmailAuthFactorErr
}

func (c *AuthCallback) NotifySherlockWebServer(ctx context.Context, authId string, authFactor caPb.AuthFactor, authFactorStatus caPb.AuthFactorStatus) error {
	authRecord, err := c.cAuthDAO.GetByAuthId(ctx, authId)
	if err != nil {
		cxLogger.Error(ctx, "failed to notify sherlock web server, auth record not found",
			zap.String(logger.CX_AUTH_ID, authId), zap.Error(err))
		return err
	}
	// TODO (Sachin) : Avoid sending deeplink in post API call to sherlock and ask client to use Next Action in async auth factors
	status, ticket := c.ticketValidator.ValidateTicket(ctx, authRecord.TicketId, authRecord.AgentEmail, true, false, true)
	if status.Code != uint32(code.Code_OK) {
		cxLogger.Error(ctx, "failed to notify sherlock web server, invalid ticket", zap.String(logger.CX_AUTH_ID, authId),
			zap.Int64(logger.TICKET_ID, authRecord.TicketId))
		return fmt.Errorf("invalid ticket")
	}
	resp, err := c.usersClient.GetUser(ctx, &user.GetUserRequest{Identifier: &user.GetUserRequest_Id{Id: authRecord.UserId}})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error calling user service to get user from id", zap.Error(te))
		return te
	}
	header := &cxPb.Header{
		AgentEmail: authRecord.AgentEmail,
		TicketId:   authRecord.TicketId,
		Ticket:     ticket,
		User:       resp.GetUser(),
	}
	sherlockDeepLink, err := c.authEngine.GetNextAction(ctx, authId, header)
	if err != nil {
		cxLogger.Error(ctx, "failed to notify sherlock web server, error while generating sherlock deeplink",
			zap.String(logger.CX_AUTH_ID, authId), zap.Error(err))
		return err
	}
	requestBody := &SherlockCallbackRequest{
		Data: &Data{
			TicketId:   authRecord.TicketId,
			AgentEmail: authRecord.AgentEmail,
			AuthFactorStatusResponse: &caPb.AuthFactorStatusResponse{
				AuthFactor:       authFactor,
				AuthFactorStatus: authFactorStatus,
			},
			SherlockDeepLink: sherlockDeepLink,
		},
	}
	jsonByte, err := json.Marshal(requestBody)
	if err != nil {
		cxLogger.Error(ctx, "failed to marshal GetUserDetailsRequest params", zap.Error(err))
		return err
	}
	requestBodyJson := string(jsonByte)

	request, err := http.NewRequest("POST", c.sherlockConfig.SherlockCallbackURL, strings.NewReader(requestBodyJson))

	if err != nil {
		cxLogger.Error(ctx, "failed to notify sherlock web server, failed to initialise http request",
			zap.String(logger.CX_AUTH_ID, authId), zap.Error(err))
		return err
	}

	request.Header.Add("content-type", "application/json")
	// header required for api authentication
	request.Header.Add("app-source", "CX")
	secretByte := []byte(c.secrets.Ids[config.SherlockApiKey])

	var secretStruct struct {
		ApiKey string `json:"api-key"`
	}
	err = json.Unmarshal(secretByte, &secretStruct)
	if err != nil {
		cxLogger.Error(ctx, "error while unmarshaling api key", zap.Error(err))
		return err
	}
	request.Header.Add("api-key", secretStruct.ApiKey)
	response, err := c.httpClient.Do(request)
	if err != nil {
		cxLogger.Error(ctx, "failed to notify sherlock web server", zap.Error(err))
		return err
	}
	cxLogger.Info(ctx, "notified sherlock web server", zap.Any("response", response))
	defer func() {
		err = response.Body.Close()
		if err != nil {
			cxLogger.Error(ctx, "Error while closing response body received from sherlock", zap.Error(err))
		}
	}()
	return nil
}

func isValidityExpired(validationTime time.Time, validityDuration string) (bool, error) {
	// find the diff between current time and the time which needs to be checked for validity
	timeDiff := time.Since(validationTime)

	// get auth validity duration from config
	authValidity, err := time.ParseDuration(validityDuration)
	if err != nil {
		return true, fmt.Errorf("invalid auth duration in config")
	}
	// if the difference is more than auth validity then auth factor try has expired
	if timeDiff > authValidity {
		return true, nil
	}
	return false, nil
}

func (c *AuthCallback) getCacheKeyForExternalAuthFactor(actorId string) string {
	return fmt.Sprintf(c.customerAuthConfig.AuthFactorCacheKey, actorId)
}
