package sherlock_user

import (
	"context"
	"fmt"
	"reflect"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	cxPb "github.com/epifi/gamma/api/cx"
	sherlockUserPb "github.com/epifi/gamma/api/cx/sherlock_user"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/sherlock_user/dao"
	"github.com/epifi/be-common/pkg/epifierrors"
)

type Service struct {
	factory *SherlockUserProvisionerFactory
}

func NewSherlockUserService(factory *SherlockUserProvisionerFactory) *Service {
	return &Service{
		factory: factory,
	}
}

func (s *Service) CreateSherlockUser(ctx context.Context, req *sherlockUserPb.CreateSherlockUserRequest) (*sherlockUserPb.CreateSherlockUserResponse, error) {
	var statusList []*sherlockUserPb.ProvisioningStatus

	provisioners, err := s.factory.GetSherlockUserProvisionerList(ctx, getCreationProvisioners())
	if err != nil {
		cxLogger.Error(ctx, "error retrieving create sherlock user provisioners", zap.Error(err))
		return &sherlockUserPb.CreateSherlockUserResponse{ProvisioningStatusList: nil}, nil
	}

	for _, prov := range provisioners {
		resp := prov.CreateUser(ctx, req)
		if !resp.GetStatus().IsSuccess() {
			cxLogger.Error(ctx, "error creating user for provisioner", zap.Any("status: ", resp.GetStatus()), zap.Any("provisioner_name: ", resp.GetSherlockUserProvisioner().String()))
		} else {
			cxLogger.Info(ctx, "user successfully created", zap.Any("provisioner_name: ", resp.GetSherlockUserProvisioner().String()))
		}
		statusList = append(statusList, resp)
	}

	return &sherlockUserPb.CreateSherlockUserResponse{ProvisioningStatusList: statusList}, nil
}

func (s *Service) UpdateSherlockUserInfo(ctx context.Context, req *sherlockUserPb.UpdateSherlockUserInfoRequest) (*sherlockUserPb.UpdateSherlockUserInfoResponse, error) {

	if len(req.GetUpdateFieldMask()) == 0 {
		return nil, fmt.Errorf("update field mask cannot be empty")
	}

	var statusList []*sherlockUserPb.ProvisioningStatus

	provisioners, err := s.factory.GetSherlockUserProvisionerList(ctx, getUpdateProvisioners())
	if err != nil {
		cxLogger.Error(ctx, "error retrieving update sherlock user provisioners", zap.Error(err))
		return &sherlockUserPb.UpdateSherlockUserInfoResponse{ProvisioningStatusList: nil}, nil
	}

	for _, prov := range provisioners {
		resp := prov.UpdateUser(ctx, req.GetSherlockUserInfo(), req.GetUpdateFieldMask())
		if !resp.GetStatus().IsSuccess() {
			cxLogger.Error(ctx, "error updating user for provisioner", zap.Any("status: ", resp.GetStatus()), zap.Any("provisioner_name: ", resp.GetSherlockUserProvisioner().String()))
		} else {
			cxLogger.Info(ctx, "user successfully updated", zap.Any("provisioner_name: ", resp.GetSherlockUserProvisioner().String()))
		}
		statusList = append(statusList, resp)
	}

	return &sherlockUserPb.UpdateSherlockUserInfoResponse{ProvisioningStatusList: statusList}, nil
}

func (s *Service) GetSherlockUser(ctx context.Context, req *sherlockUserPb.GetSherlockUserRequest) (*sherlockUserPb.GetSherlockUserResponse, error) {

	var info *sherlockUserPb.SherlockUserInfo
	var err error

	if req.GetEmailId() != "" {
		info, err = s.GetUserDetailsByEmail(ctx, req.GetEmailId())
	} else {
		info, err = s.GetUserDetailsBySearchId(ctx, req.GetSearchId())
	}

	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			cxLogger.Error(ctx, "record not found for email", zap.Error(err), zap.Any("email_id", req.GetEmailId()))
			return &sherlockUserPb.GetSherlockUserResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg(err.Error()),
			}, nil
		} else {
			cxLogger.Error(ctx, "internal error while retrieving user details", zap.Error(err))
			return &sherlockUserPb.GetSherlockUserResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}

	return &sherlockUserPb.GetSherlockUserResponse{
		Status:           rpcPb.StatusOk(),
		SherlockUserInfo: info,
	}, nil

}

func (s *Service) GetAllSherlockUsers(ctx context.Context, req *sherlockUserPb.GetAllSherlockUsersRequest) (*sherlockUserPb.GetAllSherlockUsersResponse, error) {

	epifiUserProvisioner, err := s.factory.GetUserFetcher(sherlockUserPb.SherlockUserProvisioner_EPIFI)
	if err != nil {
		cxLogger.Error(ctx, "unable to fetch epifi user provisioner", zap.Error(err))
		return nil, err
	}

	pageToken, err := getPageToken(req.GetPageContext())
	if err != nil {
		cxLogger.Error(ctx, "unable to decode page tokens", zap.Error(err))
		return &sherlockUserPb.GetAllSherlockUsersResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("unable to decode page tokens"),
		}, nil
	}

	if req.GetPageContext().GetPageSize() > 50 {
		cxLogger.Error(ctx, "page size should not be greater than 50")
		return &sherlockUserPb.GetAllSherlockUsersResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("page size should not be greater than 50"),
		}, nil
	}

	pageSize := int(req.GetPageContext().GetPageSize())
	usersList, err := epifiUserProvisioner.GetUsers(ctx, pageSize, pageToken, req.GetFilterList())
	if err != nil {
		cxLogger.Error(ctx, "internal error while retrieving user details", zap.Error(err))
		return &sherlockUserPb.GetAllSherlockUsersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	userDetails, pageCtx, err := paginateUserDetailsResponse(pageSize, usersList, pageToken)

	if err != nil {
		cxLogger.Error(ctx, "error while encoding page token", zap.Error(err))
		return &sherlockUserPb.GetAllSherlockUsersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while encoding page token"),
		}, nil
	}

	return &sherlockUserPb.GetAllSherlockUsersResponse{
		Status:               rpcPb.StatusOk(),
		PageContext:          pageCtx,
		SherlockUserInfoList: userDetails,
	}, nil
}

func (s *Service) FetchLoggedInUserInfo(ctx context.Context, req *sherlockUserPb.FetchLoggedInUserInfoRequest) (*sherlockUserPb.FetchLoggedInUserInfoResponse, error) {

	email := req.GetHeader().GetAgentEmail()
	info, err := s.GetUserDetailsByEmail(ctx, email)

	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			cxLogger.Error(ctx, "record not found for email", zap.Error(err), zap.Any("email_id", email))
			return &sherlockUserPb.FetchLoggedInUserInfoResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg(err.Error()),
			}, nil
		} else {
			cxLogger.Error(ctx, "internal error while retrieving user details", zap.Error(err))
			return &sherlockUserPb.FetchLoggedInUserInfoResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}

	return &sherlockUserPb.FetchLoggedInUserInfoResponse{
		Status:           rpcPb.StatusOk(),
		SherlockUserInfo: info,
	}, nil
}

func getCreationProvisioners() []sherlockUserPb.SherlockUserProvisioner {
	list := []sherlockUserPb.SherlockUserProvisioner{sherlockUserPb.SherlockUserProvisioner_EPIFI, sherlockUserPb.SherlockUserProvisioner_CASBIN}
	return list
}

func getUpdateProvisioners() []sherlockUserPb.SherlockUserProvisioner {
	list := []sherlockUserPb.SherlockUserProvisioner{sherlockUserPb.SherlockUserProvisioner_EPIFI, sherlockUserPb.SherlockUserProvisioner_CASBIN}
	return list
}

func (s *Service) GetUserDetailsByEmail(ctx context.Context, emailId string) (*sherlockUserPb.SherlockUserInfo, error) {
	if emailId == "" {
		return nil, fmt.Errorf("email id cannot be empty")
	}

	epifiUserProvisioner, err := s.factory.GetUserFetcher(sherlockUserPb.SherlockUserProvisioner_EPIFI)
	if err != nil {
		cxLogger.Error(ctx, "unable to fetch epifi user provisioner", zap.Error(err))
		return nil, err
	}

	resp, err := epifiUserProvisioner.GetUser(ctx, emailId)
	if err != nil {
		cxLogger.Error(ctx, "unable to fetch sherlock user details", zap.Error(err))
		return nil, err
	}

	return resp, nil
}

func (s *Service) GetUserDetailsBySearchId(ctx context.Context, searchId *sherlockUserPb.SearchId) (*sherlockUserPb.SherlockUserInfo, error) {

	if searchId.GetSearchIdType() == sherlockUserPb.SearchIdType_SEARCH_ID_TYPE_UNSPECIFIED {
		return nil, errors.New("search identifier cannot be unspecified")
	}

	epifiUserProvisioner, err := s.factory.GetUserFetcher(sherlockUserPb.SherlockUserProvisioner_EPIFI)
	if err != nil {
		cxLogger.Error(ctx, "unable to fetch epifi user provisioner", zap.Error(err))
		return nil, err
	}

	resp, err := epifiUserProvisioner.GetUserBySearchId(ctx, searchId)
	if err != nil {
		cxLogger.Error(ctx, "unable to fetch sherlock user details", zap.Error(err))
		return nil, err
	}

	return resp, nil
}

func getPageToken(pageContextRequest *cxPb.PageContextRequest) (*dao.PageToken, error) {
	pageToken := &dao.PageToken{}
	var encodedToken string
	if pageContextRequest.GetAfterToken() != "" {
		encodedToken = pageContextRequest.GetAfterToken()
	}
	if pageContextRequest.GetBeforeToken() != "" {
		encodedToken = pageContextRequest.GetBeforeToken()
	}
	if encodedToken != "" {
		err := pageToken.UnmarshalToken(encodedToken)
		if err != nil {
			return nil, errors.Wrap(err, "error while unmarshalling page token")
		}
	}
	return pageToken, nil
}

func paginateUserDetailsResponse(pageSize int, usersList []*sherlockUserPb.SherlockUserInfo, pageToken *dao.PageToken) ([]*sherlockUserPb.SherlockUserInfo, *cxPb.PageContextResponse, error) {
	resCount := len(usersList)
	var tempUserDetails []*sherlockUserPb.SherlockUserInfo

	for i, user := range usersList {
		if i == pageSize {
			break
		}
		tempUserDetails = append(tempUserDetails, user)
	}

	hasAfter, hasBefore := getHasBeforeAndAfter(pageToken, resCount, pageSize)

	var pageContextResponse *cxPb.PageContextResponse

	if len(tempUserDetails) > 0 {
		resp, err := getPageContextResponse(tempUserDetails, hasBefore, hasAfter)
		if err != nil {
			return nil, nil, errors.Wrap(err, "failed to encode page token")
		}
		pageContextResponse = resp
	}

	return tempUserDetails, pageContextResponse, nil
}

func getHasBeforeAndAfter(pageToken *dao.PageToken, resCount int, pageSize int) (bool, bool) {
	var hasBefore, hasAfter bool
	if pageToken.IsReverse {
		if pageToken.Offset > 0 {
			hasAfter = true
		}
		if resCount > pageSize {
			hasBefore = true
		}
	} else {
		if pageToken.Offset > 0 {
			hasBefore = true
		}
		if resCount > pageSize {
			hasAfter = true
		}
	}
	return hasAfter, hasBefore
}

func getPageContextResponse(userInfoList []*sherlockUserPb.SherlockUserInfo, hasBefore bool, hasAfter bool) (*cxPb.PageContextResponse, error) {

	beforeToken := &dao.PageToken{
		Timestamp: userInfoList[0].GetCreatedAt(),
		Offset:    0,
		IsReverse: true,
	}
	afterToken := &dao.PageToken{
		Timestamp: userInfoList[len(userInfoList)-1].GetCreatedAt(),
		Offset:    0,
		IsReverse: false,
	}
	for _, user := range userInfoList {
		if reflect.DeepEqual(beforeToken.Timestamp, user.CreatedAt) {
			beforeToken.Offset++
		}
		if reflect.DeepEqual(afterToken.Timestamp, user.CreatedAt) {
			afterToken.Offset++
		}
	}
	encodedBeforeToken, err := beforeToken.MarshalToken()
	if err != nil {
		return nil, errors.Wrap(err, "unable to encode before page token")
	}
	encodedAfterToken, err := afterToken.MarshalToken()
	if err != nil {
		return nil, errors.Wrap(err, "unable to encode after page token")
	}
	PageContext := &cxPb.PageContextResponse{
		BeforeToken: encodedBeforeToken,
		HasBefore:   hasBefore,
		AfterToken:  encodedAfterToken,
		HasAfter:    hasAfter,
	}
	return PageContext, nil
}
