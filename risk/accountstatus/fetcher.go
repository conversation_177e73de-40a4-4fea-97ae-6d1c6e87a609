package accountstatus

import (
	"context"

	"github.com/epifi/be-common/pkg/money"

	"google.golang.org/genproto/googleapis/type/date"

	accountPb "github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	enumsPb "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
)

// OperationalDetails contains all the details required around account status from risk use case pov
// this can be re-used at different places instead of repeating same logic at multiple places to
// extract the status details from savings or other services
//
//go:generate mockgen -source=fetcher.go -destination=../test/mocks/accountstatus/mock_fetcher.go -package=mock_fetcher
type OperationalDetails struct {
	FreezeStatus     accountEnumsPb.FreezeStatus
	FreezeReason     []string
	IsAccountClosed  bool
	AccountType      accountPb.Type
	LienBalance      *money.Money
	AccountCloseDate *date.Date
}

type Fetcher interface {
	// FetchLatestStatus will fetch the latest account status details from relevant service based on the implementation used
	// will fail with epifierrors.ErrInvalidArgument if actor or account id is missing in request
	// will return non-nil error for any other errors.
	FetchLatestStatus(ctx context.Context, actorId string, accountId string) (*OperationalDetails, error)
	// FetchAccountStatusForActor will fetch the operational details of the account for given actor id
	// data freshness will be according to data_freshness enum value given
	// will return non-nil error for any system error.
	// will return resource not found explicitly if error is from vendor side
	FetchAccountStatusForActor(ctx context.Context, actorId string, freshness enumsPb.DataFreshness, productOffering accountTypesPb.AccountProductOffering) (*OperationalDetails, error)
}

type UseNewOperationalStatusAPIFlag bool

// ProvideFetcherImplementation will be used to get the right fetcher implementation based on the flag provided
// this can be useful in wire to change the implementation used at the time of dependency ingestion based on service config
func ProvideFetcherImplementation(savingsClient savingsPb.SavingsClient,
	operationalStatusClient operationalStatusPb.OperationalStatusServiceClient,
	balanceClient accountBalancePb.BalanceClient,
	useNewOperationalStatusAPI UseNewOperationalStatusAPIFlag) Fetcher {
	switch {
	case bool(useNewOperationalStatusAPI):
		return NewOperationalStatusFetcher(operationalStatusClient, savingsClient)
	default:
		return NewBalanceV1Fetcher(savingsClient, balanceClient)
	}
}
