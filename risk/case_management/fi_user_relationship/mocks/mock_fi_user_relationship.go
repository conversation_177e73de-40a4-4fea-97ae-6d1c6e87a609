// Code generated by MockGen. DO NOT EDIT.
// Source: ./fi_user_relationship.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockFiUserRelationship is a mock of FiUserRelationship interface.
type MockFiUserRelationship struct {
	ctrl     *gomock.Controller
	recorder *MockFiUserRelationshipMockRecorder
}

// MockFiUserRelationshipMockRecorder is the mock recorder for MockFiUserRelationship.
type MockFiUserRelationshipMockRecorder struct {
	mock *MockFiUserRelationship
}

// NewMockFiUserRelationship creates a new mock instance.
func NewMockFiUserRelationship(ctrl *gomock.Controller) *MockFiUserRelationship {
	mock := &MockFiUserRelationship{ctrl: ctrl}
	mock.recorder = &MockFiUserRelationshipMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFiUserRelationship) EXPECT() *MockFiUserRelationshipMockRecorder {
	return m.recorder
}

// GetFiUserRelationshipUI mocks base method.
func (m *MockFiUserRelationship) GetFiUserRelationshipUI(ctx context.Context, actorId string) (map[string]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFiUserRelationshipUI", ctx, actorId)
	ret0, _ := ret[0].(map[string]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFiUserRelationshipUI indicates an expected call of GetFiUserRelationshipUI.
func (mr *MockFiUserRelationshipMockRecorder) GetFiUserRelationshipUI(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFiUserRelationshipUI", reflect.TypeOf((*MockFiUserRelationship)(nil).GetFiUserRelationshipUI), ctx, actorId)
}
