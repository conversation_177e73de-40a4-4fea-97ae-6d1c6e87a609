package casestore

import (
	"context"
	"fmt"

	"github.com/google/wire"

	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var CaseStoreWireSet = wire.NewSet(NewCaseStoreImplFactory, wire.Bind(new(CaseStore),
	new(*CaseStoreImplFactory)), NewExternalCRMCaseStore, NewCachedCaseStore)

type CaseStoreImplFactory struct {
	cachedCaseStore *CachedCaseStore
	*ExternalCRMCaseStore
}

func NewCaseStoreImplFactory(cachedCaseStore *CachedCaseStore, externalCRMCaseStore *ExternalCRMCaseStore) *CaseStoreImplFactory {
	return &CaseStoreImplFactory{
		cachedCaseStore:      cachedCaseStore,
		ExternalCRMCaseStore: externalCRMCaseStore,
	}
}

var _ CaseStore = &CaseStoreImplFactory{}

func (f *CaseStoreImplFactory) GetCaseById(ctx context.Context, caseId string, options ...*reviewPb.GetCaseOption) (*reviewPb.Case, error) {
	for _, option := range options {
		switch v := option.GetOption().(type) {
		case *reviewPb.GetCaseOption_DataFreshness:
			switch {
			case option.GetDataFreshness() == cmEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME:
				return f.cachedCaseStore.GetCaseById(ctx, caseId, options...)
			default:
				return f.ExternalCRMCaseStore.GetCaseById(ctx, caseId, options...)
			}
		default:
			return nil, fmt.Errorf("unknown get case option %v %w", v, epifierrors.ErrInvalidArgument)
		}
	}
	return f.ExternalCRMCaseStore.GetCaseById(ctx, caseId, options...)
}

func (f *CaseStoreImplFactory) UpdateCase(ctx context.Context, caseDetails *reviewPb.Case, updateMasks []reviewPb.CaseFieldMask) (*reviewPb.Case, error) {
	return f.cachedCaseStore.UpdateCase(ctx, caseDetails, updateMasks)
}
