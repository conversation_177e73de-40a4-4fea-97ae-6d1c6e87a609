package batchruleengine

import (
	"context"
	"fmt"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
)

type AlertBuilder interface {
	BuildFromRuleHits(ctx context.Context, hits []*RuleHit) ([]*cmPb.RawAlert, error)
}

func NewAlertBuilderImpl() *AlertBuilderImpl {
	return &AlertBuilderImpl{}
}

type AlertBuilderImpl struct {
}

var _ AlertBuilder = &AlertBuilderImpl{}

func (a *AlertBuilderImpl) BuildFromRuleHits(_ context.Context, hits []*RuleHit) ([]*cmPb.RawAlert, error) {
	var alerts []*cmPb.RawAlert

	for _, hit := range hits {
		alert, err := hit.ToRawAlert()
		if err != nil {
			return nil, fmt.Errorf("failed to convert to alert %w", err)
		}
		alerts = append(alerts, alert)
	}

	return alerts, nil
}
