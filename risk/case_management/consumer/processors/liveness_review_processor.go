package processors

import (
	"context"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	persistentQueuePb "github.com/epifi/gamma/api/persistentqueue"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/risk/case_management/helper"

	"github.com/pkg/errors"
)

type LivenessProcessor struct {
	livenessClient livenessPb.LivenessClient
	pqHelper       helper.IPersistentQueueHelper
}

func NewLivenessProcessor(livenessClient livenessPb.LivenessClient, pqHelper helper.IPersistentQueueHelper) *LivenessProcessor {
	return &LivenessProcessor{
		livenessClient: livenessClient,
		pqHelper:       pqHelper,
	}
}

func (l *LivenessProcessor) ProcessPayload(ctx context.Context, payloadType caseManagementPb.PayloadType, payload interface{}) error {
	// validate if payload is of liveness type
	livenessReviewPayload, ok := payload.(*caseManagementPb.RiskCase_LivenessReview)
	if !ok || livenessReviewPayload == nil {
		return errors.New("invalid payload received for processing liveness review")
	}

	latestAttempt, err := getLatestOnboardingLivenessAttempt(ctx, l.livenessClient, livenessReviewPayload.LivenessReview.GetActorId())
	if err != nil {
		return errors.Wrap(err, "error while getting latest onboarding liveness attempt for user")
	}

	pqPayloadType, err := l.pqHelper.ConvertToPQPayloadType(ctx, payloadType)
	if err != nil {
		return errors.Wrap(err, "error while converting to persistent queue payload type")
	}
	pqLivenessReviewPayload := buildPersistentQueueLivenessReviewPay(livenessReviewPayload.LivenessReview, latestAttempt)

	err = l.pqHelper.PushToQueue(ctx, livenessReviewPayload.LivenessReview.GetActorId(), pqPayloadType,
		&persistentQueuePb.Payload{LivenessPayload: pqLivenessReviewPayload})
	if err != nil {
		return errors.Wrap(err, "error while pushing review payload to persistent queue")
	}
	return nil
}

func buildPersistentQueueLivenessReviewPay(payload *caseManagementPb.LivenessReview, attempt *livenessPb.LivenessAttempt) *persistentQueuePb.LivenessReview {
	return &persistentQueuePb.LivenessReview{
		ActorId:       payload.GetActorId(),
		RequestId:     attempt.GetRequestId(),
		VideoLocation: attempt.GetVideoLocation(),
		CreatedAt:     attempt.GetCreatedAt(),
	}
}

// getLatestOnboardingLivenessAttempt will call the liveness service to fetch last 100 liveness attempts of the user and
// it will loop through all the attempts to find the latest attempt with liveness flow as Onboarding
// TODO(sachin): This approach is not ideal, we should add support to filter based on liveness flow in the rpc and update the code here
func getLatestOnboardingLivenessAttempt(ctx context.Context,
	livenessClient livenessPb.LivenessClient, actorId string) (*livenessPb.LivenessAttempt, error) {
	// get all liveness attempts data for current user
	attemptsResp, attemptsErr := livenessClient.GetLivenessAttempts(ctx, &livenessPb.GetLivenessAttemptsRequest{
		ActorId: actorId,
		Limit:   100,
	})
	if attemptsErr = epifigrpc.RPCError(attemptsResp, attemptsErr); attemptsErr != nil {
		return nil, errors.Wrap(attemptsErr, "error while fetching liveness attempts data for the user")
	}
	// loop through the attempts and get the latest attempt in onboarding flow
	for _, attempt := range attemptsResp.GetLivenessAttempts() {
		if attempt.GetLivenessFlow() == livenessPb.LivenessFlow_ONBOARDING {
			return attempt, nil
		}
	}
	return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "liveness attempt for onboarding flow not found")
}
