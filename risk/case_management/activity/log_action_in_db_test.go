package activity

import (
	"testing"

	caseManagementActivityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	caseReviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	enumsPb "github.com/epifi/gamma/api/risk/case_management/review"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

var (
	a1 = &caseReviewPb.Action{
		CaseId:     "888",
		ReviewType: enumsPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
		ActionType: caseReviewPb.ActionType_ACTION_TYPE_FREEZE_ACCOUNT,
		Parameters: &caseReviewPb.ActionParameters{
			Parameter: &caseReviewPb.ActionParameters_AccountFreezeParameters{
				AccountFreezeParameters: &caseReviewPb.AccountFreezeParameters{
					FreezeLevel: caseReviewPb.FreezeLevel_FREEZE_LEVEL_CREDIT,
					RequestReason: &caseReviewPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
				},
			},
		},
		Source:       caseReviewPb.ActionSource_ACTION_SOURCE_AUTO_FREEZE_FLOW,
		AnalystEmail: "<EMAIL>",
		InitiatedAt:  &timestampPb.Timestamp{Seconds: **********, Nanos: *********},
		ActorId:      testActorId,
	}
)

func TestProcessor_AddAction(t *testing.T) {
	type args struct {
		req *caseManagementActivityPb.LogActionInDBRequest
	}
	type mockLogActionInDB struct {
		isEnable bool
		req      *caseReviewPb.Action
		res      *caseReviewPb.Action
		err      error
	}
	tests := []struct {
		name              string
		args              args
		mockLogActionInDB mockLogActionInDB
		wantErr           bool
		want              *caseManagementActivityPb.LogActionInDBResponse
		assertErr         func(err error) bool
	}{
		{
			name: "successfully added DB entry",
			args: args{
				req: &caseManagementActivityPb.LogActionInDBRequest{
					CaseId:       a1.CaseId,
					ReviewType:   a1.ReviewType,
					ActionType:   a1.ActionType,
					Parameters:   a1.Parameters,
					Source:       a1.Source,
					AnalystEmail: a1.AnalystEmail,
					InitiatedAt:  a1.InitiatedAt,
					ActorId:      a1.GetActorId(),
				},
			},
			mockLogActionInDB: mockLogActionInDB{
				isEnable: true,
				req:      a1,
				err:      nil,
			},
		},
		{
			name: "permanent error: missing params",
			args: args{
				req: &caseManagementActivityPb.LogActionInDBRequest{
					ReviewType:   a1.ReviewType,
					ActionType:   a1.ActionType,
					Parameters:   a1.Parameters,
					Source:       a1.Source,
					AnalystEmail: a1.AnalystEmail,
					InitiatedAt:  a1.InitiatedAt,
				},
			},
			mockLogActionInDB: mockLogActionInDB{
				isEnable: true,
				req: &caseReviewPb.Action{
					ReviewType:   a1.ReviewType,
					ActionType:   a1.ActionType,
					Parameters:   a1.Parameters,
					Source:       a1.Source,
					AnalystEmail: a1.AnalystEmail,
					InitiatedAt:  a1.InitiatedAt,
				},
				err: epifierrors.ErrInvalidArgument,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient error: fail by external errors",
			args: args{
				req: &caseManagementActivityPb.LogActionInDBRequest{
					CaseId:       a1.CaseId,
					ReviewType:   a1.ReviewType,
					ActionType:   a1.ActionType,
					Parameters:   a1.Parameters,
					Source:       a1.Source,
					AnalystEmail: a1.AnalystEmail,
					InitiatedAt:  a1.InitiatedAt,
				},
			},
			mockLogActionInDB: mockLogActionInDB{
				isEnable: true,
				req: &caseReviewPb.Action{
					CaseId:       a1.CaseId,
					ReviewType:   a1.ReviewType,
					ActionType:   a1.ActionType,
					Parameters:   a1.Parameters,
					Source:       a1.Source,
					AnalystEmail: a1.AnalystEmail,
					InitiatedAt:  a1.InitiatedAt,
				},
				err: epifierrors.ErrResourceExhausted,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)

			if tt.mockLogActionInDB.isEnable {
				md.mockReviewActionDao.EXPECT().Create(gomock.Any(), tt.mockLogActionInDB.req).Return(tt.mockLogActionInDB.res, tt.mockLogActionInDB.err)
			}
			_, err := env.ExecuteActivity(riskNs.LogActionInDB, tt.args.req)
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("LogActionInDB() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("LogActionInDB() error = %v assertion failed", err)
				return
			}
		})
	}
}
