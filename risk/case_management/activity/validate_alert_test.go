package activity

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	"github.com/epifi/gamma/api/risk/case_management/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"
)

var (
	rawAlertWithoutEntityType = &caseManagementPb.RawAlert{
		Identifier: &caseManagementPb.RuleIdentifier{Identifier: &caseManagementPb.RuleIdentifier_RuleId{
			RuleId: "test-rule-1",
		}},
		BatchName: "test-batch-1",
		ActorId:   "test-actor-1",
		AccountId: "account-1",
		EntityId:  "entity-1",
	}
	rawAlertWithoutEntityId = &caseManagementPb.RawAlert{
		Identifier: &caseManagementPb.RuleIdentifier{Identifier: &caseManagementPb.RuleIdentifier_RuleId{
			RuleId: "test-rule-1",
		}},
		BatchName:  "test-batch-1",
		ActorId:    "test-actor-1",
		AccountId:  "account-1",
		EntityType: enums.EntityType_ENTITY_TYPE_USER,
	}
	rawAlertWithoutIdentifier = &caseManagementPb.RawAlert{
		Identifier: &caseManagementPb.RuleIdentifier{Identifier: nil},
		BatchName:  "test-batch-1",
		ActorId:    "test-actor-1",
		AccountId:  "account-1",
		EntityType: enums.EntityType_ENTITY_TYPE_USER,
		EntityId:   "entity-1",
	}
	rawAlertValidWithRuleId = &caseManagementPb.RawAlert{
		Identifier: &caseManagementPb.RuleIdentifier{Identifier: &caseManagementPb.RuleIdentifier_RuleId{
			RuleId: "test-rule-1",
		}},
		BatchName:  "test-batch-1",
		ActorId:    "test-actor-1",
		AccountId:  "account-1",
		EntityType: enums.EntityType_ENTITY_TYPE_USER,
		EntityId:   "entity-1",
	}
	rawAlertValidWithExternalRuleId = &caseManagementPb.RawAlert{
		Identifier: &caseManagementPb.RuleIdentifier{Identifier: &caseManagementPb.RuleIdentifier_ExternalId{
			ExternalId: "test-external-rule-1",
		}},
		BatchName:  "test-batch-1",
		ActorId:    "test-actor-1",
		AccountId:  "account-1",
		EntityType: enums.EntityType_ENTITY_TYPE_USER,
		EntityId:   "entity-1",
	}
	ruleFixture1 = &caseManagementPb.Rule{Id: "test-rule-1", Name: "test", Version: 1, State: caseManagementPb.RuleState_RULE_STATE_ACTIVE}
	ruleFixture2 = &caseManagementPb.Rule{Id: "test-rule-1", Name: "test", Version: 1, State: caseManagementPb.RuleState_RULE_STATE_INACTIVE}
)

func TestProcessor_ValidateAlert(t *testing.T) {
	type args struct {
		ctx context.Context
		req *activityPb.ValidateAlertRequest
	}
	tests := []struct {
		name      string
		mocks     func(mock *mockedDependencies, args args)
		args      args
		want      *activityPb.ValidateAlertResponse
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "error, entity type not passed",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertWithoutEntityType,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "error, entity type not passed",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertWithoutEntityId,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "error, nil rule identifier",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertWithoutIdentifier,
				},
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "rule identifier is id, intermittent error in dao",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).Return(nil, errors.New("failed"))
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "rule identifier is id, permanent error in dao",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).Return(nil, epifierrors.ErrInvalidArgument)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "rule identifier is id, record not found error in dao",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "rule identifier is id, inactive rule state error",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).Return([]*caseManagementPb.Rule{
					ruleFixture2,
				}, nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "rule identifier is id, empty rule list returned from dao",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).Return([]*caseManagementPb.Rule{}, nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "rule identifier is id, rule manager fails with transient error",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).Return([]*caseManagementPb.Rule{ruleFixture1}, nil)
				mock.mockRuleManager.EXPECT().GetPrecisionForRule(gomock.Any(), ruleFixture1).Return(float32(0), errors.New("failed to fetch rule precision"))
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "rule identifier is id, multiple rules returned from dao",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).
					Return([]*caseManagementPb.Rule{ruleFixture1, ruleFixture1}, nil)
			},
			want:    nil,
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "rule identifier is id, success",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkById(gomock.Any(), []string{"test-rule-1"}).
					Return([]*caseManagementPb.Rule{ruleFixture1}, nil)
				mock.mockRuleManager.EXPECT().GetPrecisionForRule(gomock.Any(), ruleFixture1).Return(float32(0.6), nil)
			},
			want: &activityPb.ValidateAlertResponse{AlertWithRule: &caseManagementPb.AlertWithRuleDetails{
				Alert: buildAlertObject(rawAlertValidWithRuleId, ruleFixture1, 0.6), Rule: ruleFixture1}},
			wantErr: false,
		},
		{
			name: "rule identifier is external id, success",
			args: args{
				req: &activityPb.ValidateAlertRequest{
					RawAlert: rawAlertValidWithExternalRuleId,
				},
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.ruleDao.EXPECT().GetBulkByExternalId(gomock.Any(), []string{"test-external-rule-1"}).
					Return([]*caseManagementPb.Rule{ruleFixture1}, nil)
				mock.mockRuleManager.EXPECT().GetPrecisionForRule(gomock.Any(), ruleFixture1).Return(float32(0.6), nil)
			},
			want: &activityPb.ValidateAlertResponse{AlertWithRule: &caseManagementPb.AlertWithRuleDetails{
				Alert: buildAlertObject(rawAlertValidWithExternalRuleId, ruleFixture1, 0.6), Rule: ruleFixture1}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			proc, mockedDeps, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(proc)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got := &activityPb.ValidateAlertResponse{}
			result, err := env.ExecuteActivity(riskNs.ValidateAlert, tt.args.req)
			if result != nil && result.HasValue() {
				getErr := result.Get(&got)
				if getErr != nil {
					t.Errorf("ValidateAlert() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ValidateAlert() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ValidateAlert() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(got, tt.want):
				t.Errorf("ValidateAlert() got = %v, want %v", got, tt.want)
			}
		})
	}
}
