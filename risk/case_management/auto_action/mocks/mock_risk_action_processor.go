// Code generated by MockGen. DO NOT EDIT.
// Source: risk_action_processor.go

// Package mock_auto_action is a generated GoMock package.
package mock_auto_action

import (
	context "context"
	reflect "reflect"

	case_management "github.com/epifi/gamma/api/risk/case_management"
	auto_action "github.com/epifi/gamma/api/risk/case_management/auto_action"
	review "github.com/epifi/gamma/api/risk/case_management/review"
	gomock "github.com/golang/mock/gomock"
)

// MockRiskActionProcessor is a mock of RiskActionProcessor interface.
type MockRiskActionProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockRiskActionProcessorMockRecorder
}

// MockRiskActionProcessorMockRecorder is the mock recorder for MockRiskActionProcessor.
type MockRiskActionProcessorMockRecorder struct {
	mock *MockRiskActionProcessor
}

// NewMockRiskActionProcessor creates a new mock instance.
func NewMockRiskActionProcessor(ctrl *gomock.Controller) *MockRiskActionProcessor {
	mock := &MockRiskActionProcessor{ctrl: ctrl}
	mock.recorder = &MockRiskActionProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskActionProcessor) EXPECT() *MockRiskActionProcessorMockRecorder {
	return m.recorder
}

// Dedupe mocks base method.
func (m *MockRiskActionProcessor) Dedupe(ctx context.Context, suggestedActions []*case_management.SuggestedAction, caseObj *review.Case) (*auto_action.AutoAction, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dedupe", ctx, suggestedActions, caseObj)
	ret0, _ := ret[0].(*auto_action.AutoAction)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Dedupe indicates an expected call of Dedupe.
func (mr *MockRiskActionProcessorMockRecorder) Dedupe(ctx, suggestedActions, caseObj interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dedupe", reflect.TypeOf((*MockRiskActionProcessor)(nil).Dedupe), ctx, suggestedActions, caseObj)
}

// EnrichAutoCommunication mocks base method.
func (m *MockRiskActionProcessor) EnrichAutoCommunication(ctx context.Context, action *auto_action.DeduplicatedReviewAction, caseId string) (*auto_action.DeduplicatedReviewAction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EnrichAutoCommunication", ctx, action, caseId)
	ret0, _ := ret[0].(*auto_action.DeduplicatedReviewAction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnrichAutoCommunication indicates an expected call of EnrichAutoCommunication.
func (mr *MockRiskActionProcessorMockRecorder) EnrichAutoCommunication(ctx, action, caseId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnrichAutoCommunication", reflect.TypeOf((*MockRiskActionProcessor)(nil).EnrichAutoCommunication), ctx, action, caseId)
}
