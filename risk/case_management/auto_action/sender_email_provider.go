package auto_action

import (
	"context"

	"github.com/epifi/be-common/pkg/epifierrors"
	riskWorkerConfig "github.com/epifi/gamma/risk/config/worker"
)

type SenderEmailIdProvider interface {
	// GetSenderEmailId abstract the implementation to get sender email id for the risk related emails to the consumers.
	// return epifierrors.ErrRecordNotFound if it could not be found
	GetSenderEmailId(ctx context.Context) (string, error)
}

type SenderEmailFromWorkerConfig struct {
	conf *riskWorkerConfig.Config
}

func NewSenderEmailFromWorkerConfig(conf *riskWorkerConfig.Config) *SenderEmailFromWorkerConfig {
	return &SenderEmailFromWorkerConfig{
		conf: conf,
	}
}

var _ SenderEmailIdProvider = &SenderEmailFromWorkerConfig{}

func (f *SenderEmailFromWorkerConfig) GetSenderEmailId(ctx context.Context) (string, error) {
	if f.conf != nil && f.conf.BlockingWorkFlowConfig != nil && len(f.conf.BlockingWorkFlowConfig.FromEmail) != 0 {
		return f.conf.BlockingWorkFlowConfig.FromEmail, nil
	}
	return "", epifierrors.ErrRecordNotFound
}
