// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	rpc "github.com/epifi/be-common/api/rpc"
	pagination "github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	tiering "github.com/epifi/gamma/api/tiering"
	enums "github.com/epifi/gamma/api/tiering/enums"
	gomock "github.com/golang/mock/gomock"
)

// MockTierCriteriaDao is a mock of TierCriteriaDao interface.
type MockTierCriteriaDao struct {
	ctrl     *gomock.Controller
	recorder *MockTierCriteriaDaoMockRecorder
}

// MockTierCriteriaDaoMockRecorder is the mock recorder for MockTierCriteriaDao.
type MockTierCriteriaDaoMockRecorder struct {
	mock *MockTierCriteriaDao
}

// NewMockTierCriteriaDao creates a new mock instance.
func NewMockTierCriteriaDao(ctrl *gomock.Controller) *MockTierCriteriaDao {
	mock := &MockTierCriteriaDao{ctrl: ctrl}
	mock.recorder = &MockTierCriteriaDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTierCriteriaDao) EXPECT() *MockTierCriteriaDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTierCriteriaDao) Create(ctx context.Context, tierCriteria *tiering.TierCriteria) (*tiering.TierCriteria, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, tierCriteria)
	ret0, _ := ret[0].(*tiering.TierCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockTierCriteriaDaoMockRecorder) Create(ctx, tierCriteria interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTierCriteriaDao)(nil).Create), ctx, tierCriteria)
}

// Get mocks base method.
func (m *MockTierCriteriaDao) Get(ctx context.Context, id string, fieldMasks ...tiering.TierCriteriaFieldMask) (*tiering.TierCriteria, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*tiering.TierCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockTierCriteriaDaoMockRecorder) Get(ctx, id interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockTierCriteriaDao)(nil).Get), varargs...)
}

// GetActiveCriteria mocks base method.
func (m *MockTierCriteriaDao) GetActiveCriteria(ctx context.Context, fieldMasks ...tiering.TierCriteriaFieldMask) (*tiering.TierCriteria, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveCriteria", varargs...)
	ret0, _ := ret[0].(*tiering.TierCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveCriteria indicates an expected call of GetActiveCriteria.
func (mr *MockTierCriteriaDaoMockRecorder) GetActiveCriteria(ctx interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveCriteria", reflect.TypeOf((*MockTierCriteriaDao)(nil).GetActiveCriteria), varargs...)
}

// GetByCriteriaName mocks base method.
func (m *MockTierCriteriaDao) GetByCriteriaName(ctx context.Context, criteriaName enums.CriteriaName, fieldMasks ...tiering.TierCriteriaFieldMask) (*tiering.TierCriteria, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, criteriaName}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByCriteriaName", varargs...)
	ret0, _ := ret[0].(*tiering.TierCriteria)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCriteriaName indicates an expected call of GetByCriteriaName.
func (mr *MockTierCriteriaDaoMockRecorder) GetByCriteriaName(ctx, criteriaName interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, criteriaName}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCriteriaName", reflect.TypeOf((*MockTierCriteriaDao)(nil).GetByCriteriaName), varargs...)
}

// Update mocks base method.
func (m *MockTierCriteriaDao) Update(ctx context.Context, tierCriteria *tiering.TierCriteria, updateMask []tiering.TierCriteriaFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, tierCriteria, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockTierCriteriaDaoMockRecorder) Update(ctx, tierCriteria, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTierCriteriaDao)(nil).Update), ctx, tierCriteria, updateMask)
}

// MockActorTierInfoDao is a mock of ActorTierInfoDao interface.
type MockActorTierInfoDao struct {
	ctrl     *gomock.Controller
	recorder *MockActorTierInfoDaoMockRecorder
}

// MockActorTierInfoDaoMockRecorder is the mock recorder for MockActorTierInfoDao.
type MockActorTierInfoDaoMockRecorder struct {
	mock *MockActorTierInfoDao
}

// NewMockActorTierInfoDao creates a new mock instance.
func NewMockActorTierInfoDao(ctrl *gomock.Controller) *MockActorTierInfoDao {
	mock := &MockActorTierInfoDao{ctrl: ctrl}
	mock.recorder = &MockActorTierInfoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockActorTierInfoDao) EXPECT() *MockActorTierInfoDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockActorTierInfoDao) Create(ctx context.Context, actorTierInfo *tiering.ActorTierInfo) (*tiering.ActorTierInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, actorTierInfo)
	ret0, _ := ret[0].(*tiering.ActorTierInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockActorTierInfoDaoMockRecorder) Create(ctx, actorTierInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockActorTierInfoDao)(nil).Create), ctx, actorTierInfo)
}

// Get mocks base method.
func (m *MockActorTierInfoDao) Get(ctx context.Context, actorId string, fieldMasks ...tiering.ActorTierInfoFieldMask) (*tiering.ActorTierInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*tiering.ActorTierInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockActorTierInfoDaoMockRecorder) Get(ctx, actorId interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockActorTierInfoDao)(nil).Get), varargs...)
}

// Update mocks base method.
func (m *MockActorTierInfoDao) Update(ctx context.Context, actorTierInfo *tiering.ActorTierInfo, updateMask []tiering.ActorTierInfoFieldMask) (*tiering.ActorTierInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, actorTierInfo, updateMask)
	ret0, _ := ret[0].(*tiering.ActorTierInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockActorTierInfoDaoMockRecorder) Update(ctx, actorTierInfo, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockActorTierInfoDao)(nil).Update), ctx, actorTierInfo, updateMask)
}

// MockEligibleTierMovementDao is a mock of EligibleTierMovementDao interface.
type MockEligibleTierMovementDao struct {
	ctrl     *gomock.Controller
	recorder *MockEligibleTierMovementDaoMockRecorder
}

// MockEligibleTierMovementDaoMockRecorder is the mock recorder for MockEligibleTierMovementDao.
type MockEligibleTierMovementDaoMockRecorder struct {
	mock *MockEligibleTierMovementDao
}

// NewMockEligibleTierMovementDao creates a new mock instance.
func NewMockEligibleTierMovementDao(ctrl *gomock.Controller) *MockEligibleTierMovementDao {
	mock := &MockEligibleTierMovementDao{ctrl: ctrl}
	mock.recorder = &MockEligibleTierMovementDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEligibleTierMovementDao) EXPECT() *MockEligibleTierMovementDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockEligibleTierMovementDao) Create(ctx context.Context, eligibleTierMovement *tiering.EligibleTierMovement) (*tiering.EligibleTierMovement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, eligibleTierMovement)
	ret0, _ := ret[0].(*tiering.EligibleTierMovement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockEligibleTierMovementDaoMockRecorder) Create(ctx, eligibleTierMovement interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockEligibleTierMovementDao)(nil).Create), ctx, eligibleTierMovement)
}

// Get mocks base method.
func (m *MockEligibleTierMovementDao) Get(ctx context.Context, id string, fieldMasks ...tiering.EligibleTierMovementFieldMask) (*tiering.EligibleTierMovement, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*tiering.EligibleTierMovement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockEligibleTierMovementDaoMockRecorder) Get(ctx, id interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockEligibleTierMovementDao)(nil).Get), varargs...)
}

// GetByActorId mocks base method.
func (m *MockEligibleTierMovementDao) GetByActorId(ctx context.Context, actorId string, limit int) ([]*tiering.EligibleTierMovement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId, limit)
	ret0, _ := ret[0].([]*tiering.EligibleTierMovement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockEligibleTierMovementDaoMockRecorder) GetByActorId(ctx, actorId, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockEligibleTierMovementDao)(nil).GetByActorId), ctx, actorId, limit)
}

// GetLatestByActorIdAndMovementType mocks base method.
func (m *MockEligibleTierMovementDao) GetLatestByActorIdAndMovementType(ctx context.Context, actorId string, movementType enums.TierMovementType, fieldMasks ...tiering.EligibleTierMovementFieldMask) (*tiering.EligibleTierMovement, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, movementType}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestByActorIdAndMovementType", varargs...)
	ret0, _ := ret[0].(*tiering.EligibleTierMovement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdAndMovementType indicates an expected call of GetLatestByActorIdAndMovementType.
func (mr *MockEligibleTierMovementDaoMockRecorder) GetLatestByActorIdAndMovementType(ctx, actorId, movementType interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, movementType}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdAndMovementType", reflect.TypeOf((*MockEligibleTierMovementDao)(nil).GetLatestByActorIdAndMovementType), varargs...)
}

// GetLatestByActorIdAndStatus mocks base method.
func (m *MockEligibleTierMovementDao) GetLatestByActorIdAndStatus(ctx context.Context, actorId string, status enums.EligibleTierMovementStatus, fieldMasks ...tiering.EligibleTierMovementFieldMask) (*tiering.EligibleTierMovement, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, status}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestByActorIdAndStatus", varargs...)
	ret0, _ := ret[0].(*tiering.EligibleTierMovement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdAndStatus indicates an expected call of GetLatestByActorIdAndStatus.
func (mr *MockEligibleTierMovementDaoMockRecorder) GetLatestByActorIdAndStatus(ctx, actorId, status interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, status}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdAndStatus", reflect.TypeOf((*MockEligibleTierMovementDao)(nil).GetLatestByActorIdAndStatus), varargs...)
}

// GetLatestByActorIdMovementTypeStatus mocks base method.
func (m *MockEligibleTierMovementDao) GetLatestByActorIdMovementTypeStatus(ctx context.Context, actorId string, movementType enums.TierMovementType, status enums.EligibleTierMovementStatus, fieldMasks ...tiering.EligibleTierMovementFieldMask) (*tiering.EligibleTierMovement, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, movementType, status}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestByActorIdMovementTypeStatus", varargs...)
	ret0, _ := ret[0].(*tiering.EligibleTierMovement)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdMovementTypeStatus indicates an expected call of GetLatestByActorIdMovementTypeStatus.
func (mr *MockEligibleTierMovementDaoMockRecorder) GetLatestByActorIdMovementTypeStatus(ctx, actorId, movementType, status interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, movementType, status}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdMovementTypeStatus", reflect.TypeOf((*MockEligibleTierMovementDao)(nil).GetLatestByActorIdMovementTypeStatus), varargs...)
}

// Update mocks base method.
func (m *MockEligibleTierMovementDao) Update(ctx context.Context, eligibleTierMovement *tiering.EligibleTierMovement, updateMask []tiering.EligibleTierMovementFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, eligibleTierMovement, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockEligibleTierMovementDaoMockRecorder) Update(ctx, eligibleTierMovement, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockEligibleTierMovementDao)(nil).Update), ctx, eligibleTierMovement, updateMask)
}

// MockTierMovementHistoryDao is a mock of TierMovementHistoryDao interface.
type MockTierMovementHistoryDao struct {
	ctrl     *gomock.Controller
	recorder *MockTierMovementHistoryDaoMockRecorder
}

// MockTierMovementHistoryDaoMockRecorder is the mock recorder for MockTierMovementHistoryDao.
type MockTierMovementHistoryDaoMockRecorder struct {
	mock *MockTierMovementHistoryDao
}

// NewMockTierMovementHistoryDao creates a new mock instance.
func NewMockTierMovementHistoryDao(ctrl *gomock.Controller) *MockTierMovementHistoryDao {
	mock := &MockTierMovementHistoryDao{ctrl: ctrl}
	mock.recorder = &MockTierMovementHistoryDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTierMovementHistoryDao) EXPECT() *MockTierMovementHistoryDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTierMovementHistoryDao) Create(ctx context.Context, tierMovementHistory *tiering.TierMovementHistory) (*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, tierMovementHistory)
	ret0, _ := ret[0].(*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockTierMovementHistoryDaoMockRecorder) Create(ctx, tierMovementHistory interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).Create), ctx, tierMovementHistory)
}

// Get mocks base method.
func (m *MockTierMovementHistoryDao) Get(ctx context.Context, id string, fieldMasks ...tiering.TierMovementHistoryFieldMask) (*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockTierMovementHistoryDaoMockRecorder) Get(ctx, id interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).Get), varargs...)
}

// GetByActorId mocks base method.
func (m *MockTierMovementHistoryDao) GetByActorId(ctx context.Context, actorId string, limit int) ([]*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", ctx, actorId, limit)
	ret0, _ := ret[0].([]*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetByActorId(ctx, actorId, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetByActorId), ctx, actorId, limit)
}

// GetByEligibilityMovementRefId mocks base method.
func (m *MockTierMovementHistoryDao) GetByEligibilityMovementRefId(ctx context.Context, eligibilityMovementReferenceId string, fieldMasks ...tiering.TierMovementHistoryFieldMask) (*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, eligibilityMovementReferenceId}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByEligibilityMovementRefId", varargs...)
	ret0, _ := ret[0].(*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByEligibilityMovementRefId indicates an expected call of GetByEligibilityMovementRefId.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetByEligibilityMovementRefId(ctx, eligibilityMovementReferenceId interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, eligibilityMovementReferenceId}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByEligibilityMovementRefId", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetByEligibilityMovementRefId), varargs...)
}

// GetDistinctTiersForActor mocks base method.
func (m *MockTierMovementHistoryDao) GetDistinctTiersForActor(ctx context.Context, actorId string, timeSince time.Time) ([]enums.Tier, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDistinctTiersForActor", ctx, actorId, timeSince)
	ret0, _ := ret[0].([]enums.Tier)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDistinctTiersForActor indicates an expected call of GetDistinctTiersForActor.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetDistinctTiersForActor(ctx, actorId, timeSince interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDistinctTiersForActor", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetDistinctTiersForActor), ctx, actorId, timeSince)
}

// GetLatestByActorId mocks base method.
func (m *MockTierMovementHistoryDao) GetLatestByActorId(ctx context.Context, actorId string, fieldMasks ...tiering.TierMovementHistoryFieldMask) (*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestByActorId", varargs...)
	ret0, _ := ret[0].(*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorId indicates an expected call of GetLatestByActorId.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetLatestByActorId(ctx, actorId interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorId", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetLatestByActorId), varargs...)
}

// GetLatestByActorIdAndMovementType mocks base method.
func (m *MockTierMovementHistoryDao) GetLatestByActorIdAndMovementType(ctx context.Context, actorId string, movementType enums.TierMovementType, fieldMasks ...tiering.TierMovementHistoryFieldMask) (*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, movementType}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestByActorIdAndMovementType", varargs...)
	ret0, _ := ret[0].(*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdAndMovementType indicates an expected call of GetLatestByActorIdAndMovementType.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetLatestByActorIdAndMovementType(ctx, actorId, movementType interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, movementType}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdAndMovementType", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetLatestByActorIdAndMovementType), varargs...)
}

// GetLatestByActorIdAndTierBeforeTimestamp mocks base method.
func (m *MockTierMovementHistoryDao) GetLatestByActorIdAndTierBeforeTimestamp(ctx context.Context, actorId string, tiers []enums.Tier, timeBefore time.Time, fieldMasks ...tiering.TierMovementHistoryFieldMask) (*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, tiers, timeBefore}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetLatestByActorIdAndTierBeforeTimestamp", varargs...)
	ret0, _ := ret[0].(*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestByActorIdAndTierBeforeTimestamp indicates an expected call of GetLatestByActorIdAndTierBeforeTimestamp.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetLatestByActorIdAndTierBeforeTimestamp(ctx, actorId, tiers, timeBefore interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, tiers, timeBefore}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestByActorIdAndTierBeforeTimestamp", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetLatestByActorIdAndTierBeforeTimestamp), varargs...)
}

// GetMovementCountByActorIdMovementType mocks base method.
func (m *MockTierMovementHistoryDao) GetMovementCountByActorIdMovementType(ctx context.Context, actorId string, movementType enums.TierMovementType, timeAfter time.Time) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMovementCountByActorIdMovementType", ctx, actorId, movementType, timeAfter)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMovementCountByActorIdMovementType indicates an expected call of GetMovementCountByActorIdMovementType.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetMovementCountByActorIdMovementType(ctx, actorId, movementType, timeAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMovementCountByActorIdMovementType", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetMovementCountByActorIdMovementType), ctx, actorId, movementType, timeAfter)
}

// GetMovementCountByActorIdTierFromMovementType mocks base method.
func (m *MockTierMovementHistoryDao) GetMovementCountByActorIdTierFromMovementType(ctx context.Context, actorId string, tierFrom enums.Tier, movementType enums.TierMovementType, timeAfter time.Time) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMovementCountByActorIdTierFromMovementType", ctx, actorId, tierFrom, movementType, timeAfter)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMovementCountByActorIdTierFromMovementType indicates an expected call of GetMovementCountByActorIdTierFromMovementType.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetMovementCountByActorIdTierFromMovementType(ctx, actorId, tierFrom, movementType, timeAfter interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMovementCountByActorIdTierFromMovementType", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetMovementCountByActorIdTierFromMovementType), ctx, actorId, tierFrom, movementType, timeAfter)
}

// GetMovementListByActorIdMovementTypeAfterTimestamp mocks base method.
func (m *MockTierMovementHistoryDao) GetMovementListByActorIdMovementTypeAfterTimestamp(ctx context.Context, actorId string, movementType enums.TierMovementType, timeAfter time.Time, limit int) ([]*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMovementListByActorIdMovementTypeAfterTimestamp", ctx, actorId, movementType, timeAfter, limit)
	ret0, _ := ret[0].([]*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMovementListByActorIdMovementTypeAfterTimestamp indicates an expected call of GetMovementListByActorIdMovementTypeAfterTimestamp.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetMovementListByActorIdMovementTypeAfterTimestamp(ctx, actorId, movementType, timeAfter, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMovementListByActorIdMovementTypeAfterTimestamp", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetMovementListByActorIdMovementTypeAfterTimestamp), ctx, actorId, movementType, timeAfter, limit)
}

// GetMovementListByActorIdTierFromMovementType mocks base method.
func (m *MockTierMovementHistoryDao) GetMovementListByActorIdTierFromMovementType(ctx context.Context, actorId string, tierFrom enums.Tier, movementType enums.TierMovementType, timeAfter time.Time, limit int) ([]*tiering.TierMovementHistory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMovementListByActorIdTierFromMovementType", ctx, actorId, tierFrom, movementType, timeAfter, limit)
	ret0, _ := ret[0].([]*tiering.TierMovementHistory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMovementListByActorIdTierFromMovementType indicates an expected call of GetMovementListByActorIdTierFromMovementType.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetMovementListByActorIdTierFromMovementType(ctx, actorId, tierFrom, movementType, timeAfter, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMovementListByActorIdTierFromMovementType", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetMovementListByActorIdTierFromMovementType), ctx, actorId, tierFrom, movementType, timeAfter, limit)
}

// GetTierMovementHistories mocks base method.
func (m *MockTierMovementHistoryDao) GetTierMovementHistories(ctx context.Context, actorId string, pageToken *pagination.PageToken, pageSize uint32, options []storagev2.FilterOption, fieldMasks ...tiering.TierMovementHistoryFieldMask) ([]*tiering.TierMovementHistory, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, pageToken, pageSize, options}
	for _, a := range fieldMasks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTierMovementHistories", varargs...)
	ret0, _ := ret[0].([]*tiering.TierMovementHistory)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTierMovementHistories indicates an expected call of GetTierMovementHistories.
func (mr *MockTierMovementHistoryDaoMockRecorder) GetTierMovementHistories(ctx, actorId, pageToken, pageSize, options interface{}, fieldMasks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, pageToken, pageSize, options}, fieldMasks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTierMovementHistories", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).GetTierMovementHistories), varargs...)
}

// Update mocks base method.
func (m *MockTierMovementHistoryDao) Update(ctx context.Context, tierMovementHistory *tiering.TierMovementHistory, updateMask []tiering.TierMovementHistoryFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, tierMovementHistory, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockTierMovementHistoryDaoMockRecorder) Update(ctx, tierMovementHistory, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockTierMovementHistoryDao)(nil).Update), ctx, tierMovementHistory, updateMask)
}

// MockActorScreenInteractionDao is a mock of ActorScreenInteractionDao interface.
type MockActorScreenInteractionDao struct {
	ctrl     *gomock.Controller
	recorder *MockActorScreenInteractionDaoMockRecorder
}

// MockActorScreenInteractionDaoMockRecorder is the mock recorder for MockActorScreenInteractionDao.
type MockActorScreenInteractionDaoMockRecorder struct {
	mock *MockActorScreenInteractionDao
}

// NewMockActorScreenInteractionDao creates a new mock instance.
func NewMockActorScreenInteractionDao(ctrl *gomock.Controller) *MockActorScreenInteractionDao {
	mock := &MockActorScreenInteractionDao{ctrl: ctrl}
	mock.recorder = &MockActorScreenInteractionDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockActorScreenInteractionDao) EXPECT() *MockActorScreenInteractionDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockActorScreenInteractionDao) Create(ctx context.Context, actorScreenInteraction *tiering.ActorScreenInteraction) (*tiering.ActorScreenInteraction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, actorScreenInteraction)
	ret0, _ := ret[0].(*tiering.ActorScreenInteraction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockActorScreenInteractionDaoMockRecorder) Create(ctx, actorScreenInteraction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockActorScreenInteractionDao)(nil).Create), ctx, actorScreenInteraction)
}

// GetByActorIdAndScreen mocks base method.
func (m *MockActorScreenInteractionDao) GetByActorIdAndScreen(ctx context.Context, actorId string, screen enums.TieringScreen) (*tiering.ActorScreenInteraction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorIdAndScreen", ctx, actorId, screen)
	ret0, _ := ret[0].(*tiering.ActorScreenInteraction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndScreen indicates an expected call of GetByActorIdAndScreen.
func (mr *MockActorScreenInteractionDaoMockRecorder) GetByActorIdAndScreen(ctx, actorId, screen interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndScreen", reflect.TypeOf((*MockActorScreenInteractionDao)(nil).GetByActorIdAndScreen), ctx, actorId, screen)
}

// Update mocks base method.
func (m *MockActorScreenInteractionDao) Update(ctx context.Context, actorScreenInteraction *tiering.ActorScreenInteraction, updateMask []tiering.ActorScreenInteractionFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, actorScreenInteraction, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockActorScreenInteractionDaoMockRecorder) Update(ctx, actorScreenInteraction, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockActorScreenInteractionDao)(nil).Update), ctx, actorScreenInteraction, updateMask)
}
