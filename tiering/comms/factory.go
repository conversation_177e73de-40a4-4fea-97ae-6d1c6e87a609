package comms

import (
	"fmt"

	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/tiering/comms/processors"
)

// Factory is the top layer on top of comms processors
type Factory interface {
	// GetCommTypeImpl fetches the comms processor based on comms type
	GetCommTypeImpl(notificationType tieringEnumPb.NotificationType) (processors.Processor, error)
}

// FactoryService implements Factory
type FactoryService struct {
	upgradeProcessor   *processors.UpgradeProcessor
	downgradeProcessor *processors.DowngradeProcessor
	graceProcessor     *processors.GraceProcessor
}

func NewFactoryService(
	upgradeProcessor *processors.UpgradeProcessor,
	downgradeProcessor *processors.DowngradeProcessor,
	graceProcessor *processors.GraceProcessor,
) *FactoryService {
	return &FactoryService{
		upgradeProcessor:   upgradeProcessor,
		downgradeProcessor: downgradeProcessor,
		graceProcessor:     graceProcessor,
	}
}

var _ Factory = &FactoryService{}

func (s *FactoryService) GetCommTypeImpl(notificationType tieringEnumPb.NotificationType) (processors.Processor, error) {
	switch notificationType {
	case tieringEnumPb.NotificationType_NOTIFICATION_TYPE_UPGRADE:
		return s.upgradeProcessor, nil
	case tieringEnumPb.NotificationType_NOTIFICATION_TYPE_DOWNGRADE:
		return s.downgradeProcessor, nil
	case tieringEnumPb.NotificationType_NOTIFICATION_TYPE_GRACE:
		return s.graceProcessor, nil
	default:
		return nil, fmt.Errorf("%s notification type is not handled", notificationType.String())
	}
}
