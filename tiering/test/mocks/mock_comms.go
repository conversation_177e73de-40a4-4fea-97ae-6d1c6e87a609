// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	enums "github.com/epifi/gamma/api/tiering/enums"
	gomock "github.com/golang/mock/gomock"
)

// MockSender is a mock of Sender interface.
type MockSender struct {
	ctrl     *gomock.Controller
	recorder *MockSenderMockRecorder
}

// MockSenderMockRecorder is the mock recorder for MockSender.
type MockSenderMockRecorder struct {
	mock *MockSender
}

// NewMockSender creates a new mock instance.
func NewMockSender(ctrl *gomock.Controller) *MockSender {
	mock := &MockSender{ctrl: ctrl}
	mock.recorder = &MockSenderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSender) EXPECT() *MockSenderMockRecorder {
	return m.recorder
}

// SendNotification mocks base method.
func (m *MockSender) SendNotification(ctx context.Context, actorId string, notificationType enums.NotificationType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendNotification", ctx, actorId, notificationType)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendNotification indicates an expected call of SendNotification.
func (mr *MockSenderMockRecorder) SendNotification(ctx, actorId, notificationType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendNotification", reflect.TypeOf((*MockSender)(nil).SendNotification), ctx, actorId, notificationType)
}
