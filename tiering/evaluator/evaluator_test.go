// nolint: dogsled
package evaluator

import (
	"context"
	"errors"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	moneypb "google.golang.org/genproto/googleapis/type/money"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/salaryprogram"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	segmentpb "github.com/epifi/gamma/api/segment"
	mocks2 "github.com/epifi/gamma/api/segment/mocks"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/tiering/config/genconf"
	evaluatorOpts "github.com/epifi/gamma/tiering/evaluator/options"

	kycPb "github.com/epifi/gamma/api/kyc"
	tieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	"github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/tiering/test"
	"github.com/epifi/gamma/tiering/test/mocks"
)

var (
	gconf *genconf.Config

	FiveTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_BASE_TIER,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_BaseTier{
								BaseTier: &criteriaPb.BaseTier{
									BaseTier: enums.Tier_TIER_FIVE,
								},
							},
						},
					},
				},
			},
		}
	}

	TenTierOptions = func() []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_BASE_TIER,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_BaseTier{
								BaseTier: &criteriaPb.BaseTier{
									BaseTier: enums.Tier_TIER_TEN,
								},
							},
						},
					},
				},
			},
		}
	}

	OneHundredTierOptions = func(minBalance *moneypb.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Balance{
								Balance: &criteriaPb.Balance{
									MinBalance: minBalance,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_UsStocksSip{
								UsStocksSip: &criteriaPb.UsStocksSip{
									MinWalletAddFunds: &moneypb.Money{
										CurrencyCode: "USD",
										Units:        200,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Deposits{
								Deposits: &criteriaPb.Deposits{
									MinDepositsAmount: &moneypb.Money{
										CurrencyCode: "INR",
										Units:        100000,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_AA_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{

					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
									SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	OneThousandTierOptions = func(minBalance *moneypb.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Balance{
								Balance: &criteriaPb.Balance{
									MinBalance: minBalance,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_UsStocksSip{
								UsStocksSip: &criteriaPb.UsStocksSip{
									MinWalletAddFunds: &moneypb.Money{
										CurrencyCode: "USD",
										Units:        500,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Deposits{
								Deposits: &criteriaPb.Deposits{
									MinDepositsAmount: &moneypb.Money{
										CurrencyCode: "INR",
										Units:        200000,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_AA_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{

					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
									SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	OneThousandEightHundredTierOptions = func(activationType salaryprogram.SalaryActivationType) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType:               activationType,
									B2BSalaryProgramVerificationStatus: true,
									B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_1,
								},
							},
						},
					},
				},
			},
		}
	}

	TwoThousandTierOptions = func(activationType salaryprogram.SalaryActivationType) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType:               activationType,
									B2BSalaryProgramVerificationStatus: true,
									B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_2,
								},
							},
						},
					},
				},
			},
		}
	}

	OneThousandFiveHundredTierOptions = func(activationType salaryprogram.SalaryActivationType) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: activationType,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_AA_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{

					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
									SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_3,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}
	criteria1 = &tieringPb.TierCriteria{
		Id:           "57f83510-407f-4a9a-ada3-998a032a54ee",
		CriteriaName: tieringEnumPb.CriteriaName_CRITERIA_NAME_C_TWO,
		Details: &tieringPb.Criterion{
			MovementDetailsList: []*criteriaPb.MovementDetails{
				{
					TierName: tieringEnumPb.Tier_TIER_FIVE,
					Options:  FiveTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_TEN,
					Options:  TenTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					Options:  OneHundredTierOptions(&moneypb.Money{CurrencyCode: "INR", Units: 10000}),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					Options:  OneThousandTierOptions(&moneypb.Money{CurrencyCode: "INR", Units: 50000}),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED,
					Options:  OneThousandFiveHundredTierOptions(salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_TWO_THOUSAND,
					Options:  TwoThousandTierOptions(salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION),
				},
			},
		},
		Status: tieringEnumPb.TierCriteriaStatus_TIER_CRITERIA_STATUS_ACTIVE,
	}

	OneHundredTierOptionsV2 = func(minBalanceForUpgrade, minBalanceToPreventDowngrade *moneypb.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_BalanceV2{
								BalanceV2: &criteriaPb.BalanceV2{
									MinBalanceForUpgrade:         minBalanceForUpgrade,
									MinBalanceToPreventDowngrade: minBalanceToPreventDowngrade,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_UsStocksSip{
								UsStocksSip: &criteriaPb.UsStocksSip{
									MinWalletAddFunds: &moneypb.Money{
										CurrencyCode: "USD",
										Units:        200,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Deposits{
								Deposits: &criteriaPb.Deposits{
									MinDepositsAmount: &moneypb.Money{
										CurrencyCode: "INR",
										Units:        100000,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_AA_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{

					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
									SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	OneThousandTierOptionsV2 = func(minBalanceForUpgrade, minBalanceToPreventDowngrade *moneypb.Money) []*criteriaPb.Option {
		return []*criteriaPb.Option{
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_BalanceV2{
								BalanceV2: &criteriaPb.BalanceV2{
									MinBalanceForUpgrade:         minBalanceForUpgrade,
									MinBalanceToPreventDowngrade: minBalanceToPreventDowngrade,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_UsStocksSip{
								UsStocksSip: &criteriaPb.UsStocksSip{
									MinWalletAddFunds: &moneypb.Money{
										CurrencyCode: "USD",
										Units:        500,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
				Actions: []*criteriaPb.Action{
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Deposits{
								Deposits: &criteriaPb.Deposits{
									MinDepositsAmount: &moneypb.Money{
										CurrencyCode: "INR",
										Units:        200000,
									},
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
			{
				CriteriaOptionType: tieringEnumPb.CriteriaOptionType_AA_SALARY_AND_KYC,
				Actions: []*criteriaPb.Action{

					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Salary{
								Salary: &criteriaPb.Salary{
									SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
									SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
								},
							},
						},
					},
					{
						ActionDetails: &criteriaPb.QualifyingCriteria{
							Criteria: &criteriaPb.QualifyingCriteria_Kyc{
								Kyc: &criteriaPb.Kyc{
									KycLevel: kycPb.KYCLevel_FULL_KYC,
								},
							},
						},
					},
				},
			},
		}
	}

	criteria2 = &tieringPb.TierCriteria{
		Id:           "99f83510-407f-4a9a-ada3-998a032a54ee",
		CriteriaName: tieringEnumPb.CriteriaName_CRITERIA_NAME_C_FOUR,
		Details: &tieringPb.Criterion{
			MovementDetailsList: []*criteriaPb.MovementDetails{
				{
					TierName: tieringEnumPb.Tier_TIER_FIVE,
					Options:  FiveTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_TEN,
					Options:  TenTierOptions(),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					Options:  OneHundredTierOptionsV2(&moneypb.Money{CurrencyCode: "INR", Units: 10000}, &moneypb.Money{CurrencyCode: "INR", Units: 10000}),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND,
					Options:  OneThousandTierOptionsV2(&moneypb.Money{CurrencyCode: "INR", Units: 50000}, &moneypb.Money{CurrencyCode: "INR", Units: 40000}),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED,
					Options:  OneThousandFiveHundredTierOptions(salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED,
					Options:  OneThousandEightHundredTierOptions(salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION),
				},
				{
					TierName: tieringEnumPb.Tier_TIER_TWO_THOUSAND,
					Options:  TwoThousandTierOptions(salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION),
				},
			},
		},
		Status: tieringEnumPb.TierCriteriaStatus_TIER_CRITERIA_STATUS_ACTIVE,
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, _, gconf, _, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestTierEvaluatorService_Evaluate(t *testing.T) {
	type mockStruct struct {
		mockDataCollectorFactory  *mocks.MockDataCollectorFactory
		mockBalanceDataCollector  *mocks.MockDataCollector
		mockKycDataCollector      *mocks.MockDataCollector
		mockSalaryDataCollector   *mocks.MockDataCollector
		mockBaseTierDataCollector *mocks.MockDataCollector
		mockUsstocksDataCollector *mocks.MockDataCollector
		mockDepositsDataCollector *mocks.MockDataCollector
		mockSegmentationClient    *mocks2.MockSegmentationServiceClient
	}
	type args struct {
		ctx          context.Context
		actorId      string
		tierCriteria *tieringPb.TierCriteria
		mocks        func(mockStruct *mockStruct)
		opts         []evaluatorOpts.EvaluatorOptions
	}
	tests := []struct {
		name    string
		args    args
		want    *tieringPb.EvaluatorMeta
		wantErr bool
	}{
		{
			name: "success, 2000 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 2000 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 1800 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria2,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_1,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 2000 tier, user base tier is 5",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_FIVE,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 1800 tier, user base tier is 5",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria2,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_1,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_FIVE,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 1500 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 1000 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        50000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 100 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        10000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 100 tier, full kyc, not enough funds",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        9999,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "success, 100 tier, min kyc, enough funds",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        10000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_MIN_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "success, 10 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_MIN_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "success, 5 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_MIN_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_FIVE,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_FIVE,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "failure, error collecting balance data",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error collecting kyc data",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error collecting salary data",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.ZeroUSD(),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},

			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error getting balance data collector",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(nil, errors.New("some random error"))
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
				},
			},

			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error getting kyc data collector",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(nil, errors.New("some random error"))
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
				},
			},

			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error getting salary data collector",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria1,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(nil, errors.New("some random error"))
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
				},
			},

			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "success, criteria 11, tier 1000, satisfied on USS, with multiple ways flag enabled",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(500),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},

			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_US_STOCKS_SIP_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied on USS for Tier_1000, multiple ways flag disabled, evaluated to base tier tier_10",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(500),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},

			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for aa salary band 2, multiple ways flag disabled, evaluated to tier_1270",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
								SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_TWO_SEVENTY,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for aa salary band 2, multiple ways flag enabled, evaluated to tier_1000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
								SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_AA_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_AA_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for aa salary band 2, multiple ways flag disabled, evaluated to tier_1270",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
								SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_TWO_SEVENTY,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for aa salary band 3, multiple ways flag enabled, evaluated to tier_1290",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
								SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_3,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_TWO_NINETY,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_AA_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_AA_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for aa salary band 3, multiple ways flag disabled, evaluated to tier_1290",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
								SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_3,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_TWO_NINETY,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		// summary of next 4 test cases: full salary band 2 with
		// 1. b2c user - multiple ways flag enabled
		// 2. b2b user - multiple ways flag enabled
		// 3. b2c user - multiple ways flag disabled
		// 4. b2b user - multiple ways flag disabled
		{
			name: "success, criteria 11, satisfied for full salary band 2, b2c user, multiple ways flag enabled, evaluated to tier_1000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_2,
								B2BSalaryProgramVerificationStatus: false,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for full salary band 2, b2b user, multiple ways flag enabled, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_2,
								B2BSalaryProgramVerificationStatus: true,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for full salary band 2, b2c user, multiple ways flag disabled, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_2,
								B2BSalaryProgramVerificationStatus: false,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for full salary band 2, b2b user, multiple ways flag disabled, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_2,
								B2BSalaryProgramVerificationStatus: false,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for full salary, band unspecified, b2c user, multiple ways flag enabled, evaluated to base tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								// SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_2,
								B2BSalaryProgramVerificationStatus: false,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for full salary, band unspecified, b2b user, multiple ways flag enabled, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_UNSPECIFIED,
								B2BSalaryProgramVerificationStatus: true,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for deposit condition for plus, multiple ways flag enabled, evaluated to tier_100",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.AmountINR(200000).GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_DEPOSITS_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_DEPOSITS_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for balance condition for infinite, multiple ways flag disabled, evaluated to tier_100",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.AmountINR(0).GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 11, satisfied for full salary, band 3, satisfied balance criteria for prime, b2c user, multiple ways flag enabled, evaluated to prime with multiple criterias",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_3,
								B2BSalaryProgramVerificationStatus: false,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_TWO_NINETY,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC, enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, old criteria, b2b_salary_band_1, multiple ways flag enabled, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_1,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, old criteria, b2b_salary_band_2, multiple ways flag enabled, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, new criteria, b2b_salary_band_1, multiple ways flag enabled, evaluated to tier_1800",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria13,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_1,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, new criteria, b2b_salary_band_2, multiple ways flag enabled, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria13,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 12, satisfied for aa salary band 2, multiple ways flag enabled, actor in AaSalaryExcludedSegments, evaluated to tier based on balance - Standard",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
								SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segmentpb.SegmentMembership{
							"aa-salary-exclusion-segment": {
								SegmentStatus: segmentpb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
								IsActorMember: true,
							},
						},
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "success, criteria 12, satisfied for full salary, band 3, satisfied balance criteria for prime, b2c user, multiple ways flag enabled, actor in SalaryB2CExcludedSegments, evaluated to prime with balance criteria",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria12,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								SalaryBand:                         salaryEnumsPb.SalaryBand_SALARY_BAND_3,
								B2BSalaryProgramVerificationStatus: false,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segmentpb.SegmentMembership{
							"salary-b2c-exclusion-segment": {
								SegmentStatus: segmentpb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
								IsActorMember: true,
							},
						},
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_TWO_NINETY,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, new criteria, b2b_salary_band_2, multiple ways flag enabled, actor in SalaryB2CExcludedSegments, evaluated to tier_2000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria13,
				opts:         []evaluatorOpts.EvaluatorOptions{evaluatorOpts.WithMultipleWaysEvaluation()},
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsstocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        200000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
								B2BSalaryProgramVerificationStatus: true,
								B2BSalaryBand:                      salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_2,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsstocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segmentpb.SegmentMembership{
							"salary-b2c-exclusion-segment": {
								SegmentStatus: segmentpb.SegmentStatus_SEGMENT_INSTANCE_FOUND,
								IsActorMember: true,
							},
						},
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			require.NoError(t, unmarshalErr)
			require.NoError(t, unmarshalErr2)

			ctrl := gomock.NewController(t)
			mockDataCollectorFactory := mocks.NewMockDataCollectorFactory(ctrl)
			mockBalanceDataCollector := mocks.NewMockDataCollector(ctrl)
			mockKycDataCollector := mocks.NewMockDataCollector(ctrl)
			mockSalaryDataCollector := mocks.NewMockDataCollector(ctrl)
			mockBaseTierDataCollector := mocks.NewMockDataCollector(ctrl)
			mockUsstocksDataCollector := mocks.NewMockDataCollector(ctrl)
			mockDepositsDataCollector := mocks.NewMockDataCollector(ctrl)
			mockSegmentationClient := mocks2.NewMockSegmentationServiceClient(ctrl)
			defer ctrl.Finish()

			s := &TierEvaluatorService{
				gconf:                gconf,
				dataCollectorFactory: mockDataCollectorFactory,
				segmentationClient:   mockSegmentationClient,
			}
			m := &mockStruct{
				mockDataCollectorFactory:  mockDataCollectorFactory,
				mockBalanceDataCollector:  mockBalanceDataCollector,
				mockKycDataCollector:      mockKycDataCollector,
				mockSalaryDataCollector:   mockSalaryDataCollector,
				mockBaseTierDataCollector: mockBaseTierDataCollector,
				mockUsstocksDataCollector: mockUsstocksDataCollector,
				mockDepositsDataCollector: mockDepositsDataCollector,
				mockSegmentationClient:    mockSegmentationClient,
			}
			tt.args.mocks(m)
			got, err := s.Evaluate(tt.args.ctx, tt.args.actorId, enums.Tier_TIER_UNSPECIFIED, tt.args.tierCriteria, tt.args.opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Evaluate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Evaluate() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestTierEvaluatorService_Evaluate_CriteriaV2(t *testing.T) {
	type mockStruct struct {
		mockDataCollectorFactory  *mocks.MockDataCollectorFactory
		mockBalanceDataCollector  *mocks.MockDataCollector
		mockKycDataCollector      *mocks.MockDataCollector
		mockSalaryDataCollector   *mocks.MockDataCollector
		mockBaseTierDataCollector *mocks.MockDataCollector
		mockUsStocksDataCollector *mocks.MockDataCollector
		mockDepositsDataCollector *mocks.MockDataCollector
		mockSegmentationClient    *mocks2.MockSegmentationServiceClient
	}
	type args struct {
		ctx          context.Context
		actorId      string
		currentTier  enums.Tier
		tierCriteria *tieringPb.TierCriteria
		mocks        func(mockStruct *mockStruct)
	}
	tests := []struct {
		name    string
		args    args
		want    *tieringPb.EvaluatorMeta
		wantErr bool
	}{
		{
			name: "success, 2000 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TWO_THOUSAND,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
			},
			wantErr: false,
		},
		{
			name: "success, 1500 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_SALARY_AND_KYC},
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_SALARY_AND_KYC,
			},
			wantErr: false,
		},
		{
			name: "success, 1000 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        50000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
			},
			wantErr: false,
		},
		{
			name: "success, current tier - 1000. Balance 45000. Evaluated Tier - 1000",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_THOUSAND,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        45000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_THOUSAND,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
			},
			wantErr: false,
		},
		{
			name: "success, current tier - 100. Balance 45000. Evaluated Tier - 100",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        45000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 100 tier",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        10000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_ONE_HUNDRED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BALANCE_V2_AND_KYC,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BALANCE_V2_AND_KYC},
			},
			wantErr: false,
		},
		{
			name: "success, 100 tier, full kyc, not enough funds",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        9999,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "success, 100 tier, min kyc, enough funds",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        10000,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_MIN_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_TEN,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_BASE_TIER,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_BASE_TIER},
			},
			wantErr: false,
		},
		{
			name: "failure, error collecting balance data",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error collecting kyc data",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_SalaryData{
							SalaryData: &tieringPb.SalaryData{
								SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
							},
						},
					}, nil)
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error collecting salary data",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
					m.mockBalanceDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BalanceData{
							BalanceData: &tieringPb.BalanceData{
								AvailableBalance: &moneypb.Money{
									CurrencyCode: "INR",
									Units:        0,
								},
							},
						},
					}, nil)
					m.mockKycDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_KycData{
							KycData: &tieringPb.KycData{
								KycLevel: kycPb.KYCLevel_FULL_KYC,
							},
						},
					}, nil)
					m.mockSalaryDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(nil, errors.New("some random error"))
					m.mockBaseTierDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_BaseTierData{
							BaseTierData: &tieringPb.BaseTierData{
								ActorBaseTier: enums.Tier_TIER_TEN,
							},
						},
					}, nil)
					m.mockUsStocksDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_UsStocksData{
							UsStocksData: &tieringPb.UsStocksData{
								WalletAddFundsValueInL30D: money.AmountUSD(0),
							},
						},
					}, nil)
					m.mockDepositsDataCollector.EXPECT().CollectData(gomock.Any(), gomock.Any()).Return(&tieringPb.CollectedData{
						Data: &tieringPb.CollectedData_DepositsData{
							DepositsData: &tieringPb.DepositsData{
								TotalDeposits: money.ZeroINR().GetPb(),
							},
						},
					}, nil)
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentpb.IsMemberResponse{
						Status:               rpcPb.StatusOk(),
						SegmentMembershipMap: make(map[string]*segmentpb.SegmentMembership),
					}, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error getting balance data collector",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(nil, errors.New("some random error"))
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error getting kyc data collector",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				currentTier:  enums.Tier_TIER_ONE_HUNDRED,
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(nil, errors.New("some random error"))
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockSalaryDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
		{
			name: "failure, error getting salary data collector",
			args: args{
				ctx:          context.Background(),
				actorId:      "actor-1",
				tierCriteria: criteria2,
				mocks: func(m *mockStruct) {
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBalanceDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockKycDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(nil, errors.New("some random error"))
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockBaseTierDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockUsStocksDataCollector, nil)
					m.mockDataCollectorFactory.EXPECT().GetImpl(gomock.Any()).
						Return(m.mockDepositsDataCollector, nil)
				},
			},
			want: &tieringPb.EvaluatorMeta{
				EvaluatedTier:                  enums.Tier_TIER_UNSPECIFIED,
				HighPriorityCriteriaOptionType: enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED,
				SatisfiedCriteriaOptionTypes:   []enums.CriteriaOptionType{enums.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockDataCollectorFactory := mocks.NewMockDataCollectorFactory(ctrl)
			mockBalanceDataCollector := mocks.NewMockDataCollector(ctrl)
			mockKycDataCollector := mocks.NewMockDataCollector(ctrl)
			mockSalaryDataCollector := mocks.NewMockDataCollector(ctrl)
			mockBaseTierDataCollector := mocks.NewMockDataCollector(ctrl)
			mockUsstocksDataCollector := mocks.NewMockDataCollector(ctrl)
			mockDepositsDataCollector := mocks.NewMockDataCollector(ctrl)
			mockSegmentationClient := mocks2.NewMockSegmentationServiceClient(ctrl)
			defer ctrl.Finish()

			s := &TierEvaluatorService{
				gconf:                gconf,
				dataCollectorFactory: mockDataCollectorFactory,
				segmentationClient:   mockSegmentationClient,
			}
			m := &mockStruct{
				mockDataCollectorFactory:  mockDataCollectorFactory,
				mockBalanceDataCollector:  mockBalanceDataCollector,
				mockKycDataCollector:      mockKycDataCollector,
				mockSalaryDataCollector:   mockSalaryDataCollector,
				mockBaseTierDataCollector: mockBaseTierDataCollector,
				mockUsStocksDataCollector: mockUsstocksDataCollector,
				mockDepositsDataCollector: mockDepositsDataCollector,
				mockSegmentationClient:    mockSegmentationClient,
			}
			tt.args.mocks(m)
			got, err := s.Evaluate(tt.args.ctx, tt.args.actorId, tt.args.currentTier, tt.args.tierCriteria)
			if (err != nil) != tt.wantErr {
				t.Errorf("Evaluate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Evaluate() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}
