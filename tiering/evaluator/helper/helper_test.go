package helper

import (
	"errors"
	"testing"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/salaryprogram"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	tieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb2 "github.com/epifi/gamma/api/tiering/enums"
)

func TestEvaluateBalanceData(t *testing.T) {
	type args struct {
		requiredBalanceData *criteriaPb.Balance
		haveBalanceData     *tieringPb.BalanceData
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "true, have == required",
			args: args{
				haveBalanceData: &tieringPb.BalanceData{
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				},
				requiredBalanceData: &criteriaPb.Balance{
					MinBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				},
			},
			want: true,
		},
		{
			name: "true, have < required",
			args: args{
				haveBalanceData: &tieringPb.BalanceData{
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        499,
					},
				},
				requiredBalanceData: &criteriaPb.Balance{
					MinBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				},
			},
			want: false,
		},
		{
			name: "true, have > required",
			args: args{
				haveBalanceData: &tieringPb.BalanceData{
					AvailableBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        501,
					},
				},
				requiredBalanceData: &criteriaPb.Balance{
					MinBalance: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EvaluateBalanceData(tt.args.requiredBalanceData, tt.args.haveBalanceData); got != tt.want {
				t.Errorf("EvaluateBalanceData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEvaluateKycData(t *testing.T) {
	type args struct {
		requiredKycData *criteriaPb.Kyc
		haveKycData     *tieringPb.KycData
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "true, have full, required min",
			args: args{
				haveKycData: &tieringPb.KycData{
					KycLevel: kycPb.KYCLevel_FULL_KYC,
				},
				requiredKycData: &criteriaPb.Kyc{
					KycLevel: kycPb.KYCLevel_MIN_KYC,
				},
			},
			want: true,
		},
		{
			name: "true, have full, required full",
			args: args{
				haveKycData: &tieringPb.KycData{
					KycLevel: kycPb.KYCLevel_FULL_KYC,
				},
				requiredKycData: &criteriaPb.Kyc{
					KycLevel: kycPb.KYCLevel_FULL_KYC,
				},
			},
			want: true,
		},
		{
			name: "true, have min, required min",
			args: args{
				haveKycData: &tieringPb.KycData{
					KycLevel: kycPb.KYCLevel_MIN_KYC,
				},
				requiredKycData: &criteriaPb.Kyc{
					KycLevel: kycPb.KYCLevel_MIN_KYC,
				},
			},
			want: true,
		},
		{
			name: "true, have min, required full",
			args: args{
				haveKycData: &tieringPb.KycData{
					KycLevel: kycPb.KYCLevel_MIN_KYC,
				},
				requiredKycData: &criteriaPb.Kyc{
					KycLevel: kycPb.KYCLevel_FULL_KYC,
				},
			},
			want: false,
		},
		{
			name: "true, have unspecified, required min",
			args: args{
				haveKycData: &tieringPb.KycData{
					KycLevel: kycPb.KYCLevel_UNSPECIFIED,
				},
				requiredKycData: &criteriaPb.Kyc{
					KycLevel: kycPb.KYCLevel_MIN_KYC,
				},
			},
			want: false,
		},
		{
			name: "true, have full, required unspecified",
			args: args{
				haveKycData: &tieringPb.KycData{
					KycLevel: kycPb.KYCLevel_FULL_KYC,
				},
				requiredKycData: &criteriaPb.Kyc{
					KycLevel: kycPb.KYCLevel_UNSPECIFIED,
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EvaluateKycData(tt.args.requiredKycData, tt.args.haveKycData); got != tt.want {
				t.Errorf("EvaluateKycData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEvaluateSalaryData(t *testing.T) {
	type args struct {
		requiredSalaryData        *criteriaPb.Salary
		haveSalaryData            *tieringPb.SalaryData
		evaluationTier            tieringEnumPb2.Tier
		toEvaluateForMultipleWays bool
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "false, not salaried",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
				},
				toEvaluateForMultipleWays: false,
			},
			want: false,
		},
		{
			name: "false, not salary lite",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_ACTIVATION_TYPE_UNSPECIFIED,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION,
				},
				toEvaluateForMultipleWays: false,
			},
			want: false,
		},
		{
			name: "true, salaried",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
				},
				toEvaluateForMultipleWays: false,
			},
			want: true,
		},
		{
			name: "true, salary lite",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION,
				},
				toEvaluateForMultipleWays: false,
			},
			want: true,
		},
		{
			name: "true, salary lite but salary band not in criteria",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_SALARY_LITE_ACTIVATION,
				},
				toEvaluateForMultipleWays: false,
			},
			want: true,
		},
		{
			name: "true, aa salary band 1",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				toEvaluateForMultipleWays: false,
			},
			want: true,
		},
		{
			name: "false, aa salary band 1",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				toEvaluateForMultipleWays: false,
			},
			want: false,
		},
		{
			name: "false, aa salary band 1, different activation type received",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_2,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				toEvaluateForMultipleWays: false,
			},
			want: false,
		},
		{
			name: "false, aa salary band 1, unspecified band",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				toEvaluateForMultipleWays: false,
			},
			want: false,
		},
		{
			name: "true, evaluate salary for multiple ways",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
					B2BSalaryProgramVerificationStatus: true,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType:               salaryprogram.SalaryActivationType_FULL_SALARY_ACTIVATION,
					B2BSalaryProgramVerificationStatus: true,
				},
				toEvaluateForMultipleWays: true,
			},
			want: true,
		},
		{
			name: "true, evaluate band 1 for multiple ways",
			args: args{
				haveSalaryData: &tieringPb.SalaryData{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				requiredSalaryData: &criteriaPb.Salary{
					SalaryActivationType: salaryprogram.SalaryActivationType_AA_SALARY_ACTIVATION,
					SalaryBand:           salaryEnumsPb.SalaryBand_SALARY_BAND_1,
				},
				toEvaluateForMultipleWays: true,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EvaluateSalaryData(tt.args.requiredSalaryData, tt.args.haveSalaryData, tt.args.toEvaluateForMultipleWays, tt.args.evaluationTier, false, false); got != tt.want {
				t.Errorf("EvaluateSalaryData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHandleWrappingErrors(t *testing.T) {
	type args struct {
		errs []error
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "successful wrapping, 2",
			args: args{
				errs: []error{
					errors.New("error 1"),
					errors.New("error 2"),
				},
			},
			wantErr: true,
		},
		{
			name: "successful wrapping, 1",
			args: args{
				errs: []error{
					errors.New("error 1"),
				},
			},
			wantErr: true,
		},
		{
			name: "successful wrapping, 2",
			args: args{
				errs: []error{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := HandleWrappingErrors(tt.args.errs...); (err != nil) != tt.wantErr {
				t.Errorf("HandleWrappingErrors() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestEvaluateUsStockSipData(t *testing.T) {
	type args struct {
		requiredUsStocksData *criteriaPb.UsStocksSip
		haveUsStocksData     *tieringPb.UsStocksData
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "true, have >= required",
			args: args{
				haveUsStocksData: &tieringPb.UsStocksData{
					WalletAddFundsValueInL30D: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
					},
				},
				requiredUsStocksData: &criteriaPb.UsStocksSip{
					MinWalletAddFunds: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
					},
				},
			},
			want: true,
		},
		{
			name: "false, have < required",
			args: args{
				haveUsStocksData: &tieringPb.UsStocksData{
					WalletAddFundsValueInL30D: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        99,
					},
				},
				requiredUsStocksData: &criteriaPb.UsStocksSip{
					MinWalletAddFunds: &moneyPb.Money{
						CurrencyCode: "USD",
						Units:        100,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EvaluateUsStockSipData(tt.args.requiredUsStocksData, tt.args.haveUsStocksData); got != tt.want {
				t.Errorf("EvaluateUsStockSipData() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestEvaluateDepositsData(t *testing.T) {
	type args struct {
		requiredFDData *criteriaPb.Deposits
		haveFDData     *tieringPb.DepositsData
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "true, have >= required",
			args: args{
				haveFDData: &tieringPb.DepositsData{
					TotalDeposits: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100000,
					},
				},
				requiredFDData: &criteriaPb.Deposits{
					MinDepositsAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100000,
					},
				},
			},
			want: true,
		},
		{
			name: "false, have < required",
			args: args{
				haveFDData: &tieringPb.DepositsData{
					TotalDeposits: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        99999,
					},
				},
				requiredFDData: &criteriaPb.Deposits{
					MinDepositsAmount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100000,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EvaluateDepositsData(tt.args.requiredFDData, tt.args.haveFDData); got != tt.want {
				t.Errorf("EvaluateDepositsData() = %v, want %v", got, tt.want)
			}
		})
	}
}
