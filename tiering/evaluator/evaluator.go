//go:generate mockgen -source=evaluator.go -destination=../test/mocks/mock_evaluator.go -package=mocks
package evaluator

import (
	"context"
	"fmt"
	"sort"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/data_collector"
	dataCollectorOptions "github.com/epifi/gamma/tiering/data_collector/options"
	tieringErrors "github.com/epifi/gamma/tiering/errors"
	"github.com/epifi/gamma/tiering/evaluator/helper"
	"github.com/epifi/gamma/tiering/evaluator/options"
)

// optionsPriority is a map of criteria option type to its priority used when criteria has multiple options
var optionsPriority = map[tieringEnumPb.CriteriaOptionType]int{
	tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC: 1,
	tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC:      2,
	tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC:    3,
	tieringEnumPb.CriteriaOptionType_BALANCE_AND_KYC:       4,
	tieringEnumPb.CriteriaOptionType_SALARY_AND_KYC:        5,
	tieringEnumPb.CriteriaOptionType_AA_SALARY_AND_KYC:     6,
	tieringEnumPb.CriteriaOptionType_BASE_TIER:             7,
}

// ignoreTiersInNewCriteria is a list of tiers that should be ignored when evaluating with new criteria(multiple options)
var ignoreTiersInNewCriteria = []tieringEnumPb.Tier{
	tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_FIFTY,
	tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_SEVENTY,
}

// TierEvaluator will be implemented by all criteria with distinct QC combinations
// eg: criteria with (QC1, QC2), criteria with (QC1, QC2, QC3) etc.,
type TierEvaluator interface {
	// Evaluate evaluates tier for an actor based on given tier criteria
	Evaluate(ctx context.Context, actorId string, currentTier tieringEnumPb.Tier, tierCriteria *tieringPb.TierCriteria, opts ...options.EvaluatorOptions) (*tieringPb.EvaluatorMeta, error)
}

type TierEvaluatorService struct {
	dataCollectorFactory data_collector.DataCollectorFactory
	gconf                *genconf.Config
	segmentationClient   segmentPb.SegmentationServiceClient
}

var _ TierEvaluator = &TierEvaluatorService{}

func NewTierEvaluatorService(
	dataCollectorFactory data_collector.DataCollectorFactory,
	gconf *genconf.Config,
	segmentationClient segmentPb.SegmentationServiceClient,
) *TierEvaluatorService {
	return &TierEvaluatorService{
		dataCollectorFactory: dataCollectorFactory,
		gconf:                gconf,
		segmentationClient:   segmentationClient,
	}
}

// eligibleTier is a struct to hold eligible tier and its options
type eligibleTier struct {
	tier        tieringEnumPb.Tier
	optionTypes []tieringEnumPb.CriteriaOptionType
}

// Evaluate evaluates all criteria with QC combination as (Balance, Kyc, Salary)
// returns the highest possible tier if user satisfies multiple tiers
func (s *TierEvaluatorService) Evaluate(ctx context.Context, actorId string, currentTier tieringEnumPb.Tier, tierCriteria *tieringPb.TierCriteria,
	opts ...options.EvaluatorOptions) (*tieringPb.EvaluatorMeta, error) {
	evaluatorParams := &tieringPb.EvaluateTierForActorRequest_Options{}
	for _, opt := range opts {
		opt(evaluatorParams)
	}
	gatherDataResp, gatherDataErr := s.gatherData(ctx, actorId, evaluatorParams)
	if gatherDataErr != nil {
		return nil, errors.Wrap(gatherDataErr, "error in gathering data")
	}
	var eligibleTiers []*eligibleTier
	// iterate over all tiers and check if any of them are eligible
	for _, tierDetail := range tierCriteria.GetDetails().GetMovementDetailsList() {
		tier := tierDetail.GetTierName()
		eligibleOptions := make([]tieringEnumPb.CriteriaOptionType, 0)
		for _, opt := range tierDetail.GetOptions() {
			isPassed, evaluateErr := s.evaluateOption(opt, tier, currentTier, gatherDataResp, evaluatorParams.GetToEvalForMultipleWays())
			if evaluateErr != nil {
				return nil, errors.Wrap(evaluateErr, "error evaluating data")
			}
			if isPassed {
				logger.Debug(ctx, "eligible for tier with option", zap.Any("tier", tier), zap.Any("option", opt.GetCriteriaOptionType()))
				eligibleOptions = append(eligibleOptions, opt.GetCriteriaOptionType())
			}
		}
		if len(eligibleOptions) > 0 {
			eligibleTiers = append(eligibleTiers, &eligibleTier{
				tier:        tier,
				optionTypes: eligibleOptions,
			})
		}
	}
	// Filter if we need to evaluate for multiple ways(new criteria)
	if evaluatorParams.GetToEvalForMultipleWays() {
		eligibleTiers = filterTiers(eligibleTiers)
	}
	// handle no eligible tiers case
	numOfEligibleTiers := len(eligibleTiers)
	if numOfEligibleTiers == 0 {
		return nil, tieringErrors.ErrNoEligibleTiersForActor
	}
	// return highest possible eligible tier
	sort.Slice(eligibleTiers, func(i, j int) bool {
		return eligibleTiers[i].tier.Number() < eligibleTiers[j].tier.Number()
	})
	finalEligibleTier := eligibleTiers[numOfEligibleTiers-1]
	return &tieringPb.EvaluatorMeta{
		EvaluatedTier:                  finalEligibleTier.tier,
		HighPriorityCriteriaOptionType: findHighestPriorityOption(finalEligibleTier.optionTypes),
		SatisfiedCriteriaOptionTypes:   finalEligibleTier.optionTypes,
	}, nil
}

func filterTiers(eligibleTiers []*eligibleTier) []*eligibleTier {
	filteredTiers := make([]*eligibleTier, 0)
	for _, t := range eligibleTiers {
		if !lo.Contains(ignoreTiersInNewCriteria, t.tier) {
			filteredTiers = append(filteredTiers, t)
		}
	}
	return filteredTiers
}

func findHighestPriorityOption(optionTypes []tieringEnumPb.CriteriaOptionType) tieringEnumPb.CriteriaOptionType {
	// Shouldn't happen ideally
	if len(optionTypes) == 0 {
		return tieringEnumPb.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED
	}
	// default to first option
	mostPriorityOption := optionTypes[0]
	highestPriority := int(^uint(0) >> 1) // Max int value
	for _, option := range optionTypes {
		if priority, exists := optionsPriority[option]; exists && priority < highestPriority {
			highestPriority = priority
			mostPriorityOption = option
		}
	}
	return mostPriorityOption
}

// gatherData makes parallel calls to balance, kyc and salary data collectors
func (s *TierEvaluatorService) gatherData(ctx context.Context, actorId string, evaluatorParams *tieringPb.EvaluateTierForActorRequest_Options) (*tieringPb.GatherDataResponse, error) {
	// Get implementation of each data collector
	balanceDataCollector, balanceDataCollectorErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_BALANCE)
	kycDataCollector, kycDataCollectorErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_KYC)
	salaryDataCollector, salaryDataCollectorErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_SALARY)
	baseTierDataCollector, baseTierDataCollectorErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_BASE_TIER)
	usstocksDataCollector, ussstocksDataCollectrErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_US_STOCKS_WALLET_ADD_FUNDS)
	depositsDataCollector, depositsDataCollectorErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_DEPOSITS)
	// if any one of the data collector is not implemented, we can't proceed further
	if balanceDataCollectorErr != nil || kycDataCollectorErr != nil || salaryDataCollectorErr != nil || baseTierDataCollectorErr != nil || ussstocksDataCollectrErr != nil || depositsDataCollectorErr != nil {
		return nil, helper.HandleWrappingErrors(
			errors.Wrap(balanceDataCollectorErr, "balance data collector error:"),
			errors.Wrap(kycDataCollectorErr, "kyc data collector error:"),
			errors.Wrap(salaryDataCollectorErr, "salary data collector error:"),
			errors.Wrap(baseTierDataCollectorErr, "base tier data collector error:"),
			errors.Wrap(ussstocksDataCollectrErr, "usstocks data collector error:"),
			errors.Wrap(depositsDataCollectorErr, "deposits data collector error:"),
		)
	}
	// Balance Data Collection
	gatherDataErrGrp, _ := errgroup.WithContext(ctx)
	var balanceData *tieringPb.BalanceData
	gatherDataErrGrp.Go(func() error {
		var dataCollectorOptionsList []dataCollectorOptions.DataCollectorOptions
		if evaluatorParams.GetToEvalOnRealTimeData() {
			dataCollectorOptionsList = append(dataCollectorOptionsList, dataCollectorOptions.WithBalanceRealtime())
		}
		if evaluatorParams.GetToSkipAppAccessCheck() {
			dataCollectorOptionsList = append(dataCollectorOptionsList, dataCollectorOptions.WithSkipAppAccessCheck())
		}

		balanceDataUnscoped, balanceDataErr := balanceDataCollector.CollectData(ctx, actorId, dataCollectorOptionsList...)
		if balanceDataErr != nil {
			return errors.Wrap(balanceDataErr, "error collecting balance data")
		}
		balanceData = balanceDataUnscoped.GetBalanceData()
		return nil
	})
	// Kyc Data Collection
	var kycData *tieringPb.KycData
	gatherDataErrGrp.Go(func() error {
		kycDataUnscoped, kycDataErr := kycDataCollector.CollectData(ctx, actorId)
		if kycDataErr != nil {
			return errors.Wrap(kycDataErr, "error collecting kyc data")
		}
		kycData = kycDataUnscoped.GetKycData()
		return nil
	})
	// Salary Data Collection
	var salaryData *tieringPb.SalaryData
	gatherDataErrGrp.Go(func() error {
		salaryDataUnscoped, salaryDataErr := salaryDataCollector.CollectData(ctx, actorId)
		if salaryDataErr != nil {
			return errors.Wrap(salaryDataErr, "error collecting salary data")
		}
		salaryData = salaryDataUnscoped.GetSalaryData()
		return nil
	})
	// base tier data collection
	var baseTierData *tieringPb.BaseTierData
	gatherDataErrGrp.Go(func() error {
		baseTierDataUnscoped, baseTierDataErr := baseTierDataCollector.CollectData(ctx, actorId)
		if baseTierDataErr != nil {
			return errors.Wrap(baseTierDataErr, "error collecting base tier data")
		}
		baseTierData = baseTierDataUnscoped.GetBaseTierData()
		return nil
	})
	// usstocks data collection
	var usstocksData *tieringPb.UsStocksData
	gatherDataErrGrp.Go(func() error {
		usstocksDataUnscoped, usstocksDataErr := usstocksDataCollector.CollectData(ctx, actorId)
		if usstocksDataErr != nil {
			return errors.Wrap(usstocksDataErr, "error collecting usstocks data")
		}
		usstocksData = usstocksDataUnscoped.GetUsStocksData()
		return nil
	})
	// deposits data collection
	var depositsData *tieringPb.DepositsData
	gatherDataErrGrp.Go(func() error {
		depositsDataUnscoped, depositsDataErr := depositsDataCollector.CollectData(ctx, actorId)
		if depositsDataErr != nil {
			return errors.Wrap(depositsDataErr, "error collecting deposits data")
		}
		depositsData = depositsDataUnscoped.GetDepositsData()
		return nil
	})

	// segment data collection
	var segmentData *tieringPb.SegmentData
	gatherDataErrGrp.Go(func() error {
		segmentData = &tieringPb.SegmentData{}

		// Get all excluded segment IDs for all criteria
		var allExcludedSegments []string
		if s.gconf.CriteriaSegmentExclusions() != nil {
			salaryB2CSegments := s.gconf.CriteriaSegmentExclusions().SalaryB2CExcludedSegments().ToStringArray()
			aaSalarySegments := s.gconf.CriteriaSegmentExclusions().AaSalaryExcludedSegments().ToStringArray()
			allExcludedSegments = append(allExcludedSegments, salaryB2CSegments...)
			allExcludedSegments = append(allExcludedSegments, aaSalarySegments...)
		}

		if len(allExcludedSegments) > 0 {
			isMemberResp, isMemberErr := s.segmentationClient.IsMember(ctx, &segmentPb.IsMemberRequest{
				ActorId:    actorId,
				SegmentIds: allExcludedSegments,
			})
			if epifigrpc.RPCError(isMemberResp, isMemberErr) != nil {
				return errors.Wrap(epifigrpc.RPCError(isMemberResp, isMemberErr), "error checking segment membership")
			}

			// Check if actor should be excluded for each criteria type
			if s.gconf.CriteriaSegmentExclusions() != nil {
				// Check SALARY_B2C exclusion
				salaryB2CSegments := s.gconf.CriteriaSegmentExclusions().SalaryB2CExcludedSegments().ToStringArray()
				for _, segmentId := range salaryB2CSegments {
					if isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
						segmentData.ShouldExcludeSalaryB2CCriteria = true
						break
					}
				}

				// Check AA_SALARY exclusion
				aaSalarySegments := s.gconf.CriteriaSegmentExclusions().AaSalaryExcludedSegments().ToStringArray()
				for _, segmentId := range aaSalarySegments {
					if isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
						segmentData.ShouldExcludeAaSalaryCriteria = true
						break
					}
				}
			}
		}

		return nil
	})

	gatherDataErr := gatherDataErrGrp.Wait()
	if gatherDataErr != nil {
		return nil, gatherDataErr
	}
	return &tieringPb.GatherDataResponse{
		BalanceData:  balanceData,
		KycData:      kycData,
		SalaryData:   salaryData,
		BaseTierData: baseTierData,
		UsStocksData: usstocksData,
		DepositsData: depositsData,
		SegmentData:  segmentData,
	}, nil
}

func (s *TierEvaluatorService) evaluateOption(opt *criteriaPb.Option, evaluationTier tieringEnumPb.Tier,
	currentTier tieringEnumPb.Tier, gatherDataResp *tieringPb.GatherDataResponse, toEvaluateForMultipleWays bool) (bool, error) {
	for _, action := range opt.GetActions() {
		qc := action.GetActionDetails()
		var isActionPassed bool
		switch qc.GetCriteria().(type) {
		case *criteriaPb.QualifyingCriteria_Balance:
			// skip balance evaluation for Prime 3% band if multiple ways evaluation is disabled
			if evaluationTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_NINETY && !toEvaluateForMultipleWays {
				isActionPassed = false
				break
			}
			isActionPassed = helper.EvaluateBalanceData(qc.GetBalance(), gatherDataResp.GetBalanceData())
		case *criteriaPb.QualifyingCriteria_Kyc:
			isActionPassed = helper.EvaluateKycData(qc.GetKyc(), gatherDataResp.GetKycData())
		case *criteriaPb.QualifyingCriteria_Salary:
			shouldExcludeAaSalaryCriteria := gatherDataResp.GetSegmentData().GetShouldExcludeAaSalaryCriteria()
			shouldExcludeSalB2bCriteria := gatherDataResp.GetSegmentData().GetShouldExcludeSalaryB2CCriteria()
			isActionPassed = helper.EvaluateSalaryData(qc.GetSalary(), gatherDataResp.GetSalaryData(), toEvaluateForMultipleWays, evaluationTier, shouldExcludeSalB2bCriteria, shouldExcludeAaSalaryCriteria)
		case *criteriaPb.QualifyingCriteria_BalanceV2:
			// skip balance evaluation for Prime 3% band if multiple ways evaluation is disabled
			if evaluationTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_NINETY && !toEvaluateForMultipleWays {
				isActionPassed = false
				break
			}
			isActionPassed = helper.EvaluateBalanceDataV2(qc.GetBalanceV2(), gatherDataResp.GetBalanceData(), evaluationTier, currentTier)
		case *criteriaPb.QualifyingCriteria_BaseTier:
			isActionPassed = helper.EvaluateBaseTierData(qc.GetBaseTier(), gatherDataResp.GetBaseTierData())
		case *criteriaPb.QualifyingCriteria_UsStocksSip:
			// ignore this option for old criteria
			isActionPassed = toEvaluateForMultipleWays && helper.EvaluateUsStockSipData(qc.GetUsStocksSip(), gatherDataResp.GetUsStocksData())
		case *criteriaPb.QualifyingCriteria_Deposits:
			// ignore this option for old criteria
			isActionPassed = toEvaluateForMultipleWays && helper.EvaluateDepositsData(qc.GetDeposits(), gatherDataResp.GetDepositsData())
		case *criteriaPb.QualifyingCriteria_AaSalary:
			// ignore this option for old criteria
			shouldExcludeAaSalaryCriteria := gatherDataResp.GetSegmentData().GetShouldExcludeAaSalaryCriteria()
			isActionPassed = toEvaluateForMultipleWays && helper.EvaluateAaSalaryData(qc.GetAaSalary(), gatherDataResp.GetSalaryData(), shouldExcludeAaSalaryCriteria)
		default:
			return false, fmt.Errorf("evaluation criteria type not supported: %s", qc.GetCriteria())
		}
		// no action should fail
		if !isActionPassed {
			return false, nil
		}
	}
	return true, nil
}
