//go:generate mockgen -source=factory.go -destination=../test/mocks/mock_data_collector_factory.go -package=mocks
package data_collector

import (
	"fmt"

	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/tiering/data_collector/collectors"
)

type DataCollectorFactory interface {
	GetImpl(qcType tieringEnumPb.QualifyingCriteriaType) (DataCollector, error)
}

type DataCollectorFactorySvc struct {
	balanceDataCollector  *collectors.BalanceDataCollector
	kycDataCollector      *collectors.KycDataCollector
	salaryDataCollector   *collectors.SalaryDataCollector
	baseTierDataCollector *collectors.BaseTierDataCollector
	usStocksDataCollector *collectors.UsStocksDataCollector
	depositsDataCollector *collectors.DepositsDataCollector
}

func NewDataCollectorFactorySvc(
	balanceDataCollector *collectors.BalanceDataCollector,
	kycDataCollector *collectors.KycDataCollector,
	salaryDataCollector *collectors.SalaryDataCollector,
	baseTierDataCollector *collectors.BaseTierDataCollector,
	usStocksDataCollector *collectors.UsStocksDataCollector,
	depositsDataCollector *collectors.DepositsDataCollector,
) *DataCollectorFactorySvc {
	return &DataCollectorFactorySvc{
		balanceDataCollector:  balanceDataCollector,
		kycDataCollector:      kycDataCollector,
		salaryDataCollector:   salaryDataCollector,
		baseTierDataCollector: baseTierDataCollector,
		usStocksDataCollector: usStocksDataCollector,
		depositsDataCollector: depositsDataCollector,
	}
}

func (d *DataCollectorFactorySvc) GetImpl(qcType tieringEnumPb.QualifyingCriteriaType) (DataCollector, error) {
	switch qcType {
	case tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_BALANCE:
		return d.balanceDataCollector, nil
	case tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_KYC:
		return d.kycDataCollector, nil
	case tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_SALARY:
		return d.salaryDataCollector, nil
	case tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_BASE_TIER:
		return d.baseTierDataCollector, nil
	case tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_US_STOCKS_WALLET_ADD_FUNDS:
		return d.usStocksDataCollector, nil
	case tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_DEPOSITS:
		return d.depositsDataCollector, nil
	default:
		return nil, fmt.Errorf("data collector for %s QC not implemented yet", qcType.String())
	}
}
