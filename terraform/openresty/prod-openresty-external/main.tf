terraform {
  backend "s3" {
    # credentials are "implicit" and not configured here
    bucket = "epifi-prod-terraform-backend"
    key    = "ec2-systems/openresty/prod-openresty-external/terraform.tfstate"
    region = "ap-south-1"
  }
}

data "aws_lb_target_group" "prod_openresty_ext_tg" {
  arn  = "arn:aws:elasticloadbalancing:ap-south-1:************:targetgroup/prod-openresty/32e38a46af7c8443"
}

resource "aws_lb_target_group_attachment" "prod_openresty_ext_tg_attach" {
  target_group_arn = data.aws_lb_target_group.prod_openresty_ext_tg.arn
  target_id        = module.prod_openresty_ext.ec2_instance_id
  port             = 443
}

module "prod_openresty_ext" {

  source = "../../modules/openresty"

  env            = "prod"
  name_id        = "ext"
  aws_account_id = ************

  spec = {

    ami           = "ami-09025b1b22873a46d"
    instance_type = "t3a.small"

    root_volume_size = 64

    subnet_id = "subnet-058b134f1a3e29899"
    # security groups managed outside terraform currently (go-infra)
    vpc_security_group_ids = [
      "sg-0831a06031ad41410" # name: prod-openresty-external
    ]
    secrets = [
      "prod/openresty/ext/tls",
      "prod/openresty/ext/client-conf"
    ]
    iam_perm_policies = [
      # outside the management scope of this module (used by multiple entities)
      "ssm_s3_logging_policy"
    ]

    ec2_user_data = file("./ec2_user_data.sh")

  }

}