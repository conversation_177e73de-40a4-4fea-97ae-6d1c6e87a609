# Refers requirements from: https://github.com/epiFi/gamma/tree/master/cmd/cx

terraform {
  backend "s3" {
    # Pass configuration from file through command line
  }
}

module "cx" {
  source                  = "../../../modules/grpc-service/v3/deployment"
  image_id                = var.image_id
  min                     = var.min
  max                     = var.max
  desired                 = var.desired
  tenant                  = var.tenant
  env                     = var.env
  region                  = "ap-south-1"
  service_name            = "cx"
  target_port             = [9508, 9785]
  aws_vpc                 = var.aws_vpc
  protocol                = ["HTTPS", "HTTPS"]
  protocol_version        = ["GRPC", "HTTP1"]
  instance_profile_name   = "${var.env}-cx"
  volume_size             = 20
  ec2_instance_type       = "t3a.medium"
  fallback_instance_types = ["t3a.large", "m5a.large", "c5a.large"]
  lb_name                 = "${var.env}-private-backend"
  swap                    = var.swap
  clean                   = var.clean
  current_color           = var.current_color
  subnets                 = var.private_subnets
  env_wildcard_cert_arn   = var.env_wildcard_cert_arn
  pointz_zone_id          = var.pointz_zone_id
  private_hosted_zone_id  = var.private_hosted_zone_id
  private_env_zone_id     = var.private_env_zone_id
  dns                     = var.dns
  active_launch_template  = var.active_launch_template
  warm_pool_min           = var.warm_pool_min
  health_protocol         = ["HTTPS", "HTTPS"]
  listener_port           = [9508, 9785]
  health_check_port       = 9785
  health_check_path       = "/_health"

  # Tagging variables
  bu-name             = "cx"
  service             = "cx"
  compliance          = ""
  data-classification = ""
  feature             = ""
}
