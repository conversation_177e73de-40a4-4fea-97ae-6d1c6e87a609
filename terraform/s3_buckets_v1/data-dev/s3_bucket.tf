module "epifi-p2pinvestment-liquiloans-raw-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-p2pinvestment-liquiloans-raw-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}
module "epifi-p2pinvestment-liquiloans-ds-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-p2pinvestment-liquiloans-ds-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-p2pinvestment-liquiloans-dna-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-p2pinvestment-liquiloans-dna-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}
module "epifi-p2pinvestment-liquiloans-sf-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-p2pinvestment-liquiloans-sf-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-p2pinvestment-liquiloans-dp-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-p2pinvestment-liquiloans-dp-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}
module "epifi-usstocks-alpaca-raw-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-usstocks-alpaca-raw-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-usstocks-alpaca-dp-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-usstocks-alpaca-dp-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-usstocks-alpaca-ds-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-usstocks-alpaca-ds-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-usstocks-alpaca-sf-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-usstocks-alpaca-sf-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-usstocks-alpaca-dna-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-usstocks-alpaca-dna-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-credit-card-raw-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-credit-card-raw-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-credit-card-dp-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-credit-card-dp-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}
module "epifi-credit-card-ds-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-credit-card-ds-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-credit-card-sf-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-credit-card-sf-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-credit-card-dna-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-credit-card-dna-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-wealth-data-services-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-wealth-data-services-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-dev-dp-resources" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-dev-dp-resources"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-dev-dp-resources/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

  lifecycle_rule_inputs = [
    {
        id      = "logs cleanup policy"
        prefix  = "logs/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 7
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
}
module "epifi-dna-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

lifecycle_rule_inputs = [
    {
        id      = "uat data cleanup policy"
        prefix  = "uat/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 7
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
}
module "epifi-dp-dev" {
  service                          = "risk"
  bu-name                          = "user-risk"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy  = [
    {
      sid     = "Staging"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::epifi-dp-dev",
        "arn:aws:s3:::epifi-dp-dev/*",
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::epifi-dp-dev",
        "arn:aws:s3:::epifi-dp-dev/*",
      ]
    },
    {
      sid     = "UAT"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::epifi-dp-dev",
        "arn:aws:s3:::epifi-dp-dev/*",
      ]
    }
  ]
lifecycle_rule_inputs = [
    {
        id      = "uat data cleanup policy"
        prefix  = "uat/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 7
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
  s3_event_queues = [
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:dev-risk-batch-rule-engine-event-queue"
      "path" : "qa/data/automated_report/risk"
    },
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:dev-dc-amc-charges-event-queue"
      "path" : "staging/data/automated_report/debit_card"
    },
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:dev-dc-amc-charges-event-queue"
      "path" : "qa/data/automated_report/debit_card"
    }
  ]
}
module "epifi-ds-dev" {
  service                          = "risk"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

 lifecycle_rule_inputs = [
    {
        id      = "events cleanup policy"
        prefix  = "qa/data/event/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 90
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    },
    {
        id      = "entity wormhole data cleanup policy"
        prefix  = "qa/data/entity/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 30
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    },
    {
        id      = "uat data cleanup policy"
        prefix  = "uat/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 10
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
}
module "epifi-ozonetel-transcription" {
  service                          = "cx"
  bu-name                          = "cx"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-ozonetel-transcription"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-ozonetel-transcription/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "UAT"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Deploy"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Demo"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Vendor Staging"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::432457413665:root"]
        }
      ]
    },
    {
      sid     = "Staging"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-raw-dev" {
  bu-name                          = "horizontal"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  s3_event_queues = [
    {
      "queue_arn": "arn:aws:sqs:ap-south-1:************:dev-dp-experian-gcp-queue"
      "path": "qa/data/vendor/experian"
    }
  ]

  s3_event_topics = [
    {
      "topic_arn" : "arn:aws:sns:ap-south-1:************:staging-segment-export-s3-part-file-creation-event-v2-topic"
      "path" : "staging/data/vendor/segmentation_service/"
    },
    {
      "topic_arn" : "arn:aws:sns:ap-south-1:************:qa-segment-export-s3-part-file-creation-event-v2-topic"
      "path" : "qa/data/vendor/segmentation_service/"
    }
  ]

  lifecycle_rule_inputs = [
    {
        id      = "events cleanup policy"
        prefix  = "qa/data/event/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 60
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    },
    {
        id      = "entity sqoop data cleanup policy"
        prefix  = "qa/data/entity/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 10
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    },
    {
        id      = "uat data cleanup policy"
        prefix  = "uat/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 10
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
}

module "epifi-sf-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

lifecycle_rule_inputs = [
    {
        id      = "uat data cleanup policy"
        prefix  = "uat/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 7
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
}
module "fi-marketing-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "fi-marketing-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "fi-marketing-dev/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 180
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "sagemaker-ap-south-1-************" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "sagemaker-ap-south-1-************"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "sagemaker-ap-south-1-************/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "stage-epifi-ds" {
  bu-name                          = "horizontal"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "stage-epifi-ds"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "stage-epifi-ds/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid = "RODataProd"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/liveness/*",
        "arn:aws:s3:::stage-epifi-ds/merchant_service/*",
      ]
    },
    {
      sid     = "UAT"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid     = "Deploy"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid     = "Demo"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid     = "Vendor Staging"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::432457413665:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid     = "Staging"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid = "Demo-jenkins"
      actions = [
        "s3:GetObject",
        "s3:GetObjectVersion",
        "s3:GetObjectAcl",
        "s3:ListBucket",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid = "UAT-jenkins"
      actions = [
        "s3:GetObject",
        "s3:GetObjectVersion",
        "s3:GetObjectAcl",
        "s3:ListBucket",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid = "Staging-jenkins"
      actions = [
        "s3:GetObject",
        "s3:GetObjectVersion",
        "s3:GetObjectAcl",
        "s3:ListBucket",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    },
    {
      sid = "QA-jenkins"
      actions = [
        "s3:GetObject",
        "s3:GetObjectVersion",
        "s3:GetObjectAcl",
        "s3:ListBucket",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:role/role_decrypt_deploy"]
        }
      ]
      resources = [
        "arn:aws:s3:::stage-epifi-ds",
        "arn:aws:s3:::stage-epifi-ds/*",
      ]
    }
  ]

}
module "epifi-data-rewards-dev" {
  service                          = "segment"
  bu-name                          = "growth-infra"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-data-rewards-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-data-rewards-dev/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type = "AWS"
          identifiers = [
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root"
          ]
        }
      ]
    }
  ]

}
module "epifi-federal-raw-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-federal-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-federal-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 180
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-federal-ds-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-federal-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-federal-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-federal-dna-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-federal-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-federal-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-federal-sf-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-federal-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-federal-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-federal-dp-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-federal-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-federal-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "data-ansible-output-bucket" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "data-ansible-output-bucket"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "epifi-data-sandbox-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-data-sandbox-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "epifi-risk-ops-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-risk-ops-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-risk-ops-dev/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "epifi-startree-pinot-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-startree-pinot-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-startree-pinot-dev/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid = "Statement1"
      actions = [
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetObject",
        "s3:GetBucketLocation",
        "s3:GetBucketAcl"
      ]
      principals = [
        {
          type = "AWS"
          identifiers = [
            "arn:aws:iam::065793575545:role/sc-fi-saas-dev20220307183630739600000009",
            "arn:aws:iam::065793575545:role/sc-fi-saas-dev20220307182640134200000001"
          ]
        }
      ]
    }
  ]

}
module "epifi-wealth-dp-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-wealth-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-wealth-dp-dev/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = []
}
module "epifi-wealth-ds-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-wealth-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-wealth-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "epifi-wealth-raw-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-wealth-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-wealth-raw-dev/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

  lifecycle_rule_inputs = [
    {
        id      = "events cleanup policy"
        prefix  = "qa/data/event/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 90
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    },
    {
        id      = "entity sqoop data cleanup policy"
        prefix  = "qa/data/entity/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 30
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
}
module "epifi-wealth-sf-dev" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-wealth-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-wealth-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "stage-epifi-data-services" {
  service                          = "segment"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "stage-epifi-data-services"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "stage-epifi-data-services/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 180
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

}
module "epifi-data-services-dev" {
  service                          = "categorizer"
  bu-name                          = "demystifi"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-data-services-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-data-services-dev/"
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  s3_event_queues = [
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:staging-risk-redlist-update-queue"
      "path" : "onboarding-risk-detection/negative_db/staging"
    },
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:qa-risk-redlist-update-queue"
      "path" : "onboarding-risk-detection/negative_db/qa"
    },
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:demo-risk-redlist-update-queue"
      "path" : "onboarding-risk-detection/negative_db/demo"
    },
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:staging-categorizer-crowd-aggregation-category-update-queue"
      "path" : "transaction_categoriser/recat_crowd_aggregation/recat_results"
    },
    {
      "queue_arn" : "arn:aws:sqs:ap-south-1:************:qa-recurring-txns-ds-file-upload-events-queue"
      "path" : "subscriptions_recurring_txns/active_subscriptions.json"
    }
  ]
  policy = [
    {
      sid     = "Deploy"
      actions = ["s3:*"]
      principals = [
        {
          type = "AWS"
          identifiers = [
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root",
            "arn:aws:iam::************:root"
          ]
        }
      ]
    }
  ]

}
module "epifi-data-dev-s3-logs" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-data-dev-s3-logs"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-data-dev-s3-logs/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 7
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid       = "S3PolicyStmt-DO-NOT-MODIFY-1663127969743"
      actions   = ["s3:PutObject"]
      resources = ["arn:aws:s3:::epifi-data-dev-s3-logs/*"]
      principals = [
        {
          type        = "Service"
          identifiers = ["logging.s3.amazonaws.com"]
        }
      ]
    }
  ]

}
module "epifi-moengage-vendor-data-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-moengage-vendor-data-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-moengage-vendor-data-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy                           = [
    {
      sid        = "BucketPolicyForAccountsWrite"
      actions    = ["s3:PutObject"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:role/service-role/core_s3_export-role-e1qw8ulf"]
        }
      ]
    }
  ]

}
module "epifi-frm-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-frm-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 180
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
}
module "epifi-frm-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-frm-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = []
}
module "epifi-frm-ds-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-frm-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
}
module "epifi-frm-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-frm-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = false
  logging_enabled                  = false
 server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
}


module "epifi-finance-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-finance-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
}

module "epifi-pl-liquiloans-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-liquiloans-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-liquiloans-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-liquiloans-ds-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-liquiloans-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-liquiloans-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-liquiloans-dna-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-liquiloans-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-liquiloans-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-liquiloans-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-liquiloans-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-liquiloans-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-liquiloans-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-liquiloans-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-liquiloans-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-ds-eda-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-ds-eda-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 30
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"


}

module "epifi-ds-serving-artifacts-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-ds-serving-artifacts-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"


}

module "epifi-ds-datasets-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-ds-datasets-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 1825
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"


}

module "epifi-ds-sagemaker-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-ds-sagemaker-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = false
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 30
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"


}

module "epifi-pl-idfc-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-idfc-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-idfc-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-idfc-ds-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-idfc-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-idfc-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-idfc-dna-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-idfc-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-idfc-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-idfc-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-idfc-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-idfc-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-pl-idfc-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-pl-idfc-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-pl-idfc-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-fiftyfin-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fiftyfin-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-fiftyfin-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-fiftyfin-ds-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fiftyfin-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-fiftyfin-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-fiftyfin-dna-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fiftyfin-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-fiftyfin-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-fiftyfin-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fiftyfin-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-fiftyfin-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-fiftyfin-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fiftyfin-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-fiftyfin-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-abfl-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-abfl-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-abfl-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-abfl-ds-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-abfl-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-abfl-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-abfl-dna-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-abfl-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-abfl-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-abfl-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-abfl-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-abfl-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-abfl-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-abfl-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-abfl-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-lenden-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-lenden-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-lenden-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-lenden-ds-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-lenden-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-lenden-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-lenden-dna-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-lenden-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-lenden-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-lenden-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-lenden-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-lenden-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-lenden-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-lenden-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-lenden-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-moneyview-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-moneyview-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-moneyview-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-moneyview-ds-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-moneyview-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-moneyview-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-moneyview-dna-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-moneyview-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-moneyview-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-moneyview-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-moneyview-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-moneyview-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-moneyview-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-moneyview-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-moneyview-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-loanstech-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-loanstech-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-loanstech-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}

module "epifi-loanstech-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-loanstech-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-loanstech-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}

module "epifi-loanstech-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-loanstech-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-loanstech-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}


module "epifi-paisabazaar-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-paisabazaar-raw-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 180
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-paisabazaar-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-paisabazaar-sf-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-paisabazaar-sf-dp" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-paisabazaar-sf-dp"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-data-pl-scrub-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-data-pl-scrub-raw-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 30 #30 days
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root", "arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root", "arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-fi-brand-raw-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fi-brand-raw-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 180
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-fi-brand-dp-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fi-brand-dp-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}

module "epifi-fi-brand-sf-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-fi-brand-sf-dev"
  block_public_access              = true
  acl_enabled                      = false
  lifecycle_enabled                = true
  onwership_enabled                = true
  logging_enabled                  = false
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = 3650
  server_side_encryption_bucketkey = "false"

  object_ownership = "BucketOwnerEnforced"
  policy = [
    {
      sid = "BucketPolicyForAccountRead"
      actions = [
        "s3:GetObject",
        "s3:List*",
        "s3:GetObjectVersion",
        "s3:GetBucketAcl",
        "s3:GetBucketLocation",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid = "BucketPolicyForAccountsWrite"
      actions = [
        "s3:PutObject",
        "s3:PutObjectAcl",
      ]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}
module "epifi-scribe-dev" {
  service                          = "categorizer"
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-scribe-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = false
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-scribe-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = false
  s3_object_expiration             = null
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"

 lifecycle_rule_inputs = [
    {
        id      = "events cleanup policy"
        prefix  = "qa/data/event/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 90
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    },
    {
        id      = "entity wormhole data cleanup policy"
        prefix  = "qa/data/entity/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 30
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    },
    {
        id      = "uat data cleanup policy"
        prefix  = "uat/"
        status  = "Enabled"

        expiration_inputs = [{
        days                         = 10
        date                         = null
        expired_object_delete_marker = null
        },
      ] 
        transition_inputs                    = []
        noncurrent_version_transition_inputs = []
        noncurrent_version_expiration_inputs = []
    }
  ]
}

module "epifi-stockguardian-raw-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-ds-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-ds-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-ds-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-dna-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-dna-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-dna-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-sf-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-dp-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}


module "epifi-stockguardian-lsp-raw-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-lsp-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-lsp-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-lsp-sf-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-lsp-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-lsp-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-lsp-dp-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-lsp-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-lsp-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}

module "epifi-stockguardian-tsp-raw-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-tsp-raw-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-tsp-raw-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 120
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "QA"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    },
    {
      sid     = "Data-dev"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-tsp-sf-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-tsp-sf-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-tsp-sf-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]

}
module "epifi-stockguardian-tsp-dp-dev" {
  source                           = "../../modules/s3_buckets/v1"
  s3_bucket_name                   = "epifi-stockguardian-tsp-dp-dev"
  block_public_access              = false
  acl_enabled                      = false
  lifecycle_enabled                = false
  onwership_enabled                = false
  logging_enabled                  = true
  s3_logging_bucket_name           = "epifi-data-dev-s3-logs"
  log_prefix                       = "epifi-stockguardian-tsp-dp-dev/"
  server_side_encryption_enabled   = true
  mfa_delete                       = "Disabled"
  s3_object_expiration             = 365
  server_side_encryption_bucketkey = "false"
  request_payer                    = "BucketOwner"
  object_ownership                 = "BucketOwnerEnforced"
  policy = [
    {
      sid     = "OtherAccountAccess"
      actions = ["s3:*"]
      principals = [
        {
          type        = "AWS"
          identifiers = ["arn:aws:iam::************:root"]
        }
      ]
    }
  ]
}
