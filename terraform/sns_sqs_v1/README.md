# SQS SNS

**Location of Module** - [SNS_SQS](https://github.com/epiFi/infra/tree/master/terraform/modules/sns_sqs/v1)

** To establish Many to One DLQ relation in queues
lets take an example to understand what all you need to do in a scenario where you want to provision 3queues that point to 1dlq, then in that case create a queue module for 1queue and its dlq(1:1) as we create normally and then in rest2 of the queues modules this same dlq do the following:
- set ```use_existing_dlq``` variable
- add ```dlq_name=name-of-dlq-queue``` without any prefix or suffix(just the name)
- add ```depends_on=[module.<name-of-first-module-where-dlq-was-created>]``` like this-> [module.card-pi-creation-queue]

For creation of new queue, sns topic or their attachments, update main.tf file in respective env folders adding snippet with required parameters. And, then update the entry in list_queues_topics file.

### When env=non-prod:
To apply changes, Set you profile accordingly.
Run following cmds:
```
AWS_PROFILE=default-mfa terraform init -backend-config=s3-backend-qa.conf
AWS_PROFILE=default-mfa terraform plan -var-file=../../env/qa.tf
AWS_PROFILE=default-mfa terraform apply -var-file=../../env/qa.tf
```

### When env=prod:
Profile is already set inside jenkins server.
```
terraform init -backend-config=s3-backend-qa.conf
terraform plan -var-file=../../env/qa.tf
terraform apply -var-file=../../env/qa.tf
```

## Note
For all non-prod envs, devops can run apply from local, but for prod we need to run it from a jenkins server in prod account. And before doing that run fetch-infra job to pull latest master/your branch code. As of yet, we run these commands as mentioned above, manually! Plan is to automate this process of creation/updation of sqs,sns.
