########################### CPU ALARMS BLUE ###########################

resource "aws_cloudwatch_metric_alarm" "blue-asg-cpu-high" {

  count             = var.env == "prod" ? 1 : var.enable_autoscaling_alarms ? 1 : 0
  alarm_name        = "${var.env}-${var.service_name}-cpu-blue-scale-up"
  alarm_description = "This metric monitors ec2 cpu for high utilization"

  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2"

  namespace   = "AWS/EC2"
  period      = "60"
  statistic   = "Average"
  metric_name = "CPUUtilization"

  threshold = var.blue_cpu_scale_up_threshold

  alarm_actions = [
    aws_autoscaling_policy.asg-cpu-policy-blue-scale-up.arn
  ]

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.application-blue-asg.name
  }
}

########################### Memory ALARMS BLUE ###########################

resource "aws_cloudwatch_metric_alarm" "blue-asg-memory-high" {

  count             = var.env == "prod" ? 1 : var.enable_autoscaling_alarms ? 1 : 0
  alarm_name        = "${var.env}-${var.service_name}-memory-blue-scale-up"
  alarm_description = "This metric monitors ec2 memory for high utilization"

  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2"

  metric_name = "mem_used_percent"
  namespace   = "CWAgent"
  period      = "60"
  statistic   = "Average"

  threshold = var.blue_mem_scale_up_threshold

  alarm_actions = [
    aws_autoscaling_policy.asg-memory-policy-blue-scale-up.arn
  ]

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.application-blue-asg.name
    ImageId              = var.image_id
    InstanceType         = var.ec2_instance_type
  }
}

########################### CPU ALARMS GREEN #####################

resource "aws_cloudwatch_metric_alarm" "green-asg-cpu-high" {

  count             = var.env == "prod" ? 1 : var.enable_autoscaling_alarms ? 1 : 0
  alarm_name        = "${var.env}-${var.service_name}-cpu-green-scale-up"
  alarm_description = "This metric monitors ec2 cpu for high utilization"

  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2"

  namespace   = "AWS/EC2"
  period      = "60"
  statistic   = "Average"
  metric_name = "CPUUtilization"

  threshold = var.green_cpu_scale_up_threshold

  alarm_actions = [
    aws_autoscaling_policy.asg-cpu-policy-green-scale-up.arn
  ]

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.application-green-asg.name
  }
}

########################### Memory ALARMS GREEN ###########################

resource "aws_cloudwatch_metric_alarm" "green-asg-memory-high" {

  count             = var.env == "prod" ? 1 : var.enable_autoscaling_alarms ? 1 : 0
  alarm_name        = "${var.env}-${var.service_name}-memory-green-scale-up"
  alarm_description = "This metric monitors ec2 memory for high utilization"

  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2"

  metric_name = "mem_used_percent"
  namespace   = "CWAgent"
  period      = "60"
  statistic   = "Average"

  threshold = var.green_mem_scale_up_threshold

  alarm_actions = [
    aws_autoscaling_policy.asg-memory-policy-green-scale-up.arn
  ]

  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.application-green-asg.name
    ImageId              = var.image_id
    InstanceType         = var.ec2_instance_type
  }
}

########################### CPU & Memory ALARMS BLUE ###########################

resource "aws_cloudwatch_metric_alarm" "blue-asg-cpu-and-memory-low" {
  count                     = var.env == "prod" ? 1 : var.enable_autoscaling_alarms ? 1 : 0
  alarm_name                = "${var.env}-${var.service_name}-cpu-and-memory-blue-scale-down"
  alarm_description         = "This metric monitors ec2 cpu and memory for low utilization"
  comparison_operator       = "GreaterThanThreshold"
  threshold                 = 0
  evaluation_periods        = 1

  # TODO(sayan): remove once tested in production
  alarm_actions = [
    aws_autoscaling_policy.asg-cpu-and-memory-policy-blue-scale-down.arn
  ]

  metric_query {
    id          = "e1"
    expression  = "IF (mem_used<${var.blue_mem_scale_down_threshold} AND cpu_utilization<${var.blue_cpu_scale_down_threshold},1,0)"
    label       = "CPUAndMemoryBelowThreshold"
    return_data = "true"
  }

  metric_query {
    id = "mem_used"

    metric {
      metric_name = "mem_used_percent"
      namespace   = "CWAgent"
      period      = "120"
      stat        = "Average"

      dimensions = {
        AutoScalingGroupName = aws_autoscaling_group.application-blue-asg.name
        ImageId              = var.image_id
        InstanceType         = var.ec2_instance_type
      }
    }
  }

  metric_query {
    id = "cpu_utilization"

    metric {
      metric_name = "CPUUtilization"
      namespace   = "AWS/EC2"
      period      = "120"
      stat        = "Average"

      dimensions = {
        AutoScalingGroupName = aws_autoscaling_group.application-blue-asg.name
      }
    }
  }
}

########################### CPU & Memory ALARMS GREEN ###########################

resource "aws_cloudwatch_metric_alarm" "green-asg-cpu-and-memory-low" {
  count                     = var.env == "prod" ? 1 : var.enable_autoscaling_alarms ? 1 : 0
  alarm_name                = "${var.env}-${var.service_name}-cpu-and-memory-green-scale-down"
  alarm_description         = "This metric monitors ec2 cpu and memory for low utilization"
  comparison_operator       = "GreaterThanThreshold"
  threshold                 = 0
  evaluation_periods        = 1

  # TODO(sayan): remove once tested in production
  alarm_actions = [
    aws_autoscaling_policy.asg-cpu-and-memory-policy-green-scale-down.arn
  ]

  metric_query {
    id          = "e1"
    expression  = "IF (mem_used<${var.green_mem_scale_down_threshold} AND cpu_utilization<${var.green_cpu_scale_down_threshold},1,0)"
    label       = "CPUAndMemoryBelowThreshold"
    return_data = "true"
  }

  metric_query {
    id = "mem_used"

    metric {
      metric_name = "mem_used_percent"
      namespace   = "CWAgent"
      period      = "120"
      stat        = "Average"

      dimensions = {
        AutoScalingGroupName = aws_autoscaling_group.application-green-asg.name
        ImageId              = var.image_id
        InstanceType         = var.ec2_instance_type
      }
    }
  }

  metric_query {
    id = "cpu_utilization"

    metric {
      metric_name = "CPUUtilization"
      namespace   = "AWS/EC2"
      period      = "120"
      stat        = "Average"

      dimensions = {
        AutoScalingGroupName = aws_autoscaling_group.application-green-asg.name
      }
    }
  }
}
