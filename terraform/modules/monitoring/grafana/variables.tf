variable "env" {
  description = "Name of the Environment, e.g. demo, staging, prod etc"
  type        = string
}

variable "region" {
  description = "Name of the region, e.g. ap-south-1, ap-southeast-1 etc"
  type        = string
  default     = "ap-south-1"
}

variable "owner" {
  type        = number
  description = "AWS Account id to use."
}

variable "resource_owner" {
  type        = string
  description = "Owner of the resource."
  default     = "devops+terraform"
}

variable "availability_zone" {
  description = "AZ"
  type        = list(any)
  default     = ["ap-south-1a", "ap-south-1b", "ap-south-1c"]
}

variable "volume_size" {
  description = "The size, in GB, of the root EBS volume."
  type        = number
  default     = 50
}

variable "aws_vpc" {
  type        = string
  description = "The identification of the VPC where we are going to create the resources."
}

variable "vpc_cidr" {
  type        = list(string)
  description = "The CIDR block of the VPC where we are going to create the resources."
}

variable "cidr" {
  type        = list(string)
  description = "The CIDR block of the VPC where we are going to create the resources."
  default     = [""]
}

variable "private_subnets" {
  description = "can reach internet via NAT"
  type        = list(string)
  default     = [""]
}

variable "grafana_count" {
  description = "The number of grafana instances to be launched."
  type        = number
  default     = 1
}

variable "grafana_ami_id" {
  description = "The AMI id of grafana base image generated by packer."
}

variable "grafana_instance_type" {
  description = "EC2 instance type to be used with grafana."
  type        = string
  default     = "t3a.medium"
}

variable "grafana_rds_engine_version" {
  description = "MySQL version of the launched RDS."
  type        = string
  default     = "8.0.28"
}

variable "grafana_rds_instance_type" {
  description = "RDS instance type to be used with grafana RDS."
  type        = string
  default     = "db.t4g.small"
}

variable "grafana_rds_storage" {
  description = "Storage for the MySQL RDS instance."
  type        = number
  default     = 30
}

variable "parameter_group_name" {
  description = "Name of the parameter group for RDS instance."
  type        = string
  default     = "default.mysql8.0"
}

variable "grafana_rds_max_storage" {
  description = "Maximum storage for the MySQL RDS instance."
  type        = number
  default     = 100
}

variable "vpc_endpoint_s3_pl_id" {
  description = "prefix list access"
  type        = list(string)
  default     = []
}

variable "key_name" {
  type    = string
  default = "monitoring"
}

variable "root_volume_size" {
  description = "The size, in GB, of the root EBS volume."
  type        = number
  default     = 50
}

variable "certificate_arn" {
  type        = string
  description = "ARN of the certificate for Grafana endpoint HTTPS. Current is *.pointz.in"
}

variable "grafana_listener_port" {
  description = "Port on which Grafana process listens on"
  type        = number
  default     = "80"
}

variable "backup_retention_period" {
  description = "Retention period for backups, in days"
  type        = number
  default     = "7"
}
