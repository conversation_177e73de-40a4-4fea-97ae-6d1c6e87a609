package model

import (
	"strconv"
	"time"

	guuid "github.com/google/uuid"

	"github.com/epifi/be-common/pkg/nulltypes"
)

type CreditAccountDetail struct {
	// Primary identifier to credit_report_summary table.
	Id string `gorm:"primary_key"`

	// Foreign Key identifier, from credit_reports table
	CreditReportId string
	// Dedup identifier to prevent duplicates
	DedupId string
	// Timestamp of credit report download. Used to determine purged_at.
	CreditReportDownloadedAt time.Time `gorm:"column:credit_report_dowloaded_at"`
	// Actor mapped to credit_report
	ActorId nulltypes.NullString

	ReportId                               string
	ReportDate                             string
	ReportTime                             string
	AccountHolderTypeCode                  string
	AccountNumber                          string
	AccountStatus                          string
	AccountType                            string
	AmountPastDue                          string
	CreditLimitAmount                      string
	CurrencyCode                           string
	CurrentBalance                         string
	DateClosed                             string
	DateOfAddition                         string
	DateOfLastPayment                      string
	DateReported                           string
	HighestCreditOrOriginalLoanAmount      string
	Income                                 string
	IdentificationNumber                   string
	OpenDate                               string
	OriginalChargeOffAmount                string
	PaymentHistoryProfile                  string
	PaymentRating                          string
	PortfolioType                          string
	RepaymentTenure                        string
	ScheduledMonthlyPaymentAmount          string
	SettlementAmount                       string
	SubscriberName                         string
	TermsDuration                          string
	ValueOfCollateral                      string
	ValueOfCreditsLastMonth                string
	WrittenOffAmtPrincipal                 string
	WrittenOffAmtTotal                     string
	TermsFrequency                         string
	SpecialComment                         string
	DateOfFirstDelinquency                 string
	SuitFiledWilfulDefaultWrittenOffStatus string
	SuitFiledWilfulDefault                 string
	CreditFacilityStatus                   string
	TypeOfCollateral                       string
	RateOfInterest                         string
	PromotionalRateFlag                    string
	IncomeIndicator                        string
	IncomeFrequencyIndicator               string
	DefaultStatusDate                      string
	LitigationStatusDate                   string
	WrittenOffStatusDate                   string
	SubscriberComments                     string
	ConsumerComments                       string
	CustomerSegment                        string
	CreditAccountHistories                 []*CreditAccountHistory       `gorm:"foreignKey:CreditAccountDetailsId"`
	CreditAccountHolderDetails             []*CreditAccountHolderDetails `gorm:"foreignKey:CreditAccountDetailsId"`
}

// nolint:funlen
func GetCreditAccountDetails(c *CreditReport) ([]*CreditAccountDetail, error) {
	var creditAccountDetailModels []*CreditAccountDetail
	if c.CreditReportDataRaw == nil {
		return creditAccountDetailModels, nil
	}
	for idx, account := range c.CreditReportDataRaw.GetCreditAccount().GetCreditAccountDetails() {
		highestCreditOrOriginalLoanAmount := account.GetHighestCreditOrOriginalLoanAmount()
		// in case highestCreditOrOriginalLoanAmount has length greater than 14 it means it is a garbage value(as per analytics team) in credit
		// report and we do not need to store it.
		if len(highestCreditOrOriginalLoanAmount) > 14 {
			continue
		}
		dedupId := c.CreditReportDataRaw.GetCreditProfileHeader().GetReportNumber() + "_" + strconv.Itoa(idx)
		model := &CreditAccountDetail{
			Id:                                     guuid.New().String(),
			CreditReportId:                         c.Id,
			DedupId:                                dedupId,
			CreditReportDownloadedAt:               c.CreatedAt,
			ActorId:                                c.ActorId,
			AccountHolderTypeCode:                  account.GetAccountHolderTypeCode(),
			AccountNumber:                          account.GetAccountNumber(),
			AccountStatus:                          account.GetAccountStatus(),
			AccountType:                            account.GetAccountType(),
			AmountPastDue:                          account.GetAmountPastDue(),
			CreditLimitAmount:                      account.GetCreditLimitAmount(),
			CurrencyCode:                           account.GetCurrencyCode(),
			CurrentBalance:                         account.GetCurrentBalance(),
			DateClosed:                             account.GetDateClosed(),
			DateOfAddition:                         account.GetDateOfAddition(),
			DateOfLastPayment:                      account.GetDateOfLastPayment(),
			DateReported:                           account.GetDateReported(),
			HighestCreditOrOriginalLoanAmount:      highestCreditOrOriginalLoanAmount,
			Income:                                 account.GetIncome(),
			IdentificationNumber:                   account.GetIdentificationNumber(),
			OpenDate:                               account.GetOpenDate(),
			OriginalChargeOffAmount:                account.GetOriginalChargeOffAmount(),
			PaymentHistoryProfile:                  account.GetPaymentHistoryProfile(),
			PaymentRating:                          account.GetPaymentRating(),
			PortfolioType:                          account.GetPortfolioType(),
			RepaymentTenure:                        account.GetRepaymentTenure(),
			ScheduledMonthlyPaymentAmount:          account.GetScheduledMonthlyPaymentAmount(),
			SettlementAmount:                       account.GetSettlementAmount(),
			SubscriberName:                         account.GetSubscriberName(),
			TermsDuration:                          account.GetTermsDuration(),
			ValueOfCollateral:                      account.GetValueOfCollateral(),
			ValueOfCreditsLastMonth:                account.GetValueOfCreditsLastMonth(),
			WrittenOffAmtPrincipal:                 account.GetWrittenOffAmtPrincipal(),
			WrittenOffAmtTotal:                     account.GetWrittenOffAmtTotal(),
			TermsFrequency:                         account.GetTermsFrequency(),
			SpecialComment:                         account.GetSpecialComment(),
			DateOfFirstDelinquency:                 account.GetDateOfFirstDelinquency(),
			SuitFiledWilfulDefaultWrittenOffStatus: account.GetSuitFiledWillfulDefaultWrittenOffStatus(),
			SuitFiledWilfulDefault:                 account.GetSuitFiledWilfulDefault(),
			TypeOfCollateral:                       account.GetTypeOfCollateral(),
			RateOfInterest:                         account.GetRateOfInterest(),
			PromotionalRateFlag:                    account.GetPromotionalRateFlag(),
			IncomeIndicator:                        account.GetIncomeIndicator(),
			IncomeFrequencyIndicator:               account.GetIncomeFrequencyIndicator(),
			DefaultStatusDate:                      account.GetDefaultStatusDate(),
			LitigationStatusDate:                   account.GetLitigationStatusDate(),
			WrittenOffStatusDate:                   account.GetWriteOffStatusDate(),
			SubscriberComments:                     account.GetSubscriberComments(),
			ConsumerComments:                       account.GetConsumerComments(),
			CustomerSegment:                        account.GetCustomerSegment(),
			ReportId:                               c.CreditReportDataRaw.GetCreditProfileHeader().GetReportNumber(),
			ReportDate:                             c.CreditReportDataRaw.GetCreditProfileHeader().GetReportDate(),
			ReportTime:                             c.CreditReportDataRaw.GetCreditProfileHeader().GetReportTime(),
			CreditAccountHistories:                 GetCreditAccountHistoryModel(c, account, dedupId),
			CreditAccountHolderDetails:             GetCreditAccountHolderDetails(c, account, dedupId),
		}
		creditAccountDetailModels = append(creditAccountDetailModels, model)
	}

	return creditAccountDetailModels, nil
}
