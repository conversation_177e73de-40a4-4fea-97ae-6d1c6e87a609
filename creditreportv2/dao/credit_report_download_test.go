package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"
	"time"

	"github.com/golang/protobuf/proto" //nolint:depguard
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"golang.org/x/net/context"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	pb "github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/frontend/deeplink"
	creditReportVgPb "github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/creditreportv2/dao/model"
)

var (
	actorId1   = "actor1"
	actorId2   = "actor2"
	actorId3   = "actor3"
	actorId4   = "actor4"
	crdSample1 = &pb.CreditReportDownload{
		Id:               uuid.New().String(),
		RequestId:        uuid.New().String(),
		ActorId:          actorId1,
		Vendor:           commonvgpb.Vendor_EXPERIAN,
		FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED,
		OtpInfo:          &pb.OtpInfo{},
		ConsentInfo:      &pb.ConsentInfo{},
		ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
		ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		NextAction:       &deeplink.Deeplink{},
	}

	crdFixture1 = &pb.CreditReportDownload{
		Id:               "a5004f28-5d52-4991-82a9-2a1e5010e991",
		RequestId:        "b5004f28-5d52-4991-82a9-2a1e5010e991",
		ActorId:          actorId2,
		Vendor:           commonvgpb.Vendor_EXPERIAN,
		FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED,
		OtpInfo:          &pb.OtpInfo{},
		ConsentInfo:      &pb.ConsentInfo{},
		ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
		ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		RedirectDeeplink: &deeplink.Deeplink{},
		NextAction:       nil,
	}

	crdFixture2 = &pb.CreditReportDownload{
		Id:               "a5004f28-5d52-4991-82a9-2a1e5010e992",
		RequestId:        "b5004f28-5d52-4991-82a9-2a1e5010e992",
		ActorId:          actorId3,
		Vendor:           commonvgpb.Vendor_EXPERIAN,
		FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED,
		OtpInfo:          &pb.OtpInfo{},
		ConsentInfo:      &pb.ConsentInfo{},
		ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
		ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		RedirectDeeplink: &deeplink.Deeplink{},
		NextAction:       &deeplink.Deeplink{},
	}

	crdFixture3 = &pb.CreditReportDownload{
		Id:               "a5004f28-5d52-4991-82a9-2a1e5010e993",
		RequestId:        "b5004f28-5d52-4991-82a9-2a1e5010e993",
		ActorId:          actorId3,
		Vendor:           commonvgpb.Vendor_EXPERIAN,
		FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED,
		OtpInfo:          &pb.OtpInfo{},
		ConsentInfo:      &pb.ConsentInfo{},
		ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
		ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		RedirectDeeplink: &deeplink.Deeplink{},
		NextAction:       &deeplink.Deeplink{},
		DownloadedAt:     timestamppb.New(time.Date(2022, time.April, 10, 17, 20, 0, 0, time.UTC)),
		CompletedAt:      timestamppb.New(time.Date(2022, time.April, 10, 17, 30, 0, 0, time.UTC)),
	}

	crdFixture4 = &pb.CreditReportDownload{
		Id:               "a5004f28-5d52-4991-82a9-2a1e5010e994",
		RequestId:        "b5004f28-5d52-4991-82a9-2a1e5010e994",
		ActorId:          actorId3,
		Vendor:           commonvgpb.Vendor_KARZA,
		FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_EXISTING,
		OtpInfo:          &pb.OtpInfo{},
		ConsentInfo:      &pb.ConsentInfo{},
		ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED,
		ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		RedirectDeeplink: &deeplink.Deeplink{},
		NextAction:       &deeplink.Deeplink{},
		Provenance:       pb.Provenance_PROVENANCE_ANALYSER,
	}

	crdFixture5 = &pb.CreditReportDownload{
		Id:               "a5004f28-5d52-4991-82a9-2a1e5010e996",
		RequestId:        "b5004f28-5d52-4991-82a9-2a1e5010e996",
		ActorId:          actorId4,
		Vendor:           commonvgpb.Vendor_CIBIL,
		FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED,
		OtpInfo:          &pb.OtpInfo{},
		ConsentInfo:      &pb.ConsentInfo{},
		ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
		ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
		RedirectDeeplink: &deeplink.Deeplink{},
		NextAction:       &deeplink.Deeplink{},
	}
)

func TestCreditReportDownloadDaoCrdb_Create(t *testing.T) {
	type args struct {
		ctx   context.Context
		entry *pb.CreditReportDownload
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.CreditReportDownload
		wantErr bool
		err     error
	}{
		{
			name: "successfully add an entry",
			args: args{
				ctx:   context.Background(),
				entry: crdSample1,
			},
			want:    crdSample1,
			wantErr: false,
		},
		{
			name: "duplicate entry",
			args: args{
				ctx:   context.Background(),
				entry: crdFixture1,
			},
			wantErr: true,
			err:     epifierrors.ErrDuplicateEntry,
		},
	}
	pkgTest.PrepareScopedDatabase(t, crTs.conf.EpifiDb.GetName(), crTs.db, affectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCreditReportDownloadDaoCrdb(crTs.db)
			got, err := c.Create(tt.args.ctx, tt.args.entry)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.err)
				}
			}
			if err == nil && !isEqualCreditReportDownload(got, tt.want) {
				t.Errorf("Create() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditReportDownloadDaoCrdb_GetById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.CreditReportDownload
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by id",
			args: args{
				ctx: context.Background(),
				id:  crdFixture1.GetId(),
			},
			want: crdFixture1,
		},
		{
			name: "record not found",
			args: args{
				ctx: context.Background(),
				id:  uuid.New().String(),
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "id not passed in args",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	pkgTest.PrepareScopedDatabase(t, crTs.conf.EpifiDb.GetName(), crTs.db, affectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCreditReportDownloadDaoCrdb(crTs.db)
			got, err := c.GetById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetById() error = %v, wantErr %v", err, tt.err)
				}
			}
			if err == nil && !isEqualCreditReportDownload(got, tt.want) {
				t.Errorf("GetById() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditReportDownloadDaoCrdb_GetByRequestId(t *testing.T) {
	type args struct {
		ctx   context.Context
		reqId string
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.CreditReportDownload
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by reqId",
			args: args{
				ctx:   context.Background(),
				reqId: crdFixture2.GetRequestId(),
			},
			want: crdFixture2,
		},
		{
			name: "record not found",
			args: args{
				ctx:   context.Background(),
				reqId: uuid.New().String(),
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "reqId not passed in args",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	pkgTest.PrepareScopedDatabase(t, crTs.conf.EpifiDb.GetName(), crTs.db, affectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCreditReportDownloadDaoCrdb(crTs.db)
			got, err := c.GetByRequestId(tt.args.ctx, tt.args.reqId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRequestId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByRequestId() error = %v, wantErr %v", err, tt.err)
				}
			}
			if err == nil && !isEqualCreditReportDownload(got, tt.want) {
				t.Errorf("GetByRequestId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditReportDownloadDaoCrdb_Update(t *testing.T) {
	type args struct {
		ctx         context.Context
		entry       *pb.CreditReportDownload
		updateMasks []pb.CreditReportDownloadFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *pb.CreditReportDownload
	}{
		{
			name: "id of entry to be updated not present in req",
			args: args{
				ctx: context.Background(),
				entry: &pb.CreditReportDownload{
					RequestId: uuid.New().String(),
					ActorId:   actorId3,
				},
				updateMasks: []pb.CreditReportDownloadFieldMask{
					pb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_ACTOR_ID,
				},
			},
			wantErr: true,
		},
		{
			name: "updateMask array is empty",
			args: args{
				ctx: context.Background(),
				entry: &pb.CreditReportDownload{
					Id:        uuid.New().String(),
					RequestId: uuid.New().String(),
					ActorId:   actorId3,
				},
				updateMasks: []pb.CreditReportDownloadFieldMask{},
			},
			wantErr: true,
		},
		{
			name: "successfully update entry",
			args: args{
				ctx: context.Background(),
				entry: &pb.CreditReportDownload{
					Id:               "a5004f28-5d52-4991-82a9-2a1e5010e991",
					RequestId:        "b5004f28-5d52-4991-82a9-2a1e5011e992",
					ActorId:          actorId3,
					Vendor:           commonvgpb.Vendor_EXPERIAN,
					FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED,
					OtpInfo:          &pb.OtpInfo{},
					ConsentInfo:      &pb.ConsentInfo{},
					ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
					ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
					RedirectDeeplink: &deeplink.Deeplink{},
					Details: &pb.Details{
						DownloadDetails: &pb.DownloadDetails{
							VendorDownloadDetails: &pb.DownloadDetails_CibilDownloadDetails{
								CibilDownloadDetails: &pb.CibilDownloadDetails{
									CustomerId: "customer_id",
									SkipAuth:   false,
									AuthStatus: creditReportVgPb.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE,
								},
							},
						},
					},
				},
				updateMasks: []pb.CreditReportDownloadFieldMask{
					pb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_ACTOR_ID,
					pb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_REQUEST_ID,
					pb.CreditReportDownloadFieldMask_CREDIT_REPORT_DOWNLOAD_FIELD_MASK_DETAILS,
				},
			},
			want: &pb.CreditReportDownload{
				Id:               "a5004f28-5d52-4991-82a9-2a1e5010e991",
				RequestId:        "b5004f28-5d52-4991-82a9-2a1e5011e992",
				ActorId:          actorId3,
				Vendor:           commonvgpb.Vendor_EXPERIAN,
				FetchType:        pb.CreditReportFetchType_CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED,
				OtpInfo:          &pb.OtpInfo{},
				ConsentInfo:      &pb.ConsentInfo{},
				ProcessStatus:    pb.CreditReportDownloadStatus_CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED,
				ProcessSubStatus: pb.CreditReportDownloadSubStatus_CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED,
				RedirectDeeplink: &deeplink.Deeplink{},
				Details: &pb.Details{
					DownloadDetails: &pb.DownloadDetails{
						VendorDownloadDetails: &pb.DownloadDetails_CibilDownloadDetails{
							CibilDownloadDetails: &pb.CibilDownloadDetails{
								CustomerId: "customer_id",
								SkipAuth:   false,
								AuthStatus: creditReportVgPb.AtlasResponseStatus_ATLAS_RESPONSE_STATUS_FAILURE,
							},
						},
					},
				},
			},
		},
	}
	pkgTest.PrepareScopedDatabase(t, crTs.conf.EpifiDb.GetName(), crTs.db, affectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCreditReportDownloadDaoCrdb(crTs.db)
			if err := c.Update(tt.args.ctx, tt.args.entry, tt.args.updateMasks); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.want != nil {
				got := getById(tt.args.entry.GetId())
				if !isEqualCreditReportDownload(got, tt.want) {
					t.Errorf("Update() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestCreditReportDownloadDaoCrdb_GetByActorId(t *testing.T) {
	type args struct {
		ctx           context.Context
		actorId       string
		filterOptions []storageV2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.CreditReportDownload
		wantErr bool
		err     error
	}{
		{
			name: "successfully get by actorId",
			args: args{
				ctx:     context.Background(),
				actorId: crdFixture2.GetActorId(),
			},
			want: []*pb.CreditReportDownload{
				crdFixture4,
				crdFixture3,
				crdFixture2,
			},
		},
		{
			name: "record not found",
			args: args{
				ctx:     context.Background(),
				actorId: uuid.New().String(),
			},
			wantErr: false,
			want:    []*pb.CreditReportDownload{},
			err:     nil,
		},
		{
			name: "actorId not passed in args",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "apply provenance filter option",
			args: args{
				ctx:     context.Background(),
				actorId: actorId3,
				filterOptions: []storageV2.FilterOption{
					WithProvenance(pb.Provenance_PROVENANCE_ANALYSER),
				},
			},
			want: []*pb.CreditReportDownload{
				crdFixture4,
			},
		},
		{
			name: "apply vendor filter option",
			args: args{
				ctx:     context.Background(),
				actorId: actorId3,
				filterOptions: []storageV2.FilterOption{
					WithVendor(commonvgpb.Vendor_KARZA),
				},
			},
			want: []*pb.CreditReportDownload{
				crdFixture4,
			},
		},
		{
			name: "apply order descending filter option",
			args: args{
				ctx:     context.Background(),
				actorId: actorId3,
				filterOptions: []storageV2.FilterOption{
					WithOrderByCreatedAtDescending(),
				},
			},
			want: []*pb.CreditReportDownload{
				crdFixture4,
				crdFixture3,
				crdFixture2,
			},
		},
	}
	pkgTest.PrepareScopedDatabase(t, crTs.conf.EpifiDb.GetName(), crTs.db, affectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCreditReportDownloadDaoCrdb(crTs.db)
			got, err := c.GetByActorId(tt.args.ctx, tt.args.actorId, tt.args.filterOptions...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetByActorId() error = %v, wantErr %v", err, tt.err)
				}
			}
			if err == nil && !areEqualEqualCreditReportDownloadArr(got, tt.want) {
				t.Errorf("GetByActorId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreditReportDownloadDaoCrdb_GetLatestByActorIdAndVendor(t *testing.T) {
	type args struct {
		ctx     context.Context
		actorId string
		vendor  commonvgpb.Vendor
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.CreditReportDownload
		wantErr bool
		err     error
	}{
		{
			name: "successfully get latest by actor id and vendor",
			args: args{
				ctx:     context.Background(),
				actorId: crdFixture5.GetActorId(),
				vendor:  crdFixture5.GetVendor(),
			},
			want: crdFixture5,
		},
		{
			name: "record not found",
			args: args{
				ctx:     context.Background(),
				actorId: "actor1234",
				vendor:  commonvgpb.Vendor_EXPERIAN,
			},
			wantErr: true,
			err:     epifierrors.ErrRecordNotFound,
		},
		{
			name: "actor id not passed in args",
			args: args{
				ctx:    context.Background(),
				vendor: commonvgpb.Vendor_EXPERIAN,
			},
			wantErr: true,
		},
		{
			name: "failure - vendor id is unspecified",
			args: args{
				ctx:     context.Background(),
				vendor:  commonvgpb.Vendor_VENDOR_UNSPECIFIED,
				actorId: "actorId",
			},
			wantErr: true,
		},
	}
	pkgTest.PrepareScopedDatabase(t, crTs.conf.EpifiDb.GetName(), crTs.db, affectedTestTables)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := NewCreditReportDownloadDaoCrdb(crTs.db)
			got, err := c.GetLatestByActorIdAndVendor(tt.args.ctx, tt.args.actorId, tt.args.vendor)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestByActorIdAndVendor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.err != nil {
				if !errors.Is(err, tt.err) {
					t.Errorf("GetLatestByActorIdAndVendor() error = %v, wantErr %v", err, tt.err)
				}
			}
			if err == nil && !isEqualCreditReportDownload(got, tt.want) {
				t.Errorf("GetLatestByActorIdAndVendor() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isEqualCreditReportDownload(got, want *pb.CreditReportDownload) bool {
	if got != nil {
		got.CreatedAt = nil
		got.UpdatedAt = nil
		got.DeletedAt = nil
	}
	return proto.Equal(got, want)
}

func areEqualEqualCreditReportDownloadArr(gotArr, wantArr []*pb.CreditReportDownload) bool {
	if len(gotArr) != len(wantArr) {
		return false
	}
	for _, want := range wantArr {
		found := false
		for _, got := range gotArr {
			if isEqualCreditReportDownload(got, want) {
				found = true
			}
		}
		if !found {
			return false
		}
	}
	return true
}

func getById(id string) *pb.CreditReportDownload {
	var crdModel model.CreditReportDownload
	crTs.db.Where("id = ?", id).First(&crdModel)
	return crdModel.ToProto()
}
