package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"google.golang.org/protobuf/proto"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/creditreportv2/consumer"
	userPb "github.com/epifi/gamma/api/user"
	creditreport "github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/creditreportv2/config"
	"github.com/epifi/gamma/creditreportv2/consumer/provider"
	"github.com/epifi/gamma/creditreportv2/dao"
	"github.com/epifi/gamma/creditreportv2/internal"
	wireTypes "github.com/epifi/gamma/creditreportv2/wire/types"
	"github.com/epifi/gamma/pkg/vendorstore"

	"go.uber.org/zap"
)

type CreditReportConsumerService struct {
	consumer.UnimplementedCreditReportConsumerServer
	conf                                   *config.Config
	creditReportDao                        dao.CreditReportDao
	creditReportRawDao                     dao.CreditReportsRawDao
	creditReportUserSubscriptionDao        dao.CreditReportUserSubscriptionDetailDao
	vgCreditReportClient                   creditreport.CreditReportClient
	vendorStore                            vendorstore.VendorStore
	eventBroker                            events.Broker
	s3Client                               s3.S3Client
	userProcessor                          internal.IUserProcessor
	txnExecutor                            storage.TxnExecutor
	creditReportDerivedAttributesPublisher wireTypes.CreditReportDerivedAttributesPublisher
	crFlattenFactory                       provider.IFactory
	crDownloadEventPublisher               queue.Publisher
}

const (
	// layout format -> YYYYMMDD
	layout = "20060102"
	// context cancel cut off for credit report APIs
	creditReportRpcTimeout = 30 * time.Second
)

func NewCreditReportConsumerService(conf *config.Config, creditReportDao dao.CreditReportDao, creditReportRawDao dao.CreditReportsRawDao,
	creditReportUserSubscriptionDao dao.CreditReportUserSubscriptionDetailDao,
	vgCreditReportClient creditreport.CreditReportClient, vendorStore vendorstore.VendorStore, eventBroker events.Broker,
	s3Client s3.S3Client, userProcessor internal.IUserProcessor, txnExecutor storage.TxnExecutor,
	creditReportDerivedAttributesPublisher wireTypes.CreditReportDerivedAttributesPublisher, crFactory *provider.Factory, crDownloadEventPublisher wireTypes.CreditReportDownloadEventsPublisher) *CreditReportConsumerService {
	return &CreditReportConsumerService{
		conf:                                   conf,
		creditReportDao:                        creditReportDao,
		creditReportRawDao:                     creditReportRawDao,
		creditReportUserSubscriptionDao:        creditReportUserSubscriptionDao,
		vgCreditReportClient:                   vgCreditReportClient,
		vendorStore:                            vendorStore,
		eventBroker:                            eventBroker,
		s3Client:                               s3Client,
		userProcessor:                          userProcessor,
		txnExecutor:                            txnExecutor,
		creditReportDerivedAttributesPublisher: creditReportDerivedAttributesPublisher,
		crFlattenFactory:                       crFactory,
		crDownloadEventPublisher:               crDownloadEventPublisher,
	}
}

var _ consumer.CreditReportConsumerServer = &CreditReportConsumerService{}

func newSuccessResp() *queuePb.ConsumerResponseHeader {
	return &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_SUCCESS,
	}
}

func newTransientResp() *queuePb.ConsumerResponseHeader {
	return &queuePb.ConsumerResponseHeader{
		Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
	}
}

func (c *CreditReportConsumerService) markManualIntervention(ctx context.Context, creditReportResp *creditReportPb.CreditReport, mask []creditReportPb.CreditReportFieldMask) queuePb.MessageConsumptionStatus {
	logger.Error(ctx, "max attempts exhausted")
	err := c.creditReportDao.UpdateById(ctx, creditReportResp, mask)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to %v to MANUAL INTERVENTION", mask[0].String()), zap.Error(err))
		return queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
	}
	return queuePb.MessageConsumptionStatus_PERMANENT_FAILURE
}

func (c *CreditReportConsumerService) isPastCreditReportPresenceCheckMaxDuration(request *consumer.ProcessReportPresenceCheckRequest) bool {
	return time.Now().After(request.GetPresenceCheckStartedAt().AsTime().Add(c.conf.Screening.CreditReportPresenceCheck.CreditReportPresenceCheckMaxDuration))
}

func (c *CreditReportConsumerService) isPastCreditReportVerificationMaxDuration(request *consumer.ProcessReportVerificationRequest) bool {
	return time.Now().After(request.GetVerificationStartedAt().AsTime().Add(c.conf.Screening.CreditReportVerification.CreditReportVerificationMaxDuration))
}

func (c *CreditReportConsumerService) getNameForCreditReportCheck(ctx context.Context, user *userPb.User, allowedSwapping bool) *commontypes.Name {

	// first priority goes to pan_name, if available
	panName := proto.Clone(user.GetProfile().GetPanName()).(*commontypes.Name)

	// if panName is nil, try to get from data verification details
	if panName == nil {
		for _, detail := range user.GetDataVerificationDetails().GetDataVerificationDetails() {
			if detail.GetDataType() == userPb.DataType_DATA_TYPE_PAN_NAME {
				panName = detail.GetPanName()
			}
		}
	}

	if panName == nil {
		return user.GetProfile().GetGmailName()
	}

	if len(panName.GetFirstName()) > 1 || !allowedSwapping {
		return panName
	}
	logger.Info(ctx, fmt.Sprintf("pan name has first name length %v and second name length %v, swapping first and last names", len(panName.GetFirstName()), len(panName.GetLastName())))
	// when firstName is of length one in that case we are not able to fetch credit report from experian

	// Swap the first and last name
	panName.FirstName, panName.LastName = panName.GetLastName(), panName.GetFirstName()

	return panName
}

func (c *CreditReportConsumerService) getPanForCreditReportCheck(user *userPb.User) string {
	// first priority goes to pan_name, if available
	panNumber := user.GetProfile().GetPAN()

	// if panNumber is empty, try to get from data verification details
	if panNumber == "" {
		dataVerificationDetails := user.GetDataVerificationDetails().GetDataVerificationDetails()
		for i := len(dataVerificationDetails) - 1; i >= 0; i-- {
			if dataVerificationDetails[i].GetDataType() == userPb.DataType_DATA_TYPE_PAN {
				return dataVerificationDetails[i].GetPanNumber()
			}
		}
	}
	return panNumber
}
