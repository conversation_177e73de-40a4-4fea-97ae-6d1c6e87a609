package attributes

import (
	"fmt"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/context"

	"github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	"github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/gamma/creditreportv2/derivedattributes/helpers"
	"github.com/epifi/gamma/creditreportv2/derivedattributes/valuetypes"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	vintageAttributeBaseName = "VINTAGE"
	reportDateFormat         = "********"
)

// LOGIC:
//
// This attribute computes the maximum month difference between report date and
// credit account open date. This attribute needs to be calculated individually
// for every account type, where the account holder is a primary account holder.
// So it has a variation for every AccountType.
//
// Attribute name format: '<AccountType>_VINTAGE'
type Vintage struct {
	basename    string
	accountType derivedattributes.AccountType
}

// NewVintageVariations create new instances of Vintage for all possible variations
func NewVintageVariations() []*Vintage {
	accountTypesToCalculateFor := []derivedattributes.AccountType{
		derivedattributes.AccountType_ACCOUNT_TYPE_UNSECURED,
		derivedattributes.AccountType_ACCOUNT_TYPE_BUREAU,
		derivedattributes.AccountType_ACCOUNT_TYPE_CL,
		derivedattributes.AccountType_ACCOUNT_TYPE_TWL,
	}
	var vintageAttributes []*Vintage
	for _, accountType := range accountTypesToCalculateFor {
		vintage := &Vintage{}
		vintage.basename = vintageAttributeBaseName
		vintage.accountType = accountType
		vintageAttributes = append(vintageAttributes, vintage)
	}
	return vintageAttributes
}

func (a *Vintage) GetName() string {
	return fmt.Sprintf("%v_%v", helpers.AccountTypeName[a.accountType], a.basename)
}

func (a *Vintage) GetValue(ctx context.Context, creditReportData *credit_report.CreditReportData) (valuetypes.IAttrVal, error) {
	if creditReportData.GetCreditAccount() == nil || creditReportData.GetCreditAccount().GetCreditAccountDetails() == nil {
		return nil, nil
	}
	creditAccountDetails := creditReportData.GetCreditAccount().GetCreditAccountDetails()
	// Filter accounts for type
	creditAccountDetailsFiltered := helpers.FilterCreditAccountsForType(creditAccountDetails, a.accountType)
	// Filter and get accounts where the account holder type is primary
	creditAccountDetailsFiltered = helpers.FilterCreditAccountsForHolderType(creditAccountDetailsFiltered, helpers.ACCOUNT_HOLDER_TYPE_PRIMARY)
	// vintage cannot be calculated
	if len(creditAccountDetailsFiltered) < 1 {
		return nil, nil
	}
	// get report date
	reportDate, err := time.Parse(reportDateFormat, creditReportData.GetCreditProfileHeader().GetReportDate())
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error parsing ReportDate for %v", *a), zap.Error(err))
		return nil, err
	}
	vintage := valuetypes.NewIntAttr()
	vintage.SetValue(0)
	validValueFound := false
	// find vintage (max month difference between credit account open date and report date)
	for _, creditAccountDetail := range creditAccountDetailsFiltered {
		if creditAccountDetail.GetOpenDate() == "" {
			continue
		}
		openDate, err := time.Parse(reportDateFormat, creditAccountDetail.GetOpenDate())
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error parsing OpenDate for %v", *a), zap.Error(err))
			return nil, err
		}
		validValueFound = true
		monthDiff, _ := helpers.DiffMonths(reportDate, openDate)
		if monthDiff > vintage.GetValue() {
			vintage.SetValue(monthDiff)
		}
	}

	if !validValueFound {
		// we need to return an interface being nil: https://www.jerf.org/iri/post/2957/
		return nil, nil
	}

	return vintage, nil
}
