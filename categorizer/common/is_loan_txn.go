package common

import (
	"strings"
)

type LoanRemarksThreshold struct {
	remark string
}

var RemarksData = []*LoanRemarksThreshold{
	{
		remark: "Loan",
	},
	{
		remark: "DMI FINANCE PVT",
	},
	{
		remark: "POONAWALLA FINCORP LIMITE",
	},
}

func IsTxnALoanTxnBasedOnRemarks(remark string) bool {
	for _, data := range RemarksData {
		if strings.Contains(strings.ToLower(remark), strings.ToLower(data.remark)) {
			return true
		}
	}
	return false
}
