// Code generated by MockGen. DO NOT EDIT.
// Source: ./actor_processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	payloads "github.com/epifi/gamma/categorizer/common/payloads"
	genconf "github.com/epifi/be-common/pkg/cfg/genconf"
	gomock "github.com/golang/mock/gomock"
)

// MockActorProcessor is a mock of ActorProcessor interface.
type MockActorProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockActorProcessorMockRecorder
}

// MockActorProcessorMockRecorder is the mock recorder for MockActorProcessor.
type MockActorProcessorMockRecorder struct {
	mock *MockActorProcessor
}

// NewMockActorProcessor creates a new mock instance.
func NewMockActorProcessor(ctrl *gomock.Controller) *MockActorProcessor {
	mock := &MockActorProcessor{ctrl: ctrl}
	mock.recorder = &MockActorProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockActorProcessor) EXPECT() *MockActorProcessorMockRecorder {
	return m.recorder
}

// GetActorDetails mocks base method.
func (m *MockActorProcessor) GetActorDetails(ctx context.Context, actorId string) (*payloads.ActorDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActorDetails", ctx, actorId)
	ret0, _ := ret[0].(*payloads.ActorDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActorDetails indicates an expected call of GetActorDetails.
func (mr *MockActorProcessorMockRecorder) GetActorDetails(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActorDetails", reflect.TypeOf((*MockActorProcessor)(nil).GetActorDetails), ctx, actorId)
}

// IsActorAllowedForFeature mocks base method.
func (m *MockActorProcessor) IsActorAllowedForFeature(ctx context.Context, featureFlag *genconf.FeatureReleaseConfig, actorDetails *payloads.ActorDetails) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsActorAllowedForFeature", ctx, featureFlag, actorDetails)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsActorAllowedForFeature indicates an expected call of IsActorAllowedForFeature.
func (mr *MockActorProcessorMockRecorder) IsActorAllowedForFeature(ctx, featureFlag, actorDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsActorAllowedForFeature", reflect.TypeOf((*MockActorProcessor)(nil).IsActorAllowedForFeature), ctx, featureFlag, actorDetails)
}
