//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	actorPb "github.com/epifi/gamma/api/actor"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/auth/location"
	connectedaccountpb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/gplace"
	mPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	actorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	"github.com/epifi/gamma/api/order/payment"
	paypb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	upiPb "github.com/epifi/gamma/api/upi"
	usersPb "github.com/epifi/gamma/api/user"
	groupPb "github.com/epifi/gamma/api/user/group"
	vendorClient "github.com/epifi/gamma/api/vendors/http"
	"github.com/epifi/gamma/categorizer/category"
	"github.com/epifi/gamma/categorizer/config"
	"github.com/epifi/gamma/categorizer/config/genconf"
	categorizerConsumer "github.com/epifi/gamma/categorizer/consumer"
	"github.com/epifi/gamma/categorizer/consumer/filters"
	"github.com/epifi/gamma/categorizer/consumer/generator"
	"github.com/epifi/gamma/categorizer/consumer/processor/aa"
	"github.com/epifi/gamma/categorizer/consumer/processor/cc"
	"github.com/epifi/gamma/categorizer/consumer/processor/crowd_aggregation"
	"github.com/epifi/gamma/categorizer/consumer/processor/order"
	"github.com/epifi/gamma/categorizer/consumer/validators"
	"github.com/epifi/gamma/categorizer/dao"
	"github.com/epifi/gamma/categorizer/developer"
	"github.com/epifi/gamma/categorizer/developer/processor"
	"github.com/epifi/gamma/categorizer/ds"
	gplaceImpl "github.com/epifi/gamma/categorizer/gplace"
	"github.com/epifi/gamma/categorizer/internal/actor"
	piInternal "github.com/epifi/gamma/categorizer/internal/pi/impl"
	"github.com/epifi/gamma/categorizer/processor/cache"
	"github.com/epifi/gamma/categorizer/processor/sortcategories"
	categorizerService "github.com/epifi/gamma/categorizer/service"
	wireTypes "github.com/epifi/gamma/categorizer/wire/types"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	"github.com/epifi/gamma/pkg/dmf/txnaggregates"
)

// config: {"client": "CrowdAggregatedCategoryUpdateParams().S3BucketName()"}
func InitializeCategorizerConsumerService(conf *genconf.Config, pgdb cmdTypes.CategorizerPGDB, actorClient actorPb.ActorClient,
	piClient piPb.PiClient, upiClient upiPb.UPIClient, merchantClient mPb.MerchantServiceClient, userGroupClient groupPb.GroupClient,
	userClient usersPb.UsersClient, txnCatPublisher wireTypes.TxnCatPublisher, gplaceClient gplace.GPlaceClient,
	locationClient location.LocationClient, batchTxnCategoryPublisher wireTypes.BatchTxnCategoryPublisher,
	redisClient wireTypes.CategorizerVMRedisStore, client s3.S3Client) *categorizerConsumer.Service {
	wire.Build(
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		vendorClient.NewHttpClient,
		wire.NewSet(
			categorizerConsumer.NewDsCategorizerImpl, wire.Bind(new(categorizerConsumer.DsCategorizer), new(*categorizerConsumer.DsCategorizerImpl)), dsUrlsProvider),
		categorizerConsumer.NewCategorizerConsumerService,
		aa.WireSet,
		order.WireSet,
		cc.WireSet,
		crowd_aggregation.WireCrowdAggregationSet,
		validators.WireSet,
		filters.WireSet,
		generator.WireSet,
		gplaceImpl.WireSet,
		category.WireSet,
		actor.ActorProcessorWireSet,
		piInternal.PiProcessorWireSet,
		ds.WireSet,
		dsClientProvider,
		gormDbProvider,
		storageV2.DefaultTxnExecutorWireSet,
		cache.InMemoryCategoriesCacheWireSet,
	)
	return &categorizerConsumer.Service{}
}

func InitializeCategorizerService(genconf *genconf.Config, pgdb cmdTypes.CategorizerPGDB, orderClient orderPb.OrderServiceClient, actorClient actorPb.ActorClient,
	userGroupClient groupPb.GroupClient, txnCatPublisher wireTypes.TxnCatPublisher, aaOrderClient aaOrderPb.AccountAggregatorClient,
	piClient piPb.PiClient, paymentClient payment.PaymentClient, actorActivityClient actorActivityPb.ActorActivityClient,
	accountingClient accounting.AccountingClient, payClient paypb.PayClient, upiClient upiPb.UPIClient, merchantClient mPb.MerchantServiceClient,
	txnAggClient txnAggregatesPb.TxnAggregatesClient, connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	savingsClient savingsClientPb.SavingsClient, fireflyClient firefly.FireflyClient, accountPiCliet accountPiPb.AccountPIRelationClient) *categorizerService.CategorizerService {
	wire.Build(
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		cache.InMemoryCategoriesCacheWireSet,
		piInternal.PiProcessorWireSet,
		category.WireSet,
		categorizerService.NewCategorizerService,
		datetime.WireDefaultTimeSet,
		datafetcher.IMerchantsWireSet,
		datafetcher.ActorAccountsWireSet,
		txnaggregates.TxnAggregatesWireSet,
		sortcategories.SortCategoriesStrategyWireSet,
		gormDbProvider,
		storageV2.DefaultTxnExecutorWireSet,
	)
	return &categorizerService.CategorizerService{}
}

func InitialiseCategorizerDevEntityService(pgdb cmdTypes.CategorizerPGDB, genconf *genconf.Config) *developer.CategorizerDevService {
	wire.Build(
		developer.NewCategorizerDevService,
		developer.NewDevFactory,
		processor.NewTransactionCategory,
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		processor.NewGplaceCategory,
	)
	return &developer.CategorizerDevService{}
}

func gormDbProvider(db cmdTypes.CategorizerPGDB) *gorm.DB {
	return db
}

func dsUrlsProvider(gconf *genconf.Config) *config.DsUrls {
	return gconf.DsUrls()
}

func gplaceParamsProvider(gconf *genconf.Config) *config.GplaceParams {
	return gconf.GplaceParams()
}

func dsClientProvider(gconf *genconf.Config) *cfg.HttpClient {
	return gconf.HttpClientConfig()
}
