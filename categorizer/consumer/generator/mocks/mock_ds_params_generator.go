// Code generated by MockGen. DO NOT EDIT.
// Source: ./ds_params_generator.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	categorizer "github.com/epifi/gamma/api/categorizer"
	consumer "github.com/epifi/gamma/api/firefly/accounting/consumer"
	order "github.com/epifi/gamma/api/order"
	aa "github.com/epifi/gamma/api/order/aa"
	gomock "github.com/golang/mock/gomock"
)

// MockDsParamsGenerator is a mock of DsParamsGenerator interface.
type MockDsParamsGenerator struct {
	ctrl     *gomock.Controller
	recorder *MockDsParamsGeneratorMockRecorder
}

// MockDsParamsGeneratorMockRecorder is the mock recorder for MockDsParamsGenerator.
type MockDsParamsGeneratorMockRecorder struct {
	mock *MockDsParamsGenerator
}

// NewMockDsParamsGenerator creates a new mock instance.
func NewMockDsParamsGenerator(ctrl *gomock.Controller) *MockDsParamsGenerator {
	mock := &MockDsParamsGenerator{ctrl: ctrl}
	mock.recorder = &MockDsParamsGeneratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDsParamsGenerator) EXPECT() *MockDsParamsGeneratorMockRecorder {
	return m.recorder
}

// GenerateDSParamsForAATxn mocks base method.
func (m *MockDsParamsGenerator) GenerateDSParamsForAATxn(ctx context.Context, aaTxnUpdateEvent *aa.AATxnUpdate) (*categorizer.DsCategorizerRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateDSParamsForAATxn", ctx, aaTxnUpdateEvent)
	ret0, _ := ret[0].(*categorizer.DsCategorizerRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDSParamsForAATxn indicates an expected call of GenerateDSParamsForAATxn.
func (mr *MockDsParamsGeneratorMockRecorder) GenerateDSParamsForAATxn(ctx, aaTxnUpdateEvent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDSParamsForAATxn", reflect.TypeOf((*MockDsParamsGenerator)(nil).GenerateDSParamsForAATxn), ctx, aaTxnUpdateEvent)
}

// GenerateDSParamsForCCTxn mocks base method.
func (m *MockDsParamsGenerator) GenerateDSParamsForCCTxn(ctx context.Context, ccTxnEvent *consumer.CreditCardTransactionEvent) (*categorizer.DsCategorizerRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateDSParamsForCCTxn", ctx, ccTxnEvent)
	ret0, _ := ret[0].(*categorizer.DsCategorizerRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDSParamsForCCTxn indicates an expected call of GenerateDSParamsForCCTxn.
func (mr *MockDsParamsGeneratorMockRecorder) GenerateDSParamsForCCTxn(ctx, ccTxnEvent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDSParamsForCCTxn", reflect.TypeOf((*MockDsParamsGenerator)(nil).GenerateDSParamsForCCTxn), ctx, ccTxnEvent)
}

// GenerateDSParamsForOrder mocks base method.
func (m *MockDsParamsGenerator) GenerateDSParamsForOrder(ctx context.Context, orderUpdateEvent *order.OrderUpdate) (*categorizer.DsCategorizerRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateDSParamsForOrder", ctx, orderUpdateEvent)
	ret0, _ := ret[0].(*categorizer.DsCategorizerRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateDSParamsForOrder indicates an expected call of GenerateDSParamsForOrder.
func (mr *MockDsParamsGeneratorMockRecorder) GenerateDSParamsForOrder(ctx, orderUpdateEvent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateDSParamsForOrder", reflect.TypeOf((*MockDsParamsGenerator)(nil).GenerateDSParamsForOrder), ctx, orderUpdateEvent)
}
