package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/categorizer"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	ErrorMoneyFlowNotSupported           = errors.New("moneyflow type not supported")
	ErrorEmptyResponseFromGplaceApi      = errors.New("empty response from gplace api")
	ErrorSkippingGplaceEmptyMerchantName = errors.New("skipping gplace api call as merchant name is empty")
	ErrGotZeroSystemCategoryFromDS       = fmt.Errorf("received 0 system categories")
	PiIdGenericError                     = fmt.Errorf("pi id is generic")
)

// These order tags indicate that the transaction is interest for a FD.
var fdInterestTags = []orderPb.OrderTag{orderPb.OrderTag_INTEREST, orderPb.OrderTag_FD}

var (
	genTransientFailure = func() *queuePb.ConsumerResponseHeader {
		return &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
		}
	}
	genFinalFailure = func() *queuePb.ConsumerResponseHeader {
		return &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
		}
	}
	genSuccess = func() *queuePb.ConsumerResponseHeader {
		return &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		}
	}
)

const (
	MaxRadiusInMeters = 2000
)

// GetOntologiesString function is used for creating a string of combined levels of a transaction category ontology.
// Transaction category ontology is a combination of L0, L1, L2 and L3 category levels. L0 refers to generic level of category i.e Spend etc.
// L1, L2, L3 gives more detailed categories. A combination of the 4 leads to a unique category of transaction.
// The Ontology String returned by this method are a combination of all these 4 levels.
// TODO: can this method be moved to service as it is not used in consumer?
func GetOntologiesString(ontologies *categorizer.FiOntology) string {
	var ontologiesString string
	if ontologies.L0 == categorizer.L0_L0_UNSPECIFIED {
		return ontologiesString
	}
	ontologiesString += ontologies.GetL0().String()
	if ontologies.L1 == categorizer.L1_L1_UNSPECIFIED {
		return ontologiesString
	}
	ontologiesString += ontologies.GetL1().String()
	if ontologies.L2 == categorizer.L2_L2_UNSPECIFIED {
		return ontologiesString
	}
	ontologiesString += ontologies.GetL2().String()
	if ontologies.L3 == categorizer.L3_L3_UNSPECIFIED {
		return ontologiesString
	}
	ontologiesString += ontologies.GetL3().String()
	return ontologiesString
}

// getUserGroups returns user groups to which a given actor is linked
// in case user doesn't belong to any grp then empty slice is returned
func (s *Service) getUserGroups(ctx context.Context, actorId string,
	actorIdToUserGroupsMap map[string][]commontypes.UserGroup) ([]commontypes.UserGroup, error) {
	// check if actorId -> user groups mapping already present in cache map
	if actorIdToUserGroupsMap != nil {
		if val, found := actorIdToUserGroupsMap[actorId]; found {
			return val, nil
		}
	}
	actorRes, err := s.ActorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	if err1 := epifigrpc.RPCError(actorRes, err); err1 != nil {
		return nil, fmt.Errorf("failed to fetch actor details using GetEntityDetailsByActorId: %w", err1)
	}

	if actorRes.GetType() != typesPb.ActorType_USER {
		if actorIdToUserGroupsMap != nil {
			actorIdToUserGroupsMap[actorId] = []commontypes.UserGroup{}
		}
		return []commontypes.UserGroup{}, nil
	}

	userGrpRes, err := s.UserGroupClient.GetGroupsMappedToEmail(ctx,
		&userGroupPb.GetGroupsMappedToEmailRequest{Email: actorRes.GetEmailId()})
	if err1 := epifigrpc.RPCError(userGrpRes, err); err1 != nil {
		return nil, fmt.Errorf("failed to fetch user group for entityId %s: %w", actorRes.GetEntityId(), err1)
	}

	if actorIdToUserGroupsMap != nil {
		actorIdToUserGroupsMap[actorId] = userGrpRes.GetGroups()
	}
	return userGrpRes.GetGroups(), nil
}

// To check if a txn is an on-app txn, order provenance should be USER_APP or THIRD_PARTY
// To check that an UPI, P2M, on-app txn is offline transaction, the following conditions to be true
// 1. Full VPA i.e Upi pi type
// 2. Non-zero VPA MCC
// 3. not P2P_COLLECT and not URN_TRANSFER workflows (These workflows are mostly used by online merchants)
func isTxnMerchantOnAppOfflineTxn(orderProvenance orderPb.OrderProvenance, toVpaMcc string,
	orderWorkflow orderPb.OrderWorkflow) bool {
	return (orderProvenance == orderPb.OrderProvenance_USER_APP || orderProvenance == orderPb.OrderProvenance_THIRD_PARTY) &&
		(toVpaMcc != "" && toVpaMcc != "0000") &&
		(orderWorkflow != orderPb.OrderWorkflow_P2P_COLLECT && orderWorkflow != orderPb.OrderWorkflow_URN_TRANSFER)
}

// An off-app transaction will be the one whose order provenance is not USER_APP and not THIRD_PARTY
// To check that an UPI, P2M, off-app txn, the following condition should be true
// 1. Full VPA i.e Upi pi type
// 2. Non-zero VPA MCC
// NOTE: workflows information is not present for off-app txns, else we would have checked for them as well.
func isTxnMerchantOffAppTxn(orderProvenance orderPb.OrderProvenance, toVpaMcc string,
	orderWorkflow orderPb.OrderWorkflow) bool {
	return (orderProvenance != orderPb.OrderProvenance_USER_APP && orderProvenance != orderPb.OrderProvenance_THIRD_PARTY) &&
		(toVpaMcc != "" && toVpaMcc != "0000") && (orderWorkflow == orderPb.OrderWorkflow_NO_OP || orderWorkflow == orderPb.OrderWorkflow_OFF_APP_UPI)
}

func (s *Service) isFDInterestTxn(orderTags []orderPb.OrderTag) bool {
	// check if both the fdInterestTags are present in the order tags
	for _, fdInterestTag := range fdInterestTags {
		exists := false
		for _, orderTag := range orderTags {
			if orderTag == fdInterestTag {
				exists = true
				break
			}
		}
		if !exists {
			return false
		}
	}
	return true
}

func (s *Service) isSavingsAccountInterestTxn(orderTags []orderPb.OrderTag) bool {
	if len(orderTags) == 1 && orderTags[0] == orderPb.OrderTag_INTEREST {
		return true
	}
	return false
}

func (s *Service) isValidP2PRequestToCategorize(fromActorId string, toActorId string,
	accountingEntryType paymentPb.AccountingEntryType,
	fromAccountType accountsPb.Type, toAccountType accountsPb.Type) bool {
	// In case is for add funds to SD/FD or sd/FD redemption. We need to categorize only from the perspective of SAVINGS account, since from_actor and to_actor are same
	if fromActorId == toActorId {
		if (accountingEntryType == paymentPb.AccountingEntryType_DEBIT && fromAccountType != accountsPb.Type_SAVINGS) ||
			(accountingEntryType == paymentPb.AccountingEntryType_CREDIT && toAccountType != accountsPb.Type_SAVINGS) {
			return false
		}
	}

	return true
}

// Checks if ontology exists in cache. If it exists then it returns that ontology details from cache.
// If not then it checks in DB. If it exists in DB then it saves to cache and returns the ontology details.
// Else if it doesn't exist in DB then it throws error.
func (s *Service) fetchTxnCategoryOntology(ctx context.Context, ontologyId string) (*categorizerPb.TxnCategoryOntology, error) {
	var txnCategoryOntology *categorizerPb.TxnCategoryOntology
	ontologyCacheValue, exists := s.txnCategoryOntologyMap.Get(ontologyId)
	if !exists {
		ontology, err := s.OntologiesDao.GetByOntologyIds(ctx, []string{ontologyId})
		if err != nil {
			logger.Error(ctx, "Unable to fetch the ontology for the given ontologyId", zap.Error(err),
				zap.String(logger.TXN_CATEGORY_ONTOLOGY, ontologyId))
			return nil, err
		}
		// set the value in txnCategoryOntologyMap
		s.txnCategoryOntologyMap.SetIfAbsent(ontologyId, ontology[0])
		txnCategoryOntology = ontology[0]
	} else {
		txnCategoryOntology = ontologyCacheValue.(*categorizerPb.TxnCategoryOntology)
	}

	return txnCategoryOntology, nil
}
