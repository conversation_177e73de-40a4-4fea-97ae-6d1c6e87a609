package rawdetailsfetcher_test

import (
	"context"
	"errors"
	"testing"

	"github.com/go-test/deep"
	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher"
	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher/datafetcher"
	mockRawFactory "github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher/mocks"
	"github.com/epifi/gamma/analyser/variablegenerator/rawdetailsfetcher/rorawdetailsmap"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
)

type mockFields struct {
	rawDetailsFactory *mockRawFactory.MockIRawDetailsFactory
}

// todo: add multipe raw details test since there is one raw details now so not added
func Test_GetRawDetailsFetcher(t *testing.T) {
	type args struct {
		ctx context.Context
		req *rawdetailsfetcher.GetRawDetailsFetcherRequest
	}
	sampleReq := &rawdetailsfetcher.GetRawDetailsFetcherRequest{
		ActorId: "test-actor-id",
		RawDetailsNames: []analyserVariablePb.RawDetailsName{
			analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME,
		},
	}
	sampleRawDetails := &analyserVariablePb.RawDetails{
		RawDetailsName:       analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME,
		RawDetailsDataStatus: analyserVariablePb.RawDetailsDataStatus_RAW_DETAILS_DATA_STATUS_AVAILABLE,
	}
	sampleRawDetailsFactoryReq := &datafetcher.GetRawDetailsRequest{
		ActorId:        sampleReq.ActorId,
		RawDetailsName: analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME,
	}
	tests := []struct {
		name       string
		arg        args
		setupMocks func(m *mockFields)
		want       *rawdetailsfetcher.GetRawDetailsFetcherResponse
		wantErr    bool
	}{
		{
			name: "Error fetching raw details",
			arg: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMocks: func(m *mockFields) {
				m.rawDetailsFactory.EXPECT().GetRawDetails(gomock.Any(), sampleRawDetailsFactoryReq).Return(nil, errors.New("error fetching raw details")).Times(1)
			},
			wantErr: true,
		},
		{
			name: "Successful raw details retrieval",
			arg: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMocks: func(m *mockFields) {
				m.rawDetailsFactory.EXPECT().GetRawDetails(gomock.Any(), sampleRawDetailsFactoryReq).Return(sampleRawDetails, nil).Times(1)
			},
			want: &rawdetailsfetcher.GetRawDetailsFetcherResponse{
				RORawDetailsMap: rorawdetailsmap.NewReadOnlyRawDetailsMap(map[analyserVariablePb.RawDetailsName]*analyserVariablePb.RawDetails{
					analyserVariablePb.RawDetailsName_RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME: sampleRawDetails,
				}),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctl := gomock.NewController(t)
			m := initMocks(ctl)
			tt.setupMocks(m)
			r := rawdetailsfetcher.NewRawDetailsFetcher(m.rawDetailsFactory)
			got, err := r.GetRawDetailsFetcher(tt.arg.ctx, tt.arg.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRawDetailsFetcher() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := deep.Equal(got, tt.want); len(diff) > 0 {
				t.Errorf("GetRawDetailsFetcher() got and want are different. \ngot :%v\nwant:%v\ndiff:%v", got, tt.want, diff)
			}
		})
	}
}

func initMocks(ctl *gomock.Controller) *mockFields {
	return &mockFields{
		rawDetailsFactory: mockRawFactory.NewMockIRawDetailsFactory(ctl),
	}
}
