// Code generated by tools/conf_gen/dynamic_conf_gen.go
package worker

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "application":
		return obj.Application.Get(dynamicFieldPath[1:])
	case "vortexconf":
		return obj.VortexConf.Get(dynamicFieldPath[1:])
	case "vortexconnectorconf":
		return obj.VortexConnectorConf.Get(dynamicFieldPath[1:])
	case "migratetopgdbconf":
		return obj.MigrateToPgdbConf.Get(dynamicFieldPath[1:])
	case "pgdbarchivalconf":
		return obj.PgdbArchivalConf.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VortexConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "tablespecificconfigs":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.TableSpecificConfigs, nil
		case len(dynamicFieldPath) > 1:

			return obj.TableSpecificConfigs[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.TableSpecificConfigs, nil
	case "default":
		return obj.Default.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VortexConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DefaultSyncConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "bulkparams":
		return obj.BulkParams.Get(dynamicFieldPath[1:])
	case "deltaparams":
		return obj.DeltaParams.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DefaultSyncConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Params) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "batchsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BatchSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BatchSize, nil
	case "buffertime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BufferTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BufferTime, nil
	case "steptime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StepTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StepTime, nil
	case "batchdelay":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BatchDelay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BatchDelay, nil
	case "minsyncinterval":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinSyncInterval\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinSyncInterval, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Params", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TableConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "overrideconfigs":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.OverrideConfigs, nil
		case len(dynamicFieldPath) > 1:

			return obj.OverrideConfigs[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.OverrideConfigs, nil
	case "syncconfig":
		return obj.SyncConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TableConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SyncConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "bulksyncbatchsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BulkSyncBatchSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BulkSyncBatchSize, nil
	case "deltasyncbatchsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DeltaSyncBatchSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DeltaSyncBatchSize, nil
	case "bulksyncdelay":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BulkSyncDelay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BulkSyncDelay, nil
	case "deltasyncdelay":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DeltaSyncDelay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DeltaSyncDelay, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SyncConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TimeRangeConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "starttime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StartTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StartTime, nil
	case "endtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EndTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EndTime, nil
	case "syncconfig":
		return obj.SyncConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TimeRangeConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VortexConnectorConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "kafkasinkconnectorconf":
		return obj.KafkaSinkConnectorConf.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VortexConnectorConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *KafkaSinkConnectorConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "flushmessages":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FlushMessages\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FlushMessages, nil
	case "flushmaxmessages":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FlushMaxMessages\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FlushMaxMessages, nil
	case "flushmaxbytes":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FlushMaxBytes\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FlushMaxBytes, nil
	case "maxmessagebytes":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxMessageBytes\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxMessageBytes, nil
	case "flushfrequency":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FlushFrequency\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FlushFrequency, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for KafkaSinkConnectorConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MigrateToPgdbConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "tablespecificconfigs":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.TableSpecificConfigs, nil
		case len(dynamicFieldPath) > 1:

			return obj.TableSpecificConfigs[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.TableSpecificConfigs, nil
	case "default":
		return obj.Default.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MigrateToPgdbConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PgdbArchivalConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "tablespecificconfigs":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.TableSpecificConfigs, nil
		case len(dynamicFieldPath) > 1:

			return obj.TableSpecificConfigs[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.TableSpecificConfigs, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PgdbArchivalConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ArchivalParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "batchduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BatchDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BatchDuration, nil
	case "batchdelay":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BatchDelay\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BatchDelay, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ArchivalParams", strings.Join(dynamicFieldPath, "."))
	}
}
