package migratetopgdb

import (
	"context"
	"fmt"
	"time"

	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	vortexpb "github.com/epifi/gamma/api/nebula/activity/vortex"
	"github.com/epifi/gamma/nebula/config/worker"
	"github.com/epifi/gamma/nebula/config/worker/genconf"
)

// nolint:dupl
func updateParamsFromDynamicConfig(ctx context.Context, conf *genconf.MigrateToPgdbConfig, srcTableName, destTableName, srcDbName, destDbName string, syncType vortexpb.SyncType) *genconf.Params {
	lg := activity.GetLogger(ctx)

	tableKey := fmt.Sprintf("%s-%s-%s-%s", srcDbName, srcTableName, destDbName, destTableName)
	tableConfig, exist := conf.TableSpecificConfigs().Load(tableKey)
	if !exist {
		lg.Info("No dynamic found for this table....", zap.String(logger.SYNC_TYPE, string(syncType)), zap.String("table_key", tableKey))
		return getDefaultParams(conf, syncType)
	}
	newSyncParams, _ := genconf.NewParams()
	switch syncType {
	case vortexpb.SyncType_SYNC_TYPE_BULK:
		staticDefaultParam := &worker.Params{
			BatchSize:       conf.Default().BulkParams().BatchSize(),
			BatchDelay:      conf.Default().BulkParams().BatchDelay(),
			BufferTime:      conf.Default().BulkParams().BufferTime(),
			MinSyncInterval: conf.Default().BulkParams().MinSyncInterval(),
			StepTime:        conf.Default().BulkParams().StepTime(),
		}
		err := newSyncParams.Set(staticDefaultParam, true, nil)
		if err != nil {
			lg.Error(fmt.Sprintf("error while setting bulk params %s err %v", tableConfig.BulkSyncDelay(), err))
		}
		if tableConfig.BulkSyncDelay() != "" {
			delay, parsingErr := parseTimeDuration(ctx, tableConfig.BulkSyncDelay())
			if parsingErr != nil {
				lg.Error(fmt.Sprintf("error while parsing config time %s err: %v", tableConfig.BulkSyncDelay(), err))
			} else {
				err = newSyncParams.SetBatchDelay(delay, true, nil)
				if err != nil {
					lg.Error(fmt.Sprintf("error while setting batch delay %s err: %v", tableConfig.BulkSyncDelay(), err))
				}
			}
		}

		if tableConfig.BulkSyncBatchSize() != 0 {
			err = newSyncParams.SetBatchSize(tableConfig.BulkSyncBatchSize(), true, nil)
			if err != nil {
				lg.Error(fmt.Sprintf("error while setting batch size %d err: %v", tableConfig.BulkSyncBatchSize(), err))
			}
		}
	case vortexpb.SyncType_SYNC_TYPE_DELTA:
		staticDefaultParam := &worker.Params{
			BatchSize:       conf.Default().DeltaParams().BatchSize(),
			BatchDelay:      conf.Default().DeltaParams().BatchDelay(),
			BufferTime:      conf.Default().DeltaParams().BufferTime(),
			MinSyncInterval: conf.Default().DeltaParams().MinSyncInterval(),
			StepTime:        conf.Default().DeltaParams().StepTime(),
		}
		err := newSyncParams.Set(staticDefaultParam, true, nil)
		if err != nil {
			lg.Error(fmt.Sprintf("error while setting delta params %s err: %v", tableConfig.DeltaSyncDelay(), err))
		}
		if tableConfig.DeltaSyncDelay() != "" {
			delay, parsingErr := parseTimeDuration(ctx, tableConfig.DeltaSyncDelay())
			if parsingErr != nil {
				lg.Error(fmt.Sprintf("error while parsing config time %s err: %v", tableConfig.DeltaSyncDelay(), parsingErr))
			} else {
				err = newSyncParams.SetBatchDelay(delay, true, nil)
				if err != nil {
					lg.Error(fmt.Sprintf("error while setting delta sync delay %s err: %v", tableConfig.DeltaSyncDelay(), err))
				}
			}
		}
		if tableConfig.DeltaSyncBatchSize() != 0 {
			err = newSyncParams.SetBatchSize(tableConfig.DeltaSyncBatchSize(), true, nil)
			if err != nil {
				lg.Error(fmt.Sprintf("error while setting delta sync batch size %d", tableConfig.DeltaSyncBatchSize()))
			}
		}
	}
	return newSyncParams

}

func parseTimeDuration(ctx context.Context, delayStr string) (time.Duration, error) {
	logger := activity.GetLogger(ctx)
	delay, err := time.ParseDuration(delayStr)
	if err != nil {
		logger.Error(fmt.Sprintf("Unable to parse time duration from string %s", delayStr))
		return time.Duration(0), err
	}
	return delay, nil
}

func getDefaultParams(conf *genconf.MigrateToPgdbConfig, syncType vortexpb.SyncType) *genconf.Params {
	if syncType == vortexpb.SyncType_SYNC_TYPE_BULK {
		return conf.Default().BulkParams()
	} else if syncType == vortexpb.SyncType_SYNC_TYPE_DELTA {
		return conf.Default().DeltaParams()
	}
	return nil
}
