package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"os"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/salaryprogram/test"
)

var (
	fixturePolicyIssuanceReq1 = &healthinsurancePb.HealthInsurancePolicyIssuanceRequest{
		Id:               "12fed212-8167-4b64-a212-6fb438582ace",
		ActorId:          "actor-1",
		PolicyVendor:     commonvgpb.Vendor_RISKCOVRY,
		VendorRequestId:  "vendor-req-id-1",
		RequestStatus:    healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_CREATED,
		RequestExpiresAt: timestamppb.New(time.Date(2022, 12, 20, 10, 58, 29, 0, time.UTC)),
	}

	fixturePolicyIssuanceReq2 = &healthinsurancePb.HealthInsurancePolicyIssuanceRequest{
		Id:               "12fed212-8167-4b64-a212-6fb438582ac1",
		ActorId:          "actor-1",
		PolicyVendor:     commonvgpb.Vendor_RISKCOVRY,
		VendorRequestId:  "vendor-req-id-2",
		RequestStatus:    healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL,
		RequestExpiresAt: timestamppb.New(time.Date(2022, 12, 20, 10, 58, 29, 0, time.UTC)),
	}

	fixturePolicyDetails1 = &healthinsurancePb.HealthInsurancePolicyDetails{
		Id:                      "2ab63d1c-f1f1-4523-a9c9-89e57c68a21b",
		ActorId:                 fixturePolicyIssuanceReq2.GetActorId(),
		PolicyVendor:            fixturePolicyIssuanceReq2.GetPolicyVendor(),
		VendorPolicyId:          "vendor-policy-id-1",
		PolicyIssuanceRequestId: fixturePolicyIssuanceReq2.GetId(),
		PolicyActiveFrom:        timestamppb.New(time.Date(2022, 12, 20, 10, 58, 29, 0, time.UTC)),
		PolicyMetadata: &healthinsurancePb.PolicyMetadata{
			VendorPolicyId:    "vendor-policy-id-1",
			PolicyActiveFrom:  timestamppb.New(time.Date(2022, 12, 20, 10, 58, 29, 0, time.UTC)),
			SumAssured:        moneyPb.ParseFloat(1000, moneyPb.RupeeCurrencyCode),
			Premium:           moneyPb.ParseFloat(10, moneyPb.RupeeCurrencyCode),
			Coi:               "coi-1",
			CertificateNumber: "certification-number-1",
			InsuredPeople: []*healthinsurancePb.PolicyMetadata_InsuredPeople{
				{
					Name:  "name-1",
					Email: "email-1",
				},
			},
		},
		PolicyRequestSource: healthinsurancePb.PolicyRequestSource_REQUEST_SOURCE_FI_APP,
	}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {

	config, db, teardown := test.InitTestServer(true)

	// init policy details dao test suite
	policyDetailsDao := NewPGDBPolicyDetailsDao(db)
	policyDetailsDaoTs = newPolicyDetailsDaoTestSuite(db, policyDetailsDao, config)

	// init policy issuance request dao test suite
	policyIssuanceReqDao := NewPGDBPolicyIssuanceRequestDao(db)
	policyIssuanceReqDaoTs = newPolicyIssuanceRequestDaoTestSuite(db, policyIssuanceReqDao, config)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)

}
