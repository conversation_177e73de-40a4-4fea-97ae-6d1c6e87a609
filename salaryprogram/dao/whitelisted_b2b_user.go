package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"errors"
	"fmt"
	"time"

	errorsPkg "github.com/pkg/errors"
	"golang.org/x/net/context"
	gormv2 "gorm.io/gorm"
	"gorm.io/gorm/clause"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	gormctx "github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/salaryprogram/dao/model"
)

type PGDBWhitelistedB2bUserDao struct {
	db *gormv2.DB
}

func NewPGDBWhitelistedB2bUserDao(db pkgTypes.SalaryprogramPGDB) *PGDBWhitelistedB2bUserDao {
	return &PGDBWhitelistedB2bUserDao{
		db: db,
	}
}

var _ IWhitelistedB2bUserDao = &PGDBWhitelistedB2bUserDao{}

func (p *PGDBWhitelistedB2bUserDao) CreateInBatches(ctx context.Context, whitelistedB2bUsers []*salaryprogramPb.WhitelistedB2BUser, batchSize int) error {
	defer metric_util.TrackDuration("salaryprogram/dao", "PGDBWhitelistedB2bUserDao", "CreateInBatches", time.Now())
	if err := p.validateCreateInBatchesReq(whitelistedB2bUsers, batchSize); err != nil {
		return errorsPkg.Wrap(epifierrors.ErrInvalidArgument, err.Error())
	}

	db := gormctx.FromContextOrDefault(ctx, p.db)

	whitelistedB2bUsersDbModels := make([]*model.WhitelistedB2bUser, len(whitelistedB2bUsers))
	for idx, whitelistedB2bUser := range whitelistedB2bUsers {
		whitelistedB2bUsersDbModels[idx] = model.NewWhitelistedB2bUserModel(whitelistedB2bUser)
	}

	// update columns if there is conflict with phone_number (duplicate phone number passed)
	if err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "phone_number"}},
		DoUpdates: clause.AssignmentColumns([]string{"employer_id", "hrms_management_vendor", "vendor_employer_id", "vendor_user_id"}),
	}).CreateInBatches(&whitelistedB2bUsersDbModels, batchSize).Error; err != nil {
		return fmt.Errorf("error while creating db entries in batches, err: %w", err)
	}

	return nil
}

func (p *PGDBWhitelistedB2bUserDao) GetByPhoneNumber(ctx context.Context, phoneNumber string) (*salaryprogramPb.WhitelistedB2BUser, error) {
	defer metric_util.TrackDuration("salaryprogram/dao", "PGDBWhitelistedB2bUserDao", "GetByPhoneNumber", time.Now())
	if phoneNumber == "" {
		return nil, fmt.Errorf("empty phone number received")
	}

	db := gormctx.FromContextOrDefault(ctx, p.db)

	whitelistedB2bUsersDbModel := &model.WhitelistedB2bUser{}
	if err := db.Take(whitelistedB2bUsersDbModel, "phone_number = ?", phoneNumber).Error; err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, fmt.Errorf("error fetching by phone number, err: %w", err)
	}

	return whitelistedB2bUsersDbModel.GetProto(), nil
}

func (p *PGDBWhitelistedB2bUserDao) validateCreateInBatchesReq(whitelistedB2bUsers []*salaryprogramPb.WhitelistedB2BUser, batchSize int) error {
	if len(whitelistedB2bUsers) == 0 {
		return fmt.Errorf("whitelistedB2bUsers list cannot be empty")
	}
	if batchSize == 0 {
		return fmt.Errorf("batchSize cannot be zero")
	}

	for _, whitelistedB2bUser := range whitelistedB2bUsers {
		if len(whitelistedB2bUser.GetPhoneNumber()) != 12 {
			return fmt.Errorf("length of phone number should 12, gotLength: %v", len(whitelistedB2bUser.GetPhoneNumber()))
		}
		parsedPhoneNum, err := commontypes.ParsePhoneNumber(whitelistedB2bUser.GetPhoneNumber())
		if err != nil {
			return fmt.Errorf("error parsing the phone number, err: %w", err)
		}
		if whitelistedB2bUser.GetEmployerId() == "" {
			return fmt.Errorf("employerId cannot be empty, phoneNumber: %s", mask.GetMaskedPhoneNumber(parsedPhoneNum, "x"))
		}
		if (whitelistedB2bUser.GetVendorUserId() == "") != (whitelistedB2bUser.GetVendorEmployerId() == "") {
			return fmt.Errorf("VendorUserId and VendorEmployerId should be present together or should be empty together, VendorEmployerId: %s, VendorUserId: %s", whitelistedB2bUser.GetVendorEmployerId(), whitelistedB2bUser.GetVendorUserId())
		}
		if whitelistedB2bUser.GetVendorUserId() != "" && whitelistedB2bUser.GetVendorEmployerId() != "" && whitelistedB2bUser.GetHrmsManagementVendor() == commonvgpb.Vendor_VENDOR_UNSPECIFIED {
			return fmt.Errorf("HrmsManagementVendor cannot be unspecified if VendorEmployerId and VendorUserId are present")
		}
	}

	return nil
}
