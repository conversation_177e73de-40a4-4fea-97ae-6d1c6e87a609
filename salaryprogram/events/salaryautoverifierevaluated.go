package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/crypto"
	eventsPkg "github.com/epifi/be-common/pkg/events"
)

type SalaryAutoVerifierEvaluatedEvent struct {
	ActorId    string
	SessionId  string
	ProspectId string
	Timestamp  time.Time
	EventId    string
	EventType  string
	EventName  string

	// custom properties
	OrderId                  string
	EmployerId               string
	VerificationStatus       string
	FailureReason            string
	DsNameMatchRpcReqResList string
}

func NewSalaryAutoVerifierEvaluatedEvent(actorId string, orderId, employerId, verificationStatus, failureReason, dsNameMatchRpcReqResList string) *SalaryAutoVerifierEvaluatedEvent {
	return &SalaryAutoVerifierEvaluatedEvent{
		ActorId:                  actorId,
		Timestamp:                time.Now(),
		EventId:                  uuid.New().String(),
		EventType:                eventsPkg.EventTrack,
		EventName:                SalaryAutoVerifierEvaluatedEventName,
		OrderId:                  crypto.GetSHA256Hash(orderId),
		EmployerId:               employerId,
		VerificationStatus:       verificationStatus,
		FailureReason:            failureReason,
		DsNameMatchRpcReqResList: dsNameMatchRpcReqResList,
	}
}

func (s *SalaryAutoVerifierEvaluatedEvent) GetEventId() string {
	return s.EventId
}

func (s *SalaryAutoVerifierEvaluatedEvent) GetUserId() string {
	return s.ActorId
}

func (s *SalaryAutoVerifierEvaluatedEvent) GetProspectId() string {
	return s.ProspectId
}

func (s *SalaryAutoVerifierEvaluatedEvent) GetEventName() string {
	return s.EventName
}

func (s *SalaryAutoVerifierEvaluatedEvent) GetEventType() string {
	return s.EventType
}

func (s *SalaryAutoVerifierEvaluatedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *SalaryAutoVerifierEvaluatedEvent) GetEventTraits() map[string]interface{} {
	return nil
}
