package valuegenerator

import deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"

type BarChartData interface {
	GetPrimaryValue() string
	GetSecondaryValue() string
	GetBarChartItemsData() []BarChartItemData
	GetBenchmarkValue() float64
}

type BarChartItemData interface {
	GetTopValue() string
	GetBottomIconUrl() string
	GetBarValue() float64
	GetDeeplinkForBar() *deeplinkPb.Deeplink
	GetBarColor() string
}

type UnimplementedDeeplinkForBar struct{}

func (u *UnimplementedDeeplinkForBar) GetDeeplinkForBar() *deeplinkPb.Deeplink {
	return nil
}

type SummaryData interface {
	GetSummaryValue() string
}

type SummaryDataWithTitle interface {
	SummaryData
	GetSummaryTitle() string
}

type NumberCardData interface {
	GetNumberCardValue() string
}

type NumberCardDataWithTitle interface {
	NumberCardData
	GetNumberCardTitle() string
}

type TextExplainerSectionData interface {
	GetExplainerText() string
}

type DonutCardData interface {
	GetPrimaryValue() string
	GetSecondaryValue() string
	GetDonutCardSlices() []DonutCardSlice
}

type DonutCardSlice interface {
	GetSliceValue() float64
	GetToolTipTexts() (string, string)
	GetDonutColor() string
}

type AreaChartData interface {
	GetInfoTitle() string
	GetInfoValue() string
	GetLinePoints() []LinePointData
	GetHorizontalAxisLabels() []HorizontalAxisLabelData
}

type LinePointData interface {
	GetXYCoordinates() (float64, float64)
	GetSliderLabels() []string
}

type HorizontalAxisLabelData interface {
	GetXPosition() float64
	GetLabel() string
}

type HorizontalAxisLabel struct {
	XPosition float64
	Label     string
}

func (h *HorizontalAxisLabel) GetXPosition() float64 {
	return h.XPosition
}

func (h *HorizontalAxisLabel) GetLabel() string {
	return h.Label
}
