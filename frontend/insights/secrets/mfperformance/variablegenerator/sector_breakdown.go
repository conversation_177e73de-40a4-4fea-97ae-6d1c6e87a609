package variablegenerator

import (
	"context"
	"sort"
	"strings"

	"github.com/shopspring/decimal"

	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/money"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	mfAnalyserVariablePb "github.com/epifi/gamma/api/analyser/variables/mutualfund"
	secretsModelPb "github.com/epifi/gamma/api/insights/secrets/model"
	"github.com/epifi/gamma/api/investment/mutualfund"
	secretErrors "github.com/epifi/gamma/frontend/insights/secrets/errors"
	"github.com/epifi/gamma/frontend/insights/secrets/mfperformance/store"
)

type MfSectorBreakdownVariableGenerator struct {
}

func NewMfSectorBreakdownVariableGenerator() *MfSectorBreakdownVariableGenerator {
	return &MfSectorBreakdownVariableGenerator{}
}

func (m *MfSectorBreakdownVariableGenerator) GetAnalysisVariable(ctx context.Context, secretConfig *secretsModelPb.Secret, store *store.MfPerformanceStore, variableName analyserVariablePb.AnalysisVariableName) (*analyserVariablePb.AnalysisVariable, error) {
	var sectorBreakdownDetail []*mfAnalyserVariablePb.MfSectorDetail

	analysedSchemes := store.GetAnalysedSchemes()
	var mapEquitySectorToPortfolioVal = make(map[mutualfund.GlobalEquitySector]float64)
	var mapBondSectorToPortfolioVal = make(map[mutualfund.GlobalbondSector]float64)
	var totalPortfolioValue decimal.Decimal

	for _, scheme := range analysedSchemes {
		totalPortfolioValue = totalPortfolioValue.Add(money.ToDecimal(scheme.EnrichedAnalytics.GetAnalytics().GetSchemeDetails().GetCurrentValue()))
		equitySectorMap := scheme.SchemeDetail.GetFundFundamentalDetails().GetGlobalEquitySectors().GetEquitySectors()
		for sector, value := range equitySectorMap {
			sectorEnum := goUtils.Enum(sector, mutualfund.GlobalEquitySector_value, mutualfund.GlobalEquitySector_GlobalEquitySector_UNSPECIFIED)
			// multiplying sector weight in fund with current value of fund to get sector allocation in portfolio
			// once we get sector allocation of each sector in portfolio, we can calculate percentage of each sector in portfolio by dividing sector allocation by total portfolio value
			// same in case of bond sector
			sectorAllocation, _ := money.ToDecimal(scheme.EnrichedAnalytics.GetAnalytics().GetSchemeDetails().GetCurrentValue()).Mul(decimal.NewFromFloat32(value)).Div(decimal.NewFromInt(100)).Float64()
			mapEquitySectorToPortfolioVal[sectorEnum] += sectorAllocation
		}

		bondSectorMap := scheme.SchemeDetail.GetFundFundamentalDetails().GetGlobalBondSectors().GetBondSectors()
		for sector, value := range bondSectorMap {
			if strings.Contains(sector, "SUPER_SECTOR_") {
				sectorEnum := goUtils.Enum(sector, mutualfund.GlobalbondSector_value, mutualfund.GlobalbondSector_GlobalbondSector_UNSPECIFIED)
				sectorAllocation, _ := money.ToDecimal(scheme.EnrichedAnalytics.GetAnalytics().GetSchemeDetails().GetCurrentValue()).Mul(decimal.NewFromFloat32(value)).Div(decimal.NewFromInt(100)).Float64()
				mapBondSectorToPortfolioVal[sectorEnum] += sectorAllocation
			}
		}
	}

	totalPortfolioValueFloat, _ := totalPortfolioValue.Float64()
	if totalPortfolioValueFloat == 0 {
		return nil, secretErrors.NoDataToBuildSecretPostFilters
	}

	for sector, sectorAllocation := range mapEquitySectorToPortfolioVal {
		sectorPercentage := (sectorAllocation / totalPortfolioValueFloat) * 100
		sectorBreakdownDetail = append(sectorBreakdownDetail, &mfAnalyserVariablePb.MfSectorDetail{
			SectorDetail: &mfAnalyserVariablePb.MfSectorDetail_EquitySectorDetail{
				EquitySectorDetail: &mfAnalyserVariablePb.MfEquitySectorDetail{
					SectorName:                       sector,
					EquitySectorAllocationPercentage: sectorPercentage,
				},
			},
		})
	}

	for sector, sectorAllocation := range mapBondSectorToPortfolioVal {
		sectorPercentage := (sectorAllocation / totalPortfolioValueFloat) * 100
		sectorBreakdownDetail = append(sectorBreakdownDetail, &mfAnalyserVariablePb.MfSectorDetail{
			SectorDetail: &mfAnalyserVariablePb.MfSectorDetail_BondSuperSectorDetail{
				BondSuperSectorDetail: &mfAnalyserVariablePb.MfBondSuperSectorDetail{
					SuperSectorName:                sector,
					BondSectorAllocationPercentage: sectorPercentage,
				},
			},
		})
	}

	sort.Slice(sectorBreakdownDetail, func(i, j int) bool {
		return sectorBreakdownDetail[i].GetPercentageReturns() > sectorBreakdownDetail[j].GetPercentageReturns()
	})

	return &analyserVariablePb.AnalysisVariable{
		AnalysisVariableName: variableName,
		Variable: &analyserVariablePb.AnalysisVariable_MfSectorDetails{
			MfSectorDetails: &mfAnalyserVariablePb.MfSectorDetails{
				SectorDetails: sectorBreakdownDetail,
			},
		},
	}, nil
}
