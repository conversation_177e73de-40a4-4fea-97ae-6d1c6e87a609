package epf

import (
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/consent"
	feEpfPb "github.com/epifi/gamma/api/frontend/insights/epf"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
)

type Service struct {
	feEpfPb.UnimplementedEpfServer

	config           *genconf.Config
	epfClient        beEpfPb.EpfClient
	consentClient    consent.ConsentClient
	userClient       userPb.UsersClient
	deeplinkBuilder  deeplink_builder.IDeeplinkBuilder
	actorClient      actorPb.ActorClient
	releaseEvaluator release.IEvaluator
}

func NewEpfService(
	config *genconf.Config,
	epfClient beEpfPb.EpfClient,
	userClient userPb.UsersClient,
	consentClient consent.ConsentClient,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
	actorClient actorPb.ActorClient,
	releaseEvaluator release.IEvaluator,
) *Service {
	return &Service{
		config:           config,
		epfClient:        epfClient,
		userClient:       userClient,
		consentClient:    consentClient,
		deeplinkBuilder:  deeplinkBuilder,
		actorClient:      actorClient,
		releaseEvaluator: releaseEvaluator,
	}
}
