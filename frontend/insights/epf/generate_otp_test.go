package epf

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"

	consentPb "github.com/epifi/gamma/api/consent"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	feEpfPb "github.com/epifi/gamma/api/frontend/insights/epf"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
)

func TestService_GenerateOtp(t *testing.T) {
	type args struct {
		ctx  context.Context
		conf *config.Config
		req  *feEpfPb.GenerateOtpRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *feEpfPb.GenerateOtpResponse
		wantErr    bool
	}{
		{
			name: "failed to generate otp, failure",
			args: args{
				ctx: context.Background(),
				conf: &config.Config{
					InsightsParams: &config.InsightsParams{EpfConfig: &config.EpfConfig{DisableEpfPassbookOtpFlow: false}},
				},
				req: &feEpfPb.GenerateOtpRequest{
					UanNumber:      "uan-1",
					ClientReqId:    "clientReqId-1",
					IsConsentTaken: false,
				},
			},
			setupMocks: func(f *fields) {
				f.deeplinkBuilder.EXPECT().NetWorthAssetHubDeeplink(gomock.Any(), gomock.Any()).Return(getNetworthDeeplink(), nil)
				f.epfClient.EXPECT().GenerateOtp(gomock.Any(), gomock.Any()).Return(&beEpfPb.GenerateOtpResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &feEpfPb.GenerateOtpResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusInternal(),
				},
				FailureFooterText: failureFooterText,
			},
			wantErr: false,
		},
		{
			name: "successfully generate otp without checking/creating consent",
			args: args{
				ctx: context.Background(),
				conf: &config.Config{
					InsightsParams: &config.InsightsParams{EpfConfig: &config.EpfConfig{DisableEpfPassbookOtpFlow: false}},
				},
				req: &feEpfPb.GenerateOtpRequest{
					UanNumber:      "uan-1",
					ClientReqId:    "clientReqId-1",
					IsConsentTaken: false,
				},
			},
			setupMocks: func(f *fields) {
				f.deeplinkBuilder.EXPECT().NetWorthAssetHubDeeplink(gomock.Any(), gomock.Any()).Return(getNetworthDeeplink(), nil)
				f.epfClient.EXPECT().GenerateOtp(gomock.Any(), gomock.Any()).Return(&beEpfPb.GenerateOtpResponse{
					Status:            rpc.StatusOk(),
					MaskedPhoneNumber: "99XXXXXX99",
				}, nil)
			},
			want: &feEpfPb.GenerateOtpResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				InfoText: infoText,
			},
			wantErr: false,
		},
		{
			name: "failed to fetch consent, failure",
			args: args{
				ctx: context.Background(),
				conf: &config.Config{
					InsightsParams: &config.InsightsParams{EpfConfig: &config.EpfConfig{DisableEpfPassbookOtpFlow: false}},
				},
				req: &feEpfPb.GenerateOtpRequest{
					UanNumber:      "uan-1",
					ClientReqId:    "clientReqId-1",
					IsConsentTaken: true,
				},
			},
			setupMocks: func(f *fields) {
				f.deeplinkBuilder.EXPECT().NetWorthAssetHubDeeplink(gomock.Any(), gomock.Any()).Return(getNetworthDeeplink(), nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), gomock.Any()).Return(&consentPb.FetchConsentResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &feEpfPb.GenerateOtpResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternal(),
					ErrorView: BottomSheetErrorViewSomethingWentWrong(getNetworthDeeplink()),
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create new consent, failure",
			args: args{
				ctx: context.Background(),
				conf: &config.Config{
					InsightsParams: &config.InsightsParams{EpfConfig: &config.EpfConfig{DisableEpfPassbookOtpFlow: false}},
				},
				req: &feEpfPb.GenerateOtpRequest{
					UanNumber:      "uan-1",
					ClientReqId:    "clientReqId-1",
					IsConsentTaken: true,
				},
			},
			setupMocks: func(f *fields) {
				f.deeplinkBuilder.EXPECT().NetWorthAssetHubDeeplink(gomock.Any(), gomock.Any()).Return(getNetworthDeeplink(), nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), gomock.Any()).Return(&consentPb.FetchConsentResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				f.consentClient.EXPECT().RecordConsents(gomock.Any(), gomock.Any()).Return(&consentPb.RecordConsentsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &feEpfPb.GenerateOtpResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternal(),
					ErrorView: BottomSheetErrorViewSomethingWentWrong(getNetworthDeeplink()),
				},
			},
			wantErr: false,
		},
		{
			name: "successfully generate otp with past epf consent present",
			args: args{
				ctx: context.Background(),
				conf: &config.Config{
					InsightsParams: &config.InsightsParams{EpfConfig: &config.EpfConfig{DisableEpfPassbookOtpFlow: false}},
				},
				req: &feEpfPb.GenerateOtpRequest{
					UanNumber:      "uan-1",
					ClientReqId:    "clientReqId-1",
					IsConsentTaken: true,
				},
			},
			setupMocks: func(f *fields) {
				f.deeplinkBuilder.EXPECT().NetWorthAssetHubDeeplink(gomock.Any(), gomock.Any()).Return(getNetworthDeeplink(), nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), gomock.Any()).Return(&consentPb.FetchConsentResponse{
					Status: rpc.StatusOk(),
				}, nil)

				f.epfClient.EXPECT().GenerateOtp(gomock.Any(), gomock.Any()).Return(&beEpfPb.GenerateOtpResponse{
					Status:            rpc.StatusOk(),
					MaskedPhoneNumber: "99XXXXXX99",
				}, nil)
			},
			want: &feEpfPb.GenerateOtpResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				InfoText: infoText,
			},
			wantErr: false,
		},
		{
			name: "successfully generate otp with creating new epf consent",
			args: args{
				ctx: context.Background(),
				conf: &config.Config{
					InsightsParams: &config.InsightsParams{EpfConfig: &config.EpfConfig{DisableEpfPassbookOtpFlow: false}},
				},
				req: &feEpfPb.GenerateOtpRequest{
					UanNumber:      "uan-1",
					ClientReqId:    "clientReqId-1",
					IsConsentTaken: true,
				},
			},
			setupMocks: func(f *fields) {
				f.deeplinkBuilder.EXPECT().NetWorthAssetHubDeeplink(gomock.Any(), gomock.Any()).Return(getNetworthDeeplink(), nil)
				f.consentClient.EXPECT().FetchConsent(gomock.Any(), gomock.Any()).Return(&consentPb.FetchConsentResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)

				f.consentClient.EXPECT().RecordConsents(gomock.Any(), gomock.Any()).Return(&consentPb.RecordConsentsResponse{
					Status: rpc.StatusOk(),
				}, nil)

				f.epfClient.EXPECT().GenerateOtp(gomock.Any(), gomock.Any()).Return(&beEpfPb.GenerateOtpResponse{
					Status:            rpc.StatusOk(),
					MaskedPhoneNumber: "99XXXXXX99",
				}, nil)
			},
			want: &feEpfPb.GenerateOtpResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				InfoText: infoText,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initField(ctrl)
			tt.setupMocks(f)
			dynConf, _ = genconf.NewConfig()
			err := dynConf.Set(tt.args.conf, false, nil)
			if err != nil {
				t.Fatalf("failed to set dynamic config")
			}
			s := NewEpfService(dynConf, f.epfClient, f.userClient, f.consentClient, f.deeplinkBuilder, nil, nil)
			got, err := s.GenerateOtp(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConnectUANsDashboard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetConnectUANsDashboard(), diff: %v", diff)
			}
		})
	}
}
