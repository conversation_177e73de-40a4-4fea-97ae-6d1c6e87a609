// nolint:dupl
package networth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"time"

	datePb "google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/insights/networth"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	networthBeModelPb "github.com/epifi/gamma/api/insights/networth/model"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputbuilder"
)

type PrivateEquity struct {
}

func NewPrivateEquity() *PrivateEquity {
	return &PrivateEquity{}
}

func (a *PrivateEquity) BuildForm(ctx context.Context, req *networthBeFePb.BuildFormRequest) (*networthFePb.NetWorthManualForm, error) {
	decl := req.GetInvestmentDeclaration().GetDeclarationDetails().GetPrivateEquity()
	name := inputbuilder.NewStringBuilder("INVESTMENT NAME", "Investment name", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME.String())
	investedValue := inputbuilder.NewInt64Builder("INVESTED VALUE (₹)", "Invested value (₹)", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE.String())
	currentValue := inputbuilder.NewInt64Builder("CURRENT VALUE (₹)", "Current value (₹)", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE.String())
	dateOfInvestment := inputbuilder.NewOptionalDateBuilder("DATE OF INVESTMENT (OPTIONAL)", "Date of investment (Optional)", networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE.String())
	if decl != nil {
		name.WithValue(decl.GetInvestmentName())
		investedValue.WithValue(decl.GetInvestedValue().GetUnits())
		currentValue.WithValue(decl.GetCurrentValue().GetUnits())
		dateOfInvestment.WithValue(decl.GetInvestmentDate())
	}

	inputSection := networthFePb.NewNetWorthManualFormComponentsSection("Investment Details").
		WithInputComponent(name.Build()).
		WithInputComponent(investedValue.Build()).
		WithInputComponent(currentValue.Build()).
		WithInputComponent(dateOfInvestment.Build())
	form := networthFePb.NewNetWorthManualForm("Private Equity", "Add Investment").
		WithComponentsSection(inputSection)
	// If the data has already been entered by the user, display the option to delete it.
	if decl != nil {
		form = form.WithActionCta(networthFePb.NewAssetActionButton().WithActionType(networthFePb.AssetActionType_ASSET_ACTION_TYPE_DELETE).WithDisplayText(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Remove", "#AA301F", commontypes.FontStyle_BUTTON_S)).WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/networth/delete_icon_manual_asset.png", 13, 13)).WithLeftImagePadding(8)))
	}
	return form, nil
}

func (a *PrivateEquity) ConvertFormInputToInvestmentDeclaration(ctx context.Context, inputComponents []*networth.NetWorthManualFormInputComponent) (*networthBeModelPb.InvestmentDeclaration, error) {
	var (
		investmentName              string
		investedValue, currentValue *moneyPb.Money
		investmentTime              *timestampPb.Timestamp
		investmentDate              *datePb.Date
	)

	for _, component := range inputComponents {
		data := component.GetInputData()
		inputValue := data.GetInputValueFromSingleOption()
		fieldName := goUtils.Enum(data.GetFieldName(), networthFePb.NetworthManualFormFieldName_value, networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_UNSPECIFIED)
		switch fieldName {
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_NAME:
			investmentName = inputValue.GetStringData().GetData().GetValue()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTED_VALUE:
			investedValue = money.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_CURRENT_VALUE:
			currentValue = money.AmountINR(inputValue.GetInt64Data().GetData().GetValue()).GetPb()
		case networthFePb.NetworthManualFormFieldName_NETWORTH_MANUAL_FORM_FIELD_INVESTMENT_DATE:
			investmentDate = inputValue.GetDateData().GetData()
			investmentTime = timestampPb.New(time.Date(int(investmentDate.GetYear()), time.Month(investmentDate.GetMonth()), int(investmentDate.GetDay()), 0, 0, 0, 0, datetime.IST))
		}
	}

	return &networthBeModelPb.InvestmentDeclaration{
		InstrumentType: typesPb.InvestmentInstrumentType_PRIVATE_EQUITY,
		InvestedAmount: investedValue,
		InvestedAt:     investmentTime,
		DeclarationDetails: &networthBeModelPb.OtherDeclarationDetails{
			Details: &networthBeModelPb.OtherDeclarationDetails_PrivateEquity{
				PrivateEquity: &networthBeModelPb.PrivateEquity{
					InvestmentName: investmentName,
					InvestedValue:  investedValue,
					CurrentValue:   currentValue,
					InvestmentDate: investmentDate,
				},
			},
		},
	}, nil
}
