package impl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/insights/networth/enums"

	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/colors"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/insights/networth/common"
	"github.com/epifi/gamma/frontend/insights/networth/deeplink"

	networthCommon "github.com/epifi/gamma/frontend/insights/networth/common"
)

type IslandGenerator struct {
	networthDashboardConfig *networthFePb.NetWorthDashboardConfig
	totalCategories         int
}

type CategoryIconDetails struct {
	x      float32
	y      float32
	width  float32
	height float32
}

var categoryToIconDimensionsMap = map[networthFePb.NetworthCategory]*CategoryIconDetails{
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS: {
		x:      0.65,
		y:      0.35,
		width:  0.147,
		height: 0.238,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS: {
		x:      0.34,
		y:      0.27,
		width:  0.125,
		height: 0.215,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_EPF: {
		x:      0.07,
		y:      0.3,
		width:  0.147,
		height: 0.238,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_JUMP: {
		x:      0.45,
		y:      0.37,
		width:  0.125,
		height: 0.215,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_DEPOSITS: {
		x:      0.20,
		y:      0.28,
		width:  0.130,
		height: 0.220,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_HOME_LOAN: {
		x:      0.06,
		y:      0,
		width:  0.167,
		height: 0.258,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN: {
		x:      0.75,
		y:      0.03,
		width:  0.157,
		height: 0.248,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN: {
		x:      0.58,
		y:      0.13,
		width:  0.137,
		height: 0.228,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN: {
		x:      0.25,
		y:      0.07,
		width:  0.137,
		height: 0.228,
	},
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS: {
		x:      0.43,
		y:      0.03,
		width:  0.130,
		height: 0.220,
	},
}

func NewIslandGenerator(networthDashboardConfig *networthFePb.NetWorthDashboardConfig, totalCategories int) *IslandGenerator {
	return &IslandGenerator{
		networthDashboardConfig: networthDashboardConfig,
		totalCategories:         totalCategories,
	}
}

func (i *IslandGenerator) Generate(
	ctx context.Context,
	actorId string,
	categoriesStatusMap map[networthFePb.NetworthCategory]networthFePb.NetworthCategoryStatus,
	isRefreshRequiredAndCritical bool, dashboardType enums.NetWorthDashBoardType,
) (*networthFePb.NetWorthVisualisation, error) {
	categoryVisualisationItems, err := i.getCategoryVisualisationItems(categoriesStatusMap, i.totalCategories)
	if err != nil {
		return nil, fmt.Errorf("failed to get visualisation items: %w", err)
	}

	var successCategoryCount int
	for _, categoryStatus := range categoriesStatusMap {
		if categoryStatus == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_INITIALIZED ||
			categoryStatus == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE {
			successCategoryCount++
		}
	}

	visualisationSummary := i.getVisualisationSummary(successCategoryCount, i.totalCategories, isRefreshRequiredAndCritical, dashboardType)

	return &networthFePb.NetWorthVisualisation{
		VisualisationType: networthFePb.VisualisationType_VISUALISATION_TYPE_ISLAND,
		Params: &networthFePb.NetWorthVisualisation_IslandParams{
			IslandParams: &networthFePb.IslandParams{
				Categories: categoryVisualisationItems,
				BackgroundImage: &networthFePb.Image{
					ImageUrl:                networthCommon.IslandIconUrl,
					ImageToScreenWidthRatio: 0.9,
					ImageAspectRatio:        0.64,
				},
				Summary: visualisationSummary,
				Message: &commontypes.Text{
					FontColor: colors.ColorOnDarkMediumEmphasis,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: i.getIslandFooterMessage(successCategoryCount, i.totalCategories, dashboardType),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
					},
				},
			},
		},
	}, nil
}

func (i *IslandGenerator) getCategoryVisualisationItems(categoriesStatus map[networthFePb.NetworthCategory]networthFePb.NetworthCategoryStatus, totalCategories int) ([]*networthFePb.CategoryVisualisationItem, error) {
	var categoryVisualisationItems []*networthFePb.CategoryVisualisationItem

	for category, categoryStatus := range categoriesStatus {
		if len(categoryVisualisationItems) >= totalCategories {
			break
		}
		if categoryStatus != networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_INITIALIZED &&
			categoryStatus != networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE {
			continue
		}
		categoryDetails, err := networthCommon.GetCategoryDetailsFromNetworthConfig(category, i.networthDashboardConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to get details for category: %s", category)
		}

		// we will only support showing icons that are given in the categoryToIconDimesionsMap
		categoryIconDimensions, exists := categoryToIconDimensionsMap[category]
		if !exists {
			continue
		}
		categoryVisualisationItems = append(categoryVisualisationItems, &networthFePb.CategoryVisualisationItem{
			CategoryIcon: commontypes.GetVisualElementImageFromUrl(categoryDetails.IconUrl),
			X:            categoryIconDimensions.x,
			Y:            categoryIconDimensions.y,
			Width:        categoryIconDimensions.width,
			Height:       categoryIconDimensions.height,
		})
	}

	return categoryVisualisationItems, nil
}

func (i *IslandGenerator) getVisualisationSummary(successCategoryCount int, totalCategories int, isRefreshRequiredAndCritical bool, dashboardType enums.NetWorthDashBoardType) *networthFePb.VisualisationSummary {
	if successCategoryCount > totalCategories {
		successCategoryCount = totalCategories
	}
	valueColor := colors.ColorSnow
	if isRefreshRequiredAndCritical {
		valueColor = colors.ColorSupportingCherry100
	}
	dashboardTypeTitleCard := "MY NET WORTH"
	if dashboardType == enums.NetWorthDashBoardType_ASSETS {
		dashboardTypeTitleCard = "TOTAL VALUE"

	}
	return &networthFePb.VisualisationSummary{
		Title: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(dashboardTypeTitleCard, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS)).
			WithRightImageUrlHeightAndWidth("https://epifi-icons.pointz.in/networth/information.png", 16, 16).WithRightImagePadding(4).
			WithDeeplink(i.getSummaryTooltipDeeplink(dashboardType)),
		Value: typesUiPb.NewITC().WithTexts(networthCommon.GetTextWithCustomFontStyle("₹", colors.ColorOnDarkMediumEmphasis, networthCommon.InterFontFamily, networthCommon.RegularFontWeight, "26"),
			commontypes.GetTextFromStringFontColourFontStyle("0", valueColor, commontypes.FontStyle_NUMBER_3XL)),
		CategoriesAdded: int32(successCategoryCount),
		TotalCategories: int32(totalCategories),
		BorderColor:     colors.ColorMoss400,
		ShadowColor:     colors.ColorMoss700,
	}
}

func (i *IslandGenerator) getSummaryTooltipDeeplink(dashboardType enums.NetWorthDashBoardType) *deeplinkPb.Deeplink {
	dashboardTypeTitile := "Net Worth"
	infoIconnString := "Net Worth is the total value of your assets minus the total value of your liabilities. It is a measure of your financial health and can be used to track financial progress over time."
	if dashboardType == enums.NetWorthDashBoardType_ASSETS {
		dashboardTypeTitile = "Total Assets"
		infoIconnString = "Total Assets is the total value of your assets. It is a measure of your financial health and can be used to track financial progress over time."
	}
	title := &commontypes.Text{
		FontColor: colors.ColorNight,
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: dashboardTypeTitile,
		},
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
		},
	}
	subtitle := &commontypes.Text{
		FontColor: colors.ColorSlate,
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: infoIconnString,
		},
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
		},
	}
	return deeplink.InformationPopupDeeplink(common.InfoIcon, title, subtitle, colors.ColorSnow, "", nil)
}

func (i *IslandGenerator) getIslandFooterMessage(successCategoryCount int, totalCategories int, dashboardType enums.NetWorthDashBoardType) string {
	switch {
	case dashboardType == enums.NetWorthDashBoardType_ASSETS:
		return "Add all your assets to get the accurate value"
	case successCategoryCount < 10:
		return fmt.Sprintf("Add %d assets or liabilities to view your Net Worth", totalCategories)
	case successCategoryCount >= 10:
		return "Your Island is full, but you can still add assets to your total"
	default:
		return ""
	}
}
