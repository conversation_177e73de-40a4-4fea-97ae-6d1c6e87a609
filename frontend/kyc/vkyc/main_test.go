package vkyc

import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	mocksBC "github.com/epifi/gamma/api/bankcust/mocks"
	mockVkyc "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	feTest "github.com/epifi/gamma/frontend/test"
	timeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
)

var conf *config.Config
var genConf *genconf.Config

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, genConf, teardown = feTest.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockDependencies struct {
	mockBankCustClient *mocksBC.MockBankCustomerServiceClient
	mockVkycClient     *mockVkyc.MockVKYCClient
	mockSavingsClient  *mockSavings.MockSavingsClient
	timeClient         *timeMocks.MockTime
}

func newMockDependencies(t *testing.T) (*Service, *mockDependencies) {
	ctrl := gomock.NewController(t)
	mockBankCustClient := mocksBC.NewMockBankCustomerServiceClient(ctrl)
	mockVkycClient := mockVkyc.NewMockVKYCClient(ctrl)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctrl)
	timeClient := timeMocks.NewMockTime(ctrl)
	svc := &Service{
		bcClient:      mockBankCustClient,
		beVkycClient:  mockVkycClient,
		savingsClient: mockSavingsClient,
		timeClient:    timeClient,
		genConf:       genConf,
	}
	md := &mockDependencies{
		mockBankCustClient: mockBankCustClient,
		mockVkycClient:     mockVkycClient,
		mockSavingsClient:  mockSavingsClient,
		timeClient:         timeClient,
	}
	return svc, md
}
