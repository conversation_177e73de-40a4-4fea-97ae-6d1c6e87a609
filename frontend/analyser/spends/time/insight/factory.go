package insight

import (
	"context"
	"fmt"

	beAggPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	categorizerFePb "github.com/epifi/gamma/api/frontend/categorizer"
	"github.com/epifi/gamma/frontend/analyser/spends/time/insight/generator"
)

var (
	InsightGeneratorNotFoundErr = fmt.Errorf("insight generator not found")
)

// Factory returns an insight generator based on the insight name passed to it.
type Factory interface {
	GetInsightGenerator(ctx context.Context, insightName generator.InsightGeneratorName) (Generator, error)
}

type FactoryImpl struct {
	insightGeneratorMapping map[generator.InsightGeneratorName]Generator
}

func NewFactory(categorizerFeClient categorizerFePb.TxnCategorizerClient) *FactoryImpl {
	return &FactoryImpl{
		insightGeneratorMapping: map[generator.InsightGeneratorName]Generator{
			generator.ABOVE_AVG_SPENDS_DAYS_COUNT:                generator.NewAboveAverageSpentDaysInsightGen(),
			generator.HIGHEST_COUNT_CATEGORY_IN_TIME_RANGE:       generator.NewHighestCountCategoryInsightGen(categorizerFeClient),
			generator.HIGHEST_AMOUNT_SPENT_WEEKDAY:               generator.NewHighestAmountSpentWeekdayInsightGenInsightGen(),
			generator.HIGHEST_SPEND_DAY_AND_AVG_SPEND_COMPARISON: generator.NewHighestSpendDayCompWithAvgInsightGen(),
			generator.COUNT_DAYS_WITH_SPENDS_BELOW_500:           generator.NewSpendsBelowAmountThresholdInsightGen(beAggPb.GroupByDimension_TIME_DIMENSION_DAY, 500, 5, 10),
			generator.COUNT_DAYS_WITH_SPENDS_HIGHER_THAN_10K:     generator.NewSpendsExceededAmountThresholdInsightGen(beAggPb.GroupByDimension_TIME_DIMENSION_DAY, 10_000, 3, 10),
		},
	}
}

func (s *FactoryImpl) GetInsightGenerator(ctx context.Context, generatorName generator.InsightGeneratorName) (Generator, error) {
	res, present := s.insightGeneratorMapping[generatorName]
	if !present {
		return nil, InsightGeneratorNotFoundErr
	}
	return res, nil
}
