package broker_details

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/wire"

	"go.uber.org/zap"

	connectedAccountExternalPb "github.com/epifi/gamma/api/connected_account/external"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/investment/config"
	"github.com/epifi/be-common/pkg/logger"
)

var IndiaStocksBrokerDetailsWireSet = wire.NewSet(NewBrokerDetailsGetter, wire.Bind(new(IBrokerDetailsGetter), new(*BrokerDetailsGetter)))

const (
	cdslFip = "CDSLFIP"
	nsdlFip = "fip@nsdl"
)

type IBrokerDetailsGetter interface {
	GetNameForAccount(ctx context.Context, accDetails *connectedAccountExternalPb.AccountDetails, dematId string) (string, error)
	GetIconUrlForAccount(ctx context.Context, accDetails *connectedAccountExternalPb.AccountDetails, dematId string) (string, error)
	GetNameAndIconForAccount(ctx context.Context, accDetails *connectedAccountExternalPb.AccountDetails, dematId string) (string, string, error)
}

type BrokerDetailsGetter struct {
	dematDPDetailsMap map[string]*config.DematDepositoryParticipantDetails
}

func NewBrokerDetailsGetter(conf *genConf.Config) *BrokerDetailsGetter {
	dematDPDetailsMap, err := config.LoadDematDepositaryParticipantDetailsMap(conf.Investment().DematDepositoryParticipantDetailsConfigJson())
	if err != nil {
		logger.Panic(fmt.Sprintf("error in loading demat DP details map from config : %s", err.Error()))
	}
	return &BrokerDetailsGetter{
		dematDPDetailsMap: dematDPDetailsMap,
	}
}

func (bd *BrokerDetailsGetter) GetNameAndIconForAccount(ctx context.Context, accDetails *connectedAccountExternalPb.AccountDetails, dematId string) (string, string, error) {
	dpId, err := getDepositaryParticipantIdFromDematAccId(ctx, accDetails, dematId)
	if err != nil {
		return "", "", fmt.Errorf("failed to get dp id for account id : %s %w", accDetails.GetAccountId(), err)
	}
	if details, found := bd.dematDPDetailsMap[dpId]; found {
		brokerName := details.GetName()
		iconUrl := details.IconUrl
		if iconUrl == "" {
			iconUrl = accDetails.GetFipMeta().GetLogoUrl()
		}
		if brokerName == "" {
			return "", iconUrl, fmt.Errorf("both registered and display name empty for dpId : %s in map", dpId)
		}
		return brokerName, iconUrl, nil
	}
	logger.Error(ctx, "dp id not found in DP details map", zap.String(logger.DP_ID, dpId))
	return accDetails.GetFipMeta().GetDisplayName(), accDetails.GetFipMeta().GetLogoUrl(), nil
}

func (bd *BrokerDetailsGetter) GetNameForAccount(ctx context.Context, accDetails *connectedAccountExternalPb.AccountDetails, dematId string) (string, error) {
	dpId, err := getDepositaryParticipantIdFromDematAccId(ctx, accDetails, dematId)
	if err != nil {
		return "", fmt.Errorf("failed to get dp id for account id : %s %w", accDetails.GetAccountId(), err)
	}
	if details, found := bd.dematDPDetailsMap[dpId]; found {
		brokerName := details.GetName()
		if brokerName == "" {
			return "", fmt.Errorf("both registered and display name empty for dpId : %s in map", dpId)
		}
		return brokerName, nil
	}
	logger.Error(ctx, "dp id not found in DP details map", zap.String(logger.DP_ID, dpId))
	return accDetails.GetFipMeta().GetDisplayName(), nil
}

func (bd *BrokerDetailsGetter) GetIconUrlForAccount(ctx context.Context, accDetails *connectedAccountExternalPb.AccountDetails, dematId string) (string, error) {
	dpId, err := getDepositaryParticipantIdFromDematAccId(ctx, accDetails, dematId)
	if err != nil {
		return "", fmt.Errorf("failed to get dp id for account id : %s %w", accDetails.GetAccountId(), err)
	}
	if details, found := bd.dematDPDetailsMap[dpId]; found {
		if details.IconUrl != "" {
			return details.IconUrl, nil
		}
		return accDetails.GetFipMeta().GetLogoUrl(), nil
	}
	logger.Error(ctx, "dp id not found in demat DP details map", zap.String(logger.DP_ID, dpId))
	return accDetails.GetFipMeta().GetLogoUrl(), nil
}

// returns depositary participant id from demat acc id
// currently cdsl demat accountId follow pattern 120XXXXXZZZZZZZZ where XXXXX is dp id for nsd
// nsdl demat acc id has pattern XXXXXXXXYYYYYYYY where XXXXXXXX is dp id for nsdl
// both demat account ids are of length 16
func getDepositaryParticipantIdFromDematAccId(ctx context.Context, accDetails *connectedAccountExternalPb.AccountDetails, dematId string) (string, error) {
	fipId := accDetails.GetFipId()
	switch fipId {
	case cdslFip:
		if len(dematId) != 16 {
			return "", fmt.Errorf("cdsl demat acc id %s length != 16, failure", dematId)
		}
		if !strings.HasPrefix(dematId, "120") {
			logger.WarnWithCtx(ctx, fmt.Sprintf("cdsl demat acc id starts with %s instead of 120", dematId[:3]))
		}
		return dematId[3:8], nil
	case nsdlFip:
		if len(dematId) != 16 {
			return "", fmt.Errorf("nsdl demat acc id length != 16, failure")
		}
		return dematId[:8], nil
	default:
		return "", fmt.Errorf("unhandled fip id : %s", fipId)
	}
}
