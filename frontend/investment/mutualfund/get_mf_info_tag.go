package mutualfund

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"math"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/api/typesv2/ui"
)

var (
	tagInfoIcon = "https://epifi-icons.pointz.in/investments/mf-tag-info.png"
	dotIconImg  = "https://epifi-icons.pointz.in/investments/mf-tag-dot.png"
)

// nolint: funlen
func getTagsForMutualFund(fund *mfPb.MutualFund, entry string) []*ui.IconTextComponent {
	tags := make([]*ui.IconTextComponent, 0)

	if fund.CategoryName == mfPb.MutualFundCategoryName_ELSS_TAX_SAVING {
		tags = append(tags, &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(DisplayText3YearLockIn, "#6A6D70", commontypes.FontStyle_BODY_S)},
		})
	}

	if entry == ENTRY_POINT_FUND_DETAILS || entry == ENTRY_POINT_INVESTED_FUND_DETAILS {
		if fund.FiContent != nil && fund.FiContent.UniqueSpecs != "" {
			tags = append(tags, &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(getEntryPointSpecificTagString(fund.FiContent.UniqueSpecs, entry), "#6A6D70", commontypes.FontStyle_BODY_S)},
			})
		}
	}
	if entry == ENTRY_POINT_ALL_FUNDS_LIST {
		if fund.PlanType != mfPb.PlanType_PLAN_TYPE_UNSPECIFIED {
			tags = append(tags, &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(getEntryPointSpecificTagString(PlanTypeToText[fund.PlanType], entry), "#6A6D70", commontypes.FontStyle_BODY_S)},
			})
		}
	}

	if fund.AssetClass != mfPb.AssetClass_ASSET_CLASS_UNSPECIFIED {
		tags = append(tags, &ui.IconTextComponent{
			RightVisualElement: getTagInfoVisualElement(),
			// since in ios client visual element support is not present in MF component
			RightIcon: getTagInfoImage(),
			Texts:     []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(getEntryPointSpecificTagString(getAssetClass(fund.AssetClass, fund.CategoryName), entry), "#6A6D70", commontypes.FontStyle_BODY_S)},
			Deeplink:  getTagInfoPopupDeeplink(getAssetClassInfoHeader(fund.AssetClass, fund.CategoryName), getAssetClassInfoDescription(fund.AssetClass, fund.CategoryName)),
		})
	}

	if fund.ComputedMinSipAmount > 0 && fund.ComputedMinSipAmount != math.MaxInt32 {
		if fund.ComputedMinSipAmount <= int32(fund.GetTxnConstraints().GetNewpMnval().GetUnits()) {
			tags = append(tags, &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Min SIP ₹%v", fund.ComputedMinSipAmount), "#6A6D70", commontypes.FontStyle_BODY_S)},
			})
		} else {
			tags = append(tags, &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Min One-time ₹%v", fund.GetTxnConstraints().GetNewpMnval().GetUnits()), "#6A6D70", commontypes.FontStyle_BODY_S)},
			})
		}
	}

	if entry == ENTRY_POINT_ALL_FUNDS_LIST {
		if fund.OptionType != mfPb.OptionType_OPTION_TYPE_UNSPECIFIED {
			tags = append(tags, &ui.IconTextComponent{
				Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(getEntryPointSpecificTagString(OptionTypeToText[fund.OptionType], entry), "#6A6D70", commontypes.FontStyle_BODY_S)},
			})
		}
	}

	if fund.FundhouseDefinedRiskLevel != mfPb.FundhouseDefinedRiskLevel_FundhouseDefinedRiskLevel_UNSPECIFIED {
		tags = append(tags, &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(getEntryPointSpecificTagString(FundRiskToText[fund.FundhouseDefinedRiskLevel], entry), "#6A6D70", commontypes.FontStyle_BODY_S)},
		})
	}

	if fund.CategoryName != mfPb.MutualFundCategoryName_MutualFundCategoryName_UNSPECIFIED {
		tags = append(tags, &ui.IconTextComponent{
			RightVisualElement: getTagInfoVisualElement(),
			// since in ios client visual element support is not present in MF component
			RightIcon: getTagInfoImage(),
			Texts:     []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(getEntryPointSpecificTagString(FundCategoryToText[fund.CategoryName], entry), "#6A6D70", commontypes.FontStyle_BODY_S)},
			Deeplink:  getTagInfoPopupDeeplink(FundCategoryToText[fund.CategoryName], FundCategoryDescFromEnum[fund.CategoryName]),
		})
	}
	return getFormattedTags(tags)
}

func getFormattedTags(tags []*ui.IconTextComponent) []*ui.IconTextComponent {
	resultTags := make([]*ui.IconTextComponent, 0)
	for ind, tag := range tags {
		if ind != 0 {
			resultTags = append(resultTags, &ui.IconTextComponent{
				LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: dotIconImg},
					Properties: &commontypes.VisualElementProperties{
						Width:  4,
						Height: 4,
					},
					ImageType: commontypes.ImageType_PNG,
				}}},
				// since in ios client visual elment support is not present in MF component
				LeftIcon: &commontypes.Image{ImageType: commontypes.ImageType_PNG, Width: 4,
					Height: 4, ImageUrl: dotIconImg},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					LeftPadding:  8,
					RightPadding: 8,
				},
			})
		}
		// add right padding on last element
		if len(tags)-1 == ind {
			if tag.GetContainerProperties() == nil {
				tag.ContainerProperties = &ui.IconTextComponent_ContainerProperties{}
			}
			tag.ContainerProperties.RightPadding = 16
		}
		// add left padding on first element
		if ind == 0 {
			if tag.GetContainerProperties() == nil {
				tag.ContainerProperties = &ui.IconTextComponent_ContainerProperties{}
			}
			tag.ContainerProperties.LeftPadding = 16
		}
		resultTags = append(resultTags, tag)
	}
	return resultTags
}

func getTagInfoImage() *commontypes.Image {
	return &commontypes.Image{
		ImageType: commontypes.ImageType_PNG,
		ImageUrl:  tagInfoIcon,
		Width:     16,
		Height:    16,
	}
}
func getTagInfoVisualElement() *commontypes.VisualElement {
	return &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
		Source: &commontypes.VisualElement_Image_Url{Url: tagInfoIcon},
		Properties: &commontypes.VisualElementProperties{
			Width:  16,
			Height: 16,
		},
		ImageType: commontypes.ImageType_PNG,
	}}}
}

func getTagInfoPopupDeeplink(title string, subtitle string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				IconUrl:      "https://epifi-icons.pointz.in/investments/informationPop.png",
				TextTitle:    commontypes.GetTextFromStringFontColourFontStyle(title, "#333333", commontypes.FontStyle_SUBTITLE_2),
				TextSubTitle: commontypes.GetTextFromStringFontColourFontStyle(subtitle, "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
				BgColor:      "#E7ECF0",
			},
		},
	}
}
