package home

import (
	"time"

	"github.com/epifi/gamma/api/user/onboarding"
)

const (
	/* Standard home colours */
	GraySteel       = "#A4A4A4"
	Green           = "#13A78E"
	Snow            = "#FFFFFF"
	MonochromeNight = "#313234"
	FiGreen         = "#00B899"
	Ink             = "#262728"
	NightGray       = "#333333"
	Charcoal        = "#383838"
	Slate           = "#878A8D"
	Smoke           = "#E3E7EC"
	Lead            = "#606265"
	DarkLemon       = "#D3B250"
	Fog             = "#ECEEF0"
	MFog            = "#E7ECF0"
	Coin            = "#D9DEE3"
	PastelBerry     = "#CDC6E8"
	MonochromeSteel = "#9DA1A4"
	Chalk           = "#F0F3F7"

	// Messages
	NudgePopupMsg                            = "Here you’ll find tips, actions that need your attention, and more curated just for you"
	DashboardPopupMsg                        = "All new Dashboard to view your finances in one place!"
	FitPopupMsg                              = "Find your FIT rules here"
	InvestPopupMsg                           = "You’ll find Fixed and \nSmart Deposits in Invest"
	AdditionalSettingsMsg                    = "Tap to see\nmore options"
	HideShowBalanceMsg                       = "Hide and unhide your balances from home page for increased privacy"
	AddActionsMsg                            = "Personalise your Home by \nadding your most-used \nactions here"
	PersonaliseShortcutsMsg                  = "Tap to personalise \nyour Shortcuts"
	ReorderMsg                               = "Long press & drag a Shortcut \nto re-order it"
	SeeHowMsg                                = "See how"
	NextBtnText                              = "Next"
	OkGotItBtnText                           = "Ok, got it"
	AddShortcutsText                         = "Add\nShortcuts"
	EditShortcutsText                        = "Edit\nShortcuts"
	AddShortcutsMsg                          = "Tap on the \"+\" sign to \nadd Shortcuts"
	RemoveShortcutsBottomSheetDialogTitle    = "Do you want to remove shortcuts?"
	RemoveShortcutsBottomSheetDialogSubtitle = "Don’t miss out on adding your most \nused actions directly on your Home. It \nhelps you reach your destination faster!"
	NoKeepThemTxt                            = "No, keep them"
	YesRemoveTxt                             = "Yes, remove"

	OldUserWalkThroughTitle          = "Hey %v!\nFi’s got a refresh"
	NewUserWalkThroughTitle          = "Hey %v!\nGlad you’re here"
	ShortcutsWalkthroughTitle        = "Add Shortcuts \nto your home"
	MaxWalkThroughImpressionsPerUser = 5

	// User profile promts
	MinKycPrompt1                = "Complete KYC & unlock rewards"
	MinKycPrompt2                = "Get epic benefits with a salary account"
	FullKycNonSalaryRegPrompt1   = "2% back on monthly spends!"
	FullKycSalaryInactivePrompt1 = "Waiting for your salary to arrive"
	SalaryActivePrompt           = "View Salary benefits"

	// redis key templates
	LayoutSimulatorRedisKeyTemplate = "home:layout:simulator:%s"

	// quest variable path
	DirectToFiLiteVariantVariablePath = "user/Onboarding/DirectToFiLite/Variant"

	// profile notch colours based on various tiering states
	UserDowngradedColour       = "#F09696"
	UserInGraceColour          = "#F1CE9B"
	UserIsMinKycColour         = "#F1CE9B"
	PitchUpgradeToSalaryColour = "#9DC2D0"

	// profile notch radial gradient properties
	RadialGradientColorPlus1  = "#DBB295"
	RadialGradientColorPlus2  = "#84432E"
	RadialGradientPlusCenterX = 107
	RadialGradientPlusCenterY = -26

	RadialGradientColorInfinite1  = "#DBE7F3"
	RadialGradientColorInfinite2  = "#3C5D7E"
	RadialGradientInfiniteCenterX = 100
	RadialGradientInfiniteCenterY = -74

	RadialGradientColorPrime1  = "#A6D9E9"
	RadialGradientColorPrime2  = "#0054BE"
	RadialGradientPrimeCenterX = 100
	RadialGradientPrimeCenterY = 0

	RadialGradientColorRegular1  = "#E1E3E5"
	RadialGradientColorRegular2  = "#909499"
	RadialGradientRegularCenterX = 100
	RadialGradientRegularCenterY = 50

	RadialGradientColorStandard1  = "#C9D3DF"
	RadialGradientColorStandard2  = "#7E8B97"
	RadialGradientStandardCenterX = 100
	RadialGradientStandardCenterY = 87

	RadialGradientColorSalary1  = "#FFE8AD"
	RadialGradientColorSalary2  = "#AB752E"
	RadialGradientSalaryCenterX = 94
	RadialGradientSalaryCenterY = -6

	RadialGradientOuterRadius = 100
)

func (s *Service) isOldUser(getDetailsRes *onboarding.GetDetailsResponse) bool {
	// Days since onboarding
	onboardingTime := getDetailsRes.GetDetails().GetCompletedAt().AsTime()
	currentTime := time.Now()
	minutesSinceOnboarding := currentTime.Sub(onboardingTime).Minutes()

	// Based on onboarding timestamp, show old or new user onboarding flow
	return minutesSinceOnboarding > s.conf.HomeRevampParams.MaxDurationSinceOnbForNewAppWalkthrough.Minutes()
}

func (s *Service) abs(int1 int, int2 int) int {
	if int1 < int2 {
		return int2 - int1
	}
	return int1 - int2
}
