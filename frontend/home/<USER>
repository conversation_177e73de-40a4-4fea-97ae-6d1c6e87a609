package home

import (
	"reflect"
	"testing"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/tiering/external"
)

func Test_getGraceStartTimeFromMovementDetail(t *testing.T) {
	currTime := timestamppb.Now()
	type args struct {
		movementDetail *external.MovementExternalDetails
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			name: "happy flow",
			args: args{
				movementDetail: &external.MovementExternalDetails{
					MovementTimestamp: timestamppb.New(currTime.AsTime().Add(5 * 24 * time.Hour)),
					GraceParams:       &external.GraceParams{Period: 1296000},
				},
			},
			want: currTime.AsTime().Add(-10 * 24 * time.Hour),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getGraceStartTimeFromMovementDetail(tt.args.movementDetail); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>("getGraceStartTimeFromMovementDetail() = %v, want %v", got, tt.want)
			}
		})
	}
}
