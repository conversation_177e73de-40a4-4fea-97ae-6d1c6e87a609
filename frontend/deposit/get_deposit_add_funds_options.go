package deposit

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	feDepositPb "github.com/epifi/gamma/api/frontend/deposit"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
)

func (s *Service) GetDepositAddFundsOptions(
	_ context.Context,
	_ *feDepositPb.GetDepositAddFundsOptionsRequest,
) (*feDepositPb.GetDepositAddFundsOptionsResponse, error) {

	// TODO(mounish): consider deposit account's current value to determine the max amount

	return &feDepositPb.GetDepositAddFundsOptionsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		MinAmount: &types.Money{
			CurrencyCode: "INR",
			Units:        1,
		},
		MaxAmount: &types.Money{
			CurrencyCode: "INR",
			Units:        200000,
		},
	}, nil
}
