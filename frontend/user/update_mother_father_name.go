package user

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strings"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"

	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/user"
	beUserPb "github.com/epifi/gamma/api/user"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	headerPkg "github.com/epifi/gamma/pkg/frontend/header"
)

// nolint:funlen

var actorIdsToSkippParentsNameCheck = []string{"ACCUSseJXsck240924"}

func (s *Service) UpdateMotherFatherName(ctx context.Context, req *pb.UpdateMotherFatherNameRequest) (*pb.UpdateMotherFatherNameResponse, error) {
	var (
		motherName = strings.TrimSpace(req.GetMotherName())
		fatherName = strings.TrimSpace(req.GetFatherName())
	)

	if motherName == "" || fatherName == "" {
		return &pb.UpdateMotherFatherNameResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("mother or father name cannot be empty"),
			},
		}, nil
	}

	actorId := req.GetReq().GetAuth().GetActorId()
	getUserResp, errGetUser := s.client.GetUser(ctx, &beUserPb.GetUserRequest{
		Identifier: &beUserPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if grpcErr := epifigrpc.RPCError(getUserResp, errGetUser); grpcErr != nil {
		logger.Error(ctx, "Error in finding user by actor id", zap.Error(grpcErr))
		return &pb.UpdateMotherFatherNameResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(grpcErr.Error()),
			},
		}, nil
	}

	userId := getUserResp.GetUser().GetId()
	kycName := getUserResp.GetUser().GetProfile().GetKycName()
	panName := getUserResp.GetUser().GetProfile().GetPanName()

	// https://monorail.pointz.in/p/fi-app/issues/detail?id=87934
	if !lo.Contains(actorIdsToSkippParentsNameCheck, actorId) {
		if blockUser, failureReason, resp := s.parentsNameSanityChecks(motherName, fatherName, panName, kycName); blockUser {
			maskedFatherName := maskNames(fatherName)
			maskedMotherName := maskNames(motherName)
			logger.Info(ctx, "parents name sanity check failed", zap.String(logger.ERROR_REASON, failureReason),
				zap.String("FATHER_NAME", maskedFatherName), zap.String("MOTHER_NAME", maskedMotherName))
			return resp, nil
		}
	}
	res, err := s.client.UpdateUser(ctx, &beUserPb.UpdateUserRequest{
		User: &beUserPb.User{
			Id: userId,
			Profile: &beUserPb.Profile{
				FatherName: names.ParseString(fatherName),
				MotherName: names.ParseString(motherName),
			},
		},
		UpdateMask: []beUserPb.UserFieldMask{beUserPb.UserFieldMask_FATHER_NAME, beUserPb.UserFieldMask_MOTHER_NAME},
	})

	if errUpdate := epifigrpc.RPCError(res, err); errUpdate != nil {
		logger.Error(ctx, "failed to update user father, mother name in db", zap.Error(errUpdate))
		return &pb.UpdateMotherFatherNameResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(errUpdate.Error()),
			},
		}, nil
	}

	nextAction, errNextAction := s.getNextAction(ctx, req.GetReq().GetAuth())
	if errNextAction != nil {
		logger.Error(ctx, "failure while calling GetNextAction", zap.Error(errNextAction))
		return &pb.UpdateMotherFatherNameResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(errNextAction.Error()),
			},
		}, nil
	}

	return &pb.UpdateMotherFatherNameResponse{
		RespHeader: headerPkg.SuccessRespHeader(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) isMotherFatherKycNameSame(req *pb.UpdateMotherFatherNameRequest, userKycName, userPanName *commontypes.Name) (bool, *header.ResponseHeader) {
	if strings.EqualFold(strings.TrimSpace(req.GetMotherName()), strings.TrimSpace(req.GetFatherName())) {
		ce := feErrors.NewClientError(feErrors.MOTHER_FATHER_NAME_MATCH)
		respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
		return true, respHeader
	}

	kycName := userKycName.ToString()
	panName := userPanName.ToString()

	if strings.EqualFold(strings.TrimSpace(req.GetMotherName()), strings.TrimSpace(kycName)) ||
		strings.EqualFold(strings.TrimSpace(req.GetMotherName()), strings.TrimSpace(panName)) {
		ce := feErrors.NewClientError(feErrors.MOTHER_KYC_NAME_MATCH)
		respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
		return true, respHeader
	}

	if strings.EqualFold(strings.TrimSpace(req.GetFatherName()), strings.TrimSpace(kycName)) ||
		strings.EqualFold(strings.TrimSpace(req.GetFatherName()), strings.TrimSpace(panName)) {
		ce := feErrors.NewClientError(feErrors.FATHER_KYC_NAME_MATCH)
		respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
		return true, respHeader
	}

	return false, nil
}

func MotherNameStopWordCheck(motherName string, stopWords map[string]bool) *errorsPb.ErrorView {
	if _, ok := stopWords[motherName]; ok {
		return feErrors.NewBottomSheetErrorView("", "Type your mother's actual name",
			"Do not enter words like 'mother', 'amma', or 'maa'. We need you to enter your mother's name as per official records.",
			"", &errorsPb.CTA{
				Type: errorsPb.CTA_DONE,
				Text: "OK, GOT IT!",
			})
	}
	return nil
}

func FatherNameStopWordCheck(fatherName string, stopWords map[string]bool) *errorsPb.ErrorView {
	if _, ok := stopWords[fatherName]; ok {
		return feErrors.NewBottomSheetErrorView("", "Type your father's actual name",
			"We need you to enter your father's name as per official records.",
			"", &errorsPb.CTA{
				Type: errorsPb.CTA_DONE,
				Text: "OK, GOT IT!",
			})
	}

	return nil
}

// nolint:funlen
func (s *Service) parentsNameSanityChecks(motherName, fatherName string, panName, kycName *commontypes.Name) (bool, string, *pb.UpdateMotherFatherNameResponse) {
	stopWords := s.genConfig.ParentNameSanityCheckConfig().StopWords().ToStringArray()

	// mother name stop word check
	if _, found := lo.Find(stopWords, func(word string) bool {
		return strings.EqualFold(word, motherName)
	}); found {
		return true, "MOTHER_NAME_STOP_WORD", &pb.UpdateMotherFatherNameResponse{
			RespHeader: &header.ResponseHeader{
				Status: feErrors.ErrViewStatus,
				ErrorView: feErrors.NewBottomSheetErrorView("", "Type your mother's actual name",
					"Do not enter words like 'mother', 'amma', or 'maa'. We need you to enter your mother's name as per official records.",
					"", &errorsPb.CTA{
						Type: errorsPb.CTA_DONE,
						Text: "OK, GOT IT!",
					}),
			},
		}
	}

	// father name stop word check
	if _, found := lo.Find(stopWords, func(word string) bool {
		return strings.EqualFold(word, fatherName)
	}); found {
		return true, "FATHER_NAME_STOP_WORD", &pb.UpdateMotherFatherNameResponse{
			RespHeader: &header.ResponseHeader{
				Status: feErrors.ErrViewStatus,
				ErrorView: feErrors.NewBottomSheetErrorView("", "Type your father's actual name",
					"We need you to enter your father's name as per official records.",
					"", &errorsPb.CTA{
						Type: errorsPb.CTA_DONE,
						Text: "OK, GOT IT!",
					}),
			},
		}
	}

	motherNameFields := strings.Fields(motherName)
	slices.Sort(motherNameFields)
	fatherNameFields := strings.Fields(fatherName)
	slices.Sort(fatherNameFields)

	// Mother = Father name check (Will prevent cases of name reordering example 'FName LName' & 'LName FName' cases)
	if checkIfSlicesAreEqual(motherNameFields, fatherNameFields) && len(motherNameFields) != 0 && len(fatherNameFields) != 0 {
		ce := feErrors.NewClientError(feErrors.MOTHER_FATHER_NAME_MATCH)
		respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
		return true, "FATHER_MOTHER_NAME_MATCH", &pb.UpdateMotherFatherNameResponse{
			RespHeader: respHeader,
		}
	}

	// single string check (Will prevent space names example 'MotherName' & 'Moth erNa me')
	var motherNameString, fatherNameString string
	for _, e := range strings.Fields(motherName) {
		motherNameString += e
	}
	for _, e := range strings.Fields(fatherName) {
		fatherNameString += e
	}

	if strings.EqualFold(motherNameString, fatherNameString) && len(motherNameString) != 0 && len(fatherNameString) != 0 {
		ce := feErrors.NewClientError(feErrors.MOTHER_FATHER_NAME_MATCH)
		respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
		return true, "FATHER_MOTHER_NAME_MATCH", &pb.UpdateMotherFatherNameResponse{
			RespHeader: respHeader,
		}
	}

	userPanName := strings.TrimSpace(panName.ToString())
	userKycName := strings.TrimSpace(kycName.ToString())
	panNameFields := strings.Fields(userPanName)
	slices.Sort(panNameFields)
	kycNameFields := strings.Fields(userKycName)
	slices.Sort(kycNameFields)

	// PAN Name same word check
	if userPanName != "" {
		if checkIfSlicesAreEqual(panNameFields, motherNameFields) {
			ce := feErrors.NewClientError(feErrors.MOTHER_KYC_NAME_MATCH)
			respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
			return true, "MOTHER_NAME_PAN_NAME_MATCH", &pb.UpdateMotherFatherNameResponse{
				RespHeader: respHeader,
			}
		}

		if checkIfSlicesAreEqual(panNameFields, fatherNameFields) {
			ce := feErrors.NewClientError(feErrors.FATHER_KYC_NAME_MATCH)
			respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
			return true, "FATHER_NAME_PAN_NAME_MATCH", &pb.UpdateMotherFatherNameResponse{
				RespHeader: respHeader,
			}
		}
	}

	// KYC Name same word check
	if userKycName != "" {
		if checkIfSlicesAreEqual(kycNameFields, motherNameFields) {
			ce := feErrors.NewClientError(feErrors.MOTHER_KYC_NAME_MATCH)
			respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
			return true, "MOTHER_NAME_KYC_NAME_MATCH", &pb.UpdateMotherFatherNameResponse{
				RespHeader: respHeader,
			}
		}

		if checkIfSlicesAreEqual(kycNameFields, fatherNameFields) {
			ce := feErrors.NewClientError(feErrors.FATHER_KYC_NAME_MATCH)
			respHeader := headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, "", "")
			return true, "FATHER_NAME_KYC_NAME_MATCH", &pb.UpdateMotherFatherNameResponse{
				RespHeader: respHeader,
			}
		}
	}

	return false, "", nil
}

// checkIfSlicesAreEqual function returns if 2 particular slices are equal, it first checks the length and if equal moves on to check each element
func checkIfSlicesAreEqual(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}

	for idx, s := range a {
		if !strings.EqualFold(s, b[idx]) {
			return false
		}
	}

	return true
}

func maskNames(name string) string {
	if len(name)%2 == 0 {
		indexToMask := len(name)/2 - 1

		substringBefore := name[:indexToMask]
		substringAfter := name[indexToMask+2:]
		replacementCharacter := "**"
		modifiedString := substringBefore + replacementCharacter + substringAfter

		return modifiedString

	}
	indexToMask := len(name) / 2

	substringBefore := name[:indexToMask]
	substringAfter := name[indexToMask+2:]
	replacementCharacter := "***"
	modifiedString := substringBefore + replacementCharacter + substringAfter

	return modifiedString

}
