package user

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/accounts"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feupionbpb "github.com/epifi/gamma/api/frontend/upi/onboarding"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	pb "github.com/epifi/gamma/api/frontend/user"
	upiDeeplinkPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func TestService_getCreditAccountActions(t *testing.T) {
	defaultMerchantPaymentDeeplink, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_UPDATE_DEFAULT_MERCHANT_PAYMENT_ACCOUNT_SCREEN, &upiDeeplinkPb.UpdateDefaultMerchantPaymentAccountScreenOptions{
		DerivedAccountId: "test-derived-account-id",
		Action:           feUpiOnbEnumsPb.AccountPreferenceAction_ACCOUNT_PREFERENCE_ACTION_DISABLE,
	})
	defaultMerchantPaymentDeeplinkWithEnablingAction, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_UPDATE_DEFAULT_MERCHANT_PAYMENT_ACCOUNT_SCREEN, &upiDeeplinkPb.UpdateDefaultMerchantPaymentAccountScreenOptions{
		DerivedAccountId: "test-derived-account-id",
		Action:           feUpiOnbEnumsPb.AccountPreferenceAction_ACCOUNT_PREFERENCE_ACTION_ENABLE,
	})
	type mockGetAccounts struct {
		enable bool
		req    *upiOnboardingPb.GetAccountsRequest
		res    *upiOnboardingPb.GetAccountsResponse
		err    error
	}
	tests := []struct {
		name             string
		derivedAccountId string
		actorId          string
		accountId        string
		accType          accounts.Type
		want             []*pb.UpiAction
		wantErr          assert.ErrorAssertionFunc
		mockGetAccounts  *mockGetAccounts
	}{
		{
			name:             "should return valid actions for credit account",
			derivedAccountId: "test-derived-account-id",
			actorId:          "test-actor-id",
			accountId:        "test-account-id",
			accType:          accounts.Type_CREDIT,
			mockGetAccounts: &mockGetAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId:       "test-actor-id",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
					AccountTypes:  []accounts.Type{accounts.Type_CREDIT},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						{
							Id:                  "test-account-id",
							BankName:            "ICICI Bank Credit Card",
							IfscCode:            "ICICI0001",
							AccountType:         accounts.Type_CREDIT,
							MaskedAccountNumber: "123456******01",
							AccountPreference:   upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS,
						},
					},
				},
			},
			want: []*pb.UpiAction{
				{
					Title: "View card transactions",
					ActionDeeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_ALL_TRANSACTIONS_ACCOUNT_FILTER,
						ScreenOptions: &deeplinkPb.Deeplink_AllTransactionsAccountFilterOptions{
							AllTransactionsAccountFilterOptions: &deeplinkPb.AllTransactionsAccountFilterOptions{
								AccountType:      accounts.Type_CREDIT,
								DerivedAccountId: "test-derived-account-id",
							},
						},
					},
					ActionCta: &pb.UpiAction_IconCta{
						IconCta: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/upi/icons/arrow.png",
							Height:   24,
							Width:    24,
						},
					},
					UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
				},
				{
					Title: "Default payment mode for Merchants",
					ActionCta: &pb.UpiAction_ToggleCta{
						ToggleCta: &pb.ToggleCta{
							ToggleState:    pb.ToggleCta_TOGGLE_STATE_ON,
							ToggleDeeplink: defaultMerchantPaymentDeeplink,
						},
					},
					UpiActionCtaState:  pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
					ActionToastMessage: "",
				},
				{
					Title: "Refresh",
					ActionDeeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LIST_UPI_ACCOUNTS, &upiDeeplinkPb.ListUpiAccountsScreenOptions{
						UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_CREDIT_CARD,
						BankInfo: &feupionbpb.BankInfo{
							Name:     "ICICI Bank Credit Card",
							IfscCode: "ICICI0001",
							Logo:     "https://epifi-icons.pointz.in/bank/logo/icici_264px.png",
						},
					}),
					ActionCta: &pb.UpiAction_IconCta{
						IconCta: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/upi/icons/arrow.png",
							Height:   24,
							Width:    24,
						},
					},
					UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
				},
			},
			wantErr: assert.NoError,
		},
		{
			name:             "should return valid actions without refresh action for credit account when that account is not fetched in GetAccountsResponse",
			derivedAccountId: "test-derived-account-id",
			actorId:          "test-actor-id",
			accountId:        "test-account-id",
			accType:          accounts.Type_CREDIT,
			mockGetAccounts: &mockGetAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId:       "test-actor-id",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
					AccountTypes:  []accounts.Type{accounts.Type_CREDIT},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						{
							Id:                  "test-account-id-2",
							BankName:            "ICICI Bank Credit Card",
							IfscCode:            "ICICI0001",
							AccountType:         accounts.Type_CREDIT,
							MaskedAccountNumber: "123456******01",
							AccountPreference:   upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS,
						},
					},
				},
			},
			want: []*pb.UpiAction{
				{
					Title: "View card transactions",
					ActionDeeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_ALL_TRANSACTIONS_ACCOUNT_FILTER,
						ScreenOptions: &deeplinkPb.Deeplink_AllTransactionsAccountFilterOptions{
							AllTransactionsAccountFilterOptions: &deeplinkPb.AllTransactionsAccountFilterOptions{
								AccountType:      accounts.Type_CREDIT,
								DerivedAccountId: "test-derived-account-id",
							},
						},
					},
					ActionCta: &pb.UpiAction_IconCta{
						IconCta: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/upi/icons/arrow.png",
							Height:   24,
							Width:    24,
						},
					},
					UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
				},
				{
					Title: "Default payment mode for Merchants",
					ActionCta: &pb.UpiAction_ToggleCta{
						ToggleCta: &pb.ToggleCta{
							ToggleState:    pb.ToggleCta_TOGGLE_STATE_OFF,
							ToggleDeeplink: defaultMerchantPaymentDeeplinkWithEnablingAction,
						},
					},
					UpiActionCtaState:  pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_DISABLED,
					ActionToastMessage: "One default merchant payment account already found",
				},
			},
			wantErr: assert.NoError,
		},
		{
			name:             "should return valid actions with mark_as_default_account_for_merchant cta as disabled and toggle state as OFF when other credit account is set as default",
			derivedAccountId: "test-derived-account-id",
			actorId:          "test-actor-id",
			accountId:        "test-account-id",
			accType:          accounts.Type_CREDIT,
			mockGetAccounts: &mockGetAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId:       "test-actor-id",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
					AccountTypes:  []accounts.Type{accounts.Type_CREDIT},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						{
							Id:                  "test-account-id",
							BankName:            "ICICI Bank Credit Card",
							IfscCode:            "ICICI0001",
							AccountType:         accounts.Type_CREDIT,
							MaskedAccountNumber: "123456******01",
						},
						{
							Id:                  "test-account-id-2",
							BankName:            "ICICI Bank Credit Card",
							IfscCode:            "ICICI0002",
							AccountType:         accounts.Type_CREDIT,
							MaskedAccountNumber: "123456******02",
							AccountPreference:   upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS,
						},
					},
				},
			},
			want: []*pb.UpiAction{
				{
					Title: "View card transactions",
					ActionDeeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_ALL_TRANSACTIONS_ACCOUNT_FILTER,
						ScreenOptions: &deeplinkPb.Deeplink_AllTransactionsAccountFilterOptions{
							AllTransactionsAccountFilterOptions: &deeplinkPb.AllTransactionsAccountFilterOptions{
								AccountType:      accounts.Type_CREDIT,
								DerivedAccountId: "test-derived-account-id",
							},
						},
					},
					ActionCta: &pb.UpiAction_IconCta{
						IconCta: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/upi/icons/arrow.png",
							Height:   24,
							Width:    24,
						},
					},
					UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
				},
				{
					Title: "Default payment mode for Merchants",
					ActionCta: &pb.UpiAction_ToggleCta{
						ToggleCta: &pb.ToggleCta{
							ToggleState:    pb.ToggleCta_TOGGLE_STATE_OFF,
							ToggleDeeplink: defaultMerchantPaymentDeeplinkWithEnablingAction,
						},
					},
					UpiActionCtaState:  pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_DISABLED,
					ActionToastMessage: "One default merchant payment account already found",
				},
				{
					Title: "Refresh",
					ActionDeeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LIST_UPI_ACCOUNTS, &upiDeeplinkPb.ListUpiAccountsScreenOptions{
						UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_CREDIT_CARD,
						BankInfo: &feupionbpb.BankInfo{
							Name:     "ICICI Bank Credit Card",
							IfscCode: "ICICI0001",
							Logo:     "https://epifi-icons.pointz.in/bank/logo/icici_264px.png",
						},
					}),
					ActionCta: &pb.UpiAction_IconCta{
						IconCta: &commontypes.Image{
							ImageUrl: "https://epifi-icons.pointz.in/upi/icons/arrow.png",
							Height:   24,
							Width:    24,
						},
					},
					UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
				},
			},
			wantErr: assert.NoError,
		},
		{
			name:             "should return error in case we don't get any account",
			derivedAccountId: "test-derived-account-id",
			actorId:          "test-actor-id",
			accountId:        "test-account-id",
			accType:          accounts.Type_CREDIT,
			mockGetAccounts: &mockGetAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId:       "test-actor-id",
					AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
					AccountTypes:  []accounts.Type{accounts.Type_CREDIT},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status:   rpc.StatusRecordNotFound(),
					Accounts: []*upiOnboardingPb.UpiAccount{},
				},
			},
			want:    nil,
			wantErr: assert.Error,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServerWithMocks(t)
			if tt.mockGetAccounts.enable {
				md.upiOnboardingClient.EXPECT().GetAccounts(gomock.Any(), tt.mockGetAccounts.req).Return(tt.mockGetAccounts.res, tt.mockGetAccounts.err)
			}
			got, err := s.getCreditAccountActions(context.Background(), tt.derivedAccountId, tt.actorId, tt.accountId, tt.accType)
			if !tt.wantErr(t, err, fmt.Sprintf("getCreditAccountActions(%v, %v, %v, %v)", tt.derivedAccountId, tt.actorId, tt.accountId, tt.accType)) {
				return
			}
			assert.Equalf(t, tt.want, got, "getCreditAccountActions(%v, %v, %v, %v)", tt.derivedAccountId, tt.actorId, tt.accountId, tt.accType)
		})
	}
}
