package file_upload_manager

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/frontend/docs/errors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type EPANFileUploadManager struct {
	panClient pan.PanClient
}

func NewEPANFileUploadManager(panClient pan.PanClient) *EPANFileUploadManager {
	return &EPANFileUploadManager{
		panClient: panClient,
	}
}

func (e *EPANFileUploadManager) UploadFile(ctx context.Context, req *UploadFileRequest) error {
	if len(req.Data) == 0 {
		logger.Error(ctx, "received empty epan document from client")
		return errors.ErrEmptyDoc
	}
	resp, err := e.panClient.UploadEPAN(ctx, &pan.UploadEPANRequest{
		Data:            req.Data,
		Password:        req.Password,
		ClientRequestId: req.ClientReqId,
		ActorId:         req.ActorId,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return fmt.Errorf("error in uploading file : %w", rpcErr)
	}
	return nil
}

func (e *EPANFileUploadManager) GetNextActionPostUpload(ctx context.Context, req *GetNextActionPostUploadRequest) (*deeplink.Deeplink, error) {
	nextActionResp, err := e.panClient.GetEPANNextAction(ctx, &pan.GetEPANNextActionRequest{
		ClientReqId: req.ClientReqId,
		Blob:        req.Blob,
		ActorId:     req.ActorId,
	})
	if rpcErr := epifigrpc.RPCError(nextActionResp, err); rpcErr != nil {
		return nil, fmt.Errorf("error in getting next action : %w", rpcErr)
	}

	return nextActionResp.GetNextAction(), nil
}
