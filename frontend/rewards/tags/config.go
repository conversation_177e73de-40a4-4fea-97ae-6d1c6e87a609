package tags

import commontypes "github.com/epifi/be-common/api/typesv2/common"

// RenderLocation refers to the location where the entity (offer, CBR, or tag) is rendered in the UI
type RenderLocation string

const (
	RenderLocationUnspecified = RenderLocation("RENDER_LOCATION_UNSPECIFIED")

	RenderLocationFiCoinOffersCatalogCard = RenderLocation("FI_COIN_OFFERS_CATALOG_CARD")
	RenderLocationFiCoinOffersFiltersList = RenderLocation("FI_COIN_OFFERS_CATALOG_FILTERS_LIST")
	RenderLocationFiCoinOffersAllFilters  = RenderLocation("FI_COIN_OFFERS_CATALOG_ALL_FILTERS")

	// New Offer Catalog Page render locations
	// figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11166-9078&node-type=frame&t=e98Tz4KMKpDiyPkq-0
	RenderLocationOffersCatalogPageCatalogCard = RenderLocation("OFFERS_CATALOG_PAGE_CATALOG_CARD")
	RenderLocationOffersCatalogPageFiltersList = RenderLocation("OFFERS_CATALOG_PAGE_FILTERS_LIST")

	RenderLocationCollectedOffersScreen      = RenderLocation("COLLECTED_OFFERS_SCREEN")
	RenderLocationCollectedOffersFiltersList = RenderLocation("COLLECTED_OFFERS_CATALOG_FILTERS_LIST")
	RenderLocationCollectedOffersAllFilters  = RenderLocation("COLLECTED_OFFERS_CATALOG_ALL_FILTERS")

	RenderLocationHomeScreen = RenderLocation("HOME_SCREEN")
	// RenderLocationOffersWidget new fi-coin/card offers widgets
	// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=26303-29259&t=SKbteM1ThJNSdZxf-1
	RenderLocationOffersWidget = RenderLocation("OFFERS_WIDGET")
	// RenderLocationCardOffersWidget V3 card widget
	// location figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=26303-29259&t=SKbteM1ThJNSdZxf-1
	RenderLocationCardOffersWidget       = RenderLocation("CARD_OFFERS_WIDGET")
	RenderLocationCardCatalogFiltersList = RenderLocation("CARD_CATALOG_FILTERS_LIST")
	RenderLocationCardCatalogAllFilters  = RenderLocation("CARD_CATALOG_ALL_FILTERS")
)

type Config struct {
	// the tag render location to fetch the RenderLocationToFontStyleMap and RenderLocationToTagDisplayPropertiesMap for where we want to render the tags
	TagRenderLocation RenderLocation
	// the extract tags is set to true to extract all the unique tags from all the offers
	ExtractTags bool
}

type tagDisplayProperties struct {
	TopPadding    int32
	BottomPadding int32
	LeftPadding   int32
	RightPadding  int32
	CornerRadius  int32
	ImageHeight   int32
	ImageWidth    int32
}

// GetTagRenderLocation is getter for Config.TagRenderLocation
func (t *Config) GetTagRenderLocation() RenderLocation {
	if t != nil {
		return t.TagRenderLocation
	}
	return RenderLocationUnspecified
}

// GetExtractTags is getter for Config.ExtractTags
func (t *Config) GetExtractTags() bool {
	if t != nil {
		return t.ExtractTags
	}
	return false
}

var RenderLocationToFontStyleMap = map[RenderLocation]commontypes.FontStyle{
	RenderLocationFiCoinOffersCatalogCard:      commontypes.FontStyle_OVERLINE_2,
	RenderLocationCollectedOffersScreen:        commontypes.FontStyle_OVERLINE_2,
	RenderLocationFiCoinOffersFiltersList:      commontypes.FontStyle_SUBTITLE_S,
	RenderLocationFiCoinOffersAllFilters:       commontypes.FontStyle_SUBTITLE_S,
	RenderLocationHomeScreen:                   commontypes.FontStyle_OVERLINE_2XS_CAPS,
	RenderLocationCardCatalogFiltersList:       commontypes.FontStyle_SUBTITLE_S,
	RenderLocationCardCatalogAllFilters:        commontypes.FontStyle_SUBTITLE_S,
	RenderLocationOffersWidget:                 commontypes.FontStyle_OVERLINE_3XS_CAPS,
	RenderLocationCardOffersWidget:             commontypes.FontStyle_OVERLINE_3XS_CAPS,
	RenderLocationOffersCatalogPageFiltersList: commontypes.FontStyle_SUBTITLE_S,
	RenderLocationOffersCatalogPageCatalogCard: commontypes.FontStyle_OVERLINE_S_CAPS,
}

var RenderLocationToTagDisplayPropertiesMap = map[RenderLocation]tagDisplayProperties{
	RenderLocationOffersWidget:                 {TopPadding: 2, BottomPadding: 2, LeftPadding: 6, RightPadding: 6, CornerRadius: 19, ImageHeight: 12, ImageWidth: 12},
	RenderLocationCollectedOffersFiltersList:   {TopPadding: 12, BottomPadding: 12, LeftPadding: 12, RightPadding: 12, CornerRadius: 19, ImageHeight: 16, ImageWidth: 16},
	RenderLocationCollectedOffersAllFilters:    {TopPadding: 12, BottomPadding: 12, LeftPadding: 12, RightPadding: 12, CornerRadius: 19, ImageHeight: 16, ImageWidth: 16},
	RenderLocationOffersCatalogPageFiltersList: {TopPadding: 0, BottomPadding: 0, LeftPadding: 0, RightPadding: 0, CornerRadius: 19, ImageHeight: 60, ImageWidth: 60},
	RenderLocationOffersCatalogPageCatalogCard: {TopPadding: 2, BottomPadding: 2, LeftPadding: 6, RightPadding: 6, CornerRadius: 19, ImageHeight: 12, ImageWidth: 12},
}
