package liquiloans

import (
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
)

type RealTimeStplProvider struct {
	*RealTimeSubventionProvider
}

var _ provider.IDeeplinkProvider = &RealTimeStplProvider{}

func NewRealTimeStplProvider(realTimeSubventionProvider *RealTimeSubventionProvider) *RealTimeStplProvider {
	return &RealTimeStplProvider{
		realTimeSubventionProvider,
	}
}

func (ll *RealTimeStplProvider) GetLoanHeader() *palPbFeEnums.LoanHeader {
	return &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
		Vendor:      palPbFeEnums.Vendor_LIQUILOANS,
	}
}
