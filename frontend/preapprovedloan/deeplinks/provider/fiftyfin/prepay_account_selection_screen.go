// nolint:goimports
package fiftyfin

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"google.golang.org/genproto/googleapis/type/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func (lp *LamfProvider) GetPrePayAccountSelectionScreen(lh *palPbFeEnums.LoanHeader, req *provider.GetGetPrePayAccountSelectionScreenRequest) (*deeplinkPb.Deeplink, error) {
	var minInterestAmount, minPrincipalAmount, maxPrincipalAmount, maxInterestAmount *money.Money
	maxPrincipalAmount = req.BeRes.GetForeclosureDetails().GetPrincipalOutstandingAmount()
	// max interest user can pay will be total interest and penalty and other changes.
	maxInterestAmount, err := getTotalPendingInterestAndCharges(req.GetLoanDetailsResp.GetLoanInstallmentPayouts())
	if err != nil {
		return nil, fmt.Errorf("failed to get max interest to be paid:%v", err)
	}
	if moneyPkg.IsNegative(maxInterestAmount) {
		return nil, fmt.Errorf("max interest can not be negative")
	}
	// flags to check whether to allow given payment,if any of the outstanding amounts is zero we can not allow it.
	allowPrincipal := true
	allowInterest := true
	if moneyPkg.IsZero(maxInterestAmount) {
		allowInterest = false
	}
	if moneyPkg.IsZero(maxPrincipalAmount) {
		allowPrincipal = false
	}
	minInterestAmount = getMinDue(req.GetLoanDetailsResp.GetLoanInstallmentPayouts())
	// minimum principal amount user can pay is emi for the given month excluding charges and interest(mindue)
	minPrincipalAmount, err = safeSubtract(req.BeRes.GetUpcomingEmi(), minInterestAmount)
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("failed to get minPrincipalAmount: %v", minPrincipalAmount))
		// not giving error here because we do not have clear understanding of minInterestAmount right now and
		// we do not want to block user from making payment.
		minPrincipalAmount = moneyPkg.ZeroINR().GetPb()
	}
	if moneyPkg.IsNegative(minPrincipalAmount) {
		// not giving error here because we do not have clear understanding of minInterestAmount right now and
		// we do not want to block user from making payment.
		minPrincipalAmount = moneyPkg.ZeroINR().GetPb()
	}

	knowMoreDeeplink, err := lp.GetPrePayKnowMoreBottomSheetScreen(context.Background(), &provider.GetPrePayKnowMoreBottomSheetScreen{
		Lh: lh,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get know more screen: %v", err)
	}

	var (
		latestInterestLprStatus  preapprovedloan.LoanPaymentRequestStatus
		latestPrincipalLprStatus preapprovedloan.LoanPaymentRequestStatus
	)
	if req.BeRes.GetLatestLoanPaymentRequest().GetAccountType() == preapprovedloan.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST {
		latestInterestLprStatus = req.BeRes.GetLatestLoanPaymentRequest().GetStatus()
	}
	if req.BeRes.GetLatestLoanPaymentRequest().GetAccountType() == preapprovedloan.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL {
		latestPrincipalLprStatus = req.BeRes.GetLatestLoanPaymentRequest().GetStatus()
	}

	if !lp.isLoanPrePayAllowedThisTimeOfMonth() {
		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LAMF_REPAYMENT_METHODS_SCREEN, &preapprovedloans.LamfRepaymentMethodsScreenOptions{
			LoanId:     req.LoanId,
			LoanHeader: lh,
			BackgroundColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#282828",
				},
			},
			ToolbarTitle: common.GetPlainStringText("Pre-Payment").WithFontColor("#FFFFFF").WithFontStyle(common.FontStyle_SUBTITLE_M),
			HeaderComponent: &widget.VisualElementTitleSubtitleElement{
				TitleText: common.GetPlainStringText("Payment closed till 8th of this month").WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor("#F6F9FD"),
				VisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/lending/prepay-blocked.png").WithProperties(&common.VisualElementProperties{
					Height: 96,
					Width:  96,
				}),
			},
		})
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LAMF_REPAYMENT_METHODS_SCREEN, &preapprovedloans.LamfRepaymentMethodsScreenOptions{
		LoanId:     req.LoanId,
		LoanHeader: lh,
		BackgroundColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#282828",
			},
		},
		ToolbarTitle: common.GetPlainStringText("Pre-Payment").WithFontColor("#FFFFFF").WithFontStyle(common.FontStyle_SUBTITLE_M),
		HeaderComponent: &widget.VisualElementTitleSubtitleElement{
			TitleText: common.GetPlainStringText("Clear your loan dues").WithFontStyle(common.FontStyle_HEADLINE_L).WithFontColor("#F6F9FD"),
			VisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/calc-2").WithProperties(&common.VisualElementProperties{
				Height: 96,
				Width:  96,
			}),
		},
		RepaymentMethods: []*preapprovedloans.LamfRepaymentMethodsScreenOptions_RepaymentMethodsComponent{
			lp.getInterestRepaymentOption(minInterestAmount, allowInterest, latestInterestLprStatus),
			lp.getPrincipalRepaymentOption(minPrincipalAmount, allowPrincipal, latestPrincipalLprStatus),
		},
		AdditionalMessage: &ui.IconTextComponent{
			LeftVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-question-mark.png").WithProperties(&common.VisualElementProperties{
				Height: 16,
				Width:  16,
			}),
			Texts: []*common.Text{
				common.GetPlainStringText("Why am I making two separate payments?").WithFontStyle(common.FontStyle_SUBTITLE_XS).WithFontColor("#00B899"),
			},
			LeftImgTxtPadding: 4,
			Deeplink:          knowMoreDeeplink,
		},

		FooterLabel: &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Note: Loan pre-payment will be closed from 4-7th of every month").WithFontStyle(common.FontStyle_SUBTITLE_XS).WithFontColor("#A4A4A4"),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#383838",
				TopPadding:    8,
				BottomPadding: 8,
			},
		},
		Cta: &deeplinkPb.Cta{
			Text:         "Continue",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_LOANS_PREPAY_V2_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&preapprovedloans.LoansPrepayV2ScreenOptions{
					LoanAccountId: req.LoanId,
					LoanHeader:    lh,
				}),
			},
		},
	})
}

// todo(naveed) need more concrete logic here.
// gets the min due to be paid
// for all loecs which have due date after the current date get the loec with the max due date.
func getMinDue(lips []*preapprovedloan.LoanInstallmentPayout) *money.Money {
	var res *preapprovedloan.LoanInstallmentPayout
	for _, lip := range lips {
		if time.Now().In(datetime.IST).Before(datetime.DateToTimeV2(lip.GetDueDate(), datetime.IST)) && (lip.GetStatus() == preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING) || (lip.GetStatus() == preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID) {
			if (res == nil) || datetime.DateToTimeV2(res.GetDueDate(), datetime.IST).After(datetime.DateToTimeV2(lip.GetDueDate(), datetime.IST)) {
				res = lip
			}
		}
	}
	// if pending lip id not of the current month means for the current month payment has already been made.
	if res == nil || time.Month(res.GetDueDate().GetMonth()) != time.Now().Month() || int32(time.Now().Year()) != res.GetDueDate().GetYear() {
		return moneyPkg.ZeroINR().GetPb()
	}
	return res.GetInterest()
}

// TotalOutstanding-TotalPrincipal-InterestPaid
// todo(team) change this logic once we have more clarity here.
func getTotalPendingInterestAndCharges(lips []*preapprovedloan.LoanInstallmentPayout) (*money.Money, error) {
	res := moneyPkg.ZeroINR().GetPb()
	var err error
	for _, lip := range lips {
		if lip.GetStatus() == preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING || lip.GetStatus() == preapprovedloan.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID {
			res, err = safeSum(res, lip.GetInterest())
			if err != nil {
				return nil, fmt.Errorf("failed to add to total outstanding amount: %v", err)
			}
		}
	}
	return res, nil
}

// DueAmount-Principal-PaidInterest
func getTotalOutstandingWithoutPrincipal(lip *preapprovedloan.LoanInstallmentPayout) (*money.Money, error) {
	totalAmountWithoutPrincipal, err := safeSubtract(lip.GetDueAmount(), lip.GetPrincipal())
	if err != nil {
		return nil, fmt.Errorf("failed to get due amount without principal: %v", err)
	}
	toBepaidAmount, err := safeSubtract(totalAmountWithoutPrincipal, lip.GetDetails().GetPaidInterestAmt())
	if err != nil {
		return nil, fmt.Errorf("failed to get due amount: %v", err)
	}
	return toBepaidAmount, nil
}

func (p *LamfProvider) GetPrePayKnowMoreBottomSheetScreen(ctx context.Context, req *provider.GetPrePayKnowMoreBottomSheetScreen) (*deeplinkPb.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_KNOW_MORE_BOTTOM_SHEET, &preapprovedloans.LoansKnowMoreBottomSheetScreenOptions{
		LoanHeader: req.Lh,
		BgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#E7ECF0",
			},
		},
		Title:    common.GetPlainStringText("Why am I making two separate \npayments?").WithFontStyle(common.FontStyle_SUBTITLE_L).WithFontColor("#313234"),
		Subtitle: common.GetPlainStringText("As per our lending partner Bajaj Finserv’s policy, \nyou’re required to pay your interest & principal to \ndifferent accounts").WithFontStyle(common.FontStyle_BODY_S).WithFontColor("#606265"),
		MethodBlocks: []*preapprovedloans.LoansKnowMoreBottomSheetScreenOptions_InfoBlock{
			{
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#FFFFFF",
					},
				},
				Icon: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/autopay.png").WithProperties(&common.VisualElementProperties{
					Height: 32,
					Width:  30,
				}),
				Title: &ui.IconTextComponent{
					Texts: []*common.Text{
						common.GetPlainStringText("Pay interest (min due)").WithFontStyle(common.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
					},
				},
				InfoBadge: &ui.IconTextComponent{
					Texts: []*common.Text{
						common.GetPlainStringText("Skipping this may lead to late fees").WithFontStyle(common.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#6D3149"),
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{
						BgColor:       "#F8E5EB",
						TopPadding:    2,
						BottomPadding: 2,
						LeftPadding:   8,
						RightPadding:  8,
						CornerRadius:  12,
					},
				},
			},
			{
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#FFFFFF",
					},
				},
				Icon: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/autosave.png").WithProperties(&common.VisualElementProperties{
					Height: 32,
					Width:  30,
				}),
				Title: &ui.IconTextComponent{
					Texts: []*common.Text{
						common.GetPlainStringText("Pay principal").WithFontStyle(common.FontStyle_SUBTITLE_S).WithFontColor("#313234"),
					},
				},
				InfoBadge: &ui.IconTextComponent{
					Texts: []*common.Text{
						common.GetPlainStringText("Paying this now may help close your loan faster").WithFontStyle(common.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#6A6D70"),
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{
						BgColor:       "#FFF8CE",
						TopPadding:    2,
						BottomPadding: 2,
						LeftPadding:   8,
						RightPadding:  8,
						CornerRadius:  12,
					},
					RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/voltage.png").WithProperties(&common.VisualElementProperties{
						Height: 12,
						Width:  12,
					}),
					RightImgTxtPadding: 2,
				},
			},
		},
	})
}

func (lp *LamfProvider) getPrincipalRepaymentOption(minPrincipalAmount *money.Money, allowPrincipal bool, lastLprStatus preapprovedloan.LoanPaymentRequestStatus) *preapprovedloans.LamfRepaymentMethodsScreenOptions_RepaymentMethodsComponent {
	var (
		repaymentTitle *ui.IconTextComponent
		rightIcon      *common.VisualElement
		isRadioButton  bool
		isSelected     bool
	)

	switch {
	// https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12553-16298&t=aLwmCoktfFRhRCse-1
	case !allowPrincipal:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Principal paid").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#004E2D"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		rightIcon = common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/material-symbol-check.png").WithProperties(&common.VisualElementProperties{
			Height: 20,
			Width:  20,
		})
	// https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12553-16298&t=aLwmCoktfFRhRCse-1
	case lastLprStatus == preapprovedloan.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_FAILED:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Principal payment failed, try again").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#AA301F"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		isRadioButton = true
		isSelected = true
	// https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12553-16609&t=aLwmCoktfFRhRCse-1
	case lastLprStatus == preapprovedloan.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED, lastLprStatus == preapprovedloan.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Principal payment under process").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#98712F"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		rightIcon = common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/material-symbol-block.png").WithProperties(&common.VisualElementProperties{
			Height: 24,
			Width:  24,
		})
	default:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Pay principal").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#646464"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		isRadioButton = true
		isSelected = true
	}

	principalComponentText := "Towards outstanding"
	if helper.CeilMoney(minPrincipalAmount).GetUnits() > 0 {
		principalComponentText = fmt.Sprintf("%v-Towards outstanding", helper.CeilMoney(minPrincipalAmount).GetUnits())
	}

	return &preapprovedloans.LamfRepaymentMethodsScreenOptions_RepaymentMethodsComponent{
		Id:             palPbFeEnums.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL.String(),
		RepaymentTitle: repaymentTitle,
		RightIcon:      rightIcon,
		IsRadioButton:  isRadioButton,
		IsSelected:     isSelected,
		AmountInfo: &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText(principalComponentText).WithFontStyle(common.FontStyle_SUBTITLE_2).WithFontColor("#383838"),
			},
		},
		RepaymentDesc: &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("This may help close your loan faster").WithFontStyle(common.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#6A6D70"),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#FFF8CE",
				TopPadding:    2,
				BottomPadding: 2,
				LeftPadding:   8,
				RightPadding:  8,
				CornerRadius:  12,
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/voltage.png").WithProperties(&common.VisualElementProperties{
				Height: 12,
				Width:  12,
			}),
			RightImgTxtPadding: 2,
		},
		BackgroundColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#EFF2F6",
			},
		},
	}
}

func (lp *LamfProvider) getInterestRepaymentOption(minInterestAmount *money.Money, allowInterest bool, lastLprStatus preapprovedloan.LoanPaymentRequestStatus) *preapprovedloans.LamfRepaymentMethodsScreenOptions_RepaymentMethodsComponent {
	var (
		repaymentTitle *ui.IconTextComponent
		rightIcon      *common.VisualElement
		isRadioButton  bool
		isSelected     bool
		repaymentDesc  *ui.IconTextComponent
	)

	switch {
	// https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12842-18280&t=T0eL59mydpJ3vEFR-1
	case !lp.isLoanPrePayAllowedThisTimeOfMonth():
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Payment not allowed now").WithFontStyle(common.FontStyle_HEADLINE_S).WithFontColor("#98712F"),
			},
		}
		rightIcon = common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/material-symbol-block.png").WithProperties(&common.VisualElementProperties{
			Height: 24,
			Width:  24,
		})
		repaymentDesc = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Will be debited on 7th via mandate setup, if not paid").WithFontStyle(common.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#6A6D70"),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#FFF8CE",
				TopPadding:    2,
				BottomPadding: 2,
				LeftPadding:   8,
				RightPadding:  8,
				CornerRadius:  12,
			},
		}
	// https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12584-7166&t=T0eL59mydpJ3vEFR-1
	case !allowInterest:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Interest paid").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#004E2D"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		rightIcon = common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/material-symbol-check.png").WithProperties(&common.VisualElementProperties{
			Height: 20,
			Width:  20,
		})
	// https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12553-16350&t=T0eL59mydpJ3vEFR-1
	case lastLprStatus == preapprovedloan.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_FAILED:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Interest payment failed, try again").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#AA301F"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		isRadioButton = true
		isSelected = true
		repaymentDesc = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Skipping this may lead to late fees").WithFontStyle(common.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#6D3149"),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "F8E5EB",
				TopPadding:    2,
				BottomPadding: 2,
				LeftPadding:   8,
				RightPadding:  8,
				CornerRadius:  12,
			},
		}
	// https://www.figma.com/design/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?node-id=12553-16609&t=T0eL59mydpJ3vEFR-1
	case lastLprStatus == preapprovedloan.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED, lastLprStatus == preapprovedloan.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Interest payment under process").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#98712F"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		rightIcon = common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/material-symbol-block.png").WithProperties(&common.VisualElementProperties{
			Height: 24,
			Width:  24,
		})
	default:
		repaymentTitle = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Pay interest").WithFontStyle(common.FontStyle_BODY_3_PARA).WithFontColor("#646464"),
			},
			RightVisualElement: common.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/green-information.png").WithProperties(&common.VisualElementProperties{
				Height: 20,
				Width:  20,
			}),
			RightImgTxtPadding: 2,
		}
		isRadioButton = true
		isSelected = true
		repaymentDesc = &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText("Skipping this may lead to late fees").WithFontStyle(common.FontStyle_OVERLINE_2XS_CAPS).WithFontColor("#6D3149"),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "F8E5EB",
				TopPadding:    2,
				BottomPadding: 2,
				LeftPadding:   8,
				RightPadding:  8,
				CornerRadius:  12,
			},
		}
	}

	interestComponentText := "Minimum due"
	if helper.CeilMoney(minInterestAmount).GetUnits() > 0 {
		interestComponentText = fmt.Sprintf("%v-Minimum due", helper.CeilMoney(minInterestAmount).GetUnits())
	}

	resp := &preapprovedloans.LamfRepaymentMethodsScreenOptions_RepaymentMethodsComponent{
		Id:             palPbFeEnums.LoanPaymentAccountType_LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST.String(),
		RepaymentTitle: repaymentTitle,
		RightIcon:      rightIcon,
		IsRadioButton:  isRadioButton,
		IsSelected:     isSelected,
		AmountInfo: &ui.IconTextComponent{
			Texts: []*common.Text{
				common.GetPlainStringText(interestComponentText).WithFontStyle(common.FontStyle_SUBTITLE_2).WithFontColor("#383838"),
			},
		},
		RepaymentDesc: repaymentDesc,
		BackgroundColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#EFF2F6",
			},
		},
	}
	return resp
}
