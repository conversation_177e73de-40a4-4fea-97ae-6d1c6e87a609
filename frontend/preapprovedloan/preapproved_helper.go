//nolint:dupl
package preapprovedloan

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	money2 "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/retry"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	consentPb "github.com/epifi/gamma/api/consent"
	feAnalyticsPb "github.com/epifi/gamma/api/frontend/analytics"
	"github.com/epifi/gamma/api/frontend/deeplink"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	feTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	bePayPb "github.com/epifi/gamma/api/pay/attribute"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	securedLoansPb "github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	segmentPb "github.com/epifi/gamma/api/segment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	vkycScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	"github.com/epifi/gamma/frontend/preapprovedloan/events"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	addressPkg "github.com/epifi/gamma/pkg/address"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"
)

var (
	terminalLrStatusArr = []palBePb.LoanRequestStatus{
		palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
		palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED,
		palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
	}
)

func IsLrTerminalStatus(status palBePb.LoanRequestStatus) bool {
	return lo.Contains(terminalLrStatusArr, status)
}

func (s *Service) getWebviewWithPollingCtaDlFromBaseDl(ctx context.Context, currentUrl string, dl *deeplink.Deeplink, lrId string, actorId string) *deeplink.Deeplink {
	switch dl.GetScreen() {
	case deeplink.Screen_LOANS_PROGRESS_UPDATE_SCREEN:
		progressUpdateScreen := &preapprovedloans.ProgressUpdateScreen{}
		if err := dl.GetScreenOptionsV2().UnmarshalTo(progressUpdateScreen); err != nil {
			logger.Error(context.Background(), "error while unmarshalling screen options for ProgressUpdateScreen", zap.Error(err))
			return nil
		}
		return progressUpdateScreen.GetCta().GetCta().GetDeeplink()
	case deeplink.Screen_LOANS_MANDATE_INTRO_SCREEN:
		loansMandateIntroScreen := &preapprovedloans.LoansMandateIntroScreen{}
		if err := dl.GetScreenOptionsV2().UnmarshalTo(loansMandateIntroScreen); err != nil {
			logger.Error(context.Background(), "error while unmarshalling screen options for LoansMandateIntroScreen", zap.Error(err))
			return nil
		}
		return loansMandateIntroScreen.GetCta().GetCta().GetDeeplink()
	case deeplink.Screen_LOANS_GENERIC_INTRO_SCREEN:
		loansGenericIntroScreen := &preapprovedloans.LoansGenericIntroScreen{}
		if err := dl.GetScreenOptionsV2().UnmarshalTo(loansGenericIntroScreen); err != nil {
			logger.Error(context.Background(), "error while unmarshalling screen options for loansGenericIntroScreen", zap.Error(err))
			return nil
		}
		return loansGenericIntroScreen.GetCta().GetCta().GetDeeplink()
	case deeplink.Screen_LOANS_INFO_SCREEN:
		loansInfoScreenOptions := &preapprovedloans.LoansInfoScreenOptions{}
		if err := dl.GetScreenOptionsV2().UnmarshalTo(loansInfoScreenOptions); err != nil {
			logger.Error(context.Background(), "error while unmarshalling screen options for loansInfoScreen", zap.Error(err))
			return nil
		}
		return loansInfoScreenOptions.GetContinueButton().GetCta().GetDeeplink()
	case deeplink.Screen_VKYC_INSTRUCTIONS_V2:
		isIdfcPalVkycEnabled, err := s.isIdfcVkycV2Enabled(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error while getting idfc eligibility config", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil
		}
		if isIdfcPalVkycEnabled {
			return getWebviewWithPollingCtaDlFromBaseDlForVkycInstructionsV3(ctx, currentUrl, dl, lrId)
		} else {
			getWebviewWithPollingCtaDlFromBaseDlForVkycInstructionsV2(ctx, dl)
		}
	}
	return dl
}

func getWebviewWithPollingCtaDlFromBaseDlForVkycInstructionsV2(ctx context.Context, dl *deeplink.Deeplink) *deeplink.Deeplink {
	// IMPORTANT: when you make changes here, make sure to update the handling in
	// getVkycInstructionDeeplinkWithWebview as-well of preapprovedloan/activity/idfc/vkyc_v2.go
	vkycInstructionsScreenOptions := &vkycScreenOptionsPb.VkycInstructionsV2ScreenOptions{}
	if err := dl.GetScreenOptionsV2().UnmarshalTo(vkycInstructionsScreenOptions); err != nil {
		logger.Error(ctx, "error while unmarshalling screen options for vkycInstructionsScreenOptions", zap.Error(err))
		return nil
	}

	if len(vkycInstructionsScreenOptions.GetCtas()) == 0 {
		logger.Error(ctx, "no ctas found in vkycInstructionsScreenOptions")
		return nil
	}
	overlayDeeplink := vkycInstructionsScreenOptions.GetCtas()[0].GetDeeplink()

	overlayScreenOptions := &vkycScreenOptionsPb.VkycInstructionsOverlayScreenOptions{}
	if err := overlayDeeplink.GetScreenOptionsV2().UnmarshalTo(overlayScreenOptions); err != nil {
		logger.Error(ctx, "error while unmarshalling screen options for overlayScreenOptions", zap.Error(err))
		return nil
	}

	if len(overlayScreenOptions.GetOverlayPageBlocks()) == 0 {
		logger.Error(ctx, "no overlay page blocks found in overlayScreenOptions")
		return nil
	}

	overlayPageBlocks := overlayScreenOptions.GetOverlayPageBlocks()

	if len(overlayPageBlocks[0].GetCtas()) == 0 {
		logger.Error(ctx, "no ctas found in overlayPageBlocks")
		return nil
	}

	vkycTimedLoaderScreen := overlayPageBlocks[0].GetCtas()[0].GetDeeplink()
	vkycTimedLoaderScreenOptions := &preapprovedloans.LoansTimedLoaderScreen{}

	if err := vkycTimedLoaderScreen.GetScreenOptionsV2().UnmarshalTo(vkycTimedLoaderScreenOptions); err != nil {
		logger.Error(ctx, "error while unmarshalling screen options for vkycTimedLoaderScreenOptions", zap.Error(err))
		return nil
	}
	return vkycTimedLoaderScreenOptions.GetDeeplink()
}

func getWebviewWithPollingCtaDlFromBaseDlForVkycInstructionsV3(ctx context.Context, url string, dl *deeplink.Deeplink, lrId string) *deeplink.Deeplink {
	// IMPORTANT: when you make changes here, make sure to update the handling in
	// getVkycInstructionDeeplinkWithoutWebview as-well of preapprovedloan/activity/idfc/vkyc_v2.go
	vkycInstructionsScreenOptions := &vkycScreenOptionsPb.VkycInstructionsV2ScreenOptions{}
	if err := dl.GetScreenOptionsV2().UnmarshalTo(vkycInstructionsScreenOptions); err != nil {
		logger.Error(ctx, "error while unmarshalling screen options for vkycInstructionsScreenOptions", zap.Error(err))
		return nil
	}

	if len(vkycInstructionsScreenOptions.GetCtas()) == 0 {
		logger.Error(ctx, "no ctas found in vkycInstructionsScreenOptions")
		return nil
	}
	overlayDeeplink := vkycInstructionsScreenOptions.GetCtas()[0].GetDeeplink()

	overlayScreenOptions := &vkycScreenOptionsPb.VkycInstructionsOverlayScreenOptions{}
	if err := overlayDeeplink.GetScreenOptionsV2().UnmarshalTo(overlayScreenOptions); err != nil {
		logger.Error(ctx, "error while unmarshalling screen options for overlayScreenOptions", zap.Error(err))
		return nil
	}

	if len(overlayScreenOptions.GetOverlayPageBlocks()) == 0 {
		logger.Error(ctx, "no overlay page blocks found in overlayScreenOptions")
		return nil
	}

	overlayPageBlocks := overlayScreenOptions.GetOverlayPageBlocks()

	if len(overlayPageBlocks[0].GetCtas()) == 0 {
		logger.Error(ctx, "no ctas found in overlayPageBlocks")
		return nil
	}

	vkycCallsFeDeeplink := overlayPageBlocks[0].GetCtas()[0].GetDeeplink()
	if vkycCallsFeDeeplink.GetScreen() != deeplink.Screen_LOANS_VKYC_REDIRECTION_SCREEN {
		return nil
	}
	vkycCallsFeDeeplink.Screen = deeplink.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN
	var err error
	vkycCallsFeDeeplink.ScreenOptionsV2, err = anyPb.New(&preapprovedloans.LoansWebviewWithStatusPollScreen{
		EntryUrl:            url,
		LoanRequestId:       lrId,
		AnalyticsScreenName: feAnalyticsPb.AnalyticsScreenName_VKYC_INSTRUCTIONS_WEB_VIEW_SCREEN,
	})
	if err != nil {
		logger.Error(ctx, "error in creating new LoansWebviewWithStatusPollScreen", zap.Error(err))
		return nil
	}
	return vkycCallsFeDeeplink
}

func (s *Service) handleStatusPollBeNextAction(ctx context.Context, loanReq *palBePb.LoanRequest, currLoanStep *palBePb.LoanStepExecution, req *palFePb.GetApplicationStatusRequest, dlProvider provider.IDeeplinkProvider) *deeplink.Deeplink {
	switch {
	case req.GetScreenName() == deeplink.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN:
		return s.handleWithScreenNameInRequest(ctx, loanReq, req)
	case loanReq.GetNextAction().GetScreen() == deeplink.Screen_LOANS_STATUS_POLL_SCREEN:
		dl, err := s.handleLoansStatusPollScreen(ctx, loanReq.GetNextAction(), req.GetRetryAttemptNumber())
		if err != nil {
			logger.Error(ctx, "error while updating loans status poll screen", zap.Error(err))
			return nil
		}
		return dl
	default:
		return s.incrementPollingCountAndChangeDeeplinkIfNecessary(ctx, dlProvider, loanReq, currLoanStep, req.GetRetryAttemptNumber(), req.GetAnalyticsScreenName(), req.GetWebviewParams().GetUrl())
	}
}

// nolint: gocritic
func (s *Service) handleWithScreenNameInRequest(ctx context.Context, lr *palBePb.LoanRequest, req *palFePb.GetApplicationStatusRequest) *deeplink.Deeplink {
	switch req.GetScreenName() {
	case deeplink.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN:
		return s.handleWebviewWithPolling(ctx, lr, req)
	}
	return lr.GetNextAction()
}

func (s *Service) handleWebviewWithPolling(ctx context.Context, lr *palBePb.LoanRequest, req *palFePb.GetApplicationStatusRequest) *deeplink.Deeplink {
	ctaDl := s.getWebviewWithPollingCtaDlFromBaseDl(ctx, req.GetWebviewParams().GetUrl(), lr.GetNextAction(), lr.GetId(), lr.GetActorId())
	if ctaDl.GetScreen() == deeplink.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN {
		loansWebviewWithStatusPollScreen := &preapprovedloans.LoansWebviewWithStatusPollScreen{}
		if err := ctaDl.GetScreenOptionsV2().UnmarshalTo(loansWebviewWithStatusPollScreen); err != nil {
			logger.Error(context.Background(), "error while unmarshalling screen options for LoansWebviewWithStatusPollScreen", zap.Error(err))
			return nil
		}
		if loansWebviewWithStatusPollScreen.GetEntryUrl() == req.GetWebviewParams().GetUrl() {
			return ctaDl
		}
	}
	return lr.GetNextAction()
}

func (s *Service) handleLoansStatusPollScreen(ctx context.Context, dl *deeplink.Deeplink, attemptNumber int32) (*deeplink.Deeplink, error) {
	pollScreenOptions := &preapprovedloans.LoansStatusPollScreenOptions{}
	if err := dl.GetScreenOptionsV2().UnmarshalTo(pollScreenOptions); err != nil {
		return nil, fmt.Errorf("unable to unmarshal screen options : %w", err)
	}
	retryConfig := s.genconf.Lending().PreApprovedLoan().ApplicationStatusPollConfig()
	if retryConfig == nil {
		return dl, nil
	}
	programRetryConf, ok := retryConfig.RetryConfig[strings.ToLower(pollScreenOptions.GetLoanHeader().GetLoanProgram().String())]
	if !ok {
		return dl, nil
	}
	retrier, err := retry.NewStrategyFromConfigV2(programRetryConf)
	if err != nil {
		logger.Error(ctx, "error while generating retry strategy from retry config", zap.Error(err))
		return nil, fmt.Errorf("unable to generate retry strategy from retry conf : %w", err)
	}
	if pollScreenOptions.GetRetryAttemptNumber() <= attemptNumber {
		pollScreenOptions.RetryAttemptNumber = attemptNumber + 1
	}
	retryDelay := int32(retrier.GetNextRetryIntervalDuration(uint(pollScreenOptions.GetRetryAttemptNumber())) / time.Millisecond)
	if retryDelay > pollScreenOptions.GetRetryDelay() {
		pollScreenOptions.RetryDelay = retryDelay
	}
	dl, err = deeplinkV3.GetDeeplinkV3(deeplink.Screen_LOANS_STATUS_POLL_SCREEN, pollScreenOptions)
	if err != nil {
		logger.Error(ctx, "error while generating screen options v2", zap.Error(err))
		return nil, fmt.Errorf("error while generating screen options v2 : %w", err)
	}
	return dl, nil
}

// nolint: funlen
func (s *Service) incrementPollingCountAndChangeDeeplinkIfNecessary(ctx context.Context, dlProvider provider.IDeeplinkProvider,
	loanReq *palBePb.LoanRequest, currLoanStep *palBePb.LoanStepExecution, attemptNumber int32, analyticsScreenName feAnalyticsPb.AnalyticsScreenName, vkycUrl string) *deeplink.Deeplink {
	dl := loanReq.GetNextAction()
	switch dl.GetScreen() {
	case deeplink.Screen_EARLY_SALARY_STATUS_POLL_SCREEN:
		if attemptNumber >= baseprovider.LoanApplicationStatusPollMaxRetryNumber {
			return dlProvider.GetLoanLandingInfo(ctx, dlProvider.GetLoanHeader(), nil)
		} else {
			dl.GetPreApprovedLoanApplicationStatusPollScreenOptions().RetryAttemptNumber = attemptNumber + 1
		}
	case deeplink.Screen_PL_CHECK_ELIGIBILITY_LOADING_SCREEN:
		if attemptNumber >= baseprovider.LoanEligibilityStatusPollMaxRetryNumber {
			return dlProvider.GetLoanLandingInfo(ctx, dlProvider.GetLoanHeader(), nil)
		} else {
			screenOptionsV2 := &preapprovedloans.LoanApplicationReviewLoadingScreenOptions{}
			if err := dl.GetScreenOptionsV2().UnmarshalTo(screenOptionsV2); err != nil {
				logger.Error(context.Background(), "error while unmarshalling screen options for eligibility polling screen v2", zap.Error(err))
				return nil
			}
			screenOptionsV2.RetryAttemptNumber = attemptNumber + 1
			var dlErr error
			dl, dlErr = deeplinkV3.GetDeeplinkV3(deeplink.Screen_PL_CHECK_ELIGIBILITY_LOADING_SCREEN, screenOptionsV2)
			if dlErr != nil {
				logger.Error(context.Background(), "error while marshalling screen options for eligibility polling screen v2", zap.Error(dlErr))
				dl = &deeplink.Deeplink{Screen: deeplink.Screen_PL_CHECK_ELIGIBILITY_LOADING_SCREEN}
			}
		}
	// will need to keep this case for backward compatibility to support older client versions
	case deeplink.Screen_LOANS_PROGRESS_UPDATE_SCREEN, deeplink.Screen_LOANS_MANDATE_INTRO_SCREEN, deeplink.Screen_LOANS_INFO_SCREEN, deeplink.Screen_VKYC_INSTRUCTIONS_V2:
		ctaDl := s.getWebviewWithPollingCtaDlFromBaseDl(ctx, vkycUrl, dl, loanReq.GetId(), loanReq.GetActorId())
		switch {
		case ctaDl.GetScreen() == deeplink.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN:
			loansWebviewWithStatusPollScreen := &preapprovedloans.LoansWebviewWithStatusPollScreen{}
			if err := ctaDl.GetScreenOptionsV2().UnmarshalTo(loansWebviewWithStatusPollScreen); err != nil {
				logger.Error(context.Background(), "error while unmarshalling screen options for LoansWebviewWithStatusPollScreen", zap.Error(err))
				return nil
			}
			if loansWebviewWithStatusPollScreen.GetAnalyticsScreenName() == analyticsScreenName {
				dl = ctaDl
			}
		case currLoanStep.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VERIFY_LOAN_DETAILS:
			pfFetchDetails := currLoanStep.GetDetails().GetLoanDetailsVerificationData().GetLamf().GetPfFetch_Data()
			pfFetchAttempt := getLastPfFetchAttempt(pfFetchDetails)
			if pfFetchDetails == nil {
				break
			}
			if !s.isPfFetchCompleted(ctx, loanReq, pfFetchAttempt) {
				break
			}
			progressUpdateScreen := &preapprovedloans.ProgressUpdateScreen{}
			if err := dl.GetScreenOptionsV2().UnmarshalTo(progressUpdateScreen); err != nil {
				logger.Error(context.Background(), "error while unmarshalling screen options for ProgressUpdateScreen", zap.Error(err))
				return nil
			}
			innerDl := progressUpdateScreen.GetCta().GetCta().GetDeeplink()
			minAndroidVersion := s.genconf.Lending().PreApprovedLoan().PollScreenV2AppVersionConstraintConfig().MinAndroidVersion()
			minIosVersion := s.genconf.Lending().PreApprovedLoan().PollScreenV2AppVersionConstraintConfig().MinIOSVersion()
			if helper.ShowOldVersionForPollingScreen(ctx, minAndroidVersion, minIosVersion) {
				pollDeeplink, dlErr := s.convertIntoOldPollingScreen(ctx, innerDl)
				if pollDeeplink != nil {
					logger.Error(ctx, "error while converting new polling screen to old screen for lamf", zap.Error(dlErr))
				}
				return pollDeeplink
			}
			return innerDl

		case ctaDl.GetScreen() == deeplink.Screen_LOANS_STATUS_POLL_SCREEN && loanReq.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_LAMF:
			minAndroidVersion := s.genconf.Lending().PreApprovedLoan().PollScreenV2AppVersionConstraintConfig().MinAndroidVersion()
			minIosVersion := s.genconf.Lending().PreApprovedLoan().PollScreenV2AppVersionConstraintConfig().MinIOSVersion()
			if helper.ShowOldVersionForPollingScreen(ctx, minAndroidVersion, minIosVersion) {
				pollDeeplink, dlErr := s.convertIntoOldPollingScreen(ctx, ctaDl)
				if pollDeeplink != nil {
					logger.Error(ctx, "error while converting new polling screen to old screen for lamf", zap.Error(dlErr))
				}
				return pollDeeplink
			}
			return ctaDl
		}
	}
	return dl
}

func (s *Service) isPfFetchCompleted(ctx context.Context, loanReq *palBePb.LoanRequest, pfFetchAttempt *palBePb.LamfLoanDetailsVerificationData_PfFetchData) bool {
	if pfFetchAttempt.GetFetchCompleted() {
		return true
	}
	pfFetchStatus, err := s.securedLoanClient.GetPortfolioFetchStatus(ctx, &securedLoansPb.GetPortfolioFetchStatusRequest{
		LoanHeader: &palBePb.LoanHeader{
			LoanProgram: loanReq.GetLoanProgram(),
			Vendor:      loanReq.GetVendor(),
		},
		ClientRequestId: pfFetchAttempt.GetReqId(),
	})
	if err != nil {
		logger.Error(ctx, "error while fetching portfolio fetch status", zap.Error(err))
		return false
	}
	return IsLrTerminalStatus(pfFetchStatus.GetPortfolioFetchStatus())
}

// nolint:dupl
func getLastPfFetchAttempt(data []*palBePb.LamfLoanDetailsVerificationData_PfFetchData) *palBePb.LamfLoanDetailsVerificationData_PfFetchData {
	if len(data) == 0 {
		return nil
	}
	return data[len(data)-1]
}

func isNextActionDeeplinkSupported(ctx context.Context, dl *deeplink.Deeplink, conf *genconf.Lending) (bool, error) {
	switch dl.GetScreen() {
	case deeplink.Screen_LOANS_ALTERNATE_OFFER_SCREEN:
		so := &preapprovedloans.LoansAlternateOfferScreenOptions{}
		err := anyPb.UnmarshalTo(dl.GetScreenOptionsV2(), so, proto.UnmarshalOptions{})
		if err != nil {
			return false, errors.Wrap(err, "error in unmarshalling screen options")
		}
		// LOANS_ALTERNATE_OFFER_SCREEN is being used in the below 2 flows
		// 1. Second look flow: Show user an alternate offer if current loan request is failed
		// 2. Revised offer flow: Show user revised offer details if required based on the real time offer
		// we need to check real time offer update app version only in the second flow
		if so.GetAlternateOfferData().GetContinue().GetDeeplink().GetScreen() != deeplink.Screen_REVISED_LOAN_OFFER_DETAILS_SCREEN {
			return true, nil
		}
		platform, version := epificontext.AppPlatformAndVersion(ctx)
		switch platform {
		case commontypes.Platform_ANDROID:
			return version >= conf.PreApprovedLoan().RealTimeOfferUpdateAppVersionConstraintConfig().MinAndroidVersion(), nil
		case commontypes.Platform_IOS:
			return version >= conf.PreApprovedLoan().RealTimeOfferUpdateAppVersionConstraintConfig().MinIOSVersion(), nil
		default:
			return false, fmt.Errorf("unsupported platform: %s", platform.String())
		}
	default:
		return true, nil
	}
}

// nolint: funlen
func (s *Service) getApplicationStatusScreenV2(ctx context.Context, loanReq *palBePb.LoanRequest, currentStepExecution *palBePb.LoanStepExecution, req *palFePb.GetApplicationStatusRequest) (*deeplink.Deeplink, error) {
	startTime := time.Now()
	activity := ""
	ownership := getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(loanReq.GetVendor()), palFeEnumsPb.LoanProgram(loanReq.GetLoanProgram()))
	defer func() {
		if activity != "" {
			s.PushLoanApplicationFlowEvent(ctx, loanReq.GetActorId(), ownership, activity, time.Since(startTime), loanReq.GetId(), loanReq.GetStatus().String(), loanReq.GetSubStatus().String(), nil)
		}
	}()

	// todo(Anupam): Do a prefix match
	if loanReq.GetVendor() == palBePb.Vendor_ABFL && loanReq.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION {
		if req.GetWebviewParams().GetExitUrl() == "https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu" {
			return deeplinkProvider.GetLoanDashboardScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), loanReq.GetActorId())
		}
	}

	if loanReq.GetNextAction() != nil {
		loanReq.NextAction = s.handleStatusPollBeNextAction(ctx, loanReq, currentStepExecution, req, deeplinkProvider)
		isDlSupported, err := isNextActionDeeplinkSupported(ctx, loanReq.GetNextAction(), s.genconf.Lending())
		if err != nil {
			return nil, errors.Wrap(err, "error in checking if deeplink is supported")
		}
		if !isDlSupported {
			platform, _ := epificontext.AppPlatformAndVersion(ctx)
			return deeplinkProvider.GetForceUpgradeLandingResponse(platform, loanReq.GetOfferId(), "", deeplinkProvider.GetLoanHeader()), nil
		}
		return loanReq.GetNextAction(), nil
	}
	switch loanReq.GetStatus() {
	default:
		return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED:
		activity = events.ActivityVkyc
		return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING:
		switch loanReq.GetSubStatus() {
		default:
			return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC,
			palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_VKYC_IN_PROGRESS:
			activity = events.ActivityVkyc
			return s.getVkycDeeplink(ctx, loanReq, deeplinkProvider)
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_IN_PROGRESS:
			activity = events.ActivityLiveness
			return deeplinkProvider.GetAuthPollScreen(deeplinkProvider.GetLoanHeader(), currentStepExecution.GetOrchId()), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS:
			activity = events.ActivityLiveness
			return deeplinkProvider.GetAuthPollScreen(deeplinkProvider.GetLoanHeader(), currentStepExecution.GetOrchId()), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW,
			palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_MANUAL_REVIEW_IN_PROGRESS:
			activity = events.ActivityManualReview
			// TODO(@prasoon): Move to manual review error view sreen once it is client issue is fixed
			// return baseprovider.GetLivenessManualReviewStatusScreen(), nil
			return deeplinkProvider.GetLoanLandingInfo(ctx, deeplinkProvider.GetLoanHeader(), nil), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_OTP:
			activity = events.ActivityOtpVerification
			return deeplinkProvider.GetLoanApplicationConfirmationViaOtpScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanReq, nil, false, ""), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_OTP_GENERATED:
			activity = events.ActivityOtpVerification
			return deeplinkProvider.GetLoanApplicationConfirmationViaOtpScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanReq, nil, true, ""), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP:
			activity = events.ActivityOtpVerification
			return deeplinkProvider.GetLoanApplicationConfirmationViaOtpIncorrectScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanReq, currentStepExecution, ""), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS_AT_BANK:
			activity = events.ActivityKfs
			return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS,
			palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_KFS_IN_PROGRESS:
			activity = events.ActivityKfs
			return deeplinkProvider.GetESignScreen(deeplinkProvider.GetLoanHeader(), loanReq.GetId()), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION:
			activity = events.ActivityProfileValidation
			return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
		}
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED:
		switch loanReq.GetSubStatus() {
		default:
			return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP:
			activity = events.ActivityOtpVerification
			return deeplinkProvider.GetLoanApplicationConfirmationViaOtpIncorrectScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanReq, currentStepExecution, ""), nil
		}
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED:
		return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED:
		switch loanReq.GetSubStatus() {
		default:
			return getStatusPollScreen(ctx, deeplinkProvider, loanReq.GetId())
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP:
			activity = events.ActivityOtpVerification
			return deeplinkProvider.GetLoanApplicationConfirmationViaOtpIncorrectScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanReq, currentStepExecution, ""), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR:
			activity = events.ActivityConfirmApplication
			return baseprovider.GetLoanApplicationStatusScreenDeepLink(ctx, loanReq), nil
		}
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED,
		palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION:
		switch loanReq.GetSubStatus() {
		default:
			if loanReq.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
				return deeplinkProvider.GetLoanLandingInfo(ctx, deeplinkProvider.GetLoanHeader(), nil), nil
			}
			return deeplinkProvider.GetGenericErrorScreen(ctx, deeplinkProvider.GetLoanHeader()), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED:
			activity = events.ActivityOtpVerification
			return baseprovider.GetLoanApplicationConfirmationViaOtpAttemptsExhaustedScreenDeepLink(), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_LIVENESS,
			palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_FACE_MATCH,
			palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_MANUAL_REVIEW:
			activity = events.ActivityLiveness
			return baseprovider.GetLivenessFailedScreen(), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_VKYC:
			activity = events.ActivityVkyc
			return baseprovider.GetVkycFailedScreen(), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_PROFILE_VALIDATION:
			activity = events.ActivityProfileValidation
			return baseprovider.GetProfileValidationFailedScreen(), nil
		case palBePb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INSUFFICIENT_LIMIT:
			activity = events.ActivityLimitCheck
			return baseprovider.GetLimitCheckErrorScreen(isNewIconNeeded(ctx)), nil
		}
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_DISBURSED:
		activity = events.ActivityConfirmApplication
		return baseprovider.GetLoanApplicationStatusScreenDeepLink(ctx, loanReq), nil
	case palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS:
		if loanReq.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
			return deeplinkProvider.GetLoanLandingInfo(ctx, deeplinkProvider.GetLoanHeader(), nil), nil
		}
		activity = events.ActivityLoanAccountCreation
		return baseprovider.GetLoanApplicationStatusScreenDeepLink(ctx, loanReq), nil
	}
}

func isNewIconNeeded(ctx context.Context) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	switch platform {
	case commontypes.Platform_IOS:
		return version > 362
	case commontypes.Platform_ANDROID:
		return version > 263
	default:
		return false
	}
}

func getStatusPollScreen(ctx context.Context, dlProvider provider.IDeeplinkProvider, requestId string) (*deeplink.Deeplink, error) {
	return dlProvider.GetApplicationStatusPollScreenDeepLink(ctx, dlProvider.GetLoanHeader(), requestId)
}

func (s *Service) getVkycDeeplink(ctx context.Context, lr *palBePb.LoanRequest, dlProvider provider.IDeeplinkProvider) (*deeplink.Deeplink, error) {
	res, err := s.vkycClient.GetVKYCSummary(ctx, &vkycPb.GetVKYCSummaryRequest{ActorId: lr.GetActorId()})
	if te := epifigrpc.RPCError(res, err); te != nil {
		if !res.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(te, "failure in GetVKYCSummary")
		}
	}
	switch res.GetVkycRecord().GetVkycSummary().GetStatus() {
	case vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW, vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED:
		return baseprovider.GetVkycStatusScreen(), nil
	default:
		return dlProvider.GetVkycPendingScreen(dlProvider.GetLoanHeader(), lr.GetId()), nil
	}
}

func getFloat64fromTypesMoney(tm *typesPb.Money) float64 {
	fm, _ := money.ToDecimal(tm.GetBeMoney()).Float64()
	return fm
}

func getFloat64fromMoney(tm *money2.Money) float64 {
	fm, _ := money.ToDecimal(tm).Float64()
	return fm
}

func getEventsLoanDetailsFromBeResponse(beRes *palBePb.GetOfferDetailsResponse) *events.LoanDetails {
	return &events.LoanDetails{
		Amount:          getFloat64fromMoney(beRes.GetLoanInfo().GetAmount()),
		TenureInMonths:  beRes.GetLoanInfo().GetTenureInMonths(),
		DisbursalAmount: getFloat64fromMoney(beRes.GetLoanInfo().GetDisbursalAmount()),
		EmiAmount:       getFloat64fromMoney(beRes.GetLoanInfo().GetEmiAmount()),
		TotalPayable:    getFloat64fromMoney(beRes.GetLoanInfo().GetTotalPayable()),
		TotalDeductions: getFloat64fromMoney(beRes.GetLoanInfo().GetDeductions().GetTotalDeductions()),
		Gst:             getFloat64fromMoney(beRes.GetLoanInfo().GetDeductions().GetGst()),
		ProcessingFee:   getFloat64fromMoney(beRes.GetLoanInfo().GetDeductions().GetProcessingFee()),
		AdvanceInterest: getFloat64fromMoney(beRes.GetLoanInfo().GetDeductions().GetAdvanceInterest()),
		OfferInfo: events.OfferInfo{
			OfferId:           beRes.GetOfferId(),
			MinLoanAmount:     getFloat64fromMoney(beRes.GetOfferInfo().GetMinLoanAmount()),
			MaxLoanAmount:     getFloat64fromMoney(beRes.GetOfferInfo().GetMaxLoanAmount()),
			InterestRate:      beRes.GetOfferInfo().GetInterestRate(),
			MinTenureInMonths: beRes.GetOfferInfo().GetMinTenureInMonths(),
			MaxTenureInMonths: beRes.GetOfferInfo().GetMaxTenureInMonths(),
			MaxEmiAmount:      getFloat64fromMoney(beRes.GetOfferInfo().GetMaxEmiAmount()),
		},
	}
}

func getEventsLoanInfoDetailsFromBeResponse(beRes *palBePb.GetLoanDetailsResponse) *events.LoanInfoDetails {
	return &events.LoanInfoDetails{
		LoanType: beRes.GetLoanAccount().GetLoanType().String(),
		IfscCode: beRes.GetLoanAccount().GetIfscCode(),
		LoanAmountInfo: events.LoanAmountInfo{
			LoanAmount:         getFloat64fromMoney(beRes.GetLoanAccount().GetLoanAmountInfo().GetLoanAmount()),
			DisbursedAmount:    getFloat64fromMoney(beRes.GetLoanAccount().GetLoanAmountInfo().GetDisbursedAmount()),
			OutstandingAmount:  getFloat64fromMoney(beRes.GetLoanAccount().GetLoanAmountInfo().GetOutstandingAmount()),
			TotalPayableAmount: getFloat64fromMoney(beRes.GetLoanAccount().GetLoanAmountInfo().GetTotalPayableAmount()),
		},
		OfferId: beRes.GetLoanRequest().GetOfferId(),
	}
}

func convertBeToFeTransactionAttributeForTpapFlow(beTxnAttribute *bePayPb.TransactionAttribute) (*feTxnPb.TransactionAttribute, error) {
	feProtocol, ok := fePayPb.GetFePaymentProtocol(beTxnAttribute.GetPaymentProtocol())
	if !ok {
		return nil, fmt.Errorf("failed to convert BE to FE payment protocol")
	}
	return &feTxnPb.TransactionAttribute{
		PayerAccountId:                beTxnAttribute.GetPayerAccountId(),
		TransactionId:                 beTxnAttribute.GetRequestId(),
		MerchantRefId:                 beTxnAttribute.GetMerchantRefId(),
		PaymentProtocol:               feProtocol,
		ReferenceUrl:                  beTxnAttribute.GetReferenceUrl(),
		PayeeActorName:                beTxnAttribute.GetPayeeActorName(),
		PayerMaskedAccountNumber:      beTxnAttribute.GetPayerMaskedAccountNumber(),
		Amount:                        typesPb.GetFromBeMoney(beTxnAttribute.GetAmount()),
		Remarks:                       beTxnAttribute.GetRemarks(),
		PayerPaymentInstrument:        beTxnAttribute.GetPayerPaymentInstrument(),
		PayeePaymentInstrument:        beTxnAttribute.GetPayeePaymentInstrument(),
		DisplayPayeePaymentInstrument: beTxnAttribute.GetDisplayPayeePaymentInstrument(),
		RequestId:                     beTxnAttribute.GetRequestId(),
	}, nil
}

func convertBeToFeTransactionAttribute(beTxnAttribute *bePayPb.TransactionAttribute) (*feTxnPb.TransactionAttribute, error) {
	feProtocol, ok := fePayPb.GetFePaymentProtocol(beTxnAttribute.GetPaymentProtocol())
	if !ok {
		return nil, fmt.Errorf("failed to convert BE to FE payment protocol")
	}
	return &feTxnPb.TransactionAttribute{
		PayerAccountId:                beTxnAttribute.GetPayerAccountId(),
		TransactionId:                 beTxnAttribute.GetRequestId(),
		MerchantRefId:                 beTxnAttribute.GetMerchantRefId(),
		PaymentProtocol:               feProtocol,
		ReferenceUrl:                  beTxnAttribute.GetReferenceUrl(),
		PayeeActorName:                beTxnAttribute.GetPayeeActorName(),
		PayerMaskedAccountNumber:      beTxnAttribute.GetPayerMaskedAccountNumber(),
		Amount:                        typesPb.GetFromBeMoney(beTxnAttribute.GetAmount()),
		Remarks:                       beTxnAttribute.GetRemarks(),
		PayerPaymentInstrument:        beTxnAttribute.GetPayerPaymentInstrumentId(),
		PayeePaymentInstrument:        beTxnAttribute.GetPayeePaymentInstrumentId(),
		DisplayPayeePaymentInstrument: beTxnAttribute.GetDisplayPayeePaymentInstrument(),
		RequestId:                     beTxnAttribute.GetRequestId(),
	}, nil
}

func (s *Service) isUpdateNeeded(ctx context.Context, platform commontypes.Platform, version int, vendor palBePb.Vendor, lp palBePb.LoanProgram, actorId string) bool {
	androidVersion, iosVersion := s.getAndroidIosMinVersionsByVendorAndLoanProgram(vendor, lp)

	switch platform {
	case commontypes.Platform_ANDROID:
		if version < androidVersion {
			return true
		}
	case commontypes.Platform_IOS:
		if version < iosVersion {
			return true
		}
	default:
		logger.Error(ctx, "unhandled platform")
		return false
	}

	segmentConfig := s.genconf.Lending().PreApprovedLoan().MinKycUsersReleaseConfig()
	isUpdateNeededOnSegment, isUpdateNeededOnSegmentErr := s.isUpdateNeededOnSegmentExpression(ctx, actorId, segmentConfig.SegmentExpression(), platform, version, lp, []palBePb.LoanProgram{palBePb.LoanProgram_LOAN_PROGRAM_FLDG, palBePb.LoanProgram_LOAN_PROGRAM_STPL}, segmentConfig.MinAndroidVersion(), segmentConfig.MinIOSVersion())
	if isUpdateNeededOnSegmentErr != nil {
		// not returning the error for best effort
		logger.Error(ctx, "error in checking the segment of the user", zap.Error(isUpdateNeededOnSegmentErr))
	}
	return isUpdateNeededOnSegment
}

func (s *Service) isUpdateNeededOnSegmentExpression(
	ctx context.Context,
	actorId string,
	segmentExpression string,
	platform commontypes.Platform,
	version int,
	lp palBePb.LoanProgram,
	eligibleLoanPrograms []palBePb.LoanProgram,
	minAndroidVersion int,
	minIosVersion int,
) (bool, error) {
	if segmentExpression == "" || segmentExpression == "IsMember('')" {
		return false, nil
	}

	if lo.Contains(eligibleLoanPrograms, lp) {
		switch platform {
		case commontypes.Platform_ANDROID:
			if version >= minAndroidVersion {
				return false, nil
			}
		case commontypes.Platform_IOS:
			if version >= minIosVersion {
				return false, nil
			}
		default:
			return false, errors.New("unhandled platform")
		}

		isMemberOfExpressionsRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
			ActorId: actorId,
			SegmentIdExpressions: []string{
				segmentExpression,
			},
		})
		if err = epifigrpc.RPCError(isMemberOfExpressionsRes, err); err != nil {
			return true, errors.Wrap(err, "failed to check if actor is member of segment")
		}

		segmentExpressionMembership := isMemberOfExpressionsRes.GetSegmentExpressionMembershipMap()[segmentExpression]

		if segmentExpressionMembership.GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK {
			return true, errors.Errorf("segment expression %s failed with status %s", segmentExpression, segmentExpressionMembership.GetSegmentExpressionStatus())
		}

		IsActorInSegment := segmentExpressionMembership.GetIsActorMember()

		if IsActorInSegment {
			logger.Info(ctx, "User present in minKyc users segment :: not eligible to set mandate on fi account")
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) getAndroidIosMinVersionsByVendorAndLoanProgram(vendor palBePb.Vendor, loanProgram palBePb.LoanProgram) (int, int) {
	switch {
	case vendor == palBePb.Vendor_FEDERAL && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return s.conf.Lending.PreApprovedLoan.FederalPlAppVersionConstraintConfig.MinAndroidVersion, s.conf.Lending.PreApprovedLoan.FederalPlAppVersionConstraintConfig.MinIOSVersion
	case vendor == palBePb.Vendor_LIQUILOANS && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return s.conf.Lending.PreApprovedLoan.LiquiloansPlAppVersionConstraintConfig.MinAndroidVersion, s.conf.Lending.PreApprovedLoan.LiquiloansPlAppVersionConstraintConfig.MinIOSVersion
	case vendor == palBePb.Vendor_LIQUILOANS && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return s.conf.Lending.PreApprovedLoan.LiquiloansEsAppVersionConstraintConfig.MinAndroidVersion, s.conf.Lending.PreApprovedLoan.LiquiloansEsAppVersionConstraintConfig.MinIOSVersion
	case vendor == palBePb.Vendor_IDFC && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return s.conf.Lending.PreApprovedLoan.IdfcPlAppVersionConstraintConfig.MinAndroidVersion, s.conf.Lending.PreApprovedLoan.IdfcPlAppVersionConstraintConfig.MinIOSVersion
	case vendor == palBePb.Vendor_LIQUILOANS && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_FLDG:
		return s.conf.Lending.PreApprovedLoan.LiquiloansFldgAppVersionConstraintConfig.MinAndroidVersion, s.conf.Lending.PreApprovedLoan.LiquiloansFldgAppVersionConstraintConfig.MinIOSVersion
		// todo: need to add acq to lend confing separately if needed, for now put it alongwith filite
	case vendor == palBePb.Vendor_LIQUILOANS && (loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL || loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND):
		return s.genconf.Lending().PreApprovedLoan().LiquiloansFlPlAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().LiquiloansFlPlAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_LIQUILOANS && (loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION || loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL):
		return s.genconf.Lending().PreApprovedLoan().LiquiloansNonFiCoreAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().LiquiloansNonFiCoreAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_FIFTYFIN && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_LAMF:
		return s.genconf.Lending().PreApprovedLoan().FiftyfinLamfAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().FiftyfinLamfAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_ABFL && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return s.genconf.Lending().PreApprovedLoan().AbflPlAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().AbflPlAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_ABFL && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return s.genconf.Lending().PreApprovedLoan().AbflPwaAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().AbflPwaAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_MONEYVIEW && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return s.genconf.Lending().PreApprovedLoan().MvNonFiCorePwaAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().MvNonFiCorePwaAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_MONEYVIEW && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return s.genconf.Lending().PreApprovedLoan().MvPlAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().MvPlAppVersionConstraintConfig().MinIOSVersion()
	case loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB ||
		(vendor == palBePb.Vendor_LIQUILOANS && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION):
		return s.genconf.Lending().PreApprovedLoan().RealTimeEtbEligibilityAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().RealTimeEtbEligibilityAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_LIQUILOANS && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_STPL:
		return s.genconf.Lending().PreApprovedLoan().LiquiloansStplAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().LiquiloansStplAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_FEDERAL && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return s.genconf.Lending().PreApprovedLoan().FederalAaAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().FederalAaAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_LIQUILOANS && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		return s.genconf.Lending().PreApprovedLoan().RealTimeSubventionAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().RealTimeSubventionAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_LIQUILOANS && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		return s.genconf.Lending().PreApprovedLoan().RealTimeStplAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().RealTimeStplAppVersionConstraintConfig().MinIOSVersion()
	case (vendor == palBePb.Vendor_STOCK_GUARDIAN_LSP && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION) || (vendor == palBePb.Vendor_EPIFI_TECH && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY):
		return s.genconf.Lending().PreApprovedLoan().StockGuradianRealTimeSubvAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().StockGuradianRealTimeSubvAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_LENDEN && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return s.genconf.Lending().PreApprovedLoan().LendenPlAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().LendenPlAppVersionConstraintConfig().MinIOSVersion()
	case vendor == palBePb.Vendor_FEDERAL && loanProgram == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		return s.genconf.Lending().PreApprovedLoan().FederalRealTimeDistNtbAppVersionConstraintConfig().MinAndroidVersion(), s.genconf.Lending().PreApprovedLoan().FederalRealTimeDistNtbAppVersionConstraintConfig().MinIOSVersion()
	default:
		return 10000, 10000
	}
}

func (s *Service) isDownTime(ctx context.Context, downtimeConfig *config.Downtime) bool {
	downTimeStart, err := time.ParseInLocation("02-01-2006T15:04:05", downtimeConfig.Start, datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed to fetch downtime start time")
		return false
	}

	downTimeEnd, err := time.ParseInLocation("02-01-2006T15:04:05", downtimeConfig.End, datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed to fetch downtime end time")
		return false
	}
	if time.Now().Before(downTimeEnd) && time.Now().After(downTimeStart) {
		return true
	}
	return false
}

func (s *Service) skipLandingScreen(ctx context.Context, header *palFeEnumsPb.LoanHeader) bool {
	if isFiftyFinLamfLoanFlow(header) {
		return false
	}
	constraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().SkipLandingScreenConstraintConfig())
	appVersionConstraint := release.NewAppVersionConstraint()
	appVersionEnabled, _ := appVersionConstraint.Evaluate(ctx, constraintData, nil)
	if !appVersionEnabled || header.GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		return false
	}
	return true
}

// GetCustomerNameFromActorId To get customer name from Actor Id
func (s *Service) GetCustomerNameFromActorId(ctx context.Context, actorId string) (string, error) {
	user, err := s.GetUserByActorId(ctx, actorId)
	if err != nil {
		return "", err
	}
	return user.GetProfile().GetKycName().ToString(), nil
}

// GetUserByActorId To get user entity from actor Id
func (s *Service) GetUserByActorId(ctx context.Context, actorId string) (*userPb.User, error) {
	actorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to GetActorById")
	}
	userRes, err := s.usersClient.GetUser(ctx,
		&userPb.GetUserRequest{
			Identifier:        &userPb.GetUserRequest_Id{Id: actorRes.GetActor().GetEntityId()},
			WantPhotoInBase64: false,
		})
	if te := epifigrpc.RPCError(userRes, err); te != nil {
		return nil, errors.Wrap(te, "failed to get user from user service")
	}
	return userRes.GetUser(), nil
}

func getBeOtpFlow(flow palFePb.ConfirmApplicationRequest_OtpFlow) palBePb.ConfirmApplicationRequest_OtpFlow {
	// Earlier OTP_FLOW_E_SIGN was harcoded on client side in ConfirmApplication rpc request, BE never used to send this in
	// preApprovedLoanApplicationConfirmationViaOtpScreenOptions.
	// for android version > 343 and ios version > 500
	//	 client side harcoding is removed due to new enum OTP_FLOW_ADD_ALTERNATE_PHONE_NUMBER, and they will send enum which is received in above screen options.
	//	So, now in ConfirmApplication rpc request client will send unspecified for esign otp flow since never passed in screenoptions,
	//	so handled it here.
	switch flow {
	case palFePb.ConfirmApplicationRequest_OTP_FLOW_E_SIGN:
		return palBePb.ConfirmApplicationRequest_OTP_FLOW_E_SIGN
	default:
		return palBePb.ConfirmApplicationRequest_OTP_FLOW_E_SIGN
	}
}

// nolint: unparam
func getVendorFromBeTables(lr *palBePb.LoanRequest, lo *palBePb.LoanOffer, la *palBePb.LoanAccount, lApp *palBePb.LoanApplicant, loec *palBePb.LoanOfferEligibilityCriteria) palBePb.Vendor {
	switch {
	case la != nil:
		return la.GetVendor()
	case lr != nil:
		return lr.GetVendor()
	case lo != nil:
		return lo.GetVendor()
	case lApp != nil:
		return lApp.GetVendor()
	case loec != nil:
		return loec.GetVendor()
	default:
		return palBePb.Vendor_VENDOR_UNSPECIFIED
	}
}

func getLpFromBeTables(lr *palBePb.LoanRequest, lo *palBePb.LoanOffer, la *palBePb.LoanAccount, lApp *palBePb.LoanApplicant) palBePb.LoanProgram {
	switch {
	case la != nil:
		return la.GetLoanProgram()
	case lr != nil:
		return lr.GetLoanProgram()
	case lo != nil:
		return lo.GetLoanProgram()
	case lApp != nil:
		return lApp.GetLoanProgram()
	default:
		return palBePb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
}

var palClientCallbackTypeFeToBeMap = map[palFePb.ClientCallbackRequest_Type]palBePb.ClientCallbackRequest_Type{
	palFePb.ClientCallbackRequest_TYPE_UNSPECIFIED: palBePb.ClientCallbackRequest_TYPE_UNSPECIFIED,
	palFePb.ClientCallbackRequest_MANDATE:          palBePb.ClientCallbackRequest_MANDATE,
	palFePb.ClientCallbackRequest_E_SIGN:           palBePb.ClientCallbackRequest_E_SIGN,
	palFePb.ClientCallbackRequest_SI:               palBePb.ClientCallbackRequest_SI,
}

var palClientCallbackResultFeToBeMap = map[palFePb.ClientCallbackRequest_Result]palBePb.ClientCallbackRequest_Result{
	palFePb.ClientCallbackRequest_RESULT_UNSPECIFIED: palBePb.ClientCallbackRequest_RESULT_UNSPECIFIED,
	palFePb.ClientCallbackRequest_SUCCESS:            palBePb.ClientCallbackRequest_SUCCESS,
	palFePb.ClientCallbackRequest_INITIATED:          palBePb.ClientCallbackRequest_INITIATED,
	palFePb.ClientCallbackRequest_FAILED:             palBePb.ClientCallbackRequest_FAILED,
	palFePb.ClientCallbackRequest_PENDING:            palBePb.ClientCallbackRequest_PENDING,
}

func sanitizeLoanAddresses(ctx context.Context, addresses map[string]*typesPb.Addresses) ([]*palFePb.GetLoanAddressesResponse_AddressWithType, error) {
	var loanAddresses []*palFePb.GetLoanAddressesResponse_AddressWithType

	sortedAddresses := getSortedAddressesForLoan(addresses)
	uniqueAddresses := addressPkg.DedupeAddressesWithType(sortedAddresses)

	for _, addressWithType := range uniqueAddresses {
		addressType := addressWithType.GetType()
		address := addressWithType.GetAddress()

		replaceInvalidEntities(ctx, address)
		clientPostalAddr, err := convertToClientPostalAddressType(address)
		if err != nil {
			logger.Error(ctx, "address format mismatch.", zap.Error(err))
			return nil, err
		}
		loanAddresses = append(loanAddresses, &palFePb.GetLoanAddressesResponse_AddressWithType{
			Type:    addressType,
			Address: clientPostalAddr,
		})
	}

	return loanAddresses, nil
}

func getSortedAddressesForLoan(allAddresses map[string]*typesPb.Addresses) []*typesPb.AddressWithType {
	sortedAddresses := make([]*typesPb.AddressWithType, 0)

	sortingOrder := []typesPb.AddressType{
		typesPb.AddressType_LOAN_COMMUNICATION,
		typesPb.AddressType_SHIPPING,
		typesPb.AddressType_MAILING,
		typesPb.AddressType_PERMANENT,
	}

	for _, addressType := range sortingOrder {
		addresses, ok := allAddresses[addressType.String()]
		if !ok {
			continue
		}

		address := addresses.GetAddresses()[0]
		sortedAddresses = append(sortedAddresses, &typesPb.AddressWithType{
			Type:    addressType,
			Address: address,
		})
	}
	return sortedAddresses
}

// Replaced value for entities of a address that can be empty
func replaceInvalidEntities(ctx context.Context, address *postaladdress.PostalAddress) {
	if !addressPkg.IsValidAddressEntity(ctx, "Locality", address.GetLocality()) {
		address.Locality = ""
	}
	if !addressPkg.IsValidAddressEntity(ctx, "Sublocality", address.GetSublocality()) {
		address.Sublocality = ""
	}
}

func convertToClientPostalAddressType(postalAddr *postaladdress.PostalAddress) (*typesPb.PostalAddress, error) {
	addr := &typesPb.PostalAddress{}
	if err := copier.Copy(addr, postalAddr); err != nil {
		return nil, fmt.Errorf("failed to copy address: %w", err)
	}
	return addr, nil
}

// nolint: funlen
func getEventsOwnership(vendor palFeEnumsPb.Vendor, lp palFeEnumsPb.LoanProgram) string {
	switch lp {
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return events.OwnershipLiquiloansEs
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLiquiloansPL
		case palFeEnumsPb.Vendor_FEDERAL_BANK:
			return events.OwnershipFederal
		case palFeEnumsPb.Vendor_IDFC:
			return events.OwnershipIDFCPL
		case palFeEnumsPb.Vendor_MONEYVIEW:
			return events.OwnershipMoneyViewPL
		case palFeEnumsPb.Vendor_ABFL:
			return events.OwnershipAbflPl
		default:
			return events.OwnershipFederal
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLiquiloansFLDG
		default:
			return events.OwnershipLiquiloansFLDG
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLiquiloansSTPL
		default:
			return events.OwnershipLiquiloansSTPL
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLiquiloansFiLitePl
		default:
			return events.OwnershipLiquiloansFiLitePl
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLiquiloansAcqToLendPl
		default:
			return events.OwnershipLiquiloansAcqToLendPl
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF:
		switch vendor {
		case palFeEnumsPb.Vendor_FIFTYFIN:
			return events.OwnershipFiftyfinLAMF
		default:
			return events.OwnershipFiftyfinLAMF
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLiquiloansRealtimeDist
		case palFeEnumsPb.Vendor_FEDERAL_BANK:
			return events.OwnershipFederalRealtimeDist
		case palFeEnumsPb.Vendor_LENDEN:
			return events.OwnershipLendenRealtimeDist
		case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
			return events.OwnershipStockGuardianRealTimeDist
		case palFeEnumsPb.Vendor_ABFL:
			return events.OwnershipAbflRealTimeDist
		case palFeEnumsPb.Vendor_MONEYVIEW:
			return events.OwnershipMoneyviewRealTimeDist
		default:
			return events.OwnershipLiquiloansRealtimeDist
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		switch vendor {
		case palFeEnumsPb.Vendor_EPIFI_TECH:
			return events.OwnershipEpifiRealtimeEtb
		default:
			return events.OwnershipEpifiRealtimeEtb
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLLRealtimeSubvention
		default:
			return events.OwnershipLLRealtimeSubvention
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLLRealtimeStpl
		default:
			return events.OwnershipLLRealtimeStpl
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLLNonFiCoreStpl
		default:
			return events.OwnershipLLNonFiCoreStpl
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		switch vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return events.OwnershipLLNonFiCoreSubvention
		default:
			return events.OwnershipLLNonFiCoreSubvention
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		switch vendor {
		case palFeEnumsPb.Vendor_EPIFI_TECH:
			return events.OwnershipEpifiLoanEligibility
		default:
			return events.OwnershipEpifiLoanEligibility
		}
	default:
		return events.OwnershipFederal
	}
}

var detailsTypeToConsent = map[preapprovedloans.DetailsType]consentPb.ConsentType{
	preapprovedloans.DetailsType_DETAILS_TYPE_DOB: consentPb.ConsentType_PL_IDFC_PAN_DOB,
}

var detailsTypeToErrorViewMsg = map[preapprovedloans.DetailsType][]string{
	preapprovedloans.DetailsType_DETAILS_TYPE_PAN:     {"Invalid PAN", "Uh-oh! Looks like you entered wrong PAN. Please try again."},
	preapprovedloans.DetailsType_DETAILS_TYPE_DOB:     {"Invalid DOB", "Uh-oh! Looks like you entered wrong Date of Birth. Please try again."},
	preapprovedloans.DetailsType_DETAILS_TYPE_AADHAAR: {"invalid_aadhaar", "We were not able to verify your identity. Please check the number you entered & retry!"},
}

var screenTypeToCustomPollingInfo = map[preapprovedloans.DetailsType][]string{
	preapprovedloans.DetailsType_DETAILS_TYPE_PAN:                    {"Verifying PAN details", ""},
	preapprovedloans.DetailsType_DETAILS_TYPE_DOB:                    {"Fetching\nyour KYC details", ""},
	preapprovedloans.DetailsType_DETAILS_TYPE_AADHAAR:                {"Verifying your identity...", ""},
	preapprovedloans.DetailsType_DETAILS_TYPE_PHONE_NUMBER_AND_EMAIL: {"Searching your Mutual Funds portfolio", "https://epifi-icons.pointz.in/preapprovedloan/poll_loader_pf_fetch.png"},
}

var llEmploymentFormDetailsMap = map[palFeEnumsPb.LoanProgram][][]string{
	palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL: {
		{"EMPLOYMENT TYPE", "EMPLOYMENT TYPE"},
		{"EMPLOYER NAME", "Add employer"},
		{"WORK EMAIL", "Enter your work email"},
		{"MONTHLY INCOME", "Enter monthly income"},
	},
}

func fillClientCallbackReqWithUnmarshalledReq(req *palFePb.ClientCallbackRequest, unmarshalReq *palFePb.ClientCallbackRequest) *palFePb.ClientCallbackRequest {
	if unmarshalReq.GetIdentifier() != nil {
		req.Identifier = unmarshalReq.GetIdentifier()
	}
	if unmarshalReq.GetLoanHeader() != nil {
		req.LoanHeader = unmarshalReq.GetLoanHeader()
	}
	if unmarshalReq.GetType() != palFePb.ClientCallbackRequest_TYPE_UNSPECIFIED {
		req.Type = unmarshalReq.GetType()
	}
	if unmarshalReq.GetResult() != palFePb.ClientCallbackRequest_RESULT_UNSPECIFIED {
		req.Result = unmarshalReq.GetResult()
	}
	if unmarshalReq.GetCallbackPayload() != nil {
		req.CallbackPayload = unmarshalReq.GetCallbackPayload()
	}
	return req
}

func (s *Service) fiLiteEligibilityEvaluator(ctx context.Context, fs onboarding.FeatureStatus, actorId string, res *palFePb.GetLandingInfoResponse) (bool, *palFePb.GetLandingInfoResponse) {
	// return the PLBenefits screen if user is not in INTERNAL user group
	isInternal, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_LOANS_FI_LITE).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to check if user belongs to allowed user group for PL fi lite", zap.Error(err))
		res.GetRespHeader().Status = rpcPb.StatusInternal()
		res.GetRespHeader().ErrorView = defaultErrorView
		return false, res
	}

	if !isInternal {
		if fs == onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE {
			res.Deeplink = onboardingPkg.GetOnboardingFailureScreenForPL()
		} else {
			res.Deeplink, err = onboardingPkg.GetPLBenefitsScreen()
			if err != nil {
				logger.Error(ctx, "failed to load fi lite PL benefits screen", zap.Error(err))
				res.GetRespHeader().Status = rpcPb.StatusInternal()
				res.GetRespHeader().ErrorView = defaultErrorView
				return false, res
			}
		}
		res.GetRespHeader().Status = rpcPb.StatusOk()
		return false, res
	}
	return true, res
}

// nolint: dupl
func (s *Service) showNoOfferLandingScreenV2(ctx context.Context) bool {

	constraintData := release.NewAppVersionConstraintData(s.genconf.Lending().PreApprovedLoan().LoanNonEligibleScreenV2().AppVersionConstraintConfig())
	appVersionConstraint := release.NewAppVersionConstraint()
	appVersionEnabled, _ := appVersionConstraint.Evaluate(ctx, constraintData, nil)
	return appVersionEnabled
}

func isFiftyFinLamfLoanFlow(header *palFeEnumsPb.LoanHeader) bool {
	return header.GetVendor() == palFeEnumsPb.Vendor_FIFTYFIN && header.GetLoanProgram() == palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF
}

func getBeMfPledgeDetails(input *palFePb.MutualFundsPledgeDetails) *palBePb.PledgeDetails {
	if input == nil {
		return nil
	}
	schemeList := lo.Map(input.GetFunds(), func(item *palFePb.MutualFundsPledgeDetails_MutualFund, _ int) *palBePb.PledgeDetails_MutualFunds_Scheme {
		return &palBePb.PledgeDetails_MutualFunds_Scheme{
			Isin:     item.GetIsin(),
			Quantity: item.GetQuantity(),
		}
	})
	return &palBePb.PledgeDetails{
		MutualFunds: &palBePb.PledgeDetails_MutualFunds{
			Schemes: schemeList,
		},
	}
}

func (s *Service) populateLODropOffFeedbackIdentifierInRespHeaderIfEnabled(respHeader *feHeaderPb.ResponseHeader) {
	if s.genconf.Lending().PreApprovedLoan().IsLoanOriginationDropOffFeedbackEnabled() {
		respHeader.FeedbackEngineInfo = &feHeaderPb.FeedbackEngineInfo{
			FlowIdDetails: &feHeaderPb.FlowIdentifierDetails{
				FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOAN_ORIGINATION.String(),
			},
		}
	}
}

func getBeUserIdentifierForDerivedAccId(derivedAccountId, actorId string, accountType accounts.Type) (*palBePb.PrePayLoanRequest_UserIdentifier, error) {

	var accountId string
	derivedAccountIdProto := &accounts.DerivedAccountId{}
	err := idgen.DecodeProtoFromStdBase64(derivedAccountId, derivedAccountIdProto)
	if err != nil {
		return nil, fmt.Errorf("failed to decode derived account id %s %w", derivedAccountId, err)
	}
	if derivedAccountIdProto.GetInternalAccountId() != "" {
		accountId = derivedAccountIdProto.GetInternalAccountId()
	} else if derivedAccountIdProto.GetTpapAccountId() != "" {
		accountId = derivedAccountIdProto.GetTpapAccountId()
	}

	// Manage backward compatibility for newly added field
	if accountType == accounts.Type_TYPE_UNSPECIFIED {
		accountType = accounts.Type_SAVINGS
	}
	return &palBePb.PrePayLoanRequest_UserIdentifier{
		ActorId:     actorId,
		AccountId:   accountId,
		AccountType: accountType,
	}, nil
}

func getPinRequired(credRequiredType typesPb.CredRequiredType) feTxnPb.PinRequiredType {
	switch credRequiredType {
	case typesPb.CredRequiredType_CRED_REQUIRED_TYPE_NPCI_PIN:
		return feTxnPb.PinRequiredType_NPCI
	case typesPb.CredRequiredType_CRED_REQUIRED_TYPE_FEDERAL_SECURE_PIN:
		return feTxnPb.PinRequiredType_SECURE_PIN
	case typesPb.CredRequiredType_CRED_REQUIRED_TYPE_NONE:
		return feTxnPb.PinRequiredType_NONE
	default:
		return feTxnPb.PinRequiredType_SECURE_PIN
	}
}

func (s *Service) isIdfcVkycV2Enabled(ctx context.Context, actorId string) (bool, error) {
	if actorId == "AC210111gqwbo8ccQ1aPx5h1OqaBqg==" || actorId == "AC220929NnEueCVoRDSMbKnSZCKh5w==" || actorId == "AC210816do0Zww3bQ7Suj/JNpRVElA==" || actorId == "AC201223mF/3hA5CTF6jZAzfFqxMEA==" || actorId == "AC210205jkX9PO3lRhCSP4i4W02ElA==" {
		return true, nil
	}
	// check if feature is enabled for the actor id, user group and stickiness constraint or not
	releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_LOANS_IDFC_VKYC_V2).WithActorId(actorId)
	isEnabled, err := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		return false, fmt.Errorf("error in feature release evaluation %w", err)
	}
	return isEnabled, nil
}
