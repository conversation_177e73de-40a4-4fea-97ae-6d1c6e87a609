package generator

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/home"
	homeTypesPb "github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/ui"
)

type ApplicationInProgress struct{}

func NewApplicationInProgress() *ApplicationInProgress {
	return &ApplicationInProgress{}
}

func (a *ApplicationInProgress) GetTitle(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) (*commontypes.Text, error) {
	if homeDashboardData.GetDashboardVersion() == home.DashboardVersion_DASHBOARD_VERSION_V2 {
		return dashBoardTitleCreditCardV2, nil
	}
	return dashBoardTitleCreditCard, nil
}

func (a *ApplicationInProgress) GetBody(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) (*home.HomeDashboard_Body, error) {
	var (
		res = &home.HomeDashboard_Body{}
	)

	if homeDashboardData.GetDashboardVersion() == home.DashboardVersion_DASHBOARD_VERSION_V2 {
		return a.getBodyV2(res), nil
	}

	res.DashboardIcons = []*home.Icon{
		{
			IconImage: &commontypes.Image{
				ImageUrl: getIconImage(homeDashboardData.GetCardProgram()),
			},
			Title:    applicationInProgressTitle,
			IconType: homeTypesPb.IconType_DASHBOARD_ACTION,
			BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: darkGrey}},
		},
	}
	res.WatermarkImages = watermarkImages
	res.DashboardState = home.HomeDashboard_Body_STATE_NON_ZERO
	return res, nil
}

func (a *ApplicationInProgress) GetAdditionalSettingsIcons(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) ([]*home.Icon, error) {
	return nil, nil
}

func (a *ApplicationInProgress) GetFooter(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) ([]*ui.IconTextComponent, error) {
	return []*ui.IconTextComponent{
		{
			Texts: []*commontypes.Text{
				{
					FontColor:    windsorGrey,
					BgColor:      darkGrey,
					DisplayValue: &commontypes.Text_PlainString{PlainString: "This may take up to 24 hours"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
				},
			},
		},
	}, nil
}

func (a *ApplicationInProgress) GetDeeplink(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) (*deeplink.Deeplink, error) {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
	}, nil
}

func (a *ApplicationInProgress) GetShadow(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) ([]*ui.Shadow, error) {
	return ui.GetDashboardShadow(), nil
}

func (a *ApplicationInProgress) GetPrivacyModeFooter(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) ([]*ui.IconTextComponent, error) {
	return nil, nil
}

func (a *ApplicationInProgress) GetFooterTicker(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) (*home.HomeDashboard_FooterTicker, error) {
	if homeDashboardData.GetDashboardVersion() == home.DashboardVersion_DASHBOARD_VERSION_V2 {
		return &home.HomeDashboard_FooterTicker{
			TickerItems: []*home.HomeDashboard_FooterTicker_TickerItem{
				{
					TickerContent: ui.NewITC().
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle("We’ll notify you in 24 hrs", brightGrey, commontypes.FontStyle_BUTTON_S)),
				},
			},
			MaxTickerTextWidth: home.HomeDashboard_FooterTicker_TICKER_TEXT_WIDTH_COMPACT,
		}, nil
	}
	return nil, nil
}

func (a *ApplicationInProgress) GetZeroStateImage(ctx context.Context, homeDashboardData *ffPb.HomeDashboardData) (*commontypes.VisualElement, error) {
	return nil, nil
}

func (a *ApplicationInProgress) getBodyV2(res *home.HomeDashboard_Body) *home.HomeDashboard_Body {
	res.MoneyValueV2 = []*ui.IconTextComponent{
		{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("Your application is in progress ⏱️", white, commontypes.FontStyle_HEADLINE_M),
			},
		},
	}
	res.DashboardState = home.HomeDashboard_Body_STATE_ZERO
	res.WatermarkImages = watermarkImagesV2
	return res
}
