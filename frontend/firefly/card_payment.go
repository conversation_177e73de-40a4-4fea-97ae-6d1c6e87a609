// nolint:funlen,gocritic
package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"math"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/frontend/firefly/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/firefly/internal"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
)

func (s *Service) getCustomAmountCta(ctx context.Context, cardId, actorId string, accountDetails *ffPb.AccountDetails) *deeplink.Cta {
	tpapEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_CARD_TPAP_PAYMENTS).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error in checking tpap eligibility", zap.Error(err))
		// not returning error to avoid SPOF for credit card payments
	}
	return &deeplink.Cta{
		Type: deeplink.Cta_CUSTOM,
		Text: fmt.Sprintf("Continue"),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_CUSTOM_AMOUNT_REPAYMENT_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CreditCardCustomAmountRepaymentScreenOptions{
				CreditCardCustomAmountRepaymentScreenOptions: &deeplink.CreditCardCustomAmountRepaymentScreenOptions{
					PayCta: &deeplink.Cta{
						Type: deeplink.Cta_CUSTOM,
						Text: "Pay",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
							ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{
								InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
									CardId:                 cardId,
									Workflow:               ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_BILL_PAYMENTS,
									IsDeviceUnlockRequired: false,
									RequestData: &deeplink.InitiateCardRequestScreenOptions_BillPaymentData_{
										BillPaymentData: &deeplink.InitiateCardRequestScreenOptions_BillPaymentData{},
									},
								},
							},
						},
						DisplayTheme: deeplink.Cta_PRIMARY,
						Status:       deeplink.Cta_CTA_STATUS_ENABLED,
					},
					LowFundCta: getInsufficientBalanceCta().GetCta(),
					AccountDetails: &deeplink.CreditCardCustomAmountRepaymentScreenOptions_AccountDetails{
						AccountInfo:   accountDetails.GetAccountInfo(),
						BalanceAmount: accountDetails.GetBalanceAmount(),
					},
					PartnerLogoUrl: internal.UpiFederalLogoUrl,
					TopBarDetails: &deeplink.InfoItem{
						Title:       "Credit Card Bill",
						CopyAllowed: false,
					},
					EnableTpap: tpapEnabled,
					CardId:     cardId,
				},
			},
		},
		DisplayTheme: deeplink.Cta_PRIMARY,
		Status:       deeplink.Cta_CTA_STATUS_ENABLED,
	}
}

func getSwipeToPayCta(amount *money.Money, cardId string) (*deeplink.Cta, error) {
	amountString, err := moneyPb.ToString(amount, 2)
	if err != nil {
		return nil, err
	}
	return &deeplink.Cta{
		Type: deeplink.Cta_CUSTOM,
		Text: fmt.Sprintf("Swipe to pay ₹%s", amountString),
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
			ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{
				InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
					CardId:                 cardId,
					Workflow:               ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_BILL_PAYMENTS,
					IsDeviceUnlockRequired: false,
					RequestData: &deeplink.InitiateCardRequestScreenOptions_BillPaymentData_{
						BillPaymentData: &deeplink.InitiateCardRequestScreenOptions_BillPaymentData{
							Amount: &types.Money{
								CurrencyCode: amount.GetCurrencyCode(),
								Units:        amount.GetUnits(),
								Decimals:     amount.GetNanos(),
							},
						},
					},
				},
			},
		},
		DisplayTheme: deeplink.Cta_PRIMARY,
		Status:       deeplink.Cta_CTA_STATUS_ENABLED,
	}, nil
}

func getInsufficientBalanceCta() *deeplink.InfoItemWithCta {
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Desc:        "Your balance is low. Add money to \nyour account to proceed with the payment.",
			CopyAllowed: false,
		},
		Cta: &deeplink.Cta{
			Text: "Add money to proceed",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_TRANSFER_IN,
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
	}
}

func getCardPaymentFeTopBannerDetailsByDueDate(paymentDueDay int32, dueInfo *ffBeAccountsPb.GetCreditAccountDueInformationResponse, bannerEnabled bool) *deeplink.InfoItem {
	if !bannerEnabled {
		return nil
	}
	dueDate := getCardPaymentNextPaymentDueDate(paymentDueDay)
	endOfDueDate := datetime.EndOfDay(datetime.DateToTimeV2(dueDate, datetime.IST))
	switch {
	// Late fee banner has to be shown in case min due is unpaid, and we have crossed the due date.
	case datetime.IsBefore(timestamppb.New(endOfDueDate), timestamppb.Now()) && moneyPb.Compare(moneyPb.ZeroINR().GetPb(), dueInfo.GetUnpaidMinDue()) >= 0:
		return &deeplink.InfoItem{
			Icon:  internal.WarningTriangleUrl,
			Title: "Late fees will be applied on your next bill.<a href=''> Know more</a>",
			ToolTip: &deeplink.InfoToolTip{
				Info: &deeplink.InfoBlock{
					Title: "Late fees",
					Desc:  "To avoid Late Fees, pay the Minimum Due before the last payment date. \n\nMinimum Due is only 5% of your total Outstanding Amount. \n\nThe rest + the interest charged gets carried forward to your next bill. So, try to pay more than the Minimum Due each time. \n\nElse, your credit utilisation increases — which impacts your credit score.",
				},
			},
			CopyAllowed: false,
		}
	default:
		return &deeplink.InfoItem{
			Icon:  internal.BulbUrl,
			Title: "Paying minimum due affects your credit score.<a href=''> Know more</a>",
			ToolTip: &deeplink.InfoToolTip{
				Info: &deeplink.InfoBlock{
					Title: "Paying minimum due",
					Desc:  "To avoid Late Fees, pay the Minimum Due before the last payment date. \n\nMinimum Due is only 5% of your total Outstanding Amount. \n\nThe rest + the interest charged gets carried forward to your next bill. So, try to pay more than the Minimum Due each time. \n\nElse, your credit utilisation increases — which impacts your credit score.",
				},
			},
			CopyAllowed: false,
		}
	}
}

func getCardPaymentNextPaymentDueDate(cardActionDay int32) *date.Date {
	nextDate := datetime.TimeToDateInLoc(time.Now(), datetime.IST)
	// in case the payment due date has passed for this month, the bill gen date
	// to be shown would be of next month
	if nextDate.GetDay() > cardActionDay {
		nextDate.Month = nextDate.GetMonth()%12 + 1
		// below check is to handle the case where next month would fall in the next year
		if nextDate.GetMonth() == 1 {
			nextDate.Year = nextDate.GetYear() + 1
		}
	}
	nextDate.Day = cardActionDay
	return nextDate
}

func getCardPaymentFeBottomBannerDetails() *deeplink.InfoItemWithCta {
	//Not sending title for now.
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:        internal.CalendarUrl,
			CopyAllowed: false,
		},
		//TODO: AUTOPAYCTA TO BE FILLED
		Cta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Set up autopay",
		},
	}
}

func getCardPaymentFeAccountDetails(accountInfo *savingsPb.Account, availableBalance *money.Money) *ffPb.AccountDetails {
	accountNumberLastFourDigits := accountInfo.GetAccountNo()[len(accountInfo.GetAccountNo())-4:]
	return &ffPb.AccountDetails{
		AccountInfo: &deeplink.InfoItem{
			Icon:        internal.FederalFiBadgeUrl,
			Title:       fmt.Sprintf(internal.AccountNumberFormat, accountNumberLastFourDigits),
			CopyAllowed: false,
		},
		BalanceAmount: types.GetFromBeMoney(availableBalance),
	}
}

func getCardPaymentFeAmountDetails(dueInfo *ffBeAccountsPb.GetCreditAccountDueInformationResponse, limitInfo *ffBeAccountsPb.GetCreditAccountLimitUtilisationResponse) []*ffPb.AmountDetail {
	totalDueAmount := dueInfo.GetTotalDueAmount()
	minDueAmount := dueInfo.GetUnpaidMinDue()
	feAmountDetails := make([]*ffPb.AmountDetail, 0)

	overDueDays := getOverdueDays(&date.Date{
		Year:  dueInfo.GetDueDate().GetYear(),
		Month: dueInfo.GetDueDate().GetMonth(),
		Day:   dueInfo.GetDueDate().GetDay(),
	})
	if !moneyPb.IsPositive(totalDueAmount) {
		totalDueAmount = moneyPb.ZeroINR().GetPb()
	}
	if !moneyPb.IsPositive(minDueAmount) {
		minDueAmount = moneyPb.ZeroINR().GetPb()
	}
	totalDueAmountDetails := &ffPb.AmountDetail{
		Amount:         types.GetFromBeMoney(totalDueAmount),
		AmountType:     "Total due",
		IsCustomAmount: false,
	}

	// If partial payment of due amount is done, text changes to remaining due
	if moneyPb.Compare(totalDueAmount, dueInfo.GetUnpaidTotalDue()) != 0 {
		totalDueAmountDetails.Amount = types.GetFromBeMoney(dueInfo.GetUnpaidTotalDue())
		totalDueAmountDetails.AmountType = internal.RemainingDue
	}
	//IF total due greater than zero, show a recommended badge.
	if moneyPb.Compare(moneyPb.ZeroINR().GetPb(), dueInfo.GetUnpaidTotalDue()) == -1 && overDueDays < 3 {
		totalDueAmountDetails.BadgeNote = &commontypes.Text{
			FontColor:    "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "RECOMMENDED"},
		}
	}

	//Min Due
	minDueAmountDetails := &ffPb.AmountDetail{
		Amount:         types.GetFromBeMoney(minDueAmount),
		AmountType:     "Pay minimum due • Interest charges apply",
		IsCustomAmount: false,
	}

	switch {
	case overDueDays >= 3:
		feAmountDetails = append(feAmountDetails, minDueAmountDetails, totalDueAmountDetails)
	default:
		feAmountDetails = append(feAmountDetails, totalDueAmountDetails, minDueAmountDetails)
	}

	//Current Outstanding
	outstandingAmount := limitInfo.GetLimitUtilized()
	if !moneyPb.IsPositive(outstandingAmount) {
		outstandingAmount = moneyPb.ZeroINR().GetPb()
	}
	currentOutStandingAmountDetails := &ffPb.AmountDetail{
		Amount:         types.GetFromBeMoney(outstandingAmount),
		AmountType:     "Current outstanding",
		IsCustomAmount: false,
	}

	feAmountDetails = append(feAmountDetails, currentOutStandingAmountDetails)

	//Custom Amount
	customAmountDetails := &ffPb.AmountDetail{
		AmountType:     "Pay any amount",
		IsCustomAmount: true,
	}

	feAmountDetails = append(feAmountDetails, customAmountDetails)
	return feAmountDetails
}

func getOverdueDays(dueDate *date.Date) int32 {
	endOfDueDate := datetime.EndOfDay(datetime.DateToTimeV2(dueDate, datetime.IST))
	diff := endOfDueDate.Sub(timestamppb.Now().AsTime().In(datetime.IST))
	dayDiff := (diff.Hours()) / 24
	if dayDiff < 0 {
		return int32(math.Floor(math.Abs(dayDiff)))
	}
	return 0
}
