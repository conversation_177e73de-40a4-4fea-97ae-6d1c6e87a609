package p2pinvestment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/tiering"

	"context"
	"fmt"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	p2pPb "github.com/epifi/gamma/api/frontend/p2pinvestment"
	"github.com/epifi/gamma/api/order"
	p2pPbBe "github.com/epifi/gamma/api/p2pinvestment"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/p2pinvestment/deeplinks"
	"github.com/epifi/gamma/frontend/p2pinvestment/helper"
	p2pPkg "github.com/epifi/gamma/pkg/p2pinvestment"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

const (
	checkEligibilityResultAutoRedirectDelayInSecs int32 = 5
	etaLayout                                           = "02 Jan 06"
	InvestorNotFound                                    = "Investor not found/Investor not mapped to the ifa"
)

var _ p2pPb.P2PInvestmentServer = &Service{}

type Service struct {
	// UnimplementedP2PInvestmentServer is embedded to have forward compatible implementations.
	p2pPb.UnimplementedP2PInvestmentServer
	p2PInvestmentClient p2pPbBe.P2PInvestmentClient
	omsClient           order.OrderServiceClient
	riskProfileClient   profilePb.ProfileClient
	rpcHelper           helper.IRpcHelper
	deeplinkGenerator   *deeplinks.DeeplinkGenerator
	tieringClient       tiering.TieringClient
	conf                *genconf.Config
}

func NewService(p2PInvestmentClient p2pPbBe.P2PInvestmentClient, omsClient order.OrderServiceClient, riskProfileClient profilePb.ProfileClient,
	rpcHelper helper.IRpcHelper, deeplinkGenerator *deeplinks.DeeplinkGenerator, conf *genconf.Config, tieringClient tiering.TieringClient) *Service {
	return &Service{
		p2PInvestmentClient: p2PInvestmentClient,
		omsClient:           omsClient,
		riskProfileClient:   riskProfileClient,
		rpcHelper:           rpcHelper,
		deeplinkGenerator:   deeplinkGenerator,
		conf:                conf,
		tieringClient:       tieringClient,
	}
}

func (s *Service) getEligibleDeeplink(ctx context.Context, ceRes *p2pPbBe.CheckEligibilityResponse, actorId string, autoRedirect bool, isZeroState bool) (*deeplinkPb.Deeplink, error) {
	isInternal, err := s.rpcHelper.IsInternalUser(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error in checking if user is internal")
	}
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	plansDl, cErr := deeplinks.GetChoosePlanInfoScreenOptions(s.conf.P2PInvestment(), ceRes, nil, isInternal, appPlatform, uint32(appVersion), deeplinkPb.P2PInvestmentChoosePlanInfoScreenOption_STANDARD)
	if cErr != nil {
		return nil, errors.Wrap(cErr, "failed to get investNow deeplink")
	}
	// for non invested users, show dashboard instead of the ELIGIBILITY_CHECK_RESULT_SCREEN
	if isZeroState && ceRes.GetIsEligible() {
		newLandingEnabled, fErr := s.rpcHelper.IsFeatureEnabled(ctx, actorId, typesPb.Feature_FEATURE_JUMP_NEW_LANDING_PAGE)
		if fErr != nil {
			return nil, errors.Wrap(fErr, "error in checking if new landing page is enabled")
		}
		if newLandingEnabled {
			logger.Info(ctx, "showing P2P_ZERO_STATE_LANDING_SCREEN", zap.String(logger.ACTOR_ID_V2, actorId))
			return deeplinks.GetZeroStateLandingScreen(plansDl.GetP2PInvestmentChoosePlanInfoScreenOption().GetPlanTiles()), nil
		}
		logger.Info(ctx, "showing zero state dashboard deeplink", zap.String(logger.ACTOR_ID_V2, actorId))
		return deeplinks.GetDashboardDeeplink(), nil
	}
	// scheme level scheme exhaution is not required because user has not invested yet
	investNowDl, err := deeplinks.GetChoosePlanInfoScreenOptions(s.conf.P2PInvestment(), ceRes, nil, isInternal, appPlatform, uint32(appVersion), deeplinkPb.P2PInvestmentChoosePlanInfoScreenOption_STANDARD)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get investNow deeplink")
	}

	// Get User Profile
	userRes, err := s.rpcHelper.GetUserByActorId(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to GetUserByActorId")
	}

	userProfile := userRes.GetProfile()

	return deeplinks.GetCheckEligibilityResultDeeplink(ctx, userProfile.GetPanName().GetFirstName(), ceRes, autoRedirect, checkEligibilityResultAutoRedirectDelayInSecs, investNowDl), nil
}

func (s *Service) GetDeeplink(ctx context.Context, req *p2pPb.GetDeeplinkRequest) (*p2pPb.GetDeeplinkResponse, error) {
	res := &p2pPb.GetDeeplinkResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusInternal(),
		},
	}
	dl, err := s.deeplinkGenerator.GenerateDeeplink(ctx, req.GetScreen(), &deeplinks.GenerateDeeplinkRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if err != nil {
		logger.Error(ctx, "deeplink generator failed", zap.Error(err))
		return res, nil
	}
	res.GetRespHeader().Status = rpcPb.StatusOk()
	res.Deeplink = dl
	return res, nil
}

// Withdraw RPC to withdraw some or total amount from P2P investments
// P2P withdrawal is an asynchronous flow where the withdrawal generally takes T+1 or T+2 days to reach a terminal state.
// P2P withdrawal includes making a withdrawal request to the p2p vendor and settling the withdrawal request with
// partner bank
//
// The RPC creates an order of P2P_WITHDRAWAL workflow and returns the client request id of this order
// Client can check the status of this order using the GetP2POrderStatus RPC
func (s *Service) Withdraw(ctx context.Context, req *p2pPb.WithdrawRequest) (*p2pPb.WithdrawResponse, error) {
	res := &p2pPb.WithdrawResponse{
		RespHeader: &header.ResponseHeader{},
	}
	if (money.Compare(req.GetAmount().GetBeMoney(), money.FromPaisa(100))) == -1 {
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = minimumWithdrawAmountErrorView
		return res, nil
	}
	beRes, err := s.p2PInvestmentClient.Withdraw(ctx, &p2pPbBe.WithdrawRequest{
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		WithdrawAmount: req.GetAmount().GetBeMoney(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "p2PInvestmentClient.Withdraw failed",
			zap.Error(err),
		)
		res.RespHeader.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.OrderClientReqId = beRes.GetOrderClientReqId()[0]
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetEarlyWithdrawalCharges(ctx context.Context, req *p2pPb.GetEarlyWithdrawalChargesRequest) (*p2pPb.GetEarlyWithdrawalChargesResponse, error) {
	// To be implemented as part of v1. For now, we return nil penalty charge.
	res := &p2pPb.GetEarlyWithdrawalChargesResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		EarlyWithdrawalMessage: "Early withdrawal charges: ₹",
		PenaltyCharge:          nil,
	}
	return res, nil
}

//nolint:funlen
func (s *Service) GetWithdrawMoneyAttributes(ctx context.Context, req *p2pPb.GetWithdrawMoneyAttributesRequest) (*p2pPb.GetWithdrawMoneyAttributesResponse, error) {
	errRes := &p2pPb.GetWithdrawMoneyAttributesResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusInternal(),
		},
	}

	investor, iErr := s.rpcHelper.GetInvestor(ctx, req.GetReq().GetAuth().GetActorId())
	if iErr != nil {
		logger.Error(ctx, "Investor not found", zap.Error(iErr))
	}

	giaRes, giaErr := s.rpcHelper.GetInvestmentAttributes(ctx, investor.GetId(), p2pPbBe.SchemeName_SCHEME_NAME_UNSPECIFIED)
	if giaErr != nil {
		logger.Error(ctx, "failed to get GetInvestmentAttributes", zap.Error(giaErr))
	}
	maskedOperativeAcc := giaRes.GetMaskedOperativeAccountNumber()
	beRes, err := s.p2PInvestmentClient.GetWithdrawMoneyAttributes(ctx, &p2pPbBe.GetWithdrawMoneyAttributesRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Amount:  req.GetAmount().GetBeMoney(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "p2PInvestmentClient.GetWithdrawMoneyAttributes failed",
			zap.Error(err),
		)
		return errRes, nil
	}
	amountbreakUp := []*p2pPb.GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp{}

	for _, breakUpUnit := range beRes.GetAmountBreakUp() {
		if money.IsZero(breakUpUnit.GetAmount()) {
			continue
		}
		breakUp := &p2pPb.GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails_AmountBreakUp{
			PlanDetails: p2pPkg.GetPlanDetailsForWithdrawalBreakup(ctx, breakUpUnit.GetSchemeName()),
			Amount:      typesPb.GetFromBeMoney(breakUpUnit.GetAmount()),
			SchemeName:  breakUpUnit.GetSchemeName().String(),
		}
		amountbreakUp = append(amountbreakUp, breakUp)
	}

	// this field is required in nudge screen to show what will be the future value of amount if it is keep invested
	amountIncludingMissOutReturn, sumErr := money.Sum(req.GetAmount().GetBeMoney(), beRes.GetMissOutReturn())

	if sumErr != nil {
		logger.Error(ctx, "p2PInvestmentClient.GetWithdrawMoneyAttributes failed",
			zap.Error(sumErr),
		)
		return errRes, nil
	}

	penaltyMessage := getPenaltyMessageForEarlyWithdrawal(beRes.GetAmountBreakUp())

	res := &p2pPb.GetWithdrawMoneyAttributesResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		IsNudgePresent: isNudgePresent(),
		Title:          helper.GetText(WithdrawInvestmentTitle, WithdrawInvestmentColor, commontypes.FontStyle_SUBTITLE_2),
		RequestedAmount: &p2pPb.GetWithdrawMoneyAttributesResponse_RequestedAmountTile{
			RequestedAmountTitle: helper.GetText(WithdrawInvestmentRequestedAmountTitle, WithdrawInvestmentRequestedAmountColor, commontypes.FontStyle_BODY_3),
			Amount:               req.GetAmount(),
		},
		OperativeAccountMessage: helper.GetTextWithHtml(fmt.Sprintf(OperativeAccountMessage, maskedOperativeAcc), OperativeAccountMessageColor, OperativeAccountMessageFontFamily, OperativeAccountMessageFontStyle, OperativeAccountMessageFontSize),
		AmountBreakUpDetails: &p2pPb.GetWithdrawMoneyAttributesResponse_AmountBreakUpDetails{
			Info:           helper.GetText(AmountBreakUpTitle, AmountBreakUpColor, commontypes.FontStyle_BODY_XS),
			AmountBreakUp:  amountbreakUp,
			Icon:           AmountBreakUpIcon,
			PenaltyMessage: helper.GetText(penaltyMessage, PenaltyMessageColor, commontypes.FontStyle_BODY_XS),
		},
		NudgeDetails: &p2pPb.GetWithdrawMoneyAttributesResponse_NudgeDetails{
			Title:        helper.GetText(getNudgeTitleText(getDisplayStringForAmount(beRes.GetMissOutReturn())), NudgeTitleTextColor, commontypes.FontStyle_SUBTITLE_S),
			TitleBgColor: NudgeTitleBackgroundColor,
			Desc:         helper.GetText(getNudgeDescText(getDisplayStringForAmount(req.GetAmount().GetBeMoney()), getDisplayStringForAmount(amountIncludingMissOutReturn)), NudgeDescColor, commontypes.FontStyle_SUBTITLE_S),
			Img: &commontypes.Image{
				ImageUrl: NudgeImageURL,
			},
			ProceedCta: &deeplinkPb.Cta{
				Text:         NudgeProceedCtaText,
				Deeplink:     deeplinks.GetDashboardDeeplink(),
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
			WithdrawLaterCta: &deeplinkPb.Cta{
				Text:         NudgeWithdrawLaterCtaText,
				DisplayTheme: deeplinkPb.Cta_SECONDARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
		},
		InfoItems: helper.GetWithdrawalInfoItem(),
	}
	return res, nil
}

func (s *Service) WithdrawV2(ctx context.Context, req *p2pPb.WithdrawV2Request) (*p2pPb.WithdrawV2Response, error) {
	res := &p2pPb.WithdrawV2Response{
		RespHeader: &header.ResponseHeader{},
	}
	if (money.Compare(req.GetAmount().GetBeMoney(), money.FromPaisa(100))) == -1 {
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = minimumWithdrawAmountErrorView
		return res, nil
	}
	beRes, err := s.p2PInvestmentClient.WithdrawV2(ctx, &p2pPbBe.WithdrawV2Request{
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		Amount:        req.GetAmount().GetBeMoney(),
		AmountBreakUp: getAmountBreakUpForBe(req.GetAmountBreakUp()),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "p2PInvestmentClient.WithdrawV2 failed",
			zap.Error(te),
		)
		res.RespHeader.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.OrderClientReqId = beRes.GetOrderClientReqId()[0]
	res.RespHeader.Status = rpcPb.StatusOk()
	return res, nil
}

func getPenaltyMessageForEarlyWithdrawal(breakUpDetails []*p2pPbBe.GetWithdrawMoneyAttributesResponse_AmountBreakUp) string {
	for _, breakUpUnit := range breakUpDetails {
		if breakUpUnit.GetSchemeName() == p2pPbBe.SchemeName_SCHEME_NAME_LL_DEFAULT {
			return PenaltyMessageForEarlyWithdrawal
		}
	}
	return ""
}
