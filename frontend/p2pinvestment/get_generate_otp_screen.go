package p2pinvestment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/header"
	p2pPb "github.com/epifi/gamma/api/frontend/p2pinvestment"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) GetGenerateOTPScreen(ctx context.Context, req *p2pPb.GetGenerateOTPScreenRequest) (*p2pPb.GetGenerateOTPScreenResponse, error) {

	res := &p2pPb.GetGenerateOTPScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Title: commontypes.GetTextFromStringFontColourFontStyle("Verify with an OTP",
			"#333333", commontypes.FontStyle_HEADLINE_L),
		WaitingTimeInSecs: 300,
	}

	actorId := req.GetReq().GetAuth().GetActorId()
	orderExternalId := req.GetOrderExternalId()

	beResp, err := s.rpcHelper.CreateMaturityAction(ctx, actorId, orderExternalId, req.GetRenewalType())
	if err != nil {
		logger.Error(ctx, "error while calling CreateMaturityAction", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}
	investor, err := s.rpcHelper.GetInvestor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while calling GetInvestor", zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}
	res.RenewalInvestmentRequestId = beResp.GetRenewalInvestmentRequestId()
	res.SubTitle = getSubtitleForOtpScreen(investor.GetDetails().GetPhoneNo())
	return res, nil
}

func getSubtitleForOtpScreen(number *commontypes.PhoneNumber) *commontypes.Text {
	return commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Enter the OTP sent to +%v %v", number.GetCountryCode(), number.GetNationalNumber()),
		"#8D8D8D", commontypes.FontStyle_BODY_S)
}
