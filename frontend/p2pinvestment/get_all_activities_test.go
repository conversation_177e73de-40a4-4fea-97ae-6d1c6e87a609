package p2pinvestment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"google.golang.org/protobuf/testing/protocmp"

	"github.com/google/go-cmp/cmp"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/p2pinvestment/common"
	"github.com/epifi/gamma/frontend/p2pinvestment/helper"
	"github.com/epifi/gamma/frontend/p2pinvestment/mocks"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/header"
	p2pFePb "github.com/epifi/gamma/api/frontend/p2pinvestment"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	p2pMocks "github.com/epifi/gamma/api/p2pinvestment/mocks"
	"github.com/epifi/gamma/frontend/config/genconf"
)

func TestService_GetAllActivities(t *testing.T) {
	type fields struct {
		p2PInvestmentClient *p2pMocks.MockP2PInvestmentClient
		conf                *genconf.Config
	}
	type args struct {
		ctx context.Context
		req *p2pFePb.GetAllActivitiesRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *p2pFePb.GetAllActivitiesResponse
		wantErr bool
		prepare func(*args, *fields)
	}{
		{
			name: "should successfully get all activities for not selected filter",
			args: args{
				ctx: context.Background(),
				req: &p2pFePb.GetAllActivitiesRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
				},
			},
			want: &p2pFePb.GetAllActivitiesResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				SortedActivities: []*p2pFePb.Activity{
					{
						Id:   "activity-id-1",
						Type: "Investment in Flexi 7%",
						Details: &p2pFePb.Activity_InvestmentActivityDetails{
							InvestmentActivityDetails: &p2pFePb.InvestmentActivityDetails{
								SchemeName: helper.GetText("Flexi 7%", "#646464", commontypes.FontStyle_SUBTITLE_3),
							},
						},
					},
				},
				FilterChips: []*p2pFePb.GetAllActivitiesResponse_FilterChip{
					{
						DisplayName: "All",
						FilterId:    common.DisplaySchemeName_DISPLAY_SCHEME_NAME_UNSPECIFIED.String(),
						IsSelected:  true,
					},
					{
						DisplayName: "Flexi 7%",
						FilterId:    common.DisplaySchemeName_DISPLAY_SCHEME_NAME_7_FLEXI_LIQUID.String(),
						IsSelected:  false,
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.p2PInvestmentClient.EXPECT().GetActivities(a.ctx, &p2pPb.GetActivitiesRequest{
					ActorId: "actor-id-1",
					PageContext: &rpc.PageContextRequest{
						PageSize: defaultPageSize,
					},
				}).Return(&p2pPb.GetActivitiesResponse{
					Status: rpc.StatusOk(),
					Activities: []*p2pPb.Activity{
						{
							Id:         "activity-id-1",
							Type:       p2pPb.ActivityType_ACTIVITY_TYPE_INVESTMENT,
							SchemeName: p2pPb.SchemeName_SCHEME_NAME_LL_FLEXI,
						},
					},
					InvestedSchemes: []p2pPb.SchemeName{p2pPb.SchemeName_SCHEME_NAME_LL_FLEXI},
				}, nil)
			},
		},
		{
			name: "should successfully get flexi 7% activities",
			args: args{
				ctx: context.Background(),
				req: &p2pFePb.GetAllActivitiesRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "actor-id-1",
						},
					},
					FilterId: "DISPLAY_SCHEME_NAME_7_FLEXI_LIQUID",
				},
			},
			want: &p2pFePb.GetAllActivitiesResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				SortedActivities: []*p2pFePb.Activity{
					{
						Id:   "activity-id-1",
						Type: "Investment in Flexi 7%",
						Details: &p2pFePb.Activity_InvestmentActivityDetails{
							InvestmentActivityDetails: &p2pFePb.InvestmentActivityDetails{
								SchemeName: helper.GetText("Flexi 7%", "#646464", commontypes.FontStyle_SUBTITLE_3),
							},
						},
					},
				},
				FilterChips: []*p2pFePb.GetAllActivitiesResponse_FilterChip{
					{
						DisplayName: "All",
						FilterId:    common.DisplaySchemeName_DISPLAY_SCHEME_NAME_UNSPECIFIED.String(),
						IsSelected:  false,
					},
					{
						DisplayName: "Flexi 7%",
						FilterId:    common.DisplaySchemeName_DISPLAY_SCHEME_NAME_7_FLEXI_LIQUID.String(),
						IsSelected:  true,
					},
				},
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.p2PInvestmentClient.EXPECT().GetActivities(a.ctx, &p2pPb.GetActivitiesRequest{
					ActorId: "actor-id-1",
					SchemeNameList: []p2pPb.SchemeName{p2pPb.SchemeName_SCHEME_NAME_LL_FLEXI,
						p2pPb.SchemeName_SCHEME_NAME_LL_FLEXI_REINVESTMENT},
					PageContext: &rpc.PageContextRequest{
						PageSize: defaultPageSize,
					},
				}).Return(&p2pPb.GetActivitiesResponse{
					Status: rpc.StatusOk(),
					Activities: []*p2pPb.Activity{
						{
							Id:         "activity-id-1",
							Type:       p2pPb.ActivityType_ACTIVITY_TYPE_INVESTMENT,
							SchemeName: p2pPb.SchemeName_SCHEME_NAME_LL_FLEXI,
						},
					},
					InvestedSchemes: []p2pPb.SchemeName{p2pPb.SchemeName_SCHEME_NAME_LL_FLEXI},
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			p2PInvestmentClient := p2pMocks.NewMockP2PInvestmentClient(ctr)
			rpcHelper := mocks.NewMockIRpcHelper(ctr)

			f := fields{
				p2PInvestmentClient: p2PInvestmentClient,
				conf:                conf,
			}
			if tt.prepare != nil {
				tt.prepare(&tt.args, &f)
			}
			s := &Service{
				p2PInvestmentClient: p2PInvestmentClient,
				rpcHelper:           rpcHelper,
				conf:                conf,
			}
			got, err := s.GetAllActivities(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllActivities() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(tt.want, got, opts...); diff != "" {
				t.Errorf("GetAllActivities(): got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}
