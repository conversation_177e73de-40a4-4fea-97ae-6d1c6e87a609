package usstocks

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/frontend/document_upload/client_states"
	"github.com/epifi/gamma/api/frontend/document_upload/polling"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	// TODO(mounish): move to config
	connectedAccountStatementDuration = 12 // (in months)
)

func (u *USStocksDocumentExchange) pollConnectedAccountStatementGeneration(
	ctx context.Context,
	actorId string,
	connectedAccountDocumentParams *polling.ConnectedAccountStatementParams,
) (error, string, client_states.DocumentGenerationStatus) {
	accountId := connectedAccountDocumentParams.GetAccountId()

	// fetch CA statement for last 1 year
	fromTime := time.Now().AddDate(0, -connectedAccountStatementDuration, 0)
	toTime := time.Now()

	downloadConnectedAccountBankStatementRes, err := u.connectedAccountsClient.DownloadConnectedAccountBankStatement(
		ctx,
		&connectedAccountPb.DownloadConnectedAccountBankStatementRequest{
			ActorId:            actorId,
			AccountReferenceId: accountId,
			StartTime:          timestampPb.New(fromTime),
			EndTime:            timestampPb.New(toTime),
			FileType:           typesPb.FileType_FILE_TYPE_PDF,
		})
	if err = epifigrpc.RPCError(downloadConnectedAccountBankStatementRes, err); err != nil {
		if downloadConnectedAccountBankStatementRes.GetStatus().GetCode() == uint32(connectedAccountPb.DownloadConnectedAccountBankStatementResponse_NO_TRANSACTION) {
			logger.Error(ctx, "No transactions found for given duration", zap.Error(err))
			return epifierrors.ErrRecordNotFound, "", client_states.DocumentGenerationStatus_DOCUMENT_GENERATION_STATUS_FAILED
		}
		logger.Error(ctx, "failed to download connected account bank statement", zap.Error(err))
		return err, "", client_states.DocumentGenerationStatus_DOCUMENT_GENERATION_STATUS_FAILED
	}

	imageUrl := downloadConnectedAccountBankStatementRes.GetAccountStatementFileV2().GetFileUrl()

	if !u.ussConfig.IsSofAnalysisFlowActive() {
		return nil, imageUrl, client_states.DocumentGenerationStatus_DOCUMENT_GENERATION_STATUS_COMPLETED
	}

	// first leg of storing sof. temp url is stored in sof details which is expected to be explicitely submitted by user as sof proof
	// later in the flow. temp url is used to validate that user is submitting the same doc that we have generated here
	err = u.CreateSofDetailsForAaStatement(ctx, actorId, fromTime, toTime, accountId, imageUrl)
	if err != nil {
		logger.Error(ctx, "failed to create sof details entry for user", zap.Error(err))
		return nil, "", client_states.DocumentGenerationStatus_DOCUMENT_GENERATION_STATUS_FAILED
	}

	return nil, imageUrl, client_states.DocumentGenerationStatus_DOCUMENT_GENERATION_STATUS_COMPLETED
}

func (u *USStocksDocumentExchange) uploadSofDocument(ctx context.Context, documentUrl, actorId string) error {
	resp, err := u.iftClient.UploadSOFDocument(ctx, &internationalfundtransfer.UploadSOFDocumentRequest{
		ActorId:         actorId,
		SignedUrl:       documentUrl,
		SofDocumentType: internationalfundtransfer.SOFDocumentType_SOF_DOCUMENT_TYPE_CONNECTED_ACCOUNT_STATEMENT,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		return errors.Wrap(te, "unable to upload sof document")
	}
	err = u.sofCollectionSignalEvaluator.SendSofCollectionSignal(ctx, actorId)
	if err != nil {
		return errors.New("error sending SOF collection signal")
	}
	return nil
}

func (u *USStocksDocumentExchange) CreateSofDetailsForAaStatement(ctx context.Context, actorId string, fromTime time.Time,
	toTime time.Time, accountId string, tmpDocUrl string) error {
	initSofDetailsDocParamsResp, err := u.iftClient.InitSofDetailsDocumentParams(ctx, &internationalfundtransfer.InitSofDetailsDocumentParamsRequest{
		ActorId:         actorId,
		SofDocumentType: internationalfundtransfer.SOFDocumentType_SOF_DOCUMENT_TYPE_CONNECTED_ACCOUNT_STATEMENT,
		DocumentInfo: &internationalfundtransfer.SOFDocumentInfo{
			Info: &internationalfundtransfer.SOFDocumentInfo_AaAccountDocInfo{
				AaAccountDocInfo: &internationalfundtransfer.AaAccountDocInfo{
					FromTime:    timestampPb.New(fromTime),
					ToTime:      timestampPb.New(toTime),
					AaAccountId: accountId,
					TempDocUrl:  tmpDocUrl,
				},
			},
		},
		// note that valid_till will be populated once the document is uploaded in next step,
	})
	if rpcErr := epifigrpc.RPCError(initSofDetailsDocParamsResp, err); rpcErr != nil {
		return fmt.Errorf("failed to init sof details doc params in db: %s", rpcErr)
	}
	return nil
}
