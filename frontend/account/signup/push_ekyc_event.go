package signup

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/frontend/account/signup"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/frontend/account/metrics"
	"github.com/epifi/gamma/frontend/events"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) PushEKYCEvent(ctx context.Context, req *signup.PushEKYCEventRequest) (*signup.PushEKYCEventResponse, error) {
	logger.Info(ctx, fmt.Sprintf("PushEKYCEvent Raw Response String: %v", req.GetRawResponse()),
		zap.String(logger.STATUS, req.GetEkycStatus()),
		zap.String(logger.REQUEST_TYPE, req.GetRequestType().String()),
		zap.String(logger.ERROR_CODE, req.GetErrorCode()),
		zap.String(logger.ERROR_REASON, req.GetErrorMessage()))

	customErrorMap := map[string]*struct {
		EKYCStatus string
		ErrorCode  string
	}{
		"Trust anchor for certification path not found": {
			EKYCStatus: "N",
			ErrorCode:  "CertPKNotFound",
		},
		"Internal Server Error": {
			EKYCStatus: "N",
			ErrorCode:  "InternalServerError",
		},
		"Job was cancelled": {
			EKYCStatus: "N",
			ErrorCode:  "JobCancelled",
		},
		"failed to connect": {
			EKYCStatus: "N",
			ErrorCode:  "ConnectionFailure",
		},
		"timeout": {
			EKYCStatus: "N",
			ErrorCode:  "ConnectionTimeout",
		},
		"Unable to resolve host": {
			EKYCStatus: "N",
			ErrorCode:  "HostResolutionError",
		},
	}

	if req.GetEkycStatus() == "" && req.GetErrorCode() == "" && req.GetErrorMessage() != "" {
		for k, v := range customErrorMap {
			if strings.Contains(req.GetErrorMessage(), k) {
				req.EkycStatus = v.EKYCStatus
				req.ErrorCode = v.ErrorCode
			}
		}
		// if the status is still empty, fill with unknown values
		// In this case, devs are supposed to debug by checking the error message
		if req.GetEkycStatus() == "" && req.GetErrorCode() == "" {
			req.EkycStatus = "N"
			req.ErrorCode = "UnknownErrorCode"
		}
	}

	switch req.GetRequestType() {
	case signup.PushEKYCEventRequest_REQUEST_TYPE_GENERATE_OTP:
		metrics.RecordGenerateOTPStatus(req.GetEkycStatus(), req.GetErrorCode())
	case signup.PushEKYCEventRequest_REQUEST_TYPE_VERIFY_OTP:
		metrics.RecordVerifyOTPStatus(req.GetEkycStatus(), req.GetErrorCode())
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events.NewEKYCEventServer(
			req.GetReq().GetAuth().GetActorId(),
			req.GetRequestType().String(),
			req.GetEkycStatus(),
			req.GetErrorCode(),
			req.GetErrorMessage()))
	})

	return &signup.PushEKYCEventResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}
