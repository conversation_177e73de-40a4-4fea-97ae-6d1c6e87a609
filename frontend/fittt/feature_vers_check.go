package fittt

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/frontend/config/genconf"
)

type Feature uint32

const (
	FEATURE_UNSPECIFIED Feature = iota
	AggregationTileV2
	FeaturedRulesBanners
)

func (s *Service) initAppVersionMap(conf *genconf.Config) {
	s.appVersionMap = map[commontypes.Platform]map[Feature]func() uint32{
		commontypes.Platform_ANDROID: {
			AggregationTileV2:    conf.Fittt().MinAndroidVersionToSupportAggregationTileV2,
			FeaturedRulesBanners: conf.Fittt().MinAndroidVersionToSupportFeaturedRulesBanner,
		},
		commontypes.Platform_IOS: {
			AggregationTileV2:    conf.Fittt().MinIOSVersionToSupportAggregationTileV2,
			FeaturedRulesBanners: conf.Fittt().MinIOSVersionToSupportFeaturedRulesBanner,
		},
	}
}

// nolint: unparam
func (s *Service) checkSupport(feature Feature, platform commontypes.Platform, appVers uint32) (isSupported bool, supportedVers uint32) {
	m, exist := s.appVersionMap[platform]
	if !exist {
		return false, 0
	}
	minVersion := m[feature]()
	return minVersion != 0 && appVers >= minVersion, minVersion
}

func (s *Service) checkSupportV1(platform commontypes.Platform, appVers uint32, verSupportInfo *manager.VersionSupportInfo) (bool, uint32) {
	// no restriction on version specified
	if verSupportInfo == nil {
		return true, 0
	}

	var minSupportedVersion uint32
	switch platform {
	case commontypes.Platform_ANDROID:
		minSupportedVersion = verSupportInfo.GetMinSupportedAndroidAppVersion()
	case commontypes.Platform_IOS:
		minSupportedVersion = verSupportInfo.GetMinSupportedIosAppVersion()
	default:
		return false, 0
	}
	if minSupportedVersion == 0 || appVers < minSupportedVersion {
		return false, minSupportedVersion
	}
	return true, minSupportedVersion
}
