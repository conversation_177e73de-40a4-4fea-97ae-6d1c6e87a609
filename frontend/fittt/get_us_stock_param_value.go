package fittt

import (
	"context"

	"github.com/pkg/errors"

	fePb "github.com/epifi/gamma/api/frontend/fittt"
)

func (s *Service) getUSStockParamValue(ctx context.Context, usStockId string) (*fePb.Value, error) {
	stock, err := s.usStocks.GetStock(ctx, usStockId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting stock name")
	}
	return &fePb.Value{
		Id: usStockId,
		Value: &fePb.Value_UsStockValue{
			UsStockValue: &fePb.USStockValue{
				StockId:   usStockId,
				StockName: stock.Name,
			},
		},
	}, nil
}
