package categorizer

import (
	"flag"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"testing"

	fePayTransaction "github.com/epifi/gamma/frontend/pay/transaction"
	"github.com/epifi/gamma/frontend/test"
)

var _, b, _, _ = runtime.Caller(0)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, _, teardown = test.InitTestServer()
	cfgDir := filepath.Join(b, "..", "..", "..", "frontend", "config", "values")
	displayCategoryFilePath := filepath.Join(cfgDir, conf.DisplayCategoryMappingJson)
	err := fePayTransaction.LoadDisplayCategoryMapping(displayCategoryFilePath)
	if err != nil {
		log.Fatal("error loading display categories mapping", err)
	}

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

// type mockFields struct {
//	mockBeTxnCategoryClient *mockBeCategorizer.MockTxnCategorizerClient
//	mockOrderClient         *mockOrder.MockOrderServiceClient
//	mockAaOrderClient       *mocks.MockAccountAggregatorClient
// }
//
// func initMocks(ctrl *gomock.Controller) *mockFields {
//	return &mockFields{
//		mockBeTxnCategoryClient: mockBeCategorizer.NewMockTxnCategorizerClient(ctrl),
//		mockOrderClient:         mockOrder.NewMockOrderServiceClient(ctrl),
//		mockAaOrderClient:       mocks.NewMockAccountAggregatorClient(ctrl),
//	}
// }
