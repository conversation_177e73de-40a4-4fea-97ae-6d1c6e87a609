// nolint: dupl
package providers

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	frontend "github.com/epifi/gamma/api/frontend"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	upiPb "github.com/epifi/gamma/api/frontend/upi"
	payPb "github.com/epifi/gamma/api/pay"
	types "github.com/epifi/gamma/api/typesv2"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/common"
	dataCollector "github.com/epifi/gamma/frontend/pay/transaction/payment_options/data_collector"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/intent"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/providers/args"
)

const (
	viewMoreCtaText                         = "View more"
	viewMoreCtaTextFontColor                = "#00B899"
	viewMoreCtaContainerBgColor             = "#F6F9FD"
	viewMoreCtaContainerCornerRadius  int32 = 19
	viewMoreCtaContainerTopPadding    int32 = 12
	viewMoreCtaContainerBottomPadding int32 = 12
	viewMoreCtaContainerLeftPadding   int32 = 24
	viewMoreCtaContainerRightPadding  int32 = 24

	upiAppFontColor                = "#6A6D70"
	upiAppLeftImgTextPadding int32 = 8
	upiAppLeftImgHeight      int32 = 32
	upiAppLeftImgWidth       int32 = 32
)

type IntentPaymentOptionProvider struct {
	conf *genconf.Config
}

func (i *IntentPaymentOptionProvider) GetPaymentOptionArgs(collectedData *dataCollector.CollectedData, optionsDisplay *config.OptionsDisplay, paymentAmount *types.Money) args.IPaymentOptionsArgs {
	var args []func(*intent.Args)
	// for now, intent option is always collapsible
	args = append(args, intent.WithIsCollapsible())
	if lo.Contains(optionsDisplay.SectionsToExpand, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT) {
		args = append(args, intent.WithExpandCollapsibleState(transaction.ExpandCollapseState_EXPAND_COLLAPSE_STATE_EXPANDED))
	}
	if lo.Contains(optionsDisplay.UnavailableSections, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT) {
		args = append(args, intent.WithExpandCollapsibleState(transaction.ExpandCollapseState_EXPAND_COLLAPSE_STATE_COLLAPSED),
			intent.WithPaymentOptionState(transaction.AvailabilityState_AVAILABILITY_STATE_UNAVAILABLE))
	} else {
		args = append(args, intent.WithPaymentOptionState(transaction.AvailabilityState_AVAILABILITY_STATE_AVAILABLE))
	}
	return intent.NewIntentArgs(args...)
}

func NewIntentPaymentOptionProvider(conf *genconf.Config) *IntentPaymentOptionProvider {
	return &IntentPaymentOptionProvider{conf: conf}
}

func (i *IntentPaymentOptionProvider) GetPaymentOptionType() transaction.PaymentOptionType {
	return transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT
}

func (i *IntentPaymentOptionProvider) GetPaymentOption(ctx context.Context, collectedData *dataCollector.CollectedData, paymentAmount *types.Money, vendor frontend.Vendor, optionsDisplay *config.OptionsDisplay, uiEntryPoint timeline.TransactionUIEntryPoint, accountDetails *payPb.AccountDetails) (*transaction.GetPaymentOptionsResponse_PaymentOption, error) {
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	optionArgs := i.GetPaymentOptionArgs(collectedData, optionsDisplay, paymentAmount)
	vendorIntegSpecs, err := getIntegrationSpecsForVendor(vendor, i.conf)
	if err != nil {
		// not returning error to ensure backward compatibility
		logger.Error(ctx, "error in fetching integration specs for vendor", zap.Error(err))
	}
	upiAppsInfo, getUpiAppsInfoErr := getUpiAppsInfoByPlatform(appPlatform, i.conf)
	if getUpiAppsInfoErr != nil {
		return nil, errors.Wrap(getUpiAppsInfoErr, "error fetching upi apps info by app platform")
	}
	return &transaction.GetPaymentOptionsResponse_PaymentOption{
		OptionTitle:         common.GetPaymentOptionTitle(uiEntryPoint, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT, i.conf.PaymentOptionsConfig()),
		ExpandCollapseState: optionArgs.ExpandCollapsibleState(),
		IsCollapsible:       optionArgs.IsCollapsible(),
		DownArrow:           common.GetDownArrow(),
		PaymentOptionState:  optionArgs.PaymentOptionState(),
		PaymentOptionType:   transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT,
		BackgroundColour:    widgetPb.GetBlockBackgroundColour(common.PaymentOptionBgColor),
		PaymentOptionCta:    common.GetPaymentOptionCtaV2(paymentAmount, uiEntryPoint, transaction.PaymentOptionType_PAYMENT_OPTION_TYPE_INTENT),
		Option: &transaction.GetPaymentOptionsResponse_PaymentOption_IntentOption{
			IntentOption: &transaction.IntentPaymentOption{
				IntegrationSpecs:         vendorIntegSpecs,
				UpiAppsInfo:              upiAppsInfo,
				TotalAppsToShowByDefault: i.conf.PaymentOptionsConfig().IntentOptionsConfig().TotalAppsToShowByDefault(),
				ViewMoreCta: &uiPb.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(viewMoreCtaText, viewMoreCtaTextFontColor, commontypes.FontStyle_BUTTON_S),
					},
					ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
						BgColor:       viewMoreCtaContainerBgColor,
						CornerRadius:  viewMoreCtaContainerCornerRadius,
						LeftPadding:   viewMoreCtaContainerLeftPadding,
						RightPadding:  viewMoreCtaContainerRightPadding,
						TopPadding:    viewMoreCtaContainerTopPadding,
						BottomPadding: viewMoreCtaContainerBottomPadding,
					},
				},
			},
		},
	}, nil
}

func getUpiAppsInfoByPlatform(appPlatform commontypes.Platform, gconf *genconf.Config) ([]*transaction.IntentPaymentOption_UpiAppInfo, error) {
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		var upiAppsInfo []*transaction.IntentPaymentOption_UpiAppInfo
		for _, upiPackage := range gconf.PaymentOptionsConfig().IntentOptionsConfig().UpiAppsAndroidPackages() {
			upiAppsInfo = append(upiAppsInfo, &transaction.IntentPaymentOption_UpiAppInfo{
				AppDisplayData: &uiPb.IconTextComponent{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle(upiPackage.AppName, upiAppFontColor, commontypes.FontStyle_SUBTITLE_3),
					},
					LeftImgTxtPadding: upiAppLeftImgTextPadding,
					LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(upiPackage.AppLogo, upiAppLeftImgHeight, upiAppLeftImgWidth),
				},
				UpiApp: &transaction.IntentPaymentOption_UpiApp{
					PackageName: upiPackage.PackageId,
				},
			})
		}
		return upiAppsInfo, nil
	case commontypes.Platform_IOS:
		var upiAppsInfo []*transaction.IntentPaymentOption_UpiAppInfo
		for upiApp, upiAppDetails := range gconf.AddFundsV2Params().UpiAppUrnPrefixMap() {
			upiAppsInfo = append(upiAppsInfo, &transaction.IntentPaymentOption_UpiAppInfo{
				AppDisplayData: getAppDisplayData(upiAppDetails.DisplayName, upiAppDetails.ImageUrl),
				UpiApp: &transaction.IntentPaymentOption_UpiApp{
					UpiApp:          upiPb.UpiApp(upiPb.UpiApp_value[upiApp]),
					UpiAppUriPrefix: upiAppDetails.UrnPrefix,
				},
			})
		}
		return upiAppsInfo, nil
	}
	return nil, fmt.Errorf("unhandled platform: %s", appPlatform.String())
}

func getAppDisplayData(appName, appLogo string) *uiPb.IconTextComponent {
	return &uiPb.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(appName, upiAppFontColor, commontypes.FontStyle_SUBTITLE_3),
		},
		LeftImgTxtPadding: upiAppLeftImgTextPadding,
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(appLogo, upiAppLeftImgHeight, upiAppLeftImgWidth),
	}
}

func (i *IntentPaymentOptionProvider) IsPaymentOptionApplicable(req *IsPaymentOptionApplicableRequest) bool {
	if !lo.Contains(req.OptionsDisplay.SectionsToDisplay, i.GetPaymentOptionType()) {
		return false
	}
	switch req.UiEntryPoint {
	case timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_SECURED_CC:
		return false
	default:
		maxAmountAllowed := i.conf.PaymentOptionsConfig().TpapOptionsConfig().MaxAmountAllowed()
		paymentAmtFloat, _ := moneyPkg.ToDecimal(req.PaymentAmount.GetBeMoney()).Float64()
		if paymentAmtFloat > float64(maxAmountAllowed) {
			return false
		}
		return true
	}
}

var _ IPaymentOption = &IntentPaymentOptionProvider{}
