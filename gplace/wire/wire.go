//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package wire

import (
	vgGPlacePb "github.com/epifi/gamma/api/vendorgateway/gplace"
	gPlace "github.com/epifi/gamma/gplace"
	"github.com/epifi/gamma/gplace/config"
	gPlaceDao "github.com/epifi/gamma/gplace/dao"
	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"

	"github.com/google/wire"
)

func InitializeGPlaceService(conf *config.Config, dbPGDB cmdTypes.VendordataPGDB, vgGPlaceClient vgGPlacePb.GPlaceClient) *gPlace.GPlaceService {
	wire.Build(
		gPlace.NewGPlaceService,
		gPlaceDao.WireGPlaceDataDaoSet,
		cmdTypes.VendordataPGDBGormDBProvider,
	)
	return &gPlace.GPlaceService{}
}
