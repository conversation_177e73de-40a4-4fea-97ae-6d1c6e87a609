package model

import (
	"time"

	gormv2 "gorm.io/gorm"

	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/typesv2/account"
)

// AccountPI is relationship table between accounts <> PI
// It only captures relationship for internal(epiFi owned) PIs
type AccountPI struct {
	Id string `gorm:"primary_key"`

	// Reference to actor table
	ActorId string

	// Account ID for one of the accounts table eg. savings
	AccountId string

	// Type of the account eg. savings
	// TODO(kunal): We'll implement a config map between accounts type and table name on need basis
	AccountType accountsPb.Type

	// Account Product Offering associated with the AccountType
	Apo account.AccountProductOffering

	// Reference to PI table
	PiId string

	// Standard timestamp fields
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gormv2.DeletedAt
}
