---
# tasks file for prometheus upgrade

- name: Download and install binaries
  unarchive:
    src: "{{ prometheus_software_file_url }}" 
    dest: "{{ prometheus_extracted_file_location }}"
    owner: "{{ prometheus_user }}"
    group: "{{ prometheus_user }}"
    creates: "{{ prometheus_remote_extracted_directory }}"
    remote_src: true

- name: Ensure required directories are created
  file:
    name: "{{ item }}"
    state: directory
    recurse: true
    owner: "{{ prometheus_user }}"
    group: "{{ prometheus_user }}"
  loop: "{{ prometheus_configuration_directories }}"

- name: Stop Prometheus service
  become: true
  ansible.builtin.systemd:
    name: prometheus
    state: stopped

- name: Copy updated Prometheus systemd unit file
  template:
    src: prometheus.service.j2
    dest: "/etc/systemd/system/prometheus.service"
    owner: root
    group: root
    mode: 0644
  notify: update prometheus service
  no_log: true

# NOTE: For the following import, the monitoring repo
#       needs to be available locally at the location
#       provided by the monitoring_repo input variable
#
#       This task set is not optional for the upgrade 
#       operation, by design.
- name: Install Prometheus configuration and alert rules from monitoring repo
  import_tasks: configure.yml
  notify: reload prometheus
