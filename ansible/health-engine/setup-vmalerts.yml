---
- name: Create system group for vmalerts
  group:
    name: "vmalert"
    state: present
    system: true

- name: Create system user for vmalerts
  user:
    name: "vmalert"
    groups: "vmalert"
    append: true
    shell: /usr/sbin/nologin
    system: true
    createhome: false

- name: Prepare configuration dir
  ansible.builtin.file:
    state: directory
    path: "/etc/vmalert"
    mode: 0755
    owner: "vmalert"
    group: "vmalert"

- name: Install alert configs
  ansible.builtin.copy:
    src: "files/{{ target_env }}/rules/"
    dest: "/etc/vmalert"
    mode: 0751
    owner: "vmalert"
    group: "vmalert"
    validate: "/usr/local/bin/vmalert-prod -dryRun -rule %s"
    backup: no
  notify: restart vmalerts

- name: Install systemd service unit config file for vmalerts
  template:
    src: vmalerts.service.j2
    dest: /etc/systemd/system/vmalerts.service
    owner: root
    group: root
    mode: 0644
  notify:
    - restart vmalerts
  no_log: True
