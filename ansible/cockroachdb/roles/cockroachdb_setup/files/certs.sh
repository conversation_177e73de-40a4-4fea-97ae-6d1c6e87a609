#!/bin/bash

mkdir -p /var/lib/cockroach/private
chown cockroachdb:cockroachdb /var/lib/cockroach/private
env=$1

download_ca() {
    echo "downloading the cert/key authority pair from secretsmanager"
    aws secretsmanager get-secret-value --secret-id ${env}/cockroach/ca.key | jq -r '.SecretString | fromjson | ."ca.key"' | tr ' ' '\n' | sed -z 's/BEGIN\nRSA\nPRIVATE\nKEY/BEGIN RSA PRIVATE KEY/g' | sed -z 's/END\nRSA\nPRIVATE\nKEY/END RSA PRIVATE KEY/g' > /var/lib/cockroach/private/ca.key
    aws secretsmanager get-secret-value --secret-id ${env}/cockroach/ca.crt | jq -r '.SecretString' | tr ' ' '\n' | sed -z 's/BEGIN\nCERTIFICATE/BEGIN CERTIFICATE/g' | sed -z 's/END\nCERTIFICATE/END CERTIFICATE/g' > /var/lib/cockroach/certs/ca.crt
    chown cockroachdb:cockroachdb /var/lib/cockroach/certs/ca.crt
    chown cockroachdb:cockroachdb /var/lib/cockroach/private/ca.key
    chmod 700 /var/lib/cockroach/private/ca.key
    chmod 700 /var/lib/cockroach/certs/ca.crt
}

download_user_secrets() {
  echo "downloading the cert/key for user "root", pair from secretsmanager"
  aws secretsmanager get-secret-value --secret-id ${env}/cockroach/client.root | jq -r '.SecretString | fromjson | ."cert"' | base64 --decode > /var/lib/cockroach/certs/client.root.crt
  aws secretsmanager get-secret-value --secret-id ${env}/cockroach/client.root | jq -r '.SecretString | fromjson | ."key"' | base64 --decode > /var/lib/cockroach/certs/client.root.key
  chown cockroachdb:cockroachdb /var/lib/cockroach/certs/client.root.key
  chown cockroachdb:cockroachdb /var/lib/cockroach/certs/client.root.crt
  chmod 700 /var/lib/cockroach/certs/client.root.crt
  chmod 700 /var/lib/cockroach/certs/client.root.key
}

generate_node_key() {
  #. Creating the list of possible valid endpoints.
  echo "generating the node key"
  local="localhost 127.0.0.1"
  local_ip="$(hostname -i)"
  local_host="$(hostname)"

  # If the environment variable is not prod. Then query ${env}-cockroach-nlb else ${env}-cockroachdb
  nlb_hostname="$(aws elbv2 describe-load-balancers  --names ${env}-cockroachdb --query 'LoadBalancers[0].DNSName' --output text)"

  local_host_alter="$(hostname -f)"
  nlb_azs_ip="$(host ${nlb_hostname} | awk '{print $NF}')"
  #. Creating the actual cert/key pair for the instance.
  /usr/local/bin/cockroach cert create-node \
    $local \
    $local_ip \
    $local_host \
    $local_host_alter \
    $nlb_azs_ip \
    $nlb_hostname \
    cockroachdb.${env}.epifi.vpc \
    --certs-dir=/var/lib/cockroach/certs --ca-key=/var/lib/cockroach/private/ca.key

  chown cockroachdb:cockroachdb /var/lib/cockroach/certs/node.crt
  chown cockroachdb:cockroachdb /var/lib/cockroach/certs/node.key

  rm -rf /var/lib/cockroach/private
}

secrets_list="$(aws secretsmanager list-secrets | jq -r ".SecretList[].Name")"
if [[ "${secrets_list[@]}" =~ "${env}/cockroach/ca.key" ]] && [[ "${secrets_list[@]}" =~ "${env}/cockroach/ca.crt" ]]; then
  echo "the secrets already exits...creating from Terraform variable"
  download_ca
  download_user_secrets
  generate_node_key
else
  echo "the secret doesn't already exists...exiting"
  echo "cockroachdb cluster creation has been failed"
  exit 1
fi
