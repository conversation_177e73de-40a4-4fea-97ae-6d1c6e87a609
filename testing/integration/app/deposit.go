// nolint:funlen,gocritic
package app

import (
	"context"
	"fmt"
	"time"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	types "github.com/epifi/gamma/api/typesv2"

	feDepositPb "github.com/epifi/gamma/api/frontend/deposit"

	"github.com/stretchr/testify/require"

	"github.com/epifi/gamma/api/frontend/header"
)

type DepositParams struct {
	Name           string
	Amount         *types.Money
	SkipAddFunds   bool
	Term           *types.DepositTerm
	Type           accounts.Type
	InterestPayout feDepositPb.InterestPayout
}

func CreateDeposit(
	ctx context.Context,
	a *require.Assertions,
	dc feDepositPb.DepositClient,
	ah *header.RequestHeader,
	dps ...DepositParams,
) {
	//create all deposits
	for i := 0; i < len(dps); i++ {
		go createDeposit(ctx, a, dc, ah, dps[i])
	}
}

func VerifyDeposits(
	ctx context.Context,
	a *require.Assertions,
	dc feDepositPb.DepositClient,
	ah *header.RequestHeader,
	depositType accounts.Type,
	depositParams ...DepositParams,
) {
	var depositTypeString = depositType.String()

	// wait for all deposits to be created
	listResp := &feDepositPb.ListDepositAccountsResponse{
		Status: nil,
		CategorisedDeposits: map[string]*feDepositPb.ListDepositAccountsResponse_CategorisedDepositAccount{
			depositTypeString: {
				TotalPrincipleAmount: nil,
				DepositAccounts:      []*feDepositPb.DepositAccount{},
			}},
	}

	var i, maxTries = 0, 10
	var err error
	for ; i < maxTries; i++ {
		time.Sleep(time.Second * 3)
		listResp, err = dc.ListDepositAccounts(ctx, &feDepositPb.ListDepositAccountsRequest{
			Req:   ah,
			Type:  depositType,
			State: feDepositPb.DepositState_CREATED,
		})
		a.NoError(err)
		a.Equal(rpc.StatusOk().Code, listResp.Status.Code)

		if areAccountsPresent(listResp.CategorisedDeposits[depositTypeString].DepositAccounts, depositParams) {
			break
		}
	}

	a.Less(i, maxTries, "Deposit account not created even after checking status %v times", maxTries)

	// Check all deposits were created with the expected values
	depositAccounts := listResp.CategorisedDeposits[depositTypeString].DepositAccounts
	nameToAcc := make(map[string]*feDepositPb.DepositAccount, len(depositAccounts))
	for _, acc := range depositAccounts {
		nameToAcc[acc.Name] = acc
	}
	for i := 0; i < len(depositParams); i++ {
		depositParam := &depositParams[i]
		depositAccount := nameToAcc[depositParam.Name]
		a.NotNil(depositAccount)
		a.Equal(depositType, depositAccount.Type)
		a.Equal(depositParam.Name, depositAccount.Name)
		a.NotEmpty(depositAccount.AccountNumber)
		a.NotEmpty(depositAccount.AccountId)
		a.True(cmp.Equal(depositParam.Amount, depositAccount.GetPrincipleAmount(), protocmp.Transform()))
	}
}

// areAccountsPresent checks if depositAccounts with name in depositParams are present
func areAccountsPresent(depositAccounts []*feDepositPb.DepositAccount, depositParams []DepositParams) bool {
	// map from name to account
	nameToAcc := make(map[string]*feDepositPb.DepositAccount, len(depositAccounts))
	for _, acc := range depositAccounts {
		nameToAcc[acc.Name] = acc
	}
	for i := range depositParams {
		if nameToAcc[depositParams[i].Name] == nil {
			return false
		}
	}

	return true
}

func VerifyAddFundsToSD(
	ctx context.Context,
	a *require.Assertions,
	feDepositClient feDepositPb.DepositClient,
	requestHeader *header.RequestHeader,
	depositAccountId string,
	expectedPrincipleAmount *types.Money,
) {
	maxTries := 10
	for i := 0; i < maxTries; i++ {
		time.Sleep(time.Second * 3)
		getAccountDetailsResponse, err := feDepositClient.GetAccountDetails(ctx, &feDepositPb.GetAccountDetailsRequest{
			Req:       requestHeader,
			AccountId: depositAccountId,
		})
		a.NoError(err)
		a.Equal(rpc.StatusOk().Code, getAccountDetailsResponse.Status.Code)

		if cmp.Equal(expectedPrincipleAmount, getAccountDetailsResponse.GetDepositAccount().GetPrincipleAmount(), protocmp.Transform()) {
			return
		}
	}

	a.Error(fmt.Errorf("failed to verify add funds to SD even after %d attempts", maxTries))
}

func createDeposit(
	ctx context.Context,
	a *require.Assertions,
	dc feDepositPb.DepositClient,
	ah *header.RequestHeader,
	dp DepositParams,
) {
	createResp, err := dc.CreateDeposit(ctx, &feDepositPb.CreateDepositRequest{
		Req:          ah,
		SkipAddFunds: dp.SkipAddFunds,
		DepositInfo: &feDepositPb.DepositInfo{
			Name:           dp.Name,
			DepositType:    dp.Type,
			Amount:         dp.Amount,
			Duration:       &feDepositPb.DepositInfo_Term{Term: dp.Term},
			InterestPayout: dp.InterestPayout,
		},
	})
	a.NoError(err, "Deposit creation failed")
	a.Equal(rpc.StatusOk().Code, createResp.Status.Code)
}

func PreCloseSmartDeposit(ctx context.Context,
	a *require.Assertions,
	dc feDepositPb.DepositClient,
	ah *header.RequestHeader,
	depositType accounts.Type) {

	var depositTypeString = depositType.String()
	var err error

	listResp, err := dc.ListDepositAccounts(ctx, &feDepositPb.ListDepositAccountsRequest{
		Req:  ah,
		Type: depositType,
	})
	a.NoError(err)
	a.True(listResp.GetStatus().IsSuccess())

	// take first deposit account in created state
	accounts := listResp.CategorisedDeposits[depositTypeString].DepositAccounts
	a.True(len(accounts) > 0)
	var account *feDepositPb.DepositAccount
	for idx := range accounts {
		if accounts[idx].State == feDepositPb.DepositState_CREATED {
			account = accounts[idx]
			break
		}
	}
	// account should be not nil
	a.NotNil(account, fmt.Sprintf("no account found to close of type %s", depositType.String()))

	// make pre-close deposit api call
	preCloseRes, err := dc.PreCloseAccount(ctx, &feDepositPb.PreCloseAccountRequest{
		Req:       ah,
		AccountId: account.GetAccountId(),
	})
	a.NoError(err)
	a.True(preCloseRes.GetStatus().IsSuccess())

	var preClosedAccount *feDepositPb.DepositAccount

	// Since it takes time to pre-close a deposit account as it depends on receiving an inbound txn, the ListDepositAccounts
	// API might take some tries to fetch the pre-closed account.
	// Max tries have been set to 10
	var i, maxTries = 0, 10
	for ; i < maxTries; i++ {
		time.Sleep(time.Second * 3)
		listResp, err = dc.ListDepositAccounts(ctx, &feDepositPb.ListDepositAccountsRequest{
			Req:   ah,
			State: feDepositPb.DepositState_PRECLOSED,
			Type:  depositType,
		})

		a.NoError(err)
		a.True(listResp.GetStatus().IsSuccess())

		accounts = listResp.CategorisedDeposits[depositTypeString].DepositAccounts
		for idx := range accounts {
			if accounts[idx].AccountId == account.AccountId &&
				accounts[idx].State == feDepositPb.DepositState_PRECLOSED {
				preClosedAccount = accounts[idx]
				break
			}
		}
		if preClosedAccount != nil {
			break
		}
	}

	a.True(preClosedAccount != nil && preClosedAccount.State == feDepositPb.DepositState_PRECLOSED)
}
