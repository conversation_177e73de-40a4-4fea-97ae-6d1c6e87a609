package integration

import (
	"context"
	"testing"

	"github.com/epifi/gamma/testing/integration/app"

	"github.com/golang/protobuf/proto"    // nolint:depguard
	"github.com/stretchr/testify/require" // nolint:depguard
	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/owners"
	"github.com/epifi/gamma/scripts/crud/userdata"
)

const (
	tieringPreOnboardedUsers = 2
	MinKycUserPhNumber       = 5000000001
	FullKycUserPhNumber      = 5000000002
	// SalariedUserPhNumber     = 5000000003
)

func TieringFlows(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()
	tts := app.NewTieringTestSuite(feTieringClient, beTieringClient, epifiDbV2)

	// get user from pool
	pooledUser, release := GetPooledUser(ctx, a)
	reqH := proto.Clone(pooledUser.RequestHeader).(*header.RequestHeader)
	defer release()
	actorId, err := userdata.GetActorIdForUser(epifiDbV2, actorPgdb, pooledUser.Data.Phone)
	if err != nil {
		logger.ErrorNoCtx("error getting actor id for user", zap.Any("phoneNumber", pooledUser.Data.Phone.GetNationalNumber()), zap.Error(err))
		return
	}
	reqH.Auth.ActorId = actorId
	logger.InfoNoCtx("reqH", zap.Any(logger.REQUEST, reqH))
	logger.InfoNoCtx("actor id and ph number of user",
		zap.Any("phoneNumber", pooledUser.Data.Phone.GetNationalNumber()), zap.Any(logger.ACTOR_ID_V2, actorId))

	runTestsForUser(t, tts, reqH)
}

func runTestsForUser(t *testing.T, tts *app.TieringTestSuite, reqH *header.RequestHeader) {
	// By dividing into subtests, each test is run in parallel
	// Ref - https://go.dev/blog/subtests : Table-driven tests using subtests
	t.Run("tiering test", func(t *testing.T) {
		t.Run("tiering launch info test", func(t *testing.T) {
			defer Recover(t)
			logger.InfoNoCtx("running get tiering launch info test")
			tts.TestGetTieringLaunchInfo(t, reqH)
		})
		t.Run("get deeplink test", func(t *testing.T) {
			defer Recover(t)
			logger.InfoNoCtx("running get deeplink test")
			tts.TestGetDeeplink(t, reqH)
		})
		t.Run("upgrade test", func(t *testing.T) {
			defer Recover(t)
			logger.InfoNoCtx("running upgrade test")
			tts.TestUpgrade(t, reqH)
		})
		t.Run("get tier all plans test", func(t *testing.T) {
			defer Recover(t)
			logger.InfoNoCtx("running get tier all plans test")
			tts.TestGetTierAllPlans(t, reqH)
		})
		t.Run("record component shown to actor test", func(t *testing.T) {
			defer Recover(t)
			logger.InfoNoCtx("running record component shown to actor test")
			tts.TestRecordComponentShownToActor(t, reqH)
		})
	})
}

// GetAllPreloadedPooledUsers returns all the prelaoded onboarded users
func GetAllPreloadedPooledUsers(a *require.Assertions, db *gormv2.DB) []*app.PooledUserWithRelease {
	var res []*app.PooledUserWithRelease
	for i := 0; i < tieringPreOnboardedUsers; i++ {
		pu, rl := app.GetPreLoadedPooledUser(a, db)
		res = append(res, &app.PooledUserWithRelease{User: pu, ReleaseUserFn: rl})
	}
	return res

}

func TieringTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is the test for tiering flows",
				BU:          owners.BUSINESS_UNIT_PAY,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: TieringFlows,
		},
	}
}
