package mcpserver

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"runtime/debug"
	"sync"
	"syscall"

	"github.com/epifi/be-common/mcp/pkg/logger"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
)

var (
	shutdownChan = make(chan struct{})
	wg           sync.WaitGroup
)

func HandlePanic(safeLogger *logger.SafeLogger) {
	if r := recover(); r != nil {
		stackTrace := debug.Stack()
		safeLogger.Printf("PANIC RECOVERED: %v\nStack Trace:\n%s", r, stackTrace)
		// Ensure logs are flushed before exiting
		if err := safeLogger.Flush(); err != nil {
			fmt.Printf("Error flushing logs: %v\n", err)
		}
		os.Exit(1)
	}
}

func SetupSignalHandler(safeLogger *logger.SafeLogger) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sig<PERSON>han, syscall.SIGINT, syscall.SIGTERM)
	ctx := context.Background()
	goroutine.RunWithCtx(ctx, func(gCtx context.Context) {
		sig := <-sigChan
		safeLogger.Printf("Received signal: %v", sig)
		close(shutdownChan)

		// Wait for all goroutines to finish
		waitgroup.SafeWaitCtx(gCtx, &wg)

		safeLogger.Println("Gracefully shutting down...")
		if err := safeLogger.Flush(); err != nil {
			log.Printf("Error flushing logs: %v\n", err)
		}
		if err := safeLogger.Close(); err != nil {
			log.Printf("Error closing log file: %v\n", err)
		}
		os.Exit(0)
	})
}
