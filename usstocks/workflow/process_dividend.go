package workflow

import (
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"google.golang.org/protobuf/types/known/emptypb"

	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	usStocksWfPb "github.com/epifi/gamma/api/usstocks/workflow"
)

// ProcessDividend is workflow code that contains all the business logic to handle tasks that need to be
// performed after the dividend is received by the actor
// Following needs to be done:
// 1. Send notification to user that dividend is received
// 2. Send refund to user for the GST amount debited
// 3. Send notification to user that GST amount debited is now refunded
func ProcessDividend(ctx workflow.Context, _ *emptypb.Empty) (*emptypb.Empty, error) {
	wfReq := &usStocksWfPb.ProcessDividendRequest{}
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, wfReq)
	if err != nil {
		return nil, errors.Wrap(err, "error getting wf req params")
	}

	_ = workflow.GetVersion(ctx, "process-aggregated-dividends", workflow.DefaultVersion, 1)
	switch wfReq.GetRequestIdentifier().(type) {
	case *usStocksWfPb.ProcessDividendRequest_StockSymbol:
		return processStockDividend(ctx, wfProcessingParams.GetClientReqId(), wfReq)
	case *usStocksWfPb.ProcessDividendRequest_AggregatedRemittanceTxnId:
		_, processErr := refundGstForAggregatedDividends(ctx, wfProcessingParams.GetClientReqId(), wfReq)
		if processErr != nil {
			return nil, fmt.Errorf("failed to execute stage: %w", processErr)
		}
		return &emptypb.Empty{}, nil
	default:
		return nil, fmt.Errorf("unhdanled req: %v", wfReq.GetRequestIdentifier())
	}
}
