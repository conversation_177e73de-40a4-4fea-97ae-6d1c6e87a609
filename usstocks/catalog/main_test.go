package catalog

import (
	"flag"
	"os"
	"testing"

	gormv2 "gorm.io/gorm"

	genConf "github.com/epifi/gamma/usstocks/config/genconf"
	"github.com/epifi/gamma/usstocks/test"
)

// service test suite
type svcTestSuite struct {
	conf *genConf.Config
}

func newSvcTestSuite(conf *genConf.Config) *svcTestSuite {
	return &svcTestSuite{
		conf: conf,
	}
}

var (
	db    *gormv2.DB
	conf  *genConf.Config
	svcTS *svcTestSuite
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

// nolint
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	_, conf, db, teardown = test.InitTestServer()

	svcTS = newSvcTestSuite(conf)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
