package utils

import (
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
)

// GetStockIdsFromCollectionStockMappings returns a list of stock ids from a list of collection stock mappings
func GetStockIdsFromCollectionStockMappings(
	collectionStockMappings []*usstocksCatalogPb.CollectionStockMapping) []string {
	var stockIds []string
	for _, collectionStockMapping := range collectionStockMappings {
		stockIds = append(stockIds, collectionStockMapping.GetStockId())
	}
	return stockIds
}

// GetStocksFromStockMap returns a list of stocks from a map of stock ids to stocks
func GetStocksFromStockMap(stockMap map[string]*usstocksCatalogPb.Stock) []*usstocksCatalogPb.Stock {
	var stocks []*usstocksCatalogPb.Stock
	for _, stock := range stockMap {
		stocks = append(stocks, stock)
	}
	return stocks
}

// GetCollectionStocksFromStocks returns a list of collection stocks from a list of stocks
func GetCollectionStocksFromStocks(stocks []*usstocksCatalogPb.Stock) []*usstocksCatalogPb.CollectionStock {
	var collectionStocks []*usstocksCatalogPb.CollectionStock
	for _, stock := range stocks {
		collectionStocks = append(collectionStocks, &usstocksCatalogPb.CollectionStock{
			Stock: stock,
		})
	}
	return collectionStocks
}
