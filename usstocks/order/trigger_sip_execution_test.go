package order_test

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks"
	ussAccountPb "github.com/epifi/gamma/api/usstocks/account"
	usstocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/usstocks/catalog/stockprocessor"
	walletOrderProcessing "github.com/epifi/gamma/usstocks/order/walletorderprocessor"
)

func TestService_TriggerSIPExecution(t *testing.T) {
	s, md := getServiceWithMocks(t)
	type args struct {
		ctx context.Context
		req *usstocksOrderPb.TriggerSIPExecutionRequest
	}
	actorId := "test-actor-id"
	vendorAccountId := "test-vendor-account-id"
	stockId1 := "test-stock-id"
	stockId2 := "test-stock-id-2"
	workflowId := "test-workflow-id"
	clientRequestId := "test-client-request-id"
	fitttSubscriptionId1 := "test-subscription-1"
	fitttSubscriptionId2 := "test-subscription-2"
	sampleWalletOrder := &usstocksOrderPb.WalletOrder{
		ActorId:       actorId,
		ClientOrderId: clientRequestId,
		OrderType:     usstocks.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS,
		OrderSubType:  usstocks.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP,
		InvoiceDetails: &usstocksOrderPb.WalletOrderInvoiceDetails{
			AmountInInr: money.ParseInt(18000, money.RupeeCurrencyCode),
		},
		VendorAccountId: vendorAccountId,
	}
	sampleWalletOrder2 := &usstocksOrderPb.WalletOrder{
		ActorId:       actorId,
		ClientOrderId: clientRequestId,
		OrderType:     usstocks.WalletOrderType_WALLET_ORDER_TYPE_ADD_FUNDS,
		OrderSubType:  usstocks.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP,
		InvoiceDetails: &usstocksOrderPb.WalletOrderInvoiceDetails{
			AmountInInr: money.ParseInt(10000, money.RupeeCurrencyCode),
		},
		VendorAccountId: vendorAccountId,
	}
	sampleReq := &usstocksOrderPb.TriggerSIPExecutionRequest{
		ActorId: actorId,
		SipExecutionInfo: []*usstocksOrderPb.SIPExecutionInfo{
			{
				SubscriptionId: fitttSubscriptionId1,
				Id: &usstocksOrderPb.SIPExecutionInfo_StockId{
					StockId: stockId2,
				},
				SipAmountInr:     money.ParseInt(8000, money.RupeeCurrencyCode),
				SipExecutionDate: dateTimePkg.TimeToDateInLoc(time.Now(), dateTimePkg.IST),
			},
			{
				SubscriptionId: fitttSubscriptionId2,
				Id: &usstocksOrderPb.SIPExecutionInfo_StockId{
					StockId: stockId1,
				},
				SipAmountInr:     money.ParseInt(10000, money.RupeeCurrencyCode),
				SipExecutionDate: dateTimePkg.TimeToDateInLoc(time.Now(), dateTimePkg.IST),
			},
		},
		ClientRequestId: clientRequestId,
	}

	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *usstocksOrderPb.TriggerSIPExecutionResponse
		wantErr        bool
	}{
		{
			name: "Initiation successful for sip execution",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				md.walletOrderProcessorFactory.EXPECT().GetOrderProcessor(sampleWalletOrder).Return(md.walletOrderProcessor, nil)
				md.walletOrderProcessor.EXPECT().ValidateOrder(gomock.Any(), sampleWalletOrder).Return(nil)
				md.accountDao.EXPECT().GetByActorAndVendor(gomock.Any(), actorId, commonvgpb.Vendor_ALPACA, []ussAccountPb.AccountFieldMask{
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_VENDOR_ACCOUNT_ID,
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS,
				}).Return(&ussAccountPb.Account{VendorAccountId: vendorAccountId, AccountStatus: ussAccountPb.AccountStatus_ACTIVE}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId1).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId2).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.walletOrderProcessor.EXPECT().InitiateOrderProcessing(gomock.Any(), &walletOrderProcessing.InitiateOrderProcessingRequest{
					Order:            sampleWalletOrder,
					SipExecutionInfo: sampleReq.GetSipExecutionInfo(),
				}).Return(&walletOrderProcessing.InitiateOrderProcessingResponse{
					WfReqId: workflowId,
				}, nil)
			},
			want: &usstocksOrderPb.TriggerSIPExecutionResponse{
				Status:     rpc.StatusOk(),
				WorkflowId: workflowId,
			},
			wantErr: false,
		},
		{
			name: "if error while getting wallet order processor",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId1).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId2).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.walletOrderProcessorFactory.EXPECT().GetOrderProcessor(sampleWalletOrder).Return(md.walletOrderProcessor, fmt.Errorf("some error"))

				md.accountDao.EXPECT().GetByActorAndVendor(gomock.Any(), actorId, commonvgpb.Vendor_ALPACA, []ussAccountPb.AccountFieldMask{
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_VENDOR_ACCOUNT_ID,
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS,
				}).Return(&ussAccountPb.Account{VendorAccountId: vendorAccountId, AccountStatus: ussAccountPb.AccountStatus_ACTIVE}, nil)
			},
			want: &usstocksOrderPb.TriggerSIPExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "if error while validating wallet order",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId1).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId2).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.walletOrderProcessorFactory.EXPECT().GetOrderProcessor(sampleWalletOrder).Return(md.walletOrderProcessor, nil)
				md.walletOrderProcessor.EXPECT().ValidateOrder(gomock.Any(), sampleWalletOrder).Return(fmt.Errorf("some error"))
				md.accountDao.EXPECT().GetByActorAndVendor(gomock.Any(), actorId, commonvgpb.Vendor_ALPACA, []ussAccountPb.AccountFieldMask{
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_VENDOR_ACCOUNT_ID,
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS,
				}).Return(&ussAccountPb.Account{VendorAccountId: vendorAccountId, AccountStatus: ussAccountPb.AccountStatus_ACTIVE}, nil)
			},
			want: &usstocksOrderPb.TriggerSIPExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error in getting stock status for stock 1",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId1).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, fmt.Errorf("some error"))
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId2).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
			},
			want: &usstocksOrderPb.TriggerSIPExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "if stock 2 is not fractionable",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				md.walletOrderProcessorFactory.EXPECT().GetOrderProcessor(sampleWalletOrder2).Return(md.walletOrderProcessor, nil)
				md.walletOrderProcessor.EXPECT().ValidateOrder(gomock.Any(), sampleWalletOrder2).Return(nil)
				md.accountDao.EXPECT().GetByActorAndVendor(gomock.Any(), actorId, commonvgpb.Vendor_ALPACA, []ussAccountPb.AccountFieldMask{
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_VENDOR_ACCOUNT_ID,
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS,
				}).Return(&ussAccountPb.Account{VendorAccountId: vendorAccountId, AccountStatus: ussAccountPb.AccountStatus_ACTIVE}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId1).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId2).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: false,
					IsTradable:     true,
				}, nil)
				md.walletOrderProcessor.EXPECT().InitiateOrderProcessing(gomock.Any(), &walletOrderProcessing.InitiateOrderProcessingRequest{
					Order:            sampleWalletOrder2,
					SipExecutionInfo: []*usstocksOrderPb.SIPExecutionInfo{sampleReq.GetSipExecutionInfo()[1]},
				}).Return(&walletOrderProcessing.InitiateOrderProcessingResponse{
					WfReqId: workflowId,
				}, nil)
			},
			want: &usstocksOrderPb.TriggerSIPExecutionResponse{
				Status:     rpc.StatusOk(),
				WorkflowId: workflowId,
			},
			wantErr: false,
		},
		{
			name: "error initiating order processing",
			args: args{
				ctx: context.Background(),
				req: sampleReq,
			},
			setupMockCalls: func() {
				md.walletOrderProcessorFactory.EXPECT().GetOrderProcessor(sampleWalletOrder).Return(md.walletOrderProcessor, nil)
				md.walletOrderProcessor.EXPECT().ValidateOrder(gomock.Any(), sampleWalletOrder).Return(nil)
				md.accountDao.EXPECT().GetByActorAndVendor(gomock.Any(), actorId, commonvgpb.Vendor_ALPACA, []ussAccountPb.AccountFieldMask{
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_VENDOR_ACCOUNT_ID,
					ussAccountPb.AccountFieldMask_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS,
				}).Return(&ussAccountPb.Account{VendorAccountId: vendorAccountId, AccountStatus: ussAccountPb.AccountStatus_ACTIVE}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId1).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.stockProcessor.EXPECT().GetStockStatus(gomock.Any(), stockId2).Return(&stockprocessor.GetStockStatusResponse{
					IsActive:       true,
					IsFractionable: true,
					IsTradable:     true,
				}, nil)
				md.walletOrderProcessor.EXPECT().InitiateOrderProcessing(gomock.Any(), &walletOrderProcessing.InitiateOrderProcessingRequest{
					Order:            sampleWalletOrder,
					SipExecutionInfo: sampleReq.GetSipExecutionInfo(),
				}).Return(&walletOrderProcessing.InitiateOrderProcessingResponse{
					WfReqId: workflowId,
				}, fmt.Errorf("some error"))
			},
			want: &usstocksOrderPb.TriggerSIPExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Request Validation failed for trigger sip execution",
			args: args{
				ctx: context.Background(),
				req: &usstocksOrderPb.TriggerSIPExecutionRequest{
					ActorId:         actorId,
					ClientRequestId: clientRequestId,
				},
			},
			setupMockCalls: func() {
			},
			want: &usstocksOrderPb.TriggerSIPExecutionResponse{
				Status: rpc.StatusInternalWithDebugMsg("sip execution info is empty"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			got, err := s.TriggerSIPExecution(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("TriggerSIPExecution() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			tt.want.Status.DebugMessage = got.GetStatus().GetDebugMessage()
			tt.want.Status.ShortMessage = got.GetStatus().GetShortMessage()
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("TriggerSIPExecution() got = %v, want %v", got, tt.want)
			}
		})
	}
}
