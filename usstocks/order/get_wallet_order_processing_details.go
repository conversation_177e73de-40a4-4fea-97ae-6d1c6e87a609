package order

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/usstocks"

	"context"
	"errors"
	"sort"
	"time"

	pkgErrors "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	usstocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	"github.com/epifi/be-common/pkg/logger"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	iftPkg "github.com/epifi/gamma/pkg/internationalfundtransfer"
	marketCalendar "github.com/epifi/gamma/usstocks/utils/market_calendar"
)

// GetWalletOrderProcessingDetails expects order_id and returns
// 1. Corresponding order
// 2. List of stages with their corresponding current status
// RPC uses workflow history to get list of stages and their corresponding status
func (s *Service) GetWalletOrderProcessingDetails(ctx context.Context, req *orderPb.GetWalletOrderProcessingDetailsRequest) (*orderPb.GetWalletOrderProcessingDetailsResponse, error) {
	order, err := s.getWalletOrderWithActorValidation(ctx, req.GetOrderId(), req.GetClientOrderId(), req.GetVendorOrderId(),
		req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting wallet order processing details", zap.Error(err),
			zap.String(logger.ORDER_ID, req.GetOrderId()), zap.String(logger.CLIENT_ORDER_ID, req.GetClientOrderId()),
			zap.String(logger.VENDOR_ORDER_ID, req.GetVendorOrderId()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &orderPb.GetWalletOrderProcessingDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
				Order:  nil,
			}, nil
		}

		if errors.Is(err, invalidActorAssociationError) {
			return &orderPb.GetWalletOrderProcessingDetailsResponse{
				Status: rpc.NewStatusWithoutDebug(
					uint32(orderPb.GetWalletOrderProcessingDetailsResponse_INVALID_ACTOR_ASSOCIATION),
					"order not associated with actor"),
			}, nil
		}
		return &orderPb.GetWalletOrderProcessingDetailsResponse{
			Status: rpc.StatusInternal(),
			Order:  nil,
		}, nil
	}

	orderProcessingStages, err := s.getWalletOrderProcessingStages(ctx, order)
	if err != nil {
		logger.Error(ctx, "error in getting wallet order processing stages", zap.Error(err),
			zap.String(logger.ORDER_ID, order.GetId()))
		return &orderPb.GetWalletOrderProcessingDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &orderPb.GetWalletOrderProcessingDetailsResponse{
		Status:       rpc.StatusOk(),
		Order:        order,
		StageDetails: orderProcessingStages,
	}, nil
}

func (s *Service) getWalletOrderProcessingStages(ctx context.Context, order *orderPb.WalletOrder) ([]*orderPb.WalletOrderProcessingStageDetails, error) {
	clientRequestId := order.GetId()
	if order.GetOrderSubType() == usstocks.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP {
		clientRequestId = order.GetClientOrderId()
	}
	workflowStages, err := s.celestialClient.GetWorkflowHistory(ctx, &celestialPb.GetWorkflowHistoryRequest{
		Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     clientRequestId,
				Client: workflow.Client_US_STOCKS,
			},
		},
		Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
	})
	if err2 := epifigrpc.RPCError(workflowStages, err); err2 != nil {
		logger.Error(ctx, "error in getting workflow history for order", zap.Error(err2), zap.String(logger.CLIENT_REQUEST_ID, order.GetId()))
		return nil, err2
	}

	var orderProcessingStages []*orderPb.WalletOrderProcessingStageDetails
	for _, stage := range workflowStages.GetWorkflowHistoryList() {
		eta, err := GetWalletStageCompletionETA(epifitemporal.Stage(stage.GetStageEnum().GetStageValue()), stage.GetStatus(), stage.GetCreatedAt())
		if err != nil {
			return nil, err
		}

		// not assigning ETA, if ETA time is in past
		// this decision is taken to not show ETA at all instead of showing incorrect ETA to user
		if eta.AsTime().Before(time.Now()) {
			eta = nil
		}

		processingStage := &orderPb.WalletOrderProcessingStageDetails{
			Stage:                stage.GetStageEnum().GetStageValue(),
			Status:               stage.GetStatus(),
			LastUpdatedTimestamp: stage.GetUpdatedAt(),
			Eta:                  eta,
		}

		orderProcessingStages = append(orderProcessingStages, processingStage)
	}
	return discardDuplicateWalletOrderProcessingStages(orderProcessingStages), nil
}

func GetWalletStageCompletionETA(stage epifitemporal.Stage, status stagePb.Status, createdAt *timestamppb.Timestamp) (*timestamppb.Timestamp, error) {
	// not sending ETA for terminal state orders
	if celestial.IsTerminalStatus(status) {
		return nil, nil
	}

	switch stage {
	case usstocksNs.ForeignFundTransferStage:
		// eta = T + next bank working day + next market day
		nextBankWorkingDay, err := iftPkg.GetNextBankWorkingDay(createdAt.AsTime())
		if err != nil {
			return nil, pkgErrors.Wrap(err, "error in getting eta for foreign fund transfer stage")
		}

		_, _, eta, err := marketCalendar.GetNextRegularTradingOpenCloseTime(nextBankWorkingDay.EndTime)
		if err != nil {
			return nil, pkgErrors.Wrap(err, "error getting next regular trading open close time")
		}
		return eta, nil
	case usstocksNs.TrackWalletFundTransferStage:
		_, _, nextMarketCloseTime, err := marketCalendar.GetNextRegularTradingOpenCloseTime(createdAt.AsTime())
		if err != nil {
			return nil, pkgErrors.Wrap(err, "error getting next regular trading open close time")
		}
		return nextMarketCloseTime, nil
	case usstocksNs.TrackInwardsRemittanceStatusStage:
		// T + 1 next market day + 1 bank working day
		_, _, nextMarketCloseTime, err := marketCalendar.GetNextRegularTradingOpenCloseTime(createdAt.AsTime())
		if err != nil {
			return nil, pkgErrors.Wrap(err, "error getting next regular trading open close time")
		}

		eta, err := iftPkg.GetNextBankWorkingDay(nextMarketCloseTime.AsTime())
		if err != nil {
			return nil, pkgErrors.Wrap(err, "error in getting eta for foreign fund transfer stage")
		}
		return timestamppb.New(eta.EndTime), nil
	default:
		return nil, nil
	}
}

func discardDuplicateWalletOrderProcessingStages(stages []*orderPb.WalletOrderProcessingStageDetails) []*orderPb.WalletOrderProcessingStageDetails {
	// sorting orders in ascending order of their updated at time
	// sorting before putting the values to map is required as this will ensure that the values present in map are latest values
	sort.Sort(walletOrderProcessingStages(stages))

	// using map to discard duplicate stage.
	// duplicate stages can be present when workflow is reset
	orderProcessingStagesMap := make(map[string]*orderPb.WalletOrderProcessingStageDetails)
	for _, s := range stages {
		orderProcessingStagesMap[s.GetStage()] = s
	}

	var orderProcessingStages []*orderPb.WalletOrderProcessingStageDetails
	for _, s := range orderProcessingStagesMap {
		orderProcessingStages = append(orderProcessingStages, s)
	}

	// sorting orders in ascending order of their updated at time
	sort.Sort(walletOrderProcessingStages(orderProcessingStages))
	return orderProcessingStages
}

type walletOrderProcessingStages []*orderPb.WalletOrderProcessingStageDetails

func (p walletOrderProcessingStages) Len() int      { return len(p) }
func (p walletOrderProcessingStages) Swap(i, j int) { p[i], p[j] = p[j], p[i] }
func (p walletOrderProcessingStages) Less(i, j int) bool {
	return p[i].GetLastUpdatedTimestamp().AsTime().Before(p[j].GetLastUpdatedTimestamp().AsTime())
}
