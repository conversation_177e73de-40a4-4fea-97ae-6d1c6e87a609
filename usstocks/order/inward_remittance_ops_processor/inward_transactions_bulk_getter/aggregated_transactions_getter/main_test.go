package aggregated_transactions_getter

import (
	"flag"
	"os"
	"testing"

	"github.com/epifi/gamma/usstocks/test"

	genConf "github.com/epifi/gamma/usstocks/config/genconf"
)

var (
	conf *genConf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

// nolint
func TestMain(m *testing.M) {
	flag.Parse()

	conf, _ = test.InitTestServerWithoutDBConn()
	exitCode := m.Run()
	os.Exit(exitCode)
}
