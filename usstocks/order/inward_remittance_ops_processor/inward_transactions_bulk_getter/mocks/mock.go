// Code generated by MockGen. DO NOT EDIT.
// Source: inward_transactions_bulk_getter.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	inward_transaction_utils "github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_utils"
	inward_transactions_bulk_getter "github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transactions_bulk_getter"
	gomock "github.com/golang/mock/gomock"
)

// MockInwardTransactionsBulkGetter is a mock of InwardTransactionsBulkGetter interface.
type MockInwardTransactionsBulkGetter struct {
	ctrl     *gomock.Controller
	recorder *MockInwardTransactionsBulkGetterMockRecorder
}

// MockInwardTransactionsBulkGetterMockRecorder is the mock recorder for MockInwardTransactionsBulkGetter.
type MockInwardTransactionsBulkGetterMockRecorder struct {
	mock *MockInwardTransactionsBulkGetter
}

// NewMockInwardTransactionsBulkGetter creates a new mock instance.
func NewMockInwardTransactionsBulkGetter(ctrl *gomock.Controller) *MockInwardTransactionsBulkGetter {
	mock := &MockInwardTransactionsBulkGetter{ctrl: ctrl}
	mock.recorder = &MockInwardTransactionsBulkGetterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockInwardTransactionsBulkGetter) EXPECT() *MockInwardTransactionsBulkGetterMockRecorder {
	return m.recorder
}

// BulkGetTransactions mocks base method.
func (m *MockInwardTransactionsBulkGetter) BulkGetTransactions(arg0 context.Context, arg1 *inward_transactions_bulk_getter.InwardTransactionBulkGetterRequest) ([]*inward_transaction_utils.InwardTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkGetTransactions", arg0, arg1)
	ret0, _ := ret[0].([]*inward_transaction_utils.InwardTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkGetTransactions indicates an expected call of BulkGetTransactions.
func (mr *MockInwardTransactionsBulkGetterMockRecorder) BulkGetTransactions(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkGetTransactions", reflect.TypeOf((*MockInwardTransactionsBulkGetter)(nil).BulkGetTransactions), arg0, arg1)
}
