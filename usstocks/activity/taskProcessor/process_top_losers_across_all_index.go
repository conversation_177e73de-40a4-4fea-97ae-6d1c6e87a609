package taskProcessor

import (
	"context"
	"sort"

	"github.com/pkg/errors"

	usstocksPb "github.com/epifi/gamma/api/usstocks"
	catalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	orderMgPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/scheduled_task"
	"github.com/epifi/be-common/pkg/epifigrpc"
)

type ProcessTopLosersAcrossAllIndex struct {
	baseCatalogProcessor IBaseCatalogProcessor
	catalogClient        catalogPb.CatalogManagerClient
	orderMgClient        orderMgPb.OrderManagerClient
}

func NewProcessTopLosersAcrossAllIndex(catalogClient catalogPb.CatalogManagerClient, baseCatalogProcessor IBaseCatalogProcessor, orderMgClient orderMgPb.OrderManagerClient) *ProcessTopLosersAcrossAllIndex {
	return &ProcessTopLosersAcrossAllIndex{
		catalogClient:        catalogClient,
		baseCatalogProcessor: baseCatalogProcessor,
		orderMgClient:        orderMgClient,
	}
}

// nolint: dupl
func (p *ProcessTopLosersAcrossAllIndex) Process(ctx context.Context, _ *scheduled_task.ScheduledTask) error {
	getMarketStatusResp, getMarketStatusErr := p.orderMgClient.GetMarketStatus(ctx, &orderMgPb.GetMarketStatusRequest{})
	if grpcErr := epifigrpc.RPCError(getMarketStatusResp, getMarketStatusErr); grpcErr != nil {
		return errors.Wrapf(grpcErr, "error while GetMarketStatus")
	}
	// do nothing if market is not open
	if getMarketStatusResp.GetMarketStatus() != usstocksPb.MarketStatus_MARKET_STATUS_OPEN {
		return nil
	}
	topLosersStocks := make([]*catalogPb.Stock, 0)
	for _, index := range SupportedUssIndex {
		resp, err := p.catalogClient.GetMarketIndex(ctx, &catalogPb.GetMarketIndexRequest{IndexName: index, TopLosersListSize: COLLECTION_SIZE})
		if err = epifigrpc.RPCError(resp, err); err != nil {
			return errors.Wrapf(err, "error while GetMarketIndex %s index", index)
		}
		for _, stocks := range resp.GetTopLoserStocks() {
			if stocks.GetDailyPerformance().GetDailyPercentChange() < 0 {
				topLosersStocks = append(topLosersStocks, stocks)
			}
		}
	}
	// These custom functions are designed to handle potential overlaps of stocks across different indices.
	// Note: A custom function is implemented due to possible discrepancies in daily prices retrieved via network calls.
	// The 'lo' package compares entire structures to ensure uniqueness.
	topLosersStocksUnq := make([]*catalogPb.Stock, 0)
	isStockSeen := make(map[string]bool, 0)
	for _, stock := range topLosersStocks {
		if isStockSeen[stock.GetId()] {
			continue
		}
		topLosersStocksUnq = append(topLosersStocksUnq, stock)
		isStockSeen[stock.GetId()] = true
	}
	sort.SliceStable(topLosersStocksUnq, func(i, j int) bool {
		return topLosersStocksUnq[i].GetDailyPerformance().GetDailyPercentChange() < topLosersStocksUnq[j].GetDailyPerformance().GetDailyPercentChange()
	})
	filteredTopLosersStocks := topLosersStocksUnq[:min(COLLECTION_SIZE, len(topLosersStocksUnq))]
	err := p.updateCollectionForGivenStocks(ctx, filteredTopLosersStocks, catalogPb.CollectionName_COLLECTION_NAME_TOP_LOSERS_ACROSS_ALL_INDEX.String())
	if err != nil {
		return errors.Wrapf(err, "error while updating collection %s", catalogPb.CollectionName_COLLECTION_NAME_TOP_LOSERS_ACROSS_ALL_INDEX.String())
	}
	return nil
}

// nolint: dupl
func (p *ProcessTopLosersAcrossAllIndex) updateCollectionForGivenStocks(ctx context.Context, stocks []*catalogPb.Stock, collectionId string) error {
	collectionStockMapping := make([]*catalogPb.CollectionStockMapping, 0)
	weight := 1
	for _, stock := range stocks {
		collectionStockMapping = append(collectionStockMapping, &catalogPb.CollectionStockMapping{
			CollectionId: collectionId,
			StockId:      stock.GetId(),
			Weight:       uint32(weight),
		})
		weight++
	}
	return p.baseCatalogProcessor.UpdateCollectionWithStkMapping(ctx, collectionStockMapping, collectionId)
}
