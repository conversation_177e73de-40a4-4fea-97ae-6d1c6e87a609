package consumer_test

import (
	"flag"
	"os"
	"testing"

	genConf "github.com/epifi/gamma/usstocks/config/genconf"
	"github.com/epifi/gamma/usstocks/test"
)

var (
	conf *genConf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer

// nolint
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	conf, teardown = test.InitTestServerWithoutDBConn()

	svcTS = newSvcTestSuite(conf)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
