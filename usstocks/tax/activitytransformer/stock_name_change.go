package activitytransformer

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	ussTaxPb "github.com/epifi/gamma/api/usstocks/tax"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/usstocks/utils"
)

type StockNameChangeProcessor struct {
	corporateActionsFetcher utils.ICorporateActionsFetcher
}

func NewStockNameChangeProcessor(corporateActionsFetcher utils.ICorporateActionsFetcher) *StockNameChangeProcessor {
	return &StockNameChangeProcessor{
		corporateActionsFetcher: corporateActionsFetcher,
	}
}

func (s *StockNameChangeProcessor) ProcessActivity(ctx context.Context, req *ProcessActivityRequest) (*ProcessActivityResponse, error) {
	res, err := processQtySuspenseActivity(req, vgStocksPb.NonTradeActivityType_NON_TRADE_ACTIVITY_TYPE_STOCK_NAME_CHANGE)
	if err != nil {
		return nil, fmt.Errorf("error processing name change activity: %w", err)
	}
	return res, nil
}

// ProcessSuspenseActivities evaluates stock name change activity
// A name change is executed by debiting all available units from the account under old symbol and crediting same number
// of units under new symbol. There are some name change scenarios which donot lead to symbol update hence a credit and
// debit on same symbol is observed
// Eg An account holds 5 units of APPL stock. A stock name change is announced to new symbol APL
// A debit on 5 APPL units and credit of 5 APL units will be observed.
func (s *StockNameChangeProcessor) ProcessSuspenseActivities(ctx context.Context, req *ProcessSuspenseActivitiesRequest) (*ProcessSuspenseActivitiesResponse, error) {
	txns := req.Transactions
	processedSymbols := make(map[string]any)
	for symbol, activities := range req.ActivitiesMap {
		if _, found := processedSymbols[symbol]; found {
			// either old or new symbol has already been processed so this symbol need not be processed again
			continue
		}
		if len(activities) == 0 {
			return nil, fmt.Errorf("atleast one name change activity is expected for %s", symbol)
		}
		nameChange, err := s.corporateActionsFetcher.GetCorporateAction(ctx, symbol, activities[0].GetExecutedDate(), vgStocksPb.CorporateActionType_CORPORATE_ACTION_TYPE_NAME_CHANGE)
		if err != nil {
			return nil, fmt.Errorf("error fetching name change corporate actions: %w for symbol : %s", err, symbol)
		}
		oldSymbol, newSymbol := nameChange.GetNameChange().GetOldSymbol(), nameChange.GetNameChange().GetNewSymbol()
		txns, err = s.processNameChange(oldSymbol, newSymbol, req.ActivitiesMap, txns)
		if err != nil {
			return nil, fmt.Errorf("error processing name change form %s to %s: %w", oldSymbol, newSymbol, err)
		}
		processedSymbols[oldSymbol], processedSymbols[newSymbol] = nil, nil
	}
	return &ProcessSuspenseActivitiesResponse{Transactions: txns}, nil
}

func (s *StockNameChangeProcessor) processNameChange(oldSymbol, newSymbol string, activitiesMap map[string][]*vgStocksPb.NonTradeActivity, txns []*ussTaxPb.TransactionWrapper) ([]*ussTaxPb.TransactionWrapper, error) {
	var mergedActivities []*vgStocksPb.NonTradeActivity
	if oldSymbol == newSymbol {
		mergedActivities = activitiesMap[oldSymbol]
	} else {
		oldSymbolActivity, err := s.extractAndValidateOldSymbolActivity(oldSymbol, activitiesMap)
		if err != nil {
			return nil, fmt.Errorf("error extracting old symbol activity: %w", err)
		}
		newSymbolActivity, err := s.extractAndValidateNewSymbolActivity(newSymbol, activitiesMap)
		if err != nil {
			return nil, fmt.Errorf("error extracting new symbol activity: %w", err)
		}
		mergedActivities = append(mergedActivities, oldSymbolActivity, newSymbolActivity)
	}

	txns, err := s.applyNameChangeOnTransactions(oldSymbol, newSymbol, txns, mergedActivities)
	if err != nil {
		return nil, fmt.Errorf("error in applying name change corporate actions: %w", err)
	}

	return txns, nil
}

// extractAndValidateOldSymbolActivity extracts activities for old symbol and performs validations
func (s *StockNameChangeProcessor) extractAndValidateOldSymbolActivity(oldSymbol string, activitiesMap map[string][]*vgStocksPb.NonTradeActivity) (*vgStocksPb.NonTradeActivity, error) {
	oldSymbolActivities, ok := activitiesMap[oldSymbol]
	if !ok {
		return nil, fmt.Errorf("old symbol %s activities not found", oldSymbol)
	}
	if len(oldSymbolActivities) != 1 {
		return nil, fmt.Errorf("expected 1 active activity found %d for old symbol", len(oldSymbolActivities))
	}
	if oldSymbolActivities[0].GetDividendMetaInfo().GetQty() >= 0 {
		return nil, fmt.Errorf("expected negative qty for old symbol %s activities", oldSymbol)
	}
	return oldSymbolActivities[0], nil
}

// extractAndValidateNewSymbolActivity extracts activities for new symbol and performs validations
func (s *StockNameChangeProcessor) extractAndValidateNewSymbolActivity(newSymbol string, activitiesMap map[string][]*vgStocksPb.NonTradeActivity) (*vgStocksPb.NonTradeActivity, error) {
	newSymbolActivities, ok := activitiesMap[newSymbol]
	if !ok {
		return nil, fmt.Errorf("old symbol %s activities not found", newSymbol)
	}
	if len(newSymbolActivities) != 1 {
		return nil, fmt.Errorf("expected 1 active activity found %d for old symbol", len(newSymbolActivities))
	}
	if newSymbolActivities[0].GetDividendMetaInfo().GetQty() <= 0 {
		return nil, fmt.Errorf("expected positive qty for old symbol %s activities", newSymbol)
	}
	return newSymbolActivities[0], nil
}

// applyNameChangeOnTransactions replaces the old symbol with new symbol in all relevant transactions
func (s *StockNameChangeProcessor) applyNameChangeOnTransactions(oldSymbol, newSymbol string, txns []*ussTaxPb.TransactionWrapper, nameChangeActivities []*vgStocksPb.NonTradeActivity) ([]*ussTaxPb.TransactionWrapper, error) {
	found := false
	activities := lo.Map(nameChangeActivities, func(activity *vgStocksPb.NonTradeActivity, _ int) *vgStocksPb.AccountActivity {
		return vgStocksPb.NewNonTradeAccountActivity(activity)
	})
	for _, txn := range txns {
		if txn.GetSymbol() == oldSymbol {
			txn.Symbol = newSymbol
			txn.ParentActivities = append(txn.ParentActivities, activities...)
			found = true
		}
	}
	if !found {
		return nil, fmt.Errorf("could not find any transaction for old symbol %s", oldSymbol)
	}

	return txns, nil
}
