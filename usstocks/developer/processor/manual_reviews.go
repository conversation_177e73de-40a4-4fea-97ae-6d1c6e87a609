package processor

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	pb "github.com/epifi/gamma/api/usstocks/account"
	devPb "github.com/epifi/gamma/api/usstocks/developer"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/usstocks/account/dao"
)

const (
	reviewReasonTypeParamName = "reviewReasonTypeParamName"
	reviewStatusTypeParamName = "reviewStatusTypeParamName"
	reviewItemTypeParamName   = "reviewItemTypeParamName"
)

type ManualReviewsProcessor struct {
	manualReviewsDao dao.ManualReviewDao
	reviewTypes      []string
	reviewStatuses   []string
	reviewReasons    []string
}

func NewManualReviewsProcessor(manualReviewsDao dao.ManualReviewDao) *ManualReviewsProcessor {
	var reviewTypes []string
	for reviewTypeIntValStr, _ := range pb.ReviewItemType_value {
		reviewTypes = append(reviewTypes, reviewTypeIntValStr)
	}

	var reviewStatuses []string
	for reviewStatusStrVal, _ := range pb.ReviewStatus_value {
		reviewStatuses = append(reviewStatuses, reviewStatusStrVal)
	}

	var reviewReasons []string
	for reviewReasonStrVal, _ := range pb.ReviewReason_value {
		reviewReasons = append(reviewReasons, reviewReasonStrVal)
	}

	return &ManualReviewsProcessor{
		manualReviewsDao: manualReviewsDao,
		reviewTypes:      reviewTypes,
		reviewReasons:    reviewReasons,
		reviewStatuses:   reviewStatuses,
	}
}

func (s *ManualReviewsProcessor) FetchParamList(_ context.Context, _ devPb.USStocksEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            actorIdParamName,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            reviewItemTypeParamName,
			Label:           "Review type",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         s.reviewTypes,
		},
		{
			Name:            reviewStatusTypeParamName,
			Label:           "Review status",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         s.reviewStatuses,
		},
		{
			Name:            reviewReasonTypeParamName,
			Label:           "Review reason",
			Type:            db_state.ParameterDataType_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         s.reviewReasons,
		},
	}
	return paramList, nil
}

// nolint:dupl
func (s *ManualReviewsProcessor) FetchData(ctx context.Context, _ devPb.USStocksEntity, filters []*db_state.Filter) (string, error) {
	var marshalledRes []byte
	marshalOptions := protojson.MarshalOptions{}
	marshalOptions.UseEnumNumbers = false

	manualReviews, err := s.getManualReviews(ctx, filters)
	if err != nil {
		logger.Error(ctx, "error getting manual reviews", zap.Error(err))
		marshalledRes = []byte(err.Error())
		return string(marshalledRes), nil
	}

	var marshalledManualReviews []string
	for _, manualReview := range manualReviews {
		b, marshalErr := marshalOptions.Marshal(manualReview)
		if marshalErr != nil {
			logger.Error(ctx, fmt.Sprintf("error marshalling to json: %v", manualReview), zap.Error(err))
			marshalledRes = []byte(fmt.Sprintf("error marshalling to json: %v, err: %s", manualReview, err.Error()))
			return string(marshalledRes), nil
		}
		marshalledManualReviews = append(marshalledManualReviews, string(b))
	}
	return "[" + strings.Join(marshalledManualReviews, ", ") + "]", nil
}

func (s *ManualReviewsProcessor) getManualReviews(ctx context.Context, filters []*db_state.Filter) ([]*pb.ManualReview, error) {
	if len(filters) == 0 {
		return nil, fmt.Errorf("no filters applied")
	}

	var filterOptions []storageV2.FilterOption
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case actorIdParamName:
			if filter.GetStringValue() != "" {
				filterOptions = append(filterOptions, dao.WithActorId(filter.GetStringValue()))
			}
		case reviewItemTypeParamName:
			filterOptions = append(filterOptions, dao.WithManualReviewItemType(pb.ReviewItemType(pb.ReviewItemType_value[filter.GetDropdownValue()])))
		case reviewReasonTypeParamName:
			filterOptions = append(filterOptions, dao.WithManualReviewReason(pb.ReviewReason(pb.ReviewReason_value[filter.GetDropdownValue()])))
		case reviewStatusTypeParamName:
			filterOptions = append(filterOptions, dao.WithManualReviewStatus(pb.ReviewStatus(pb.ReviewStatus_value[filter.GetDropdownValue()])))
		default:
			return nil, fmt.Errorf("unhandled filter param name: %s", filter.GetParameterName())
		}
	}
	filterOptions = append(filterOptions, dao.WithLimit(50))
	manualReviews, err := s.manualReviewsDao.GetByFilterOptions(ctx, filterOptions...)
	if err != nil {
		return nil, errors.Wrap(err, "error getting manual reviews")
	}
	return manualReviews, nil
}
