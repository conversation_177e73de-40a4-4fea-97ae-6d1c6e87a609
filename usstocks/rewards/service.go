package rewards

import (
	"github.com/epifi/be-common/api/celestial"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	ussRewPb "github.com/epifi/gamma/api/usstocks/rewards"
	genConf "github.com/epifi/gamma/usstocks/config/genconf"
	"github.com/epifi/gamma/usstocks/rewards/dao"
)

type Service struct {
	ussRewPb.UnimplementedUssRewardManagerServer
	config           *genConf.Config
	rewardRequestDao dao.RewardRequestDao
	rewardsClient    rewardsPb.RewardsGeneratorClient
	celestialClient  celestial.CelestialClient
	txnExecutor      storagev2.TxnExecutor
}

func NewService(config *genConf.Config, rewardRequestDao dao.RewardRequestDao, rewardsClient rewardsPb.RewardsGeneratorClient, celestialClient celestial.CelestialClient, txnExecutor storagev2.TxnExecutor) *Service {
	return &Service{
		config:           config,
		rewardRequestDao: rewardRequestDao,
		rewardsClient:    rewardsClient,
		celestialClient:  celestialClient,
		txnExecutor:      txnExecutor,
	}
}
