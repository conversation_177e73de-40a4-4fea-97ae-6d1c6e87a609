CREATE TABLE IF NOT EXISTS order_vendor_order_map (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
    vendor_order_id STRING NOT NULL,
    vendor STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id),
	INDEX order_vendor_order_map_updated_at_idx (updated_at ASC),
	UNIQUE INDEX order_vendor_order_map_order_id_unique_idx (order_id ASC)
);

comment on table order_vendor_order_map is 'Stores mapping of internal order id to vendor order id and vendor(in case of PG, eg:Razorpay)';
comment on column order_vendor_order_map.order_id is 'Stores order Id of created at our end.';
comment on column order_vendor_order_map.vendor_order_id is 'Stores order id created at vendor end(in case of PG, eg:Ra<PERSON>pay).';
comment on column order_vendor_order_map.vendor is 'Stores vendor where we have initiate the order.';
