CREATE TABLE IF NOT EXISTS upi_number_pi_mapping (
	id 			UUID		 NOT NULL DEFAULT gen_random_uuid(),
	pi_id		STRING		 NOT NULL,
	upi_number  STRING 		 NOT NULL,
	state 	 	STRING 		 NOT NULL,
	expire_at   TIMESTAMPTZ  NULL,
	-- standard time stamp fields
	created_at       TIMESTAMPTZ    	NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at       TIMESTAMPTZ    	NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at       TIMESTAMPTZ    	NULL,
	PRIMARY KEY(id),
	UNIQUE(upi_number),
	INDEX upi_number_pi_mapping_updated_at_idx (updated_at DESC),
	INDEX upi_number_pi_mapping_pi_id_idx(pi_id ASC),
	FAMILY "frequently_updated" (state, expire_at, updated_at),
	FAMILY "primary"(id, pi_id, upi_number, created_at, deleted_at)
	);
COMMENT ON TABLE upi_number_pi_mapping IS 'maintains a mapping between upi number and pi id of the vpa';
COMMENT ON COLUMN upi_number_pi_mapping.id IS 'stores the unique random id generated by default and acts as a primary key';
COMMENT ON COLUMN upi_number_pi_mapping.pi_id IS 'stores the Pi id of the vpa to which upi number belongs to';
COMMENT ON COLUMN upi_number_pi_mapping.upi_number IS 'stores the upi number linked to the vpa';
COMMENT ON COLUMN upi_number_pi_mapping.state IS '{"proto_type":"upi.mapper.enums.UpiNumberPiMappingState", "comment":"stores the state of the upi_number linked to the vpa", "ref":"api.upi.mapper.enums.upi_number_pi_mapping_enums.proto"}';
COMMENT ON COLUMN upi_number_pi_mapping.expire_at IS 'stores the time till which the upi number can be relinked before it gets recycled after deregistering';
