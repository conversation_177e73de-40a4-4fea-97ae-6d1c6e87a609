-- Card creation failed for user due to name length greater than 20 characters
-- but we were not able to update card and creation states to failed.
-- We are updating the card state to INVALID and card creation request state to FAILED to mark them as failed.
-- This will be resolved once https://github.com/epiFi/gamma/pull/10350 gets merged
update cards set state = 'INVALID' where id = 'baff133c-ce91-4726-b033-ba2284c9fb64';
update card_creation_requests set state = 'FAILED' where card_id = 'baff133c-ce91-4726-b033-ba2284c9fb64';
