-- https://monorail.pointz.in/p/fi-app/issues/detail?id=28788
-- B2C_FUND_TRANSFER orders move to MANUAL INTERVENTION when "ResourceExhausted" error is thrown
-- Moving these orders to PAYMENT_FAILED state and associated txn to FAILED state


UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220929i12W3Z3wSjCVPbPk/ZxxEA==' AND status = 'INITIATED' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220929wt+tA9gmTZGF0s8VbwmroQ==' AND status = 'INITIATED' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN2209293/I+XUSSSZqZpVRzyosttw==' AND status = 'INITIATED' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220929fifNoC26RWOJy6ETcs6ZaA==' AND status = 'INITIATED' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN2209286R7Yu14uSyqOrxWO2BdM9w==' AND status = 'INITIATED' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220928jZGy0jmJQwK0odFMODWB8A==' AND status = 'INITIATED' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220928Ha7PRUq2RIaerzJaVA480w==' AND status = 'INITIATED' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220927oJa2rftAR6qLCr+p2OTmWA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220918wMLKRg9/RR+z0Dv07tnE1w==' AND status = 'IN_PROGRESS' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN2208024I8vmHaFTaWVdIHQGMd2IA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220803RPexT38JTmeTg0njRilv1g==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220803t/rETJJTTE6O+0v1zSGilw==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220803bK0mY92+TmS2+Y9qx+i0YQ==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220801WbWYP6UxRxCmo0GtFAGCMA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220929zjbKlCbRQsG7xpcH9ekJMA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220929d3oCsnLFS0qvLiVmFCDTPw==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220929DNk+Cq8fRTGVU8XD7HvTNg==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220929dBBEziZ2TJ6vpdbziyrr4Q==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220928wgwf4dfsR2udAR2aWTisGw==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220928whpmVCpPRS6sG6azXWbhmA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220928xbaA3RcyQYKt8745/QsmQw==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220927KfScNsEBQUOwEtFlsiLU6g==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220918U/5qG4F5QVCjyfB7v7tYRA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220802IRpbCDWcSSaArDiaX+hlvA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220801z5zqcfVmT6ifstespfJVlg==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220801Fo1AR/ltQnOeAAjVzdEkFA==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220801nCJ08VpiSFyGUzLloXvI/A==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220801LcY7UZHwTpaKgZ4ksl3K6Q==' AND status = 'MANUAL_INTERVENTION' ;
