-- Deposit Pre-closures started during CSIS time are stuck in SUSPECT state at vendor and hence
-- we keep on performing a status check on it.
-- Marking those deposit requests as failed will trigger the pre-closure flow again with new requestIDs which
-- the vendor will process successfully.
-- The new requests are initiated as last_attempt is still false.
-- Freshdesk 11102, Monorail 9971 - DR210601u4xjQ1FGTAmNvQUl/9Gmlg==, DR210601mgQaG7NjQcGnweQjnWMwqg==
-- Freshdesk 9653 DR2105305OB9akmoSx6uuYrkCTwvjg==,
-- Freshdesk 10662 DR210601u4xjQ1FGTAmNvQUl/9Gmlg=, DR210601mgQaG7NjQcGnweQjnWMwqg==
-- Freshdesk 10749 DR210601dc6cFxB4Rre86W41WrHolw==, DR2106016caf2SwtTLChSa+BKWbc8w==
UPDATE deposit_requests
SET state = 'REQUEST_FAILED'
where id in ('DR210601u4xjQ1FGTAmNvQUl/9Gmlg==',
             'DR210601mgQaG7NjQcGnweQjnWMwqg==',
             'DR2105305OB9akmoSx6uuYrkCTwvjg==',
             'DR210601dc6cFxB4Rre86W41WrHolw==',
             'DR2106016caf2SwtTLChSa+BKWbc8w==')
  and state = 'REQUEST_IN_PROGRESS'
  and last_attempt = false
  and type = 'PRECLOSE'
  and created_at >= current_date - interval '7 days';
