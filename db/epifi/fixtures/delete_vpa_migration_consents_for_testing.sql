-- We tried to test vpa migration on production sometime back, but faced issues from vendor side and it failed
-- Now that issues are resolved at vendor's end, we want to try vpa migration again, but as we tried last time,
-- user's consent is already captured which will not allow us to retry that again, so we need to delete it for those users.
DELETE FROM consents WHERE
actor_id IN ('AC220627gHhDm6mtQEqG6NNbjOGxFw==')
AND consent_type = 'VPA_MIGRATION';
