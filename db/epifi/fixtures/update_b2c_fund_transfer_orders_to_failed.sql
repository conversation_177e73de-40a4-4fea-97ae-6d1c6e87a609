-- https://monorail.pointz.in/p/fi-app/issues/detail?id=28784
-- B2C_FUND_TRANSFER orders move to MANUAL INTERVENTION when "DATA NOT FOUND"
-- These are failed due to resource exhausted. After integration with new b2c account we have solved it. These order need to be updated/force process/reset workflow to re-initiate transaction.

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN2301050YDbn1bpRAGpOxDbbpL5dw==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220915JEqRsykPQSCSTZQ7pizENQ==' AND status = 'UNKNOWN' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN2209094M6kksoqQLWvHo58GwCrBA==' AND status = 'UNKNOWN' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220831JW8+WkdgSEW6ivF39w0sGg==' AND status = 'UNKNOWN' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220511i7/hh28sTgy05lBSDHCSRQ==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220206hK3sEIjdTSql+Q4+v+6Jjw==' AND status = 'UNKNOWN' ;

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN220206KVTo62JhQQy41lSnTafmpg==' AND status = 'UNKNOWN' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD220206a9Z8ZKPGRxeNMLSEgTe8fg==' AND status = 'IN_PAYMENT' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230105f0LDdCy1RQa9OV+S1Tmc9w==' AND status = 'MANUAL_INTERVENTION' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD2202066rmGc95aQJm0FoH/C5MZ7Q==' AND status = 'IN_PAYMENT' ;
