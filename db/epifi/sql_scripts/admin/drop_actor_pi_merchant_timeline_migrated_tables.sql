DROP TABLE IF EXISTS timelines CASCADE;
DROP TABLE IF EXISTS actor_pi_resolutions CASCADE;
DROP TABLE IF EXISTS actors CASCADE;
DROP TABLE IF EXISTS blocked_actors_map CASCADE;
DROP TABLE IF EXISTS payment_instruments CASCADE;
DROP TABLE IF EXISTS account_pis CASCADE;
DROP TABLE IF EXISTS aa_account_pis CASCADE;
DROP TABLE IF EXISTS pi_state_logs CASCADE;
DROP TABLE IF EXISTS account_merchant_infos CASCADE;
DROP TABLE IF EXISTS card_merchant_infos CASCADE;
DROP TABLE IF EXISTS merchant_pis CASCADE;
DROP TABLE IF EXISTS merchant_pi_gplace_data CASCADE;
DROP TABLE IF EXISTS merchants CASCADE;
DROP TABLE IF EXISTS probable_known_merchants CASCADE;
DROP TABLE IF EXISTS payment_instrument_purge_audits CASCADE;
