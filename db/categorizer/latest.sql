CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
CREATE TABLE public.crowd_aggregated_categories (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    pi_id character varying NOT NULL,
    accounting_entry character varying NOT NULL,
    ontology_ids jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.gplace_type_to_ontology_id_mappings (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    gplace_type character varying NOT NULL,
    ontology_id character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.merchant_pi_gplace_fi_ontologies (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    pi_id character varying NOT NULL,
    merchant_dirty_name character varying,
    latitude numeric(8,4),
    longitude numeric(8,4),
    fi_ontology_ids character varying[],
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.txn_categories (
    id character varying NOT NULL,
    txn_id character varying NOT NULL,
    actor_id character varying NOT NULL,
    ontology_id character varying NOT NULL,
    confidence_score numeric(4,2),
    provenance character varying NOT NULL,
    ds_categorisation_time timestamp with time zone,
    model_version character varying,
    is_display_enabled boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    data_channel character varying NOT NULL,
    categorisation_source character varying
);
CREATE TABLE public.txn_category_ontologies (
    ontology_id character varying NOT NULL,
    l0 character varying,
    l1 character varying,
    l2 character varying,
    l3 character varying,
    display_category character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.user_display_categories (
    display_category character varying NOT NULL,
    ontology_id character varying NOT NULL,
    category_type character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
CREATE TABLE public.user_future_txn_categories (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    actor_id character varying NOT NULL,
    pi_id character varying NOT NULL,
    accounting_entry_type character varying NOT NULL,
    display_category character varying NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
ALTER TABLE ONLY public.crowd_aggregated_categories
    ADD CONSTRAINT crowd_aggregated_categories_pkey PRIMARY KEY (pi_id, id);
ALTER TABLE ONLY public.gplace_type_to_ontology_id_mappings
    ADD CONSTRAINT gplace_type_to_ontology_id_mappings_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.merchant_pi_gplace_fi_ontologies
    ADD CONSTRAINT merchant_pi_gplace_fi_ontologies_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.txn_categories
    ADD CONSTRAINT txn_categories_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.txn_category_ontologies
    ADD CONSTRAINT txn_category_ontologies_pkey PRIMARY KEY (ontology_id);
ALTER TABLE ONLY public.user_display_categories
    ADD CONSTRAINT user_display_categories_pkey PRIMARY KEY (display_category);
ALTER TABLE ONLY public.user_future_txn_categories
    ADD CONSTRAINT user_future_txn_categories_pkey PRIMARY KEY (id);
CREATE UNIQUE INDEX crowd_aggregated_categories_pi_id_accounting_entry_key ON public.crowd_aggregated_categories USING btree (pi_id, accounting_entry) WHERE (deleted_at IS NULL);
CREATE INDEX crowd_aggregated_categories_updated_at_idx ON public.crowd_aggregated_categories USING btree (updated_at);
CREATE INDEX gplace_type_to_ontology_id_mappings_gplace_type_idx ON public.gplace_type_to_ontology_id_mappings USING btree (gplace_type);
CREATE INDEX gplace_type_to_ontology_id_mappings_updated_at_idx ON public.gplace_type_to_ontology_id_mappings USING btree (updated_at);
CREATE INDEX merchant_pi_gplace_fi_ontologies_merchant_name_location_idx ON public.merchant_pi_gplace_fi_ontologies USING btree (merchant_dirty_name, latitude, longitude);
CREATE UNIQUE INDEX merchant_pi_gplace_fi_ontologies_pi_id_key ON public.merchant_pi_gplace_fi_ontologies USING btree (pi_id);
CREATE INDEX merchant_pi_gplace_fi_ontologies_updated_at_idx ON public.merchant_pi_gplace_fi_ontologies USING btree (updated_at);
CREATE INDEX txn_categories_actor_id_updated_at_data_channel_provenance_idx ON public.txn_categories USING btree (actor_id, updated_at, data_channel, provenance, is_display_enabled) WHERE (deleted_at IS NULL);
CREATE INDEX txn_categories_ontology_id_idx ON public.txn_categories USING btree (ontology_id);
CREATE UNIQUE INDEX txn_categories_txn_id_actor_id_ontology_id_provenance_key ON public.txn_categories USING btree (txn_id, actor_id, ontology_id, provenance) WHERE (deleted_at IS NULL);
CREATE INDEX txn_categories_updated_at_idx ON public.txn_categories USING btree (updated_at);
CREATE INDEX txn_category_ontologies_display_category_idx ON public.txn_category_ontologies USING btree (display_category) WHERE (deleted_at IS NULL);
CREATE INDEX txn_category_ontologies_l0_idx ON public.txn_category_ontologies USING btree (l0);
CREATE INDEX txn_category_ontologies_ontology_id_idx ON public.txn_category_ontologies USING btree (ontology_id) WHERE (deleted_at IS NULL);
CREATE INDEX txn_category_ontologies_updated_at_idx ON public.txn_category_ontologies USING btree (updated_at);
CREATE INDEX user_future_txn_categories_actor_id_pi_id_idx ON public.user_future_txn_categories USING btree (actor_id, pi_id) WHERE (deleted_at IS NULL);
CREATE INDEX user_future_txn_categories_updated_at_idx ON public.user_future_txn_categories USING btree (updated_at);
ALTER TABLE ONLY public.txn_categories
    ADD CONSTRAINT txn_categories_ontology_id_fkey FOREIGN KEY (ontology_id) REFERENCES public.txn_category_ontologies(ontology_id);
