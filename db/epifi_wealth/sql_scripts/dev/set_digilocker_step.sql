update onboarding_step_details
set staled_at=NOW()
where id = (
	-- fetch the latest step row
	select id
	from onboarding_step_details
	where onboarding_details_id = (
		select id
		from onboarding_details
		where actor_id = 'AC210905BKx4SylyRci0VoMnOvD+qA=='
		  and onboarding_type = 'ONBOARDING_TYPE_WEALTH'
	)
	  AND step = 'ONBOARDING_STEP_COLLECT_MISSING_PERSONAL_INFO'
	order by created_at desc
	limit 1
	);

update onboarding_details set current_step = 'ONBOARDING_STEP_DOWNLOAD_FROM_DIGILOCKER', status = 'ONBOARDING_STATUS_IN_PROGRESS'
where actor_id = 'AC210905BKx4SylyRci0VoMnOvD+qA==' and onboarding_type = 'ONBOARDING_TYPE_WEALTH'
