-- indexes on tax_document_params table
CREATE UNIQUE INDEX IF NOT EXISTS tax_document_params_workflow_id_document_type_key ON public.tax_document_params(workflow_id, document_type);
CREATE INDEX IF NOT EXISTS tax_document_params_account_id_idx ON public.tax_document_params(account_id);

-- indexes on tax_documents table
CREATE UNIQUE INDEX IF NOT EXISTS tax_documents_external_id_key ON public.tax_documents(external_id);
CREATE UNIQUE INDEX IF NOT EXISTS tax_documents_acc_id_time_range_type_computed_till_doc_type_source_key ON public.tax_documents(account_id, time_range_type, computed_till, document_type, source)
	where deleted_at is NULL;
