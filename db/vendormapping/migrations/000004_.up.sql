
-- Functions update_dp_vendor_mappings_table_actors and update_dp_vendor_mappings_table_waitlist are used by scoop
-- to upsert data in PGDB. For ref - https://docs.google.com/document/d/16W0fZX-bEoL_eJrvLG3BtTTAqbwBJhplMDwMqMKlFeI/edit#heading=h.6a2njyi58jjf

CREATE FUNCTION update_dp_vendor_mappings_table_actors(prospectID TEXT, actorID TEXT, firehoseID TEXT, amplitudeID TEXT,
                                                       moengageID TEXT, createdAT timestamp with time zone,
                                                       updatedAT timestamp with time zone) RETURNS VOID AS
$$
BEGIN
    LOOP
        UPDATE dp_vendor_mappings
        SET updated_at = updatedAT
        WHERE actor_id = actorID;
        IF
            found THEN
            RETURN;
        END IF;

        BEGIN
            INSERT INTO dp_vendor_mappings (prospect_id, actor_id, firehose_id, amplitude_id, moengage_id, created_at,
                                            updated_at)
            VALUES (prospectID, actorID, firehoseID, amplitudeID, moengageID, createdAT, updatedAT);
            RETURN;
        EXCEPTION
            WHEN unique_violation THEN
        END;
    END LOOP;
END;
$$
    LANGUAGE plpgsql;

CREATE FUNCTION update_dp_vendor_mappings_table_waitlist(prospectID TEXT, actorID TEXT, firehoseID TEXT,
                                                         amplitudeID TEXT, moengageID TEXT,
                                                         createdAT timestamp with time zone,
                                                         updatedAT timestamp with time zone) RETURNS VOID AS
$$
BEGIN
    INSERT INTO dp_vendor_mappings (prospect_id, actor_id, firehose_id, amplitude_id, moengage_id, created_at,
                                    updated_at)
    VALUES (prospectID, actorID, firehoseID, amplitudeID, moengageID, createdAT, updatedAT)
    ON CONFLICT (prospect_id) DO NOTHING;
    RETURN;
EXCEPTION
    WHEN unique_violation THEN
END;
$$
    LANGUAGE plpgsql;
