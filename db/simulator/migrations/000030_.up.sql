CREATE TABLE IF NOT EXISTS creditcard_vendor_responses
(
	id                   STRING      NOT NULL,
	entity_id            STRING      NOT NULL,
	vendor               STRING      NOT NULL,
	vendor_response_type STRING      NOT NULL,
	response_data        JSONB       NULL,
	created_at           TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at           TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	PRIMARY KEY (id, vendor, vendor_response_type ASC),
	INDEX index_on_entity_id (entity_id ASC)
);

CREATE TABLE IF NOT EXISTS credit_card_transactions
(
	id                        STRING      NOT NULL,
	reference_id              STRING      NOT NULL,
	transaction_type          STRING      NOT NULL,
	transaction_transfer_type STRING      NOT NULL,
	transaction_time          TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	transaction_status        STRING      NOT NULL,
	billing_status            STRING      NOT NULL,
	authorization_status      STRING      NOT NULL,
	created_at                TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at                TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	transaction_details       JSONB       NULL,
	vendor                    STRING      NOT NULL,
	PRIMARY KEY (id, vendor ASC),
	INDEX index_on_refid_vendor (reference_id, vendor ASC),
	INDEX index_on_refid_txntime_vendor (reference_id, vendor, transaction_time ASC)
);
