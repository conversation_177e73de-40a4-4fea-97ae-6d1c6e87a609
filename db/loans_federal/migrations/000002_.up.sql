ALTER TABLE IF EXISTS loan_offers ADD CONSTRAINT loan_offers_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_offer_eligibility_criteria ADD CONSTRAINT loan_offer_eligibility_criteria_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_accounts ADD CONSTRAINT loan_accounts_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_requests ADD CONSTRAINT loan_requests_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_step_executions ADD CONSTRAINT loan_step_executions_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_activities ADD CONSTRAINT loan_activities_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_installment_info ADD CONSTRAINT loan_installment_info_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_installment_payout ADD CONSTRAINT loan_installment_payout_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_payment_requests ADD CONSTRAINT loan_payment_requests_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS loan_applicants ADD CONSTRAINT loan_applicants_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS collection_leads ADD CONSTRAINT collection_leads_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS collection_allocations ADD CONSTRAINT collection_allocations_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS collection_communications ADD CONSTRAINT collection_communications_pkey PRIMARY KEY (id);

ALTER TABLE IF EXISTS fetched_assets ADD CONSTRAINT fetched_assets_pkey PRIMARY KEY (id);
