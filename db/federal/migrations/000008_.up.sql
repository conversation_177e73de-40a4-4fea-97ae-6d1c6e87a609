CREATE TABLE IF NOT EXISTS loan_applicants
(
	id                  STRING      NOT NULL,
	actor_id            STRING      NOT NULL,
	vendor              STRING      NOT NULL,
	vendor_applicant_id STRING      NULL,
	vendor_request_id   STRING      NULL,
	loan_program        STRING      NULL,
	status              STRING      NOT NULL,
	sub_status          STRING      NOT NULL,
	created_at          TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at          TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at          TIMESTAMPTZ NULL,

	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX loan_applicants_idx_updated_at (updated_at DESC),
	UNIQUE INDEX unique_loan_applicants_actor_vendor_loan_program (actor_id ASC, vendor ASC, loan_program ASC)
);


ALTER TABLE IF EXISTS loan_requests ADD COLUMN IF NOT EXISTS next_action JSONB DEFAULT NULL;
COMMENT
ON COLUMN loan_requests.next_action IS 'next action provides the deeplink name for the next screen';

ALTER TABLE IF EXISTS loan_step_executions ADD COLUMN IF NOT EXISTS group_stage STRING DEFAULT NULL;
COMMENT
ON COLUMN loan_step_executions.group_stage IS 'group stage to which the loan step belongs';
