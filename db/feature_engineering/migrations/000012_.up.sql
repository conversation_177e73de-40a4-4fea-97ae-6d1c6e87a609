ALTER TABLE IF EXISTS cibil_tradelines
	ADD COLUMN IF NOT EXISTS id VARCHAR;
ALTER TABLE IF EXISTS cibil_tradelines_histories
	ADD COLUMN IF NOT EXISTS tradelines_id                      VARCHAR,
	ADD COLUMN IF NOT EXISTS generic_remark_symbol              VA<PERSON>HAR,
	ADD COLUMN IF NOT EXISTS generic_remark_description         VARCHAR,
	ADD COLUMN IF NOT EXISTS generic_remark_rank                VARCHAR,
	ADD COLUMN IF NOT EXISTS generic_remark_abbreviation        VARCHAR,
	ADD COLUMN IF NOT EXISTS rating_remark_symbol               VARCHAR,
	ADD COLUMN IF NOT EXISTS rating_remark_description          VARCHAR,
	ADD COLUMN IF NOT EXISTS rating_remark_rank                 VARCHAR,
	ADD COLUMN IF NOT EXISTS rating_remark_abbreviation         VARCHAR,
	ADD COLUMN IF NOT EXISTS complience_remark_symbol           VARCHAR,
	ADD COLUMN IF NOT EXISTS complience_remark_description      VARCHAR,
	ADD COLUMN IF NOT EXISTS complience_remark_rank             VARCHAR,
	ADD COLUMN IF NOT EXISTS complience_remark_abbreviation     VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_date            DATE NULL,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_status          VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_changed         VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_current_balance VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_high_credit     VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_credit_limit    VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_payment_due     VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_past_due        VARCHAR,
	ADD COLUMN IF NOT EXISTS monthly_pay_status_actual_payment  VARCHAR;
