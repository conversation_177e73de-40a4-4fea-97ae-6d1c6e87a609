CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE IF NOT EXISTS public.user_device_properties (
											   id UUID NOT NULL DEFAULT uuid_generate_v4(),
											   actor_id VARCHAR NOT NULL,
											   phone_number VARCHAR NOT NULL,
											   property VARCHAR NOT NULL,
											   value JSONB NOT NULL,
											   deleted_at_unix BIGINT,
											   created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
											   updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS user_device_properties_updated_at_idx ON user_device_properties (updated_at ASC);
COMMENT ON TABLE public.user_device_properties IS 'stores data for device related user properties';
COMMENT ON COLUMN public.user_device_properties.property IS 'stores the type of property eg. device language, device model etc.';
COMMENT ON COLUMN public.user_device_properties.value IS '{"proto_type": "user.UserDeviceProperty", "message": "stores the value corresponding to the device property"}';

CREATE TABLE IF NOT EXISTS public.contacts (
								 id UUID NOT NULL DEFAULT uuid_generate_v4(),
								 actor_id VARCHAR NOT NULL,
								 phone_number_hash VARCHAR NOT NULL,
								 created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
								 updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
								 deleted_at TIMESTAMP WITH TIME ZONE NULL
);
CREATE INDEX IF NOT EXISTS contacts_updated_at_idx ON contacts (updated_at ASC);

CREATE TABLE IF NOT EXISTS public.contact_properties (
										   hashed_phone_number VARCHAR NOT NULL,
										   actor_id VARCHAR NULL,
										   onboarding_completed_at TIMESTAMP WITH TIME ZONE NULL,
										   created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
										   updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
										   kyc_name JSONB NULL
);
CREATE INDEX IF NOT EXISTS contact_properties_updated_at_idx ON contact_properties (updated_at ASC);
COMMENT ON TABLE public.contact_properties IS 'This table stores all the properties related to a hashed contact. This table works to supplement contacts table';
