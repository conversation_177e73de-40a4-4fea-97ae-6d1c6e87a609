-- UPSERT INTO cc_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, valid_since, valid_till, deactivated_at, created_at, updated_at, deleted_at)
-- VALUES
--     ('CCO802HTvq+T/2NyBwbkFPDug191007==', 'AC221205Q91IVyjSQRGbXcTwU+kfMg==', '', 'FEDERAL', '{"limit": {"currencyCode": "INR", "units": "50000"}}', '2023-04-13 10:09:51.636000 +00:00', '2023-05-13 10:09:51.636000 +00:00', null, current_timestamp, current_timestamp, null),
-- 	('CCO882HTvq+T16NyBwbkFPDug191008==', 'AC2207044pm1Xa0XQ7mXRdeReRZ7wA==', '', 'FEDERAL', '{"limit": {"currencyCode": "INR", "units": "5001"}}', '2023-05-13 10:09:51.636000 +00:00', '2023-10-13 10:09:51.636000 +00:00', null, current_timestamp, current_timestamp, null);

-- UPSERT INTO cc_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, valid_since, valid_till, deactivated_at, created_at, updated_at, deleted_at)
-- VALUES ('CCO4j6/R/gzQUKjFMTlct1yrQ191007==','AC220825thpzpFqpQjCAVv/hkI5OJA==','','FEDERAL','{"limit": {"currencyCode": "INR", "units": "4999"}}','2023-07-10 10:09:51.636000 +00:00', '2023-08-10 10:09:51.636000 +00:00',null,current_timestamp,current_timestamp,null);

-- INSERT INTO cc_offer_eligibility_criteria (id, actor_id, vendor, status, vendor_response, deleted_at) VALUES ('CCECJMq4i7krQe6awZN9a4gHZA191007==', 'AC210117UpPlWTzZQ8Owpn0JnIwtqQ==', 'FEDERAL', 'CC_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED', '{}', null);
-- INSERT INTO credit_card.public.cc_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, valid_since, valid_till, deactivated_at, deleted_at, cc_offer_eligibility_criteria_id) VALUES ('CCOBr2WEsMBRrqIhAC5pdJrKw191007==','AC210117UpPlWTzZQ8Owpn0JnIwtqQ==','','FEDERAL','{"limit": {"currencyCode": "INR", "units": "5000"}}','2023-07-10 10:09:51.636000 +00:00', '2023-08-10 10:09:51.636000 +00:00',null,null,'CCECJMq4i7krQe6awZN9a4gHZA191007==')

-- INSERT INTO credit_card.public.cc_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, valid_since, valid_till, deactivated_at, deleted_at, cc_offer_eligibility_criteria_id) VALUES ('CCOkBHodi9GS2mboMwSWyVbtQ230721==','AC2206169RxlYXjKT3S2J5Qen/9DHA==','','FEDERAL','{"limit": {"currencyCode": "INR", "units": "20000"}}','2023-07-21 10:09:51.636000 +00:00', '2023-08-10 10:09:51.636000 +00:00',null,null,'')

INSERT INTO credit_card.public.cc_offers (id, actor_id, vendor_offer_id, vendor, offer_constraints, valid_since, valid_till, deactivated_at, deleted_at, cc_offer_eligibility_criteria_id) VALUES ('CCOrv4v6HVZROifuhgNpBbS/w191007==','AC221107Y1bjgXJFQEiCltMmQPoFgg==','','FEDERAL','{"limit": {"currencyCode": "INR", "units": "20000"}}','2023-07-24 10:09:51.636000 +00:00', '2023-08-10 10:09:51.636000 +00:00',null,null,'')
