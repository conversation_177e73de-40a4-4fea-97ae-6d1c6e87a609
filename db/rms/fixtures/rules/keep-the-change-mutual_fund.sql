-- Keep the change --
INSERT INTO rules (
	id, name, description, event_type, client,
	category, condition, actions, max_subscriptions_per_actor,
	max_subscriptions,
	state, weight
)
VALUES
	(
		'551c8700-21c9-4612-bfd9-430e64305bca','KEEP THE CHANGE', '{ "display_str": "When I spend with Fi, round-up to the next {configuredRoundAmount} and invest the difference in {depositAccountId}", "input_params": [ { "name": "configuredRoundAmount", "input_type": "MONEY" }, { "name": "depositAccountId", "input_type": "MUTUAL_FUND" } ] }',
		'PAYMENT', 'FITTT',
		'AUTO_INVEST', '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}',
		'{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "depositAmount", "expression": "calculateChangeAmountV2(paymentAmount, configuredRoundAmount)", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}]}}]}',
		'1', '**********',
		'RULE_STATE_ACTIVE', 90
	);

-- INSERT INTO rules (
--   id, name, description, event_type, client,
--   category, condition, actions, max_subscriptions_per_actor,
--   max_subscriptions,
--   state, weight
-- )
-- VALUES
--   (
--     '551c8700-21c9-4612-bfd9-430e64305bca','KEEP THE CHANGE', '{ "display_str": "When I spend with Fi, round-up to the next {configuredRoundAmount} and invest the difference in {mutualFundVal}", "input_params": [ { "name": "configuredRoundAmount", "input_type": "MONEY" }, { "name": "mutualFundVal", "input_type": "MUTUAL_FUND" } ] }',
--     'PAYMENT', 'FITTT',
--     'AUTO_INVEST', '{"condition": "paymentAmount.Units >= 50", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}',
--     ''{"actionArr": [{"type": "PURCHASE_MUTUAL_FUND", "data": {"expressions": [{"varName": "depositAmount", "expression": "calculateChangeAmountV2(paymentAmount, configuredRoundAmount)", "valuePath": {"paymentAmount": {"path": ["Data", "PaymentEvent", "OrderUpdate", "OrderWithTransactions", "Order", "Amount"]}}}]}}]}',
--     '1', '**********',
--     'RULE_STATE_ACTIVE', 90
--   );

INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state) values
    ('9a9df95f-331a-44ba-8644-d7f38a925057' ,'551c8700-21c9-4612-bfd9-430e64305bca', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"20"}}', false, 'PARAM_STATE_ACTIVE'),
    ('b156e3c2-47ce-4898-8597-31437e5366f7' ,'551c8700-21c9-4612-bfd9-430e64305bca', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"50"}}', true, 'PARAM_STATE_ACTIVE'),
    ('25193bd2-1565-4393-8889-c0e455aea6d2' ,'551c8700-21c9-4612-bfd9-430e64305bca', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"100"}}', false, 'PARAM_STATE_ACTIVE'),
    ('10e07989-06b1-4023-b90d-7bda8c7c6c76' ,'551c8700-21c9-4612-bfd9-430e64305bca', 'MONEY', '{"money_val":{"currency_code":"INR", "units":"200"}}', false, 'PARAM_STATE_ACTIVE');

INSERT INTO rule_display_infos (id, category, name, formatted_name, sentence_case_name ,home_page_text, home_page_img_url, landing_page_text, landing_page_img_url, background_color, tags_bg_color) values
    ('3e6b85f4-3748-4301-80b8-fbd9d7a50734', 'AUTO_INVEST', 'KEEP THE CHANGE', 'KEEP THE \nCHANGE', 'Keep the Change', 'When I spend with Fi, round-up and invest','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png','When I spend with Fi, round-up to the next <u>defaultMoneyValue</u> and invest the difference','https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Landing.png','#CDC6E8','#9183C7');

INSERT  into rule_tag_mappings (id, rule_id, tag_id) values ('4ad4c54f-4f86-49c7-b2a3-63a8bd9c40b8', '551c8700-21c9-4612-bfd9-430e64305bca', 'Shopping');

insert into home_cards (id, state, deeplink, card_data, weight) values
	('dad40a92-54c4-4552-9e07-b01e49f41663', 'HOME_CARD_ACTIVE', '{"screen": "FIT_COLLECTION_PAGE", "fitCollectionPageScreenOptions": {"collectionId": "e29f99de-1b78-4594-97d7-ddffc2689a99"}}',
	 '{"tags": [{"text": "NEW", "bgColor": "#9183C7"}], "description": {"descStr": "When I spend with Fi, \nround-up and invest", "displayInfo": {"fontColor": "#282828"}, "replaceableParamMap": {}}, "displayInfo": {"imgUrl": "https://epifi-icons.pointz.in/fittt-images/collections/RetailTherapy_Explore.jpg", "bgColor": "#CDC6E8", "homeCardOldVersionImgUrl": "https://epifi-icons.pointz.in/fittt-images/png-illustrations/TipYourself-Home.png"}}', 20);

insert into collections (id, state, display_info, child_ids, is_featured, type) values
('e29f99de-1b78-4594-97d7-ddffc2689a99', 'COLLECTION_ACTIVE', '{"name":{"text":"Retail\nTherapy"},"cardDisplayInfo":{"imgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/RetailTherapy_Explore.jpg", "homePageImgUrl":"https://epifi-icons.pointz.in/fittt-images/collections/RetailTherapy_Home.jpg","bgColor":""},"description":{"text":"Your shopping habit could be your saving habit. Set aside money when you shop? That’s two in the bag.","fontColor":""}}', '{"ruleIds":{"list":["551c8700-21c9-4612-bfd9-430e64305bca"]}}', true, 'COLLECTION_TYPE_AUTO_INVEST');
