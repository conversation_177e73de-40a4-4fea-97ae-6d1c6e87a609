ALTER TABLE aa_account_column_histories
	ADD CONSTRAINT fk_aa_account_column_histories_account_id
		FOREIGN KEY (account_id)
			REFERENCES aa_accounts (id);

ALTER TABLE aa_batch_process_transactions
	ADD CONSTRAINT fk_aa_batch_process_transactions_fetch_attempt_id
		FOREIGN KEY (fetch_attempt_id)
			REFERENCES aa_data_fetch_attempts (id);

ALTER TABLE aa_batch_process_transactions
	ADD CONSTRAINT fk_aa_batch_process_transactions_process_attempt_id
		FOREIGN KEY (process_attempt_id)
			REFERENCES aa_data_process_attempts (id);

ALTER TABLE aa_consent_account_mappings
	ADD CONSTRAINT fk_aa_consent_account_mappings_consent_reference_id
		FOREIGN KEY (consent_reference_id) REFERENCES aa_consents (id);

ALTER TABLE aa_consent_account_mappings
	ADD CONSTRAINT fk_aa_consent_account_mappings_account_reference_id
		FOREIGN KEY (account_reference_id) REFERENCES aa_accounts (id);

ALTER TABLE aa_consents
	ADD CONSTRAINT fk_aa_consents_consent_request_id
		FOREIGN KEY (consent_request_id)
			REFERENCES aa_consent_requests (id);

ALTER TABLE aa_data_fetch_attempts
	ADD CONSTRAINT fk_aa_data_fetch_attempts_consent_id
		FOREIGN KEY (consent_id)
			REFERENCES aa_consents (consent_id);

ALTER TABLE aa_data_fetch_attempts
	ADD CONSTRAINT fk_aa_data_fetch_attempts_consent_reference_id
		FOREIGN KEY (consent_reference_id)
			REFERENCES aa_consents (id);

ALTER TABLE aa_data_process_attempts
	ADD CONSTRAINT fk_aa_data_process_attempts_attempt_id
		FOREIGN KEY (attempt_id)
			REFERENCES aa_data_fetch_attempts (id);

ALTER TABLE aa_deposit_accounts
	ADD CONSTRAINT fk_aa_deposit_accounts_aa_account_id
		FOREIGN KEY (aa_account_id) REFERENCES aa_accounts (id);

ALTER TABLE aa_deposit_transactions
	ADD CONSTRAINT fk_aa_deposit_transactions_transaction_ref_id
		FOREIGN KEY (transaction_ref_id) REFERENCES aa_transactions (id);

ALTER TABLE aa_recurring_deposit_accounts
	ADD CONSTRAINT fk_aa_recurring_deposit_accounts_aa_account_id
		FOREIGN KEY (aa_account_id) REFERENCES aa_accounts (id);

ALTER TABLE aa_recurring_deposit_transactions
	ADD CONSTRAINT fk_aa_recurring_deposit_transactions_transaction_ref_id
		FOREIGN KEY (transaction_ref_id)
			REFERENCES aa_transactions (id);

ALTER TABLE aa_term_deposit_accounts
	ADD CONSTRAINT fk_aa_term_deposit_accounts_aa_account_id
		FOREIGN KEY (aa_account_id) REFERENCES aa_accounts (id);

ALTER TABLE aa_term_deposit_transactions
	ADD CONSTRAINT fk_aa_term_deposit_transactions_transaction_ref_id
		FOREIGN KEY (transaction_ref_id)
			REFERENCES aa_transactions (id);

ALTER TABLE aa_transactions
	ADD CONSTRAINT fk_aa_transactions_account_reference_id
		FOREIGN KEY (account_reference_id)
			REFERENCES aa_accounts (id);
