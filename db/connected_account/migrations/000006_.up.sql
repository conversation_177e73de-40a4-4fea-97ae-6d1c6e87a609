-- Create aa_equity_accounts Table
CREATE TABLE IF NOT EXISTS aa_equity_accounts
(
    id            UUID                     NOT NULL DEFAULT uuid_generate_v4(),
    aa_account_id UUID                     NOT NULL,
    created_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    deleted_at    TIMESTAMP WITH TIME ZONE,
    current_value VARCHAR                  NULL,

    PRIMARY KEY (id),
    CONSTRAINT fk_aa_equity_accounts_aa_account_id FOREIGN KEY (aa_account_id) REFERENCES aa_accounts (id)
);

-- Add Index on aa_equity_accounts Table
CREATE UNIQUE INDEX IF NOT EXISTS aa_equity_accounts_idx_aa_account_id ON aa_equity_accounts (aa_account_id ASC);
CREATE INDEX IF NOT EXISTS aa_equity_accounts_idx_updated_at ON aa_equity_accounts (updated_at DESC);

-- Comment on aa_equity_accounts Table
COMMENT ON TABLE aa_equity_accounts IS 'This table stores equity shares account information for all users which represents the form of part ownership of shareholders in a business venture';

-- Comment on aa_equity_accounts Columns
COMMENT ON COLUMN aa_equity_accounts.id IS 'The unique identifier for each equity account.';
COMMENT ON COLUMN aa_equity_accounts.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN aa_equity_accounts.created_at IS 'The timestamp when the equity account was created.';
COMMENT ON COLUMN aa_equity_accounts.updated_at IS 'The timestamp when the equity account was last updated.';
COMMENT ON COLUMN aa_equity_accounts.deleted_at IS 'The timestamp when the equity account was soft-deleted (if applicable).';
COMMENT ON COLUMN aa_equity_accounts.current_value IS 'The current value of investment as on date.';

-----------------------------------------------------------------------------------------------------------------------------

-- Create aa_equity_holdings Table
CREATE TABLE IF NOT EXISTS aa_equity_holdings
(
    id               UUID                     NOT NULL DEFAULT uuid_generate_v4(),
    aa_account_id    UUID                     NOT NULL,
    created_at       TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at       TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    deleted_at       TIMESTAMP WITH TIME ZONE,
    isin             VARCHAR                  NULL,
    issuer_name      VARCHAR                  NULL,
    type             VARCHAR                  NULL,
    units 			 VARCHAR 				  NULL,
	last_traded_price  VARCHAR                  NULL,
    meta_data        JSONB                             DEFAULT '{}'::jsonb,

    PRIMARY KEY (id),
    CONSTRAINT fk_aa_equity_holdings_aa_account_id FOREIGN KEY (aa_account_id) REFERENCES aa_accounts (id)
);

-- Add Index on aa_equity_holdings Table
CREATE UNIQUE INDEX IF NOT EXISTS aa_equity_holdings_idx_aa_account_id ON aa_equity_holdings (aa_account_id ASC);
CREATE INDEX IF NOT EXISTS aa_equity_holdings_idx_updated_at ON aa_equity_holdings (updated_at DESC);

-- Comment on aa_equity_holdings Table
COMMENT ON TABLE aa_equity_holdings IS 'This table stores equity shares account holding information for each equity account which represents the form of part ownership of shareholders in a business venture';

-- Comment on aa_equity_holdings Columns
COMMENT ON COLUMN aa_equity_holdings.id IS 'The unique identifier for each equity holding.';
COMMENT ON COLUMN aa_equity_holdings.aa_account_id IS 'The ID of the aa_accounts table.';
COMMENT ON COLUMN aa_equity_holdings.created_at IS 'The timestamp when the equity holding was created.';
COMMENT ON COLUMN aa_equity_holdings.updated_at IS 'The timestamp when the equity holding was last updated.';
COMMENT ON COLUMN aa_equity_holdings.deleted_at IS 'The timestamp when the equity holding was soft-deleted (if applicable).';
COMMENT ON COLUMN aa_equity_holdings.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN aa_equity_holdings.last_traded_price IS 'Last trade price of security or closing price';
COMMENT ON COLUMN aa_equity_holdings.issuer_name IS 'Name of party who issued the equities in which investment made';
COMMENT ON COLUMN aa_equity_holdings.type IS '{"proto_type":"", "comment":"represents the equity holding type"}';
COMMENT ON COLUMN aa_equity_holdings.units IS 'units allotted in folio till date';
COMMENT ON COLUMN aa_equity_holdings.meta_data IS '{"proto_type":"", "comment":"meta data for the equity holding which includes: isinDescription"}';

-----------------------------------------------------------------------------------------------------------------------------

-- Create aa_equity_transactions Table
CREATE TABLE IF NOT EXISTS aa_equity_transactions
(
    id                 UUID                     NOT NULL DEFAULT uuid_generate_v4(),
    created_at         TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at         TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    deleted_at         TIMESTAMP WITH TIME ZONE,
    transaction_ref_id UUID                     NOT NULL,
    isin               VARCHAR                  NULL,
    type 			   VARCHAR 					NULL,
    units 			   VARCHAR                  NULL,
    rate 			   VARCHAR					NULL,
    meta_data          JSONB                             DEFAULT '{}'::jsonb,

    PRIMARY KEY (id),
    CONSTRAINT fk_aa_equity_transactions_transaction_ref_id FOREIGN KEY (transaction_ref_id) REFERENCES aa_transactions (id)
);

-- Add Index on aa_equity_transactions Table
CREATE INDEX IF NOT EXISTS aa_equity_transactions_idx_transaction_ref_id ON aa_equity_transactions (transaction_ref_id ASC);
CREATE INDEX IF NOT EXISTS aa_equity_transactions_idx_updated_at ON aa_equity_transactions (updated_at DESC);

-- Comment on aa_equity_transactions Table
COMMENT ON TABLE aa_equity_transactions IS 'This table stores all of equity account transactions details that have been posted in an account.';

-- Comment on aa_equity_transactions Columns
COMMENT ON COLUMN aa_equity_transactions.id IS 'The unique identifier for each equity transaction.';
COMMENT ON COLUMN aa_equity_transactions.created_at IS 'The timestamp when the equity transaction was created.';
COMMENT ON COLUMN aa_equity_transactions.updated_at IS 'The timestamp when the equity transaction was last updated.';
COMMENT ON COLUMN aa_equity_transactions.deleted_at IS 'The timestamp when the equity transaction was soft-deleted (if applicable).';
COMMENT ON COLUMN aa_equity_transactions.transaction_ref_id IS 'The ID of aa_transaction table.';
COMMENT ON COLUMN aa_equity_transactions.isin IS 'International Securities Identification Number (ISIN) uniquely identifies a security.';
COMMENT ON COLUMN aa_equity_transactions.meta_data IS '{"proto_type":"", "comment":"meta data for the equity holding which includes: exchange, companyName, orderId, isinDescription"}';
