ALTER TABLE p2p_investors
	ADD COLUMN IF NOT EXISTS user_eligibility jsonb NULL;
ALTER TABLE p2p_investors
	ADD COLUMN IF NOT EXISTS scheme_eligibility jsonb NULL;

ALTER TABLE p2p_investment_schemes
	ADD COLUMN IF NOT EXISTS scheme_status jsonb NULL;

COMMENT
ON COLUMN p2p_investors.user_eligibility IS
    '{"proto_type": "p2p_investors.user_eligibility", "comment": "stores whether the user is eligible or not"}';
COMMENT
ON COLUMN p2p_investors.scheme_eligibility IS
    '{"proto_type": "p2p_investors.scheme_eligibility", "comment": "stores whether the user is eligible for particular scheme or not"}';

COMMENT
ON COLUMN p2p_investment_schemes.scheme_status IS
    '{"proto_type": "p2p_investment_schemes.scheme_status", "comment": "stores whether the scheme is still in use or not"}';
