package stateprocessor

import (
	"context"
	"errors"
	"fmt"

	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	"github.com/epifi/gamma/casper/exchanger/dao"
	"github.com/epifi/gamma/casper/internalerrors"
	"github.com/epifi/be-common/pkg/epifierrors"
)

type updateRedemptionCountProcessor struct {
	exchangerOrderDao dao.IExchangerOfferOrderDao
}

func NewUpdateRedemptionCountProcessor(exchangerOrderDao dao.IExchangerOfferOrderDao) *updateRedemptionCountProcessor {
	return &updateRedemptionCountProcessor{exchangerOrderDao: exchangerOrderDao}
}

var _ IStateProcessor = &updateRedemptionCountProcessor{}

// Process will increase the redemption count done by the actor for the given
// offer in the month of redemption, if user level monthly cap has been set for
// the offer
// todo(divyadeep): write UT
func (u updateRedemptionCountProcessor) Process(ctx context.Context, exchangerOrder *exchangerPb.ExchangerOfferOrder, exchangerOffer *exchangerPb.ExchangerOffer, _ *exchangerPb.TransitionAction, _ *ActorState) (*StateProcessingResp, error) {
	if exchangerOffer.GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap() != 0 {
		if err := u.exchangerOrderDao.UpsertMonthlyRedemptionCount(ctx, exchangerOrder, exchangerOffer); err != nil {
			if errors.Is(err, epifierrors.ErrRowNotUpdated) {
				// if row doesn't get updated, it means we've hit the max possible cap for the month for the actor/offer combo
				return nil, internalerrors.MaxMonthlyRedemptionsCountReached
			}
			return nil, fmt.Errorf("error while updating monthly redemptions count for actor/offer, err: %w", err)
		}
	}

	exchangerOrder.State = exchangerPb.ExchangerOfferOrderState_ORDER_STATE_REDEMPTION_COUNT_UPDATED

	updateErr := u.exchangerOrderDao.Update(ctx, exchangerOrder, []exchangerPb.ExchangerOfferOrderFieldMask{exchangerPb.ExchangerOfferOrderFieldMask_STATE})
	if updateErr != nil {
		return nil, fmt.Errorf("transient error while updating exchanger-order in updateRedemptionCountProcessor. err: %w", updateErr)
	}

	return &StateProcessingResp{ReachedState: exchangerOrder.GetState()}, nil
}
