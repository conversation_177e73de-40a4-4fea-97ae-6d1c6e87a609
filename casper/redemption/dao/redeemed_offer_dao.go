package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/pagination"

	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/casper/redemption/dao/model"
)

type RedeemedOfferDaoImpl struct {
	db *gormv2.DB
}

func NewRedeemedOfferDaoImpl(db pkgTypes.CasperPGDB) *RedeemedOfferDaoImpl {
	return &RedeemedOfferDaoImpl{db: db}
}

var _ IRedeemedOfferDao = &RedeemedOfferDaoImpl{}

var redeemedOfferColumnNames = map[redemption.RedeemedOfferFieldMask]string{
	redemption.RedeemedOfferFieldMask_OFFER_DETAILS:          "redeemed_offer_details",
	redemption.RedeemedOfferFieldMask_OFFER_EXPIRY_AT:        "offer_expires_at",
	redemption.RedeemedOfferFieldMask_OFFER_REDEMPTION_STATE: "redemption_state",
	redemption.RedeemedOfferFieldMask_REDEMPTION_PRICE:       "redemption_price",
}

func (c *RedeemedOfferDaoImpl) Create(ctx context.Context, redeemedOffer *redemption.RedeemedOffer) (*redemption.RedeemedOffer, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "Create", time.Now())
	if redeemedOffer.RedemptionRequestId == "" {
		return nil, errors.New("cannot create redeemed offer entry, request id nil")
	}
	dbModel, err := convertToDBModel(ctx, redeemedOffer)
	if err != nil {
		return nil, err
	}
	dbModel.ExternalId = idgen.RandAlphaNumericString(12)

	db := c.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	if res := db.Create(dbModel); res.Error != nil {
		return nil, errors.Wrap(res.Error, "error creating redeemed offer entry")
	}
	return convertToProtoModel(ctx, dbModel)
}

func (c *RedeemedOfferDaoImpl) GetByRequestId(ctx context.Context, refId string) (*redemption.RedeemedOffer, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "GetByRequestId", time.Now())
	dbModel := &model.RedeemedOffer{}
	db := c.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	res := db.Where("redemption_request_id = ?", refId).First(dbModel)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, nil
	}
	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "error fetching redeemed offer by request id")
	}
	return convertToProtoModel(ctx, dbModel)
}

func (c *RedeemedOfferDaoImpl) GetById(ctx context.Context, id string) (*redemption.RedeemedOffer, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "GetById", time.Now())
	dbModel := &model.RedeemedOffer{}
	db := c.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	res := db.Where("id = ?", id).First(dbModel)
	if errors.Is(res.Error, gormv2.ErrRecordNotFound) {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, errors.Wrap(res.Error, "error fetching redeemed offer by id")
	}
	return convertToProtoModel(ctx, dbModel)
}

func (c *RedeemedOfferDaoImpl) GetByIds(ctx context.Context, ids []string) ([]*redemption.RedeemedOffer, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "GetByIds", time.Now())
	var redeemedOffers []*model.RedeemedOffer
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	res := db.Where("id in (?)", ids).Find(&redeemedOffers)
	if len(redeemedOffers) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	if res.Error != nil {
		return nil, fmt.Errorf("error fetching redeemed offer by id, err: %w", res.Error)
	}
	var redeemedOfferProtos []*redemption.RedeemedOffer
	for _, redeemedOffer := range redeemedOffers {
		redeemedOfferProto, err := convertToProtoModel(ctx, redeemedOffer)
		if err != nil {
			return nil, fmt.Errorf("error while converting to redeemedOfferProto, redeemedOfferId: %s, err: %w", redeemedOffer.ID, err)
		}
		redeemedOfferProtos = append(redeemedOfferProtos, redeemedOfferProto)
	}
	return redeemedOfferProtos, nil
}

func (c *RedeemedOfferDaoImpl) Update(ctx context.Context, redeemedOffer *redemption.RedeemedOffer, fieldMask []redemption.RedeemedOfferFieldMask) error {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "Update", time.Now())
	updateColumns := getColumnsNames(ctx, fieldMask)
	if len(updateColumns) == 0 {
		return errors.New("no columns to update")
	}
	dbModel, err := convertToDBModel(ctx, redeemedOffer)
	if err != nil {
		return err
	}
	db := c.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	if err := db.Model(dbModel).Where("id = ?", redeemedOffer.Id).Select(updateColumns).Updates(dbModel).Error; err != nil {
		return errors.Wrap(err, "error updating redeemed offer")
	}
	return nil
}

func (c *RedeemedOfferDaoImpl) GetByActorId(ctx context.Context, actorId string) ([]*redemption.RedeemedOffer, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "GetByActorId", time.Now())
	var (
		offerModelList []*model.RedeemedOffer
		offerProtoList = []*redemption.RedeemedOffer{}
		err            error
	)
	if actorId == "" {
		return nil, errors.New("nil actor id")
	}
	db := c.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	if err = db.Find(&offerModelList, "actor_id = ?", actorId).Error; err != nil {
		return nil, errors.Wrap(err, "error fetching collected offers by actor id")
	}
	for _, offer := range offerModelList {
		var offerProto *redemption.RedeemedOffer
		if offerProto, err = convertToProtoModel(ctx, offer); err != nil {
			return nil, err
		}
		offerProtoList = append(offerProtoList, offerProto)
	}
	return offerProtoList, nil
}

// nolint:funlen
func (c *RedeemedOfferDaoImpl) GetByActorIdAndFilters(ctx context.Context, req *redemption.GetRedeemedOffersForActorRequest) ([]*redemption.RedeemedOffer, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "GetByActorIdAndFilters", time.Now())
	var (
		redeemedOffersModelList []*model.RedeemedOffer
		redeemedOffersProtoList = []*redemption.RedeemedOffer{}
		err                     error
	)

	if req.GetActorId() == "" {
		return nil, nil, fmt.Errorf("error fetch redeemed offers for actor, nil actor id")
	}

	db := c.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	query := db.Where("redeemed_offers.actor_id = ?", req.GetActorId())

	if req.GetFilters().GetRedeemedOfferId() != "" {
		query = query.Where("redeemed_offers.id = ?", req.GetFilters().GetRedeemedOfferId())
	}

	if req.GetFilters().GetOfferId() != "" {
		query = query.Where("redeemed_offers.offer_id = ?", req.GetFilters().GetOfferId())
	}

	if req.GetFilters().GetOfferType() != casperPb.OfferType_UNSPECIFIED_OFFER_TYPE {
		query = query.Where("redeemed_offers.offer_type = ?", req.GetFilters().GetOfferType())
	}

	// This filter is deprecated
	if req.GetFilters().GetRedemptionState() != redemption.OfferRedemptionState_OFFER_REDEMPTION_STATE_UNSPECIFIED {
		query = query.Where("redeemed_offers.redemption_state = ?", req.GetFilters().GetRedemptionState())
	}

	if len(req.GetFilters().GetRedemptionStates()) > 0 {
		query = query.Where("redeemed_offers.redemption_state in (?)", req.GetFilters().GetRedemptionStates())
	}

	if req.GetFilters().GetFromDate() != nil {
		query = query.Where("redeemed_offers.created_at >= ?", req.GetFilters().GetFromDate().AsTime())
	}

	if req.GetFilters().GetUptoDate() != nil {
		query = query.Where("redeemed_offers.created_at < ?", req.GetFilters().GetUptoDate().AsTime())
	}

	if req.GetFilters().GetVendor() != casperPb.OfferVendor_UNSPECIFIED {
		// todo (utkarsh) : add offer vendor column in redeemed offers
	}

	if req.GetFilters().GetExpiryStatus() != redemption.GetRedeemedOffersForActorRequest_Filters_UNSPECIFIED_EXPIRY_STATUS {
		switch req.GetFilters().GetExpiryStatus() {
		// fetch only expired redeemed offers
		case redemption.GetRedeemedOffersForActorRequest_Filters_EXPIRED:
			query = query.Where("redeemed_offers.offer_expires_at <= ?", time.Now())
		// fetch only active redeemed offers
		case redemption.GetRedeemedOffersForActorRequest_Filters_NOT_EXPIRED:
			query = query.Where("redeemed_offers.offer_expires_at > ? or redeemed_offers.offer_expires_at is NULL", time.Now())
		}
	}

	if len(req.GetFilters().GetOrOfferTags()) != 0 {
		var filterTagsString string
		for idx, tag := range req.GetFilters().GetOrOfferTags() {
			filterTagsString += "'" + tag.String() + "'"
			if idx != len(req.GetFilters().GetOrOfferTags())-1 {
				filterTagsString += ","
			}
		}
		query.Joins("INNER JOIN offers ON redeemed_offers.offer_id = offers.id").Where(fmt.Sprintf("offers.tags_info->'tags' ?| array[%s] OR offers.tags_info->'manualTags' ?| array[%s]", filterTagsString, filterTagsString))
	}

	pageSize := int(req.GetPageContext().GetPageSize())
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		return nil, nil, fmt.Errorf("error while getting page token from PageContext")
	}
	if pageToken != nil {
		if pageToken.Timestamp != nil {
			if pageToken.IsReverse {
				query = query.Where("redeemed_offers.created_at >= ?", pageToken.GetTimestamp().AsTime())
			} else {
				query = query.Where("redeemed_offers.created_at <= ?", pageToken.GetTimestamp().AsTime())
			}
		}
		query = query.Offset(int(pageToken.Offset))
	}

	// fetching 1 more record than in the page to populate if next page present or not
	if err = query.Order("redeemed_offers.created_at desc").Limit(pageSize + 1).Find(&redeemedOffersModelList).Error; err != nil {
		return nil, nil, fmt.Errorf("error fetching paginated active redeemed offers by actor id, err: %w", err)
	}

	rows, pageCtxRes, err := pagination.NewPageCtxResp(pageToken, pageSize, model.RedeemedOfferRows(redeemedOffersModelList))
	if err != nil {
		return nil, nil, fmt.Errorf("error while getting pageCtxRes, err: %w", err)
	}

	for _, offer := range rows.(model.RedeemedOfferRows) {
		var offerProto *redemption.RedeemedOffer
		if offerProto, err = convertToProtoModel(ctx, offer); err != nil {
			return nil, nil, err
		}
		redeemedOffersProtoList = append(redeemedOffersProtoList, offerProto)
	}

	return redeemedOffersProtoList, pageCtxRes, nil
}

func (c *RedeemedOfferDaoImpl) GetByStateAndOfferTypeWithinTimeRange(ctx context.Context, redemptionState redemption.OfferRedemptionState, offerType casperPb.OfferType, createdAtStartTime *time.Time, createdAtEndTime *time.Time) ([]*redemption.RedeemedOffer, error) {
	defer metric_util.TrackDuration("casper/redemption/dao", "RedeemedOfferDaoImpl", "GetByStateAndOfferTypeWithinTimeRange", time.Now())

	redeemedOfferModels := make([]*model.RedeemedOffer, 0)
	db := c.db.Session(&gormv2.Session{Context: epificontext.CloneCtx(ctx)})
	res := db.Where("redemption_state = ? AND offer_type = ? AND created_at >= ? AND created_at < ?", redemptionState, offerType, createdAtStartTime, createdAtEndTime).Find(&redeemedOfferModels)
	if res.Error != nil {
		return nil, fmt.Errorf("error fetching redeemed offers by state and offerType, err: %w", res.Error)
	}

	redeemedOffers := make([]*redemption.RedeemedOffer, 0)
	for _, redeemedOfferModel := range redeemedOfferModels {
		redeemedOfferProto, err := convertToProtoModel(ctx, redeemedOfferModel)
		if err != nil {
			return nil, err
		}
		redeemedOffers = append(redeemedOffers, redeemedOfferProto)
	}
	return redeemedOffers, nil
}

func getColumnsNames(_ context.Context, fieldMask []redemption.RedeemedOfferFieldMask) []string {
	var columns []string
	for _, field := range fieldMask {
		columns = append(columns, redeemedOfferColumnNames[field])
	}
	return columns
}

func convertToDBModel(_ context.Context, redeemedOffer *redemption.RedeemedOffer) (*model.RedeemedOffer, error) {
	redeemedOfferDbModel := &model.RedeemedOffer{
		RedemptionRequestId: redeemedOffer.RedemptionRequestId,
		ActorId:             redeemedOffer.ActorId,
		OfferId:             redeemedOffer.OfferId,
		OfferType:           redeemedOffer.OfferType,
		RedemptionState:     redeemedOffer.RedemptionState,
		RedemptionPrice:     redeemedOffer.RedemptionPrice,
	}

	// parsing expiry time from redeemed redeemedOffer proto.
	// redeemedOffer details structure differs based on redeemedOffer type.
	switch redeemedOffer.GetOfferType() {
	case casperPb.OfferType_GIFT_CARD:
		offerExpiryTimestamp := redeemedOffer.GetRedeemedOfferDetails().GetEgiftCardDetails().GetExpiryTime()
		if offerExpiryTimestamp != nil {
			redeemedOfferDbModel.OfferExpiresAt = aws.Time(offerExpiryTimestamp.AsTime())
		}
	case casperPb.OfferType_THRIWE_BENEFITS_PACKAGE:
		offerExpiryTimestamp := redeemedOffer.GetRedeemedOfferDetails().GetThriweBenefitsPackageDetails().GetExpiryTime()
		if offerExpiryTimestamp != nil {
			redeemedOfferDbModel.OfferExpiresAt = aws.Time(offerExpiryTimestamp.AsTime())
		}
	case casperPb.OfferType_VISTARA_AIR_MILES:
		offerExpiryTimestamp := redeemedOffer.GetRedeemedOfferDetails().GetVistaraAirMilesDetails().GetExpiryTime()
		if offerExpiryTimestamp != nil {
			redeemedOfferDbModel.OfferExpiresAt = aws.Time(offerExpiryTimestamp.AsTime())
		}
	case casperPb.OfferType_CLUB_ITC_GREEN_POINTS:
		offerExpiryTimestamp := redeemedOffer.GetRedeemedOfferDetails().GetClubItcGreenPointsDetails().GetExpiryTime()
		if offerExpiryTimestamp != nil {
			redeemedOfferDbModel.OfferExpiresAt = aws.Time(offerExpiryTimestamp.AsTime())
		}
	case casperPb.OfferType_CMS_COUPON:
		offerExpiryTimestamp := redeemedOffer.GetRedeemedOfferDetails().GetCmsCouponDetails().GetValidTill()
		if offerExpiryTimestamp != nil {
			redeemedOfferDbModel.OfferExpiresAt = aws.Time(offerExpiryTimestamp.AsTime())
		}
	case casperPb.OfferType_LOUNGE_ACCESS:
		offerExpiryTimestamp := redeemedOffer.GetRedeemedOfferDetails().GetLoungeAccessDetails().GetExpiryTime()
		if offerExpiryTimestamp != nil {
			redeemedOfferDbModel.OfferExpiresAt = aws.Time(offerExpiryTimestamp.AsTime())
		}
	default:
	}

	// marshalling redeemedOffer details into json
	if redeemedOffer.GetRedeemedOfferDetails() != nil {
		offerDetailsJson, err := protojson.Marshal(redeemedOffer.GetRedeemedOfferDetails())
		if err != nil {
			return nil, errors.Wrap(err, "error while marshalling collected redeemedOffer details")
		}
		offerDetailsString := string(offerDetailsJson)
		redeemedOfferDbModel.RedeemedOfferDetails = &offerDetailsString
	}

	return redeemedOfferDbModel, nil
}

func convertToProtoModel(_ context.Context, offer *model.RedeemedOffer) (*redemption.RedeemedOffer, error) {
	offerProto := &redemption.RedeemedOffer{
		Id:                  offer.ID,
		ExternalId:          offer.ExternalId,
		RedemptionRequestId: offer.RedemptionRequestId,
		ActorId:             offer.ActorId,
		OfferId:             offer.OfferId,
		OfferType:           offer.OfferType,
		RedemptionState:     offer.RedemptionState,
		RedemptionPrice:     offer.RedemptionPrice,
		CreatedAt:           timestampPb.New(offer.CreatedAt),
		UpdatedAt:           timestampPb.New(offer.UpdatedAt),
	}

	if offer.RedeemedOfferDetails != nil {
		offerDetailsProto := &redemption.RedeemedOffer_RedeemedOfferDetails{}
		err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(*offer.RedeemedOfferDetails), offerDetailsProto)
		if err != nil {
			return nil, errors.Wrap(err, "error while unmarshalling redeemed offer details")
		}
		offerProto.RedeemedOfferDetails = offerDetailsProto
	}
	if offer.OfferExpiresAt != nil && !offer.OfferExpiresAt.IsZero() {
		offerProto.ExpiresAt = timestampPb.New(*offer.OfferExpiresAt)
	}

	return offerProto, nil
}
