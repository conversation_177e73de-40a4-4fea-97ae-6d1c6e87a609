package dao_test

import (
	"context"
	"errors"
	"net"
	"testing"

	"github.com/epifi/gamma/api/vendordata/ip"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/vendordata/ip/dao"
	"github.com/epifi/gamma/vendordata/ip/dao/model"

	"github.com/google/go-cmp/cmp"
	"go.uber.org/zap"
	"google.golang.org/protobuf/testing/protocmp"
	"gorm.io/gorm"
)

var (
	geoIpNetworkTestSuite *GeoIpNetworkTestSuite
)

func TestGeoIpNetworkDaoPGDB_Create(t *testing.T) {
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(geoIpNetworkTestSuite.conf.VendorDataDb, false)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}
	testDao := geoIpNetworkTestSuite.getDao(db)
	if err = populateNetworkFixtures(testDao); err != nil {
		t.Errorf("Error populating fixtures for test: %#v", err)
	}

	tests := []struct {
		name    string
		req     *ip.GeoIpNetwork
		want    *ip.GeoIpNetwork
		wantErr error
	}{
		{
			name: "successful creation",
			req: &ip.GeoIpNetwork{
				Network:    "*******",
				GeonameId:  12345,
				PostalCode: "123456",
			},
			want: &ip.GeoIpNetwork{
				Network:    "*******",
				GeonameId:  12345,
				PostalCode: "123456",
			},
			wantErr: nil,
		},
		{
			name: "duplicate entry",
			req: &ip.GeoIpNetwork{
				Network:    "*******",
				GeonameId:  223311,
				PostalCode: "223311",
			},
			want:    nil,
			wantErr: epifierrors.ErrDuplicateEntry,
		},
		{
			name: "necessary constraints not mentioned",
			req: &ip.GeoIpNetwork{
				Network: "*******",
			},
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err = testDao.Create(context.Background(), tt.req); !errors.Is(err, tt.wantErr) {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr == nil {
				got := getGeoNetworkDataByIp(db, tt.req.GetNetwork())
				if !compareGeoNetwork(got, tt.want) {
					t.Errorf("Create() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestGeoIpNetworkDaoPGDB_GetByIp(t *testing.T) {
	db, _, cleanup, err := pkgTest.PrepareRandomScopedRdsTestDb(geoIpNetworkTestSuite.conf.VendorDataDb, false)
	t.Cleanup(cleanup)
	if err != nil {
		t.Errorf("Error creating db for test: %#v", err)
	}
	testDao := geoIpNetworkTestSuite.getDao(db)
	if err = populateNetworkFixtures(testDao); err != nil {
		t.Errorf("Error populating fixtures for test: %#v", err)
	}

	tests := []struct {
		name    string
		ip      string
		want    *ip.GeoIpNetwork
		wantErr error
	}{
		{
			name: "successfully fetched subnet match",
			ip:   "*******/32",
			want: &ip.GeoIpNetwork{
				Network:    "*******/32",
				GeonameId:  123456,
				PostalCode: "530649",
			},
			wantErr: nil,
		},
		{
			name: "successfully fetched ip in subnet",
			ip:   "*******",
			want: &ip.GeoIpNetwork{
				Network:    "*******/24",
				GeonameId:  123566,
				PostalCode: "351987",
			},
			wantErr: nil,
		},
		{
			name:    "record not found",
			ip:      "*******",
			want:    nil,
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name:    "ip not mentioned",
			ip:      "",
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Run(tt.name, func(t *testing.T) {
				got, getErr := testDao.GetByIp(context.Background(), tt.ip)
				if !errors.Is(getErr, tt.wantErr) {
					t.Errorf("GetByIp() error = %v, wantErr %v", getErr, tt.wantErr)
				}
				if tt.wantErr == nil {
					if !compareGeoNetwork(got, tt.want) {
						t.Errorf("GetByIp() got = %v, want %v", got, tt.want)
					}
				}
			})
		})
	}
}

func populateNetworkFixtures(testDao dao.GeoIpNetworkDao) error {
	if err := testDao.Create(context.Background(), &ip.GeoIpNetwork{
		Network:    "*******",
		GeonameId:  223311,
		PostalCode: "223311",
	}); err != nil {
		return err
	}
	if err := testDao.Create(context.Background(), &ip.GeoIpNetwork{
		Network:    "*******/32",
		GeonameId:  123456,
		PostalCode: "530649",
	}); err != nil {
		return err
	}
	if err := testDao.Create(context.Background(), &ip.GeoIpNetwork{
		Network:    "*******/24",
		GeonameId:  123566,
		PostalCode: "351987",
	}); err != nil {
		return err
	}
	return nil
}

func getGeoNetworkDataByIp(db *gorm.DB, ip string) *ip.GeoIpNetwork {
	var mod *model.GeoIpNetwork
	if err := db.Where("network >>= ?", ip).Take(&mod).Error; err != nil {
		logger.ErrorNoCtx("error while getting", zap.Error(err))
		return nil
	}
	return mod.ToProto()
}

func compareGeoNetwork(got, want *ip.GeoIpNetwork) bool {
	opts := []cmp.Option{
		protocmp.Transform(),
		protocmp.IgnoreFields(&ip.GeoIpNetwork{}, "network"),
	}
	if diff := cmp.Diff(got, want, opts...); diff != "" {
		return false
	}
	ip1, _, err := net.ParseCIDR(got.GetNetwork())
	if err != nil {
		return false
	}
	var ip2 net.IP
	ip2, _, err = net.ParseCIDR(want.GetNetwork())
	if err != nil {
		logger.InfoNoCtx("using network as ip")
		ip2 = net.ParseIP(want.GetNetwork())
	}
	return ip2.Equal(ip1)
}
