package omegle

import (
	"context"

	dbStatePb "github.com/epifi/be-common/api/sherlock/dev/dbstate"

	devOmeglePb "github.com/epifi/gringott/api/external/omegle/developer"
	devVkycCallPb "github.com/epifi/gringott/api/external/vkyccall/developer"
)

type Service struct {
	devOmegleClient devOmeglePb.DevOmegleClient
}

func NewService(devOmegleClient devOmeglePb.DevOmegleClient) *Service {
	return &Service{
		devOmegleClient: devOmegleClient,
	}
}

var _ devVkycCallPb.DevVkycCallServer = (*Service)(nil)

func (s *Service) GetEntityList(ctx context.Context, req *dbStatePb.GetEntityListRequest) (*dbStatePb.GetEntityListResponse, error) {
	return s.devOmegleClient.GetEntityList(ctx, req)
}

func (s *Service) GetParameterList(ctx context.Context, req *dbStatePb.GetParameterListRequest) (*dbStatePb.GetParameterListResponse, error) {
	return s.devOmegleClient.GetParameterList(ctx, req)
}

func (s *Service) GetData(ctx context.Context, req *dbStatePb.GetDataRequest) (*dbStatePb.GetDataResponse, error) {
	return s.devOmegleClient.GetData(ctx, req)
}
