package application

import (
	"fmt"

	gwApplicationPb "github.com/epifi/gringott/api/stockguardian/sgapigateway/application"

	applicationEnumsPb "github.com/epifi/gringott/api/stockguardian/sgapplication/enums"
)

func convertLoanApplicationStageStatus(status applicationEnumsPb.LoanApplicationStageStatus) (gwApplicationPb.LoanApplicationStageStatus, error) {
	switch status {
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CREATED:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CREATED, nil
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_IN_PROGRESS:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_IN_PROGRESS, nil
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_EXPIRED:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_EXPIRED, nil
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_SUCCESS:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_SUCCESS, nil
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_FAILED:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_FAILED, nil
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CANCELLED:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CANCELLED, nil
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_MANUAL_INTERVENTION:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_MANUAL_INTERVENTION, nil
	case applicationEnumsPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED:
		return gwApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED, nil
	default:
		return 0, fmt.Errorf("unknown LoanApplicationStageStatus: %v", status)
	}
}

func convertLoanApplicationStageName(stageName applicationEnumsPb.LoanApplicationStageName) (gwApplicationPb.LoanApplicationStageName, error) {
	switch stageName {
	case applicationEnumsPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_KYC:
		return gwApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_KYC, nil
	case applicationEnumsPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_OFFER_GENERATION:
		return gwApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_OFFER_GENERATION, nil
	case applicationEnumsPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_DRAWDOWN:
		return gwApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_DRAWDOWN, nil
	case applicationEnumsPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_MANDATE:
		return gwApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_MANDATE, nil
	case applicationEnumsPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_ESIGN:
		return gwApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_ESIGN, nil
	case applicationEnumsPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_DISBURSEMENT:
		return gwApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_DISBURSEMENT, nil
	case applicationEnumsPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_PENNY_DROP:
		return gwApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_PENNY_DROP, nil
	default:
		return 0, fmt.Errorf("unknown LoanApplicationStageName: %v", stageName)
	}
}

func GetAppMgmtConsentType(consentType gwApplicationPb.ConsentType) (applicationEnumsPb.ConsentType, error) {
	switch consentType {
	case gwApplicationPb.ConsentType_CONSENT_TYPE_CKYC_DOWNLOAD:
		return applicationEnumsPb.ConsentType_CONSENT_TYPE_CKYC_DOWNLOAD, nil
	case gwApplicationPb.ConsentType_CONSENT_TYPE_CREDIT_REPORT_HARD_PULL:
		return applicationEnumsPb.ConsentType_CONSENT_TYPE_CREDIT_REPORT_HARD_PULL, nil
	case gwApplicationPb.ConsentType_CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS:
		return applicationEnumsPb.ConsentType_CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS, nil
	case gwApplicationPb.ConsentType_CONSENT_TYPE_UNSPECIFIED:
		return applicationEnumsPb.ConsentType_CONSENT_TYPE_UNSPECIFIED, nil
	default:
		return 0, fmt.Errorf("unsupported consent type: %s", consentType)
	}
}
