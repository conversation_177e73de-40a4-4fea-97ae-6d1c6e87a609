package names

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"
)

func TestNameMatch(t *testing.T) {
	t.<PERSON>()

	type args struct {
		baseName       *commontypes.Name
		challengerName *commontypes.Name
	}
	tests := []struct {
		name    string
		args    args
		want    *MatchResult
		wantErr bool
	}{
		{
			name: "shortened middle name",
			args: args{
				baseName: &commontypes.Name{
					FirstName:  "Sachin",
					MiddleName: "R",
					LastName:   "Tendulkar",
				},
				challengerName: &commontypes.Name{
					FirstName:  "Sachin",
					MiddleName: "<PERSON>esh",
					LastName:   "Tendulkar",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    0,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    1,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    3,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    1,
					},
				},
				WeightedSumScore: 18,
			},
		},
		{
			name: "spilled words: mid in last name",
			args: args{
				baseName: &commontypes.Name{
					FirstName:  "Sachin",
					MiddleName: "Ramesh",
					LastName:   "Tendulkar",
				},
				challengerName: &commontypes.Name{
					FirstName:  "Sachin",
					MiddleName: "",
					LastName:   "Ramesh Tendulkar",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    1,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    1,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    3,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    1,
					},
				},
				WeightedSumScore: 28,
			},
		},
		{
			name: "reverse name",
			args: args{
				baseName: &commontypes.Name{
					FirstName: "Sachin",
					LastName:  "Tendulkar",
				},
				challengerName: &commontypes.Name{
					FirstName: "Tendulkar",
					LastName:  "Sachin",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    0,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    1,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    2,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    1,
					},
				},
				WeightedSumScore: 17,
			},
		},
		{
			name: "diff in name length",
			args: args{
				baseName: &commontypes.Name{
					FirstName:  "NUKALA",
					MiddleName: "MACHARA VEERA VENKATA SATYANARAYANA",
					LastName:   "MURTHY",
				},
				challengerName: &commontypes.Name{
					FirstName:  "MVVS",
					MiddleName: "MURTHY",
					LastName:   "NUKALA",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    0,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    2,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    0,
					},
				},
				WeightedSumScore: 2,
			},
		},
		{
			name: "initals only",
			args: args{
				baseName: &commontypes.Name{
					FirstName:  "Tamil",
					MiddleName: "Selvan R",
					LastName:   "S",
				},
				challengerName: &commontypes.Name{
					FirstName: "R",
					LastName:  "S",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    0,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    0,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    0,
					},
				},
				WeightedSumScore: 0,
			},
		},
		{
			name: "string existing in concatenated name",
			args: args{
				baseName: &commontypes.Name{
					FirstName:  "PRAMOD",
					MiddleName: "PRUTHVI",
					LastName:   "RAJ",
				},
				challengerName: &commontypes.Name{
					FirstName:  "PRUTHVIRAJ",
					MiddleName: "",
					LastName:   "P",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    0,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    0,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    1,
					},
				},
				WeightedSumScore: 7,
			},
		},
		{
			name: "existing in concatenated name along with missing string",
			args: args{
				baseName: &commontypes.Name{
					FirstName:  "PRAMOD",
					MiddleName: "PRUTHVI",
					LastName:   "RAJ",
				},
				challengerName: &commontypes.Name{
					FirstName:  "PRAMOD",
					MiddleName: "",
					LastName:   "PRUTHVI",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    0,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    2,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    1,
					},
				},
				WeightedSumScore: 9,
			},
		},
		{
			name: "string existing in concatenated name and initial",
			args: args{
				baseName: &commontypes.Name{
					FirstName:  "P",
					MiddleName: "PRUTHVI",
					LastName:   "RAJ",
				},
				challengerName: &commontypes.Name{
					FirstName:  "PRUTHVIRAJ",
					MiddleName: "",
					LastName:   "PRAMOD",
				},
			},
			want: &MatchResult{
				Scores: []*MatchScore{
					{
						Criteria: FULL_NAME_MATCH,
						Weight:   10,
						Score:    0,
					},
					{
						Criteria: FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: REVERSE_FIRST_LAST_MATCH,
						Weight:   8,
						Score:    0,
					},
					{
						Criteria: SHORT_NAME_MATCH,
						Weight:   1,
						Score:    0,
					},
					{
						Criteria: CONCAT_MATCH,
						Weight:   7,
						Score:    1,
					},
				},
				WeightedSumScore: 7,
			},
		},
	}
	for _, tt := range tests {
		// tt is mutated on each iteration, so keep a copy of it
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := NameMatchAllCriteria(context.Background(), tt.args.baseName, tt.args.challengerName)
			if (err != nil) != tt.wantErr {
				t.Errorf("NameMatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NameMatch()\n got = %v\nwant = %v", got, tt.want)
			}
		})
	}
}

func TestParseStringWithPrefix(t *testing.T) {
	t.Parallel()
	type args struct {
		name string
	}
	tests := []struct {
		name string
		args args
		want *commontypes.Name
	}{
		{
			name: "with prefix without dot",
			args: args{
				name: "Mr Byomkesh Bakshi",
			},
			want: &commontypes.Name{
				FirstName: "BYOMKESH",
				LastName:  "BAKSHI",
			},
		},
		{
			name: "with prefix with dot",
			args: args{
				name: "M/s. Byomkesh Bakshi",
			},
			want: &commontypes.Name{
				FirstName: "BYOMKESH",
				LastName:  "BAKSHI",
			},
		},
		{
			name: "without prefix",
			args: args{
				name: "Byomkesh Bakshi",
			},
			want: &commontypes.Name{
				FirstName: "BYOMKESH",
				LastName:  "BAKSHI",
			},
		},
	}
	for _, tt := range tests {
		// tt is mutated on each iteration, so keep a copy of it
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			if got := ParseStringWithPrefix(tt.args.name); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseStringWithPrefix() = %v, want %v", got, tt.want)
			}
		})
	}
}
