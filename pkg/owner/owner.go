package owner

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"gorm.io/gorm"

	commonTypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
)

func GetOwnerFromOwnership(ownership commonTypes.Ownership) (commonTypes.Owner, error) {
	// check if in case ownership is added but owner is not added, in this case, it will try to find in an out of bound index
	if len(commonTypes.Owner_name) <= int(ownership) {
		return commonTypes.Owner_OWNER_UNSPECIFIED, fmt.Errorf("owner not added when added in ownership: %s", ownership.String())
	}
	owner := commonTypes.Owner(ownership + 1)
	if ownership.String() != owner.String()[6:] {
		return commonTypes.Owner_OWNER_UNSPECIFIED, fmt.Errorf("owner and ownership does not match: Ownership: %s, Owner: %s", ownership.String(), owner.String())
	}
	return owner, nil
}

func GetOwnershipFromOwner(owner commonTypes.Owner) (commonTypes.Ownership, error) {
	// check if in case owner is added but ownership is not added, in this case, it will try to find in an out of bound index
	// check if in case owner passed is unspecified i.e. 0, in this case, it will try to find in -1 index, which is not allowed
	if len(commonTypes.Ownership_name) < int(owner) || int(owner) < 1 {
		return commonTypes.Ownership_EPIFI_TECH, fmt.Errorf("ownership not added when added in owner: %s", owner.String())
	}
	ownership := commonTypes.Ownership(owner - 1)
	if ownership.String() != owner.String()[6:] {
		return commonTypes.Ownership_EPIFI_TECH, fmt.Errorf("owner and ownership does not match: Ownership: %s, Owner: %s", ownership.String(), owner.String())
	}
	return ownership, nil
}

// GetDbFromContextOrProvider checks if there is db conn passed in context then it will return the same db
// else it will try to create new from the owner and will return the same
func GetDbFromContextOrProvider(ctx context.Context, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], owner commonTypes.Owner) (*gorm.DB, error) {
	if owner == commonTypes.Owner_OWNER_UNSPECIFIED {
		return nil, errors.New("invalid owner")
	}
	ownership, err := GetOwnershipFromOwner(owner)
	if err != nil {
		return nil, fmt.Errorf("failed to get ownership for owner: %s", owner)
	}
	db, err := dbResourceProvider.GetResourceForOwnership(ownership)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("failed to get db from ownership: %s", owner.String()))
	}
	return gormctxv2.FromContextOrDefault(ctx, db), nil
}
