package monitoring

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

// KibanaMonitor is a function prototype that can be used to define a log monitor for kibana.
// To add monitoring for a log statement, use either KibanaErrorMonitor or KibanaInfoMonitor to log.
// Then add a monitoring rule in Kibana for "PREFIX ALERT:"
// These should not be used for every log, but only in case you want to establish some form of monitoring on unexpected code flows.
// Using this monitor allows to aggregate all the monitoring alerts for a prefix together.
type KibanaMonitor func(ctx context.Context, prefix string, logStr string, zapFields ...zap.Field)

func KibanaErrorMonitor(ctx context.Context, prefix string, errorLog string, zapFields ...zap.Field) {
	logger.Error(ctx, fmt.Sprintf("%v ALERT: %v", strings.ToUpper(prefix), errorLog), zapFields...)
}

func KibanaInfoMonitor(ctx context.Context, prefix string, infoLog string, zapFields ...zap.Field) {
	logger.Info(ctx, fmt.Sprintf("%v ALERT: %v", strings.ToUpper(prefix), infoLog), zapFields...)
}

// KibanaServiceMonitor is a function prototype that can be used to define a log monitor for kibana.
// To add monitoring for a log statement, use either KibanaErrorServiceMonitor or KibanaInfoServiceMonitor to log.
// Then add a monitoring rule in Kibana for "SERVICE_NAME ALERT:"
// These should not be used for every log, but only in case you want to establish some form of monitoring on unexpected code flows.
// Using this monitor allows to aggregate all the monitoring alerts for a service together.
type KibanaServiceMonitor func(ctx context.Context, svcName cfg.ServiceName, logStr string, zapFields ...zap.Field)

func KibanaErrorServiceMonitor(ctx context.Context, svcName cfg.ServiceName, errorLog string, zapFields ...zap.Field) {
	logger.Error(ctx, fmt.Sprintf("%v ALERT: %v", strings.ToUpper(string(svcName)), errorLog), zapFields...)
}

func KibanaInfoServiceMonitor(ctx context.Context, svcName cfg.ServiceName, infoLog string, zapFields ...zap.Field) {
	logger.Info(ctx, fmt.Sprintf("%v ALERT: %v", strings.ToUpper(string(svcName)), infoLog), zapFields...)
}
