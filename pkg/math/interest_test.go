package math

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestCalculateInterestRate(t *testing.T) {
	tests := []struct {
		name    string
		req     *CalculateInterestRateRequest
		want    decimal.Decimal
		wantErr bool
	}{
		{
			name: "valid input",
			req: &CalculateInterestRateRequest{
				Contribution: decimal.NewFromInt(1000),
				FutureValue:  decimal.NewFromInt(2000),
				NumPeriods:   10,
			},
			want:    decimal.NewFromFloat(0.0717734625362931),
			wantErr: false,
		},
		{
			name: "zero contribution",
			req: &CalculateInterestRateRequest{
				Contribution: decimal.Zero,
				FutureValue:  decimal.NewFromInt(2000),
				NumPeriods:   10,
			},
			want:    decimal.Zero,
			wantErr: true,
		},
		{
			name: "zero future value",
			req: &CalculateInterestRateRequest{
				Contribution: decimal.NewFromInt(1000),
				FutureValue:  decimal.Zero,
				NumPeriods:   10,
			},
			want:    decimal.Zero,
			wantErr: true,
		},
		{
			name: "non-positive number of periods",
			req: &CalculateInterestRateRequest{
				Contribution: decimal.NewFromInt(1000),
				FutureValue:  decimal.NewFromInt(2000),
				NumPeriods:   0,
			},
			want:    decimal.Zero,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CalculateInterestRate(tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.True(t, got.Round(10).Equal(tt.want.Round(10)), "got: %v, want: %v", got, tt.want)
			}
		})
	}
}

func TestCalculateFutureValueLumpSum(t *testing.T) {
	tests := []struct {
		name    string
		req     *CalculateFutureValueRequest
		want    decimal.Decimal
		wantErr bool
	}{
		{
			name: "valid input",
			req: &CalculateFutureValueRequest{
				Contribution: decimal.NewFromInt(1000),
				Rate:         decimal.NewFromFloat(0.05),
				NumPeriods:   10,
			},
			want:    decimal.NewFromFloat(1628.894626777442),
			wantErr: false,
		},
		{
			name: "zero contribution",
			req: &CalculateFutureValueRequest{
				Contribution: decimal.Zero,
				Rate:         decimal.NewFromFloat(0.05),
				NumPeriods:   10,
			},
			want:    decimal.Zero,
			wantErr: true,
		},
		{
			name: "non-positive number of periods",
			req: &CalculateFutureValueRequest{
				Contribution: decimal.NewFromInt(1000),
				Rate:         decimal.NewFromFloat(0.05),
				NumPeriods:   0,
			},
			want:    decimal.Zero,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CalculateFutureValueLumpSum(tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.True(t, got.Round(10).Equal(tt.want.Round(10)), "got: %v, want: %v", got, tt.want)
			}
		})
	}
}

func TestCalculateFutureValueRecurring(t *testing.T) {
	tests := []struct {
		name    string
		req     *CalculateFutureValueRequest
		want    decimal.Decimal
		wantErr bool
	}{
		{
			name: "valid input",
			req: &CalculateFutureValueRequest{
				Contribution: decimal.NewFromInt(1000),
				Rate:         decimal.NewFromFloat(0.05),
				NumPeriods:   10,
			},
			want:    decimal.NewFromFloat(13206.7871623262695313),
			wantErr: false,
		},
		{
			name: "zero contribution",
			req: &CalculateFutureValueRequest{
				Contribution: decimal.Zero,
				Rate:         decimal.NewFromFloat(0.05),
				NumPeriods:   10,
			},
			want:    decimal.Zero,
			wantErr: true,
		},
		{
			name: "non-positive number of periods",
			req: &CalculateFutureValueRequest{
				Contribution: decimal.NewFromInt(1000),
				Rate:         decimal.NewFromFloat(0.05),
				NumPeriods:   0,
			},
			want:    decimal.Zero,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CalculateFutureValueRecurring(tt.req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.True(t, got.Round(10).Equal(tt.want.Round(10)), "got: %v, want: %v", got, tt.want)
			}
		})
	}
}
