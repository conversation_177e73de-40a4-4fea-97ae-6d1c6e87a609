package sourceandintent

// AcquisitionIntent : type for maintaining intent of the user coming on Fi
type AcquisitionIntent string

// AcquisitionSource : type for maintaining source of the user coming on Fi
type AcquisitionSource string

// DecryptionKeys type houses the secrets/keys required for decrypting attribution details
type DecryptionKeys struct {
	FacebookInstallReferrerDecryptionKey string
}
