package ec2V2

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/elasticloadbalancingv2"
	"github.com/aws/aws-sdk-go-v2/service/elasticloadbalancingv2/types"
)

type CreateTargetGroupInput struct {
	Name  string
	VpcId string
	Port  int
}

func (p *Processor) CreateTargetGroup(input *CreateTargetGroupInput) (string, error) {
	ctx := context.Background()
	out, err := p.elb.CreateTargetGroup(ctx, &elasticloadbalancingv2.CreateTargetGroupInput{
		Name:               aws.String(input.Name),
		HealthCheckEnabled: aws.Bool(true),
		//HealthCheckPort:           aws.String(strconv.Itoa(input.HealthCheckPort)),
		//HealthCheckProtocol: types.ProtocolEnumTcp,
		Port:       aws.Int32(int32(input.Port)),
		TargetType: types.TargetTypeEnumInstance,
		Protocol:   types.ProtocolEnumTcp,
		//Tags:                []types.Tag{},
		VpcId: &input.VpcId,
	})
	if err != nil {
		return "", err
	}
	return *out.TargetGroups[0].TargetGroupArn, nil
}
