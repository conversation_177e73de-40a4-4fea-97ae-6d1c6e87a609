package firefly

import (
	"reflect"
	"testing"
	"time"

	datePb "google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	ffBillingPb "github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/be-common/pkg/datetime"
)

func TestGetBillingWindowFromTime(t *testing.T) {
	fifthFebTime := datetime.DateToTime(&datePb.Date{
		Year:  2022,
		Month: 2,
		Day:   5,
	}, datetime.IST)

	thirdFebTime := datetime.DateToTime(&datePb.Date{
		Year:  2022,
		Month: 2,
		Day:   3,
	}, datetime.IST)

	secondJanTime := datetime.DateToTime(&datePb.Date{
		Year:  2022,
		Month: 1,
		Day:   2,
	}, datetime.IST)

	twentySecondFeb := datetime.DateToTime(&datePb.Date{
		Year:  2022,
		Month: 2,
		Day:   22,
	}, datetime.IST)

	tests := []struct {
		name              string
		billGenerationDay int32
		timeToFetch       time.Time
		fromDate          *datePb.Date
		toDate            *datePb.Date
		fromTimeStamp     *timestampPb.Timestamp
		toTimeStamp       *timestampPb.Timestamp
	}{
		{
			name:              "bill generation day as 4th and time as 5th Feb",
			billGenerationDay: 4,
			timeToFetch:       *fifthFebTime,
			fromDate: &datePb.Date{
				Year:  2022,
				Month: 2,
				Day:   4,
			},
			toDate: &datePb.Date{
				Year:  2022,
				Month: 3,
				Day:   3,
			},
			fromTimeStamp: timestampPb.New(*datetime.DateToTime(&datePb.Date{
				Year:  2022,
				Month: 2,
				Day:   4,
			}, datetime.IST)),
			toTimeStamp: timestampPb.New(datetime.DateToTime(&datePb.Date{
				Year:  2022,
				Month: 3,
				Day:   3,
			}, datetime.IST).Add(23*time.Hour + 59*time.Minute + 59*time.Second)),
		},
		{
			name:              "bill generation day as 4th and time as 3rd Feb",
			billGenerationDay: 4,
			timeToFetch:       *thirdFebTime,
			fromDate: &datePb.Date{
				Year:  2022,
				Month: 1,
				Day:   4,
			},
			toDate: &datePb.Date{
				Year:  2022,
				Month: 2,
				Day:   3,
			},
			fromTimeStamp: timestampPb.New(*datetime.DateToTime(&datePb.Date{
				Year:  2022,
				Month: 1,
				Day:   4,
			}, datetime.IST)),
			toTimeStamp: timestampPb.New(datetime.DateToTime(&datePb.Date{
				Year:  2022,
				Month: 2,
				Day:   3,
			}, datetime.IST).Add(23*time.Hour + 59*time.Minute + 59*time.Second)),
		},
		{
			name:              "bill generation date as 5th and time as 2nd Jan",
			billGenerationDay: 5,
			timeToFetch:       *secondJanTime,
			fromDate: &datePb.Date{
				Year:  2021,
				Month: 12,
				Day:   5,
			},
			toDate: &datePb.Date{
				Year:  2022,
				Month: 1,
				Day:   4,
			},
			fromTimeStamp: timestampPb.New(*datetime.DateToTime(&datePb.Date{
				Year:  2021,
				Month: 12,
				Day:   5,
			}, datetime.IST)),
			toTimeStamp: timestampPb.New(datetime.DateToTime(&datePb.Date{
				Year:  2022,
				Month: 1,
				Day:   4,
			}, datetime.IST).Add(23*time.Hour + 59*time.Minute + 59*time.Second)),
		},
		{
			name:              "bill generation as 22nd and time as 22nd Feb",
			billGenerationDay: 22,
			timeToFetch:       *twentySecondFeb,
			fromDate: &datePb.Date{
				Year:  2022,
				Month: 2,
				Day:   22,
			},
			toDate: &datePb.Date{
				Year:  2022,
				Month: 3,
				Day:   21,
			},
			fromTimeStamp: timestampPb.New(*datetime.DateToTime(&datePb.Date{
				Year:  2022,
				Month: 2,
				Day:   22,
			}, datetime.IST)),
			toTimeStamp: timestampPb.New(datetime.DateToTime(&datePb.Date{
				Year:  2022,
				Month: 3,
				Day:   21,
			}, datetime.IST).Add(23*time.Hour + 59*time.Minute + 59*time.Second)),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3 := GetBillingWindowFromTime(tt.billGenerationDay, tt.timeToFetch)
			if !reflect.DeepEqual(got, tt.fromDate) {
				t.Errorf("GetBillingWindow() got = %v, want %v", got, tt.fromDate)
			}
			if !reflect.DeepEqual(got1, tt.toDate) {
				t.Errorf("GetBillingWindow() got1 = %v, want %v", got1, tt.toDate)
			}
			if !reflect.DeepEqual(got2, tt.fromTimeStamp) {
				t.Errorf("GetBillingWindow() got2 = %v, want %v", got, tt.fromTimeStamp)
			}
			if !reflect.DeepEqual(got3, tt.toTimeStamp) {
				t.Errorf("GetBillingWindow() got3 = %v, want %v", got1, tt.toTimeStamp)
			}
		})
	}
}

func TestGetBillingWindowAsString(t *testing.T) {
	type args struct {
		billWindow *ffBillingPb.BillWindow
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Bill window : 4th may 2023 - 3rd june 2023",
			args: args{
				billWindow: &ffBillingPb.BillWindow{
					FromTimestamp: &timestampPb.Timestamp{
						Seconds: 1683158400,
						Nanos:   0,
					},
					ToTimestamp: &timestampPb.Timestamp{
						Seconds: 1685750400,
						Nanos:   0,
					},
				},
			},
			want: "04_May_2023-03_Jun_2023",
		},
		{
			name: "Bill window : 22nd may 2023 - 21st june 2023",
			args: args{
				billWindow: &ffBillingPb.BillWindow{
					FromTimestamp: &timestampPb.Timestamp{
						Seconds: **********,
						Nanos:   0,
					},
					ToTimestamp: &timestampPb.Timestamp{
						Seconds: **********,
						Nanos:   0,
					},
				},
			},
			want: "22_May_2023-21_Jun_2023",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetBillingWindowAsString(tt.args.billWindow); got != tt.want {
				t.Errorf("GetBillingWindowAsString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetAccountIdAndAccountTypeFromDerivedAccountId(t *testing.T) {
	type args struct {
		derivedAccId string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		want1   enums.PaymentAccountType
		wantErr bool
	}{
		{
			name: "Successful conversion for third party account",
			args: args{
				derivedAccId: "GiRkZjc4MWE0Zi0xOWJkLTQ0MzUtOWMxZS03OGJlOWE0OTBmOTc=",
			},
			want:    "df781a4f-19bd-4435-9c1e-78be9a490f97",
			want1:   enums.PaymentAccountType_PAYMENT_ACCOUNT_TYPE_THIRD_PARTY_ACCOUNT,
			wantErr: false,
		},
		{
			name: "Successful fetch for an internal account",
			args: args{
				derivedAccId: "CiRkZjc4MWE0Zi0xOWJkLTQ0MzUtOWMxZS03OGJlOWE0OTBmOTc=",
			},
			want:    "df781a4f-19bd-4435-9c1e-78be9a490f97",
			want1:   enums.PaymentAccountType_PAYMENT_ACCOUNT_TYPE_FI_SAVINGS_ACCOUNT,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := GetAccountIdAndAccountTypeFromDerivedAccountId(tt.args.derivedAccId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountIdAndAccountTypeFromDerivedAccountId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetAccountIdAndAccountTypeFromDerivedAccountId() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetAccountIdAndAccountTypeFromDerivedAccountId() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestGetWeekendsInRange(t *testing.T) {
	type args struct {
		start time.Time
		end   time.Time
	}
	tests := []struct {
		name string
		args args
		want [][2]time.Time
	}{
		{
			name: "Range with multiple weekends",
			args: args{
				start: time.Date(2023, 5, 1, 0, 0, 0, 0, datetime.IST),
				end:   time.Date(2023, 5, 31, 23, 59, 59, 0, datetime.IST),
			},
			want: [][2]time.Time{
				{time.Date(2023, 5, 6, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 7, 23, 59, 59, *********, datetime.IST)},
				{time.Date(2023, 5, 13, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 14, 23, 59, 59, *********, datetime.IST)},
				{time.Date(2023, 5, 20, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 21, 23, 59, 59, *********, datetime.IST)},
				{time.Date(2023, 5, 27, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 28, 23, 59, 59, *********, datetime.IST)},
			},
		},
		{
			name: "Range starting on Saturday and ending Sunday",
			args: args{
				start: time.Date(2023, 5, 6, 0, 0, 0, 0, datetime.IST),
				end:   time.Date(2023, 5, 7, 23, 59, 59, 0, datetime.IST),
			},
			want: [][2]time.Time{
				{time.Date(2023, 5, 6, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 7, 23, 59, 59, *********, datetime.IST)},
			},
		},
		{
			name: "Range starting on Saturday and ending Saturday",
			args: args{
				start: time.Date(2023, 5, 6, 0, 0, 0, 0, datetime.IST),
				end:   time.Date(2023, 5, 6, 23, 59, 59, 0, datetime.IST),
			},
			want: [][2]time.Time{
				{time.Date(2023, 5, 6, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 6, 23, 59, 59, *********, datetime.IST)},
			},
		},
		{
			name: "Range starting on Sunday",
			args: args{
				start: time.Date(2023, 5, 7, 0, 0, 0, 0, datetime.IST),
				end:   time.Date(2023, 5, 7, 23, 59, 59, 0, datetime.IST),
			},
			want: [][2]time.Time{
				{time.Date(2023, 5, 7, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 7, 23, 59, 59, *********, datetime.IST)},
			},
		},
		{
			name: "Range with no weekends",
			args: args{
				start: time.Date(2023, 5, 8, 0, 0, 0, 0, datetime.IST),
				end:   time.Date(2023, 5, 12, 23, 59, 59, 0, datetime.IST),
			},
			want: [][2]time.Time{},
		},
		{
			name: "Range that spans one weekend",
			args: args{
				start: time.Date(2023, 5, 5, 0, 0, 0, 0, datetime.IST),
				end:   time.Date(2023, 5, 8, 23, 59, 59, 0, datetime.IST),
			},
			want: [][2]time.Time{
				{time.Date(2023, 5, 6, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 7, 23, 59, 59, *********, datetime.IST)},
			},
		},
		{
			name: "Range ending on a Saturday",
			args: args{
				start: time.Date(2023, 5, 1, 0, 0, 0, 0, datetime.IST),
				end:   time.Date(2023, 5, 6, 23, 59, 59, 0, datetime.IST),
			},
			want: [][2]time.Time{
				{time.Date(2023, 5, 6, 0, 0, 0, 0, datetime.IST), time.Date(2023, 5, 6, 23, 59, 59, *********, datetime.IST)},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetWeekendsInRange(tt.args.start, tt.args.end); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getWeekendsInRange() = %v, want %v", got, tt.want)
			}
		})
	}
}
