package loans

import (
	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"

	"github.com/epifi/gamma/api/preapprovedloan"
)

type EventEmitter struct {
	EventName   string
	StageName   string
	Vendor      string
	LoanProgram string
	Status      string
	SubStatus   string
	EventParams map[string]string
	EventType   string
	EventId     string
	ActorId     string
	ProspectId  string
	FlowName    string
}

func NewEventEmitter(lse *preapprovedloan.LoanStepExecution, eventName string, vendor string, loanProgram string, eventParams map[string]string) *EventEmitter {
	return &EventEmitter{
		EventName:   eventName,
		StageName:   lse.GetStepName().String(),
		Vendor:      vendor,
		LoanProgram: loanProgram,
		Status:      lse.GetStatus().String(),
		SubStatus:   lse.GetSubStatus().String(),
		EventParams: eventParams,
		EventType:   events.EventTrack,
		EventId:     uuid.New().String(),
		ActorId:     lse.GetActorId(),
		ProspectId:  "",
		FlowName:    lse.GetFlow().String(),
	}
}

func (c *EventEmitter) GetEventType() string {
	return c.EventType
}

func (c *EventEmitter) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *EventEmitter) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *EventEmitter) GetEventId() string {
	return c.EventId
}

func (c *EventEmitter) GetUserId() string {
	return c.ActorId
}

func (c *EventEmitter) GetProspectId() string {
	return c.ProspectId
}

func (c *EventEmitter) GetEventName() string {
	return c.EventName
}
