//nolint:dupl
package datetime

import (
	"context"
	"database/sql"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/relvacode/iso8601"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// layout numbers abstracted. Ref: https://golang.org/src/time/format.go
const (
	YYYY = "2006"
	MM   = "01"
	DD   = "02"
	Mon  = "Jan"

	TWENTY_FOUR_HOUR_LAYOUT = "15:04"
	TWELVE_HOUR_LAYOUT      = "03:04 PM"
	DATE_LAYOUT_YYYYMMDD    = "2006-01-02"
	DATE_LAYOUT_DDMMYYYY    = "02-01-2006"

	CreatedAt  = "created_at"
	Descending = "desc"
	Ascending  = "asc"

	MinutesInADay = 1440 * time.Minute
	HoursInADay   = 24 * time.Hour

	IST_TIME_ZONE = "Asia/Kolkata"

	// EST5EDT_TIME_ZONE refers to timezones that observe EST (Eastern Standard Time) with United States daylight
	// saving time rules, like America/New_York
	EST5EDT_TIME_ZONE = "EST5EDT"

	UTC_TIME_ZONE        = "UTC"
	IST_DATE_TIME_SUFFIX = " +0530 IST"
)

var (
	// TIME_MIN - minimum time for comparison
	TIME_MIN = time.Time{}
	// TIME_MAX - maximum time for comparison. INT_MAX equivalent for time - https://stackoverflow.com/a/32620397
	TIME_MAX = time.Unix(1<<63-62135596801, 999999999)

	IST, _           = time.LoadLocation(IST_TIME_ZONE)
	EST5EDT, _       = time.LoadLocation(EST5EDT_TIME_ZONE)
	UTC, _           = time.LoadLocation(UTC_TIME_ZONE)
	ErrInvalidDate   = errors.New("Invalid date")
	ErrUserOverAge   = errors.New("User over age")
	ErrUserUnderAge  = errors.New("User under age")
	WeekdayStringMap = map[string]time.Weekday{
		time.Sunday.String():    time.Sunday,
		time.Monday.String():    time.Monday,
		time.Tuesday.String():   time.Tuesday,
		time.Wednesday.String(): time.Wednesday,
		time.Thursday.String():  time.Thursday,
		time.Friday.String():    time.Friday,
		time.Saturday.String():  time.Saturday,
	}
	fullToShortWeekDayNamesMap = map[string]string{
		"MONDAY":    "Mon",
		"TUESDAY":   "Tue",
		"WEDNESDAY": "Wed",
		"THURSDAY":  "Thu",
		"FRIDAY":    "Fri",
		"SATURDAY":  "Sat",
		"SUNDAY":    "Sun",
	}
)

var dateFormats = []string{
	fmt.Sprintf("%s-%s-%s", YYYY, MM, DD),
	fmt.Sprintf("%s-%s-%s", DD, MM, YYYY),
	fmt.Sprintf("%s%s%s", DD, Mon, YYYY),
	fmt.Sprintf("%s/%s/%s", DD, MM, YYYY),
	fmt.Sprintf("%s-%s-%s", DD, Mon, YYYY),
}

// DateFromString takes in date string, understands the format
// and returns date.Date object.
// dateFormats contains the list of formats the functions supports
// TODO(aditya): Write tests for this function and replace with dateparse
func DateFromString(s string) *date.Date {
	for i := 0; i < len(dateFormats); i++ {
		t, err := time.Parse(dateFormats[i], s)
		if err != nil {
			continue
		}
		return &date.Date{
			Day:   int32(t.Day()),
			Month: int32(t.Month()),
			Year:  int32(t.Year()),
		}
	}
	logger.DebugNoCtx(fmt.Sprintf("No compatible date format found for %s", s))
	return nil
}

// DateToString converts date to string formatted as per given layout
// If location is passed nil then IST is used as default location
func DateToString(d *date.Date, layout string, loc *time.Location) string {
	tm := DateToTime(d, loc)
	return tm.Format(layout)
}

// TimestampToString converts the timestamp to string formatted as per given layout
// If location is passed nil then IST is used as default location
func TimestampToString(timestamp *timestamppb.Timestamp, layout string, loc *time.Location) string {
	if loc == nil {
		loc = IST
	}
	tm := TimestampToTime(timestamp).In(loc)
	return tm.Format(layout)
}

// DateToTime converts date to time
// If location is passed nil then IST is used as default location
// Use DateToTimeV2 if direct time value is needed instead of pointer
func DateToTime(d *date.Date, loc *time.Location) *time.Time {
	tm := DateToTimeV2(d, loc)
	return &tm
}

// DateToTimeV2 converts date to time
// If location is passed nil then IST is used as default location
func DateToTimeV2(d *date.Date, loc *time.Location) time.Time {
	if loc == nil {
		loc = IST
	}
	month := time.Month(d.GetMonth())
	year := int(d.GetYear())
	day := int(d.GetDay())
	tm := time.Date(year, month, day, 00, 00, 00, 00, loc)

	return tm
}

// DateToSqlNullTime converts date to sql null time value
// If passed date is nil, time returned will be null (valid = false)
// If location is passed nil then IST is used as default location
// Use DateToTimeV2 if direct time value is needed instead of nulltime
func DateToSqlNullTime(d *date.Date, loc *time.Location) sql.NullTime {
	if d == nil {
		return sql.NullTime{}
	}
	nulTm := sql.NullTime{
		Time:  DateToTimeV2(d, loc),
		Valid: true,
	}
	return nulTm
}

func DateToTimestamp(d *date.Date, loc *time.Location) *timestamppb.Timestamp {
	tm := DateToTime(d, loc)
	return timestamppb.New(*tm)
}

// Deprecated: in favour of TimestampToDateInLoc
// TimestampToDate converts proto timestamp to date
func TimestampToDate(timestamp *timestamppb.Timestamp) *date.Date {
	tm := timestamp.AsTime()
	return TimeToDate(&tm)
}

// TimestampToDateInLoc converts proto timestamp to date
// IF location is passed nil then IST is used as default location
func TimestampToDateInLoc(timestamp *timestamppb.Timestamp, loc *time.Location) *date.Date {
	if loc == nil {
		loc = IST
	}

	return TimeToDateInLoc(timestamp.AsTime(), loc)
}

// Deprecated: in favour of TimeToDateInLoc
// TimeToDate converts time to date
func TimeToDate(tm *time.Time) *date.Date {
	return &date.Date{
		Year:  int32(tm.Year()),
		Month: int32(tm.Month()),
		Day:   int32(tm.Day()),
	}
}

// TimeToDateInLoc converts time to date
// IF location is passed nil then IST is used as default location
func TimeToDateInLoc(tm time.Time, loc *time.Location) *date.Date {
	if loc == nil {
		loc = IST
	}

	tmInLoc := tm.In(loc)
	return &date.Date{
		Year:  int32(tmInLoc.Year()),
		Month: int32(tmInLoc.Month()),
		Day:   int32(tmInLoc.Day()),
	}
}

// ParseStringTimeStampProto converts a string time to timestamp proto, provided a correct layout
// the time is parsed in UTC by default
func ParseStringTimeStampProto(layout, value string) (*timestamppb.Timestamp, error) {
	tim, err := time.Parse(layout, value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse value to given layout: %w", err)
	}

	protoTime := timestamppb.New(tim)
	return protoTime, nil
}

// ParseStringTimestampProtoInLocation converts a string time in a given layout and timezone to a proto timestamp
func ParseStringTimestampProtoInLocation(layout, value string, location *time.Location) (*timestamppb.Timestamp, error) {
	tim, err := time.ParseInLocation(layout, value, location)
	if err != nil {
		return nil, fmt.Errorf("failed to parse value to given layout: %w", err)
	}

	protoTime := timestamppb.New(tim)
	return protoTime, nil
}

// ParseStringToDateInLocation converts a string time in a given layout and timezone to a proto date
func ParseStringToDateInLocation(layout, value string, location *time.Location) (*date.Date, error) {
	tim, err := time.ParseInLocation(layout, value, location)
	if err != nil {
		return nil, fmt.Errorf("failed to parse value to given layout: %w", err)
	}

	protoDate := TimeToDateInLoc(tim, location)
	return protoDate, nil
}

// TimeFromNow returns IST future/past time based on duration passed.
//
// Future time: If your current time is : 2020-10-13 15:07:09.993052 +0530 IST
// and duration is passed 5*time.Minute it will return future time `2020-10-13 15:12:09.99304 +0530 IST`
//
// Past time: Current time is `2020-10-13 15:10:08.453796 +0530 IST` it will return `2020-10-13 15:05:08.45378 +0530 IST`
// if duration is passed -5*time.Minute
func TimeFromNow(duration time.Duration) time.Time {
	return time.Now().In(IST).Add(duration)
}

// IsPastDateFromToday check if input time is past time from now in input timezone.
// Return true if dateTime is past time
// Return false if dateTime is equal to current time or future time.
func IsPastDateFromToday(dateTime time.Time) bool {
	return time.Now().In(dateTime.Location()).After(dateTime)
}

// Deprecated: In favour of IsDateBeforeTodayInLoc
// IsDateBeforeToday checks if input date is before today.
func IsDateBeforeToday(d *date.Date) bool {
	now := time.Now()
	today := TimeToDate(&now)

	return (d.GetYear() < today.GetYear()) ||
		(d.GetYear() == today.GetYear() && d.GetMonth() < today.GetMonth()) ||
		(d.GetYear() == today.GetYear() && d.GetMonth() == today.GetMonth() && d.GetDay() < today.GetDay())
}

// IsDateBeforeTodayInLoc checks if input date is before today.
// IF location is passed nil then IST is used as default location
func IsDateBeforeTodayInLoc(d *date.Date, loc *time.Location) bool {
	if loc == nil {
		loc = IST
	}
	now := time.Now()
	today := TimeToDateInLoc(now, loc)

	return (d.GetYear() < today.GetYear()) ||
		(d.GetYear() == today.GetYear() && d.GetMonth() < today.GetMonth()) ||
		(d.GetYear() == today.GetYear() && d.GetMonth() == today.GetMonth() && d.GetDay() < today.GetDay())
}

// IsDateAfterTodayInLoc checks if input date is after today.
// Location defaults to IST
func IsDateAfterTodayInLoc(d *date.Date, loc *time.Location) bool {
	if loc == nil {
		loc = IST
	}
	now := time.Now()
	today := TimeToDateInLoc(now, loc)

	return (d.GetYear() > today.GetYear()) ||
		(d.GetYear() == today.GetYear() && d.GetMonth() > today.GetMonth()) ||
		(d.GetYear() == today.GetYear() && d.GetMonth() == today.GetMonth() && d.GetDay() > today.GetDay())
}

// GetDaysInMonth takes year and month as input and returns the number of days in that month.
func GetDaysInMonth(month time.Month, year int) int {
	nextMonth, year := AddNMonthsToMonth(month, year, 1)
	lastDayOfMonth := time.Date(year, nextMonth, 0, 0, 0, 0, 0, time.UTC)
	return lastDayOfMonth.Day()
}

// AddNMonthsToMonth uses the input `month` and `year` as the starting time and adds `n` months to it.
// If `n` passed is negative, months are subtracted
func AddNMonthsToMonth(month time.Month, year int, n int) (time.Month, int) {
	months := int(month) + n
	var finalMonth, yearsToAdd int
	if months <= 0 {
		yearsToAdd = months/12 - 1
		finalMonth = 12 + months%12
	} else {
		yearsToAdd = (months - 1) / 12
		finalMonth = ((months - 1) % 12) + 1
	}
	return time.Month(finalMonth), year + yearsToAdd
}

// AddNMonths takes `time` as input and adds `n` months in that time.
// Ex: input: time=2020-11-03 n=2 | output: 2021-01-03
//
//	input: time=2021-01-31 n=1 | output: 2021-02-28
//	input: time=2021-03-29 n=1 | output: 2021-02-28
//	input: time=2021-02-28 n=1 | output: 2021-03-28
func AddNMonths(inputTime *time.Time, n int) *time.Time {
	if n == 0 {
		return inputTime
	}
	var newTime time.Time
	endMonth, year := AddNMonthsToMonth(inputTime.Month(), inputTime.Year(), n)
	daysInEndMonth := GetDaysInMonth(endMonth, year)
	currDay := inputTime.Day()

	dayOfMonth := integer.Min(currDay, daysInEndMonth)
	newTime = time.Date(year, endMonth, dayOfMonth, inputTime.Hour(),
		inputTime.Minute(), inputTime.Second(), inputTime.Nanosecond(), inputTime.Location())
	return &newTime
}

func DateEquals(x, y *date.Date) bool {
	if x == nil || y == nil {
		return false
	}
	return x.GetDay() == y.GetDay() &&
		x.GetMonth() == y.GetMonth() &&
		x.GetYear() == y.GetYear()
}

// DateToDDMMYYYY outputs the date in a DD-MM-YYYY format.
func DateToDDMMYYYY(aDate *date.Date) string {
	return DateToDDMMYYYYV2(aDate, "-")
}

// DateToDDMMYYYYV2 outputs the date in a DD<sep>MM<sep>YYYY format
func DateToDDMMYYYYV2(aDate *date.Date, sep string) string {
	return fmt.Sprintf("%02d%s%02d%s%04d", aDate.GetDay(), sep, aDate.GetMonth(), sep, aDate.GetYear())
}

// MinutesOfDayForTime take time struct and return minutes elapsed in on that day
func MinutesOfDayForTime(time *time.Time) int {
	return time.Hour()*60 + time.Minute()
}

// MinutesOfDayForHHMM take time in HH:MM in 24-hour format and return minutes elapsed in day
func MinutesOfDayForHHMM(time string) (int, error) {
	splitTime := strings.Split(time, ":")
	if len(splitTime) < 2 {
		return 0, fmt.Errorf("wrong time format passed")
	}

	hour, err := strconv.Atoi(splitTime[0])
	if err != nil || hour > 23 || hour < 0 {
		return 0, fmt.Errorf("wrong hour string passed")
	}

	minute, err := strconv.Atoi(splitTime[1])
	if err != nil || minute > 59 || minute < 0 {
		return 0, fmt.Errorf("wrong minute string passed")
	}

	return hour*60 + minute, nil
}

// CurIstTimeLieBetweenStartAndEndInHHMM takes start and end time in HH:MM format and check if cur IST time
// lie between them.
// Returns error in case parsing fails
func CurIstTimeLieBetweenStartAndEndInHHMM(startTime, endTime string) (bool, error) {
	now := time.Now().In(IST)
	curTime := MinutesOfDayForTime(&now)

	start, err := MinutesOfDayForHHMM(startTime)
	if err != nil {
		return false, fmt.Errorf("startTime parsing error %w", err)
	}

	end, err := MinutesOfDayForHHMM(endTime)
	if err != nil {
		return false, fmt.Errorf("endTime parsing error %w", err)
	}
	return curTimeLieBetweenStartAndEnd(start, end, curTime), nil
}

// CurTimeLieBetweenStartAndEnd will return if current time lie between start time and end time
// Time is in minutes elapsed in day
func curTimeLieBetweenStartAndEnd(startTime, endTime, curTime int) bool {
	return (startTime < endTime && curTime >= startTime && curTime <= endTime) ||
		(startTime > endTime && (curTime >= startTime || curTime <= endTime))
}

// TimeLieBetweenStartAndEndInHHMM takes time.Time and start, end time in HH:MM format and check if time.Time
// lie between start and end.
// Returns error in case parsing fails
func TimeLieBetweenStartAndEndInHHMM(time time.Time, startTime, endTime string) (bool, error) {
	curTime := MinutesOfDayForTime(&time)

	start, err := MinutesOfDayForHHMM(startTime)
	if err != nil {
		return false, fmt.Errorf("startTime parsing error %w", err)
	}

	end, err := MinutesOfDayForHHMM(endTime)
	if err != nil {
		return false, fmt.Errorf("endTime parsing error %w", err)
	}
	return curTimeLieBetweenStartAndEnd(start, end, curTime), nil
}

func IsBetweenTimestamp(curTimestamp time.Time, startTimestamp time.Time, endTimestamp time.Time) bool {
	if startTimestamp.Before(curTimestamp) && endTimestamp.After(curTimestamp) {
		return true
	} else {
		return false
	}
}

// Method to parse ISO 8601 timestamp formats to time. A lot of timestamp formats are possible in ISO 8601
// hence we are using this library to convert it into time for future use cases as well.
// NOTE : Users of this method are expected to add test cases with the timestamp string they pass
func ParseIso8601DateTimeString(timestamp string) (*timestamppb.Timestamp, error) {
	if timestamp == "" {
		return nil, errors.New("empty timestamp passed for parsing")
	}
	tm, err := iso8601.ParseString(timestamp)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("error parsing timestamp string : %v to time.Time", timestamp))
	}
	tp := timestamppb.New(tm)
	return tp, err
}

func FormatTo12HourFormat(timeIn24HourFormat string) (string, error) {
	t, err := time.Parse(TWENTY_FOUR_HOUR_LAYOUT, timeIn24HourFormat)
	if err != nil {
		return "", err
	}
	return t.Format(TWELVE_HOUR_LAYOUT), nil
}

func Max(x, y *date.Date) *date.Date {
	if x.GetYear() > y.GetYear() ||
		x.GetYear() == y.GetYear() && x.GetMonth() > y.GetMonth() ||
		x.GetYear() == y.GetYear() && x.GetMonth() == y.GetMonth() && x.GetDay() > y.GetDay() {
		return x
	}
	return y
}

// converts standard timstamps which are created_at, updated_at, deleted_at in time.Time format to timestamppb.Timestamp format
func TimeToTimestamp(createdAt, updatedAt time.Time, deletedAt *time.Time) (*timestamppb.Timestamp, *timestamppb.Timestamp, *timestamppb.Timestamp) {
	createdTimestamp := timestamppb.New(createdAt)
	updatedTimestamp := timestamppb.New(updatedAt)
	var deletedTimestamp *timestamppb.Timestamp = nil
	if deletedAt != nil {
		deletedTimestamp = timestamppb.New(*deletedAt)
	}
	return createdTimestamp, updatedTimestamp, deletedTimestamp
}

// GetTimeAtStartOfTheDay returns time at the start of the day for given inputTime.
// Eg: for 6th Jan 15:00 IST as input time, it will return 6th Jan 00:00 IST
func GetTimeAtStartOfTheDay(inputTime time.Time) time.Time {
	return time.Date(inputTime.Year(), inputTime.Month(), inputTime.Day(), 0, 0, 0, 0, inputTime.Location())
}

// GetDaysInYear returns the number of days in a particular year.
// For leap year, it returns 366, for rest 365
func GetDaysInYear(year int) int {
	return time.Date(year, 12, 31, 0, 0, 0, 0, time.UTC).YearDay()
}

func StartOfDay(val time.Time) time.Time {
	val = val.In(IST)
	return time.Date(val.Year(), val.Month(), val.Day(), 0, 0, 0, 0, IST)
}

func EndOfDay(val time.Time) time.Time {
	val = val.In(IST)
	return time.Date(val.Year(), val.Month(), val.Day(), 23, 59, 59, 0, IST)
}

func StartOfDayWithLocation(val time.Time) time.Time {
	return time.Date(val.Year(), val.Month(), val.Day(), 0, 0, 0, 0, val.Location())
}

func EndOfDayWithLocation(val time.Time) time.Time {
	return time.Date(val.Year(), val.Month(), val.Day(), 23, 59, 59, 0, val.Location())
}

// check if given date is valid or not
func ValidateDate(date *date.Date) error {
	// dd-mm-yyyy
	layout := "02-01-2006"
	_, err := time.Parse(layout, DateToDDMMYYYY(date))
	if err != nil {
		return err
	}
	return nil
}

func StartOfWeek(date time.Time, weekStartDay time.Weekday) time.Time {
	dayDiff := date.Weekday() - weekStartDay
	sow := StartOfDay(date.Add(time.Duration(-24*dayDiff) * time.Hour))
	return sow
}

func EndOfWeek(date time.Time, weekEndDay time.Weekday) time.Time {
	dayDiff := weekEndDay - date.Weekday()
	eow := EndOfDay(date.Add(time.Duration(24*dayDiff) * time.Hour))
	return eow
}

func TimeNowInIST(ctx context.Context) string {
	location, err := time.LoadLocation(IST_TIME_ZONE)
	if err != nil {
		logger.Error(ctx, "error in loading location based on time zone", zap.Error(err))
		return ""
	}

	currentTime := time.Now().In(location).String()

	timeWithoutTimeZone := strings.TrimSuffix(currentTime, IST_DATE_TIME_SUFFIX)
	return timeWithoutTimeZone
}

// By Default, if nil proto timestamp is converted into time.Time using AsTime, it gives January 1, year 1970, 00:00:00.000000000 UTC.
// TimestampToTime converts proto timestamp to standard time format.
// TimestampToTime can be used at scenario where requirement is to get time
// as January 1, year 1, 00:00:00.000000000 UTC, when nil proto timestamp is passed.
// Eg: for nil proto timestamp, it will return January 1, year 1, 00:00:00.000000000 UTC
// Eg: for a valid proto timestamp, it will convert using AsTime func.
func TimestampToTime(timestamp *timestamppb.Timestamp) time.Time {
	if timestamp == nil {
		return time.Time{}
	}

	return timestamp.AsTime()
}

func StartOfMonth(date time.Time) time.Time {
	return time.Date(date.Year(), date.Month(), 1, 0, 0, 0, 0, date.Location())
}

// EndOfMonth returns time for the end of this month
func EndOfMonth(date time.Time) time.Time {
	nextmonth, year := AddNMonthsToMonth(date.Month(), date.Year(), 1)
	return time.Date(year, nextmonth, 1, 0, 0, 0, 0, date.Location()).Add(-1 * time.Duration(1))
}

// PreviousMonth returns time for the start of day of previous month
func PreviousMonth(date time.Time) time.Time {
	prevMonth, year := AddNMonthsToMonth(date.Month(), date.Year(), -1)
	return time.Date(year, prevMonth, 1, 0, 0, 0, 0, date.Location())
}

func StartOfYear(year int, loc *time.Location) time.Time {
	return time.Date(year, time.January, 1, 0, 0, 0, 0, loc)
}

// returns month for a date
// date: time=2020-11-03, inShortFormat: false | output: November 2022
// date: time=2020-11-03, inShortFormat: true | output: Nov 2022
func GetMonthString(date time.Time, inShortFormat bool) string {
	if inShortFormat {
		return date.Format("Jan 2006")
	}
	return date.Format("January 2006")
}

// returns true if t1 is before t2, false if t1 >= t2.
func IsBefore(t1, t2 *timestamppb.Timestamp) bool {
	date1 := t1.AsTime()
	date2 := t2.AsTime()
	return date1.Before(date2)
}

// returns true if t1 is after t2, false if t1 <= t2.
func IsAfter(t1, t2 *timestamppb.Timestamp) bool {
	date1 := t1.AsTime()
	date2 := t2.AsTime()
	return date1.After(date2)
}

// GetShortWeekdayString returns the short weekday string for the given weekday string.
// Eg: "MONDAY" -> "Mon"
func GetShortWeekdayString(weekday string) string {
	if shortName, present := fullToShortWeekDayNamesMap[weekday]; present {
		return shortName
	}
	return weekday
}

// GetHourAndMinute take time in HH:MM in 24-hour format and return hour and minutes
func GetHourAndMinute(time string) (int, int, error) {
	splitTime := strings.Split(time, ":")
	if len(splitTime) < 2 {
		return 0, 0, fmt.Errorf("wrong time format passed")
	}

	hour, err := strconv.Atoi(splitTime[0])
	if err != nil || hour > 23 || hour < 0 {
		return 0, 0, fmt.Errorf("wrong hour string passed")
	}

	minute, err := strconv.Atoi(splitTime[1])
	if err != nil || minute > 59 || minute < 0 {
		return 0, 0, fmt.Errorf("wrong minute string passed")
	}
	return hour, minute, nil
}

// MaxDurationTime will return max duration from the given two duration
func MaxDurationTime(a, b time.Duration) time.Duration {
	if a > b {
		return a
	}
	return b
}

// isDatePastXDays return true if given date is before x days
func IsDatePastXDays(ts *timestamppb.Timestamp, xDay int) bool {
	// dividing hours by 24 to get number of days
	daysDifference := int(math.Round(time.Since(ts.AsTime()).Hours() / 24))
	return ts.IsValid() && daysDifference >= xDay
}

// EndOfFinancialYear returns timestamp for end of current financial year
// eg: If current time is 5 Dec 2022 returns 31 March 2023
// If current time is 1 Mar 2023 returns 31 March 2023
func EndOfFinancialYear(val time.Time) time.Time {
	val = val.In(IST)
	if val.Month() < time.March {
		return time.Date(val.Year(), 3, 31, 0, 0, 0, 0, IST)
	}
	return time.Date(val.Year()+1, 3, 31, 0, 0, 0, 0, IST)
}

// StartOfFinancialYear returns timestamp for start of current financial year
// eg: If current time is 5 Dec 2022 returns 1 April 2022
// If current time is 5 March 2023 returns 1 April 2022
func StartOfFinancialYear(val time.Time) time.Time {
	val = val.In(IST)
	if val.Month() < time.April {
		return time.Date(val.Year()-1, 4, 1, 0, 0, 0, 0, IST)
	}
	return time.Date(val.Year(), 4, 1, 0, 0, 0, 0, IST)
}

// GetTimeStringWithMeridianInfo accepts a time object and returns a formatted string denoting the time in AM/PM format
// eg: If the time object is something like {hour : 13 , minute: 5}, the returned string will be 1:05 PM
func GetTimeStringWithMeridianInfo(time *time.Time) (string, error) {
	if time == nil {
		return "", errors.New("expected non-null time object in request")
	}
	meridian := "AM"
	hour := time.Hour()
	if hour >= 12 {
		meridian = "PM"
	}
	// done to ensure the case of 12 PM where it should not resolve to something like 00:15 PM
	if hour > 12 {
		hour -= 12
	}
	hourPrefix := ""
	if hour < 10 {
		hourPrefix = "0"
	}
	minutePrefix := ""
	if time.Minute() < 10 {
		minutePrefix = "0"
	}
	return fmt.Sprintf("%s%d:%s%d %s", hourPrefix, hour, minutePrefix, time.Minute(), meridian), nil
}

// GetDatesBetween returns a list of dates between the given start and end dates
// (inclusive of both start and end dates)
// e.g: If start date is 1st Jan 2021 and end date is 3rd Jan 2021, the returned list will be
// [1st Jan 2021, 2nd Jan 2021, 3rd Jan 2021]
func GetDatesBetween(startDate, endDate *date.Date) []*date.Date {
	var dates []*date.Date
	startTime := DateToTimeV2(startDate, time.UTC)
	endTime := DateToTimeV2(endDate, time.UTC)
	for currentDate := startTime; !currentDate.After(endTime); currentDate = currentDate.AddDate(0, 0, 1) {
		dates = append(dates, TimeToDateInLoc(currentDate, time.UTC))
	}
	return dates
}

// IsDateAfter If date1 occurs after date2, will return true
// If both dates are equal, or if date1 occurs before date2, will return false
func IsDateAfter(date1, date2 *date.Date) bool {
	return (date1.GetYear() > date2.GetYear()) ||
		(date1.GetYear() == date2.GetYear() && date1.GetMonth() > date2.GetMonth()) ||
		(date1.GetYear() == date2.GetYear() && date1.GetMonth() == date2.GetMonth() && date1.GetDay() > date2.GetDay())
}

// Returns the time which is earliest of the two
// If both time objects are exactly same return the "time2"
func Min(time1 time.Time, time2 time.Time) time.Time {
	if time1.Before(time2) {
		return time1
	}
	return time2
}

func IsTimestampEmpty(ts *timestamppb.Timestamp) bool {
	return ts.GetSeconds() == 0 && ts.GetNanos() == 0
}

// TimeNowForAnyLocationAndGivenFormat gives current timestamp in form of string. It creates Timestamp for given location and in given format
// If location is not passed by default it creates current timestamp for IST location
func TimeNowForAnyLocationAndGivenFormat(ctx context.Context, loc *time.Location, format string) string {
	if loc == nil {
		loc = IST
	}
	location, err := time.LoadLocation(loc.String())
	if err != nil {
		logger.Error(ctx, "error in loading location based on time zone")
		return ""
	}
	currentTime := time.Now().In(location).Format(format)

	return currentTime
}

func GetAbsoluteTimeDifference(time1, time2 time.Time) time.Duration {
	diff1 := time1.Sub(time2)
	diff2 := time2.Sub(time1)

	if diff1 > 0 {
		return diff1
	}

	return diff2
}

// GetIntegerizedDurationDisplayString takes a time.Duration and converts it into string representation
// indicating the duration in the largest possible unit (years, months, weeks, days, hours, minutes, or seconds).
// It also provides an option to convert the output string to lowercase.
// e.g. (60*60*24*8 seconds or 8 days -> 1 WEEK AGO), (60*60*24*30*14 seconds or 14 months -> 1 YEAR AGO)
// (60*60*24*80 seconds or 80 days -> 2 MONTHS AGO)
func GetIntegerizedDurationDisplayString(duration time.Duration, toLowerCase bool) string {
	// Calculate duration in different units
	seconds := int(duration.Seconds())
	minutes := int(duration.Minutes())
	hours := int(duration.Hours())
	days := hours / 24
	weeks := days / 7
	months := days / 30
	years := days / 365

	// Determine the largest unit to display
	var value int
	var unit string
	switch {
	case years > 0:
		value = years
		unit = "YEAR"
	case months > 0:
		value = months
		unit = "MONTH"
	case weeks > 0:
		value = weeks
		unit = "WEEK"
	case days > 0:
		value = days
		unit = "DAY"
	case hours > 0:
		value = hours
		unit = "HR"
	case minutes > 0:
		value = minutes
		unit = "MIN"
	default:
		value = seconds
		unit = "SEC"
	}

	displayString := strconv.Itoa(value) + " " + unit + pluralize(value) + " AGO"

	if toLowerCase {
		displayString = strings.ToLower(displayString)
	}

	return displayString
}

func pluralize(n int) string {
	if n > 1 {
		return "S"
	}
	return ""
}

// ParseDurationWithDefault parses the given duration string and returns the time.Duration value.
// If the parsing fails, it returns the default duration value passed
func ParseDurationWithDefault(durationString string, defaultDuration time.Duration) time.Duration {
	duration, err := time.ParseDuration(durationString)
	if err != nil {
		return defaultDuration
	}
	return duration
}
