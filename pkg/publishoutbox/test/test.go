package test

import (
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/user/config"

	"go.uber.org/zap"
)

// InitTestServerWithoutDb initiates components needed for tests
// Will be invoked from TestMain but can be called from individual tests for *special* cases only
func InitTestServerWithoutDb() (*config.Config, func()) {
	// Init config
	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	// Setup logger
	logger.Init(conf.Application.Environment)

	return conf, func() {
	}
}
