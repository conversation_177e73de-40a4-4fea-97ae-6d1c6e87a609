package cx

import (
	"context"
	"os"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/api/cx/watson"
	mockWatson "github.com/epifi/gamma/api/cx/watson/mocks"

	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	logger.Init(cfg.TestEnv)
	exitCode := m.Run()
	os.Exit(exitCode)
}

func TestIsIncidentPresent(t *testing.T) {
	t.<PERSON>()
	type mockStruct struct {
		mockWatsonClient *mockWatson.MockWatsonClient
	}
	tests := []struct {
		name            string
		actorID         string
		issueCategoryID string
		setUpMockCalls  func(*mockStruct)
		expected        bool
	}{
		{
			name:            "incident exists",
			actorID:         "actor123",
			issueCategoryID: "issue456",
			setUpMockCalls: func(m *mockStruct) {
				resp := &watson.GetIncidentsForClientResponse{
					Status: rpc.StatusOk(),
				}
				m.mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), &watson.GetIncidentsForClientRequest{
					IncidentFilter: &watson.IncidentFiltersForClient{
						ActorId:         "actor123",
						ResponseLimit:   1,
						IssueCategoryId: "issue456",
						IncidentStates: []watson.IncidentState{
							watson.IncidentState_INCIDENT_STATE_DROPPED,
							watson.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
							watson.IncidentState_INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT,
							watson.IncidentState_INCIDENT_STATE_TICKET_CREATED,
						},
					},
				}).Return(resp, nil)
			},
			expected: true,
		},
		{
			name:            "incident not found",
			actorID:         "actor123",
			issueCategoryID: "issue456",
			setUpMockCalls: func(m *mockStruct) {
				resp := &watson.GetIncidentsForClientResponse{
					Status: rpc.StatusRecordNotFound(),
				}
				m.mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), &watson.GetIncidentsForClientRequest{
					IncidentFilter: &watson.IncidentFiltersForClient{
						ActorId:         "actor123",
						ResponseLimit:   1,
						IssueCategoryId: "issue456",
						IncidentStates: []watson.IncidentState{
							watson.IncidentState_INCIDENT_STATE_DROPPED,
							watson.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
							watson.IncidentState_INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT,
							watson.IncidentState_INCIDENT_STATE_TICKET_CREATED,
						},
					},
				}).Return(resp, nil)
			},
			expected: false,
		},
		{
			name:            "internal error",
			actorID:         "actor123",
			issueCategoryID: "issue456",
			setUpMockCalls: func(m *mockStruct) {
				resp := &watson.GetIncidentsForClientResponse{
					Status: rpc.StatusInternal(),
				}
				m.mockWatsonClient.EXPECT().GetIncidentsForClient(gomock.Any(), &watson.GetIncidentsForClientRequest{
					IncidentFilter: &watson.IncidentFiltersForClient{
						ActorId:         "actor123",
						ResponseLimit:   1,
						IssueCategoryId: "issue456",
						IncidentStates: []watson.IncidentState{
							watson.IncidentState_INCIDENT_STATE_DROPPED,
							watson.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
							watson.IncidentState_INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT,
							watson.IncidentState_INCIDENT_STATE_TICKET_CREATED,
						},
					},
				}).Return(resp, nil)
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockWatsonClient := mockWatson.NewMockWatsonClient(ctr)
			mockStruct := &mockStruct{
				mockWatsonClient: mockWatsonClient,
			}
			if tt.setUpMockCalls != nil {
				tt.setUpMockCalls(mockStruct)
			}
			result := IsIncidentPresent(context.Background(), mockWatsonClient, tt.actorID, tt.issueCategoryID)
			assert.Equal(t, tt.expected, result)

		})
	}
}
