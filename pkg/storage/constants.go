package storage

import "time"

const (
	PostgresSQLSchema = "postgresql"
	PostgresDriver    = "postgres"
)

const (
	DBConnSSLMode           = "sslmode"
	DBConnSSLRootCert       = "sslrootcert"
	DBConnSSLKey            = "sslkey"
	DBConnSSLCert           = "sslcert"
	DBConnAppName           = "application_name"
	StatementTimeout        = "statement_timeout"
	IdleInTxnSessionTimeout = "idle_in_transaction_session_timeout"
)

const (
	DBSSLModeDisable    = "disable"
	DBSSLModeVerifyFull = "verify-full"
	DBSSLModeRequired   = "required"
)

const (
	TxnRetries                     = 3
	DefaultStatementTimeout        = 30 * time.Second
	DefaultIdleInTxnSessionTimeout = 1 * time.Minute
)
