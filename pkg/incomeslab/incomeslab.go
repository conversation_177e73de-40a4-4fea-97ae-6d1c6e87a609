package incomeslab

import (
	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"

	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"

	"github.com/pkg/errors"
)

// GetSalaryRangeFromIncomeSlab returns a min and max range for a salary based on income slab
func GetSalaryRangeFromIncomeSlab(slab types.IncomeSlab) (int32, int32, error) {
	switch slab {
	case types.IncomeSlab_INCOME_SLAB_BELOW_1_LAC:
		return 0, 100000, nil
	case types.IncomeSlab_INCOME_SLAB_1_TO_5_LAC:
		return 100000, 500000, nil
	case types.IncomeSlab_INCOME_SLAB_5_TO_10_LAC:
		return 500000, 1000000, nil
	case types.IncomeSlab_INCOME_SLAB_10_TO_25_LAC:
		return 1000000, 2500000, nil
	case types.IncomeSlab_INCOME_SLAB_25_LAC_TO_1_CRORE:
		return 2500000, 10000000, nil
	case types.IncomeSlab_INCOME_SLAB_ABOVE_1_CRORE:
		return 10000000, 0, nil
	case types.IncomeSlab_INCOME_SLAB_ABOVE_25_LAC:
		return 2500000, 0, nil
	default:
		return 0, 0, errors.Errorf("income slab conversion not supported yet: %v", slab)
	}
}

// GetIncomeSlabBySalaryRange returns an income slab for the given salary range
func GetIncomeSlabBySalaryRange(salaryRange *userPb.SalaryRange) commonTypesPb.IncomeSlab {
	if salaryRange == nil {
		return commonTypesPb.IncomeSlab_INCOME_SLAB_UNSPECIFIED
	}
	switch {
	default:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_UNSPECIFIED
	case salaryRange.MaxValue <= 100000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_BELOW_1_LAC
	case salaryRange.MinValue >= 100000 && salaryRange.MaxValue <= 500000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_1_TO_5_LAC
	case salaryRange.MinValue >= 500000 && salaryRange.MaxValue <= 1000000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_5_TO_10_LAC
	case salaryRange.MinValue >= 1000000 && salaryRange.MaxValue <= 2500000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_10_TO_25_LAC
	case salaryRange.MinValue >= 2500000 && salaryRange.MaxValue <= 10000000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_25_LAC_TO_1_CRORE
	case salaryRange.MinValue >= 10000000:
		return commonTypesPb.IncomeSlab_INCOME_SLAB_ABOVE_1_CRORE
	}
}
