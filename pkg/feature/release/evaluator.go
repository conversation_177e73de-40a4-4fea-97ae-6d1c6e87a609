package release

import (
	"context"
	"fmt"
	"strings"

	staticConf "github.com/epifi/gamma/pkg/feature/release/config"
	genConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"

	"github.com/google/wire"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/logger"
)

var constraintsWireSet = wire.NewSet(NewAppVersionConstraint, NewStickinessConstraint, NewUserGroupConstraint)

var constraintFactoryWireSet = wire.NewSet(
	constraintsWireSet, wire.NewSet(NewConstraintFactoryImpl, wire.Bind(new(ConstraintFactory), new(*ConstraintFactoryImpl))),
)

var EvaluatorWireSet = wire.NewSet(NewEvaluator, constraintFactoryWireSet, wire.Bind(new(IEvaluator), new(*Evaluator)))

// TODO: Remove after every server moves to dynamic config V2
var StaticConfEvaluatorWireSet = wire.NewSet(NewStaticConfEvaluator, constraintFactoryWireSet, wire.Bind(new(IEvaluator), new(*Evaluator)))

type IEvaluator interface {
	Evaluate(ctx context.Context, commonConstraintData *CommonConstraintData) (bool, error)
}

type Evaluator struct {
	featureConstraintsData map[string][]ConstraintData
	constraintFactory      ConstraintFactory
}

func NewEvaluator(conf *genConf.FeatureReleaseConfig, factory ConstraintFactory) *Evaluator {

	featureConstraintsData := make(map[string][]ConstraintData)
	conf.FeatureConstraints().Range(func(feature string, constraintConfig *genConf.ConstraintConfig) bool {
		featureConstraintsData[strings.ToUpper(feature)] = factory.GetConstraintData(constraintConfig)
		return true
	})
	return &Evaluator{
		featureConstraintsData: featureConstraintsData,
		constraintFactory:      factory,
	}
}

func newEvaluatorFromABFeatureConfig(conf *genConf.ABFeatureReleaseConfig, factory ConstraintFactory) *Evaluator {
	featureConstraintsData := make(map[string][]ConstraintData)
	conf.FeatureConstraints().Iterate(func(feature string, constraintConfig *genConf.ABFeatureConstraintConfig) bool {
		featureConstraintsData[strings.ToUpper(feature)] = factory.GetConstraintData(constraintConfig.ConstraintConfig())
		return false
	})
	return &Evaluator{
		featureConstraintsData: featureConstraintsData,
		constraintFactory:      factory,
	}
}

func NewStaticConfEvaluator(conf *staticConf.FeatureReleaseConfig, factory ConstraintFactory) *Evaluator {
	featureConstraintsData := make(map[string][]ConstraintData)
	for feature, constraintConfig := range conf.FeatureConstraints {
		featureConstraintsData[strings.ToUpper(feature)] = factory.GetConstraintData(getDynConstrFromStaticConstr(constraintConfig))
	}
	return &Evaluator{
		featureConstraintsData: featureConstraintsData,
		constraintFactory:      factory,
	}
}

func (e *Evaluator) Evaluate(ctx context.Context, commonConstraintData *CommonConstraintData) (bool, error) {
	constraintDataList, ok := e.featureConstraintsData[commonConstraintData.Feature.String()]
	if !ok {
		logger.Debug(ctx, fmt.Sprintf("no constraints found for feature: %v", commonConstraintData.Feature.String()))
		return true, nil
	}

	// assuming default for the case where constraints are not defined for a feature.
	// 1. appVersionResult = true -> enable the feature for all app versions if nothing is specified
	// 2. ugResult = false -> if allowed user group is not defined, do not enable feature for the user.
	// 3. stickiness = true -> if roll out percent is not defined, assume the feature is to be enabled for all users
	// TODO(anand): update above comment. Revisit the logic.
	appVersionResult, ugResult, stickinessResult := true, true, true
	for _, constraintData := range constraintDataList {
		constraint, err := e.constraintFactory.GetConstraint(ctx, constraintData)
		if err != nil {
			return false, errors.Wrap(err, fmt.Sprintf("failed to get constraint for the data: %t", constraintData))
		}

		val, err := constraint.Evaluate(ctx, constraintData, commonConstraintData)
		if err != nil {
			return false, errors.Wrap(err, fmt.Sprintf("failed to evaluate constraint: %t", constraint))
		}

		switch constraint.(type) {
		case *AppVersionConstraint:
			appVersionResult = appVersionResult && val
			logger.Debug(ctx, fmt.Sprintf("FeatureRelease config values: feature: %v, constraintType: AppVersionConstraint  returned %t", commonConstraintData.Feature.String(), val))
		case *UserGroupConstraint:
			ugResult = ugResult && val
			logger.Debug(ctx, fmt.Sprintf("FeatureRelease config values: feature: %v, constraintType: UserGroupConstraint  returned %t", commonConstraintData.Feature.String(), val))
		case *StickinessConstraint:
			stickinessResult = stickinessResult && val
			logger.Debug(ctx, fmt.Sprintf("FeatureRelease config values: feature: %v, constraintType: StickinessConstraint  returned %t", commonConstraintData.Feature.String(), val))
			// Will be using this log to monitor rollout percentage on kibana
			logger.Debug(ctx, fmt.Sprintf("FeatureRelease config values: feature: %v, constraintType: StickinessConstraint  returned %t", commonConstraintData.Feature.String(), val))
		default:
			return false, fmt.Errorf("constraint chaining not supported for %t", constraint)
		}
	}

	// TODO(anand): move chaining of constraints to config. Can be a regex evaluator implemented using a stack.
	// Current implementation assumes that a feature:
	// 1. needs to mandatory pass app version checks
	// 2. if user is part of allowed user groups, enable the feature if 1 is true
	// 3. if user qualifies the percent rollout bucket, enable the feature if 1 is true
	return appVersionResult && (ugResult || stickinessResult), nil
}

// TODO: Remove after every server moves to dynamic config V2
func getDynConstrFromStaticConstr(staticConf *staticConf.ConstraintConfig) *genConf.ConstraintConfig {
	g := &genConf.ConstraintConfig{}
	g.Init()
	_ = g.Set(staticConf, false, nil)
	return g
}
