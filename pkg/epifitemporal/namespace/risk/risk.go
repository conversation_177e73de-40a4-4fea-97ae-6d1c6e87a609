package risk

// nolint: goimports
import "github.com/epifi/be-common/pkg/epifitemporal"

const (
	ApplyTotalFreezeOnAccount   epifitemporal.Workflow = "ApplyTotalFreezeOnAccount"
	ApplyTotalUnfreezeOnAccount epifitemporal.Workflow = "ApplyTotalUnfreezeOnAccount"
	ApplyCreditFreezeOnAccount  epifitemporal.Workflow = "ApplyCreditFreezeOnAccount"
	RiskActionDataSender        epifitemporal.Workflow = "RiskActionDataSender"
	// V2 workflows
	RiskBankActionCreditFreeze epifitemporal.Workflow = "RiskBankActionCreditFreeze"
	RiskBankActionUnfreeze     epifitemporal.Workflow = "RiskBankActionUnfreeze"
	RiskBankActionFullFreeze   epifitemporal.Workflow = "RiskBankActionFullFreeze"
	RiskBankActionDebitFreeze  epifitemporal.Workflow = "RiskBankActionDebitFreeze"

	// case management workflows
	RiskProcessReviewAction        epifitemporal.Workflow = "RiskProcessReviewAction"
	RiskFullFreeze                 epifitemporal.Workflow = "RiskFullFreeze"
	RiskUnfreeze                   epifitemporal.Workflow = "RiskUnfreeze"
	RiskCreditFreeze               epifitemporal.Workflow = "RiskCreditFreeze"
	RiskDebitFreeze                epifitemporal.Workflow = "RiskDebitFreeze"
	RiskPassUser                   epifitemporal.Workflow = "RiskPassUser"
	RiskProcessAlert               epifitemporal.Workflow = "RiskProcessAlert"
	RiskUpsertCase                 epifitemporal.Workflow = "RiskUpsertCase"
	RiskUpsertCaseV1               epifitemporal.Workflow = "RiskUpsertCaseV1"
	RequestUserInfo                epifitemporal.Workflow = "RequestUserInfo"
	MoveToReview                   epifitemporal.Workflow = "MoveToReview"
	AddLivenessRetry               epifitemporal.Workflow = "AddLivenessRetry"
	ProcessOnboardingReviewVerdict epifitemporal.Workflow = "ProcessOnboardingReviewVerdict"
	Snooze                         epifitemporal.Workflow = "Snooze"
	ExecuteAutoActions             epifitemporal.Workflow = "ExecuteAutoActions"
	ProcessAfuReviewVerdict        epifitemporal.Workflow = "ProcessAfuReviewVerdict"
	OrchestrateForm                epifitemporal.Workflow = "OrchestrateForm"
	RiskProcessReviewActionV1      epifitemporal.Workflow = "RiskProcessReviewActionV1"
	RejectEscalation               epifitemporal.Workflow = "RejectEscalation"
	ProcessEscalationEvent         epifitemporal.Workflow = "ProcessEscalationEvent"
	GenerateForm                   epifitemporal.Workflow = "GenerateForm"
	ProcessUnifiedLeaComplaint     epifitemporal.Workflow = "ProcessUnifiedLeaComplaint"
	RiskBankActionApplyLien        epifitemporal.Workflow = "RiskBankActionApplyLien"
	ApplyLien                      epifitemporal.Workflow = "ApplyLien"
)

const (
	RiskTaskQueue epifitemporal.TaskQueue = "risk"
)

const (
	GenerateBankActionData  epifitemporal.Activity = "GenerateBankActionData"
	UpdateRiskActionStatus  epifitemporal.Activity = "UpdateRiskActionStatus"
	GetActionStatusFromBank epifitemporal.Activity = "GetActionStatusFromBank"
	FiFreezeStatusUpdate    epifitemporal.Activity = "FiFreezeStatusUpdate"
	// GetNotificationTemplate is deprecated use GetNotificationTemplateForBankActions
	GetNotificationTemplate epifitemporal.Activity = "GetNotificationTemplate"
	AddToBankAction         epifitemporal.Activity = "AddToBankAction"
	GetReminderPoint        epifitemporal.Activity = "GetReminderPoint"
	// GetNotificationTemplateForBankActions enhancement over GetNotificationTemplate
	GetNotificationTemplateForBankActions epifitemporal.Activity = "GetNotificationTemplateForBankActions"

	// case management activities
	LogActionInDB                  epifitemporal.Activity = "LogActionInDB"
	ValidateAlert                  epifitemporal.Activity = "ValidateAlert"
	CreateAlertInDB                epifitemporal.Activity = "CreateAlertInDB"
	CheckCaseCreationEligibility   epifitemporal.Activity = "CheckCaseCreationEligibility"
	AcquireDistributedLockForAlert epifitemporal.Activity = "AcquireDistributedLockForAlert"
	CheckExistingCase              epifitemporal.Activity = "CheckExistingCase"
	CheckCaseAppendEligibility     epifitemporal.Activity = "CheckCaseAppendEligibility"
	CreateCaseForAlert             epifitemporal.Activity = "CreateCaseForAlert"
	UpdateAlert                    epifitemporal.Activity = "UpdateAlert"
	UpdateCaseForAlert             epifitemporal.Activity = "UpdateCaseForAlert"
	ReleaseDistributedLockForAlert epifitemporal.Activity = "ReleaseDistributedLockForAlert"
	ValidateBankAction             epifitemporal.Activity = "ValidateBankAction"
	UpdateCase                     epifitemporal.Activity = "UpdateCase"
	ApplyOnboardingReviewVerdict   epifitemporal.Activity = "ApplyOnboardingReviewVerdict"
	AddLivenessRetries             epifitemporal.Activity = "AddLivenessRetries"
	UnsnoozeCase                   epifitemporal.Activity = "UnsnoozeCase"
	GetExtendedRule                epifitemporal.Activity = "GetExtendedRule"
	GetAutoActions                 epifitemporal.Activity = "GetAutoActions"
	AddComment                     epifitemporal.Activity = "AddComment"
	ApplyAFUReviewVerdict          epifitemporal.Activity = "ApplyAFUReviewVerdict"
	GetAlerts                      epifitemporal.Activity = "GetAlerts"
	GetCase                        epifitemporal.Activity = "GetCase"
	ShouldExcludeReview            epifitemporal.Activity = "ShouldExcludeReview"
	MoveToReviewActivity           epifitemporal.Activity = "MoveToReview"
	ApplyScreenerVerdict           epifitemporal.Activity = "ApplyScreenerVerdict"
	SendOutcallReminderComms       epifitemporal.Activity = "SendOutcallReminderComms"
	UpdateAction                   epifitemporal.Activity = "UpdateAction"
	CreateForm                     epifitemporal.Activity = "CreateForm"
	GetForm                        epifitemporal.Activity = "GetForm"
	UpdateForm                     epifitemporal.Activity = "UpdateForm"
	GenerateQuestionnaire          epifitemporal.Activity = "GenerateQuestionnaire"
	GetOutcallNotifications        epifitemporal.Activity = "GetOutcallNotifications"
	SendForm                       epifitemporal.Activity = "SendForm"
	DetermineActionProcessing      epifitemporal.Activity = "DetermineActionProcessing"
	SendForSecondaryReview         epifitemporal.Activity = "SendForSecondaryReview"
	GetCXTicket                    epifitemporal.Activity = "GetCXTicket"
	UpdateCXTickets                epifitemporal.Activity = "UpdateCXTickets"
	DetermineEscalationHandling    epifitemporal.Activity = "DetermineEscalationHandling"
	CreateEscalationRawAlertObject epifitemporal.Activity = "CreateEscalationRawAlertObject"
	AppendDetailsToCXTicket        epifitemporal.Activity = "AppendDetailsToCXTicket"
	GetActorType                   epifitemporal.Activity = "GetActorType"

	// unified LEA complaints activity
	ValidateAndGetLeaHandler epifitemporal.Activity = "ValidateAndGetLeaHandler"
	GetUnifiedLeaComms       epifitemporal.Activity = "GetUnifiedLEAComms"
	CreateAsyncAlerts        epifitemporal.Activity = "CreateAsyncAlerts"
	AppAccessUpdate          epifitemporal.Activity = "AppAccessUpdate"

	// Apply lien workflow activities
	CheckForExistingLien epifitemporal.Activity = "CheckForExistingLien"
	AddLien              epifitemporal.Activity = "AddLien"
	VerifyLien           epifitemporal.Activity = "VerifyLien"
	UpdateBankAction     epifitemporal.Activity = "UpdateBankAction"
	GetLienComms         epifitemporal.Activity = "GetLienComms"
	SendComms            epifitemporal.Activity = "SendComms"
)

const (
	TotalFreezeAccountDetailsSentSignal  epifitemporal.Signal = "TotalFreezeAccountDetailsSentSignal"
	CreditFreezeAccountDetailsSentSignal epifitemporal.Signal = "CreditFreezeAccountDetailsSentSignal"
	UnfreezeAccountDetailsSentSignal     epifitemporal.Signal = "UnfreezeAccountDetailsSentSignal"
	DebitFreezeAccountDetailsSentSignal  epifitemporal.Signal = "DebitFreezeAccountDetailsSentSignal"
	OrchestrateFormSubmissionSignal      epifitemporal.Signal = "OrchestrateFormSubmissionSignal"
	BankActionsReminderSignal            epifitemporal.Signal = "BankActionsReminderSignal"
	LEAReminderSignal                    epifitemporal.Signal = "LEAReminderSignal"
)

const (
	FullFreezeValidateBankAction   epifitemporal.Stage = "ValidateBankAction"
	FullFreezeSpawnBankActionChild epifitemporal.Stage = "SpawnBankActionChild"

	CreditFreezeValidateBankAction   epifitemporal.Stage = "ValidateBankAction"
	CreditFreezeSpawnBankActionChild epifitemporal.Stage = "SpawnBankActionChild"

	UnfreezeValidateBankAction   epifitemporal.Stage = "ValidateBankAction"
	UnfreezeSpawnBankActionChild epifitemporal.Stage = "SpawnBankActionChild"

	DebitFreezeValidateBankAction   epifitemporal.Stage = "ValidateBankAction"
	DebitFreezeSpawnBankActionChild epifitemporal.Stage = "SpawnBankActionChild"

	BankActionUnfreezeAddToBankAction epifitemporal.Stage = "AddToBankAction"
	BankActionUnfreezeWaitForSignal   epifitemporal.Stage = "WaitForSignal"
	BankActionUnfreezeEnquireStatus   epifitemporal.Stage = "EnquireStatus"
	BankActionUnfreezeUpdateAppState  epifitemporal.Stage = "UpdateAppState"
	BankActionUnfreezeNotify          epifitemporal.Stage = "Notify"

	BankActionCreditFreezeAddToBankAction epifitemporal.Stage = "AddToBankAction"
	BankActionCreditFreezeWaitForSignal   epifitemporal.Stage = "WaitForSignal"
	BankActionCreditFreezeEnquireStatus   epifitemporal.Stage = "EnquireStatus"
	BankActionCreditFreezeUpdateAppState  epifitemporal.Stage = "UpdateAppState"
	BankActionCreditFreezeNotify          epifitemporal.Stage = "Notify"

	BankActionFullFreezeAddToBankAction epifitemporal.Stage = "AddToBankAction"
	BankActionFullFreezeWaitForSignal   epifitemporal.Stage = "WaitForSignal"
	BankActionFullFreezeEnquireStatus   epifitemporal.Stage = "EnquireStatus"
	BankActionFullFreezeUpdateAppState  epifitemporal.Stage = "UpdateAppState"
	BankActionFullFreezeNotify          epifitemporal.Stage = "Notify"

	BankActionDebitFreezeAddToBankAction epifitemporal.Stage = "AddToBankAction"
	BankActionDebitFreezeWaitForSignal   epifitemporal.Stage = "WaitForSignal"
	BankActionDebitFreezeEnquireStatus   epifitemporal.Stage = "EnquireStatus"
	BankActionDebitFreezeUpdateAppState  epifitemporal.Stage = "UpdateAppState"
	BankActionDebitFreezeNotify          epifitemporal.Stage = "Notify"

	BankActionReminderGetReminderPoint epifitemporal.Stage = "GetReminder"
	BankActionReminderTriggerStage     epifitemporal.Stage = "ReminderTrigger"

	ApplyOnboardingVerdict epifitemporal.Stage = "ApplyOnboardingVerdict"
	ProcessScreenerVerdict epifitemporal.Stage = "ProcessScreenerVerdict"
	ApplyLivenessRetry     epifitemporal.Stage = "ApplyLivenessRetry"

	// request user info workflow stages
	RequestUserInfoSendForm          epifitemporal.Stage = "SendForm"
	RequestUserInfoPostOutcallAction epifitemporal.Stage = "PostOutcallAction"
	RequestUserInfoUpdateCase        epifitemporal.Stage = "UpdateCase"
	RequestUserInfoGetCase           epifitemporal.Stage = "GetCase"

	// move to review workflow stages
	MoveToReviewUpdateCase epifitemporal.Stage = "UpdateCase"
	MoveToReviewGetCase    epifitemporal.Stage = "GetCase"

	// Upsert case workflow stages
	UpsertCaseDeduplication            epifitemporal.Stage = "Deduplication"
	UpsertCaseUpdation                 epifitemporal.Stage = "Updation"
	UpsertCaseCreation                 epifitemporal.Stage = "Creation"
	UpsertCaseTriggerAutoAction        epifitemporal.Stage = "TriggerAutoAction"
	UpsertCaseMoveToManualIntervention epifitemporal.Stage = "MoveToManualIntervention"

	// snooze workflow stages
	SnoozeCreate epifitemporal.Stage = "Create"
	SnoozeSleep  epifitemporal.Stage = "Sleep"
	SnoozeRemove epifitemporal.Stage = "Remove"

	// Execute Auto Action stages
	ExecuteAutoActionsSleep                epifitemporal.Stage = "ExecuteAutoActionsSleep"
	ExecuteAutoActionsCaseActionInProgress epifitemporal.Stage = "ExecuteAutoActionsCaseActionInProgress"
	ExecuteAutoActionsMoveToManualReview   epifitemporal.Stage = "ExecuteAutoActionsMoveToManualReview"
	ExecuteAutoActionsGetFinalActions      epifitemporal.Stage = "ExecuteAutoActionsGetFinalActions"
	ExecuteAutoActionsAddComments          epifitemporal.Stage = "ExecuteAutoActionsAddComments"
	ExecuteAutoActionsExecute              epifitemporal.Stage = "ExecuteAutoActionsExecute"
	ExecuteAutoActionsGetCase              epifitemporal.Stage = "ExecuteAutoActionsGetCase"
	ExecuteAutoActionsSendCommunication    epifitemporal.Stage = "ExecuteAutoActionsSendCommunication"

	// process afu verdict workflow stages
	ProcessAFUReviewVerdictApplyVerdict epifitemporal.Stage = "ApplyVerdict"
	ProcessAFUReviewVerdictGetAlerts    epifitemporal.Stage = "GetAlerts"

	// request user info workflow stages
	CheckReviewExclusionStatus epifitemporal.Stage = "CheckReviewExclusionStatus"

	// Process review action workflow stages
	ProcessReviewActionLogActionInDB       epifitemporal.Stage = "LogActionInDB"
	ProcessReviewActionDetermineProcessing epifitemporal.Stage = "DetermineProcessing"
	ProcessReviewActionProcess             epifitemporal.Stage = "Process"

	// Orchestrate form workflwo stages
	OrchestrateFormGenerateForm                epifitemporal.Stage = "GenerateForm"
	OrchestrateFormSendForm                    epifitemporal.Stage = "SendForm"
	OrchestrateFormWaitForSubmissionTillExpiry epifitemporal.Stage = "WaitForSubmissionTillExpiry"

	// Reject Escalation workflow stages
	RejectEscalationGetCxTicket     epifitemporal.Stage = "GetCxTicket"
	RejectEscalationUpdateCxTickets epifitemporal.Stage = "UpdateCxTickets"

	// Process escalation workflow stages
	DetermineHandling               epifitemporal.Stage = "DetermineHandling"
	CreateRawAlertObject            epifitemporal.Stage = "CreateRawAlertObject"
	ExecuteAppendDetailsToCXTicket  epifitemporal.Stage = "ExecuteAppendDetailsToCXTicket"
	ProcessEscalationUpdateCXTicket epifitemporal.Stage = "UpdateCxTicket"

	// Process unified LEA complaint
	ValidateAndGetUnifiedLeaComplaintHandler epifitemporal.Stage = "ValidateAndGetUnifiedLeaComplaintHandler"
	AppAccessUpdation                        epifitemporal.Stage = "AppAccessUpdation"
	GetUnifiedComms                          epifitemporal.Stage = "GetUnifiedComms"
	SendUnifiedComms                         epifitemporal.Stage = "SendUnifiedComms"
	CreateAlertsAsync                        epifitemporal.Stage = "CreateAlertsAsync"
	GetLEAReminderPoint                      epifitemporal.Stage = "GetLEAReminderPoint"

	// Action apply lien workflow stages
	ApplyLienValidateBankAction   = "ApplyLienValidateBankAction"
	ApplyLienSpawnBankActionChild = "ApplyLienSpawnBankActionChild"

	// Risk bank action apply lien workflow stages
	RiskBankActionApplyLienCheckForExistingLien epifitemporal.Stage = "RiskBankActionCheckForExistingLien"
	RiskBankActionApplyLienAddLien              epifitemporal.Stage = "RiskBankActionAddLien"
	RiskBankActionApplyLienVerifyLien           epifitemporal.Stage = "RiskBankActionVerifyLien"
	RiskBankActionApplyLienAddToBankAction      epifitemporal.Stage = "RiskBankActionAddToBankAction"
	RiskBankActionApplyLienSendComms            epifitemporal.Stage = "RiskBankActionSendComms"
)
