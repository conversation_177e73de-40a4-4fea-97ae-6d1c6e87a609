package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/google/uuid"
	"go.uber.org/zap"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	examplesNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/examples"
	"github.com/epifi/be-common/pkg/logger"
)

/*
Script to initiate a workflow using celestial RPC
*/
func main() {
	logger.Init(cfg.DevelopmentEnv)
	conn := epifigrpc.NewConn("localhost:9519")
	defer epifigrpc.CloseConn(conn)

	celestialClient := celestialPb.NewCelestialClient(conn)
	resp, err := celestialClient.InitiateWorkflow(context.Background(), &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: "actor-1",
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(examplesNs.SampleScalarWorkflow),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     uuid.NewString(),
				Client: workflowPb.Client_PAY,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
	})
	err = epifigrpc.RPCError(resp, err)
	if err != nil {
		logger.Panic("RPC failed", zap.Error(err))
	}

	logger.InfoNoCtx("RPC succeeded")
}
