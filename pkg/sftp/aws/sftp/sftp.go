package sftp

import (
	"bytes"
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"io"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/pkg/sftp"
)

// awsSftp provides aws sftp backed implementation of the sftp.Sftp interface
// It takes in cryptor for decrypting file data based on the encryption
// If cryptor is not passed we will consider that the file data isn't encrypted
type awsSftp struct {
	s3Client s3.S3Client
	cryptor  crypto.Cryptor
}

type AwsSftpBucketConfig struct {
	AwsBucket string `iam:"s3-readwrite"`
	AclString string
	SrcFolder string
	DstFolder string
}

func NewAwsSftp(s3Client s3.S3Client, cryptor crypto.Cryptor) *awsSftp {
	return &awsSftp{
		s3Client: s3Client,
		cryptor:  cryptor,
	}
}

var AwsSftpWireSet = wire.NewSet(NewAwsSftp, wire.Bind(new(sftp.Sftp), new(*awsSftp)))

func (a *awsSftp) ReadFile(ctx context.Context, uploadedFilePath string, delimiter rune) ([][]string, error) {
	records, err := a.readCsvFromS3AndParse(ctx, uploadedFilePath, delimiter)
	if err != nil {
		return nil, fmt.Errorf("error in reading file from s3..file path %s %w", uploadedFilePath, err)
	}
	return records, nil
}

// MoveFilePostRead moves read file to destination folder specified in the SftpAwsBucketConfig config
func (a *awsSftp) MoveFilePostRead(ctx context.Context, uploadedFilePath, destinationFilePath, aclString string) error {
	err := a.s3Client.MV(ctx, uploadedFilePath, destinationFilePath,
		aclString)
	if err != nil {
		return fmt.Errorf("failed to move file current path %s to destination path %s %w", uploadedFilePath,
			destinationFilePath, err)
	}
	return nil
}

// readCsvFromS3AndParse do below steps:
// 1. read csv data from s3 bucket
// 2. Decrypt data using cryptor if cryptor is not nil
// 3. Parse csv and return records against each line of csv. We will return the record on best effort basis, in case
// of error in parsing any record we will log the error
// Returns error if any error occurred in performing above steps
func (a *awsSftp) readCsvFromS3AndParse(ctx context.Context, fullFilePath string, delimiter rune) ([][]string, error) {
	var (
		data    []byte
		err     error
		records [][]string
		record  []string
	)
	// read csv file from s3
	data, err = a.s3Client.Read(ctx, fullFilePath)
	if err != nil {
		return nil, fmt.Errorf("error in reading csv file name %v %w", err, epifierrors.ErrTransient)
	}

	if a.cryptor != nil {
		decryptedData, decryptErr := a.cryptor.DecryptAndVerify(ctx, data, "")
		if decryptErr != nil {
			return nil, fmt.Errorf("error in decrypting %v %w", decryptErr, epifierrors.ErrPermanent)
		}
		data = decryptedData
	}

	csvReader := csv.NewReader(bytes.NewReader(data))
	csvReader.Comma = delimiter
	for {
		record, err = csvReader.Read()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			logger.Error(ctx, "error in reading record from csv", zap.Error(err))
		} else {
			records = append(records, record)
		}
	}
	return records, nil
}
