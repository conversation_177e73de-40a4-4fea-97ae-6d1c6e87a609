package usstocks

import (
	"github.com/shopspring/decimal"
	"google.golang.org/genproto/googleapis/type/date"
)

// ForexRate represents the rate of exchange of 1 USD to INR on a given date
type ForexRate struct {
	Date *date.Date

	Rate decimal.Decimal
}

// Historical values of USD-INR exchange rates averaged at a monthly level
// Source: https://in.investing.com/currencies/usd-inr-historical-data
// Note: Values are intentionally ordered by date to retrieve the latest and oldest value easily.
// New values should be added at the beginning of the slice.
var monthlyForexRates = []*ForexRate{
	{Date: &date.Date{Year: 2024, Month: 11, Day: 1}, Rate: decimal.NewFromFloat(84.08)},
	{Date: &date.Date{Year: 2024, Month: 10, Day: 1}, Rate: decimal.NewFromFloat(84.065)},
	{Date: &date.Date{Year: 2024, Month: 9, Day: 1}, Rate: decimal.NewFromFloat(83.755)},
	{Date: &date.Date{Year: 2024, Month: 8, Day: 1}, Rate: decimal.NewFromFloat(83.867)},
	{Date: &date.Date{Year: 2024, Month: 7, Day: 1}, Rate: decimal.NewFromFloat(83.699)},
	{Date: &date.Date{Year: 2024, Month: 6, Day: 1}, Rate: decimal.NewFromFloat(83.355)},
	{Date: &date.Date{Year: 2024, Month: 5, Day: 1}, Rate: decimal.NewFromFloat(83.424)},
	{Date: &date.Date{Year: 2024, Month: 4, Day: 1}, Rate: decimal.NewFromFloat(83.45)},
	{Date: &date.Date{Year: 2024, Month: 3, Day: 1}, Rate: decimal.NewFromFloat(83.35)},
	{Date: &date.Date{Year: 2024, Month: 2, Day: 1}, Rate: decimal.NewFromFloat(82.9)},
	{Date: &date.Date{Year: 2024, Month: 1, Day: 1}, Rate: decimal.NewFromFloat(83.095)},
	{Date: &date.Date{Year: 2023, Month: 12, Day: 1}, Rate: decimal.NewFromFloat(83.19)},
	{Date: &date.Date{Year: 2023, Month: 11, Day: 1}, Rate: decimal.NewFromFloat(83.357)},
	{Date: &date.Date{Year: 2023, Month: 10, Day: 1}, Rate: decimal.NewFromFloat(83.256)},
	{Date: &date.Date{Year: 2023, Month: 9, Day: 1}, Rate: decimal.NewFromFloat(83.03)},
	{Date: &date.Date{Year: 2023, Month: 8, Day: 1}, Rate: decimal.NewFromFloat(82.702)},
	{Date: &date.Date{Year: 2023, Month: 7, Day: 1}, Rate: decimal.NewFromFloat(82.24)},
	{Date: &date.Date{Year: 2023, Month: 6, Day: 1}, Rate: decimal.NewFromFloat(82.091)},
	{Date: &date.Date{Year: 2023, Month: 5, Day: 1}, Rate: decimal.NewFromFloat(82.68)},
	{Date: &date.Date{Year: 2023, Month: 4, Day: 1}, Rate: decimal.NewFromFloat(81.72)},
	{Date: &date.Date{Year: 2023, Month: 3, Day: 1}, Rate: decimal.NewFromFloat(82.16)},
	{Date: &date.Date{Year: 2023, Month: 2, Day: 1}, Rate: decimal.NewFromFloat(82.64)},
	{Date: &date.Date{Year: 2023, Month: 1, Day: 1}, Rate: decimal.NewFromFloat(81.739)},
	{Date: &date.Date{Year: 2022, Month: 12, Day: 1}, Rate: decimal.NewFromFloat(82.717)},
	{Date: &date.Date{Year: 2022, Month: 11, Day: 1}, Rate: decimal.NewFromFloat(81.359)},
	{Date: &date.Date{Year: 2022, Month: 10, Day: 1}, Rate: decimal.NewFromFloat(82.77)},
	{Date: &date.Date{Year: 2022, Month: 9, Day: 1}, Rate: decimal.NewFromFloat(81.509)},
	{Date: &date.Date{Year: 2022, Month: 8, Day: 1}, Rate: decimal.NewFromFloat(79.491)},
	{Date: &date.Date{Year: 2022, Month: 7, Day: 1}, Rate: decimal.NewFromFloat(79.336)},
	{Date: &date.Date{Year: 2022, Month: 6, Day: 1}, Rate: decimal.NewFromFloat(78.95)},
	{Date: &date.Date{Year: 2022, Month: 5, Day: 1}, Rate: decimal.NewFromFloat(77.569)},
	{Date: &date.Date{Year: 2022, Month: 4, Day: 1}, Rate: decimal.NewFromFloat(76.52)},
	{Date: &date.Date{Year: 2022, Month: 3, Day: 1}, Rate: decimal.NewFromFloat(75.901)},
	{Date: &date.Date{Year: 2022, Month: 2, Day: 1}, Rate: decimal.NewFromFloat(75.493)},
	{Date: &date.Date{Year: 2022, Month: 1, Day: 1}, Rate: decimal.NewFromFloat(74.529)},
	{Date: &date.Date{Year: 2021, Month: 12, Day: 1}, Rate: decimal.NewFromFloat(74.467)},
	{Date: &date.Date{Year: 2021, Month: 11, Day: 1}, Rate: decimal.NewFromFloat(75.09)},
	{Date: &date.Date{Year: 2021, Month: 10, Day: 1}, Rate: decimal.NewFromFloat(74.915)},
	{Date: &date.Date{Year: 2021, Month: 9, Day: 1}, Rate: decimal.NewFromFloat(74.164)},
	{Date: &date.Date{Year: 2021, Month: 8, Day: 1}, Rate: decimal.NewFromFloat(72.947)},
	{Date: &date.Date{Year: 2021, Month: 7, Day: 1}, Rate: decimal.NewFromFloat(74.337)},
	{Date: &date.Date{Year: 2021, Month: 6, Day: 1}, Rate: decimal.NewFromFloat(74.36)},
	{Date: &date.Date{Year: 2021, Month: 5, Day: 1}, Rate: decimal.NewFromFloat(72.511)},
	{Date: &date.Date{Year: 2021, Month: 4, Day: 1}, Rate: decimal.NewFromFloat(74.05)},
	{Date: &date.Date{Year: 2021, Month: 3, Day: 1}, Rate: decimal.NewFromFloat(73.137)},
	{Date: &date.Date{Year: 2021, Month: 2, Day: 1}, Rate: decimal.NewFromFloat(73.92)},
	{Date: &date.Date{Year: 2021, Month: 1, Day: 1}, Rate: decimal.NewFromFloat(72.877)},
	{Date: &date.Date{Year: 2020, Month: 12, Day: 1}, Rate: decimal.NewFromFloat(73.036)},
	{Date: &date.Date{Year: 2020, Month: 11, Day: 1}, Rate: decimal.NewFromFloat(73.99)},
	{Date: &date.Date{Year: 2020, Month: 10, Day: 1}, Rate: decimal.NewFromFloat(74.554)},
	{Date: &date.Date{Year: 2020, Month: 9, Day: 1}, Rate: decimal.NewFromFloat(73.56)},
	{Date: &date.Date{Year: 2020, Month: 8, Day: 1}, Rate: decimal.NewFromFloat(73.254)},
	{Date: &date.Date{Year: 2020, Month: 7, Day: 1}, Rate: decimal.NewFromFloat(74.916)},
	{Date: &date.Date{Year: 2020, Month: 6, Day: 1}, Rate: decimal.NewFromFloat(75.54)},
	{Date: &date.Date{Year: 2020, Month: 5, Day: 1}, Rate: decimal.NewFromFloat(75.59)},
	{Date: &date.Date{Year: 2020, Month: 4, Day: 1}, Rate: decimal.NewFromFloat(75.077)},
	{Date: &date.Date{Year: 2020, Month: 3, Day: 1}, Rate: decimal.NewFromFloat(75.333)},
	{Date: &date.Date{Year: 2020, Month: 2, Day: 1}, Rate: decimal.NewFromFloat(72.534)},
	{Date: &date.Date{Year: 2020, Month: 1, Day: 1}, Rate: decimal.NewFromFloat(71.54)},
	{Date: &date.Date{Year: 2019, Month: 12, Day: 1}, Rate: decimal.NewFromFloat(71.35)},
	{Date: &date.Date{Year: 2019, Month: 11, Day: 1}, Rate: decimal.NewFromFloat(71.746)},
	{Date: &date.Date{Year: 2019, Month: 10, Day: 1}, Rate: decimal.NewFromFloat(70.978)},
	{Date: &date.Date{Year: 2019, Month: 9, Day: 1}, Rate: decimal.NewFromFloat(70.64)},
	{Date: &date.Date{Year: 2019, Month: 8, Day: 1}, Rate: decimal.NewFromFloat(71.451)},
	{Date: &date.Date{Year: 2019, Month: 7, Day: 1}, Rate: decimal.NewFromFloat(68.86)},
	{Date: &date.Date{Year: 2019, Month: 6, Day: 1}, Rate: decimal.NewFromFloat(68.94)},
	{Date: &date.Date{Year: 2019, Month: 5, Day: 1}, Rate: decimal.NewFromFloat(69.57)},
	{Date: &date.Date{Year: 2019, Month: 4, Day: 1}, Rate: decimal.NewFromFloat(69.636)},
	{Date: &date.Date{Year: 2019, Month: 3, Day: 1}, Rate: decimal.NewFromFloat(69.18)},
	{Date: &date.Date{Year: 2019, Month: 2, Day: 1}, Rate: decimal.NewFromFloat(70.83)},
	{Date: &date.Date{Year: 2019, Month: 1, Day: 1}, Rate: decimal.NewFromFloat(70.95)},
	{Date: &date.Date{Year: 2018, Month: 12, Day: 1}, Rate: decimal.NewFromFloat(69.56)},
	{Date: &date.Date{Year: 2018, Month: 11, Day: 1}, Rate: decimal.NewFromFloat(69.64)},
	{Date: &date.Date{Year: 2018, Month: 10, Day: 1}, Rate: decimal.NewFromFloat(73.95)},
	{Date: &date.Date{Year: 2018, Month: 9, Day: 1}, Rate: decimal.NewFromFloat(72.5)},
	{Date: &date.Date{Year: 2018, Month: 8, Day: 1}, Rate: decimal.NewFromFloat(71)},
	{Date: &date.Date{Year: 2018, Month: 7, Day: 1}, Rate: decimal.NewFromFloat(68.45)},
	{Date: &date.Date{Year: 2018, Month: 6, Day: 1}, Rate: decimal.NewFromFloat(68.45)},
	{Date: &date.Date{Year: 2018, Month: 5, Day: 1}, Rate: decimal.NewFromFloat(67.42)},
	{Date: &date.Date{Year: 2018, Month: 4, Day: 1}, Rate: decimal.NewFromFloat(66.45)},
	{Date: &date.Date{Year: 2018, Month: 3, Day: 1}, Rate: decimal.NewFromFloat(65.11)},
	{Date: &date.Date{Year: 2018, Month: 2, Day: 1}, Rate: decimal.NewFromFloat(65.2)},
	{Date: &date.Date{Year: 2018, Month: 1, Day: 1}, Rate: decimal.NewFromFloat(63.54)},
}
