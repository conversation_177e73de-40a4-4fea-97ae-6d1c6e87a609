package actorcapturetrace

import (
	"context"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc/metadata"
)

const traceRequestKey = "trace_actor_request"

func setAttrToSpanAndOutgoingMetadata(ctx context.Context) context.Context {
	// if otel is not enabled, this returns a noop span and does not hamper flow
	span := trace.SpanFromContext(ctx)
	span.SetAttributes(attribute.Bool(traceRequestKey, true))

	return metadata.AppendToOutgoingContext(ctx, traceRequestKey, "true")
}

func isTraceKeyPresentInMetadata(ctx context.Context) bool {
	traceReqVal := metadata.ValueFromIncomingContext(ctx, traceRequestKey)
	return len(traceReqVal) == 1 && traceReqVal[0] == "true"
}
