// Package roarray implements a Read Only array.
package roarray

// ROArray is an immutable array which allows only to read values by indexing.
// ROArray doesn't guarantee immutability of the value stored in the array.
type ROArray[V any] struct {
	arr []V
}

// New creates a new array object with elements from the given array.
func New[V any](vals []V) ROArray[V] {
	// copy array to a new array object.
	// This is done to avoid impact of mutation on the array passed as input to this function in future by the caller.
	return ROArray[V]{arr: append([]V{}, vals...)}
}

// Len returns the length of the array
func (a ROArray[V]) Len() int { return len(a.arr) }

// At returns the element at the given index. It is just a wrapper for <PERSON>'s default indexing operation.
func (a ROArray[V]) At(index int) V { return a.arr[index] }

// Slice returns a copy of ROArray in the form of a slice
func (a ROArray[V]) Slice() []V { return append([]V{}, a.arr...) }

// ToStringArray converts ROArray of string to a string array
func (a ROArray[string]) ToStringArray() []string {
	var result []string
	for i := 0; i < a.Len(); i++ {
		result = append(result, a.At(i))
	}
	return result
}
