package lock

import (
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/golang/mock/gomock"
	"github.com/jonboulle/clockwork"
	"github.com/redis/go-redis/v9"

	mockIdGen "github.com/epifi/be-common/pkg/idgen/mocks"
	"github.com/epifi/be-common/pkg/lock/test"
)

var (
	redisClient *redis.Client
	mockClient  redismock.ClientMock
	uuidGen     *mockIdGen.MockIUuidGenerator
	teardown    func()
)

func TestMain(m *testing.M) {
	redisClient, mockClient, teardown = test.InitTestServer()

	exitCode := m.Run()
	// os.Exit does not respects deferred functions.
	teardown()
	os.Exit(exitCode)
}
func initMocks(ctrl *gomock.Controller) {
	fakeClock := clockwork.NewFakeClockAt(time.Now())
	uuidGen = mockIdGen.NewMockIUuidGenerator(ctrl)
	redisLockManager := NewRedisLockManager(redisClient, fakeClock, uuidGen)
	rlts = newRedisLockTestSuite(redisLockManager, fakeClock, mockClient, uuidGen)
	rts = newRedisV9LockTestSuite(redisLockManager, fakeClock, mockClient, uuidGen)
}
