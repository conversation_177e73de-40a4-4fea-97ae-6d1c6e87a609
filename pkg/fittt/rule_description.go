package fittt

import (
	"context"
	"strconv"
	"strings"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/money"
	rmsManagerPb "github.com/epifi/gamma/api/rms/manager"
)

type FitttPkg struct {
	usStocks IUSStocks
}

func NewFitttPkg(usStocks IUSStocks) *FitttPkg {
	return &FitttPkg{
		usStocks: usStocks,
	}
}

// PrepareDescriptionString returns description string from rule subscription
func (p *FitttPkg) PrepareDescriptionString(ctx context.Context, rule *rmsManagerPb.Rule, ruleParamValues *rmsManagerPb.RuleParamValues) (
	string, string, error) {
	descriptionText := rule.GetDescription().GetDisplayStr()
	shortDescriptionText := getShortRuleDisplayString(descriptionText)
	inputParams := rule.GetDescription().GetInputParams()

	for _, inputParam := range inputParams {
		nameKey := "{" + inputParam.GetName() + "}"
		ruleParamValue := ruleParamValues.GetRuleParamValues()[inputParam.GetName()]
		ruleParamValueString, err := p.getRuleParamValueString(ctx, ruleParamValue)
		if err != nil {
			return "", "", errors.Wrap(err, "error getting rule param value string")
		}
		descriptionText = strings.ReplaceAll(descriptionText, nameKey, ruleParamValueString)
		shortDescriptionText = strings.ReplaceAll(shortDescriptionText, nameKey, ruleParamValueString)
	}

	return descriptionText, shortDescriptionText, nil
}

// getShortRuleDisplayString gets short rule display string from rule display string
func getShortRuleDisplayString(ruleDisplayString string) string {
	// remove deposit account name in the display string
	ruleDisplayString = strings.ReplaceAll(ruleDisplayString, " into {depositAccountId}", "")
	ruleDisplayString = strings.ReplaceAll(ruleDisplayString, " in {depositAccountId}", "")
	return ruleDisplayString
}

// getRuleParamValueString returns formatted string from rule param value
func (p *FitttPkg) getRuleParamValueString(ctx context.Context, ruleParamValue *rmsManagerPb.Value) (string, error) {
	switch ruleParamValue.GetValue().(type) {
	case *rmsManagerPb.Value_StrVal:
		return ruleParamValue.GetStrVal(), nil
	case *rmsManagerPb.Value_IntVal:
		return strconv.FormatInt(int64(ruleParamValue.GetIntVal()), 10), nil
	case *rmsManagerPb.Value_DoubleVal:
		return strconv.FormatFloat(ruleParamValue.GetDoubleVal(), 'f', -1, 64), nil
	case *rmsManagerPb.Value_SdValue:
		return ruleParamValue.GetSdValue().GetName(), nil
	case *rmsManagerPb.Value_MoneyVal:
		return money.ToDisplayStringInIndianFormat(ruleParamValue.GetMoneyVal().GetBeMoney(), 0, true), nil
	case *rmsManagerPb.Value_MerchantVal:
		return ruleParamValue.GetMerchantVal().GetName(), nil
	case *rmsManagerPb.Value_PlayerVal:
		return ruleParamValue.GetPlayerVal().GetName(), nil
	case *rmsManagerPb.Value_TeamVal:
		return ruleParamValue.GetTeamVal().GetName(), nil
	case *rmsManagerPb.Value_FootballPlayerVal:
		return ruleParamValue.GetFootballPlayerVal().GetName(), nil
	case *rmsManagerPb.Value_FootballTeamVal:
		return ruleParamValue.GetFootballTeamVal().GetName(), nil
	case *rmsManagerPb.Value_AppVal:
		return ruleParamValue.GetAppVal().GetAppName(), nil
	case *rmsManagerPb.Value_Duration:
		return ruleParamValue.GetDuration().GetValue().String(), nil
	case *rmsManagerPb.Value_MutualFundVal:
		return ruleParamValue.GetMutualFundVal().GetName(), nil
	case *rmsManagerPb.Value_UsStockValue:
		stock, err := p.usStocks.GetStock(ctx, ruleParamValue.GetUsStockValue().GetStockId())
		if err != nil {
			return "", errors.Wrap(err, "error getting stock name")
		}
		return stock.Name, nil
	case *rmsManagerPb.Value_DateVal:
		return ruleParamValue.GetDateVal().GetDate().String(), nil
	default:
		// do nothing
	}
	return "-", nil
}
