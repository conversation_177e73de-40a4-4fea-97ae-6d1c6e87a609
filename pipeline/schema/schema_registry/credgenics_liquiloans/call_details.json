{"name": "call_details", "type": "record", "namespace": "com.epifi.dataplatform", "fields": [{"name": "campaign_name", "type": "string"}, {"name": "campaign_id", "type": "string"}, {"name": "loan_id", "type": "string"}, {"name": "dpd_bucket", "type": "string"}, {"name": "allocation_month", "type": "string"}, {"name": "product_type", "type": "string"}, {"name": "call_type", "type": "string"}, {"name": "agent_name", "type": "string"}, {"name": "agent_email_id", "type": "string"}, {"name": "did_number", "type": "string"}, {"name": "customer_contact_number", "type": "string"}, {"name": "contact_type", "type": "string"}, {"name": "borrower_type", "type": "string"}, {"name": "shoot_id", "type": "string"}, {"name": "call_start_time", "type": "string"}, {"name": "customer_leg_ringing_start_time", "type": "string"}, {"name": "customer_leg_ringing_end_time", "type": "string"}, {"name": "agent_leg_ringing_start_time", "type": "string"}, {"name": "agent_leg_ringing_end_time", "type": "string"}, {"name": "total_ringing_time_duration", "type": "string"}, {"name": "customer_call_pickup_time", "type": "string"}, {"name": "agent_call_pickup_time", "type": "string"}, {"name": "call_end_time", "type": "string"}, {"name": "total_talk_time_duration", "type": "string"}, {"name": "total_call_duration", "type": "string"}, {"name": "customer_wait_duration", "type": "string"}, {"name": "wrapup_start_time", "type": "string"}, {"name": "wrapup_end_time", "type": "string"}, {"name": "wrapup_duration", "type": "string"}, {"name": "call_disconnected_by", "type": "string"}, {"name": "call_disconnection_stage", "type": "string"}, {"name": "call_status", "type": "string"}, {"name": "disposition", "type": "string"}, {"name": "sub_disposition_1", "type": "string"}, {"name": "sub_disposition_2", "type": "string"}, {"name": "dialer_disposition", "type": "string"}, {"name": "call_transfer_from", "type": "string"}, {"name": "call_transfer_to", "type": "string"}, {"name": "call_transfer_type", "type": "string"}, {"name": "call_response", "type": "string"}, {"name": "reminder_date", "type": "string"}, {"name": "committed_amount", "type": "string"}, {"name": "role", "type": "string"}, {"name": "recording_link", "type": "string"}, {"name": "low_risk_defaulter", "type": "string"}, {"name": "reason_for_defaulter", "type": "string"}, {"name": "intention_to_pay", "type": "string"}, {"name": "customer_tone", "type": "string"}, {"name": "aware_of_default_consequences", "type": "string"}, {"name": "educated", "type": "string"}, {"name": "manager_to_review", "type": "string"}]}