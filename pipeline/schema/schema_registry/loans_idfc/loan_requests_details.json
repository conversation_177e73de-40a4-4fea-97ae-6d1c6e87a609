{"name": "MyClass", "type": "record", "namespace": "com.acme.avro", "fields": [{"name": "loanInfo", "type": {"name": "loanInfo", "type": "record", "fields": [{"name": "amount", "type": {"name": "amount", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "units", "type": "string"}]}}, {"name": "deductions", "type": {"name": "deductions", "type": "record", "fields": [{"name": "advanceInterest", "type": {"name": "advanceInterest", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "int"}, {"name": "units", "type": "string"}]}}, {"name": "gst", "type": {"name": "gst", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "units", "type": "string"}]}}, {"name": "processingFee", "type": {"name": "processingFee", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "int"}, {"name": "units", "type": "string"}]}}, {"name": "totalDeductions", "type": {"name": "totalDeductions", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "int"}, {"name": "units", "type": "string"}]}}]}}, {"name": "disbursalAmount", "type": {"name": "disbursalAmount", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "nanos", "type": "int"}, {"name": "units", "type": "string"}]}}, {"name": "emiAmount", "type": {"name": "emiAmount", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "units", "type": "string"}]}}, {"name": "interestRate", "type": "int"}, {"name": "tenureInMonths", "type": "int"}, {"name": "totalPayable", "type": {"name": "totalPayable", "type": "record", "fields": [{"name": "currencyCode", "type": "string"}, {"name": "units", "type": "string"}]}}]}}, {"name": "maskedAccountNumber", "type": "string"}, {"name": "otpInfo", "type": {"name": "otpInfo", "type": "record", "fields": [{"name": "maxAttempts", "type": "int"}]}}, {"name": "phoneNumber", "type": {"name": "phoneNumber", "type": "record", "fields": [{"name": "countryCode", "type": "int"}, {"name": "nationalNumber", "type": "string"}]}}]}