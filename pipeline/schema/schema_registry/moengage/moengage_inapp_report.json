{"name": "MyClass", "type": "record", "namespace": "com.acme.avro", "fields": [{"name": "row_id", "type": ["string", "null"]}, {"name": "campaign_id", "type": ["string", "null"]}, {"name": "date", "type": ["string", "null"]}, {"name": "campaign_name", "type": ["string", "null"]}, {"name": "parent_campaign_id", "type": ["string", "null"]}, {"name": "campaign_type", "type": ["string", "null"]}, {"name": "campaign_channel", "type": ["string", "null"]}, {"name": "created_by", "type": ["string", "null"]}, {"name": "campaign_status", "type": ["string", "null"]}, {"name": "custom_segment_name", "type": ["string", "null"]}, {"name": "custom_segment_filters", "type": ["string", "null"]}, {"name": "platforms", "type": ["string", "null"]}, {"name": "campaign_start_time", "type": ["string", "null"]}, {"name": "campaign_expiry_time", "type": ["string", "null"]}, {"name": "delivery_controls_enabled", "type": ["string", "null"]}, {"name": "maximum_times_to_show_message_ever", "type": ["string", "null"]}, {"name": "minimum_delay_between_two_messages", "type": ["string", "null"]}, {"name": "ignore_global_delay_between_two_messages", "type": ["string", "null"]}, {"name": "auto_dismiss_time", "type": ["string", "null"]}, {"name": "android_template_id", "type": ["string", "null"]}, {"name": "ios_template_id", "type": ["string", "null"]}, {"name": "android_template_name", "type": ["string", "null"]}, {"name": "ios_template_name", "type": ["string", "null"]}, {"name": "android_template_type", "type": ["string", "null"]}, {"name": "ios_template_type", "type": ["string", "null"]}, {"name": "android_personalization", "type": ["string", "null"]}, {"name": "ios_personalization", "type": ["string", "null"]}, {"name": "android_show_on_screen", "type": ["string", "null"]}, {"name": "ios_show_on_screen", "type": ["string", "null"]}, {"name": "android_impressions", "type": ["string", "null"]}, {"name": "ios_impressions", "type": ["string", "null"]}, {"name": "all_platform_impressions", "type": ["string", "null"]}, {"name": "android_clicks", "type": ["string", "null"]}, {"name": "ios_clicks", "type": ["string", "null"]}, {"name": "all_platform_clicks", "type": ["string", "null"]}, {"name": "android_ctr", "type": ["string", "null"]}, {"name": "ios_ctr", "type": ["string", "null"]}, {"name": "all_platform_ctr", "type": ["string", "null"]}, {"name": "android_closed", "type": ["string", "null"]}, {"name": "ios_closed", "type": ["string", "null"]}, {"name": "all_platform_closed", "type": ["string", "null"]}, {"name": "android_campaign_attempted", "type": ["string", "null"]}, {"name": "ios_campaign_attempted", "type": ["string", "null"]}, {"name": "all_platform_campaign_attempted", "type": ["string", "null"]}, {"name": "android_campaign_prioritized", "type": ["string", "null"]}, {"name": "ios_campaign_prioritized", "type": ["string", "null"]}, {"name": "all_platform_campaign_prioritized", "type": ["string", "null"]}]}