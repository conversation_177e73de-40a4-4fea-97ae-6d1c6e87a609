{"name": "MyClass", "type": "record", "namespace": "com.acme.avro", "fields": [{"name": "row_id", "type": ["string", "null"]}, {"name": "campaign_id", "type": ["string", "null"]}, {"name": "date", "type": ["string", "null"]}, {"name": "campaign_name", "type": ["string", "null"]}, {"name": "parent_campaign_id", "type": ["string", "null"]}, {"name": "campaign_type", "type": ["string", "null"]}, {"name": "campaign_delivery_type", "type": ["string", "null"]}, {"name": "campaign_channel", "type": ["string", "null"]}, {"name": "tag_category_uncategorized", "type": ["string", "null"]}, {"name": "created_by", "type": ["string", "null"]}, {"name": "campaign_status", "type": ["string", "null"]}, {"name": "conversion_goal_1_name", "type": ["string", "null"]}, {"name": "conversion_goal_2_name", "type": ["string", "null"]}, {"name": "conversion_goal_3_name", "type": ["string", "null"]}, {"name": "conversion_goal_4_name", "type": ["string", "null"]}, {"name": "conversion_goal_5_name", "type": ["string", "null"]}, {"name": "conversion_goal_1_event", "type": ["string", "null"]}, {"name": "conversion_goal_2_event", "type": ["string", "null"]}, {"name": "conversion_goal_3_event", "type": ["string", "null"]}, {"name": "conversion_goal_4_event", "type": ["string", "null"]}, {"name": "conversion_goal_5_event", "type": ["string", "null"]}, {"name": "conversion_goal_1_attribute", "type": ["string", "null"]}, {"name": "conversion_goal_2_attribute", "type": ["string", "null"]}, {"name": "conversion_goal_3_attribute", "type": ["string", "null"]}, {"name": "conversion_goal_4_attribute", "type": ["string", "null"]}, {"name": "conversion_goal_5_attribute", "type": ["string", "null"]}, {"name": "custom_segment_name", "type": ["string", "null"]}, {"name": "campaign_segment_filters", "type": ["string", "null"]}, {"name": "email_attribute", "type": ["string", "null"]}, {"name": "campaign_cg_percent", "type": ["string", "null"]}, {"name": "email_subject", "type": ["string", "null"]}, {"name": "template_id", "type": ["string", "null"]}, {"name": "template_name", "type": ["string", "null"]}, {"name": "from_email_address", "type": ["string", "null"]}, {"name": "reply_to_email_address", "type": ["string", "null"]}, {"name": "campaign_sent_time", "type": ["string", "null"]}, {"name": "campaign_sending_type", "type": ["string", "null"]}, {"name": "sent", "type": ["string", "null"]}, {"name": "total_sent", "type": ["string", "null"]}, {"name": "delivered", "type": ["string", "null"]}, {"name": "total_delivered", "type": ["string", "null"]}, {"name": "drops", "type": ["string", "null"]}, {"name": "unique_opens", "type": ["string", "null"]}, {"name": "total_open", "type": ["string", "null"]}, {"name": "unique_clicks", "type": ["string", "null"]}, {"name": "total_clicks", "type": ["string", "null"]}, {"name": "delivery_rate", "type": ["string", "null"]}, {"name": "open_rate", "type": ["string", "null"]}, {"name": "ctr", "type": ["string", "null"]}, {"name": "hard_bounces", "type": ["string", "null"]}, {"name": "total_hard_bounces", "type": ["string", "null"]}, {"name": "hard_bounce_rate", "type": ["string", "null"]}, {"name": "soft_bounces", "type": ["string", "null"]}, {"name": "total_soft_bounces", "type": ["string", "null"]}, {"name": "soft_bounce_rate", "type": ["string", "null"]}, {"name": "unsubscribes", "type": ["string", "null"]}, {"name": "unsubscribe_rate", "type": ["string", "null"]}, {"name": "complaints", "type": ["string", "null"]}, {"name": "complaints_rate", "type": ["string", "null"]}, {"name": "b_u_c_removed", "type": ["string", "null"]}, {"name": "duplicates_removed", "type": ["string", "null"]}, {"name": "invalid_emails", "type": ["string", "null"]}, {"name": "goal_1_view_through_converted_users", "type": ["string", "null"]}, {"name": "goal_2_view_through_converted_users", "type": ["string", "null"]}, {"name": "goal_3_view_through_converted_users", "type": ["string", "null"]}, {"name": "goal_4_view_through_converted_users", "type": ["string", "null"]}, {"name": "goal_5_view_through_converted_users", "type": ["string", "null"]}, {"name": "goal_1_view_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_2_view_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_3_view_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_4_view_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_5_view_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_1_view_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_2_view_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_3_view_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_4_view_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_5_view_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_1_view_through_cvr", "type": ["string", "null"]}, {"name": "goal_2_view_through_cvr", "type": ["string", "null"]}, {"name": "goal_3_view_through_cvr", "type": ["string", "null"]}, {"name": "goal_4_view_through_cvr", "type": ["string", "null"]}, {"name": "goal_5_view_through_cvr", "type": ["string", "null"]}, {"name": "goal_1_click_through_converted_users", "type": ["string", "null"]}, {"name": "goal_2_click_through_converted_users", "type": ["string", "null"]}, {"name": "goal_3_click_through_converted_users", "type": ["string", "null"]}, {"name": "goal_4_click_through_converted_users", "type": ["string", "null"]}, {"name": "goal_5_click_through_converted_users", "type": ["string", "null"]}, {"name": "goal_1_click_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_2_click_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_3_click_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_4_click_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_5_click_through_conversion_events", "type": ["string", "null"]}, {"name": "goal_1_click_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_2_click_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_3_click_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_4_click_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_5_click_through_total_revenue", "type": ["string", "null"]}, {"name": "goal_1_click_through_cvr", "type": ["string", "null"]}, {"name": "goal_2_click_through_cvr", "type": ["string", "null"]}, {"name": "goal_3_click_through_cvr", "type": ["string", "null"]}, {"name": "goal_4_click_through_cvr", "type": ["string", "null"]}, {"name": "goal_5_click_through_cvr", "type": ["string", "null"]}, {"name": "goal_1_in_session_converted_users", "type": ["string", "null"]}, {"name": "goal_2_in_session_converted_users", "type": ["string", "null"]}, {"name": "goal_3_in_session_converted_users", "type": ["string", "null"]}, {"name": "goal_4_in_session_converted_users", "type": ["string", "null"]}, {"name": "goal_5_in_session_converted_users", "type": ["string", "null"]}, {"name": "goal_1_in_session_conversion_events", "type": ["string", "null"]}, {"name": "goal_2_in_session_conversion_events", "type": ["string", "null"]}, {"name": "goal_3_in_session_conversion_events", "type": ["string", "null"]}, {"name": "goal_4_in_session_conversion_events", "type": ["string", "null"]}, {"name": "goal_5_in_session_conversion_events", "type": ["string", "null"]}, {"name": "goal_1_in_session_total_revenue", "type": ["string", "null"]}, {"name": "goal_2_in_session_total_revenue", "type": ["string", "null"]}, {"name": "goal_3_in_session_total_revenue", "type": ["string", "null"]}, {"name": "goal_4_in_session_total_revenue", "type": ["string", "null"]}, {"name": "goal_5_in_session_total_revenue", "type": ["string", "null"]}, {"name": "goal_1_in_session_cvr", "type": ["string", "null"]}, {"name": "goal_2_in_session_cvr", "type": ["string", "null"]}, {"name": "goal_3_in_session_cvr", "type": ["string", "null"]}, {"name": "goal_4_in_session_cvr", "type": ["string", "null"]}, {"name": "goal_5_in_session_cvr", "type": ["string", "null"]}]}