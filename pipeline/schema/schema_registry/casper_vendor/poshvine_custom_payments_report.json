{"name": "MyClass", "type": "record", "namespace": "com.acme.avro", "fields": [{"name": "payment_id", "type": "string"}, {"name": "amount", "type": "string"}, {"name": "currency", "type": "string"}, {"name": "status", "type": "string"}, {"name": "order_id", "type": "string"}, {"name": "invoice_id", "type": "string"}, {"name": "international", "type": "string"}, {"name": "payment_method", "type": "string"}, {"name": "amount_refunded", "type": "string"}, {"name": "amount_transferred", "type": "string"}, {"name": "refund_status", "type": "string"}, {"name": "captured", "type": "string"}, {"name": "description", "type": "string"}, {"name": "card_id", "type": "string"}, {"name": "card", "type": "string"}, {"name": "bank", "type": "string"}, {"name": "wallet", "type": "string"}, {"name": "vpa", "type": "string"}, {"name": "email", "type": "string"}, {"name": "contact", "type": "string"}, {"name": "notes", "type": "string"}, {"name": "fee", "type": "string"}, {"name": "tax", "type": "string"}, {"name": "error_code", "type": "string"}, {"name": "error_description", "type": "string"}, {"name": "created_at", "type": "string"}, {"name": "card_type", "type": "string"}, {"name": "card_network", "type": "string"}, {"name": "auth_code", "type": "string"}, {"name": "payments_arn", "type": "string"}, {"name": "payments_rrn", "type": "string"}, {"name": "flow", "type": "string"}]}