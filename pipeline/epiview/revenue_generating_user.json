{"name": "Finance_Base_Etl", "script_name": "revenue_generating_user", "database": "finance", "sfRole": "DNA_FINANCE_RW", "snowflake_schema_name": "BASIC_ANALYTICS_RGU", "snowflake_database": "FINANCE", "transactional": true, "version_bump": true, "tables": {"rev_variable": {"query_name": "rev_variable.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_deposits": {"query_name": "rev_deposits.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_mktg_support": {"query_name": "rev_mktg_support.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_onboarding": {"query_name": "rev_onboarding.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_replacement_card": {"query_name": "rev_replacement_card.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_savings_account": {"query_name": "rev_savings_account.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_eoq_card": {"query_name": "rev_eoq_card.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_jump": {"query_name": "rev_jump.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "finance_baseline_Switch_dates_domestic": {"query_name": "finance_baseline_Switch_dates_domestic.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "finance_baseline_Switch_dates_international": {"query_name": "finance_baseline_Switch_dates_international.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "finance_baseline_Switch_dates": {"query_name": "finance_baseline_Switch_dates.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_debit_card": {"query_name": "rev_debit_card.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_personal_loan": {"query_name": "rev_personal_loan.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_personal_loan_ll": {"query_name": "rev_personal_loan_ll.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "all_revenue_streams": {"query_name": "all_revenue_streams.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "revenue_user": {"query_name": "revenue_user.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "txn_revenue": {"query_name": "txn_revenue.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "tu_kyc": {"query_name": "tu_kyc.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rgu_flag_fin": {"query_name": "rgu_flag_fin.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}, "rev_jump_inprinciple": {"query_name": "rev_jump_inprinciple.sql", "primary_key": "unique_id", "updated_at_key": "unique_id"}}}