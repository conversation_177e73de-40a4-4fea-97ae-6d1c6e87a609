{"database": "bank_customer", "type": "entity", "snowflake_schema_name": "bank_customer", "org": "federal", "area": "bank_customer", "write_base_to_hudi": true, "tables": {"kyc_compliances": {"primary_key": "id", "updated_at_key": "updated_at", "transactional": true, "table_size": "mlarge", "snowflake_merge": true, "base_etl": {"cast": {"kyc_complied_at": "datetime", "kyc_due_at": "datetime", "created_at": "datetime", "updated_at": "datetime", "deleted_at_unix": "int", "user_sms_triggered_at": "datetime"}}, "sf-dna_etl": {"time_cast": {"kyc_complied_at": "ist", "kyc_due_at": "ist", "created_at": "ist", "user_sms_triggered_at": "ist"}, "dpvendormap": ["actor_id:actor_id"]}}}}