select onb.ACTOR_ID,
       nvl(min(datediff(day, CREATED_AT_IST, current_date())), -999) as Day_since_credit_score_userrefresh

from (Select
            a.id as actor_id
        from EPIFI_DATALAKE_FEDERAL.FEDERAL_EPIFI.savings_accounts sa
        inner join EPIFI_DATALAKE_TECH.ACTOR.ACTORS a
        on sa.primary_account_holder = a.entity_id
        where state = 'CREATED' and a.id is not null
        group by 1
    )  onb
left join EPIFI_DATALAKE_TECH.TECH_EPIFI.credit_reports a on onb.actor_id = a.ACTOR_ID
       and DOWNLOAD_STATUS = 'DOWNLOAD_STATUS_DOWNLOAD_SUCCESSFUL'
group by 1
