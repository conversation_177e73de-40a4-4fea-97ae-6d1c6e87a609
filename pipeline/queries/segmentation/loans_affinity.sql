select 
actor_id, loans_affinity_category
from (
select actor_id , probability_category as loans_affinity_category, scoring_date 
from epifi_sandbox_tech.sandbox.loans_affinity_v1_scoring
where scoring_date in (
    select max(scoring_date) as max_scoring_date from epifi_sandbox_tech.sandbox.loans_affinity_v1_scoring
    )
qualify row_number() over (partition by actor_id order by scoring_date desc) = 1
)
