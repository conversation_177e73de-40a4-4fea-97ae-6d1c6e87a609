// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials/stscreds"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/vendorgateway/zenduty"
	opensearch2 "github.com/epifi/gamma/pkg/opensearch"
	"github.com/epifi/gamma/varys"
	"github.com/epifi/gamma/varys/config/genconf"
	"github.com/epifi/gamma/varys/logfetcher"
	"github.com/epifi/gamma/varys/logsource"
	"github.com/opensearch-project/opensearch-go"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func InitialiseVarysService(conf *genconf.Config, zendutyClient zenduty.ZendutyClient) *varys.Service {
	client := openSearchClientProvider(conf)
	openSearchLogService := logsource.NewOpenSearchLogService(client)
	grpcFailureLogFetcher := logfetcher.NewGrpcFailureLogFetcher(openSearchLogService, conf)
	vendorApiFailureFetcher := logfetcher.NewVendorApiFailureFetcher(openSearchLogService, conf)
	logFetcherFactorySvc := logfetcher.NewLogFetcherFactorySvc(grpcFailureLogFetcher, vendorApiFailureFetcher)
	service := varys.NewService(conf, logFetcherFactorySvc, zendutyClient)
	return service
}

// wire.go:

func openSearchClientProvider(conf *genconf.Config) *opensearch.Client {
	awsConfig, cfgErr := config.LoadDefaultConfig(context.Background(), config.WithRegion(conf.Aws().Region))
	if cfgErr != nil {
		logger.Panic("error in loading aws config", zap.Error(cfgErr))
	}
	client := sts.NewFromConfig(awsConfig)
	creds := stscreds.NewAssumeRoleProvider(client, conf.EsConfig().RoleARN)
	awsConfig.Credentials = creds

	signer, errSigner := opensearch2.NewAWSSigner(awsConfig)
	if errSigner != nil {
		logger.Panic("err in getting AWS signer", zap.Error(errSigner))
	}

	esClient, errESClient := opensearch2.NewESClientWithRoleAuth(conf.EsConfig().ESEndpoint, signer)
	if errESClient != nil {
		logger.Panic("error in creating es client", zap.Error(errESClient))
	}

	return esClient
}
