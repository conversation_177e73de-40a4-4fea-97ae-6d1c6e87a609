variable "env" {
  type        = string
  description = "environment"
  default     = ""
}

variable "endpoints" {
  type        = list(string)
  description = "list of vpc endpoints"
  default     = []
}

variable "gateway_source_vpc_ids" {
  type        = string
  description = "list of source vpc endpoints that can call gateway"
  default     = ""
}

variable "this_account_id" {
  type        = string
  description = "id of the org where lambda is running"
  default     = ""
}
