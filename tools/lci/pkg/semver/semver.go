package semver

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"golang.org/x/mod/semver"
)

func GetPatchVersion(versionStr string) (int, error) {
	if !semver.IsValid(versionStr) {
		return 0, fmt.Errorf("invalid version string passed: %s", versionStr)
	}

	patchVersionStr := strings.Split(semver.Canonical(versionStr), ".")[2]

	patchVersion, parseErr := strconv.Atoi(patchVersionStr)
	if parseErr != nil {
		return 0, errors.Wrap(parseErr, "error converting patch version to int")
	}

	return patchVersion, nil
}
