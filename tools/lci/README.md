# LCI
Local CI is a CLI tool to run CI checks locally

## Introduction
[Slides](https://docs.google.com/presentation/d/1CJLzn174AWG-gG2DNnLwrVmGvPUAiy6tz2lOkuuHR6U/edit?usp=sharing)

## Onboarding New Services

For onboarding a new service group tests follow the steps below:
1. Create a GitHub action workflow file using the base-workflow template. Make sure that `run_test_parallel` is set to true in your base-workflow.
2. Add the paths for which the check should run in the respective repository's [path_pattern.yml](https://github.com/epiFi/gamma/blob/master/tools/actions/lci/run_lci_validation/path_pattern.yml).
Also make sure that the paths specified here are same as specified in the [github action workflow](https://github.com/epifi/be-common/blob/0a80f3b745435c7b9753504bcc86bc7192cdfc59/.github/workflows/celestial.yml#L21) added in the previous step.
3. If you want to run all checks for the impacted paths in the repository, update the common_paths in  [base-workflow.yml](https://github.com/epiFi/be-common/blob/master/.github/workflows/base-workflow.yml) 
Also, add common paths in the [path_pattern.yml](https://github.com/epiFi/gamma/blob/master/tools/actions/lci/run_lci_validation/path_pattern.yml) of the repository.
4. Add workflow file name in the [workflow.go](https://github.com/epiFi/be-common/blob/ab899c0110d817bd6371fc4725ed4ac017e4fe71/tools/lci/workflow/workflow.go#L111)
5. Add the GitHub status check in the respective repository's [run_lci_validation GH Action](https://github.com/epiFi/be-common/blob/ab899c0110d817bd6371fc4725ed4ac017e4fe71/tools/actions/lci/run_lci_validation/action.yml#L83).

(**NOTE:** The paths for which a given service group test runs is specified at two places, 1. In the github action defined in Step 1, 2. In the path_pattern.yml, mentioned in Step 2.
So whenever updating the paths please ensure that the paths stay the same at both the places)

## Updating version
- When merging new changes update the version number in the VERSION file.
- Update major version for breaking changes, minor version for new features, and patch version for bug fixes.
- Once the version bump PR is merged, run the [job](https://jenkins-deploy.pointz.in/job/Scripts/job/Platform/job/BackendInfra/job/ToolsBuilder/job/build_binary/) to create new binary for github action
- When the major or minor version is bumped, lci in github checks will start failing as we have stricter checks and expect the new version of lci in other repos.
 Raise a PR in other repos to update the module version of be-common. It is always safe to merge the be-common PR and subsequent PRs in other repos in non-peak development hours [11PM IST to 9AM IST].
