package cmd

import (
	"os"

	"github.com/spf13/cobra"
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "servergen",
	Short: "A tool to generate servers, terraform files for IAM whitelisting and validating configs",
	Long: `Generate your server code, terraform files for IAM whitelisting and
validate server & service group configs all with a single tool. The server code
generation is driven by config at pkg/cmd/config/server-definition-<env>.yml.
For a detailed user guide please refer to
https://docs.google.com/document/d/1gEE0opXNjWU2boKIhNzUu7mwHMkoiizosd4CXGsETkQ/edit?usp=sharing`,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

// Cobra uses init functions, so disable gochecknoinits
//
//nolint:gochecknoinits
func init() {
}
