// Code generated by tools/conf_gen/dynamic_conf_gen.go
package service1

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "dynamicuint8field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicUint8Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint8Field, nil
	case "dynamicuint16field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicUint16Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint16Field, nil
	case "dynamicuint32field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.<PERSON><PERSON><PERSON>("invalid path %q for primitive field \"DynamicUint32Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint32Field, nil
	case "dynamicuint64field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicUint64Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint64Field, nil
	case "dynamicint8field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt8Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt8Field, nil
	case "dynamicint16field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt16Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt16Field, nil
	case "dynamicint32field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt32Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt32Field, nil
	case "dynamicint64field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt64Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt64Field, nil
	case "dynamicintfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicIntField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicIntField, nil
	case "dynamicboolfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicBoolField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicBoolField, nil
	case "dynamicdurationfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicDurationField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicDurationField, nil
	case "dynamicmapfield1":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField1, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"DynamicMapField1\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.DynamicMapField1[dynamicFieldPath[1]], nil

		}
		return obj.DynamicMapField1, nil
	case "dynamicmapfield2":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField2, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField2[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField2, nil
	case "dynamicmapfield3":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField3, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField3[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField3, nil
	case "dynamicmapfield4":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField4, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField4[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField4, nil
	case "dynamicmapfield5":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField5, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField5[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField5, nil
	case "dynamicmapfield6":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField6, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField6[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField6, nil
	case "dynamicmapfield7":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField7, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField7[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField7, nil
	case "dynamicarrayfield1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicArrayField1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicArrayField1, nil
	case "dynamictimefield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicTimeField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicTimeField, nil
	case "dynamicstringfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicStringField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicStringField, nil
	case "dynamicstructfield":
		return obj.DynamicStructField.Get(dynamicFieldPath[1:])
	case "dynamicstaticstructfield3":
		return obj.DynamicStaticStructField3.Get(dynamicFieldPath[1:])
	case "dynamicstructfield2":
		return obj.DynamicStructField2.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *StaticConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for StaticConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *StaticFullConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for StaticFullConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Config2) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "dynamicuint8field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicUint8Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint8Field, nil
	case "dynamicuint16field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicUint16Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint16Field, nil
	case "dynamicuint32field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicUint32Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint32Field, nil
	case "dynamicuint64field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicUint64Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicUint64Field, nil
	case "dynamicint8field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt8Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt8Field, nil
	case "dynamicint16field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt16Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt16Field, nil
	case "dynamicint32field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt32Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt32Field, nil
	case "dynamicint64field":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicInt64Field\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicInt64Field, nil
	case "dynamicintfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicIntField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicIntField, nil
	case "dynamicboolfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicBoolField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicBoolField, nil
	case "dynamicdurationfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicDurationField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicDurationField, nil
	case "dynamicmapfield1":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField1, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"DynamicMapField1\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.DynamicMapField1[dynamicFieldPath[1]], nil

		}
		return obj.DynamicMapField1, nil
	case "dynamicmapfield2":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField2, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField2[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField2, nil
	case "dynamicmapfield3":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField3, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField3[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField3, nil
	case "dynamicmapfield4":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DynamicMapField4, nil
		case len(dynamicFieldPath) > 1:

			return obj.DynamicMapField4[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DynamicMapField4, nil
	case "dynamicarrayfield1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicArrayField1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicArrayField1, nil
	case "dynamictimefield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicTimeField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicTimeField, nil
	case "dynamicstringfield":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DynamicStringField\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DynamicStringField, nil
	case "dynamicstructfield":
		return obj.DynamicStructField.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config2", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DynamicConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "field1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Field1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Field1, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DynamicConfig", strings.Join(dynamicFieldPath, "."))
	}
}
