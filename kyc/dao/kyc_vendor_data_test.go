package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/nulltypes"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	pb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/kyc/config"
	"github.com/epifi/gamma/kyc/dao/model"
)

// Test Suite for KYCVendorDataDao
type KYCVendorDataDaoTestSuite struct {
	db     *gorm.DB
	kvdDao KYCVendorDataDao
	conf   *config.Config
}

var (
	kvdTS KYCVendorDataDaoTestSuite
)

func TestKYCVendorDataDB(t *testing.T) {
	// Clean database, run migrations and load fixtures
	pkgTestV2.PrepareScopedDatabase(t, kvdTS.conf.EpifiDb.GetName(), kvdTS.db, kycTables)

	t.Run("Create success", func(t *testing.T) {
		ctx := context.Background()
		_, _ = kaTS.kycAttemptDao.CreateKYCAttempt(ctx, ka1)

		res, err := kvdTS.kvdDao.Create(ctx, &kvd1)
		assert.Nil(t, err)
		assert.NotNil(t, res)

		// assert ID is not empty as it's db generated
		assert.NotEmpty(t, res.Id)

		// assert response
		assert.Equal(t, kvd1.KycAttemptId, res.KycAttemptId)
		assert.Equal(t, kvd1.PayloadType, res.PayloadType)

		// assert payload
		actualCKYCNo := res.Payload.CkycDownloads.Payloads[0].PersonalData.CkycNo
		expectedCKYCNo := kvd1.Payload.CkycDownloads.Payloads[0].PersonalData.CkycNo
		assert.Equal(t, expectedCKYCNo, actualCKYCNo)

		// assert deleted by
		assert.True(t, proto.Equal(kvd1.DeleteBy, res.DeleteBy))

		// check for entry in relations table
		fetchRelation := &model.KYCVendorData{}
		kaTS.db.Table("kyc_vendor_data").
			Select("*").
			Where("id = ?", res.Id).
			Scan(fetchRelation)
		assert.NotNil(t, fetchRelation)

		res, err = kvdTS.kvdDao.Create(ctx, &kvd2)
		assert.Nil(t, err)
		assert.NotNil(t, res)
	})

	t.Run("Create fail nil insertion", func(t *testing.T) {
		res, err := kvdTS.kvdDao.Create(context.Background(), nil)
		assert.NotNil(t, err)
		assert.Nil(t, res)
	})

	t.Run("GetByAttemptIDAndType record found", func(t *testing.T) {
		ctx := context.Background()
		get, err := kvdTS.kvdDao.GetByAttemptIDAndType(ctx, kvd1.KycAttemptId, kvd1.PayloadType)
		assert.NotNil(t, get)
		assert.Nil(t, err)
		assert.Equal(t, get.Payload.CkycDownloads.Payloads[0].PersonalData.CkycNo, kvd1.Payload.CkycDownloads.Payloads[0].PersonalData.CkycNo)
		assert.True(t, proto.Equal(kvd1.DeleteBy, get.DeleteBy),
			fmt.Sprintf("\ngot : %v\nwant: %v", kvd1.DeleteBy, get.DeleteBy),
		)

		// insert multiple kvds in DB
		res, err := kvdTS.kvdDao.Create(ctx, &kvd2)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		latestKvd, err := kvdTS.kvdDao.Create(ctx, &kvd2)
		assert.Nil(t, err)
		assert.NotNil(t, latestKvd)

		// ensure that latest kvd is picked always
		get, err = kvdTS.kvdDao.GetByAttemptIDAndType(ctx, kvd2.KycAttemptId, kvd2.PayloadType)
		assert.NotNil(t, get)
		assert.Nil(t, err)
		assert.Nil(t, get.DeleteBy)
		assert.Equal(t, latestKvd.Id, get.Id)
	})

	t.Run("GetByAttemptIDAndType record not found", func(t *testing.T) {
		ctx := context.Background()
		get, err := kvdTS.kvdDao.GetByAttemptIDAndType(ctx, "00000000-0000-0000-0000-000000000099", kvd1.PayloadType)
		assert.Nil(t, get)
		assert.NotNil(t, err)
		assert.True(t, errors.Is(err, gorm.ErrRecordNotFound))
	})
}

func unmarshalVD(o *model.KYCVendorData) *pb.KYCVendorData {
	var deleteBy *timestamppb.Timestamp
	if o.DeleteBy.Valid {
		deleteBy = timestamppb.New(o.DeleteBy.Time)
	}
	return &pb.KYCVendorData{
		Id:           o.ID,
		KycAttemptId: o.KYCAttemptID,
		PayloadType:  o.PayloadType,
		DeleteBy:     deleteBy,
		Payload:      o.Payload,
		Metadata:     o.Metadata,
	}
}

func TestKYCVendorDataDB_GetIDsByExpiryTime(t *testing.T) {
	// fixtures
	timeToExpire := &gorm.DeletedAt{
		Time:  time.Now(),
		Valid: true,
	}

	ka1Model := &model.KycAttempt{
		AttemptID:         "00000000-0000-0000-0000-000000000011",
		ActorID:           "actor-1",
		Source:            "FEDERAL",
		KycType:           pb.KycType_CKYC,
		KYCProgressStatus: pb.KYCState_CKYC_SEARCH_FOUND,
	}
	// expired; deleted
	kvd1Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000001",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_CKYC_DOWNLOAD,
		DeleteBy:     nulltypes.NewNullTime(timeToExpire.Time.Add(-1 * time.Hour)),
		Payload: &pb.KYCVendorDataPayload{
			CkycDownloads: &pb.CKYCDownloadVendorData{
				Payloads: []*pb.CKYCDownloadPayload{},
			},
		},
		DeletedAt: timeToExpire,
		Metadata: &pb.KYCVendorDataMetadata{
			RequestId: "ExpiredDeletedReqId",
		},
	}
	// expired; not deleted
	kvd2Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000002",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_CKYC_DOWNLOAD,
		DeleteBy:     nulltypes.NewNullTime(timeToExpire.Time.Add(-1 * time.Hour)),
		Payload: &pb.KYCVendorDataPayload{
			CkycDownloads: &pb.CKYCDownloadVendorData{
				Payloads: []*pb.CKYCDownloadPayload{},
			},
		},
		Metadata: &pb.KYCVendorDataMetadata{
			RequestId: "ExpiredNotDeletedReqId",
		},
	}
	// not expired
	kvd3Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000003",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_CKYC_DOWNLOAD,
		DeleteBy:     nulltypes.NewNullTime(timeToExpire.Time.Add(+1 * time.Hour)),
		Payload: &pb.KYCVendorDataPayload{
			CkycDownloads: &pb.CKYCDownloadVendorData{
				Payloads: []*pb.CKYCDownloadPayload{},
			},
		},
		Metadata: &pb.KYCVendorDataMetadata{
			RequestId: "NotExpiredReqId",
		},
	}

	// setup db
	pkgTestV2.PrepareScopedDatabase(t, kvdTS.conf.EpifiDb.GetName(), kvdTS.db, kycTables)
	if err := InsertKYCAttempt(kaTS.db, ka1Model); err != nil {
		t.Errorf("%v", err.Error())
	}
	if err := InsertKYCVendorData(kvdTS.db, kvd1Model, kvd2Model, kvd3Model); err != nil {
		t.Errorf("%v", err.Error())
	}

	type args struct {
		expiry time.Time
		limit  int
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.KYCVendorData
		wantErr string
	}{
		{
			name: "ensure only values in time range are deleted",
			args: args{
				expiry: timeToExpire.Time,
				limit:  10,
			},
			want: []*pb.KYCVendorData{
				unmarshalVD(kvd2Model),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := kvdTS.kvdDao
			ids, kycAttIds, metaReqIds, _, err := db.GetIDsByExpiryTime(context.Background(), tt.args.expiry, tt.args.limit)
			if (err != nil) != (tt.wantErr != "") {
				t.Errorf("GetByExpiryTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil && err.Error() != tt.wantErr {
				t.Errorf("invalid error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			require.Equal(t, len(ids), len(tt.want))
			for i := range ids {
				require.Equal(t, ids[i], tt.want[i].Id)
				require.Equal(t, kycAttIds[i], tt.want[i].KycAttemptId)
				require.Equal(t, metaReqIds[i], tt.want[i].Metadata.RequestId)
			}
		})
	}
}

func TestKYCVendorDataDB_DeletePayload(t *testing.T) {
	ka1Model := &model.KycAttempt{
		AttemptID:         "00000000-0000-0000-0000-001111111111",
		ActorID:           "actor-1",
		Source:            "FEDERAL",
		KycType:           pb.KycType_CKYC,
		KYCProgressStatus: pb.KYCState_CKYC_SEARCH_FOUND,
	}
	kvd1Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000001",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_VENDOR_DATA_TYPE_UNSPECIFIED,
		Payload: &pb.KYCVendorDataPayload{
			CkycDownloads: &pb.CKYCDownloadVendorData{
				Payloads: []*pb.CKYCDownloadPayload{
					{
						SignImage:     "sign-image",
						PanImage:      "pan-image",
						PassportImage: "passport-image",
					},
				},
			},
		},
	}
	kvd2Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000002",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_CKYC_DOWNLOAD,
		Payload: &pb.KYCVendorDataPayload{
			CkycDownloads: &pb.CKYCDownloadVendorData{
				Payloads: []*pb.CKYCDownloadPayload{
					{
						SignImage:     "sign-image",
						PanImage:      "pan-image",
						PassportImage: "passport-image",
					},
				},
			},
		},
	}
	kvd3Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000003",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_CKYC_DOWNLOAD,
		Payload: &pb.KYCVendorDataPayload{
			CkycDownloads: &pb.CKYCDownloadVendorData{
				Payloads: []*pb.CKYCDownloadPayload{
					{
						SignImage:     "sign-image",
						PanImage:      "pan-image",
						PassportImage: "passport-image",
					},
				},
			},
		},
	}
	// setup db
	pkgTestV2.PrepareScopedDatabase(t, kvdTS.conf.EpifiDb.GetName(), kvdTS.db, kycTables)
	if err := InsertKYCAttempt(kaTS.db, ka1Model); err != nil {
		t.Errorf("%v", err.Error())
	}
	if err := InsertKYCVendorData(kvdTS.db, kvd1Model, kvd2Model, kvd3Model); err != nil {
		t.Errorf("%v", err.Error())
	}

	type args struct {
		ids []string
	}
	tests := []struct {
		name           string
		args           args
		wantErr        bool
		wantDeleted    []string // list of IDs that should be deleted
		wantNotDeleted []string // list of IDs that should be not be deleted
	}{
		{
			name: "none deleted",
			args: args{
				ids: nil,
			},
			wantNotDeleted: []string{kvd1Model.ID, kvd2Model.ID, kvd3Model.ID},
		},
		{
			name: "nil input",
			args: args{
				ids: []string{},
			},
			wantNotDeleted: []string{kvd1Model.ID, kvd2Model.ID, kvd3Model.ID},
		},
		{
			name: "2 records deleted",
			args: args{
				ids: []string{kvd1Model.ID, kvd2Model.ID},
			},
			wantDeleted:    []string{kvd1Model.ID, kvd2Model.ID},
			wantNotDeleted: []string{kvd3Model.ID},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := kvdTS.kvdDao.DeletePayload(context.Background(), tt.args.ids); (err != nil) != tt.wantErr {
				t.Errorf("DeletePayload() error = %v, wantErr %v", err, tt.wantErr)
			}

			models := make([]*model.KYCVendorData, 0)
			if err := kvdTS.db.Unscoped().
				Where("kyc_attempt_id IN (?)", []string{
					ka1Model.AttemptID,
				}).Find(&models).Error; err != nil {
				t.Errorf("error getting kvd from db %v", err)
			}

			assert.Equal(t, len(tt.wantDeleted)+len(tt.wantNotDeleted), len(models))

			for _, got := range models {
				if StringInSlice(got.ID, tt.wantDeleted) {
					assert.NotNil(t, got.DeletedAt)
					assert.Nil(t, got.Payload)

				} else if StringInSlice(got.ID, tt.wantNotDeleted) {
					assert.Nil(t, got.DeletedAt)
					assert.NotNil(t, got.Payload)

				} else {
					t.Errorf("unexpected kvd id %v", got.ID)
				}
			}
		})
	}
}

func StringInSlice(s string, slice []string) bool {
	for _, ele := range slice {
		if s == ele {
			return true
		}
	}
	return false
}

func TestKYCVendorDataDB_UpdateBKYCRecord(t *testing.T) {
	ka1Model := &model.KycAttempt{
		AttemptID:         "00000000-0000-0000-0000-001111111111",
		ActorID:           "actor-1",
		Source:            "FEDERAL",
		KycType:           pb.KycType_BKYC,
		KYCProgressStatus: pb.KYCState_BKYC_IN_REVIEW,
	}
	kvd1Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000001",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_BKYC_RECORD,
		Payload: &pb.KYCVendorDataPayload{
			BkycRecord: &pb.BKYCRecord{
				Name: &commontypes.Name{
					FirstName: "jolly joseph",
				},
			},
		},
	}
	kvd1UpdatedModel := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000001",
		KYCAttemptID: ka1Model.AttemptID,
		PayloadType:  pb.KYCVendorDataType_BKYC_RECORD,
		Payload: &pb.KYCVendorDataPayload{
			BkycRecord: &pb.BKYCRecord{
				Name: &commontypes.Name{
					FirstName: "jolly joseph",
				},
				CommAddress: &postaladdress.PostalAddress{
					AdministrativeArea: "shaitan gli",
				},
				SignatureImage: &commontypes.Image{
					ImageDataBase64: "image",
				},
			},
		},
	}
	// setup db
	pkgTestV2.PrepareScopedDatabase(t, kvdTS.conf.EpifiDb.GetName(), kvdTS.db, kycTables)
	if err := InsertKYCAttempt(kaTS.db, ka1Model); err != nil {
		t.Errorf("%v", err.Error())
	}
	if err := InsertKYCVendorData(kvdTS.db, kvd1Model); err != nil {
		t.Errorf("%v", err.Error())
	}
	type args struct {
		ctx        context.Context
		attemptId  string
		bkycRecord *pb.BKYCRecord
		fieldMasks []pb.BKYCRecordFieldMask
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
		want    *model.KYCVendorData
	}{
		{
			name: "Update address and signature image",
			args: args{
				ctx:       ctx,
				attemptId: ka1Model.AttemptID,
				bkycRecord: &pb.BKYCRecord{
					CommAddress: &postaladdress.PostalAddress{
						AdministrativeArea: "shaitan gli",
					},
					SignatureImage: &commontypes.Image{
						ImageDataBase64: "image",
					},
				},
				fieldMasks: []pb.BKYCRecordFieldMask{
					pb.BKYCRecordFieldMask_BKYC_RECORD_FIELD_MASK_COMMUNICATION_ADDRESS,
					pb.BKYCRecordFieldMask_BKYC_RECORD_FIELD_MASK_SIGNATURE_IMAGE,
				},
			},
			want: kvd1UpdatedModel,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotErr := kvdTS.kvdDao.UpdateBKYCRecord(tt.args.ctx, tt.args.attemptId, tt.args.bkycRecord, tt.args.fieldMasks); !errors.Is(tt.wantErr, gotErr) {
				t.Errorf("UpdateBKYCRecord() error = %v, wantErr %v", gotErr, tt.wantErr)
			}
			models := &model.KYCVendorData{}
			if err := kvdTS.db.Where("kyc_attempt_id IN (?)", []string{
				ka1Model.AttemptID,
			}).Find(&models).Error; err != nil {
				t.Errorf("error getting kvd from db %v", err)
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&pb.KYCVendorData{}, "created_at"),
			}
			cmpRes := cmp.Diff(models.GetProto(), tt.want.GetProto(), opts...)
			if cmpRes != "" {
				t.Errorf("Difference in db value and expected %v", cmpRes)
			}

		})
	}
}

func TestKYCVendorDataDB_UpdateDeleteBy(t *testing.T) {
	timeA := time.Date(2023, 8, 0, 0, 0, 0, 0, datetime.IST)
	timeB := time.Date(2023, 9, 0, 0, 0, 0, 0, datetime.IST)
	attId := "00000000-0000-0000-0000-000000000001"
	ka1Model := &model.KycAttempt{
		AttemptID:         attId,
		ActorID:           "actor-1",
		Source:            "FEDERAL",
		KycType:           pb.KycType_BKYC,
		KYCProgressStatus: pb.KYCState_BKYC_IN_REVIEW,
	}
	kvd1Model := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000001",
		KYCAttemptID: attId,
		PayloadType:  pb.KYCVendorDataType_EKYC_RECORD,
		DeleteBy:     nulltypes.NewNullTime(timeA),
	}
	kvd1ModelWithUpdatedDeleteBy := &model.KYCVendorData{
		ID:           "00000000-0000-0000-0000-000000000001",
		KYCAttemptID: attId,
		PayloadType:  pb.KYCVendorDataType_EKYC_RECORD,
		DeleteBy:     nulltypes.NewNullTime(timeB),
	}
	// setup db
	pkgTestV2.PrepareScopedDatabase(t, kvdTS.conf.EpifiDb.GetName(), kvdTS.db, kycTables)
	if err := InsertKYCAttempt(kaTS.db, ka1Model); err != nil {
		t.Errorf("%v", err.Error())
	}
	if err := InsertKYCVendorData(kvdTS.db, kvd1Model); err != nil {
		t.Errorf("%v", err.Error())
	}
	type args struct {
		ctx       context.Context
		attemptId string
		kycVDType pb.KYCVendorDataType
		deleteBy  *timestamppb.Timestamp
	}
	tests := []struct {
		name     string
		args     args
		wantErr  error
		want     *model.KYCVendorData
		assertDb bool
	}{
		{
			name: "Update succesfully",
			args: args{
				ctx:       ctx,
				attemptId: attId,
				deleteBy:  timestamppb.New(timeB),
				kycVDType: pb.KYCVendorDataType_EKYC_RECORD,
			},
			want:     kvd1ModelWithUpdatedDeleteBy,
			assertDb: true,
		},
		{
			name: "Kyc vendor data not found",
			args: args{
				ctx:       ctx,
				attemptId: "00000000-0000-0000-0000-001111111112",
				deleteBy:  timestamppb.New(timeB),
				kycVDType: pb.KYCVendorDataType_EKYC_RECORD,
			},
			wantErr:  gorm.ErrRecordNotFound,
			assertDb: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotErr := kvdTS.kvdDao.UpdateDeleteBy(tt.args.ctx, tt.args.kycVDType, tt.args.attemptId, tt.args.deleteBy); !errors.Is(tt.wantErr, gotErr) {
				t.Errorf("UpdateDeleteBy() error = %v, wantErr %v", gotErr, tt.wantErr)
			}

			if tt.assertDb {
				models := &model.KYCVendorData{}
				if err := kvdTS.db.Where("kyc_attempt_id IN (?)", []string{
					attId,
				}).Find(&models).Error; err != nil {
					t.Errorf("error getting kvd from db %v", err)
				}
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&pb.KYCVendorData{}, "created_at"),
				}
				cmpRes := cmp.Diff(models.GetProto(), tt.want.GetProto(), opts...)
				if cmpRes != "" {
					t.Errorf("Difference in db value and expected %v", cmpRes)
				}
			}
		})
	}
}
