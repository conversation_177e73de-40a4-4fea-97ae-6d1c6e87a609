package vkyc

import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	mockActor "github.com/epifi/gamma/api/actor/mocks"
	mocks5 "github.com/epifi/gamma/api/bankcust/mocks"
	mocks7 "github.com/epifi/gamma/api/comms/inapptargetedcomms/mocks"
	mocks2 "github.com/epifi/gamma/api/employment/mocks"
	mockKyc "github.com/epifi/gamma/api/kyc/mocks"
	mocks6 "github.com/epifi/gamma/api/savings/mocks"
	"github.com/epifi/gamma/api/user/mocks"
	mockOnboarding "github.com/epifi/gamma/api/user/onboarding/mocks"
	mockUserIntel "github.com/epifi/gamma/api/userintel/mocks"
	"github.com/epifi/gamma/kyc/config/genconf"
	"github.com/epifi/gamma/kyc/test"
	mocks4 "github.com/epifi/gamma/kyc/vkyc/dao/mocks"
	mocks3 "github.com/epifi/gamma/kyc/vkyc/mocks"
	mocks8 "github.com/epifi/gamma/kyc/vkyc/mocks/nudges"
	mockCache "github.com/epifi/be-common/pkg/cache/mocks"
	mockevents "github.com/epifi/be-common/pkg/events/mocks"
)

var (
	genCnf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()

	_, genCnf, _, teardown = test.InitTestServerV2()

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockDependencies struct {
	mockUserClient          *mocks.MockUsersClient
	mockKycClient           *mockKyc.MockKycClient
	mockVkycSummaryDao      *mocks4.MockVKYCSummaryDao
	mockServiceProvider     *mocks3.MockServiceProvider
	mockVkycCustomerInfoDao *mocks4.MockVKYCKarzaCustomerInfoDao
	mockEmploymentClient    *mocks2.MockEmploymentClient
	mockBcClient            *mocks5.MockBankCustomerServiceClient
	mockVkycMockable        *mocks3.MockVkycMockable
	mockActorClient         *mockActor.MockActorClient
	mockSavingClient        *mocks6.MockSavingsClient
	mockIATClient           *mocks7.MockInAppTargetedCommsClient
	mockNudgeProcType       *mocks8.MockNudgeProcType
	mockVkycCacheStorage    *mockCache.MockCacheStorage
	mockUserIntelClient     *mockUserIntel.MockUserIntelServiceClient
	mockOnbClient           *mockOnboarding.MockOnboardingClient
	mockEventBroker         *mockevents.MockBroker
	mockVKYCAttemptDao      *mocks4.MockVKYCAttemptDao
	mockCallInfoDao         *mocks4.MockVKYCKarzaCallInfoDao
}

func newServiceWithMocks(t *testing.T) (*Service, *mockDependencies) {
	ctrl := gomock.NewController(t)
	mockUserClient := mocks.NewMockUsersClient(ctrl)
	mockKycClient := mockKyc.NewMockKycClient(ctrl)
	mockVkycSummaryDao := mocks4.NewMockVKYCSummaryDao(ctrl)
	mockServiceProvider := mocks3.NewMockServiceProvider(ctrl)
	mockVkycCustomerInfoDao := mocks4.NewMockVKYCKarzaCustomerInfoDao(ctrl)
	mockEmploymentClient := mocks2.NewMockEmploymentClient(ctrl)
	mockBcClient := mocks5.NewMockBankCustomerServiceClient(ctrl)
	mockVkycMockable := mocks3.NewMockVkycMockable(ctrl)
	mockActorClient := mockActor.NewMockActorClient(ctrl)
	mockSavingsClient := mocks6.NewMockSavingsClient(ctrl)
	mockIATClient := mocks7.NewMockInAppTargetedCommsClient(ctrl)
	mockNudgeProcType := mocks8.NewMockNudgeProcType(ctrl)
	mockVkycCacheStorage := mockCache.NewMockCacheStorage(ctrl)
	mockUserIntelClient := mockUserIntel.NewMockUserIntelServiceClient(ctrl)
	mockOnbClient := mockOnboarding.NewMockOnboardingClient(ctrl)
	mockEventBroker := mockevents.NewMockBroker(ctrl)
	mockVKYCAttemptDao := mocks4.NewMockVKYCAttemptDao(ctrl)
	mockCallInfoDao := mocks4.NewMockVKYCKarzaCallInfoDao(ctrl)

	md := &mockDependencies{
		mockUserClient:          mockUserClient,
		mockKycClient:           mockKycClient,
		mockVkycSummaryDao:      mockVkycSummaryDao,
		mockServiceProvider:     mockServiceProvider,
		mockVkycCustomerInfoDao: mockVkycCustomerInfoDao,
		mockEmploymentClient:    mockEmploymentClient,
		mockBcClient:            mockBcClient,
		mockVkycMockable:        mockVkycMockable,
		mockActorClient:         mockActorClient,
		mockSavingClient:        mockSavingsClient,
		mockIATClient:           mockIATClient,
		mockNudgeProcType:       mockNudgeProcType,
		mockVkycCacheStorage:    mockVkycCacheStorage,
		mockUserIntelClient:     mockUserIntelClient,
		mockOnbClient:           mockOnbClient,
		mockEventBroker:         mockEventBroker,
		mockVKYCAttemptDao:      mockVKYCAttemptDao,
		mockCallInfoDao:         mockCallInfoDao,
	}

	svc := &Service{
		conf:                     genCnf,
		userClient:               mockUserClient,
		kycClient:                mockKycClient,
		summaryDao:               mockVkycSummaryDao,
		svcProvider:              mockServiceProvider,
		vkycCustomerInfoDao:      mockVkycCustomerInfoDao,
		employmentClient:         mockEmploymentClient,
		bcClient:                 mockBcClient,
		vkycMockable:             mockVkycMockable,
		actorClient:              mockActorClient,
		savingsClient:            mockSavingsClient,
		inAppTargetedCommsClient: mockIATClient,
		profileTopBannerProc:     mockNudgeProcType,
		profileMiddleWidgetProc:  mockNudgeProcType,
		homeTopBannerProc:        mockNudgeProcType,
		homeProfileExtensionProc: mockNudgeProcType,
		homePopupProc:            mockNudgeProcType,
		vkycCacheStorage:         mockVkycCacheStorage,
		userIntelClient:          mockUserIntelClient,
		onbClient:                mockOnbClient,
		eventBroker:              mockEventBroker,
		vkycAttemptDao:           mockVKYCAttemptDao,
		vkycKarzaCallInfoDao:     mockCallInfoDao,
	}
	return svc, md
}
