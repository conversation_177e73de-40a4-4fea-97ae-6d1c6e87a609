package queue_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	mockActor "github.com/epifi/gamma/api/actor/mocks"
	mockAuth "github.com/epifi/gamma/api/auth/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	mockUpi "github.com/epifi/gamma/api/upi/mocks"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/upi/onboarding/mocks"
	"github.com/epifi/be-common/pkg/epificontext"
	mockSavingsDao "github.com/epifi/gamma/savings/dao/mocks"
	piConsumerQueue "github.com/epifi/gamma/savings/service/queue"
)

type LinkInternalAccountArgMatcher struct {
	want *upiOnboardingPb.LinkInternalAccountRequest
}

func newLinkInternalAccountArgMatcher(want *upiOnboardingPb.LinkInternalAccountRequest) *LinkInternalAccountArgMatcher {
	return &LinkInternalAccountArgMatcher{want: want}
}

func (c *LinkInternalAccountArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*upiOnboardingPb.LinkInternalAccountRequest)
	if !ok {
		return false
	}

	c.want.ClientReqId = got.ClientReqId
	return reflect.DeepEqual(c.want, got)
}

func (c *LinkInternalAccountArgMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

func TestPIConsumerService_CreateVPA(t *testing.T) {

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockActorClient := mockActor.NewMockActorClient(ctr)
	mockSavingDao := mockSavingsDao.NewMockSavingsDao(ctr)
	mockAuthClient := mockAuth.NewMockAuthClient(ctr)
	mockUpiClient := mockUpi.NewMockUPIClient(ctr)
	mockUpiOnboardingClient := mocks.NewMockUpiOnboardingClient(ctr)
	svc := piConsumerQueue.NewPIConsumerService(mockSavingDao, nil, nil, mockActorClient, mockUpiClient, nil, mockAuthClient, mockUpiOnboardingClient)
	type mockGetActorByEntityId struct {
		enable bool
		req    *actorPb.GetActorByEntityIdRequest
		res    *actorPb.GetActorByEntityIdResponse
		err    error
	}

	type mockLinkInternalAccount struct {
		enable bool
		req    *upiOnboardingPb.LinkInternalAccountRequest
		res    *upiOnboardingPb.LinkInternalAccountResponse
		err    error
	}

	type args struct {
		ctx context.Context
		req *savingsPb.CreateVPARequest
	}
	tests := []struct {
		name                    string
		args                    args
		mockGetActorByEntityId  mockGetActorByEntityId
		mockLinkInternalAccount mockLinkInternalAccount
		want                    *savingsPb.CreateVPAResponse
		wantErr                 bool
	}{
		{
			name: "successfully create vpa at upi client using link internal account",
			args: args{
				ctx: context.WithValue(context.Background(), epificontext.CtxActorKey, "actor-id"),
				req: &savingsPb.CreateVPARequest{
					UserId:      "user-id",
					AccountId:   "account-id",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					AccountType: accounts.Type_SAVINGS,
				},
			},
			mockGetActorByEntityId: mockGetActorByEntityId{
				enable: true,
				req: &actorPb.GetActorByEntityIdRequest{
					Type:     types.Actor_USER,
					EntityId: "user-id",
				},
				res: &actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id",
						Type:     types.Actor_USER,
						EntityId: "user-id",
						Name:     "name",
					},
				},
				err: nil,
			},
			mockLinkInternalAccount: mockLinkInternalAccount{
				enable: true,
				req: &upiOnboardingPb.LinkInternalAccountRequest{
					ActorId: "actor-id",
				},
				res: &upiOnboardingPb.LinkInternalAccountResponse{
					Status: rpc.StatusOk(),
				},
			},
			want: &savingsPb.CreateVPAResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "got transient failure from link internal account",
			args: args{
				ctx: context.WithValue(context.Background(), epificontext.CtxActorKey, "actor-id"),
				req: &savingsPb.CreateVPARequest{
					UserId:      "user-id",
					AccountId:   "account-id",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					AccountType: accounts.Type_SAVINGS,
				},
			},
			mockGetActorByEntityId: mockGetActorByEntityId{
				enable: true,
				req: &actorPb.GetActorByEntityIdRequest{
					Type:     types.Actor_USER,
					EntityId: "user-id",
				},
				res: &actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id",
						Type:     types.Actor_USER,
						EntityId: "user-id",
						Name:     "name",
					},
				},
				err: nil,
			},
			mockLinkInternalAccount: mockLinkInternalAccount{
				enable: true,
				req: &upiOnboardingPb.LinkInternalAccountRequest{
					ActorId: "actor-id",
				},
				res: &upiOnboardingPb.LinkInternalAccountResponse{
					Status: rpc.StatusTransientFailure(),
				},
			},
			want: &savingsPb.CreateVPAResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "got permanent failure from link internal account",
			args: args{
				ctx: context.WithValue(context.Background(), epificontext.CtxActorKey, "actor-id"),
				req: &savingsPb.CreateVPARequest{
					UserId:      "user-id",
					AccountId:   "account-id",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					AccountType: accounts.Type_SAVINGS,
				},
			},
			mockGetActorByEntityId: mockGetActorByEntityId{
				enable: true,
				req: &actorPb.GetActorByEntityIdRequest{
					Type:     types.Actor_USER,
					EntityId: "user-id",
				},
				res: &actorPb.GetActorByEntityIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id",
						Type:     types.Actor_USER,
						EntityId: "user-id",
						Name:     "name",
					},
				},
				err: nil,
			},
			mockLinkInternalAccount: mockLinkInternalAccount{
				enable: true,
				req: &upiOnboardingPb.LinkInternalAccountRequest{
					ActorId: "actor-id",
				},
				res: &upiOnboardingPb.LinkInternalAccountResponse{
					Status: rpc.StatusPermanentFailure(),
				},
			},
			want: &savingsPb.CreateVPAResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetActorByEntityId.enable {
				mockActorClient.EXPECT().GetActorByEntityId(tt.args.ctx, tt.mockGetActorByEntityId.req).
					Return(tt.mockGetActorByEntityId.res, tt.mockGetActorByEntityId.err)
			}

			if tt.mockLinkInternalAccount.enable {
				mockUpiOnboardingClient.EXPECT().LinkInternalAccount(tt.args.ctx, newLinkInternalAccountArgMatcher(tt.mockLinkInternalAccount.req)).
					Return(tt.mockLinkInternalAccount.res, tt.mockLinkInternalAccount.err)
			}

			got, err := svc.CreateVPA(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateVPA() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateVPA() got = %v, want %v", got, tt.want)
			}
		})
	}
}
