package consumer_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	"gorm.io/gorm"

	mockCelestial "github.com/epifi/be-common/api/celestial/mocks"
	"github.com/epifi/be-common/pkg/idgen"

	mocksBC "github.com/epifi/gamma/api/bankcust/mocks"
	"github.com/epifi/gamma/api/salaryprogram/mocks"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	mockTiering "github.com/epifi/gamma/api/tiering/mocks"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/savings/config"
	"github.com/epifi/gamma/savings/config/genconf"
	"github.com/epifi/gamma/savings/consumer"
	storage "github.com/epifi/gamma/savings/dao"
	"github.com/epifi/gamma/savings/test"
)

var (
	db                               *gorm.DB
	conf                             *config.Config
	dynConf                          *genconf.Config
	savingsDao                       *storage.CrdbSavingsDao
	savingsAggregationDao            *storage.SavingsAggregationsDaoCrdb
	savingsAggregationsAffectedTable = []string{"savings_accounts", "savings_account_aggregations"}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	var teardown func()
	conf, dynConf, db, teardown = test.InitTestServer()

	testIdgen := idgen.NewDomainIdGenerator(idgen.NewClock())
	cf := changefeed.NewChangefeed(db)
	savingsDao = storage.NewSavingsDao(db, cf)
	savingsAggregationDao = storage.NewSavingsAggregationsDao(db, testIdgen)

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type MockEventSubscriberDependencies struct {
	mockCelestialClient *mockCelestial.MockCelestialClient
	mockSavingsClient   *mockSavings.MockSavingsClient
	mockBCClient        *mocksBC.MockBankCustomerServiceClient
	mockTieringClient   *mockTiering.MockTieringClient
}

func NewMockEventSubscriber(t *testing.T) (*consumer.EventSubscriber, *MockEventSubscriberDependencies) {
	ctrl := gomock.NewController(t)
	mockCelestialClient := mockCelestial.NewMockCelestialClient(ctrl)
	mockSavingsClient := mockSavings.NewMockSavingsClient(ctrl)
	mockBCClient := mocksBC.NewMockBankCustomerServiceClient(ctrl)
	mockSalaryProgramClient := mocks.NewMockSalaryProgramClient(ctrl)
	mockTieringClient := mockTiering.NewMockTieringClient(ctrl)

	md := &MockEventSubscriberDependencies{
		mockCelestialClient: mockCelestialClient,
		mockSavingsClient:   mockSavingsClient,
		mockBCClient:        mockBCClient,
		mockTieringClient:   mockTieringClient,
	}
	return consumer.NewEventSubscriber(dynConf, mockCelestialClient, mockSavingsClient, mockBCClient, mockSalaryProgramClient, mockTieringClient), md
}
