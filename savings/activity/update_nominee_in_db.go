package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	vendorGatewayPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	savingsPb "github.com/epifi/gamma/api/savings"
	savingsActivityPb "github.com/epifi/gamma/api/savings/activity"
	savingsPayload "github.com/epifi/gamma/api/savings/payload"
	accountsTypes "github.com/epifi/gamma/api/typesv2/account"
)

// nolint:ineffassign
func (p *Processor) UpdateNomineeInDB(ctx context.Context, req *savingsActivityPb.UpdateNomineeInDBRequest) (*savingsActivityPb.UpdateNomineeInDBResponse, error) {
	lg := activity.GetLogger(ctx)
	clientReqId := req.GetRequestHeader().GetClientReqId()
	payload := &savingsPayload.UpdateNomineePayload{}
	if err := protojson.Unmarshal(req.GetRequestHeader().GetPayload(), payload); err != nil {
		lg.Error("error in unmarshalling update nominee in db payload", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	}

	ctx = epificontext.CtxWithActorId(ctx, payload.GetActorId())

	var nomineeDetails *savingsPb.NomineeDetails
	switch req.GetPreviousWorkflowState() {
	case savingsPb.WorkflowState_WORKFLOW_STATE_SUCCESS:
		nomineeDetails = payload.GetNomineeDetails()
	case savingsPb.WorkflowState_WORKFLOW_STATE_FAILED:
		savingsAccResp, savingsAccErr := p.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
				ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
					ActorId:                payload.GetActorId(),
					AccountProductOffering: accountsTypes.AccountProductOffering_APO_REGULAR,
					PartnerBank:            vendorGatewayPb.Vendor_FEDERAL_BANK,
				},
			},
		})
		if savingsAccErr != nil {
			lg.Error("failed to get nominee from db", zap.Error(savingsAccErr), zap.String("client_request_id", clientReqId))
			return nil, epifierrors.ErrTransient
		}
		nomineeDetails = savingsAccResp.GetAccount().GetNomineeDetails()
	}

	nomineeDetails.GetNomineeUpdateWorkflowDetails().State = req.GetPreviousWorkflowState()

	// ------------------UPDATE NOMINEE IN DB------------------
	resp, err := p.savingsClient.UpdateSavingsAccountNominees(ctx, &savingsPb.UpdateSavingsAccountNomineesRequest{
		ActorId:        payload.GetActorId(),
		NomineeDetails: nomineeDetails,
	})
	err = epifigrpc.RPCError(resp, err)
	switch {
	case resp.GetStatus().IsRecordNotFound():
		lg.Error("failed to update nominee in db", zap.String("client_request_id", clientReqId))
		return nil, errors.Wrap(epifierrors.ErrPermanent, epifierrors.ErrRecordNotFound.Error())
	case resp.GetStatus().IsInvalidArgument():
		lg.Error("failed to update nominee in db", zap.String("client_request_id", clientReqId))
		return nil, errors.Wrap(epifierrors.ErrPermanent, epifierrors.ErrInvalidArgument.Error())
	case !resp.GetStatus().IsSuccess():
		lg.Error("failed to update nominee in db", zap.String("client_request_id", clientReqId))
		return nil, epifierrors.ErrTransient
	}

	return &savingsActivityPb.UpdateNomineeInDBResponse{
		ResponseHeader: &activityPb.ResponseHeader{
			RequestPayload: req.GetRequestHeader().GetPayload(),
		},
	}, nil

}
