package activity

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	vgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifierrors"

	savingsActivityPb "github.com/epifi/gamma/api/savings/activity"
	savingsPayload "github.com/epifi/gamma/api/savings/payload"
)

func (p *Processor) EnquireNomineeAtVendor(ctx context.Context, req *savingsActivityPb.EnquireNomineeAtVendorRequest) (*savingsActivityPb.EnquireNomineeAtVendorResponse, error) {

	lg := activity.GetLogger(ctx)
	clientReqId := req.GetRequestHeader().GetClientReqId()
	payload := &savingsPayload.UpdateNomineePayload{}
	if err := protojson.Unmarshal(req.GetRequestHeader().GetPayload(), payload); err != nil {
		lg.Error("error in unmarshalling enquire nominee at vendor payload", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	}

	ctx = epificontext.CtxWithActorId(ctx, payload.GetActorId())

	//------------------ENQUIRE NOMINEE UPDATION REQUEST AT VENDOR------------------
	resp, err := p.vgSavingsClient.UpdateNominees(ctx, &vgSavingsPb.UpdateNomineeRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		ReqType: "ENQUIRE",
		ReqId:   payload.GetReqId(),
		Channel: payload.GetChannel(),
	})

	err = epifigrpc.RPCError(resp, err)
	switch {
	case resp.GetStatus().IsInvalidArgument():
		lg.Error("failed to enquire nominee at vendor", zap.String("client_request_id", clientReqId))
		return nil, errors.Wrap(epifierrors.ErrPermanent, epifierrors.ErrInvalidArgument.Error())
	case !rpc.StatusFromError(err).IsSuccess():
		return nil, epifierrors.ErrTransient
	}

	return &savingsActivityPb.EnquireNomineeAtVendorResponse{
		ResponseHeader: &activityPb.ResponseHeader{
			RequestPayload: req.GetRequestHeader().GetPayload(),
		},
	}, nil
}
