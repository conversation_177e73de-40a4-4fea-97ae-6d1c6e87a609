package workflow_test

import (
	"os"
	"testing"

	"github.com/epifi/gamma/bankcust/test"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifiTemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
)

var wts epifiTemporalTest.WorkflowTestSuite

func TestMain(m *testing.M) {
	_, _, teardown := test.InitTestWorker()
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
