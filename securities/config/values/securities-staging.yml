Application:
  Environment: "staging"
  Name: "stocks"

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9886

StocksDb:
  DbType: "PGDB"
  AppName: "stocks"
  StatementTimeout: 10s
  Name: "stocks"
  EnableDebug: true
  SSLMode: "disable"
  SecretName: "staging/rds/epifimetis/stocks_dev_user"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

StockCatalogRefreshPublisher:
  QueueName: "staging-stocks-catalog-refresh-queue"

StockCatalogRefreshSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-stocks-catalog-refresh-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "stocks"

HistoricalPricesRedisCache:
  TTL: 5m
SecuritiesCatalogAdditionPublisher:
  QueueName: "staging-securities-catalog-addition-queue"

SecuritiesCatalogAdditionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-securities-catalog-addition-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "stocks"

AddNewSecuritiesPublisher:
  QueueName: "staging-securities-catalog-addition-queue"

SecuritiesHistoricalPricePublisher:
  QueueName: "staging-securities-historical-price-queue"

AddNewSecuritiesConfig:
  MaximumPageNum: 200
  PageSize: 100

SecuritiesHistoricalPriceSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-securities-historical-price-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 30
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "stocks"
