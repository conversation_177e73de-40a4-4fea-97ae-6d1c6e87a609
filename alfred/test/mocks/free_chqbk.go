// Code generated by MockGen. DO NOT EDIT.
// Source: alfred/chequebook/checker/free_chqbk.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockFreeChequebookEligibilityChecker is a mock of FreeChequebookEligibilityChecker interface.
type MockFreeChequebookEligibilityChecker struct {
	ctrl     *gomock.Controller
	recorder *MockFreeChequebookEligibilityCheckerMockRecorder
}

// MockFreeChequebookEligibilityCheckerMockRecorder is the mock recorder for MockFreeChequebookEligibilityChecker.
type MockFreeChequebookEligibilityCheckerMockRecorder struct {
	mock *MockFreeChequebookEligibilityChecker
}

// NewMockFreeChequebookEligibilityChecker creates a new mock instance.
func NewMockFreeChequebookEligibilityChecker(ctrl *gomock.Controller) *MockFreeChequebookEligibilityChecker {
	mock := &MockFreeChequebookEligibilityChecker{ctrl: ctrl}
	mock.recorder = &MockFreeChequebookEligibilityCheckerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFreeChequebookEligibilityChecker) EXPECT() *MockFreeChequebookEligibilityCheckerMockRecorder {
	return m.recorder
}

// IsEligibleForFreeChequebook mocks base method.
func (m *MockFreeChequebookEligibilityChecker) IsEligibleForFreeChequebook(ctx context.Context, actorId string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsEligibleForFreeChequebook", ctx, actorId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsEligibleForFreeChequebook indicates an expected call of IsEligibleForFreeChequebook.
func (mr *MockFreeChequebookEligibilityCheckerMockRecorder) IsEligibleForFreeChequebook(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEligibleForFreeChequebook", reflect.TypeOf((*MockFreeChequebookEligibilityChecker)(nil).IsEligibleForFreeChequebook), ctx, actorId)
}
