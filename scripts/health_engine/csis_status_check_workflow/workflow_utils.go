package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	temporalEnumsPb "go.temporal.io/api/enums/v1"
	temporalWorkflowPb "go.temporal.io/api/workflow/v1"
	temporalWorkflowserviceSvcPb "go.temporal.io/api/workflowservice/v1"
	"go.temporal.io/sdk/client"

	healthEnginePayload "github.com/epifi/gamma/api/health_engine/payload"
	healthEngineNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/healthengine"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/scripts/health_engine/csis_status_check_workflow/config"
)

func scheduleWorkflow(temporalClient client.Client, conf *config.Config) (string, error) {

	ctx := context.Background()

	schId := "Csis-Check-" + *scheduleId

	request := healthEnginePayload.CsisStatusCheckWorkflowPayload{}

	workflowId := getWorkflowId(schId)
	catchupTimeDuration := 12 * time.Hour

	spec := client.ScheduleSpec{
		CronExpressions: []string{*cronSchedule},
	}

	_, err := temporalClient.ScheduleClient().Create(ctx, client.ScheduleOptions{
		ID:   schId,
		Spec: spec,
		Action: &client.ScheduleWorkflowAction{
			ID:        workflowId,
			Workflow:  healthEngineNs.CsisStatusCheck,
			TaskQueue: conf.Application.TaskQueue,
			Args:      []interface{}{&request},
		},
		CatchupWindow:  catchupTimeDuration,
		Overlap:        temporalEnumsPb.SCHEDULE_OVERLAP_POLICY_SKIP,
		PauseOnFailure: true,
	})

	if err != nil {
		return "", errors.Wrap(err, "Unable to create schedule")
	}

	logger.InfoNoCtx(fmt.Sprintf("Created csis check schedule with id %s ", schId))

	return schId, nil
}

func forceTriggerWorkflow(temporalClient client.Client) error {

	ctx := context.Background()

	schId := "Csis-Check-" + *scheduleId

	scheduleHandle := temporalClient.ScheduleClient().GetHandle(ctx, schId)

	err := scheduleHandle.Trigger(ctx, client.ScheduleTriggerOptions{
		// Set overlap policy to skip, to ensure there are no concurrent runs.
		Overlap: temporalEnumsPb.SCHEDULE_OVERLAP_POLICY_SKIP,
	})
	if err != nil {
		return errors.Wrap(err, "Error while triggering scheduled workflow")
	}

	return nil
}

func deleteWorkflowSchedule(temporalClient client.Client, conf *config.Config) error {

	ctx := context.Background()
	schId := "Csis-Check-" + *scheduleId

	workflowId := getWorkflowId(schId)
	scheduleHandle := temporalClient.ScheduleClient().GetHandle(ctx, schId)

	err := scheduleHandle.Pause(ctx, client.SchedulePauseOptions{
		Note: "Paused for termination",
	})
	if err != nil {
		return errors.Wrap(err, "Error while pausing scheduled workflow")
	}

	for {
		executions, executionErr := getRunningExecutions(temporalClient, conf, workflowId)
		if executionErr != nil {
			return executionErr
		}

		if len(executions) != 0 {
			logger.InfoNoCtx(fmt.Sprintf("There are %d running workflows for these schedules. "+
				"Terminating workflows....", len(executions)))
		} else {
			logger.InfoNoCtx("No workflows running for this schedule")
			break
		}

		for _, l := range executions {
			wId := l.GetExecution().GetWorkflowId()
			rId := l.GetExecution().GetRunId()

			err = temporalClient.TerminateWorkflow(context.Background(), wId, rId, "Termination Requested")
			if err != nil {
				return errors.Wrap(err, fmt.Sprintf("Unable to signal workflow %s runId %s", workflowId, rId))
			}
			time.Sleep(100 * time.Millisecond)
		}
		time.Sleep(10 * time.Second)
	}

	err = scheduleHandle.Delete(ctx)
	if err != nil {
		return errors.Wrap(err, "Error while deleting scheduled workflow")
	}

	logger.InfoNoCtx(fmt.Sprintf("Cancelled schedule %s", schId))

	return nil
}

func getRunningExecutions(temporalClient client.Client, conf *config.Config, workflowIdPrefix string) ([]*temporalWorkflowPb.WorkflowExecutionInfo, error) {

	var workflowList []*temporalWorkflowPb.WorkflowExecutionInfo
	var nextPageToken []byte = nil
	for {
		list, err := temporalClient.ListWorkflow(context.Background(), &temporalWorkflowserviceSvcPb.ListWorkflowExecutionsRequest{
			Namespace:     conf.Application.Namespace,
			Query:         fmt.Sprintf("WorkflowType = '%s' and ExecutionStatus IN ('Running')", healthEngineNs.CsisStatusCheck),
			NextPageToken: nextPageToken,
			PageSize:      1000,
		})
		if err != nil {
			return nil, errors.Wrap(err, "Error while listing workflows for schedule")
		}

		for _, execinfo := range list.Executions {
			// Check if the workflow params match the executions for this db and table
			if strings.HasPrefix(execinfo.Execution.WorkflowId, workflowIdPrefix) {
				workflowList = append(workflowList, execinfo)
			}
		}

		nextPageToken = list.NextPageToken

		if nextPageToken == nil {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	return workflowList, nil
}

func resumeWorkflow(temporalClient client.Client) error {
	ctx := context.Background()
	schId := "Csis-Check-" + *scheduleId

	scheduleHandle := temporalClient.ScheduleClient().GetHandle(ctx, schId)
	scheduleDescription, schErr := scheduleHandle.Describe(ctx)
	if schErr != nil {
		return errors.Wrap(schErr, "Error describe schedule")
	}

	if !scheduleDescription.Schedule.State.Paused {
		logger.InfoNoCtx("Schedule is not paused. No action to perform.")
		return nil
	}
	logger.InfoNoCtx(fmt.Sprintf("Last msg on schedule: %s", scheduleDescription.Schedule.State.Note))

	err := scheduleHandle.Unpause(ctx, client.ScheduleUnpauseOptions{
		Note: "Unpaused manually using jenkins job",
	})
	if err != nil {
		return errors.Wrap(err, "Error unpausing archival workflow")
	}

	logger.InfoNoCtx("Unpaused archival workflow")
	return nil
}

func getWorkflowId(scheduleId string) string {
	return "wf:" + scheduleId
}
