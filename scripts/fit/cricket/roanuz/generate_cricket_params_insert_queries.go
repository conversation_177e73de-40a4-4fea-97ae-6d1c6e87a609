// nolint
package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"

	"go.uber.org/zap"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"

	vendorspb "github.com/epifi/gamma/api/vendors/roanuz/cricket"
	"github.com/epifi/be-common/pkg/logger"
)

// var (
// 	BatsmanRuleId  = []string{"2e245171-90ee-499f-8e9a-f3312d8ba4cd", "09fdd6cf-0479-4ac5-87e1-82be46607984"}
// 	BowlerRuleId   = []string{"29985512-d48f-4bc0-a575-27fab8b8f615", "16938f89-769a-4d33-a2a2-cadbbaf75c43"}
// 	CricTeamRuleId = []string{"13779302-f9f8-404a-b319-b9e32b814ff6", "de1fae6e-7995-4d41-a76d-a495993b40aa"}
// )

var (
	teamLogoMap = map[string]string{
		"ind":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png",
		"pak":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Pakistan.png",
		"ban":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Bangladesh.png",
		"aus":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png",
		"eng":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/England.png",
		"rsa":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png",
		"afg":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Afghanistan.png",
		"wi":   "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png",
		"nz":   "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/NewZealand.png",
		"sl":   "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SriLanka.png",
		"indw": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/India.png",
		"pakw": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Pakistan.png",
		"banw": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Bangladesh.png",
		"ausw": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Australia.png",
		"engw": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/England.png",
		"rsaw": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SouthAfrica.png",
		"afgw": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Afghanistan.png",
		"wiw":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/WestIndies.png",
		"nzw":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/NewZealand.png",
		"slw":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/SriLanka.png",
		"nep":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/Nepal.png",

		// IPL teams
		"mi":   "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/mi.png",
		"csk":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/csk.png",
		"kkr":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/kkr.png",
		"dc":   "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/dc.png",
		"pbks": "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/pbks.png",
		"rcb":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/rcb.png",
		"srh":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/srh.png",
		"rr":   "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/rr.png",
		"lsg":  "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/lsg.png",
		"gt":   "https://epifi-icons.pointz.in/fittt-images/CricketTeamIcons/IPL/gt.png",

		// IPL womens
		"c__team__miw__37fe2":  "https://epifi-icons.pointz.in/fittt-images/MIW.png",
		"c__team__rcbw__878bd": "https://epifi-icons.pointz.in/fittt-images/RCBW.png",
		"c__team__dcw__5bd62":  "https://epifi-icons.pointz.in/fittt-images/DCW.png",
		"c__team__ggw__518ce":  "https://epifi-icons.pointz.in/fittt-images/GujaratGiantsW.png",
		"c__team__upww__d527d": "https://epifi-icons.pointz.in/fittt-images/UPWarriorzW.png",

		"miw":  "https://epifi-icons.pointz.in/fittt-images/MIW.png",
		"rcbw": "https://epifi-icons.pointz.in/fittt-images/RCBW.png",
		"dcw":  "https://epifi-icons.pointz.in/fittt-images/DCW.png",
		"ggw":  "https://epifi-icons.pointz.in/fittt-images/GujaratGiantsW.png",
		"upww": "https://epifi-icons.pointz.in/fittt-images/UPWarriorzW.png",
	}
)

func initCricketTournamentFile(tournamentName string, matchType cricketMatchType) (*os.File, *bufio.Writer) {
	f, err := os.Create(fmt.Sprintf("%s_%s_rules_and_params.sql", tournamentName, strings.ToLower(matchType.String())))
	if err != nil {
		panic(err)
	}
	w := bufio.NewWriter(f)
	return f, w
}

func generateCricketTournamentFixture(tournamentId string, matchType cricketMatchType) {
	file, fileWriter := initCricketTournamentFile(tournamentId, matchType)
	defer logAndClose(file)
	defer logAndFlush(fileWriter)
	switch matchType {
	case TEST:
		_, _, _, teamIdToPlayers, err := generateTestCricketRules(tournamentId, fileWriter)
		if err != nil {
			logger.Fatal("failed to generate rules and params", zap.Error(err))
		}
		generateCricketMatchesAndSchedules(teamIdToPlayers, tournamentId, "Sports.Cricket.Test."+tagId,
			tagId, true, time.Now(), TEST)
	case ODI:
		_, _, _, teamIdToPlayers, err := generateODICricketRules(tournamentId, fileWriter)
		if err != nil {
			logger.Fatal("failed to generate rules and params", zap.Error(err))
		}
		generateCricketMatchesAndSchedules(teamIdToPlayers, tournamentId, "Sports.Cricket.ODI."+tagId,
			tagId, true, time.Now(), ODI)
	case T20:
		_, _, _, teamIdToPlayers, err := generateT20CricketRules(tournamentId, fileWriter)
		if err != nil {
			logger.Fatal("failed to generate rules and params", zap.Error(err))
		}
		generateCricketMatchesAndSchedules(teamIdToPlayers, tournamentId, "Sports.Cricket.T20."+tagId,
			tagId, true, time.Now(), T20)
	default:
		logger.ErrorNoCtx("match type not handled yet")
	}
	return
}

func generateCricketPossibleParams(tournamentId string, fileWriter *bufio.Writer, batsmanRuleIds, bowlerRuleIds, teamRuleIds []string) (players map[string]*vendorspb.Player,
	playerIdToTeam map[string]*vendorspb.Team, teams map[string]*vendorspb.Team, teamIdToPlayers map[string]map[string]*vendorspb.Player,
	err error) {

	players, playerIdToTeam, teams, teamIdToPlayers, err = getPlayersAndTeams(tournamentId)
	if err != nil {
		logger.ErrorNoCtx("error while fetching player details", zap.Error(err))
		return nil, nil, nil, nil, err
	}

	// inserting player entries into db
	fileWriter.WriteString("INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values\n")
	firstEntry := true
	for _, player := range players {
		if !allowCricketPlayer(player.PlayerId) {
			continue
		}
		team := playerIdToTeam[player.PlayerId]

		if player.GetSeasonalRole() == "keeper" || player.GetSeasonalRole() == "batsman" || player.GetSeasonalRole() == "all_rounder" {
			for _, ruleId := range batsmanRuleIds {
				if !firstEntry {
					fileWriter.WriteString(",\n")
				} else {
					firstEntry = false
				}
				fileWriter.WriteString(fmt.Sprintf(`('%s', '%s', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"%s", "playerName":"%s", "team":{"teamId":"%s", "teamName":"%s", "teamLogo":"%s", "abbreviatedName":"%s"}, "playerType":"BATSMAN"}}' ,false, 'PARAM_STATE_ACTIVE', %d)`, uuid.New().String(), ruleId, player.GetPlayerId(), removeSingleQuotes(player.GetName()), team.GetTeamId(), team.GetName(), teamLogoMap[team.GetTeamId()], team.Code, getWeight(batsmanWeight, player.GetPlayerId())))
			}
		}

		if player.GetSeasonalRole() == "bowler" || player.GetSeasonalRole() == "all_rounder" {
			for _, ruleId := range bowlerRuleIds {
				if !firstEntry {
					fileWriter.WriteString(",\n")
				} else {
					firstEntry = false
				}
				fileWriter.WriteString(fmt.Sprintf(`('%s', '%s', 'CRICKET_PLAYER',' {"cricketPlayerVal":{"playerId":"%s", "playerName":"%s", "team":{"teamId":"%s", "teamName":"%s", "teamLogo":"%s", "abbreviatedName":"%s"}, "playerType":"BOWLER"}}' ,false, 'PARAM_STATE_ACTIVE', %d)`, uuid.New().String(), ruleId, player.GetPlayerId(), player.GetName(), team.GetTeamId(), team.GetName(), teamLogoMap[team.GetTeamId()], team.Code, getWeight(bowlerWeight, player.GetPlayerId())))
			}
		}
	}
	fileWriter.WriteString(";\n")

	// inserting team entries into db
	fileWriter.WriteString("INSERT INTO possible_param_values(id, rule_id, param_value_type, value, is_default_value, state, weight) values\n")
	firstEntry = true
	for _, team := range teams {
		for _, ruleId := range teamRuleIds {
			if !firstEntry {
				fileWriter.WriteString(",\n")
			} else {
				firstEntry = false
			}
			fileWriter.WriteString(fmt.Sprintf(`('%s', '%s', 'CRICKET_TEAM',' {"cricketTeamVal":{"teamId":"%s", "teamName":"%s", "teamLogo":"%s", "abbreviatedName":"%s"}}', false, 'PARAM_STATE_ACTIVE', %d)`, uuid.New().String(), ruleId, team.GetTeamId(), team.GetName(), teamLogoMap[team.GetTeamId()], team.Code, getWeight(teamWeight, team.GetTeamId())))
		}
	}

	fileWriter.WriteString(";\n")
	return players, playerIdToTeam, teams, teamIdToPlayers, nil
}

func removeSingleQuotes(str string) string {
	if strings.Contains(str, "'") {
		str = strings.ReplaceAll(str, "'", "")
	}
	return str
}

func getPlayersAndTeams(tournamentId string) (players map[string]*vendorspb.Player,
	playerIdToTeam map[string]*vendorspb.Team, teams map[string]*vendorspb.Team, teamIdToPlayers map[string]map[string]*vendorspb.Player,
	err error) {
	resp, err := cricketTournament(tournamentId)
	if err != nil {
		return nil, nil, nil, nil, err
	}
	unmarshaller := protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}
	tournamentResp := &vendorspb.TournamentResponse{}

	if err := unmarshaller.Unmarshal(resp, tournamentResp); err != nil {
		logger.ErrorNoCtx("error in unmarshalling cricketTournament response for roanuz api", zap.Error(err))
		return nil, nil, nil, nil, errors.Wrap(err, "error in unmarshalling FetchCricketTournament response for roanuz api")
	}
	players = make(map[string]*vendorspb.Player)
	playerIdToTeam = make(map[string]*vendorspb.Team)
	teamIdToPlayers = make(map[string]map[string]*vendorspb.Player)
	for _, team := range tournamentResp.Data.Teams {
		fmt.Println("team-------", team.Name, " ----- ", team.TeamId)
		resp, err := cricketTeam(tournamentId, team.TeamId)
		if err != nil {
			return nil, nil, nil, nil, err
		}
		unmarshaller := protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}
		teamResp := &vendorspb.TeamResponse{}

		if err := unmarshaller.Unmarshal(resp, teamResp); err != nil {
			logger.ErrorNoCtx("error in unmarshalling cricketTournament response for roanuz api", zap.Error(err))
			return nil, nil, nil, nil, errors.Wrap(err, "error in unmarshalling FetchCricketTournament response for roanuz api")
		}
		bowler := ""
		batsman := ""
		allrounder := ""
		teamIdToPlayers[team.TeamId] = teamResp.Data.Squad.Players
		for _, player := range teamResp.Data.Squad.Players {
			switch player.GetSeasonalRole() {
			case "keeper", "batsman":
				batsman += fmt.Sprint(player.Name, " -=- ", player.PlayerId) + "\n"
			case "bowler":
				bowler += fmt.Sprint(player.Name, " -=- ", player.PlayerId) + "\n"
			case "all_rounder":
				allrounder += fmt.Sprint(player.Name, " -=- ", player.PlayerId) + "\n"
			}

			players[player.PlayerId] = player
			playerIdToTeam[player.PlayerId] = team
		}
		fmt.Println("batsman:::")
		fmt.Println(batsman)
		fmt.Println("bowler:::")
		fmt.Println(bowler)
		fmt.Println("allrounder:::")
		fmt.Println(allrounder)
	}
	return players, playerIdToTeam, tournamentResp.Data.Teams, teamIdToPlayers, nil
}

func allowCricketPlayer(id string) bool {
	return !filterPlayers || selectedCricketPlayerIds[id]
}

var filterPlayers bool = false

var selectedCricketPlayerIds = map[string]bool{}

func getWeight(weightMap map[string]int, id string) int {
	w, exist := weightMap[id]
	if exist {
		return w
	}
	return 1
}
