//nolint:funlen,dupl
package job

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/jszwec/csvutil"
	errors2 "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/creditreportv2"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	address2 "github.com/epifi/gamma/pkg/address"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors"
	vendorMappingDao "github.com/epifi/gamma/vendormapping/dao"
)

type CsvFileModel struct {
	ActorId string `csv:"actor_id"`
}

type OneTimeJob struct {
	s3Client           s3.S3Client
	creditReportClient creditreportv2.CreditReportManagerClient
	lseDao             dao.LoanStepExecutionsDao
	loanRequestDao     dao.LoanRequestsDao
	loanAccountDao     dao.LoanAccountsDao
	vendorMappingDao   vendorMappingDao.IVendorMappingDAO
}

func NewOneTimeJob(
	s3Client s3.S3Client,
	loanAccountDao dao.LoanAccountsDao,
	creditReportClient creditreportv2.CreditReportManagerClient,
	lseDao dao.LoanStepExecutionsDao,
	loanRequestDao dao.LoanRequestsDao,
	vendorMappingDao vendorMappingDao.IVendorMappingDAO,
) *OneTimeJob {
	return &OneTimeJob{
		s3Client:           s3Client,
		loanAccountDao:     loanAccountDao,
		creditReportClient: creditReportClient,
		lseDao:             lseDao,
		loanRequestDao:     loanRequestDao,
		vendorMappingDao:   vendorMappingDao,
	}
}

type OneTimeJobArgs struct {
	Source string `json:"source"`
	S3Path string `json:"s3Path"`
}

func (b *OneTimeJob) GetArgs() interface{} {
	return &OneTimeJobArgs{}
}
func (b *OneTimeJob) Run(ctx context.Context, args ...interface{}) error {
	source := args[0].(*OneTimeJobArgs).Source
	s3Path := args[0].(*OneTimeJobArgs).S3Path
	switch source {
	case "FAILED_APPLICATION_ADDRESS":
		return b.FetchFailedApplicationAddress(ctx, s3Path)
	case "LSE_ADDRESS":
		return b.FetchAddressDetailsForLAIdsLSEAddress(ctx, s3Path)
	case "LSE_CKYC":
		return b.FetchAddressDetailsForLAIdsLSECkyc(ctx)
	case "BUREAU_ADRESS":
		return b.FetchAddressDetailsForLANumberBureauReport(ctx)
	case "BUREAU_PHONE_NUMBER":
		return b.FetchPhoneNumberDetailsForLANumberBureauReport(ctx)
	default:
		return errors.New("invalid args")
	}
}
func pushToSyncMap(syncMap *sync.Map, key, value string) {
	val, ok := syncMap.Load(key)
	if !ok {
		syncMap.Store(key, []string{value})
		return
	}
	addresses := val.([]string)
	addresses = append(addresses, value)
	syncMap.Store(key, addresses)
}

func (o *OneTimeJob) FetchFailedApplicationAddress(ctx context.Context, s3Path string) error {
	var addresses sync.Map
	var errorMap sync.Map

	csvFileBytes, readError := o.s3Client.Read(ctx, s3Path)
	if readError != nil {
		return errors2.Wrap(readError, "read from s3Client failed")
	}

	var actorIdsForAddressetch []*CsvFileModel
	if err := csvutil.Unmarshal(csvFileBytes, &actorIdsForAddressetch); err != nil {
		return errors2.Wrap(err, "error in csvutil.Unmarshal for csvFileBytes")
	}

	fireHoseIds := make([]string, 0)
	for _, firehose := range actorIdsForAddressetch {
		fireHoseIds = append(fireHoseIds, firehose.ActorId)
	}
	fireHoseIdToActorIdMap := vendors.GetActorIdsFromFirehoseIds(ctx, fireHoseIds, o.vendorMappingDao)
	finalActorIds := make([]string, 0)
	for _, actorId := range fireHoseIdToActorIdMap {
		finalActorIds = append(finalActorIds, actorId)
	}
	g, gctx := errgroup.WithContext(ctx)

	for idx, actorId := range finalActorIds {
		logger.InfoNoCtx(fmt.Sprintf("processing entry at index: %d with laId: %s", idx, actorId))

		lrs, err := o.loanRequestDao.GetByActorIdAndVendorAndStatus(ctx, actorId, palPb.Vendor_LIQUILOANS, []palPb.LoanRequestStatus{})
		if err != nil {
			errorMap.Store(actorId, err.Error())
			continue
		}
		if idx%100 == 0 {
			time.Sleep(1 * time.Second)
		}
		lr := lrs[0]
		var lseType palPb.LoanStepExecutionFlow
		if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION {
			lseType = palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION
		} else {
			lseType = palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY
		}
		g.Go(func() error {
			lseaddr, err := o.lseDao.GetByRefIdAndFlowAndName(gctx, lr.GetId(), lseType, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
			if err == nil && lseaddr.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails() != nil {
				pushToSyncMap(&addresses, actorId, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseaddr.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails())))
			}
			return nil
		})
		g.Go(func() error {
			lseckyc, err := o.lseDao.GetByRefIdAndFlowAndName(gctx, lr.GetId(), lseType, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC)
			if err == nil && lseckyc.GetDetails().GetCkycStepData().GetPermanentAddress() != nil {
				pushToSyncMap(&addresses, actorId, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseckyc.GetDetails().GetCkycStepData().GetPermanentAddress())))
			}
			return nil
		})
		g.Go(func() error {
			lseaddr, err := o.lseDao.GetByRefIdAndFlowAndName(gctx, lr.GetId(), lseType, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS)
			if err == nil && lseaddr.GetDetails().GetCkycStepData().GetPermanentAddress() != nil {
				pushToSyncMap(&addresses, actorId, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseaddr.GetDetails().GetCkycStepData().GetPermanentAddress())))
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		logger.Error(ctx, "failed to fetch details from lse address address for some laIds", zap.Error(err))
	}
	maxCols := 0
	for _, actorVal := range finalActorIds {
		val, ok := addresses.Load(actorVal)
		if ok {
			entries := val.([]string)
			if len(entries) > maxCols {
				maxCols = len(entries)
			}
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("max cols: %d", maxCols))
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	rows := make([][]string, 0)
	header := make([]string, 0)
	header = append(header, "actor_id")
	for i := 1; i <= maxCols; i++ {
		header = append(header, fmt.Sprintf("address_%v", i))
	}
	for _, actor := range finalActorIds {
		row := make([]string, maxCols+1)
		row[0] = actor
		val, ok := addresses.Load(actor)
		var addressesLaid []string
		if ok {
			addressesLaid = val.([]string)
		}
		for idx, address := range addressesLaid {
			row[idx+1] = address
		}
		rows = append(rows, row)
	}
	err := writer.WriteAll(rows)
	if err != nil {
		return err
	}
	// Flush and close the writer
	writer.Flush()
	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
	path := fmt.Sprintf("data-extraction/%d%d%d-addresses-against-actorId_(lse_address).csv",
		dateNow.GetYear(), dateNow.GetMonth(), dateNow.GetDay())
	signedUrl, writeToS3error := o.s3Client.WriteAndGetPreSignedUrl(ctx, path, []byte(tuple.String()), 1800)
	if writeToS3error != nil {
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Signed URL(LSE ADDRESS): %s", signedUrl))
	errorMap.Range(func(key, value any) bool {
		fmt.Println(key, value)
		return false
	})
	return nil
}

func (p *OneTimeJob) FetchAddressDetailsForLAIds(ctx context.Context) error {
	var addresses sync.Map
	var errors sync.Map
	g, gctx := errgroup.WithContext(ctx)
	for idx, laId := range laIdsforAddressFetch {
		logger.InfoNoCtx(fmt.Sprintf("processing entry at index: %d with laId: %s", idx, laId))
		la, err := p.loanAccountDao.GetById(ctx, laId)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
			errors.Store(laId, err.Error())
			continue
		}
		lrs, err := p.loanRequestDao.GetByLoanAccountIdAndType(ctx, laId, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
			errors.Store(laId, err.Error())
			continue
		}
		lr := func(lrs []*palPb.LoanRequest) *palPb.LoanRequest {
			for _, lr := range lrs {
				if lr.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
					return lr
				}
			}
			return nil
		}(lrs)
		if lr == nil {
			logger.Error(ctx, "lr not found", zap.String(logger.LOAN_ACCOUNT_ID, laId))
			continue
		}
		g.Go(func() error {
			lseaddr, err := p.lseDao.GetByRefIdAndFlowAndName(gctx, lr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
			if err != nil {
				logger.Error(gctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laId, err.Error())
			} else {
				pushToSyncMap(&addresses, laId, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseaddr.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails())))
			}
			return nil
		})
		g.Go(func() error {
			lseckyc, err := p.lseDao.GetByRefIdAndFlowAndName(gctx, lr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC)
			if err != nil {
				logger.Error(gctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laId, err.Error())
			} else {
				pushToSyncMap(&addresses, laId, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseckyc.GetDetails().GetCkycStepData().GetPermanentAddress())))
			}
			return nil
		})
		g.Go(func() error {
			resp, err := p.creditReportClient.GetCreditReport(gctx, &creditreportv2.GetCreditReportRequest{
				ActorId: la.GetActorId(),
			})
			if te := epifigrpc.RPCError(resp, err); te != nil {
				logger.Error(gctx, te.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laId, te.Error())
			} else {
				for _, address := range resp.GetCreditReportData().GetAddress() {
					postalAddress := &postaladdress.PostalAddress{
						AddressLines: []string{
							address.GetFirstLineOfAddressNonNormalized(), address.GetSecondLineOfAddressNonNormalized(), address.GetThirdLineOfAddressNonNormalized(), address.GetFifthLineOfAddressNonNormalized(),
						},
						PostalCode:         address.GetZipPostalCodeNonNormalized(),
						RegionCode:         address.GetCountryCodeNonNormalized(),
						AdministrativeArea: address.GetStateNonNormalized(),
						Locality:           address.GetCityNonNormalized(),
					}
					pushToSyncMap(&addresses, laId, address2.ConvertPostalAddressToString(postalAddress))
				}
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		logger.Error(ctx, "failed to fecth details from experian analytics address for some laIds", zap.Error(err))
	}
	maxCols := 0
	for _, laId := range laIdsforAddressFetch {
		val, ok := addresses.Load(laId)
		if ok {
			entries := val.([]string)
			if len(entries) > maxCols {
				maxCols = len(entries)
			}
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("max cols: %d", maxCols))
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	rows := make([][]string, 0)
	header := make([]string, 0)
	header = append(header, "loan_account_id")
	for i := 1; i <= maxCols; i++ {
		header = append(header, fmt.Sprintf("address_%v", i))
	}
	for _, laId := range laIdsforAddressFetch {
		logger.InfoNoCtx(fmt.Sprintf("appending laId:%s", laId))
		row := make([]string, maxCols+1)
		row[0] = laId
		val, ok := addresses.Load(laId)
		var addressesLaid []string
		if ok {
			addressesLaid = val.([]string)
		}
		for idx, address := range addressesLaid {
			row[idx+1] = address
		}
		rows = append(rows, row)
	}
	err := writer.WriteAll(rows)
	if err != nil {
		return err
	}
	// Flush and close the writer
	writer.Flush()
	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
	path := fmt.Sprintf("data-extraction/%d%d%d-addresses-against-loan-accounts.csv",
		dateNow.GetYear(), dateNow.GetMonth(), dateNow.GetDay())
	signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(ctx, path, []byte(tuple.String()), 1800)
	if writeToS3error != nil {
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Signed URL: %s", signedUrl))
	errors.Range(func(key, value any) bool {
		fmt.Println(key, value)
		return false
	})
	return nil
}
func (p *OneTimeJob) FetchAddressDetailsForLAIdsLSEAddress(ctx context.Context, filepath string) error {
	var addresses sync.Map
	var errors sync.Map

	csvFileBytes, readError := p.s3Client.Read(ctx, filepath)
	if readError != nil {
		return errors2.Wrap(readError, "read from s3Client failed")
	}

	var actorIdsForAddressetch []*CsvFileModel
	if err := csvutil.Unmarshal(csvFileBytes, &actorIdsForAddressetch); err != nil {
		return errors2.Wrap(err, "error in csvutil.Unmarshal for csvFileBytes")
	}

	fireHoseIds := make([]string, 0)
	for _, firehose := range actorIdsForAddressetch {
		fireHoseIds = append(fireHoseIds, firehose.ActorId)
	}
	fireHoseIdToActorIdMap := vendors.GetActorIdsFromFirehoseIds(ctx, fireHoseIds, p.vendorMappingDao)
	finalActorIds := make([]string, 0)
	for _, actorId := range fireHoseIdToActorIdMap {
		finalActorIds = append(finalActorIds, actorId)
	}
	g, gctx := errgroup.WithContext(ctx)

	for idx, actorId := range finalActorIds {
		logger.InfoNoCtx(fmt.Sprintf("processing entry at index: %d with laId: %s", idx, actorId))

		las, err := p.loanAccountDao.GetByActorIdStatusAndLoanProgram(ctx, actorId, []palPb.LoanAccountStatus{palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE}, []palPb.LoanProgram{})
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String(logger.ACTOR_ID_V2, actorId))
			errors.Store(actorId, err.Error())
			continue
		}
		// fetch the first loan account
		la := las[0]
		laId := la.GetId()
		var lrs []*palPb.LoanRequest
		lrs, err = p.loanRequestDao.GetByActorIdAndVendorAndStatus(ctx, la.GetActorId(), la.GetVendor(), []palPb.LoanRequestStatus{
			palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
		})
		if err != nil {
			errors.Store(laId, err.Error())
			continue
		}
		var lrEl, lrCr *palPb.LoanRequest
		for _, lr := range lrs {
			if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY && (lrEl == nil) {
				lrEl = lr
			}
			if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION && (lrCr == nil) {
				lrCr = lr
			}
		}
		if idx%10 == 0 {
			time.Sleep(1 * time.Second)
		}
		g.Go(func() error {
			var lseaddr *palPb.LoanStepExecution
			var err error
			switch la.GetLoanProgram() {
			case palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL, palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND, palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
				if lrEl == nil {
					errors.Store(laId, "lr not found")
				} else {
					lseaddr, err = p.lseDao.GetByRefIdAndFlowAndName(gctx, lrEl.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
				}
			default:
				if lrCr == nil {
					errors.Store(laId, "lr not found")
				} else {
					lseaddr, err = p.lseDao.GetByRefIdAndFlowAndName(gctx, lrCr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
				}
			}
			if err != nil {
				logger.Error(gctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laId, err.Error())
			} else {
				pushToSyncMap(&addresses, laId, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseaddr.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails())))
			}
			switch la.GetLoanProgram() {
			case palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL, palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND, palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
				if lrEl == nil {
					errors.Store(laId, "lr not found")
				} else {
					lseaddr, err = p.lseDao.GetByRefIdAndFlowAndName(gctx, lrEl.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS)
				}
			default:
				if lrCr == nil {
					errors.Store(laId, "lr not found")
				} else {
					lseaddr, err = p.lseDao.GetByRefIdAndFlowAndName(gctx, lrCr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS)
				}
			}
			if err != nil {
				logger.Error(gctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laId, err.Error())
			} else {
				pushToSyncMap(&addresses, actorId, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseaddr.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails())))
			}
			return nil
		})

	}
	if err := g.Wait(); err != nil {
		logger.Error(ctx, "failed to fecth details from lse address address for some laIds", zap.Error(err))
	}
	maxCols := 0
	for _, actorVal := range actorIdsForAddressetch {
		val, ok := addresses.Load(actorVal.ActorId)
		if ok {
			entries := val.([]string)
			if len(entries) > maxCols {
				maxCols = len(entries)
			}
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("max cols: %d", maxCols))
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	rows := make([][]string, 0)
	header := make([]string, 0)
	header = append(header, "loan_account_id")
	for i := 1; i <= maxCols; i++ {
		header = append(header, fmt.Sprintf("address_%v", i))
	}
	for _, actor := range actorIdsForAddressetch {
		logger.InfoNoCtx(fmt.Sprintf("appending laId:%s", actor))
		row := make([]string, maxCols+1)
		row[0] = actor.ActorId
		val, ok := addresses.Load(actor.ActorId)
		var addressesLaid []string
		if ok {
			addressesLaid = val.([]string)
		}
		for idx, address := range addressesLaid {
			row[idx+1] = address
		}
		rows = append(rows, row)
	}
	err := writer.WriteAll(rows)
	if err != nil {
		return err
	}
	// Flush and close the writer
	writer.Flush()
	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
	path := fmt.Sprintf("data-extraction/%d%d%d-addresses-against-loan-accounts_(lse_address).csv",
		dateNow.GetYear(), dateNow.GetMonth(), dateNow.GetDay())
	signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(ctx, path, []byte(tuple.String()), 1800)
	if writeToS3error != nil {
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Signed URL(LSE ADDRESS): %s", signedUrl))
	errors.Range(func(key, value any) bool {
		fmt.Println(key, value)
		return false
	})
	return nil
}
func (p *OneTimeJob) FetchAddressDetailsForLAIdsLSECkyc(ctx context.Context) error {
	var addresses sync.Map
	var errors sync.Map
	g, gctx := errgroup.WithContext(ctx)
	for idx, gLaId := range laIdsforAddressFetch {
		laNumber := gLaId
		la, err := p.loanAccountDao.GetById(ctx, laNumber)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String("ACCOUNT_NUMBER", laNumber))
			errors.Store(laNumber, err.Error())
			continue
		}
		laId := la.GetId()
		logger.InfoNoCtx(fmt.Sprintf("processing entry at index: %d with laId: %s", idx, laId))
		lrs, err := p.loanRequestDao.GetByLoanAccountIdAndType(ctx, laId, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
			errors.Store(laNumber, err.Error())
			continue
		}
		lr := func(lrs []*palPb.LoanRequest) *palPb.LoanRequest {
			for _, lr := range lrs {
				if lr.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
					return lr
				}
			}
			return nil
		}(lrs)
		if lr == nil {
			logger.Error(ctx, "lr not found", zap.String(logger.LOAN_ACCOUNT_ID, laId))
			continue
		}
		g.Go(func() error {
			lseckyc, err := p.lseDao.GetByRefIdAndFlowAndName(gctx, lr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC)
			if err != nil {
				logger.Error(gctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laNumber, err.Error())
			} else {
				pushToSyncMap(&addresses, laNumber, address2.ConvertPostalAddressToString(ConvertToPostalAddress(lseckyc.GetDetails().GetCkycStepData().GetPermanentAddress())))
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		logger.Error(ctx, "failed to fecth details from lse ckyc address for some laIds", zap.Error(err))
	}
	maxCols := 0
	for _, laNumber := range laIdsforAddressFetch {
		val, ok := addresses.Load(laNumber)
		if ok {
			entries := val.([]string)
			if len(entries) > maxCols {
				maxCols = len(entries)
			}
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("max cols: %d", maxCols))
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	rows := make([][]string, 0)
	header := make([]string, 0)
	header = append(header, "account_number")
	for i := 1; i <= maxCols; i++ {
		header = append(header, fmt.Sprintf("address_%v", i))
	}
	for _, laNumber := range laIdsforAddressFetch {
		logger.InfoNoCtx(fmt.Sprintf("appending laNumber:%s", laNumber))
		row := make([]string, maxCols+1)
		row[0] = laNumber
		val, ok := addresses.Load(laNumber)
		var addressesLaid []string
		if ok {
			addressesLaid = val.([]string)
		}
		for idx, address := range addressesLaid {
			row[idx+1] = address
		}
		rows = append(rows, row)
	}
	err := writer.WriteAll(rows)
	if err != nil {
		return err
	}
	// Flush and close the writer
	writer.Flush()
	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
	path := fmt.Sprintf("data-extraction/%d%d%d-addresses-against-loan-accounts_(lse_ckyc).csv",
		dateNow.GetYear(), dateNow.GetMonth(), dateNow.GetDay())
	signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(ctx, path, []byte(tuple.String()), 1800)
	if writeToS3error != nil {
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Signed URL(LSE CKYC): %s", signedUrl))
	errors.Range(func(key, value any) bool {
		fmt.Println(key, value)
		return false
	})
	return nil
}
func (p *OneTimeJob) FetchAddressDetailsForLANumberBureauReport(ctx context.Context) error {
	var addresses sync.Map
	var errors sync.Map
	g, gctx := errgroup.WithContext(ctx)
	for idx, gLaId := range laIdsforAddressFetch {
		laNumber := gLaId
		logger.InfoNoCtx(fmt.Sprintf("processing entry at index: %d with laNumber: %s", idx, laNumber))
		la, err := p.loanAccountDao.GetById(ctx, laNumber)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String("ACCOUNT_NUMBER", laNumber))
			errors.Store(laNumber, err.Error())
			continue
		}
		laId := la.GetId()
		lrs, err := p.loanRequestDao.GetByLoanAccountIdAndType(ctx, laId, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
			errors.Store(laNumber, err.Error())
			continue
		}
		lr := func(lrs []*palPb.LoanRequest) *palPb.LoanRequest {
			for _, lr := range lrs {
				if lr.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
					return lr
				}
			}
			return nil
		}(lrs)
		if lr == nil {
			logger.Error(ctx, "lr not found", zap.String(logger.LOAN_ACCOUNT_ID, laId))
			continue
		}
		g.Go(func() error {
			resp, err := p.creditReportClient.GetCreditReport(gctx, &creditreportv2.GetCreditReportRequest{
				ActorId: la.GetActorId(),
			})
			if te := epifigrpc.RPCError(resp, err); te != nil {
				logger.Error(gctx, te.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laNumber, te.Error())
			} else {
				for _, address := range resp.GetCreditReportData().GetAddress() {
					postalAddress := &postaladdress.PostalAddress{
						AddressLines: []string{
							address.GetFirstLineOfAddressNonNormalized(), address.GetSecondLineOfAddressNonNormalized(), address.GetThirdLineOfAddressNonNormalized(), address.GetFifthLineOfAddressNonNormalized(),
						},
						PostalCode:         address.GetZipPostalCodeNonNormalized(),
						RegionCode:         address.GetCountryCodeNonNormalized(),
						AdministrativeArea: address.GetStateNonNormalized(),
						Locality:           address.GetCityNonNormalized(),
					}
					pushToSyncMap(&addresses, laNumber, address2.ConvertPostalAddressToString(postalAddress))
				}
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		logger.Error(ctx, "failed to fecth details from bureau report address for some laIds", zap.Error(err))
	}
	maxCols := 0
	for _, laNumber := range laIdsforAddressFetch {
		val, ok := addresses.Load(laNumber)
		if ok {
			entries := val.([]string)
			if len(entries) > maxCols {
				maxCols = len(entries)
			}
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("max cols: %d", maxCols))
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	rows := make([][]string, 0)
	header := make([]string, 0)
	header = append(header, "account_number")
	for i := 1; i <= maxCols; i++ {
		header = append(header, fmt.Sprintf("address_%v", i))
	}
	for _, laNumber := range laIdsforAddressFetch {
		logger.InfoNoCtx(fmt.Sprintf("appending laNumbers:%s", laNumber))
		row := make([]string, maxCols+1)
		row[0] = laNumber
		val, ok := addresses.Load(laNumber)
		var addressesLaid []string
		if ok {
			addressesLaid = val.([]string)
		}
		for idx, address := range addressesLaid {
			row[idx+1] = address
		}
		rows = append(rows, row)
	}
	err := writer.WriteAll(rows)
	if err != nil {
		return err
	}
	// Flush and close the writer
	writer.Flush()
	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
	path := fmt.Sprintf("data-extraction/%d%d%d-addresses-against-loan-accounts_(bureau_report).csv",
		dateNow.GetYear(), dateNow.GetMonth(), dateNow.GetDay())
	signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(ctx, path, []byte(tuple.String()), 1800)
	if writeToS3error != nil {
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Signed URL(BUREAU REPORT): %s", signedUrl))
	errors.Range(func(key, value any) bool {
		fmt.Println(key, value)
		return false
	})
	return nil
}
func (p *OneTimeJob) FetchPhoneNumberDetailsForLANumberBureauReport(ctx context.Context) error {
	var phoneNumbs sync.Map
	var errors sync.Map
	g, gctx := errgroup.WithContext(ctx)
	for idx, laNumber := range laNumbers {
		logger.InfoNoCtx(fmt.Sprintf("processing entry at index: %d with laNumber: %s", idx, laNumber))
		la, err := p.loanAccountDao.GetByAccountNumberAndVendor(ctx, laNumber, palPb.Vendor_IDFC)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String("ACCOUNT_NUMBER", laNumber))
			errors.Store(laNumber, err.Error())
			continue
		}
		laId := la.GetId()
		lrs, err := p.loanRequestDao.GetByLoanAccountIdAndType(ctx, laId, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
		if err != nil {
			logger.Error(ctx, err.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
			errors.Store(laNumber, err.Error())
			continue
		}
		lr := func(lrs []*palPb.LoanRequest) *palPb.LoanRequest {
			for _, lr := range lrs {
				if lr.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
					return lr
				}
			}
			return nil
		}(lrs)
		if lr == nil {
			logger.Error(ctx, "lr not found", zap.String(logger.LOAN_ACCOUNT_ID, laId))
			continue
		}
		g.Go(func() error {
			resp, err := p.creditReportClient.GetCreditReport(gctx, &creditreportv2.GetCreditReportRequest{
				ActorId: la.GetActorId(),
			})
			if te := epifigrpc.RPCError(resp, err); te != nil {
				logger.Error(gctx, te.Error(), zap.String(logger.LOAN_ACCOUNT_ID, laId))
				errors.Store(laNumber, te.Error())
			} else {
				for _, capsApplicationDetails := range resp.GetCreditReport().GetCreditReportDataRaw().GetCaps().GetCapsApplicationDetailsArray() {
					pushToSyncMap(&phoneNumbs, laNumber, capsApplicationDetails.GetCapsApplicantDetails().GetMobilePhoneNumber())
				}
			}
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		logger.Error(ctx, "failed to fecth details from bureau report phone numbers for some laIds", zap.Error(err))
	}
	maxCols := 0
	for _, laNumber := range laNumbers {
		val, ok := phoneNumbs.Load(laNumber)
		if ok {
			entries := val.([]string)
			if len(entries) > maxCols {
				maxCols = len(entries)
			}
		}
	}
	logger.InfoNoCtx(fmt.Sprintf("max cols: %d", maxCols))
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	rows := make([][]string, 0)
	header := make([]string, 0)
	header = append(header, "account_number")
	for i := 1; i <= maxCols; i++ {
		header = append(header, fmt.Sprintf("address_%v", i))
	}
	for _, laNumber := range laNumbers {
		logger.InfoNoCtx(fmt.Sprintf("appending laNumber:%s", laNumber))
		row := make([]string, maxCols+1)
		row[0] = laNumber
		val, ok := phoneNumbs.Load(laNumber)
		var addressesLaid []string
		if ok {
			addressesLaid = val.([]string)
		}
		for idx, address := range addressesLaid {
			row[idx+1] = address
		}
		rows = append(rows, row)
	}
	err := writer.WriteAll(rows)
	if err != nil {
		return err
	}
	// Flush and close the writer
	writer.Flush()
	dateNow := datetime.TimeToDateInLoc(time.Now().In(datetime.IST), datetime.IST)
	path := fmt.Sprintf("data-extraction/%d%d%d-phone-numbers-against-loan-accounts_(bureau_report).csv",
		dateNow.GetYear(), dateNow.GetMonth(), dateNow.GetDay())
	signedUrl, writeToS3error := p.s3Client.WriteAndGetPreSignedUrl(ctx, path, []byte(tuple.String()), 1800)
	if writeToS3error != nil {
		return err
	}
	logger.Info(ctx, fmt.Sprintf("Signed URL(BUREAU REPORT): %s", signedUrl))
	errors.Range(func(key, value any) bool {
		fmt.Println(key, value)
		return false
	})
	return nil
}
