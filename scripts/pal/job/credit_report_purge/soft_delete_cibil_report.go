package credit_report_purge

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/creditreportv2/dao/model"
	palJobConfig "github.com/epifi/gamma/scripts/pal/config"
)

// SoftDeleteCibilReportDataJob job can be used to delete flattened cibil report data where raw data has already been purged.
// It iterates through all the purged cibil reports and soft delete the corresponding flattened data.
type SoftDeleteCibilReportDataJob struct {
	config        *palJobConfig.Config
	featureEnggDb *gorm.DB
}

func NewSoftDeleteCibilReportDataJob(
	featureEnggDb types.PostgresPGDB,
	config *palJobConfig.Config,
) *SoftDeleteCibilReportDataJob {
	return &SoftDeleteCibilReportDataJob{
		featureEnggDb: featureEnggDb,
		config:        config,
	}
}

type SoftDeleteCibilReportDataJobArgs struct {
	BatchSize int `json:"batchSize"`
	LoopLimit int `json:"loopLimit"`
}

func (p *SoftDeleteCibilReportDataJob) GetArgs() interface{} {
	return &SoftDeleteCibilReportDataJobArgs{}
}

// nolint:funlen, gocritic, govet, gosec
func (p *SoftDeleteCibilReportDataJob) Run(ctx context.Context, args ...interface{}) error {
	jobArgs := args[0].(*SoftDeleteCibilReportDataJobArgs)
	err := p.validateJobArgs(ctx, jobArgs)
	if err != nil {
		return fmt.Errorf("error validating job args : %w", err)
	}
	logger.Info(ctx, "starting deletion of raw cibil report data and soft deletion of flattened cibil report data.",
		zap.Int("loopLimit", jobArgs.LoopLimit),
		zap.Int("batchSize", jobArgs.BatchSize),
	)
	var (
		creditReportIds  []string
		reportsProcessed int
		// calculating max value of created_at for reports to be purged by subtracting `SoftDeletionMinAge` from current time.
		maxConsentValidity = time.Now().Add(-1 * p.config.CreditReportPurgeConfig.CibilSoftDeletionMinAge)
	)
	for i := 0; i < jobArgs.LoopLimit; i++ {
		logger.Info(ctx, "starting new batch", zap.Int("batchNo", i+1),
			zap.Int("reportsSuccessfullyProcessed", reportsProcessed))

		queryErr := p.featureEnggDb.Model(&model.CreditReportRaw{}).
			Where("consent_valid_till <= ? and deleted_at_unix = 0", maxConsentValidity).
			Limit(jobArgs.BatchSize).
			Select("id").
			Find(&creditReportIds).Error
		if queryErr != nil {
			logger.Error(ctx, "error while fetching next batch of cibil reports for soft deletion",
				zap.Error(queryErr),
				zap.Int("reportsSuccessfullyProcessed", reportsProcessed))
			return fmt.Errorf("error while selecting cibil report for expired users, err: %w", queryErr)
		}

		logger.Info(ctx, "successfully fetched list of cibil reports to be processed",
			zap.Int("batchSizeFetched", len(creditReportIds)))

		for _, id := range creditReportIds {
			purgeErr := p.purgeDataForCibil(ctx, id)
			if purgeErr != nil {
				logger.Error(ctx, fmt.Sprintf("error while purging cibil report data with id=%s", id),
					zap.Error(purgeErr),
					zap.Int("reportsSuccessfullyProcessed", reportsProcessed))
				return fmt.Errorf("error while soft deleting flattened data for cibil report with id=%s", id)
			}
			reportsProcessed++
		}
		if len(creditReportIds) < jobArgs.BatchSize {
			logger.Info(ctx, "current report batch array size is smaller than the requested batch size")
			break
		}
	}
	logger.Info(ctx, "successfully completed processing", zap.Int("reportsSuccessfullyProcessed", reportsProcessed))
	return nil
}

func (p *SoftDeleteCibilReportDataJob) validateJobArgs(_ context.Context, jobArgs *SoftDeleteCibilReportDataJobArgs) error {
	if jobArgs.LoopLimit <= 0 {
		return fmt.Errorf("loop limit cannot be smaller than 1")
	}
	if jobArgs.BatchSize <= 0 || jobArgs.BatchSize > 10_000 {
		return fmt.Errorf("batch size should be between 1-10,000")
	}
	return nil
}

func (p *SoftDeleteCibilReportDataJob) purgeDataForCibil(_ context.Context, creditReportId string) error {
	currTime := time.Now()
	// Soft delete data from flattened tables
	txnErr := p.featureEnggDb.Transaction(func(db *gorm.DB) error {
		for _, table := range flattenCibilReportTables {
			query := fmt.Sprintf("UPDATE %s SET deleted_at = ?, updated_at = now() WHERE credit_report_id = ?", table)
			if err := db.Exec(query, currTime, creditReportId).Error; err != nil {
				return errors.Wrapf(err, "error while soft deleting row from %s", table)
			}
		}
		updateCols := []string{"raw_report", "updated_at", "deleted_at_unix"}
		updateCreditReportMap := make(map[string]interface{})
		updateCreditReportMap["raw_report"] = nil
		updateCreditReportMap["deleted_at_unix"] = time.Now().Unix()
		updateCreditReportMap["updated_at"] = time.Now()
		err := db.Model(&model.CreditReportRaw{}).Select(updateCols).Where("id = ?", creditReportId).Updates(updateCreditReportMap).Error
		if err != nil {
			return fmt.Errorf("error while deleting credit report raw data for user : %w", err)
		}
		return nil
	})
	if txnErr != nil {
		return fmt.Errorf("error in soft deleting flatten tables in txn block, err: %w", txnErr)
	}
	return nil
}
