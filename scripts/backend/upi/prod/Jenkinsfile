@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '25', artifactNumToKeepStr: '25'))
        timestamps()
    }
    parameters {
        choice(name: 'ENV', choices: ['prod'], description: 'Environment to run on.')
    }
    environment {
        ENV = "${params.ENV}"
        GOPROXY = "https://goproxy.epifi.in"
    }
    stages {
        stage('Repo clone') {
            steps {
                // performs shallow clone of gamma by default
                // to perform deep clone pass `shallow: false` explicitly
                gitCheckout(repoName: 'gamma', gitBranch: "${BUILD_BRANCH}", githubOrg: 'epifi')
            }
        }
        stage('Refresh') {
            when {
                expression { params.REFRESH == true }
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.description = "#${BUILD_NUMBER}-Refresh"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Build binary') {
            steps {
                dir('gamma') {
                    sh ' make build-script target="Pay/upi" '
                }
            }
        }
        stage("Run Script") {
            steps {
                dir("gamma") {
                    script {
                        sh """
                            cd ./output/Pay/upi
                            ENVIRONMENT=${ENV} ./Pay/upi_bin
                        """
                    }
                }
            }
        }
    }
}
