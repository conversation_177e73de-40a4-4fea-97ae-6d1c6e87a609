pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
    }
    parameters {
        string(name: 'phoneNumbers', description: 'comma separated list of phone numbers of users for whose status needs to be tracked.')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        TARGET="salaryprogram_b2b_users_onb_status_tracking"
        VERSION="1.0.1"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Fetch Binary') {
            steps {
                dir('app'){
                    script{
                    sh "aws s3 cp s3://epifi-gamma-binaries-prod/${TARGET}/linux/amd64/${VERSION} ./${TARGET}.zip"
                    sh "unzip -o ${TARGET}.zip"
                    }
                }
            }
        }
        stage('Parge Build Args') {
            steps {
                script {
                    def buildArgs = [:]

                    // Loop through all environment variables
                    params.keySet().findAll { it.startsWith("BUILD_ARG_") }.each { key ->
                        // Remove the prefix and store the value with key in buildArgs map
                        buildArgs[key - "BUILD_ARG_"] = env[key]
                    }

                    // Construct the command with proper argument values
                    env.command = ""
                    buildArgs.each { k, v ->
                        // Enclose the value in quotes only if it contains spaces
                        def argValue = (v.contains(" ")) ? "\'${v}\'" : v
                        env.command += "-${k}=${argValue} "
                    }
                    echo env.command.trim()
                }
            }
        }
        stage("Execute Binary") {
            steps {
                dir('app') {
                    script {
                        sh """
                            cd ${TARGET}
                            chmod 700 ${TARGET}_bin
                            ENVIRONMENT=prod DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true ./${TARGET}_bin -phoneNumbers=${phoneNumbers}
                        """
                    }
                }
            }
        }
    }

    post {
        failure {
            slackSend channel: "<!subteam^S05UWQU2HGA|@cg-oncall>", color: "#8B0000", failOnError:true, message:"<@S05UWQU2HGA> <@U048R2T4NQK> <@U06EBKH9WJH> Build failed  - ${env.JOB_NAME} #${env.BUILD_NUMBER} (<${env.BUILD_URL}|View Logs>)"
        }
    }
}
