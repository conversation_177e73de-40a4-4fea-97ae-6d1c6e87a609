@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
    }
    parameters {
    choice(name: 'ENV', choices: ['demo', 'qa', 'staging', 'uat'], description: 'Environment in which to execute the script')
        string(name: 'JobName', description: 'Name of pal script job')
        string(name: 'Arguments', description: 'Refers to arguments for the given job in JSON format')
        booleanParam(name: 'REFRESH', defaultValue: false, description: '<PERSON><PERSON>, ignore this variable! This is for DevO<PERSON> to refresh Jenkinsfile.')
    }
    environment {
        ENV="${params.Env}"
        TARGET="pal"
        GOMODCACHE = "/var/cache/go/master/mod"
        GOCACHE = "/var/cache/go/master/go-build"
        GOPROXY = "https://goproxy.pointz.in"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }
        stage('Get Gamma Repo') {
            steps {
                script {
                    dir('gamma') {
                        deleteDir()
                        git credentialsId: 'github-app-epifi', poll: false, url: 'https://github.com/epiFi/gamma.git', branch: "master"
                    }
                }
            }
        }
        stage('Parse Build Args') {
            steps {
                script {
                    def buildArgs = [:]

                    // Loop through all environment variables
                    params.keySet().findAll { it.startsWith("BUILD_ARG_") }.each { key ->
                        // Remove the prefix and store the value with key in buildArgs map
                        buildArgs[key - "BUILD_ARG_"] = env[key]
                    }

                    // Construct the command with proper argument values
                    env.command = ""
                    buildArgs.each { k, v ->
                        // Enclose the value in quotes only if it contains spaces
                        def argValue = (v.contains(" ")) ? "\'${v}\'" : v
                        env.command += "-${k}=${argValue} "
                    }
                    echo env.command.trim()
                }
            }
        }
        stage('Execute') {
            steps {
                script {
                    dir('gamma') {
                        sh """
                        make build-script target='pal' &&
                        echo ./output/pal &&
                        cd ./output/pal &&
                        ENVIRONMENT=${ENV} ./pal_bin -JobName=${params.JobName} -Arguments=${params.Arguments}
                        """
                    }
                }
            }
        }
    }
}