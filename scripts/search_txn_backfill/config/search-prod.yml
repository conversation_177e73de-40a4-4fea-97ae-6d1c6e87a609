Application:
  Environment: "prod"

AWS:
  Region: "ap-south-1"

ESHost: "https://vpc-prod-search-access-bans26ocf6pstioabfospdyrxi.ap-south-1.es.amazonaws.com/"

EpifiDb:
  AppName: "search"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Tracing:
  Enable: true

OrderSearchPublisher:
  QueueName: "prod-order-update-indexing-consumer-queue"
