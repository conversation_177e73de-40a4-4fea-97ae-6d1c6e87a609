Application:
  Environment: "prod"

FitttDb:
  Name: "fittt"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

RMSDb:
  Name: "rms"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

AWS:
  Region: "ap-south-1"

FitttSecrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/fittt"
    RudderWriteKey: "prod/rudder/internal-writekey"

RMSSecrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/rms"
    RudderWriteKey: "prod/rudder/internal-writekey"

FitttOrderUpdateQueue:
  QueueName: "prod-fittt-order-update-queue"

FitttEnrichedEventPublisher:
  QueueName: "prod-fittt-enriched-event-queue"

ExecutionUpdatePublisher:
  QueueName: "prod-fittt-execution-update-queue"

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false
