Application:
  Environment: "development"

AWS:
  Region: "ap-south-1"

ESHost:
  "http://localhost:9200"

FAQSS3Path:
  S3Bucket: "epifi-dev-dna"
  FilePath: "s3://epifi-dev-dna/faqs/faqs.csv"

IndexName:
  "faqs"

InAppHelpDB:
  Name: "inapphelp"
  StatementTimeout: 5s
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Tracing:
  Enable: true

Secrets:
  Ids:
    PgdbCredentials: "prod/rds/epifimetis/inapphelp"
