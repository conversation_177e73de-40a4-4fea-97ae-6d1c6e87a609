Application:
  Environment: "qa"
  Name: "trigger_fit_executions_non_prod"

FitttDb:
  AppName: "fittt"
  StatementTimeout: 30s
  Name: "fittt"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 1
  MaxIdleConn: 1
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "DEBUG"
    SlowQueryLogThreshold: 1ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbCredentials: "qa/rds/postgres/fittt"
