package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestial2 "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	depositNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/deposit"
	"github.com/epifi/be-common/pkg/logger"
	accountPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	workflowPb "github.com/epifi/gamma/api/deposit/workflow"
)

// the script currently just prints the interest rate response from vendorgateway service
// TODO(mounish): extend this script to update the interest rate in the database
func main() {
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()

	// Set up a connection to the vendor gateway gRPC server.
	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	defer epifigrpc.CloseConn(celestialConn)
	celestialClient := celestialPb.NewCelestialClient(celestialConn)
	ctx := context.Background()
	cnt := 0
	for _, category := range []depositPb.InterestRateCategory{depositPb.InterestRateCategory_INTEREST_RATE_CATEGORY_GENERAL_PUBLIC, depositPb.InterestRateCategory_INTEREST_RATE_CATEGORY_SENIOR_CITIZEN} {
		for _, accountType := range []accountPb.Type{accountPb.Type_SMART_DEPOSIT, accountPb.Type_FIXED_DEPOSIT} {
			payload := &workflowPb.MonitorDepositInterestRateRequest{
				InterestRateCategory: category,
				Vendor:               commonvgpb.Vendor_FEDERAL_BANK,
				InterestEnquiryAt:    timestampPb.Now(),
				AccountType:          accountType,
			}
			bytePayload, err1 := protojson.Marshal(payload)
			if err1 != nil {
				logger.ErrorNoCtx("error while marshalling payload", zap.Error(err1))
				return
			}
			clientReqId := uuid.NewString()
			logger.InfoNoCtx("client request id of workflow", zap.Any(logger.CLIENT_REQUEST_ID, clientReqId), zap.Any(logger.ACCOUNT_TYPE, accountType), zap.Any("category", category))
			resp, err1 := celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
				Params: &celestialPb.WorkflowCreationRequestParams{
					Type:    celestial2.GetTypeEnumFromWorkflowType(depositNs.MonitorDepositInterestRateWorkflow),
					Payload: bytePayload,
					ClientReqId: &workflow.ClientReqId{
						Id:     clientReqId,
						Client: workflow.Client_DEPOSIT,
					},
					Ownership:        commontypes.Ownership_EPIFI_TECH,
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
			})
			if err2 := epifigrpc.RPCError(resp, err1); err2 != nil {
				logger.ErrorNoCtx("error while InitiateWorkflow", zap.Error(err2))
				return
			}
			cnt++
			if cnt <= 3 {
				time.Sleep(1 * time.Minute)
			}
		}
	}
}
