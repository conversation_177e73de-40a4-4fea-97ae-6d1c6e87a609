Application:
  Environment: "qa"
  Name: "fetch_tracking_details"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/demo/"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Aws:
  Region: "ap-south-1"
