package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	loyltyPb "github.com/epifi/gamma/api/vendorgateway/offers/loylty"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func main() {
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}
	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()

	// connect to vendorgateway service
	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	loyltyVgClient := loyltyPb.NewLoyltyClient(vgConn)

	// Calling loylty auth and catalog api periodically so that auth token and catalog gets cached
	// to prevent long response time in sync call.
	ctx, cancelFunc := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancelFunc()

	vgHeader := &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LOYLTY_REWARDZ}

	// create auth token
	createAuthTokenRes, err := loyltyVgClient.CreateAuthToken(ctx, &loyltyPb.CreateAuthTokenRequest{Header: vgHeader})
	if err != nil || !createAuthTokenRes.GetStatus().IsSuccess() {
		logger.Panic("error creating loylty auth token", zap.Any(logger.RPC_STATUS, createAuthTokenRes.GetStatus()), zap.Error(err))
	}

	// fetch loylty egv catalog
	allEgvProductsRes, err := loyltyVgClient.GetProductList(ctx, &loyltyPb.GetProductListRequest{
		Header: vgHeader,
		Auth:   &loyltyPb.Auth{AccessToken: createAuthTokenRes.GetAuthToken()},
	})
	if err != nil || !allEgvProductsRes.GetStatus().IsSuccess() {
		logger.Panic("loylty egv product list api failed", zap.Any(logger.RPC_STATUS, allEgvProductsRes.GetStatus()), zap.Error(err))
	}
	logger.Info(ctx, "auth token and egv catalog api call successful")
}
