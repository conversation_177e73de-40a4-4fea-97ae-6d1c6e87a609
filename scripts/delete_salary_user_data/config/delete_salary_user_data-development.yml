Application:
  Environment: "development"
  Name: "delete_salary_user_data"

SalaryProgramDb:
  AppName: "salaryprogram"
  Username: "root"
  Password: ""
  Port: 5432
  StatementTimeout: 5s
  Name: "salaryprogram"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    DbCredentials: "{\"username\": \"root\", \"password\": \"\"}"

Aws:
  Region: "ap-south-1"
