package helper

import (
	"math/rand"
	"testing"
	"time"
)

func Test_splitTicketsIntoBatchesForBulkUpdate(t *testing.T) {
	type args struct {
		ticketIds []int32
	}
	tests := []struct {
		name           string
		args           args
		wantBatchSizes []int
	}{
		{
			name: "less than limit",
			args: args{
				ticketIds: generateRandInt32Slice(12),
			},
			wantBatchSizes: []int{12},
		},
		{
			name: "equal to limit",
			args: args{
				ticketIds: generateRandInt32Slice(maxTicketListSizeForBulkUpdateAPI),
			},
			wantBatchSizes: []int{maxTicketListSizeForBulkUpdateAPI},
		},
		{
			name: "2 batches",
			args: args{
				ticketIds: generateRandInt32Slice(121),
			},
			wantBatchSizes: []int{100, 21},
		},
		{
			name: "filter tickets limit",
			args: args{
				ticketIds: generateRandInt32Slice(maxPageNumForFilterTicketsAPI * pageSizeForFilterTicketsAPI),
			},
			wantBatchSizes: []int{100, 100, 100},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := splitTicketsIntoBatchesForBulkUpdate(tt.args.ticketIds)
			if !isBatchSizesEqual(got, tt.wantBatchSizes) {
				t.Errorf("splitTicketsIntoBatchesForBulkUpdate() got = %v, wantBatchSizes %v", got, tt.wantBatchSizes)
			}
		})
	}
}

func isBatchSizesEqual(gotBatches [][]int32, wantSizes []int) bool {
	if len(gotBatches) != len(wantSizes) {
		return false
	}
	for i, batch := range gotBatches {
		if len(batch) != wantSizes[i] {
			return false
		}
	}
	return true
}

func generateRandInt32Slice(size int) []int32 {
	r := rand.New(rand.NewSource(time.Now().Unix()))
	randIntSlice := r.Perm(size)
	var randInt32Slice []int32
	for _, randVal := range randIntSlice {
		randInt32Slice = append(randInt32Slice, int32(randVal))
	}
	return randInt32Slice
}
