package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, "getincorrectkycimageusers")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToSecret, err := cfg.LoadAllSecrets(conf.EpifiDb, conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	updateDefaultConfig(conf, keyToSecret)
	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)
	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

type Config struct {
	Application *application
	AWS         *cfg.AWS
	Secrets     *cfg.Secrets
	EpifiDb     *cfg.DB
	Aws         *aws
}

type application struct {
	Environment string
	Name        string
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
}

type aws struct {
	Endpoint string
	Region   string
}
