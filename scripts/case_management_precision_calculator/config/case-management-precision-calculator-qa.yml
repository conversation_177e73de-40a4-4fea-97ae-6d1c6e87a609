Application:
  Environment: "qa"
  Name: "case-management-precision-calculator"

FRMDb:
  AppName: "case-management-precision-calculator"
  StatementTimeout: 1s
  DbType: "CRDB"
  Username: "frm_dev_user"
  Password: ""
  Name: "frm"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.frm_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.frm_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FRMPgdb:
  AppName: "case-management-precision-calculator"
  DbType: "PGDB"
  StatementTimeout: 5s
  Name: "frm_pgdb"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    FRMPgdbUsernamePassword: "qa/rds/postgres/frm_pgdb_dev_user"
