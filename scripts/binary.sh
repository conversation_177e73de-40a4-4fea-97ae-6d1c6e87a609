#!/bin/bash
set -e 
set -o pipefail
echo "Required input parameters are: target"
echo "target=$TARGET"
env
target=$TARGET
make docker-build-proxy target="$target" proxy=https://proxy.golang.org,direct || { echo 'docker-build step failed' ; exit 1; }
docker create -it --name copy_artifact_"$target_$ENV_$BUILD_NUMBER" "$target":$ENV-$BUILD_NUMBER || { echo 'docker-create step failed' ; exit 1; }
mkdir -p "$PWD"/output/"$target"
docker cp copy_artifact_"$target_$ENV_$BUILD_NUMBER":/my_bin "$PWD"/output/"$target"/"$target"_bin
docker cp copy_artifact_"$target_$ENV_$BUILD_NUMBER":/config "$PWD"/output/"$target"/
docker rm -f copy_artifact_"$target_$ENV_$BUILD_NUMBER"
ls -l "$PWD"/output/"$target"
# cleanup dangling images, stopped containers created before last hour
docker system prune -f --filter "until=1h"
