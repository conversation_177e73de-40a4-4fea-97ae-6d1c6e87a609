package main

import (
	"flag"
	"strings"

	"go.uber.org/zap"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/scripts/delete_mf_orders_of_actor/config"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

var (
	inputActors = flag.String("ActorIds", "", "Actor ID's for which we are deleting all MF orders, this is comma separated list")
)

func main() {
	flag.Parse()

	ActorIds := make([]string, 0)
	if inputActors != nil && *inputActors != "" {
		ActorIds = strings.Split(*inputActors, ",")
	}

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}
	epifiWealthDb, err := storagev2.NewCRDBWithConfig(conf.EpifiDb, false)
	if err != nil {
		logger.Panic("Failed to load DB", zap.Error(err))
	}

	epifisqlDb, err := epifiWealthDb.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = epifisqlDb.Close() }()

	var result []string
	if len(ActorIds) != 0 {
		for _, i := range ActorIds {

			if err = epifiWealthDb.Raw("delete from mf_payments where order_id IN (select id from mf_orders where actor_id = ?)", i).Scan(result).Error; err != nil {
				logger.Panic("failed to delete orders  %w", zap.Error(err))

			}

			if err = epifiWealthDb.Raw("delete from mf_order_status_updates where order_id IN (select id from mf_orders where actor_id = ?)", i).Scan(result).Error; err != nil {
				logger.Panic("failed to delete orders  %w", zap.Error(err))

			}
			if err = epifiWealthDb.Raw("delete from mf_order_confirmation_infos where order_id IN (select id from mf_orders where actor_id = ?)", i).Scan(result).Error; err != nil {
				logger.Panic("failed to delete orders  %w", zap.Error(err))

			}
			if err = epifiWealthDb.Raw("delete from mf_orders where actor_id= ?", i).Scan(result).Error; err != nil {
				logger.Panic("failed to delete orders  %w", zap.Error(err))

			}
		}
	}

}
