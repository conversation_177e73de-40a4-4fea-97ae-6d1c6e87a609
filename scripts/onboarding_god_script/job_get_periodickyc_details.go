// nolint:
package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"go.uber.org/zap"

	bcpb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type GetPeriodicKYCDetails struct {
	compClient compliancePb.ComplianceClient
	bcClient   bcpb.BankCustomerServiceClient
}

func (g *GetPeriodicKYCDetails) DoJob(ctx context.Context, req *JobRequest) error {
	var actorIds []string
	actorIds = splitCSV(req.Args1)
	var csvData [][]string
	csvData = append(csvData, []string{"ActorID", "CustomerId", "KycDueAt"})

	for _, actorId := range actorIds {
		ctx = epificontext.CtxWithActorId(ctx, actorId)
		logger.Info(ctx, fmt.Sprintf("logging actor id %v", actorId))

		compResp, compErr := g.compClient.GetPeriodicKYCDetail(ctx, &compliancePb.GetPeriodicKYCDetailRequest{
			ActorId:     actorId,
			FetchLatest: false,
		})
		if err := epifigrpc.RPCError(compResp, compErr); err != nil && !compResp.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "failed to get kyc compliance details", zap.Error(compErr))
			return err
		}
		kycDueDate := datetime.TimestampToString(compResp.GetPeriodicKYCDetail().GetKYCDueAt(), datetime.DATE_LAYOUT_YYYYMMDD, datetime.IST)
		bcResp, bcErr := g.bcClient.GetBankCustomer(ctx, &bcpb.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bcpb.GetBankCustomerRequest_ActorId{
				ActorId: actorId,
			},
		})
		if err := epifigrpc.RPCError(bcResp, bcErr); err != nil {
			logger.Error(ctx, "unable to get bank customer", zap.Error(bcErr))
			return err
		}
		customerId := bcResp.GetBankCustomer().GetVendorCustomerId()
		csvData = append(csvData, []string{actorId, customerId, kycDueDate})
	}
	print(csvData)
	errWriteCSV := writeCSVFile("./kycDueDate.csv", csvData)
	if errWriteCSV != nil {
		fmt.Println("Error in writing CSV", errWriteCSV)
	}
	return nil
}
