// nolint
package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	gormV2 "gorm.io/gorm"

	dao2 "github.com/epifi/gamma/vendormapping/dao"
	"github.com/epifi/gamma/vendormapping/dao/model"
)

type FirehoseFromActor struct {
	dbConn *gormV2.DB
}

func (j *FirehoseFromActor) DoJob(ctx context.Context, req *JobRequest) error {
	actIds := j.actors()

	fullRun := false
	if strings.EqualFold(req.Args1, "FULL_RUN") {
		fullRun = true
	}
	fmt.Printf("IS FULL RUN... %v\n", fullRun)

	if !fullRun {
		actIds = actIds[:5]
	}

	type result struct {
		actorId    string
		firehoseId string
		err        error
	}
	var results []*result
	for _, actId := range actIds {
		f, err := j.getFirehoseIdFromActorId(ctx, actId)
		results = append(results, &result{
			actorId:    actId,
			firehoseId: f,
			err:        err,
		})
		time.Sleep(1 * time.Millisecond)
	}

	fmt.Println("------------ all actor ids processed -----------")
	fmt.Println("ActorId,FirehoseId")
	for _, r := range results {
		fmt.Printf("%v,%v\n", r.actorId, r.firehoseId)
	}
	return nil
}

func (j *FirehoseFromActor) getFirehoseIdFromActorId(ctx context.Context, actorId string) (string, error) {
	vendorMappingDao := dao2.NewVendorMappingDao(j.dbConn, nil)
	vendorMappingIds, err := vendorMappingDao.GetDPMappingByVendorId(ctx, &model.DPVendorMapping{ActorId: actorId})
	if err != nil {
		fmt.Printf("failed to get firehose id for actor id: %v : %v\n", actorId, err)
		return "", err
	}
	return vendorMappingIds.FirehoseId, nil
}

// Hard-coding the list of actor ids instead of taking it as a dynamic input to track history & purpose.
// This data is of privacy concern with power to bridge two worlds of OLAP & OLTP DBs.
// Reason for firehose ids: https://monorail.pointz.in/p/fi-app/issues/detail?id=64258
// Reason for firehose ids: https://monorail.pointz.in/p/fi-app/issues/detail?id=67781
// Reason for firehose ids: https://monorail.pointz.in/p/fi-app/issues/detail?id=76739
func (j *FirehoseFromActor) actors() []string {
	return []string{
		"ACYOCTPjhYQn6J14lzaLAFOA240207==",
		"ACaqIwvI5EQvyPHvYmoKleng240203==",
		"AC0auSoKsvT2yRXpKXZAClhw230721==",
		"ACMiZYImDjTbiKLSt75Zlbtg240121==",
		"ACPRjkMf8yTY2eKNp3wtwqlw240201==",
		"ACMiZYImDjTbiKLSt75Zlbtg240121==",
		"AC006X59qoQgmXRx23mHLhpQ240201==",
		"ACJ84oc0RPTViZKAK3oZnZAg240206==",
		"ACPJguQiTpR7KDSRoJq3G/vQ230405==",
		"ACMiZYImDjTbiKLSt75Zlbtg240121==",
		"ACnCsb6bZ1TOyHT4/hQ2MA+g240203==",
		"ACPKTSpLgVQImRZb2yPJ4pTg240208==",
		"ACqh5rvbtYQ7iGZMowXHdInw240212==",
		"ACaB1+/Np7R3a9Xfd9SyXLDQ240120==",
		"ACYOCTPjhYQn6J14lzaLAFOA240207==",
		"ACEoQ9KbVFRayPEDTAESPoYQ240131==",
		"ACrk/AmLfKSQ2OyYLedlKRtA240210==",
		"ACsrNCMVF9QJ+vPUtJh/ZcLQ240216==",
		"ACd0gSiovqS8OtoChcTg0hCQ240208==",
		"ACkyNLO8U0RzusNKlhJaJ03Q240212==",
		"ACGZqgXxoQScKRgR9Z+alfxw240203==",
		"ACXzKUkopdQgazGrb9pXZepQ240106==",
		"ACFxbQWMYXQb+fR1KWZnCFVA240209==",
		"ACRnBkXfZ2TxuJpHwKM0DkeA240204==",
		"ACYulWaoHDSeSA6RlhSFGEPg240204==",
		"ACArcgAtvWRl69bpm8LKWFpA240215==",
		"ACaqIwvI5EQvyPHvYmoKleng240203==",
		"ACT6fijqOEQX+gjN/qWPaNuA240208==",
		"ACMsvzn915SXmfPooRQPmdpQ240213==",
		"AC9jF2ouwKTT+X2U68t8C1og230509==",
		"ACzjkcMgJpTOegMoNr236jyg240214==",
		"ACkaKx/FlfRbe9qH7tCTXhyQ240214==",
		"ACjk+ImMojTim9get9fyR6Sw240204==",
		"ACYwQHH4KwSKqDJ/bNywYMrQ240206==",
		"ACFxbQWMYXQb+fR1KWZnCFVA240209==",
		"ACiSlUV51DSqiLKUk/whZXKg240216==",
		"ACgK02qCInTLivfYKEv/R+SA240205==",
		"ACG98rYZDmQ/eiK6zonn77+g230620==",
		"ACNOUfK7efS8i6NuPawuN+SA240213==",
		"ACD5aBriG5QiidROp4TH4lxg240211==",
		"ACOYmI1U/OTYGLzBKSLYKD+A240206==",
		"ACGNtvVH1VSOGKc+cvhdpTBQ240205==",
		"ACPi/5qdf9Reyu3PyowFmBTw240131==",
		"ACtHChgqV4Szu5WE9A68tZHw230826==",
		"ACm3iHFtDbQwSBx/7s9KxlSg240206==",
		"AC0vkbpDamQbegciM4MZbUNQ240213==",
		"ACLNXjzwxjTIqlRe0lQP+5Sg230514==",
		"AC0oPK56DETq+WT2iXxjqxhw240207==",
		"ACMru0jCwySnyywbVECPS6Tg240213==",
		"AC32otj9cCQlKcrszs9FlnCQ240211==",
		"ACT6fijqOEQX+gjN/qWPaNuA240208==",
		"ACEwIWBD9yRA64FGUxoGJmiA240201==",
		"ACblLtZrA/QnOB8E8cwtkrRw230728==",
		"ACOa+8elG+S/mu8afRnzWgeA240210==",
		"ACOSx+Uz9oRPSxeuhTAinksQ240214==",
		"ACJzAGXK86RiamYUeI1IPzCQ240215==",
		"ACgyA5K/mvS/OMH9xMpd83xg240207==",
		"AC32otj9cCQlKcrszs9FlnCQ240211==",
		"AC32otj9cCQlKcrszs9FlnCQ240211==",
		"ACnz7tKhFWRoWaQbIQnRGczg240204==",
		"AC230224I1H9ckb5TyGVqj+NlgCURg==",
		"ACpMUGMz4pQ4mMWRyF7V/abg240219==",
		"AC31Cs5MGAS1KDVbO7PsUwow240208==",
		"ACUS8guXqITySnfkMMZWTsDA240214==",
		"ACcm2HU+aiRJuY85Xpvrgz5g240209==",
		"ACIBkDpkgYStuSTRxj6PxMXA240202==",
		"ACMIyLdgITRTC73665tOx/EA240204==",
		"AC6ywlfa66QjCmQbLUI+h4WA240211==",
		"ACNuYUtQFxT/K7Au8GJxZv5A240205==",
		"ACQ3Cs7NwJRNKOU8CGDn8VZQ230906==",
		"AC211120BJEvaCeYStmowEs7DpX7zg==",
		"ACjZ1Gl9XsQ1i4WEAYPa5sLQ231019==",
		"AC3iRx+IMLR+qruIDhztXFIw240208==",
		"ACCUB9iwsbQ++ORQNPDwWhUA240208==",
		"ACkaKx/FlfRbe9qH7tCTXhyQ240214==",
		"ACDX2zr9kwR3ew4S0UGxd5+Q240220==",
		"ACN8DpnqG7TmOzFqzMV+ykaQ231217==",
		"ACFvCjhHawTpexerBuPbSs1Q240213==",
		"ACExZ76jPBSY+HaNBvHfPFXw240208==",
		"ACU71GFrvySqybwpvAIbdzaA240217==",
		"ACcrKmlhcURa+Oszgj7DJTMQ230904==",
		"ACpSkoHDKWRiuNX3ufesS2aQ240211==",
		"ACNtO8ed78RGiEyVz3mcVUYg240118==",
		"AC220312dUcyIm1tSWmCK88ScJywcw==",
		"AC4Th2yOnqTkCOPsesVTakDA240213==",
		"ACh0aTufKaQ0mfa4my7Sp5Nw240221==",
		"ACMaiTFuIMTP6KPTamZChZKw240202==",
		"ACt3XTA6IbTlOWuoeRhF3JLQ240205==",
		"ACChiRsv7/SSSrpRB4W6wF4A240216==",
		"ACo+zHPjtIRNevrQCGFqa87Q240212==",
		"ACwJydN2vTQI6Iq7LhqnYngw240215==",
		"ACp7Xh/y1YR5qjZeiuU4IiHA240214==",
		"ACZeJCESH/TRucYCuHPvic4w240206==",
		"ACCNVvOGAdSK2axrAMy94CZw240210==",
		"ACJPLYJf+hRUmE97W+dG0jng240209==",
		"AC32otj9cCQlKcrszs9FlnCQ240211==",
		"AC82KEXPyASsWxR0G5cqFZdA240201==",
		"ACegF8di3TQpG9Gs+pprQFEQ240223==",
		"ACmL1gVlS9TcmgIJWsKre/XQ240224==",
		"ACUpI7xUQQTL+QGX1vNlB3Bw230904==",
		"ACqh5rvbtYQ7iGZMowXHdInw240212==",
		"ACwlfsBsACRuWiOTobron2wg240224==",
		"ACN9zMBK8bS9GnO2clpZeGxg240213==",
		"ACfR2gpijaQse5DgeuuS4bHA230718==",
		"ACJywIUp4XSaeK0xqTC34VLw240204==",
		"ACQ82xie8eQBK54PpEfir56w240216==",
		"ACCQxiDDkWRtyptdCaqhNLLQ240204==",
		"ACE8DWGTyuTpORJrmOrx6meg240224==",
		"AC4d8yxEWiSWOsptee9porhg240214==",
		"ACXH1VEg+rT2SxUd4n/rJy+Q230909==",
		"ACXH1VEg+rT2SxUd4n/rJy+Q230909==",
		"ACw3UUBbifTKy/X6oYa/bWEA240210==",
		"AC9y7WN3xrSFOVk8pJYVWVrQ240212==",
		"ACReRD34H2Twa7EA4+9z9v/A240121==",
		"AC75a75agJRSS3tx5Sc0qFVg240217==",
		"AC1mAsb5DITpGiQyMHFI9yzg240220==",
		"ACkvzlTUjWRY6oWaY4ZpZtZQ240224==",
		"ACXgDc8BkfSqqQRENOt5pIEw240125==",
		"AC1mAsb5DITpGiQyMHFI9yzg240220==",
		"ACUEDKlwoGRR+fNx3vJgfBHg240216==",
		"ACDrQsX2YVRwm5GexJGLnloA240224==",
		"ACkvzlTUjWRY6oWaY4ZpZtZQ240224==",
		"ACkvzlTUjWRY6oWaY4ZpZtZQ240224==",
		"ACGNtvVH1VSOGKc+cvhdpTBQ240205==",
		"ACI5X2COXNTiWZw33VMtohVw240201==",
		"ACMsvzn915SXmfPooRQPmdpQ240213==",
		"ACpZbkYtshQP2bHs3/x5jTIQ240219==",
		"ACZ59HF0ewSA2XeofJIYXDpA240218==",
		"AChWXYcHX4RceNQh0mmbGzmA240223==",
		"ACbb9DJCCyT16UOoYUaP0Flg240203==",
		"ACd8Z0sCYtRQGGEvNj9O28tg240205==",
		"ACVO7Dat9oTG+EuO9JXMgkQQ230905==",
		"ACF/bYkh2jT7aSYZQ5pKb6kg240220==",
		"ACKgMRZrxGSYi/w72x5NqaZg240214==",
		"ACCQxiDDkWRtyptdCaqhNLLQ240204==",
		"ACfrLp/68hS7W9lWPkCcSd0A230806==",
		"ACBJOegrx1T22rLqwters+Ow240221==",
		"ACF9LPvJ/BRKieWeFPqGzWvw240225==",
		"ACCJbZgw86T8KZlmioFjyKMw240209==",
		"ACC+xFwqN0SgSjm7d1CgUe7A240204==",
		"ACNuYUtQFxT/K7Au8GJxZv5A240205==",
		"AC3eYwxVD/SL+nIdnzahfCwQ240224==",
		"ACTXlBZJuzQOyYMhZbA/WB9Q230727==",
		"ACsssLpKNSQj+lqvh3AAG2fw240121==",
		"AC8AGsICvyQs6bzG4mb0dPIA240223==",
		"ACahuOhLaWTDW40TbXQ8RFEw240212==",
		"ACPsrsgXsHTlCoYFBpqX3apA240201==",
		"ACjVtM9BqSRpmIEEM6eVMm5g240130==",
		"AC6Q5rkHpdQYOFYP4geNB0tQ240213==",
		"ACW6+OJ9uFSvabpHki6Qaj3A240202==",
		"ACZ+CTxfX5RXy/KP+tTZZPng240212==",
		"ACZY+//11OSHCZpg0x+GIXXw240225==",
		"ACZY+//11OSHCZpg0x+GIXXw240225==",
		"ACpGkhB6fMTkGiVyfD+15TWA240213==",
		"ACdf+/KfZoSiqoWYW2aSIXuA240222==",
		"ACFOwAxLVHQ4aj+oDS0L8AWQ240202==",
		"ACVy7Xu+pgSPqwFvG0MqBaGg240218==",
		"ACWOqAp98uT5+kZxAAd7Ltag240207==",
		"ACj8NrAe4NSJ6Gir07OlRPoA240216==",
		"ACM7B9UKvlRtGZG79vyp9DhQ240216==",
		"ACkvzlTUjWRY6oWaY4ZpZtZQ240224==",
		"ACApLr0kHtQzmyWLnDGnrrkQ240222==",
		"AC+HM0x4uhQoCbRTw4T1Lcng240204==",
		"ACYF6akMNXQ7OO/PtLAcpBhQ231221==",
		"ACWOqAp98uT5+kZxAAd7Ltag240207==",
		"ACReRD34H2Twa7EA4+9z9v/A240121==",
		"ACNuYUtQFxT/K7Au8GJxZv5A240205==",
		"ACKBPoquAiQqOaUJck8zrqWw240201==",
		"ACGZqgXxoQScKRgR9Z+alfxw240203==",
		"ACF9LPvJ/BRKieWeFPqGzWvw240225==",
		"ACJeR6Og0aSZ+9d8JRNsrXSQ231224==",
		"ACegF8di3TQpG9Gs+pprQFEQ240223==",
		"ACPKTSpLgVQImRZb2yPJ4pTg240208==",
		"AC1mAsb5DITpGiQyMHFI9yzg240220==",
		"ACYF6akMNXQ7OO/PtLAcpBhQ231221==",
		"AC/AdEu+OrRgOkYdgC3EY7jg240131==",
		"ACMPVZfDe3QfWpU4DqIs5iUQ231119==",
		"AC7SpX7eqrTQChO1Af4JXyAg240117==",
		"AC9jF2ouwKTT+X2U68t8C1og230509==",
		"ACMsvzn915SXmfPooRQPmdpQ240213==",
		"ACMsvzn915SXmfPooRQPmdpQ240213==",
		"ACUS8guXqITySnfkMMZWTsDA240214==",
		"ACjacmuBeTSjmoVkhesoaVSA240213==",
		"ACzjkcMgJpTOegMoNr236jyg240214==",
		"AC0oPK56DETq+WT2iXxjqxhw240207==",
		"ACUpI7xUQQTL+QGX1vNlB3Bw230904==",
		"ACU71GFrvySqybwpvAIbdzaA240217==",
		"ACXzKUkopdQgazGrb9pXZepQ240106==",
		"ACdvMTZRx8R8q/bPj81aa+WA240224==",
		"ACGNtvVH1VSOGKc+cvhdpTBQ240205==",
		"ACZJ5cBc1GQ3OyPxqM4uaXvg240204==",
		"ACZJ5cBc1GQ3OyPxqM4uaXvg240204==",
		"ACZJ5cBc1GQ3OyPxqM4uaXvg240204==",
		"ACZJ5cBc1GQ3OyPxqM4uaXvg240204==",
		"ACrk/AmLfKSQ2OyYLedlKRtA240210==",
		"ACReRD34H2Twa7EA4+9z9v/A240121==",
		"ACW98CES4jS/u+qLHTbv3qvQ230327==",
		"ACuspYXhYLRcyDTb0nwxQGNg240203==",
		"ACwlfsBsACRuWiOTobron2wg240224==",
		"ACwJydN2vTQI6Iq7LhqnYngw240215==",
		"AChWXYcHX4RceNQh0mmbGzmA240223==",
		"ACpGkhB6fMTkGiVyfD+15TWA240213==",
		"ACcsc7PfcUTW22r9fuN7Q0+A240205==",
		"AC4FA6J3IjRJyjOTQa96Kl9g240215==",
		"ACxL3lRn0AQrSMOnPGfiSYEA240123==",
		"AC230320fjlyvnWbT0yT98E0QrMoYQ==",
		"ACFWTsSujKQ6qwcHkeW9HuXg240211==",
		"ACdAH0LG9ATVOtsyoQ0NevQQ240221==",
		"AC4Qm8EI7TRYqTLX4Rc6OYOA240208==",
		"AC6EmHdnkNQKOCl46abBSOsw240210==",
		"ACRN3qC3ekQOClPvH85qitSw240213==",
		"ACTxa5Y3Q0RSiu9xsqFRcRBg240224==",
		"ACSFmuTE7vSDWmipFqiXqc7g230725==",
		"ACfsQvPaSMQkuIhoao9h72Xg240225==",
		"ACOJJVLUtvR22ui9ZrAMCCGQ240125==",
		"ACOLn4bLvvRuKWv3vZLgl3hg240229==",
		"ACU9abjXFhTwqzM+l6/ZMlRA240229==",
		"AClYdX2kIsQIGJE2PqloPwlA240128==",
		"ACCu60/tiLTS+cp8KHTdR6GA230724==",
		"AC0goQ0uGuSZeKeWxc3eoNhQ240228==",
		"ACLiTkw99bRZ2V5JFqwOPzyA240227==",
		"ACcKQyWlCSR9eIUfmSKsRsAQ230814==",
		"ACw6nf4qrXTqyNAqUY2aWFLg240305==",
		"AChxOQV8KoTSWhsCMBy6ausw240229==",
		"AC7IjuV9JFT+O4LFH73NzFYw240208==",
		"ACrwelsIohTI2hnKK0EW1Idw240227==",
		"ACdxi6C673TUm8o+4m+Tb4Jw240229==",
		"AC5Kw7p+8DQ4ear6lO8jPUcw240226==",
		"ACgLya1zqRTOKnf6JqohEbwQ240229==",
		"AC7MoBJjIgTlaUP3NIEyIKnQ240227==",
		"ACVNTde2gOREGyvq6tPxgRRg240223==",
		"AC/JR0f+vCSbKuEBiZqh7a5g240306==",
		"ACXnVfwoxVQOWBL/WhEx94eQ240304==",
		"ACeKQhOcrgRA+mt/5mvpw5PA240227==",
		"ACFiQ+Dm2ISJqatt02RZom0A240306==",
		"ACr7vxPPx6TWigmFVR/CIMEg240209==",
		"ACDkRxfxhLT2i7woll+J6+vA240228==",
		"ACNHrKf1FrTu+Yp3dLx7QPfA240305==",
		"AC221216Gp7sCrOnRI6zBM9ATccDew==",
		"ACxL3lRn0AQrSMOnPGfiSYEA240123==",
		"ACBUp597uAQwmWO/YWNDlf1Q240215==",
		"ACpMUGMz4pQ4mMWRyF7V/abg240219==",
		"ACW98CES4jS/u+qLHTbv3qvQ230327==",
		"AC3GMVvRm1QxKsoF74MPdArA240219==",
		"AC/z0C1y8ARrKZUQM9NWlk4g240304==",
		"AC3lLf+RtcTcWq4qmCFd+HWQ240302==",
		"ACVNTde2gOREGyvq6tPxgRRg240223==",
		"AC3F5g8FRzRzCI0+MUvm1d4A240124==",
		"ACIvyirmfbR12ccYocVX36VQ240226==",
		"AC3lLf+RtcTcWq4qmCFd+HWQ240302==",
		"ACOPcA0U3bSqesRZHWv4tpJw240225==",
		"AC1qEBR+cQQ5iUBfug9AVfnw230503==",
		"ACYPcV4Y1QSRKPmXvcoUfUuA240221==",
		"ACdbb1tpJiRL+DoPqxtwQYtw240229==",
		"ACzGkvnA2+Ty+UaUqJHfBwqg240301==",
		"ACHPFyRNKbSp+bEkwt8f3u6w240228==",
		"AC3GMVvRm1QxKsoF74MPdArA240219==",
		"ACzxtrxoxcQwqOm3ustJPFmw240209==",
		"AC+LL40D71Q46UNf596XRSBA240219==",
		"ACLiTkw99bRZ2V5JFqwOPzyA240227==",
		"ACNHrKf1FrTu+Yp3dLx7QPfA240305==",
		"AC/JR0f+vCSbKuEBiZqh7a5g240306==",
	}
}
