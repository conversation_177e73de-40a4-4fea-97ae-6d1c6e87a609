package main

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
)

type jobSample struct{}

func (s *jobSample) DoJob(ctx context.Context, req *JobRequest) error {
	switch req.Args1 {
	case "TEST_ERROR":
		return fmt.Errorf("job sample failed")

	case "TEST_PANIC":
		panic("panic in job sample")

	default:
		break
	}
	args2List := splitCSV(req.Args2)

	logger.Info(ctx, "sample job run", zap.String("args1", req.Args1), zap.Strings("args2", args2List))
	return nil
}
