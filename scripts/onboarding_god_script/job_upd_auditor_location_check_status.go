package main

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/vkyccall/config/genconf"
	"github.com/epifi/gamma/vkyccall/dao"
)

type UpdateAuditorLocationCheckStatus struct {
	vkycCallDao dao.VkycCallDao
	conf        *genconf.Config
}

func (s *UpdateAuditorLocationCheckStatus) DoJob(ctx context.Context, req *JobRequest) error {
	actorId := req.Args1

	logger.Info(ctx, "Loaded config values",
		zap.String("DefaultDataTTL", s.conf.DefaultDataTTL().String()),
		zap.String("UserToCallDetailsKeyPrefix", s.conf.CustomerQueue().UserToCallDetailsKeyPrefix),
		zap.String("CallIdToActorIdKeyPrefix", s.conf.CustomerQueue().CallIdToActorIdKeyPrefix),
	)

	if s.conf.DefaultDataTTL() != time.Hour*168 {
		logger.Error(ctx, "validation failed: DefaultDataTTL is not 168 hours")
		return epifierrors.ErrInvalidArgument
	}

	if s.conf.CustomerQueue().UserToCallDetailsKeyPrefix != "VCIP:usertocalldetails" {
		logger.Error(ctx, "validation failed: UserToCallDetailsKeyPrefix is not VCIP:usertocalldetails")
		return epifierrors.ErrInvalidArgument
	}

	if s.conf.CustomerQueue().CallIdToActorIdKeyPrefix != "VCIP:callidtoactorid" {
		logger.Error(ctx, "validation failed: CallIdToActorIdKeyPrefix is not VCIP:callidtoactorid")
		return epifierrors.ErrInvalidArgument
	}

	callDetails, err := s.vkycCallDao.GetCallDetailsForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while fetching call details", zap.Error(err))
		return err
	}
	locationDetails := callDetails.Report.GetLocationCheckReport().GetLocationDetails()
	for _, locDetails := range locationDetails {
		locDetails.IsValid = true
	}
	err = s.vkycCallDao.SetUserCallDetails(ctx, actorId, callDetails)
	if err != nil {
		logger.Error(ctx, "error while updating call details ", zap.Error(err))
		return err
	}
	return nil
}
