package job_processor

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/gocarina/gocsv"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/scripts/Pay/pay-god-script/config"
)

const (
	defaultBatchSize = 100
)

// VpaEntry represents a row in the CSV file
type VpaEntry struct {
	Vpa string `csv:"vpa"`
}

type UpdateVpaMerchantInfoJob struct {
	filePath  string
	batchSize int
	db        *gorm.DB
}

func NewUpdateVpaMerchantInfoJob(conf *config.Config) (*UpdateVpaMerchantInfoJob, error, func()) {
	// Get database connection using storagev2
	dbConn, err := storagev2.NewGormDB(conf.EpifiDb)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %v", err), nil
	}
	job := &UpdateVpaMerchantInfoJob{
		db: dbConn,
	}

	return job, nil, func() {
		sqlDB, err := dbConn.DB()
		if err == nil && sqlDB != nil {
			_ = sqlDB.Close()
		}
	}
}

func (j *UpdateVpaMerchantInfoJob) ParseAndStoreArgs(input *JobRequest) error {
	if input.Args3 == "" {
		return fmt.Errorf("args3 is required")
	}
	j.filePath = input.Args3
	// batch size
	if input.Args2 == "" {
		j.batchSize = defaultBatchSize
		println("batchSize is not given, using default batchSize: ", defaultBatchSize)
	} else {
		// Convert string to int
		batchSize, strConvErr := strconv.Atoi(input.Args2)
		if strConvErr != nil {
			return fmt.Errorf("error converting string to int: %v", strConvErr)
		}
		j.batchSize = batchSize
	}
	return nil
}

func (j *UpdateVpaMerchantInfoJob) DoJob(ctx context.Context) error {
	file, err := os.Open(j.filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer func(file *os.File) {
		err2 := file.Close()
		if err2 != nil {
			fmt.Println("error in closing file", err2)
		}
	}(file)

	var vpaEntries []*VpaEntry
	if err = gocsv.UnmarshalFile(file, &vpaEntries); err != nil {
		logger.Panic("error in Unmarshal the file", zap.Error(err))
	}

	logger.Info(ctx, "loaded VPAs from file", zap.Int("total_vpas", len(vpaEntries)))

	// Process VPAs in batches
	for i := 0; i < len(vpaEntries); i += j.batchSize {
		end := i + j.batchSize
		if end > len(vpaEntries) {
			end = len(vpaEntries)
		}

		// Prepare batch of vpa(s) to be updated
		batchVpas := make([]string, 0, end-i)
		for _, entry := range vpaEntries[i:end] {
			if entry.Vpa == "" {
				continue
			}
			batchVpas = append(batchVpas, entry.Vpa)
		}

		// Process this batch
		if err := j.processBatch(ctx, batchVpas); err != nil {
			logger.Error(ctx, "failed to process batch",
				zap.Int("batch_start", i),
				zap.Int("batch_end", end),
				zap.Error(err))
		}

		logger.Info(ctx, "processed batch",
			zap.Int("batch_start", i),
			zap.Int("batch_end", end))
		// Sleep for 100 ms before processing next batch
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

func (j *UpdateVpaMerchantInfoJob) processBatch(ctx context.Context, originalVpas []string) error {
	if len(originalVpas) == 0 {
		return nil
	}

	// Build query for bulk update
	query := "UPDATE vpa_merchant_infos SET vpa = lower(vpa), updated_at = NOW() WHERE vpa IN (?)"

	// Execute the bulk update
	result := j.db.Exec(query, originalVpas)
	if result.Error != nil {
		return fmt.Errorf("failed to execute bulk update: %v", result.Error)
	}

	logger.Info(ctx, "processed batch", zap.Int64("rows_affected", result.RowsAffected), zap.Int("batch_size", len(originalVpas)))

	return nil
}
