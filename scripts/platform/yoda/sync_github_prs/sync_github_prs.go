// nolint:funlen
package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsConf "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	ghinstall "github.com/bradleyfalzon/ghinstallation/v2"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/oauth2"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/gamma/scripts/platform/yoda/common"
	"github.com/epifi/gamma/scripts/platform/yoda/sync_github_prs/query"

	githubv4 "github.com/shurcooL/githubv4"

	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	yodapb "github.com/epifi/gamma/api/yoda"
	"github.com/epifi/gamma/platform/yoda/dao"
	"github.com/epifi/gamma/scripts/platform/yoda/sync_github_prs/config"
)

var (

	// match file with words "design", "SOP" "runbook" "guidelines" "manual"
	fileDownloadPath = ""
	workDir          = ""
	githubToken      = ""
	startDate        time.Time
	endDate          time.Time
	pageOffset       int
	collectionName   string
	// combine all blacklisted patterns into blacklistedPattern
	blacklistedPattern = regexp.MustCompile(`(api/.*)|(.*\.json)|(.*\.yml)|(.*\.yaml)|(.*\.md)|(.*\.txt)|(.*\.csv)|(.*\.sql)|(.*\_test.go)`)
)

const (
	org      = "epiFi"
	repoName = "gamma"
)

func main() {
	flag.StringVar(&fileDownloadPath, "download-path", "files/github_prs", "Path to download files relative to workdir")
	flag.StringVar(&workDir, "work-dir", ".", "Working directory to download files and store tokens")
	flag.StringVar(&githubToken, "github-token", "", "Github token to fetch PR details")
	flag.StringVar(&collectionName, "collection_name", "epifi_common", "Name of the collection to index these resources")
	flag.IntVar(&pageOffset, "page-offset", 0, "Page offset to fetch PR details")
	startDateStr := flag.String("start-date", "", "Start date to fetch PR details. It is assumed as (endTime - 1d) if empty")
	endDateStr := flag.String("end-date", "", "End date to fetch PR details. It is assumed as time.Now() if empty")

	log.SetFlags(log.LstdFlags | log.Lshortfile)
	flag.Parse()
	ctx := context.Background()
	conf, err := config.Load()
	if err != nil {
		log.Fatalf("Unable to load config: %v", err)
	}
	logger.Init(conf.Application.Environment)

	if *endDateStr == "" {
		endDate = time.Now()
	} else {
		endDate, err = time.Parse(time.RFC3339, *endDateStr)
		if err != nil {
			log.Fatalf("Unable to parse end date: %v", err)
		}
	}

	if *startDateStr == "" {
		startDate = endDate.AddDate(0, 0, -1)
	} else {
		startDate, err = time.Parse(time.RFC3339, *startDateStr)
		if err != nil {
			log.Fatalf("Unable to parse start date: %v", err)
		}
	}

	cfg, err := awsConf.LoadDefaultConfig(context.TODO(), awsConf.WithRegion("ap-south-1"))
	if err != nil {
		log.Fatalf("Failed to load AWS configuration: %v", err)
	}
	s3Client := s3.NewFromConfig(cfg)

	db, err := storagev2.NewGormDB(conf.YodaDb)
	if err != nil {
		logger.FatalWithCtx(ctx, "Unable to connect to database:", zap.Error(err))
	}

	qlCl, err := getGithubClient(conf)
	if err != nil {
		logger.FatalWithCtx(ctx, "Unable to create github client:", zap.Error(err))
	}

	// Cleanup the dateLog file in the end
	defer func() {
		deleteErr := os.Remove(getLogfilePath(repoName))
		if deleteErr != nil {
			logger.Error(ctx, "Unable to remove date log file", zap.Error(err))
		}
	}()
	collectionId, err := common.GetCollectionId(db, collectionName)
	if err != nil {
		logger.FatalWithCtx(ctx, "Unable to get collection id", zap.Error(err))
	}
	for i := 0; i < 30; i++ {
		err = fetchPRDetailsGraphQL(ctx, qlCl, conf, s3Client, repoName, db, collectionId)
		if err != nil {
			logger.Error(ctx, "Unable to fetch PR details", zap.Error(err))
			continue
		}
	}

	logger.Info(ctx, "Successfully fetched files")
}

func getGithubClient(conf *config.Config) (*githubv4.Client, error) {
	if githubToken == "" {
		appId, err := strconv.ParseInt(conf.AppId, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("error parsing app id: %w", err)
		}
		installationId, err := strconv.ParseInt(conf.InstallationId, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("error parsing installation id: %w", err)
		}

		t, err := ghinstall.New(http.DefaultTransport, appId, installationId, []byte(conf.PrivateKey))
		if err != nil {
			return nil, fmt.Errorf("error creating github client: %w", err)
		}

		return githubv4.NewClient(&http.Client{Transport: t}), nil
	}
	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: githubToken},
	)
	tc := oauth2.NewClient(context.Background(), ts)
	return githubv4.NewClient(tc), nil
}

var exportErr = errors.New("export api failed")

func getMeaningfulFiles(files []query.FileNode) []string {
	var meaningfulFiles []string
	for _, file := range files {
		if !(file.ChangeType == "MODIFIED" || file.ChangeType == "ADDED") {
			continue
		}
		if !blacklistedPattern.MatchString(string(file.Path)) {
			meaningfulFiles = append(meaningfulFiles, string(file.Path))
		}
	}
	return meaningfulFiles
}

func getMeaningfulDescription(description string) string {
	if len(description) < 300 {
		return description
	}

	if strings.Contains(description, "A clear and concise description of what the problem") {
		// PR just uses the default template and most probably the description is not filled
		return ""
	}

	res := strings.Split(description, "PR Checklist")
	if len(res) > 1 {
		return strings.TrimSpace(res[0])
	}

	res = strings.Split(description, "Required if PR is to release branches")
	if len(res) > 1 {
		return strings.TrimSpace(res[0])
	}

	res = strings.Split(description, "List of Servers affected")
	if len(res) > 1 {
		return strings.TrimSpace(res[0])
	}

	res = strings.Split(description, "What is the rollback strategy")
	if len(res) > 1 {
		return strings.TrimSpace(res[0])
	}
	res = strings.Split(description, "PR Guidelines")
	if len(res) > 1 {
		return strings.TrimSpace(res[0])
	}
	return description
}

// nolint: gocritic
func storePRQL(ctx context.Context, conf *config.Config, pr query.PullRequestsNode, repo, resourceId string, s3Client *s3.Client) error {
	_ = ctx
	files := getMeaningfulFiles(pr.Files.Nodes)
	if len(files) == 0 {
		logger.Info(ctx, "No meaningful files found", zap.Int("number", int(pr.Number)), zap.String("resource_id", resourceId))
		return nil
	}

	builder := &strings.Builder{}

	builder.WriteString(fmt.Sprintf("Title of the PullRequest: %q\n", pr.Title))
	builder.WriteString(fmt.Sprintf("Author of the PullRequest: %q\n", pr.Author.Login))
	builder.WriteString(fmt.Sprintf("PullRequest Created At: %s\n", pr.CreatedAt.Format(time.RFC822)))
	builder.WriteString(fmt.Sprintf("PullRequest Updated At: %s\n", pr.UpdatedAt.Format(time.RFC822)))
	// include files as comma separated list
	builder.WriteString(fmt.Sprintf("PullRequest added solution in the comma separated files %q\n", strings.Join(files, ", ")))
	desc := getMeaningfulDescription(string(pr.BodyText))
	if len(desc) >= 300 {
		builder.WriteString(fmt.Sprintf("Description of the PullRequest: %s\n", desc))
	}
	for _, comment := range pr.Comments.Nodes {
		if len(comment.BodyText) >= 200 && !strings.Contains(string(comment.BodyText), "Suggested change") {
			builder.WriteString(fmt.Sprintf("User %s commented on the PullRequest with the content %q\n", comment.Author.Login, comment.BodyText))
		}
	}
	for _, review := range pr.Reviews.Nodes {
		if len(review.BodyText) >= 200 {
			builder.WriteString(fmt.Sprintf("User %s reviewed the PullRequest with the content %q\n", review.Author.Login, review.BodyText))
		}
		for _, comment := range review.Comments.Nodes {
			if len(comment.BodyText) >= 200 && !strings.Contains(string(comment.BodyText), "Suggested change") {
				builder.WriteString(fmt.Sprintf("User %s commented on the review with the content %q\n", comment.Author.Login, comment.BodyText))
			}
		}
	}

	_, putErr := s3Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(conf.YodaBucketName),
		Key:         aws.String(filepath.Join("github_prs", "formatted", repo, resourceId+".txt")),
		Body:        strings.NewReader(builder.String()),
		ContentType: aws.String("text/plain"),
	})
	if putErr != nil {
		return fmt.Errorf("error in uploading file to s3: %v", putErr)
	}

	return nil
}

func getLogfilePath(repo string) string {
	return filepath.Join(workDir, fileDownloadPath, "raw", repo, "dateLog.txt")
}

// backupPRsPerDay will group PRs per day. It will write them as a json string with date as key except the last day. It finally returns the last day PRs
func backupPRsPerDay(repo string, prs []query.SearchNode, conf *config.Config, s3Client *s3.Client) ([]query.SearchNode, error) {
	if len(prs) == 0 {
		return nil, nil
	}
	// group PRs per day
	prsPerDay := make(map[string][]query.SearchNode)
	lastDay := prs[len(prs)-1].PullRequest.CreatedAt.Time.Format(time.DateOnly)
	for _, pr := range prs {
		createdDate := pr.PullRequest.CreatedAt.Time.Format(time.DateOnly)
		prsPerDay[createdDate] = append(prsPerDay[createdDate], pr)
	}
	logFilePath := getLogfilePath(repo)
	// nolint:gosec
	f, err := os.OpenFile(logFilePath, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0644)
	if err != nil {
		return nil, errors.Wrap(err, "failed to open file for appending date log")
	}
	defer func() {
		_ = f.Close()
	}()
	// write the PRs per day except the last day
	for date, prs := range prsPerDay {
		if date == lastDay {
			continue
		}
		// write the PRs to a file
		b, err := json.Marshal(prs)
		if err != nil {
			return nil, errors.Wrap(err, "failed to marshal PRs")
		}

		_, putErr := s3Client.PutObject(context.Background(), &s3.PutObjectInput{
			Bucket:      aws.String(conf.YodaBucketName),
			Key:         aws.String(filepath.Join("github_prs", "raw", repo, date+".json")),
			Body:        strings.NewReader(string(b)),
			ContentType: aws.String("text/plain"),
		})
		if putErr != nil {
			return nil, errors.Wrap(putErr, "failed to upload file to s3")
		}

		// append to the date to dateLog.txt file as a new line
		if _, err = f.WriteString(date + "\n"); err != nil {
			return nil, errors.Wrap(err, "failed to write date to file")
		}

	}
	// return the last day PRs
	return prsPerDay[lastDay], nil
}

func fetchPRDetailsGraphQL(ctx context.Context, ghCl *githubv4.Client, conf *config.Config, s3Client *s3.Client, repo string, db *gorm.DB, collectionId string) error {
	// nolint:gosec
	err := os.MkdirAll(filepath.Join(workDir, fileDownloadPath, "raw", repo), 0755)
	if err != nil {
		logger.Error(ctx, "Unable to create directory", zap.Error(err))
		return errors.Errorf("Unable to create directory: %v", err)
	}
	resourceDao := dao.NewResourceDAO(db)
	_ = resourceDao

	pendingEndDate, err := getPendingEndDate(repo)
	if err != nil {
		logger.Error(ctx, "Failed to get pending end date", zap.Error(err))
		return errors.Errorf("Failed to get pending end date: %v", err)
	}

	variables := map[string]interface{}{
		"commentsFirst":       githubv4.NewInt(100),
		"commentsAfter":       githubv4.NewString(""),
		"searchFirst":         githubv4.NewInt(100),
		"searchAfter":         githubv4.NewString(""),
		"searchQuery":         *githubv4.NewString(githubv4.String(fmt.Sprintf("sort:created-desc base:master repo:%s/%s is:pr is:merged created:%s..%s", org, repo, startDate.Format(time.DateOnly), pendingEndDate.Format(time.DateOnly)))),
		"reviewCommentsFirst": githubv4.NewInt(20),
		"reviewCommentsAfter": githubv4.NewString(""),
		"reviewsFirst":        githubv4.NewInt(10),
		"reviewsAfter":        githubv4.NewString(""),
		"filesFirst":          githubv4.NewInt(50),
		"filesAfter":          githubv4.NewString(""),
	}
	hasNext := true
	var lastDayPrs []query.SearchNode
	for hasNext {
		var q query.Query

		err := ghCl.Query(context.Background(), &q, variables)
		if err != nil {
			logger.Error(ctx, "Unable to fetch data:", zap.Error(err))
			time.Sleep(time.Second * 20)
			continue
		}

		nodes := q.Search.Nodes
		if len(nodes) == 0 {
			break
		}
		// backup PRs per day
		lastDayPrs, err = backupPRsPerDay(repo, append(lastDayPrs, nodes...), conf, s3Client)
		if err != nil {
			logger.Error(ctx, "Failed to backup PRs per day", zap.Error(err))
			return errors.Errorf("Failed to backup PRs per day: %v", err)
		}

		err2 := processNodes(ctx, nodes, conf, repo, resourceDao, s3Client, collectionId)
		if err2 != nil {
			return err2
		}
		variables["searchAfter"] = q.Search.PageInfo.EndCursor
		hasNext = bool(q.Search.PageInfo.HasNextPage)
		time.Sleep(10 * time.Second)
	}
	return nil

}

func processNodes(ctx context.Context, nodes []query.SearchNode, conf *config.Config, repo string, resourceDao *dao.ResourceDAO, s3Client *s3.Client, collectionId string) error {
	for _, n := range nodes {
		pr := n.PullRequest
		number := int(pr.Number)
		r, created, updated, err := CreateOrUpdateResourceQL(ctx, resourceDao, pr, collectionId)
		if err != nil {
			logger.Error(ctx, "Failed to create or update resource", zap.Int("pr", number), zap.Error(err))
			return errors.Errorf("Failed to create or update resource: %v", err)
		}
		logger.Info(ctx, "Processing file", zap.Int("pr", number), zap.String("title", string(pr.Title)), zap.String("resource_id", r.GetId()), zap.Bool("created", created), zap.Bool("updated", updated))
		if created || updated {
			err = storePRQL(ctx, conf, pr, repo, r.GetId(), s3Client)
			if err != nil {
				logger.Error(ctx, "Failed to process PR", zap.Int("pr", number), zap.Error(err))
				if !errors.Is(err, exportErr) {
					return errors.Errorf("Failed to process PR: %v", err)
				}
				// skip and continue to next file
				continue
			}
			logger.Info(ctx, "Processed PR", zap.Int("pr", number))
		}
	}
	return nil
}

func getPendingEndDate(repo string) (pendingEndDate time.Time, err error) {
	b, err := os.ReadFile(getLogfilePath(repo))
	if err != nil {
		if os.IsNotExist(err) {
			return endDate, nil
		} else {
			return time.Time{}, errors.Wrap(err, "failed to read date log file")
		}
	} else {
		lines := strings.Split(strings.Trim(string(b), "\n"), "\n")
		if len(lines) < 2 {
			return endDate, nil
		} else {
			lastDate := lines[len(lines)-1]
			pendingEndDate, err = time.Parse(time.DateOnly, lastDate)
			if err != nil {
				return time.Time{}, errors.Wrap(err, "failed to parse last date")
			}
			return pendingEndDate, nil
		}
	}

}

func CreateOrUpdateResourceQL(ctx context.Context, resourceDao dao.Resource, pr query.PullRequestsNode, collectionId string) (resource *yodapb.Resource, created bool, updated bool, err error) {
	createdTime := pr.CreatedAt.Time
	var lastUpdatedAtSrc *timestamppb.Timestamp
	if pr.UpdatedAt != nil {
		lastUpdatedAtSrc = timestamppb.New(pr.UpdatedAt.Time)
	} else {
		lastUpdatedAtSrc = timestamppb.New(pr.CreatedAt.Time)
	}
	prNum := strconv.Itoa(int(pr.Number))
	if err != nil {
		return nil, false, false, errors.Wrap(err, "failed to parse created time")
	}
	resource = &yodapb.Resource{
		ResourceType:     yodapb.ResourceType_GITHUB_PULL_REQUEST,
		OriginResourceId: prNum,
		LastUpdatedAtSrc: lastUpdatedAtSrc,
		CollectionId:     collectionId,
		Metadata: &yodapb.Metadata{
			Data: &yodapb.Metadata_GithubPr{
				GithubPr: &yodapb.GithubPullRequest{
					Number:    int64(pr.Number),
					Title:     string(pr.Title),
					Url:       pr.URL.String(),
					Author:    string(pr.Author.Login),
					CreatedAt: timestamppb.New(createdTime),
					UpdatedAt: lastUpdatedAtSrc,
				},
			},
		},
	}
	// Create or update the resource in the database
	result, err := resourceDao.Create(ctx, resource)
	if err != nil {
		if storagev2.IsViolatingUniqueConstraint(err, "resources_resource_type_resource_id_idx") {
			result, getErr := resourceDao.GetByResourceType(ctx, &fieldmaskpb.FieldMask{Paths: []string{"id", "last_updated_at_src"}}, yodapb.ResourceType_GITHUB_PULL_REQUEST, prNum)
			if getErr != nil || len(result) == 0 {
				return nil, false, false, errors.Errorf("failed to get resource. %v", getErr)
			}
			existingResource := result[0]
			resource.Id = existingResource.GetId()
			if existingResource.GetLastUpdatedAtSrc().AsTime().Equal(lastUpdatedAtSrc.AsTime()) {
				return resource, false, false, nil
			}
			_, updateErr := resourceDao.Update(ctx, &fieldmaskpb.FieldMask{Paths: []string{"metadata", "last_updated_at_src"}}, resource)
			if updateErr != nil {
				return nil, false, false, errors.Wrap(updateErr, "failed to update resource")
			}
			return resource, true, true, nil
		}
		return nil, false, false, errors.Wrap(err, "failed to create resource")
	}
	return result, true, false, nil
}
