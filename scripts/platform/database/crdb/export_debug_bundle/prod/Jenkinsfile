@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'jenkins-cloud-worker' }
    options {
        ansiColor('xterm')
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
        timestamps()
    }
    parameters {
        string(name: 'instanceID', defaultValue: '', description: 'Specify the EC2 instance ID')
        choice(name: 'debugType', choices: ['export_redacted_debug_zip', 'export_tsdump', 'export_debug_zip'])
        string(name: 'startTime', defaultValue: '', description: 'Specify the start time for tsdump debug zip')
        string(name: 'endTime', defaultValue: '', description: 'Specify the end time for tsdump debug zip')
    }
    environment {
        ENV = "prod"
    }
    stages {
        stage("Setup Certs") {
            steps {
                script {
                    sh """
                    aws ssm send-command --document-name "AWS-RunShellScript" --document-version "1" --instance-ids "${params.instanceID}" --parameters "{\"commands\":[\"#!/bin/bash\", \"sh ./crdb_export_debug_bundles.sh ${params.debugType} prod '${params.startTime}' '${params.endTime}'\"], \"workingDirectory\":[\"/opt\"],\"executionTimeout\":[\"3600\"]}" --timeout-seconds 900 --max-concurrency "50" --max-errors "10" --region ap-south-1
                    """
                }
            }
        }
    }
}
