@Library("epifi-jenkins-libraries") _
pipeline {
    agent { label 'optimized-worker' }
    tools{
        go 'Go stable'
    }
    options {
        ansiColor('xterm')
        throttleJobProperty(
            categories: [],
            limitOneJobWithMatchingParams: false,
            maxConcurrentPerNode: 0,
            maxConcurrentTotal: 10,
            paramsToUseForLimit: '',
            throttleEnabled: true,
            throttleOption: 'project',
        )
        buildDiscarder(logRotator(numToKeepStr: '20', artifactNumToKeepStr: '10'))
    }
    parameters {
        string(name: 'Target', description: 'Path of the script folder in <gamma_repo_root>/scripts/')
        string(name: 'Version', defaultValue: "stable", description: 'Binary Version to Execute')
        choice(name: 'Repo_Name', choices: ['gamma', 'gringott', 'be-common'], description: 'repo name containing script')
        string(name: 'Args', description: 'Argument string to pass to binary')
        choice(
            name: 'ENV', 
            choices: [
                'demo', 
                'staging', 
                'qa', 
                'uat',
                'deploy',
                'data-dev'
            ], 
            description: 'Environment where you want to execute binary'
        ) 
        string(name: 'CHANNEL_NAME', defaultValue: "", description: 'Ignore this variable in case dont want to receive the alerts in slack. Provide the Slack channel id')
        string(name: 'ONCALL_NAME', defaultValue: "", description: 'Ignore this variable in case dont want to receive the alerts in slack. Provide the oncall name with @')
        booleanParam(name: 'ALERT_ON_SUCCESS', defaultValue: false, description: 'Ignore the varaiable in case dont want to receive the success alerts in slack.')
        booleanParam(name: 'REFRESH', defaultValue: false, description: 'Devs, ignore this variable! This is for DevOps to refresh Jenkinsfile.')
    }
    environment {
        ENV = "${params.ENV}"
    }
    stages {
        stage('Refresh') {
            when {
                expression { params.REFRESH == true}
            }
            steps {
                script {    
                    echo "Jenkins file was loaded....... finish build now"
                    currentBuild.displayName = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.description = "#${BUILD_NUMBER}-REFRESH:${params.REFRESH}"
                    currentBuild.getRawBuild().getExecutor().interrupt(Result.SUCCESS)
                    sleep(5)
                }
            }
        }

        stage('Assume Role') {
            steps {
                script {
                    dir('certs') {
                       assumeRole()
                       }
                }
            }
        }
         stage('Fetch Binary') {
            steps {
                dir('app'){
                    script{
                    currentBuild.displayName = "#${BUILD_NUMBER}-${params.Target}-${ENV}"
                    currentBuild.description = "#${BUILD_NUMBER}-${params.Target}-${ENV}"
                    sh """
                    set +x
                        . ${WORKSPACE}/.env
                    set -x
                    chmod 777 ${env.WORKSPACE}/scripts/platform/gamma/helper.sh
                    set -x
                    if [ "${params.Repo_Name}" = "gamma" ]; then
                         ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-jenkins-job-binaries ${params.Target} ${params.Version}
                    else
                         ${env.WORKSPACE}/scripts/platform/gamma/helper.sh fetch_binary epifi-jenkins-job-binaries ${params.Repo_Name}/${params.Target} ${params.Version}
                    fi
                    set +x
                    """
                    }
                }
            }
         }
        stage("Execute Binary") {
            steps {
                dir('app') {
                    script {
                        sh """
                            set +x
                            . ${WORKSPACE}/.env
                            set -x
                            
                            cd ${params.Target}
                            chmod 700 ${params.Target}_bin
                            ENVIRONMENT=${ENV} DISABLE_REMOTE_CFG_SERVER_ENDPOINTS_LOOKUP=true ./${params.Target}_bin ${params.Args} 

                            set +x
                        """
                    }
                }
            }
        }
    }
    post  
    {
        success {
            script{
            if ("${CHANNEL_NAME}" != "" && params.ALERT_ON_SUCCESS == true)
            slackSend channel: "${CHANNEL_NAME}",
                    color: '#2EB887',
                    failOnError:false,
                    message:"Build succeed - ${env.JOB_NAME} ${env.BUILD_NUMBER}"
            }
        }
        failure {
            script{
            if ("${CHANNEL_NAME}" != "")
            slackSend channel: "${CHANNEL_NAME}",
                    color: 'danger',
                    failOnError:true,
                    message:"${ONCALL_NAME} Build failed  - ${env.JOB_NAME} ${env.BUILD_NUMBER} (<${env.BUILD_URL}|View Logs>)"
            }
       }
    }
}
