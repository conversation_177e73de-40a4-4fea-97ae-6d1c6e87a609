Application:
  Environment: "demo"
  Name: "VAE_data_filling"

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "/home/<USER>/epifi/gamma/config/crdb/demo/"
  SSLRootCert: "demo/cockroach/ca.crt"
  SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"
