package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/aml/dao"
	"github.com/epifi/gamma/aml/dao/impl"
	usstocksAccountPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/scripts/aml_alert_uploader/config"
)

type AmlAlertUploader struct {
	amlCaseDetailsDao            dao.AmlCaseDetailsDao
	usstocksAccountManagerClient usstocksAccountPb.AccountManagerClient
	conf                         *config.Config
	s3Client                     *s3.Client
}

func NewAmlAlertUploader(
	amlCaseDetailsDao dao.AmlCaseDetailsDao,
	usstocksAccountManagerClient usstocksAccountPb.AccountManagerClient,
	conf *config.Config,
	s3Client *s3.Client,
) *AmlAlertUploader {
	return &AmlAlertUploader{
		amlCaseDetailsDao:            amlCaseDetailsDao,
		usstocksAccountManagerClient: usstocksAccountManagerClient,
		conf:                         conf,
		s3Client:                     s3Client,
	}
}

func main() {
	flag.Parse()

	// ctx with trace id
	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))
	fmt.Println("ctx with trace id", ctx)

	env, err := cfg.GetEnvironment()
	if err != nil {
		os.Exit(2)
	}

	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	conf, err := config.Load()
	if err != nil {
		logger.Error(ctx, "error loading config", zap.Error(err))
		return
	}
	awsSession, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		fmt.Printf("error to initialize AWS session : %v\n", zap.Error(err))
		return
	}
	s3Client := s3.NewClient(awsSession, conf.S3Conf.Bucket)
	fmt.Println("s3 bucket name", conf.S3Conf.Bucket)

	usstocksServiceConn := epifigrpc.NewConnByService(cfg.US_STOCKS_SERVICE)
	defer epifigrpc.CloseConn(usstocksServiceConn)
	usstocksClient := usstocksAccountPb.NewAccountManagerClient(usstocksServiceConn)
	ownershipDBMap := map[common.Ownership]*cfg.DB{
		common.Ownership_EPIFI_TECH: conf.EpifiDb,
	}
	// nolint: dogsled
	epifiDBResourceProvider, _, _, _ := storagev2.NewDBResourceProviderV2(ownershipDBMap, false, nil)
	amlCaseDetailsDao := impl.NewCrdbAmlCaseDetailsDao(epifiDBResourceProvider)
	s := NewAmlAlertUploader(amlCaseDetailsDao, usstocksClient, conf, s3Client)

	// upload all the aml alert files to s3
	caseDetailsList, err := s.processAmlAlertZipFile(ctx)
	if err != nil {
		logger.Error(ctx, "error uploading archive", zap.Error(err))
		return
	}
	logger.Info(ctx, "successfully uploaded archive", zap.Any("caseDetailsList", caseDetailsList))

	// update case details for us stocks investors
	s.updateCaseDetailsForUsstocksInvestors(ctx, caseDetailsList)
	return
}
