Application:
  Environment: "prod"
  Name: "risk-bulk-close-stale-cases"

FRMDb:
  AppName: "risk"
  StatementTimeout: 5s
  Username: "frm_dev_user"
  Password: ""
  Name: "frm"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.frm_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.frm_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

StaleCaseThreshold: "168h"
NewestStaleCaseThreshold: "168h"
MaxStaleCases: 18000
RateLimit: 60
