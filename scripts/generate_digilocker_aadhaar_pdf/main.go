//nolint:funlen
package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/docs"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/scripts/generate_digilocker_aadhaar_pdf/config"
	"github.com/epifi/gamma/wealthonboarding/dao/impl"
	"github.com/epifi/gamma/wealthonboarding/kra_data"
)

var (
	panNumber    = flag.String("panNumber", "", "pan number of user")
	givenActorId = flag.String("actorId", "", "actorId of user")
	careOf       = flag.String("careOf", "", "get careOf field value from docket")
	landmark     = flag.String("landmark", "", "get landmark field value from docket")
)

type Service struct {
	actorClient actorPb.ActorClient
	userClient  user.UsersClient
	wonbClient  woPb.WealthOnboardingClient
	docsClient  docs.DocsClient
}

func NewService(ac actorPb.ActorClient, uc user.UsersClient, wc woPb.WealthOnboardingClient, dc docs.DocsClient) *Service {
	return &Service{
		actorClient: ac,
		userClient:  uc,
		wonbClient:  wc,
		docsClient:  dc,
	}
}

type DocHelperImpl struct {
	conf       *config.Config
	s3Client   s3.S3Client
	httpClient *http.Client
}

func NewDocProofHelperImpl(s3Client s3.S3Client, httpClient *http.Client, conf *config.Config) *DocHelperImpl {
	return &DocHelperImpl{s3Client: s3Client, httpClient: httpClient, conf: conf}
}

/*
	this job is for Recreate digilocker aadhaar pdf by use of digilocker aadhaar xml data for hold cases with address mismatch

for this job, we take careOf and landmark field value as an input because we were not storing these two fields in onboarding details.
so we will pass these two values manually by seeing the value of these two fields from the docket.
*/
func main() {
	flag.Parse()
	if (panNumber == nil || *panNumber == "") && (givenActorId == nil || *givenActorId == "") {
		logger.ErrorNoCtx("pan number and actorId both should not be empty")
		os.Exit(1)
	}

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)

	defer func() { _ = logger.Log.Sync() }()

	conf, err := config.Load()
	if err != nil {
		logger.ErrorNoCtx("failed to load config", zap.Error(err))
		return
	}

	awsSession, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		fmt.Printf("error to initialize AWS session : %v\n", zap.Error(err))
		return
	}
	s3Client := s3.NewClient(awsSession, conf.S3Conf.Bucket)

	// setup a connection to actor service
	actorServiceConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorServiceConn)
	actorClient := actorPb.NewActorClient(actorServiceConn)

	// setup a connection to connected account service
	caServiceConn := epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	defer epifigrpc.CloseConn(caServiceConn)
	wonbClient := woPb.NewWealthOnboardingClient(caServiceConn)

	// setup a connection to user service
	userServiceConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userServiceConn)
	usersClient := user.NewUsersClient(userServiceConn)

	// setup a connection to docs service
	docsConn := epifigrpc.NewConnByService(cfg.DOCS_SERVICE)
	defer epifigrpc.CloseConn(docsConn)
	docsClient := docs.NewDocsClient(docsConn)

	s := NewService(actorClient, usersClient, wonbClient, docsClient)
	ctx := context.Background()
	actorID := *givenActorId
	if actorID == "" {
		actorID, err = s.GetActorIdByPan(ctx, *panNumber)
		if err != nil {
			logger.ErrorNoCtx("Failed to GetActorIdByPan", zap.Error(err))
			return
		}
	}

	epifiWealthDb, err := storagev2.NewCRDBWithConfig(conf.EpifiWealthDb, false)
	if err != nil {
		logger.Panic("Failed to load DB", zap.Error(err))
	}

	sqlDb, err := epifiWealthDb.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDb.Close() }()

	onbDetailsDao := impl.NewCrdbOnboardingDetailsDao(epifiWealthDb)
	od, err := onbDetailsDao.GetByActorIdAndOnbType(context.Background(), actorID, woPb.OnboardingType_ONBOARDING_TYPE_WEALTH)
	if err != nil {
		logger.ErrorNoCtx("error in GetByActorIdAndOnbType", zap.Error(err))
		logger.Panic("error while updating step")
	}
	docHelperImpl := NewDocProofHelperImpl(s3Client, &http.Client{}, conf)
	aadhaarPdfUrl, pdfErr := s.generateAadhaarPdf(ctx, *panNumber, od, docHelperImpl, *careOf, *landmark)
	if pdfErr != nil {
		logger.ErrorNoCtx("failed to GenerateAadhaarPdf", zap.Error(pdfErr))
		logger.Panic("error while generate aadhaar pdf")
	}
	logger.Info(ctx, aadhaarPdfUrl)
}

func (c *Service) GetActorIdByPan(ctx context.Context, pan string) (string, error) {
	fmt.Println("pan number is- ", pan)
	resp, err := c.userClient.GetUsers(ctx, &user.GetUsersRequest{Identifier: []*user.GetUsersRequest_GetUsersIdentifier{
		{
			Identifier: &user.GetUsersRequest_GetUsersIdentifier_Pan{Pan: pan},
		},
	}})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while getting GetUserByPAN response", zap.Error(te))
		return "", te
	}
	actorResp, err := c.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		Type:     types.Actor_USER,
		EntityId: resp.GetUsers()[len(resp.GetUsers())-1].GetId()},
	)
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		logger.Error(ctx, "error while getting GetActorById response", zap.Error(te))
		return "", te
	}
	fmt.Println("actorId is- ", actorResp.GetActor().GetId())
	return actorResp.GetActor().GetId(), nil
}

func (c *Service) generateAadhaarPdf(ctx context.Context, panNum string, od *woPb.OnboardingDetails, docHelperImpl *DocHelperImpl, co string, lm string) (string, error) {
	aadhaarRes := od.GetMetadata().GetDigilockerData().GetDigilockerAadhaarData()
	if aadhaarRes == nil {
		return "", errors.New("aadhaar response is empty")
	}
	// image data is stored in s3Path format so first convert it into base64 format
	userImage, err := docHelperImpl.DownloadDoc(ctx, aadhaarRes.GetUserImageDocument())
	aadhaarDataPdf := &kra_data.AadhaarPdf{
		DocumentType:        "e-Aadhaar generated from DigiLocker verified Aadhaar XML",
		GenerationDate:      kra_data.GetDate(aadhaarRes.GetTs()),
		DownloadDate:        kra_data.GetDate(aadhaarRes.GetTs()),
		MaskedAadhaarNumber: aadhaarRes.GetMaskedAadhaarNumber(),
		Name:                names.ToString(aadhaarRes.GetName()),
		DateOfBirth:         kra_data.ConvertDob(aadhaarRes.GetDob()),
		Gender:              kra_data.ConvertGender(aadhaarRes.GetGender()),
		CareOf:              co,
		Landmark:            lm,
		Locality:            aadhaarRes.GetAddress().GetLocality(),
		City:                aadhaarRes.GetAddress().GetSublocality(),
		PinCode:             aadhaarRes.GetAddress().GetPostalCode(),
		State:               aadhaarRes.GetAddress().GetAdministrativeArea(),
		Photo:               userImage.GetPhoto()[0].GetImageDataBase64(),
		Address:             kra_data.GetAddress(aadhaarRes.GetAddress()),
	}
	data, err := json.Marshal(aadhaarDataPdf)
	if err != nil {
		return "", errors.Wrap(err, "error while converting to aadhaar in pdf format")
	}
	resp, err := c.docsClient.GeneratePdf(ctx, &docs.GeneratePdfRequest{
		PdfTemplate:         docs.PDFTemplate_AADHAAR_CARD_DIGILOCKER,
		Data:                data,
		FileName:            panNum + "_aadhaarCard.pdf",
		ExpiryTimeInSeconds: 86400, // expiry is 1 day
	})
	if respErr := epifigrpc.RPCError(resp, err); respErr != nil {
		return "", errors.Wrap(respErr, "error while calling docs service to convert to pdf")
	}
	return resp.GetFileUrl(), nil
}

func (d *DocHelperImpl) DownloadDoc(ctx context.Context, doc *types.DocumentProof) (*types.DocumentProof, error) {
	s3ImagePath := doc.GetS3Paths()
	if len(s3ImagePath) == 0 {
		return nil, errors.New("no s3 path present")
	}
	resDoc := &types.DocumentProof{
		ProofType: doc.GetProofType(),
		Id:        doc.GetId(),
		Expiry:    doc.GetExpiry(),
		Dob:       doc.GetDob(),
	}
	for _, s3Image := range s3ImagePath {
		imagePath := strings.Split(s3Image, "/")
		imageType := getImageType(imagePath[len(imagePath)-1])
		if imageType == commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED {
			return nil, errors.New(fmt.Sprintf("image type unspecified, path: %v", s3Image))
		}
		imgBytes, readErr := d.s3Client.Read(s3Image)
		if readErr != nil {
			return nil, errors.Wrap(readErr, fmt.Sprintf("error while reading image, path : %v", s3ImagePath))
		}
		image := &commontypes.Image{
			ImageType:       imageType,
			ImageDataBase64: string(imgBytes),
		}
		resDoc.Photo = append(resDoc.Photo, image)
	}
	return resDoc, nil
}

func getImageType(fileName string) commontypes.ImageType {
	ext := strings.Split(fileName, ".")[1]
	imType, ok := commontypes.ImageType_value[strings.ToUpper(ext)]
	if !ok {
		return commontypes.ImageType_IMAGE_TYPE_UNSPECIFIED
	}
	return commontypes.ImageType(imType)
}
