package job

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	ffPb "github.com/epifi/gamma/api/firefly"
	ccFireflyBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ccBillEnumsPb "github.com/epifi/gamma/api/firefly/billing/enums"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/firefly/dao"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type UpdateRewardInfoInBillJobArgs struct {
	CardRequestIds []string `json:"cardRequestIds"`
}

type UpdateRewardInfoInBillJob struct {
	cardRequestDao          dao.CardRequestDao
	fireflyClient           ffPb.FireflyClient
	billingClient           ccFireflyBillingPb.BillingClient
	rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient
}

func NewUpdateRewardInfoInBillJob(cardRequestDao dao.CardRequestDao, billingClient ccFireflyBillingPb.BillingClient,
	rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient, fireflyClient ffPb.FireflyClient) *UpdateRewardInfoInBillJob {
	return &UpdateRewardInfoInBillJob{
		cardRequestDao:          cardRequestDao,
		billingClient:           billingClient,
		rewardsProjectionClient: rewardsProjectionClient,
		fireflyClient:           fireflyClient,
	}
}

func (i *UpdateRewardInfoInBillJob) GetArgs() interface{} {
	return &UpdateRewardInfoInBillJobArgs{}
}

func (i *UpdateRewardInfoInBillJob) Run(ctx context.Context, argString string) error {
	var (
		rewardsInfo          *ccFireflyBillingPb.RewardsInfo
		card                 *ffPb.CreditCard
		getCreditCardBillRes *ccFireflyBillingPb.GetCreditCardBillResponse
		updateBillRes        *ccFireflyBillingPb.UpdateCreditCardBillResponse
	)
	arg := &UpdateRewardInfoInBillJobArgs{}
	if err := json.Unmarshal([]byte(argString), arg); err != nil {
		return errors.Wrap(err, "args unmarshal failed")
	}
	for _, crId := range arg.CardRequestIds {
		cardRequest, err := i.cardRequestDao.GetById(ctx, crId, nil)
		if err != nil {
			logger.Error(ctx, "error in fetching card request", zap.Error(err), zap.String(logger.CARD_REQUEST_ID, crId))
			continue
		}
		actorId := cardRequest.GetActorId()
		card, err = i.getCreditCard(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error in fetching credit card", zap.String(logger.ACTOR_ID_V2,
				actorId), zap.String(logger.CARD_REQUEST_ID, crId))
			continue
		}
		if card.GetBasicInfo().GetBillGenDate() == 4 {
			continue
		}
		getCreditCardBillRes, err = i.billingClient.GetCreditCardBill(ctx, &ccFireflyBillingPb.GetCreditCardBillRequest{
			GetBy: &ccFireflyBillingPb.GetCreditCardBillRequest_ActorId{
				ActorId: actorId,
			},
		})
		if te := epifigrpc.RPCError(getCreditCardBillRes, err); te != nil {
			logger.Error(ctx, "error in fetching bill", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId),
				zap.String(logger.CARD_REQUEST_ID, crId))
			continue
		}
		bill := getCreditCardBillRes.GetCreditCardBill()
		rewardsInfo, err = i.getRewardsInfo(ctx, actorId, getCreditCardBillRes.GetBillWindow())
		if err != nil {
			logger.Error(ctx, "error in getting rewards info", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId),
				zap.String(logger.CARD_REQUEST_ID, crId), zap.String(logger.BILL_ID, getCreditCardBillRes.GetCreditCardBill().GetId()))
			continue
		}
		bill.RewardsInfo = rewardsInfo
		updateBillRes, err = i.billingClient.UpdateCreditCardBill(ctx, &ccFireflyBillingPb.UpdateCreditCardBillRequest{
			FieldMasks:     []ccBillEnumsPb.CreditCardBillFieldMask{ccBillEnumsPb.CreditCardBillFieldMask_CREDIT_CARD_BILL_FIELD_MASK_REWARDS_INFO},
			CreditCardBill: bill,
		})
		if te := epifigrpc.RPCError(updateBillRes, err); te != nil {
			logger.Error(ctx, "error in updating bill", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId),
				zap.String(logger.BILL_ID, bill.GetId()), zap.String(logger.CARD_REQUEST_ID, crId))
			continue
		}
		logger.Info(ctx, "successfully updated reward info", zap.String(logger.CARD_REQUEST_ID, crId),
			zap.String(logger.ACTOR_ID_V2, actorId))
	}
	return nil
}

func (i *UpdateRewardInfoInBillJob) getCreditCard(ctx context.Context, actorId string) (*ffPb.CreditCard, error) {
	getCardRes, err := i.fireflyClient.GetCreditCard(ctx, &ffPb.GetCreditCardRequest{
		GetBy: &ffPb.GetCreditCardRequest_ActorId{ActorId: actorId},
	})
	if te := epifigrpc.RPCError(getCardRes, err); te != nil {
		return nil, te
	}
	return getCardRes.GetCreditCard(), nil
}

func (i *UpdateRewardInfoInBillJob) getRewardsInfo(ctx context.Context, actorId string, billingWindow *ccFireflyBillingPb.BillWindow) (*ccFireflyBillingPb.RewardsInfo, error) {
	unsecuredProgramBaseAggregates, err := i.rewardsProjectionClient.GetRewardsProjections(ctx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
		ActorId: actorId,
		Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
			OfferType: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
				rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_BASE_OFFER,
				rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER,
			},
			TimeWindow: &rewardsPb.TimeWindow{
				FromTime: billingWindow.GetFromTimestamp(),
				TillTime: billingWindow.GetToTimestamp(),
			},
		},
		FetchAggregates: true,
	})
	if err != nil {
		return nil, err
	}
	unsecuredProgramAcceleratedAggregates, err := i.rewardsProjectionClient.GetRewardsProjections(ctx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
		ActorId: actorId,
		Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
			OfferType: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
				rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER,
			},
			TimeWindow: &rewardsPb.TimeWindow{
				FromTime: billingWindow.GetFromTimestamp(),
				TillTime: billingWindow.GetToTimestamp(),
			},
		},
		FetchAggregates: true,
	})
	if err != nil {
		return nil, err
	}
	baseProjections, acceleratedProjections := 0, 0
	if len(unsecuredProgramAcceleratedAggregates.GetAggregateProjections().GetRewardUnitsDetails()) > 0 {
		acceleratedProjections = int(unsecuredProgramAcceleratedAggregates.GetAggregateProjections().GetRewardUnitsDetails()[0].GetProjectedRewardUnits())
	}
	if len(unsecuredProgramBaseAggregates.GetAggregateProjections().GetRewardUnitsDetails()) > 0 {
		baseProjections = int(unsecuredProgramBaseAggregates.GetAggregateProjections().GetRewardUnitsDetails()[0].GetProjectedRewardUnits())
	}
	totalRewardCoins := baseProjections + acceleratedProjections
	return &ccFireflyBillingPb.RewardsInfo{
		RewardCoinsEarned: int32(totalRewardCoins),
		ExtraRewardsConstructInfo: &ccFireflyBillingPb.ExtraRewardsConstructInfo{
			Projected_5XRewardsCoins: int32(acceleratedProjections),
			Total_1XRewardsCoins:     int32(baseProjections),
			TotalRewardCoins:         int32(totalRewardCoins),
		},
	}, nil
}
