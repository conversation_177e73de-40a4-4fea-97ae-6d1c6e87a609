package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once          sync.Once
	config        *Config
	err           error
	dbCredentials = "DbUsernamePassword"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "bulk-import")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToIdMap := cfg.AddPgdbSslCertSecretIds(conf.VendorMappingDb, conf.Secrets.Ids)
	keyToSecret, err := cfg.LoadSecrets(keyToIdMap, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}

	err = updateDefaultConfig(conf.VendorMappingDb, keyToSecret, conf)
	if err != nil {
		return nil, err
	}

	readAndSetEnv(conf)
	return conf, nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		if c.VendorMappingDb != nil {
			c.VendorMappingDb.Host = val
		}
	}
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *cfg.DB, keyToSecret map[string]string, fullConfig *Config) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)

	cfg.UpdateDbEndpointInConfig(c, dbServerEndpoint)

	cfg.UpdatePGDBSecretValues(c, fullConfig.Secrets, keyToSecret)
	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if fullConfig.Application.Environment == cfg.TestEnv || fullConfig.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c, fullConfig.Secrets.Ids[dbCredentials])
		return nil
	}
	if _, ok := keyToSecret[dbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c, keyToSecret[dbCredentials])
	return nil
}

type Config struct {
	Application     *application
	VendorMappingDb *cfg.DB
	Aws             *aws
	Secrets         *cfg.Secrets
	S3Bucket        string
	// CsvDirPath      string
	// CsvFile         string
}

type application struct {
	Environment string
	Name        string
}

type aws struct {
	Region string
}
