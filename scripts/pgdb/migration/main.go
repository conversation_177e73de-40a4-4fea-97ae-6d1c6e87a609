package main

import (
	"flag"
	"fmt"
	"io/fs"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
)

func getDbConf(host, user, pass, database string) *cfg.DB {
	return &cfg.DB{
		Host:             host,
		Password:         pass,
		Username:         user,
		AppName:          "jenkins",
		Port:             5432,
		StatementTimeout: 5 * time.Second,
		GormV2: &cfg.GormV2Conf{
			LogLevelGormV2:        "INFO",
			SlowQueryLogThreshold: 10 * time.Millisecond,
			UseInsecureLog:        true,
		},
		EnableDebug: false,
		Name:        database,
		SSLMode:     "require",
		MaxOpenConn: 3,
		MaxIdleConn: 1,
		MaxConnTtl:  30 * time.Minute,
	}
}

var tablesWithUpdatedAtIndexRegex = regexp.MustCompile(`(?m)CREATE TABLE public\.(.*) \((\n.*?)*?((INDEX.*\(updated_at)|(\);))`)
var UpdatedAtIndexRegex = regexp.MustCompile(`(?m)CREATE INDEX (.*) ON public\.(.*) USING btree .*\(updated_at`)

func main() {
	logger.Init(cfg.DevelopmentEnv)

	sourceDbHost := os.Getenv("SOURCE_DB_HOST")
	sourceDbUser := os.Getenv("SOURCE_DB_USER")
	sourceDbPass := os.Getenv("SOURCE_DB_PASS")

	destDbHost := os.Getenv("DEST_DB_HOST")
	destDbUser := os.Getenv("DEST_DB_USER")
	destDbPass := os.Getenv("DEST_DB_PASS")

	var database string
	flag.StringVar(&database, "database", "", "database to run the checks against")
	flag.Parse()
	logger.InfoNoCtx("Connecting to database " + database)

	allTables, err := getAllTablesWithUpdatedAtIndex(database)
	if err != nil {
		logger.ErrorNoCtx("Error getting all tables", zap.Error(err))
	}

	sourceDb, err := storageV2.NewPostgresDBWithConfig(getDbConf(sourceDbHost, sourceDbUser, sourceDbPass, database), false)
	if err != nil {
		logger.Panic("Error initialising connection to db", zap.Error(err))
	}
	defer func() {
		sqlDb, _ := sourceDb.DB()
		_ = sqlDb.Close()
	}()

	destDb, err := storageV2.NewPostgresDBWithConfig(getDbConf(destDbHost, destDbUser, destDbPass, database), false)
	if err != nil {
		logger.Panic("Error initialising connection to db", zap.Error(err))
	}
	defer func() {
		sqlDb, _ := destDb.DB()
		_ = sqlDb.Close()
	}()

	allIdsMatched := true
	for _, table := range allTables {
		allIdsMatched = allIdsMatched && compareIds(sourceDb, destDb, table)
	}
	if !allIdsMatched {
		logger.ErrorNoCtx("Ids do not match for the database")
	}
}

type tableInfo struct {
	tableName  string
	primaryKey string
}

func getAllTablesWithUpdatedAtIndex(database string) ([]tableInfo, error) {
	dbDir := "../../../db/" + database

	allTables := []tableInfo{}
	err := filepath.Walk(dbDir, func(path string, info fs.FileInfo, err error) error {
		if err != nil {
			return err
		}
		parent := filepath.Dir(path)
		if info.IsDir() {
			if !strings.HasSuffix(parent, "db") {
				return nil
			}
			content, err := ioutil.ReadFile(filepath.Join(path, "latest.sql"))
			if err != nil {
				log.Println(err)
				return nil
			}
			tablesWithUpdatedAt := tablesWithUpdatedAtIndexRegex.FindAllStringSubmatch(string(content), -1)

			var tablesWithUpdatedAtInd []string

			for _, group := range tablesWithUpdatedAt {
				if strings.Contains(group[3], "INDEX") {
					tablesWithUpdatedAtInd = append(tablesWithUpdatedAtInd, group[1])
				}
			}
			updatedAtIndexes := UpdatedAtIndexRegex.FindAllStringSubmatch(string(content), -1)
			for _, group := range updatedAtIndexes {
				tablesWithUpdatedAtInd = append(tablesWithUpdatedAtInd, group[2])
			}

			// ALTER TABLE ONLY public.dp_vendor_mappings
			//    ADD CONSTRAINT dp_vendor_mappings_pkey PRIMARY KEY (id);
			for _, table := range tablesWithUpdatedAtInd {
				primaryKeyRegex := regexp.MustCompile(`(?m)ALTER TABLE ONLY public\.` + table + `\s.*ADD CONSTRAINT .* PRIMARY KEY \((.*)\);`)
				primaryKeyNames := primaryKeyRegex.FindAllStringSubmatch(string(content), -1)
				for _, group := range primaryKeyNames {
					if len(group) == 2 {
						allTables = append(allTables, tableInfo{tableName: table, primaryKey: group[1]})
					} else {
						logger.InfoNoCtx("Cannot find primaryKey for table", zap.String("table", table))
					}
				}
			}
		}
		return nil
	})
	return allTables, err
}

func compareIds(sourceDb, destDb *gorm.DB, table tableInfo) bool {
	logger.InfoNoCtx("Comparing ids for table " + table.tableName)
	sourceIds := getIds(sourceDb, table)
	destIds := getIds(destDb, table)
	if !reflect.DeepEqual(sourceIds, destIds) {
		logger.InfoNoCtx("Ids for table do not match", zap.Any("sourceIds", sourceIds), zap.Any("destIds", destIds))
		return false
	}
	return true
}

func getIds(db *gorm.DB, table tableInfo) []interface{} {
	ids := []interface{}{}
	if err := db.Raw(fmt.Sprintf("SELECT %s from %s ORDER BY updated_at, %s DESC LIMIT 10", table.primaryKey, table.tableName, table.primaryKey)).Scan(&ids).Error; err != nil {
		logger.InfoNoCtx("Error getting ids for table "+table.tableName, zap.Error(err))
	}
	return ids
}
