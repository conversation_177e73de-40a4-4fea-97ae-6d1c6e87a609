package dao

import (
	"flag"
	"log"
	"os"
	"testing"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config/genconf"
	"github.com/epifi/be-common/pkg/logger"
	testPkg "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gringott/customer/dao/test"
)

var (
	kts                       *KYCTestSuite
	dbNameToDbInstancePoolMap = make(map[string]testPkg.DbInstancePool)
	ktsAffectedTables         = []string{"kyc_vendor_data"}
)

type KYCTestSuite struct {
	kycDBName        string
	kycVendorDataDao KYCVendorDataDao
}

func NewKycTestSuite(kycDBName string, kycVendorDataDao KYCVendorDataDao) *KYCTestSuite {
	return &KYCTestSuite{
		kycDBName:        kycDBName,
		kycVendorDataDao: kycVendorDataDao,
	}
}

func TestMain(m *testing.M) {
	flag.Parse()

	serverConf, err := genconf.Load(cfg.ONBOARDING_SERVER)
	if err != nil {
		log.Fatal("failed to load onboarding server configs", err)
	}

	stockGuardianDB := serverConf.Databases()["StockGuardianPGDB"]

	test.InitTestServerWithoutDB()

	lg := testPkg.NewZapLogger(logger.Log)
	dbInstancePool := testPkg.NewPgdbDbInstancePool(lg, stockGuardianDB, 1)
	dbNameToDbInstancePoolMap[stockGuardianDB.Name] = dbInstancePool

	dbInstance, releaseFn := dbInstancePool.GetDbInstance(lg)
	releaseFn([]string{})
	dbConn := dbInstance.GetConnection()

	kycDao := NewKYCVendorDataDaoImpl(dbConn)
	kts = NewKycTestSuite(serverConf.Databases()["StockGuardianPGDB"].Name, kycDao)

	exitCode := m.Run()
	dbNameToDbInstancePoolMap[stockGuardianDB.Name].Cleanup(lg)
	os.Exit(exitCode)
}
