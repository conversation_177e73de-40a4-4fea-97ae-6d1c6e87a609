// nolint
package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiPayloadPb "github.com/epifi/gamma/api/upi/payload"
	upiWorkflowPb "github.com/epifi/gamma/api/upi/workflow"
)

// DeactivateUpiLite - executes the upi lite deactivation for an upi lite account (WITH NON-ZERO BALANCE)
// Flow:
// 1. Create UpiOnboardingEntity for upi lite deactivation
// 2. Upi lite payment orchestration
// 3. Upi lite pi deactivation
// 4. Upi lite account deactivation on our end
// 5. Send notification for upi lite account deactivation to user
func DeactivateUpiLite(ctx workflow.Context, _ *upiWorkflowPb.DeactivateUpiLiteRequest) error {
	var (
		lg                  = workflow.GetLogger(ctx)
		workflowStageStatus stagePb.Status
		wfReqID             = workflow.GetInfo(ctx).WorkflowExecution.ID
		ownership           = epificontext.OwnershipFromContext(ctx)
	)

	workflowInfo := &upiPayloadPb.DeactivateUpiLiteInfo{}
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, workflowInfo)
	if err != nil {
		lg.Error("error in getting workflow processing params to deactivate upi lite for an upi account", zap.Error(err))
		return err
	}

	clientReqId := wfProcessingParams.GetClientReqId().GetId()

	// Stage-1 => ---------------------- Upi-Onboarding-Entity Creation ---------------------------
	if workflowStageStatus, err = createUpiOnboardingEntity(ctx, wfReqID, clientReqId, &upiActivityPb.CreateUpiOnboardingEntityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
		},
		AccountId: workflowInfo.GetAccountId(),
		ActorId:   workflowInfo.GetActorId(),
		Action:    workflowInfo.GetAction(),
		Vendor:    workflowInfo.GetVendor(),
		Vpa:       workflowInfo.GetVpa(),
		Payload:   workflowInfo.GetPayload(),
	}, ownership); err != nil {
		lg.Error("failed to create upi onboarding entity for deactivation of upi lite", zap.Error(err))
		return err
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return nil
	}

	// Stage-2 => ------------------ upiLitePaymentOrchestration --------------------
	if workflowStageStatus, err = upiLitePaymentOrchestration(ctx, workflowInfo.GetActorId(), clientReqId, ownership); err != nil {
		lg.Error("failed to transfer balance from upi lite account to upi account", zap.Error(err))
		return err
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return nil
	}

	// Stage-3 => ----------------- upiLitePIDeactivation -------------------
	if workflowStageStatus, err = upiLitePIDeactivation(ctx, clientReqId, ownership, workflowInfo.GetPayload().GetDeactivateUpiLitePayload().GetLrn()); err != nil {
		lg.Error("upi lite PI deactivation failed", zap.Error(err))
		return err
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return nil
	}

	// Stage-4 => ------------------ upiLiteAccountDeactivation -------------------
	if workflowStageStatus, err = upiLiteAccountDeactivation(ctx, clientReqId, ownership, workflowInfo); err != nil {
		lg.Error("failed to deactivate upi lite account", zap.Error(err))
		return err
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return nil
	}

	// Stage-5 => ----------------- UpiLiteActionNotification --------------------
	if workflowStageStatus, err = upiLiteActionNotification(ctx, clientReqId, workflowInfo.GetActorId(), ownership); err != nil {
		lg.Error("failed to send notification after successful deactivation of upi lite", zap.Error(err))
		return err
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return nil
	}

	return nil
}

// upiLiteAccountDeactivation - used for upi lite account deactivation at our end
func upiLiteAccountDeactivation(ctx workflow.Context, clientReqId string, ownership commontypes.Ownership, workflowInfo *upiPayloadPb.DeactivateUpiLiteInfo) (stagePb.Status, error) {
	var (
		lg                       = workflow.GetLogger(ctx)
		workflowStageStatus      = stagePb.Status_INITIATED
		markAccountAsDelinkedRes = &upiActivityPb.MarkAccountAsDelinkedResponse{}
	)

	// Step 1 => Initiate workflow stage
	err := celestialPkg.InitiateWorkflowStage(ctx, upiNs.UpiLiteAccountDeactivation, workflowStageStatus)
	if err != nil {
		lg.Error("%s activity failed", epifitemporal.InitiateWorkflowStageV2, zap.Error(err))
		return workflowStageStatus, fmt.Errorf("%s activity failed: %w", epifitemporal.InitiateWorkflowStageV2, err)
	}

	delinkUpiLiteAccountPayload, _ := protojson.Marshal(&upiPayloadPb.DelinkUpiAccountInfo{
		AccountId: workflowInfo.GetAccountId(),
		ActorId:   workflowInfo.GetActorId(),
		ClientId: &celestialPb.ClientReqId{
			Id:     clientReqId,
			Client: workflowPb.Client_USER_APP,
		},
		Vendors: []commonvgpb.Vendor{workflowInfo.GetVendor()},
	})

	// activity for upi Lite account deactivation
	err = activityPkg.Execute(ctx, upiNs.MarkAccountAsDelinked, markAccountAsDelinkedRes, &upiActivityPb.MarkAccountAsDelinkedRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: clientReqId,
			Ownership:   ownership,
			Payload:     delinkUpiLiteAccountPayload,
		},
	})

	// => update workflow stage status
	workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
	updateErr := celestialPkg.UpdateWorkflowStage(ctx, upiNs.UpiLiteAccountDeactivation, workflowStageStatus)
	if updateErr != nil {
		lg.Error("%s activity failed", epifitemporal.UpdateWorkflowStage, zap.Error(updateErr))
		return workflowStageStatus, fmt.Errorf("%s activity failed: %w", epifitemporal.UpdateWorkflowStage, updateErr)
	}
	return workflowStageStatus, err
}
