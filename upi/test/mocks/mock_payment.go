// Code generated by MockGen. DO NOT EDIT.
// Source: api/order/payment/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	domain "github.com/epifi/gamma/api/order/domain"
	payment "github.com/epifi/gamma/api/order/payment"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPaymentClient is a mock of PaymentClient interface
type MockPaymentClient struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentClientMockRecorder
}

// MockPaymentClientMockRecorder is the mock recorder for MockPaymentClient
type MockPaymentClientMockRecorder struct {
	mock *MockPaymentClient
}

// NewMockPaymentClient creates a new mock instance
func NewMockPaymentClient(ctrl *gomock.Controller) *MockPaymentClient {
	mock := &MockPaymentClient{ctrl: ctrl}
	mock.recorder = &MockPaymentClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockPaymentClient) EXPECT() *MockPaymentClientMockRecorder {
	return m.recorder
}

// CreateTransaction mocks base method
func (m *MockPaymentClient) CreateTransaction(ctx context.Context, in *payment.CreateTransactionRequest, opts ...grpc.CallOption) (*payment.CreateTransactionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTransaction", varargs...)
	ret0, _ := ret[0].(*payment.CreateTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransaction indicates an expected call of CreateTransaction
func (mr *MockPaymentClientMockRecorder) CreateTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransaction", reflect.TypeOf((*MockPaymentClient)(nil).CreateTransaction), varargs...)
}

// InitiateTransaction mocks base method
func (m *MockPaymentClient) InitiateTransaction(ctx context.Context, in *payment.InitiateTransactionRequest, opts ...grpc.CallOption) (*payment.InitiateTransactionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateTransaction", varargs...)
	ret0, _ := ret[0].(*payment.InitiateTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateTransaction indicates an expected call of InitiateTransaction
func (mr *MockPaymentClientMockRecorder) InitiateTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateTransaction", reflect.TypeOf((*MockPaymentClient)(nil).InitiateTransaction), varargs...)
}

// GetTransaction mocks base method
func (m *MockPaymentClient) GetTransaction(ctx context.Context, in *payment.GetTransactionRequest, opts ...grpc.CallOption) (*payment.GetTransactionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTransaction", varargs...)
	ret0, _ := ret[0].(*payment.GetTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction
func (mr *MockPaymentClientMockRecorder) GetTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockPaymentClient)(nil).GetTransaction), varargs...)
}

// UpdateTransaction mocks base method
func (m *MockPaymentClient) UpdateTransaction(ctx context.Context, in *payment.UpdateTransactionRequest, opts ...grpc.CallOption) (*payment.UpdateTransactionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTransaction", varargs...)
	ret0, _ := ret[0].(*payment.UpdateTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransaction indicates an expected call of UpdateTransaction
func (mr *MockPaymentClientMockRecorder) UpdateTransaction(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransaction", reflect.TypeOf((*MockPaymentClient)(nil).UpdateTransaction), varargs...)
}

// GetOrderId mocks base method
func (m *MockPaymentClient) GetOrderId(ctx context.Context, in *payment.GetOrderIdRequest, opts ...grpc.CallOption) (*payment.GetOrderIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrderId", varargs...)
	ret0, _ := ret[0].(*payment.GetOrderIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderId indicates an expected call of GetOrderId
func (mr *MockPaymentClientMockRecorder) GetOrderId(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderId", reflect.TypeOf((*MockPaymentClient)(nil).GetOrderId), varargs...)
}

// GetTxnsByPi mocks base method
func (m *MockPaymentClient) GetTxnsByPi(ctx context.Context, in *payment.GetTxnsByPiRequest, opts ...grpc.CallOption) (*payment.GetTxnsByPiResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTxnsByPi", varargs...)
	ret0, _ := ret[0].(*payment.GetTxnsByPiResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxnsByPi indicates an expected call of GetTxnsByPi
func (mr *MockPaymentClientMockRecorder) GetTxnsByPi(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxnsByPi", reflect.TypeOf((*MockPaymentClient)(nil).GetTxnsByPi), varargs...)
}

// MakeDepositOwnFundTransferPayment mocks base method
func (m *MockPaymentClient) MakeDepositOwnFundTransferPayment(ctx context.Context, in *domain.ProcessFulfilmentRequest, opts ...grpc.CallOption) (*domain.ProcessFulfilmentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MakeDepositOwnFundTransferPayment", varargs...)
	ret0, _ := ret[0].(*domain.ProcessFulfilmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeDepositOwnFundTransferPayment indicates an expected call of MakeDepositOwnFundTransferPayment
func (mr *MockPaymentClientMockRecorder) MakeDepositOwnFundTransferPayment(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeDepositOwnFundTransferPayment", reflect.TypeOf((*MockPaymentClient)(nil).MakeDepositOwnFundTransferPayment), varargs...)
}

// MockPaymentServer is a mock of PaymentServer interface
type MockPaymentServer struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentServerMockRecorder
}

// MockPaymentServerMockRecorder is the mock recorder for MockPaymentServer
type MockPaymentServerMockRecorder struct {
	mock *MockPaymentServer
}

// NewMockPaymentServer creates a new mock instance
func NewMockPaymentServer(ctrl *gomock.Controller) *MockPaymentServer {
	mock := &MockPaymentServer{ctrl: ctrl}
	mock.recorder = &MockPaymentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockPaymentServer) EXPECT() *MockPaymentServerMockRecorder {
	return m.recorder
}

// CreateTransaction mocks base method
func (m *MockPaymentServer) CreateTransaction(arg0 context.Context, arg1 *payment.CreateTransactionRequest) (*payment.CreateTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransaction", arg0, arg1)
	ret0, _ := ret[0].(*payment.CreateTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTransaction indicates an expected call of CreateTransaction
func (mr *MockPaymentServerMockRecorder) CreateTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransaction", reflect.TypeOf((*MockPaymentServer)(nil).CreateTransaction), arg0, arg1)
}

// InitiateTransaction mocks base method
func (m *MockPaymentServer) InitiateTransaction(arg0 context.Context, arg1 *payment.InitiateTransactionRequest) (*payment.InitiateTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateTransaction", arg0, arg1)
	ret0, _ := ret[0].(*payment.InitiateTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateTransaction indicates an expected call of InitiateTransaction
func (mr *MockPaymentServerMockRecorder) InitiateTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateTransaction", reflect.TypeOf((*MockPaymentServer)(nil).InitiateTransaction), arg0, arg1)
}

// GetTransaction mocks base method
func (m *MockPaymentServer) GetTransaction(arg0 context.Context, arg1 *payment.GetTransactionRequest) (*payment.GetTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", arg0, arg1)
	ret0, _ := ret[0].(*payment.GetTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction
func (mr *MockPaymentServerMockRecorder) GetTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockPaymentServer)(nil).GetTransaction), arg0, arg1)
}

// UpdateTransaction mocks base method
func (m *MockPaymentServer) UpdateTransaction(arg0 context.Context, arg1 *payment.UpdateTransactionRequest) (*payment.UpdateTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTransaction", arg0, arg1)
	ret0, _ := ret[0].(*payment.UpdateTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTransaction indicates an expected call of UpdateTransaction
func (mr *MockPaymentServerMockRecorder) UpdateTransaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTransaction", reflect.TypeOf((*MockPaymentServer)(nil).UpdateTransaction), arg0, arg1)
}

// GetOrderId mocks base method
func (m *MockPaymentServer) GetOrderId(arg0 context.Context, arg1 *payment.GetOrderIdRequest) (*payment.GetOrderIdResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrderId", arg0, arg1)
	ret0, _ := ret[0].(*payment.GetOrderIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrderId indicates an expected call of GetOrderId
func (mr *MockPaymentServerMockRecorder) GetOrderId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrderId", reflect.TypeOf((*MockPaymentServer)(nil).GetOrderId), arg0, arg1)
}

// GetTxnsByPi mocks base method
func (m *MockPaymentServer) GetTxnsByPi(arg0 context.Context, arg1 *payment.GetTxnsByPiRequest) (*payment.GetTxnsByPiResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxnsByPi", arg0, arg1)
	ret0, _ := ret[0].(*payment.GetTxnsByPiResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxnsByPi indicates an expected call of GetTxnsByPi
func (mr *MockPaymentServerMockRecorder) GetTxnsByPi(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxnsByPi", reflect.TypeOf((*MockPaymentServer)(nil).GetTxnsByPi), arg0, arg1)
}

// MakeDepositOwnFundTransferPayment mocks base method
func (m *MockPaymentServer) MakeDepositOwnFundTransferPayment(arg0 context.Context, arg1 *domain.ProcessFulfilmentRequest) (*domain.ProcessFulfilmentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeDepositOwnFundTransferPayment", arg0, arg1)
	ret0, _ := ret[0].(*domain.ProcessFulfilmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeDepositOwnFundTransferPayment indicates an expected call of MakeDepositOwnFundTransferPayment
func (mr *MockPaymentServerMockRecorder) MakeDepositOwnFundTransferPayment(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeDepositOwnFundTransferPayment", reflect.TypeOf((*MockPaymentServer)(nil).MakeDepositOwnFundTransferPayment), arg0, arg1)
}
