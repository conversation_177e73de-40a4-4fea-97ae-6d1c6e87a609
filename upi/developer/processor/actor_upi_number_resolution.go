package processor

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/proto/json"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	upiDevPb "github.com/epifi/gamma/api/upi/developer"
	upiPb "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/upi/dao"
)

type ActorUPINumberResolutionProcessor struct {
	actorUpiNumberResolutionDao dao.ActorUpiNumberResolutionDao
}

func NewActorUPINumberResolutionProcessor(actorUpiNumberResolutionDao dao.ActorUpiNumberResolutionDao) *ActorUPINumberResolutionProcessor {
	return &ActorUPINumberResolutionProcessor{
		actorUpiNumberResolutionDao: actorUpiNumberResolutionDao,
	}
}

func (d *ActorUPINumberResolutionProcessor) FetchParamList(ctx context.Context, entity upiDevPb.UpiEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           "ActorId",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *ActorUPINumberResolutionProcessor) FetchData(ctx context.Context, entity upiDevPb.UpiEntity, filters []*db_state.Filter) (string, error) {

	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil to fetch UPI account")
	}
	var (
		actorUpiNumberResolutionDetail     *upiPb.ActorUpiNumberResolution
		actorUpiNumberResolutionDetailList []*upiPb.ActorUpiNumberResolution
		err                                error
		data                               []byte
	)
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorUpiNumberResolutionDetailList, err = d.actorUpiNumberResolutionDao.GetByActorId(ctx, filter.GetStringValue())
			if err != nil {
				return "", fmt.Errorf("error while fetching Detail of actor upi number resolution detail: %w", err)
			}
			data, err = json.Marshal(actorUpiNumberResolutionDetailList)
			if err != nil {
				return "", fmt.Errorf("cannot marshal struct to json: %w", err)
			}
			return string(data), nil
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	data, err = json.Marshal(actorUpiNumberResolutionDetail)

	if err != nil {
		return "", fmt.Errorf("cannot marshal struct to json: %w", err)
	}

	return string(data), nil
}
