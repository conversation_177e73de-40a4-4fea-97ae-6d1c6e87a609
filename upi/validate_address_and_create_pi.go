package upi

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/typesv2/account"
	upiPb "github.com/epifi/gamma/api/upi"
	upiPkg "github.com/epifi/gamma/pkg/upi"
)

func (s *Service) ValidateAddressAndCreatePi(ctx context.Context, req *upiPb.ValidateAddressAndCreatePiRequest) (*upiPb.ValidateAddressAndCreatePiResponse, error) {
	var (
		res    = &upiPb.ValidateAddressAndCreatePiResponse{}
		device = req.GetDevice()
		err    error
	)
	// cloning the context without context deadline
	// using different context here as if we get a response from vendor we want to
	// process it for future use
	ctx = epificontext.CloneCtx(ctx)
	res.Status = rpc.StatusOk()

	if device == nil {
		device, err = s.upiProcessor.GetDeviceForActor(ctx, req.GetPayerActorId())
		if err != nil {
			logger.Error(ctx, "error fetching device for actor", zap.String(logger.ACTOR_ID_V2, req.GetUpiVpa()), zap.Error(err))
			res.Status = rpc.StatusFromError(err)
			return res, nil
		}
	}

	verifyVpaRes, err := s.VerifyPayeeVPA(ctx, &upiPb.VerifyPayeeVPARequest{
		Device:       device,
		UpiVpa:       req.GetUpiVpa(),
		PayerActorId: req.GetPayerActorId(),
	})
	switch {
	case err != nil:
		logger.Debug(ctx, "error verifying vpa", zap.String(logger.VPA, req.GetUpiVpa()), zap.Error(err))
		res.Status = rpc.StatusInternal()
	case verifyVpaRes.GetStatus().GetCode() == statusInvalidVPA().GetCode() || verifyVpaRes.GetStatus().GetCode() == statusInvalidMerchant().GetCode():
		res.Status = rpc.NewStatusWithoutDebug(uint32(upiPb.ValidateAddressAndCreatePiResponse_INVALID_VPA), "Invalid vpa")
	case verifyVpaRes.GetStatus().IsEqualTo(statusPSPNotRegistered()):
		res.Status = rpc.NewStatusWithoutDebug(uint32(upiPb.ValidateAddressAndCreatePiResponse_PSP_NOT_REGISTERED), "PSP not registered")
	case verifyVpaRes.GetStatus().GetCode() == statusPSPNotAvailable().GetCode():
		logger.Debug(ctx, "psp is not available at the moment", zap.String(logger.VPA, req.GetUpiVpa()))
		res.Status = rpc.NewStatusWithoutDebug(uint32(upiPb.ValidateAddressAndCreatePiResponse_PSP_NOT_AVAILABLE), "PSP not available")
	case verifyVpaRes.GetStatus().GetCode() == statusVPARestricted().GetCode(),
		verifyVpaRes.GetStatus().GetCode() == statusTransactionNotPermittedToVPA().GetCode():
		logger.Debug(ctx, "vpa is restricted", zap.String(logger.VPA, req.GetUpiVpa()))
		res.Status = rpc.NewStatusWithoutDebug(uint32(upiPb.ValidateAddressAndCreatePiResponse_VPA_RESTRICTED), "Vpa is restricted")
	case verifyVpaRes.GetStatus().GetCode() == statusSuspectedFraudError().GetCode():
		logger.Debug(ctx, "suspected fraud, txns declined based on risk score by beneficiary", zap.String(logger.VPA, req.GetUpiVpa()))
		res.Status = rpc.NewStatusWithoutDebug(uint32(upiPb.ValidateAddressAndCreatePiResponse_SUSPECTED_FRAUD),
			"suspected fraud, txns declined based on risk score by beneficiary")
	case verifyVpaRes.GetStatus().GetCode() == activeInternalPiNotPresentStatus().GetCode():
		logger.Info(ctx, "active internal pi not present", zap.String("validation_mode", req.GetVpaValidationMode().String())) // Check if it's a precondition error and should be handled gracefully
		if req.GetVpaValidationMode() == upiPb.ValidateAddressAndCreatePiRequest_VPA_VALIDATION_MODE_LENIENT {
			// Create PI with just the VPA
			res.CustomerName = upiPkg.TruncatePspHandle(req.GetUpiVpa())
			piId, createErr := s.piHelperSvc.CreateVpaPi(
				ctx,
				req.GetUpiVpa(),
				"",
				// passing * as customer name as a workaround to create pi without actual customer
				// name under the hood alphanumeric characters gets anyway truncated and a pi with empty name is created
				"*",
				nil,
				req.GetOwnership(),
				"",
				accounts.Type_TYPE_UNSPECIFIED,
				account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
				res.GetCustomerName(),
			)
			if createErr != nil {
				logger.Error(ctx, "error creating pi for vpa", zap.Error(createErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
			res.Status = rpc.StatusOk()
			res.PiId = piId
			logger.Info(ctx, "pi created with lenient mode", zap.String(logger.PI_ID, res.GetPiId()))
			return res, nil
		}
		res.Status = rpc.StatusInternal()
	case !verifyVpaRes.GetStatus().IsSuccess():
		logger.Error(ctx, "VerifyPayeeVPA() returned non ok status code", zap.Uint32(logger.STATUS_CODE, verifyVpaRes.GetStatus().GetCode()))
		res.Status = rpc.StatusInternal()
	}
	if !res.GetStatus().IsSuccess() {
		return res, nil
	}

	piId, err := s.piHelperSvc.CreateVpaPi(
		ctx,
		req.GetUpiVpa(),
		verifyVpaRes.GetMcc(),
		verifyVpaRes.GetCustomerName(),
		verifyVpaRes.GetMerchant(),
		req.GetOwnership(),
		verifyVpaRes.GetIfsc(),
		verifyVpaRes.GetAccountType(),
		verifyVpaRes.GetApo(),
		"",
	)
	if err != nil {
		logger.Error(ctx, "error creating pi for vpa", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.PiId = piId
	res.CustomerName = verifyVpaRes.GetCustomerName()
	res.Merchant = verifyVpaRes.GetMerchant()
	res.Mcc = verifyVpaRes.GetMcc()
	return res, nil
}
