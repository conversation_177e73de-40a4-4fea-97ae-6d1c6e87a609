package onboarding_test

import (
	"context"
	json2 "encoding/json"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/upi/dao"
	"github.com/epifi/gamma/upi/onboarding"
)

func TestService_GetAccountsForUpiLiteActivation(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	svc, mocks, _, ctrFinishFun := getUpiServiceWithMocks(t)
	ctrFinishFun()

	mocks.mockEventBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()

	type mockGet struct {
		enable       bool
		keyId        string
		returnString string
		returnErr    error
	}

	type mockGetByActorId struct {
		enable       bool
		actorId      string
		filterOption []storagev2.FilterOption
		res          []*upiOnbPb.UpiAccount
		err          error
	}

	listAccountProvidersList := []*upi.AccountPvd{listAccountProvider2, listAccountProvider4}

	var listAccountProvidersInfo = onboarding.AccountProvidersInfo{AccountProvidersList: listAccountProvidersList}
	// nolint: errchkjson
	listAccountProvidersMarshal, _ := json2.Marshal(listAccountProvidersInfo)

	upiAccount1 := &upiOnbPb.UpiAccount{
		Id:       "acc-1",
		BankName: "icic",
		IfscCode: "2727",
	}

	upiAccount2 := &upiOnbPb.UpiAccount{
		Id:       "acc-2",
		IfscCode: "5253",
		BankName: "hdfc",
	}

	tests := []struct {
		name             string
		req              *upiOnbPb.GetAccountsForUpiLiteActivationRequest
		mockGetByActorId mockGetByActorId
		mockGet          mockGet
		want             *upiOnbPb.GetAccountsForUpiLiteActivationResponse
		wantErr          bool
	}{
		{
			name: "fetched accounts successfully for upi lite activation",
			req: &upiOnbPb.GetAccountsForUpiLiteActivationRequest{
				ActorId: "actor-1",
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				filterOption: []storagev2.FilterOption{
					dao.WithUpiAccountStatusFilter([]upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					}),
				},
				res: []*upiOnbPb.UpiAccount{
					upiAccount1, upiAccount2,
				},
				err: nil,
			},
			mockGet: mockGet{
				enable:       true,
				keyId:        prefixListAccountProviders,
				returnString: string(listAccountProvidersMarshal),
			},
			want: &upiOnbPb.GetAccountsForUpiLiteActivationResponse{
				Status: rpc.StatusOk(),
				Accounts: []*upiOnbPb.UpiAccount{
					upiAccount2,
				},
			},
		},
		{
			name: "failed to fetch account for upi lite activation as dao call failed",
			req: &upiOnbPb.GetAccountsForUpiLiteActivationRequest{
				ActorId: "actor-1",
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				filterOption: []storagev2.FilterOption{
					dao.WithUpiAccountStatusFilter([]upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					}),
				},
				err: epifierrors.ErrRecordNotFound,
			},
			want: &upiOnbPb.GetAccountsForUpiLiteActivationResponse{
				Status: rpc.StatusRecordNotFound(),
			},
		},
		{
			name: "no eligible account found for upi lite activation",
			req: &upiOnbPb.GetAccountsForUpiLiteActivationRequest{
				ActorId: "actor-1",
			},
			mockGetByActorId: mockGetByActorId{
				enable:  true,
				actorId: "actor-1",
				filterOption: []storagev2.FilterOption{
					dao.WithUpiAccountStatusFilter([]upiOnboardingEnumsPb.UpiAccountStatus{
						upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					}),
				},
				res: []*upiOnbPb.UpiAccount{
					fixture2, fixture1,
				},
				err: nil,
			},
			mockGet: mockGet{
				enable:       true,
				keyId:        prefixListAccountProviders,
				returnString: string(listAccountProvidersMarshal),
			},
			want: &upiOnbPb.GetAccountsForUpiLiteActivationResponse{
				Status:   rpc.StatusOk(),
				Accounts: []*upiOnbPb.UpiAccount{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByActorId.enable {
				mocks.mockUpiAccountDao.EXPECT().GetByActorId(
					context.Background(), tt.mockGetByActorId.actorId, gomock.Any()).
					Return(tt.mockGetByActorId.res, tt.mockGetByActorId.err)
			}

			if tt.mockGet.enable {
				mocks.mockCache.EXPECT().Get(context.Background(), tt.mockGet.keyId).Return(tt.mockGet.returnString, tt.mockGet.returnErr)
			}
			got, err := svc.GetAccountsForUpiLiteActivation(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAccountsForUpiLiteActivation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetAccountsForUpiLiteActivation() got = %v, want %v", got, tt.want)
			}
		})
	}
}
