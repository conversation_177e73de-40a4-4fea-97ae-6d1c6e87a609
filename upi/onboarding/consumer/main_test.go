package consumer_test

import (
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	mockComms "github.com/epifi/gamma/api/comms/mocks"
	upiOnbMocksPb "github.com/epifi/gamma/api/upi/onboarding/mocks"
	vgOpbUpiMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/upi/mocks"
	"github.com/epifi/gamma/upi/config"
	"github.com/epifi/gamma/upi/config/genconf"
	upiDaoMock "github.com/epifi/gamma/upi/dao/mocks"
	upiOnbConsumer "github.com/epifi/gamma/upi/onboarding/consumer"
	"github.com/epifi/gamma/upi/test"
)

var (
	upiConf *config.Config
)

type upiOnbConsumerMocks struct {
	upiOnboardingClient   *upiOnbMocksPb.MockUpiOnboardingClient
	upiNumberPiMappingDao *upiDaoMock.MockUpiNumberPiMappingDao
	vgUpiClient           *vgOpbUpiMocks.MockUPIClient
	upiOnbDetailDao       *upiDaoMock.MockUpiOnboardingDetailDao
	commsClient           *mockComms.MockCommsClient
}

func TestMain(m *testing.M) {
	flag.Parse()

	var teardown func()
	_, upiConf, _, _, teardown = test.InitTestServer() //nolint
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func getUpiOnbConsumerWithMocks(t *testing.T) (*upiOnbConsumer.Service, *upiOnbConsumerMocks, *config.Config, func()) {
	ctr := gomock.NewController(t)
	mocks := &upiOnbConsumerMocks{
		upiOnboardingClient:   upiOnbMocksPb.NewMockUpiOnboardingClient(ctr),
		upiNumberPiMappingDao: upiDaoMock.NewMockUpiNumberPiMappingDao(ctr),
		vgUpiClient:           vgOpbUpiMocks.NewMockUPIClient(ctr),
		upiOnbDetailDao:       upiDaoMock.NewMockUpiOnboardingDetailDao(ctr),
		commsClient:           mockComms.NewMockCommsClient(ctr),
	}
	dynconf, err := genconf.Load()
	require.NoError(t, err)
	return upiOnbConsumer.NewService(
			mocks.upiOnboardingClient,
			mocks.upiNumberPiMappingDao,
			mocks.vgUpiClient,
			mocks.commsClient,
			dynconf,
			mocks.upiOnbDetailDao,
			nil,
		), mocks, upiConf, func() {
			defer ctr.Finish()
		}
}
