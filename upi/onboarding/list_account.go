package onboarding

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/gamma/api/paymentinstrument"
	piAccPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	typesPb "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/savings"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	payPkg "github.com/epifi/gamma/pkg/pay"
	pkgUpi "github.com/epifi/gamma/pkg/upi"
	"github.com/epifi/gamma/upi"
	"github.com/epifi/gamma/upi/dao"
	upiEvent "github.com/epifi/gamma/upi/events"
)

var (
	allAccountsAlreadyLinkedStatus rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(uint32(upiOnboardingPb.ListAccountResponse_ALL_ACCOUNTS_ALREADY_LINKED_FOR_BANK), "all accounts already linked for bank")
	}
	primaryAccountApoPriorityMap = map[accountTypesPb.AccountProductOffering]int{
		accountTypesPb.AccountProductOffering_APO_REGULAR: 1,
		// older accounts might not have APO, therefore giving it higher priority than NRE, NRO
		accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED: 2,
		accountTypesPb.AccountProductOffering_APO_NRE:                              3,
		accountTypesPb.AccountProductOffering_APO_NRO:                              4,
	}
)

// ListAccount finds the account linked to a particular bank by mobile number and ifsc. It calls the vg api ListAccount to fetch this information
// After getting the response from vg, we check if the account is already created in the upi accounts table by matching the ifsc code and masked account number,
// if not we create upi accounts for them in batch. Internal savings accounts of user will be given Primary AccountPreference if no other primary account exists.
// When user has multiple upi accounts with corresponding savings accounts, Primary AccountPreference will be given in APO priority order specified in primaryAccountApoPriorityMap.
// After that we filter out the accounts that are already linked or if there linking is in progress
// Then we get the required info from the non filtered accounts and return them as accountInfos which contains accountId,masked account number and ifsc
func (s *Service) ListAccount(ctx context.Context, req *upiOnboardingPb.ListAccountRequest) (*upiOnboardingPb.ListAccountResponse, error) {
	var (
		res                    = &upiOnboardingPb.ListAccountResponse{}
		accountInfosVendor     []*vgUpiPb.UpiAccountInfoVendor
		savingsAccs            []*savings.Account
		accountsFromVendorCall []*upiOnboardingPb.UpiAccount
	)
	vendor := commonvgpb.Vendor_FEDERAL_BANK

	name, _, _, err := upi.GetEntityDetails(ctx, req.GetActorId(), s.actorClient)
	if err != nil {
		logger.Error(ctx, "error in getting entity details fro actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.String(logger.IFSC_CODE, req.GetBankIfsc()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	upiAccounts, err := s.upiAccountDao.GetByActorId(ctx, req.GetActorId())
	// ignoring record not found because this might be the first time we will be creating upi accounts for the selected bank
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error fetching accounts for the actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// For upi pin set, we take user's consent or aadhaar based flow
	// and call ListAccount rpc with customersConsent field as "Y" and
	// get masked aadhaarNo in the response. We need to store it in the
	// redis for some time, as user will enter the last 4 digits of aadhaar
	// no and it will be validated with this aadhaar no received in this list
	// account response. We need to populate bank details using account id
	if req.GetAccountId() != "" && req.GetIsAadhaarInitiatedFlow() {
		for _, upiAccount := range upiAccounts {
			if upiAccount.GetId() == req.GetAccountId() {
				req.BankIfsc = upiAccount.GetIfscCode()
				req.BankName = upiAccount.GetBankName()
			}
		}
	}

	// TODO(Ashutosh) - Once AadharInitiatedFlow is enable, we can put a check for not passing AccountId in that case.
	accountInfosVendor, err = s.getListAccountFromVG(ctx, vendor, name.ToString(), req.GetBankIfsc(), req.GetActorId(), req.GetDevice(), req.GetAccountId(), req.GetIsAadhaarInitiatedFlow())
	if err != nil {
		logger.Error(ctx, "error in getting list account from vendor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.String(logger.IFSC_CODE, req.GetBankIfsc()), zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	if req.GetIsAadhaarInitiatedFlow() {
		if err = s.cacheTrimmedAadhaarNo(ctx, req.GetActorId(), req.GetAccountId(), accountInfosVendor); err != nil {
			logger.Error(ctx, "error while caching trimmed aadhaar no",
				zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
				zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()),
				zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		// return success since IsAadhaarInitiatedFlow should be used only for caching aadhaar no
		res.Status = rpc.StatusOk()
		return res, nil
	}
	isFiLiteUser := s.upiOnbProcessor.IsFiLiteUser(ctx, req.GetActorId())
	if !isFiLiteUser {
		// fetching internal savings accounts
		accountsListRes, accErr := s.savingsClient.GetAccountsList(ctx, &savings.GetAccountsListRequest{
			Identifier: &savings.GetAccountsListRequest_BulkActorIdentifier{
				BulkActorIdentifier: &savings.BulkActorIdentifier{
					ActorIds:                []string{req.GetActorId()},
					AccountProductOfferings: []accountTypesPb.AccountProductOffering{accountTypesPb.AccountProductOffering_APO_REGULAR, accountTypesPb.AccountProductOffering_APO_NRE, accountTypesPb.AccountProductOffering_APO_NRO},
					PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
				},
			},
		})
		if rpcErr := epifigrpc.RPCError(accountsListRes, accErr); rpcErr != nil {
			if !accountsListRes.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "error fetching savings accounts", zap.Error(rpcErr))
				res.Status = rpc.StatusInternal()
				return res, nil
			}
		}
		savingsAccs = accountsListRes.GetAccounts()
	}

	existingUpiAccounts, accountsToCreate := s.segregateAccounts(req.GetActorId(), accountInfosVendor, upiAccounts, savingsAccs, req.GetBankName())

	// updates accountsToCreate with primary account preference if not set
	s.ensurePrimaryAccountSet(accountsToCreate, upiAccounts)

	createdAccounts, err := s.batchCreateUpiAccounts(ctx, accountsToCreate)
	if err != nil {
		logger.Error(ctx, "error batch creating accounts", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// append existing accounts and created accounts
	accountsFromVendorCall = append(accountsFromVendorCall, existingUpiAccounts...)
	accountsFromVendorCall = append(accountsFromVendorCall, createdAccounts...)

	// fetch only the accounts that are not linked and not in process of linking
	filteredAccounts, err := s.updateAndGetNonLinkedAccounts(ctx, accountsFromVendorCall)
	if err != nil {
		logger.Error(ctx, "error filtering out active and initiated accounts", zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.String(logger.IFSC_CODE, req.GetBankIfsc()), zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.AccountInfos = getAccountInfosFromUpiAccount(filteredAccounts, req.GetUpiAccountType())
	res.Status = rpc.StatusOk()
	return res, nil
}

// cacheTrimmedAadhaarNo:
//   - validates the masked aadhaar number received in response to List Account vg rpc
//   - caches the last 4 digits of aadhaar number in redis (to be used later for validation
//     in flows like upi pin set / reset.)
func (s *Service) cacheTrimmedAadhaarNo(ctx context.Context, actorId, accountId string, accountInfosVendor []*vgUpiPb.UpiAccountInfoVendor) error {
	var (
		maskedAadhaarNo string
	)
	// We can take aadhaar no from any accountInfo
	// as aadhaar no of the user will be same everywhere
	for _, accountInfo := range accountInfosVendor {
		if accountInfo.GetMaskedAadhaarNo() != "" {
			maskedAadhaarNo = accountInfo.GetMaskedAadhaarNo()
			break
		}
	}

	if !isValidMaskedAadhaarNo(maskedAadhaarNo, s.upiDynamicConf.DefaultLengthForAadhaarNumber()) {
		return fmt.Errorf("masked aadhaar number received in ListAccount() Response is invalid: err = %w", rpc.StatusAsError(rpc.StatusInternal()))
	}

	// store the aadhaar no in the redis
	key := getKeyToFetchMaskedAadhaarNo(s.upiDynamicConf.AadhaarNumberPrefix(),
		s.upiDynamicConf.DelimiterForRedisKey(), accountId, actorId)

	if err := s.redisStorage.Set(ctx, key, maskedAadhaarNo[8:12],
		s.upiDynamicConf.ListAccountAadhaarNoTTL()); err != nil {
		return fmt.Errorf("error while setting value in redis cache: err = %w", err)
	}
	return nil
}

// updateAndGetNonLinkedAccounts filters out the account that are either in active state or if there link initiation is in progress
// Also for accounts that are not active or created, we update their status to CREATED
func (s *Service) updateAndGetNonLinkedAccounts(ctx context.Context, upiAccounts []*upiOnboardingPb.UpiAccount) ([]*upiOnboardingPb.UpiAccount, error) {
	var filteredUpiAccounts []*upiOnboardingPb.UpiAccount
	for _, upiAccount := range upiAccounts {
		switch {
		case upiAccount.GetStatus() != upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE &&
			upiAccount.GetStatus() != upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_CREATED &&
			upiAccount.GetStatus() != upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE:
			// if not active, not created and not inactive, move to created state
			err := s.upiAccountDao.UpdateAndChangeStatus(ctx, upiAccount, nil, upiAccount.GetStatus(),
				upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_CREATED)
			if err != nil {
				return nil, fmt.Errorf("error updating account to created state %w", err)
			}
			filteredUpiAccounts = append(filteredUpiAccounts, upiAccount)
		case upiAccount.GetStatus() == upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_CREATED:
			isAccountLinkingInProgress, err := s.checkIfAccountLinkingInProgress(ctx, upiAccount.GetId())
			if err != nil {
				return nil, fmt.Errorf("error in checking if account linking is in progress, accountId:%v %w", upiAccount.GetId(), err)
			}
			if !isAccountLinkingInProgress {
				filteredUpiAccounts = append(filteredUpiAccounts, upiAccount)
			}
		}
	}
	if len(filteredUpiAccounts) == 0 {
		return nil, fmt.Errorf("all the accounts of users are already linked for the given bank : %w", rpc.StatusAsError(allAccountsAlreadyLinkedStatus()))
	}
	return filteredUpiAccounts, nil
}

// checkIfAccountLinkingInProgress checks if the linking of the account is in progress
func (s *Service) checkIfAccountLinkingInProgress(ctx context.Context, accountId string) (bool, error) {
	_, err := s.upiOnbDao.GetByAccountId(ctx, accountId,
		dao.WithActionFilter(upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_LINK),
		dao.WithStatusFilter([]upiOnboardingEnumsPb.UpiOnboardingStatus{
			upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
			upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
			upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_MANUAL_INTERVENTION}))
	switch {
	case storagev2.IsRecordNotFoundError(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("error fetching onboarding details for the account %w", err)
	}
	return true, nil
}

// getAccountInfosFromUpiAccount converts the upi accounts to account infos and returns the account based on upi account type requested
func getAccountInfosFromUpiAccount(upiAccounts []*upiOnboardingPb.UpiAccount, upiAccountType upiOnboardingEnumsPb.UpiAccountType) []*upiOnboardingPb.AccountInfo {
	var upiAccountInfos []*upiOnboardingPb.AccountInfo
	for _, upiAccount := range upiAccounts {
		accountInfo := &upiOnboardingPb.AccountInfo{
			Id:                  upiAccount.GetId(),
			MaskedAccountNumber: upiAccount.GetMaskedAccountNumber(),
			IfscCode:            upiAccount.GetIfscCode(),
			AccountType:         upiAccount.GetAccountType(),
		}
		switch {
		case upiAccountType == upiOnboardingEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_CREDIT_CARD:
			if upiAccount.GetAccountType() == accountsPb.Type_CREDIT {
				upiAccountInfos = append(upiAccountInfos, accountInfo)
			}
		case upiAccountType == upiOnboardingEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT:
			if upiAccount.GetAccountType() != accountsPb.Type_CREDIT {
				upiAccountInfos = append(upiAccountInfos, accountInfo)
			}
		default:
			upiAccountInfos = append(upiAccountInfos, accountInfo)
		}
	}
	return upiAccountInfos
}

// segregateAccounts separates the existing and non-existing UpiAccounts based on the provided UpiAccountInfoVendors.
// For each UpiAccountInfoVendor, it checks if the account is already created in the UpiAccounts slice.
// If the account is not already created, it creates a new UpiAccount and adds it to the nonExistingAccounts slice.
// If the account is already created, it adds it to the existingAccounts slice.
// The existingAccounts and nonExistingAccounts slices are then returned.
func (s *Service) segregateAccounts(
	actorId string,
	upiAccountInfosVendor []*vgUpiPb.UpiAccountInfoVendor,
	upiAccounts []*upiOnboardingPb.UpiAccount,
	savingAccounts []*savings.Account,
	bankName string) (existingAccounts []*upiOnboardingPb.UpiAccount, nonExistingAccounts []*upiOnboardingPb.UpiAccount) {

	for _, accountInfoVendor := range upiAccountInfosVendor {
		isAccountCreated, upiAccount := checkIfAccountAlreadyCreated(accountInfoVendor.GetMaskedAccountNumber(), accountInfoVendor.GetIfscCode(), upiAccounts)
		if !isAccountCreated {
			nonExistingAccounts = append(nonExistingAccounts, createUpiAccountProtoFromVendor(actorId, accountInfoVendor, savingAccounts, bankName))
		} else {
			existingAccounts = append(existingAccounts, upiAccount)
		}
	}

	return existingAccounts, nonExistingAccounts
}

func (s *Service) batchCreateUpiAccounts(ctx context.Context, accountsToCreate []*upiOnboardingPb.UpiAccount) ([]*upiOnboardingPb.UpiAccount, error) {
	if len(accountsToCreate) > 0 {
		createdAccounts, err := s.upiAccountDao.BatchCreate(ctx, accountsToCreate)
		if err != nil {
			return nil, fmt.Errorf("error in batch creation of upi accounts %w", err)
		}
		return createdAccounts, nil
	}
	return nil, nil
}

// ensurePrimaryAccountSet checks if the primary account is set for the user. If not, it sets the primary account based on the APO priority specified in primaryAccountApoPriorityMap
// If the user has multiple accounts with corresponding savings accounts, the account with the highest APO priority will be set as the primary account.
// Relevant account will be set as primary by mutating the account proto in accountsToCreate slice
func (s *Service) ensurePrimaryAccountSet(accountsToCreate, existingUpiAccounts []*upiOnboardingPb.UpiAccount) {
	for _, acc := range existingUpiAccounts {
		if acc.GetAccountPreference() == upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY {
			return
		}
	}

	// Filter out upi accounts of internal savings account
	filteredAccs := lo.Filter(accountsToCreate, func(account *upiOnboardingPb.UpiAccount, index int) bool {
		if account.GetAccountRefId() != "" {
			return true
		}
		return false
	})

	if len(filteredAccs) == 0 {
		return
	}

	// sort by APO priority
	slices.SortFunc(filteredAccs, func(acc1, acc2 *upiOnboardingPb.UpiAccount) int {
		return primaryAccountApoPriorityMap[acc1.GetApo()] - primaryAccountApoPriorityMap[acc2.GetApo()]
	})

	accToSetAsPrimary := filteredAccs[0]
	accToSetAsPrimary.AccountPreference = upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY
}

// createUpiAccountProtoFromVendor converts accountInfo from vendor into upi account proto. If account has a corresponding internal savings account,
// AccountRefId will be populated with the SA id.
func createUpiAccountProtoFromVendor(actorId string, accountInfoVendor *vgUpiPb.UpiAccountInfoVendor, savingsAccounts []*savings.Account, bankName string) *upiOnboardingPb.UpiAccount {
	upiAccountProto := &upiOnboardingPb.UpiAccount{
		ActorId:             actorId,
		AccountRefNumber:    accountInfoVendor.GetAccountRef(),
		MaskedAccountNumber: accountInfoVendor.GetMaskedAccountNumber(),
		IfscCode:            accountInfoVendor.GetIfscCode(),
		Status:              upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_CREATED,
		AccountMetaInfo:     accountInfoVendor.GetControlJson(),
		AccountType:         accountInfoVendor.GetAccountType(),
		Apo:                 accountInfoVendor.GetApo(),
		BankName:            bankName,
		UpiControls:         []upiOnboardingEnumsPb.UpiControl{upiOnboardingEnumsPb.UpiControl_UPI_CONTROL_DOMESTIC_PAYMENTS},
	}

	if accountInfoVendor.IsMobileBankingEnabled {
		upiAccountProto.PinSetStatus = upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET
	} else {
		upiAccountProto.PinSetStatus = upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET
	}

	for _, savingsAccount := range savingsAccounts {
		if savingsAccount.GetIfscCode() == accountInfoVendor.GetIfscCode() &&
			// we can get multiple accounts with same ifsc from NPCI.
			// So in order to identify the internal savings account,
			// we need to compare account nos as well.
			payPkg.IsAccountNumberMatch(upiAccountProto.GetMaskedAccountNumber(), savingsAccount.GetAccountNo()) {

			upiAccountProto.AccountRefId = savingsAccount.GetId()
			upiAccountProto.MaskedAccountNumber = mask.GetMaskedAccountNumber(savingsAccount.GetAccountNo(), "X")
		}
	}
	return upiAccountProto
}

// checkIfAccountAlreadyCreated checks if the account is already created in the upi accounts table by matching the masked account number and ifsc code
// and returns the account
func checkIfAccountAlreadyCreated(maskedAccountNumber, ifscCode string, accounts []*upiOnboardingPb.UpiAccount) (bool, *upiOnboardingPb.UpiAccount) {
	for _, account := range accounts {
		if payPkg.IsAccountNumberMatch(account.GetMaskedAccountNumber(), maskedAccountNumber) && account.GetIfscCode() == ifscCode {
			return true, account
		}
	}
	return false, nil
}

// getListAccountFromVG calls the vg api ListAccount to get the list of accounts for the ifsc and phone number.
// If accountId is provided and the FEATURE_VPA_PREFERENCE_FOR_LIST_ACCOUNT is enabled for the actor, it fetches the VPA for the account and uses it in the request.
// nolint:dupl
func (s *Service) getListAccountFromVG(
	ctx context.Context,
	vendor commonvgpb.Vendor,
	name,
	ifsc,
	actorId string,
	device *upiPb.Device,
	accountId string,
	isAadhaarInitiatedFlow bool) ([]*vgUpiPb.UpiAccountInfoVendor, error) {

	var (
		vgListAccountRes = &vgUpiPb.ListAccountResponse{}
		vpa              = DefaultVpaForListAccount
		vpaErr           error
	)

	defer func() {
		banksListInBytes, err := json.Marshal(getBanksListFromAccountInfos(vgListAccountRes.GetAccountInfoVendor()))
		if err != nil {
			logger.Error(ctx, "error while marshalling bankInfos into json",
				zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return
		}
		banksListInJson := string(banksListInBytes)
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), upiEvent.NewUpiBanksFetchEvent(actorId, vgListAccountRes.GetStatus().String(), banksListInJson))
	}()

	if accountId != "" && s.upiOnbProcessor.IsFeatureEnabledForActor(ctx, actorId, typesPb.Feature_FEATURE_VPA_PREFERENCE_FOR_LIST_ACCOUNT) {
		vpa, vpaErr = s.getVpaForAccountId(ctx, accountId)
		if vpaErr != nil {
			logger.Error(ctx, "error in getting vpa for account", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(vpaErr))
			return nil, fmt.Errorf("error in getting vpa for account %w", vpaErr)
		}
		// TODO (Ashutosh) - Remove this log line and isVPAEpifiFederal code when we rollout further.
		isVPAEpifiFederal := vpa == DefaultVpaForListAccount
		logger.Info(ctx, "feature is enabled for user:", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.FEATURE, typesPb.Feature_FEATURE_VPA_PREFERENCE_FOR_LIST_ACCOUNT.String()), zap.Bool("isVPAEpifiFederal", isVPAEpifiFederal))

	}

	listAccountRequest, err := s.buildListAccountRequest(vendor, name, ifsc, device, vpa, isAadhaarInitiatedFlow, actorId)
	if err != nil {
		return nil, fmt.Errorf("error in building list account vg request %w", err)
	}

	// creating UpiRequestLog entry for the vendor request
	if err = s.upiOnbProcessor.InsertRequestLogs(ctx, &upiOnboardingPb.UpiRequestLog{
		ActorId:     actorId,
		Vendor:      vendor,
		Status:      upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED,
		ApiType:     upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_LIST_ACCOUNT,
		VendorReqId: listAccountRequest.GetTxnHeader().GetTransactionId(),
	}); err != nil {
		return nil, fmt.Errorf("error creating entry in upi Request Logs for the Upi Account %w", err)
	}

	vgListAccountRes, err = s.vgUpiClient.ListAccount(ctx, listAccountRequest)
	switch {
	case err != nil:
		addUpiRequestLogsForListAccountFn := func(ctx context.Context) {
			if err = s.upiOnbProcessor.UpdateRequestLogsForVendorReqId(ctx, listAccountRequest.GetTxnHeader().GetTransactionId(), upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_FAILED, &upiOnboardingPb.DetailedStatusMetadata{
				RawStatusCode:        vgListAccountRes.GetRawResponseCode(),
				RawStatusDescription: vgListAccountRes.GetRawStatusDescription(),
				Category:             upiOnboardingEnumsPb.DetailedStatusCategory_DETAILED_STATUS_CATEGORY_SYSTEM_ERROR,
			}); err != nil {
				logger.Error(ctx, "error in updating UpiRequestLog", zap.String(logger.REQUEST_ID, listAccountRequest.GetTxnHeader().GetTransactionId()), zap.Error(err))
			}
		}
		goroutine.Run(ctx, 5*time.Second, addUpiRequestLogsForListAccountFn)

		return nil, fmt.Errorf("error in list account vg call %w", err)
	case vgListAccountRes.GetStatus().GetCode() == uint32(vgUpiPb.ListAccountResponse_NO_ACCOUNT_EXIST):
		addUpiRequestLogsForListAccountFn := func(ctx context.Context) {
			if err := s.upiOnbProcessor.UpdateRequestLogsForVendorReqId(ctx, listAccountRequest.GetTxnHeader().GetTransactionId(), upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_FAILED, &upiOnboardingPb.DetailedStatusMetadata{
				RawStatusCode:        vgListAccountRes.GetRawResponseCode(),
				RawStatusDescription: vgListAccountRes.GetRawStatusDescription(),
				Category:             upiOnboardingEnumsPb.DetailedStatusCategory_DETAILED_STATUS_CATEGORY_TECHNICAL_DECLINE,
			}); err != nil {
				logger.Error(ctx, "error in updating UpiRequestLog", zap.String(logger.REQUEST_ID, listAccountRequest.GetTxnHeader().GetTransactionId()), zap.Error(err))
			}
		}
		goroutine.Run(ctx, 5*time.Second, addUpiRequestLogsForListAccountFn)

		return nil, fmt.Errorf(" no account found for the user %w", rpc.StatusAsError(rpc.StatusRecordNotFound()))
	case !vgListAccountRes.GetStatus().IsSuccess():
		addUpiRequestLogsForListAccountFn := func(ctx context.Context) {
			if err := s.upiOnbProcessor.UpdateRequestLogsForVendorReqId(ctx, listAccountRequest.GetTxnHeader().GetTransactionId(), upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_FAILED, &upiOnboardingPb.DetailedStatusMetadata{
				RawStatusCode:        vgListAccountRes.GetRawResponseCode(),
				RawStatusDescription: vgListAccountRes.GetRawStatusDescription(),
				Category:             upiOnboardingEnumsPb.DetailedStatusCategory_DETAILED_STATUS_CATEGORY_TECHNICAL_DECLINE,
			}); err != nil {
				logger.Error(ctx, "error in updating UpiRequestLog", zap.String(logger.REQUEST_ID, listAccountRequest.GetTxnHeader().GetTransactionId()), zap.Error(err))
			}
		}
		goroutine.Run(ctx, 5*time.Second, addUpiRequestLogsForListAccountFn)

		return nil, fmt.Errorf("received non-success status code from vendor %w", rpc.StatusAsError(rpc.StatusInternal()))
	}

	addUpiRequestLogsForListAccountFn := func(ctx context.Context) {
		if err := s.upiOnbProcessor.UpdateRequestLogsForVendorReqId(ctx, listAccountRequest.GetTxnHeader().GetTransactionId(), upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS, &upiOnboardingPb.DetailedStatusMetadata{
			RawStatusCode:        vgListAccountRes.GetRawResponseCode(),
			RawStatusDescription: vgListAccountRes.GetRawStatusDescription(),
		}); err != nil {
			logger.Error(ctx, "error in updating UpiRequestLog", zap.String(logger.REQUEST_ID, listAccountRequest.GetTxnHeader().GetTransactionId()), zap.Error(err))
		}
	}
	goroutine.Run(ctx, 5*time.Second, addUpiRequestLogsForListAccountFn)

	return vgListAccountRes.GetAccountInfoVendor(), nil
}

// isValidMaskedAadhaarNo validates if the masked aadhaar no recieved from
// vendor is in expected format or not
func isValidMaskedAadhaarNo(maskedAadhaarNo string, defaultLengthForAadhaarNumber int) bool {
	if len(maskedAadhaarNo) != defaultLengthForAadhaarNumber {
		return false
	}

	for idx, ch := range maskedAadhaarNo {
		if idx >= 8 && !(ch >= '0' && ch <= '9') {
			return false
		}
	}
	return true
}

// buildListAccountRequest builds the ListAccountRequest for calling the vg api
func (s *Service) buildListAccountRequest(
	vendorBank commonvgpb.Vendor,
	name,
	ifsc string,
	device *upiPb.Device,
	vpa string,
	isAadhaarInitiatedFlow bool,
	actorId string) (*vgUpiPb.ListAccountRequest, error) {

	txnHeader := &vgUpiPb.TransactionHeader{}
	err := pkgUpi.PopulateTransactionHeader(txnHeader, vendorBank, upi.Remarks_List_Account)
	if err != nil {
		return nil, fmt.Errorf("error in populating txn header %w", err)
	}

	return &vgUpiPb.ListAccountRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: vendorBank,
		},
		TxnHeader: txnHeader,
		Payer: &upiPb.Customer{
			Device:         device,
			Type:           upiPb.CustomerType_PERSON,
			Name:           name,
			MCC:            upi.CustomerMCC,
			PaymentAddress: vpa,
			AccountDetails: &upiPb.CustomerAccountDetails{
				Ifsc: ifsc,
			},
			AadhaarConsent: isAadhaarInitiatedFlow,
		},
		ActorId: actorId,
	}, nil
}

// getBanksList generates the list of banks using accountInfos
// each bank will be represented by first 4 letters of their IFSC for our use case
func getBanksListFromAccountInfos(accountInfos []*vgUpiPb.UpiAccountInfoVendor) []string {
	if accountInfos == nil {
		return nil
	}
	banksList := make([]string, 0)
	for _, accountInfo := range accountInfos {
		banksList = append(banksList, accountInfo.GetIfscCode()[0:4])
	}
	return banksList
}

// getVpaForAccountId fetches payment instrument by account ID and returns its VPA if exists else returns default VPA
func (s *Service) getVpaForAccountId(ctx context.Context, accountId string) (string, error) {
	piByAccountId, err := s.accountPiRelationClient.GetPiByAccountId(ctx, &piAccPb.GetPiByAccountIdRequest{
		AccountId: accountId,
		PiTypes:   []paymentinstrument.PaymentInstrumentType{paymentinstrument.PaymentInstrumentType_UPI},
	})

	switch {
	case err != nil || !piByAccountId.GetStatus().IsSuccess():
		return "", fmt.Errorf("error fetching PI of UPI type for account ID %s %w", accountId, err)
	case len(piByAccountId.GetPaymentInstruments()) == 0:
		return DefaultVpaForListAccount, nil
	}
	pi := piByAccountId.GetPaymentInstruments()[0]
	if pi.GetUpi().GetVpa() == "" {
		return DefaultVpaForListAccount, nil
	}
	return pi.GetUpi().GetVpa(), nil
}
