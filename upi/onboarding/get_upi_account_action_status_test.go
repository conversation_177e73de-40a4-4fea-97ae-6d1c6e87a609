package onboarding_test

import (
	"context"
	"testing"

	"github.com/golang/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func TestService_GetUpiAccountsActionStatus(t *testing.T) {
	svc, mocks, _, ctrFinishFun := getUpiServiceWithMocks(t)
	ctrFinishFun()

	type mockGetWorkflowStatus struct {
		enable bool
		req    *celestialPb.GetWorkflowStatusRequest
		res    *celestialPb.GetWorkflowStatusResponse
		err    error
	}

	type mockGetByClientReqId struct {
		enable       bool
		clientReqId  string
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	tests := []struct {
		name                  string
		req                   *upiOnboardingPb.GetUpiAccountsActionStatusRequest
		mockGetWorkflowStatus []mockGetWorkflowStatus
		mockGetByClientReqId  []mockGetByClientReqId
		want                  *upiOnboardingPb.GetUpiAccountsActionStatusResponse
		wantErr               bool
	}{
		{
			name: "successfully fetched linking status",
			req: &upiOnboardingPb.GetUpiAccountsActionStatusRequest{
				ClientIds: []*celestialPb.ClientReqId{
					{
						Id:     "client-req-id-1",
						Client: workflowPb.Client_USER_APP,
					},
					{
						Id:     "client-req-id-2",
						Client: workflowPb.Client_USER_APP,
					},
				},
			},
			mockGetWorkflowStatus: []mockGetWorkflowStatus{
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-1",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusOk(),
						WorkflowRequest: &celestialPb.WorkflowRequest{
							Status: stagePb.Status_SUCCESSFUL,
							Stage:  workflowPb.Stage_NOTIFICATION,
							Type:   workflowPb.Type_LINK_UPI_ACCOUNT,
						},
					},
				},
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-2",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusOk(),
						WorkflowRequest: &celestialPb.WorkflowRequest{
							Status: stagePb.Status_SUCCESSFUL,
							Stage:  workflowPb.Stage_NOTIFICATION,
							Type:   workflowPb.Type_LINK_UPI_ACCOUNT,
						},
					},
				},
			},
			mockGetByClientReqId: []mockGetByClientReqId{
				{
					enable:      true,
					clientReqId: "client-req-id-1",
					upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: "account-id-1",
						Vpa:       "vpa-1",
					},
				},
				{
					enable:      true,
					clientReqId: "client-req-id-2",
					upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: "account-id-2",
						Vpa:       "vpa-2",
					},
				},
			},
			want: &upiOnboardingPb.GetUpiAccountsActionStatusResponse{
				Status: rpcPb.StatusOk(),
				ClientReqIdActionInfoMap: map[string]*upiOnboardingPb.UpiAccountActionInfo{
					"client-req-id-1": &upiOnboardingPb.UpiAccountActionInfo{
						AccountId: "account-id-1",
						Status:    upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
						Vpa:       "vpa-1",
					},
					"client-req-id-2": &upiOnboardingPb.UpiAccountActionInfo{
						AccountId: "account-id-2",
						Status:    upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
						Vpa:       "vpa-2",
					},
				},
			},
		},
		{
			name: "failed to fetched linking status as workflow was not found for given client req id",
			req: &upiOnboardingPb.GetUpiAccountsActionStatusRequest{
				ClientIds: []*celestialPb.ClientReqId{
					{
						Id:     "client-req-id-1",
						Client: workflowPb.Client_USER_APP,
					},
					{
						Id:     "client-req-id-2",
						Client: workflowPb.Client_USER_APP,
					},
				},
			},
			mockGetWorkflowStatus: []mockGetWorkflowStatus{
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-1",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusRecordNotFound(),
					},
				},
			},
			want: &upiOnboardingPb.GetUpiAccountsActionStatusResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "failed to fetched linking status because get workflow status rpc returned non success response",
			req: &upiOnboardingPb.GetUpiAccountsActionStatusRequest{
				ClientIds: []*celestialPb.ClientReqId{
					{
						Id:     "client-req-id-1",
						Client: workflowPb.Client_USER_APP,
					},
					{
						Id:     "client-req-id-2",
						Client: workflowPb.Client_USER_APP,
					},
				},
			},
			mockGetWorkflowStatus: []mockGetWorkflowStatus{
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-1",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusInternal(),
					},
				},
			},
			want: &upiOnboardingPb.GetUpiAccountsActionStatusResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "failed to fetch linking status as onb details dao call returned record not found",
			req: &upiOnboardingPb.GetUpiAccountsActionStatusRequest{
				ClientIds: []*celestialPb.ClientReqId{
					{
						Id:     "client-req-id-1",
						Client: workflowPb.Client_USER_APP,
					},
					{
						Id:     "client-req-id-2",
						Client: workflowPb.Client_USER_APP,
					},
				},
			},
			mockGetWorkflowStatus: []mockGetWorkflowStatus{
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-1",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusOk(),
						WorkflowRequest: &celestialPb.WorkflowRequest{
							Status: stagePb.Status_SUCCESSFUL,
							Stage:  workflowPb.Stage_NOTIFICATION,
							Type:   workflowPb.Type_LINK_UPI_ACCOUNT,
						},
					},
				},
			},
			mockGetByClientReqId: []mockGetByClientReqId{
				{
					enable:      true,
					clientReqId: "client-req-id-1",
					upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: "account-id-1",
						Vpa:       "vpa-1",
					},
					err: epifierrors.ErrRecordNotFound,
				},
			},
			want: &upiOnboardingPb.GetUpiAccountsActionStatusResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "failed to fetch linking status as onb details dao call returned invalid argument",
			req: &upiOnboardingPb.GetUpiAccountsActionStatusRequest{
				ClientIds: []*celestialPb.ClientReqId{
					{
						Id:     "client-req-id-1",
						Client: workflowPb.Client_USER_APP,
					},
					{
						Id:     "client-req-id-2",
						Client: workflowPb.Client_USER_APP,
					},
				},
			},
			mockGetWorkflowStatus: []mockGetWorkflowStatus{
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-1",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusOk(),
						WorkflowRequest: &celestialPb.WorkflowRequest{
							Status: stagePb.Status_SUCCESSFUL,
							Stage:  workflowPb.Stage_NOTIFICATION,
							Type:   workflowPb.Type_LINK_UPI_ACCOUNT,
						},
					},
				},
			},
			mockGetByClientReqId: []mockGetByClientReqId{
				{
					enable:      true,
					clientReqId: "client-req-id-1",
					upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: "account-id-1",
						Vpa:       "vpa-1",
					},
					err: epifierrors.ErrInvalidArgument,
				},
			},
			want: &upiOnboardingPb.GetUpiAccountsActionStatusResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
		},
		{
			name: "successfully fetched linking status but workflow not found in notification stage.",
			req: &upiOnboardingPb.GetUpiAccountsActionStatusRequest{
				ClientIds: []*celestialPb.ClientReqId{
					{
						Id:     "client-req-id-1",
						Client: workflowPb.Client_USER_APP,
					},
					{
						Id:     "client-req-id-2",
						Client: workflowPb.Client_USER_APP,
					},
				},
			},
			mockGetByClientReqId: []mockGetByClientReqId{
				{
					enable:      true,
					clientReqId: "client-req-id-1",
					upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: "account-id-1",
						Vpa:       "vpa-1",
					},
				},
				{
					enable:      true,
					clientReqId: "client-req-id-2",
					upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
						AccountId: "account-id-2",
						Vpa:       "vpa-2",
					},
				},
			},
			mockGetWorkflowStatus: []mockGetWorkflowStatus{
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-1",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusOk(),
						WorkflowRequest: &celestialPb.WorkflowRequest{
							Status: stagePb.Status_SUCCESSFUL,
							Stage:  workflowPb.Stage_ACTIVATION,
						},
					},
				},
				{
					enable: true,
					req: &celestialPb.GetWorkflowStatusRequest{
						Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
							ClientRequestId: &celestialPb.ClientReqId{
								Id:     "client-req-id-2",
								Client: workflowPb.Client_USER_APP,
							},
						},
					},
					res: &celestialPb.GetWorkflowStatusResponse{
						Status: rpcPb.StatusOk(),
						WorkflowRequest: &celestialPb.WorkflowRequest{
							Status: stagePb.Status_SUCCESSFUL,
							Stage:  workflowPb.Stage_ACTIVATION,
						},
					},
				},
			},
			want: &upiOnboardingPb.GetUpiAccountsActionStatusResponse{
				Status: rpcPb.StatusOk(),
				ClientReqIdActionInfoMap: map[string]*upiOnboardingPb.UpiAccountActionInfo{
					"client-req-id-1": &upiOnboardingPb.UpiAccountActionInfo{
						Status: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
					},
					"client-req-id-2": &upiOnboardingPb.UpiAccountActionInfo{
						Status: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for _, mockGetWfStatus := range tt.mockGetWorkflowStatus {
				if mockGetWfStatus.enable {
					mocks.mockCelestialClient.EXPECT().GetWorkflowStatus(
						context.Background(),
						mockGetWfStatus.req,
					).Return(mockGetWfStatus.res, mockGetWfStatus.err)
				}
			}
			for _, mockGetByReqId := range tt.mockGetByClientReqId {
				if mockGetByReqId.enable {
					mocks.mockUpiOnbDao.EXPECT().GetByClientRequestId(
						context.Background(),
						mockGetByReqId.clientReqId,
					).Return(mockGetByReqId.upiOnbDetail, mockGetByReqId.err)
				}
			}
			got, err := svc.GetUpiAccountsActionStatus(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUpiAccountsActionStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetUpiAccountsActionStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}
