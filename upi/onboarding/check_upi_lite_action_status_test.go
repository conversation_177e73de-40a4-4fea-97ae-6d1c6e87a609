package onboarding_test

import (
	"context"
	"testing"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
)

func TestService_CheckUpiLiteActionStatus(t *testing.T) {
	svc, mocks, _, ctrFinishFun := getUpiServiceWithMocks(t)
	ctrFinishFun()

	paymentDetails := &orderPb.PaymentDetails{
		PiFrom:          "pi-1",
		PiTo:            "pi-2",
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Mcc:             "1234",
	}

	marshalledPayload, _ := protojson.Marshal(&orderPb.P2PFundTransfer{PaymentDetails: paymentDetails})

	type mockGetWorkflowStatus struct {
		enable bool
		req    *celestialPb.GetWorkflowStatusRequest
		want   *celestialPb.GetWorkflowStatusResponse
		err    error
	}

	type mockGetByClientRequestId struct {
		enable      bool
		clientReqID string
		want        *upiOnboardingPb.UpiOnboardingDetail
		err         error
	}

	type mockGetOrder struct {
		enable bool
		req    *orderPb.GetOrderRequest
		want   *orderPb.GetOrderResponse
		err    error
	}

	type mockGetPIsByIds struct {
		enable bool
		req    *piPb.GetPIsByIdsRequest
		want   *piPb.GetPIsByIdsResponse
		err    error
	}

	tests := []struct {
		name                     string
		req                      *upiOnboardingPb.CheckUpiLiteActionStatusRequest
		mockGetWorkflowStatus    mockGetWorkflowStatus
		mockGetByClientRequestId mockGetByClientRequestId
		mockGetOrder             mockGetOrder
		mockGetPIsByIds          mockGetPIsByIds
		want                     *upiOnboardingPb.CheckUpiLiteActionStatusResponse
		wantErr                  bool
	}{
		{
			name: "successful fetch of workflow status with next action as InitiatePayment",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_BLOCKED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLitePaymentOrchestration),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId:      "order-id",
							AccountRefId: "account-ref-id",
						},
					},
				},
				err: nil,
			},

			mockGetOrder: mockGetOrder{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_OrderId{
						OrderId: "order-id",
					},
				},
				want: &orderPb.GetOrderResponse{
					Status: rpcPb.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						Workflow:     orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						OrderPayload: marshalledPayload,
					},
				},
			},

			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{"pi-1", "pi-2"},
				},
				want: &piPb.GetPIsByIdsResponse{
					Status: rpcPb.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id: "pi-2",
						},
						{
							Id: "pi-1",
						},
					},
				},
			},

			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusOk(),
				NextAction: &upiOnboardingPb.CheckUpiLiteActionStatusResponse_InitiatePayment{
					InitiatePayment: &upiOnboardingPb.InitiatePayment{
						OrderId:        "order-id",
						PayerAccountId: "account-ref-id",
						PaymentDetails: paymentDetails,
						PayerPi: &piPb.PaymentInstrument{
							Id: "pi-1",
						},
						PayeePi: &piPb.PaymentInstrument{
							Id: "pi-2",
						},
					},
				},
			},
		},
		{
			name: "successful fetch of workflow status with next action as SyncUpiLite",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_SUCCESSFUL,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLiteActionNotification),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},
			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusOk(),
				NextAction: &upiOnboardingPb.CheckUpiLiteActionStatusResponse_SyncUpiLite{
					SyncUpiLite: &upiOnboardingPb.SyncUpiLite{
						AccountId: "account-id",
					},
				},
			},
		},
		{
			name: "successful fetch of workflow status with next action as PollWorkflow (workflow in IN_PROGRESS state)",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_INITIATED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLiteActionNotification),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
						CreatedAt: timestampPb.Now(),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},
			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusOk(),
				NextAction: &upiOnboardingPb.CheckUpiLiteActionStatusResponse_PollWorkflow{
					PollWorkflow: &upiOnboardingPb.PollWorkflow{
						RetryTimer: &durationpb.Duration{
							Seconds: 4,
							Nanos:   0,
						},
					},
				},
			},
		},
		{
			name: "successful fetch of workflow status with next action as PollWorkflow (workflow in CREATED state)",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_CREATED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLiteActionNotification),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
						CreatedAt: timestampPb.Now(),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},
			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusOk(),
				NextAction: &upiOnboardingPb.CheckUpiLiteActionStatusResponse_PollWorkflow{
					PollWorkflow: &upiOnboardingPb.PollWorkflow{
						RetryTimer: &durationpb.Duration{
							Seconds: 4,
							Nanos:   0,
						},
					},
				},
			},
		},
		{
			name: "successful fetch of workflow status with PollingTimeout being over",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_INITIATED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLiteActionNotification),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},
			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusOk(),
			},
		},
		{
			name: "error while fetching workflow details",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				err: epifierrors.ErrContextCanceled,
			},

			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "workflow is in unexpected state",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_STATUS_UNSPECIFIED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLitePaymentOrchestration),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},
			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "error while fetching order for the order id",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_BLOCKED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLitePaymentOrchestration),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},

			mockGetOrder: mockGetOrder{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_OrderId{
						OrderId: "order-id",
					},
				},
				err: epifierrors.ErrRecordNotFound,
			},
			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "error while fetching Pis",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_BLOCKED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLitePaymentOrchestration),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},

			mockGetOrder: mockGetOrder{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_OrderId{
						OrderId: "order-id",
					},
				},
				want: &orderPb.GetOrderResponse{
					Status: rpcPb.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						Workflow:     orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						OrderPayload: marshalledPayload,
					},
				},
			},

			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{"pi-1", "pi-2"},
				},
				want: &piPb.GetPIsByIdsResponse{
					Status: rpcPb.StatusInternal(),
				},
			},

			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "payee pi not found",
			req: &upiOnboardingPb.CheckUpiLiteActionStatusRequest{
				ClientReqId: clientReqId,
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				want: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
					WorkflowRequest: &celestialPb.WorkflowRequest{
						Status:    stagePb.Status_BLOCKED,
						StageEnum: celestialPkg.GetStageEnumFromStage(upiNs.UpiLitePaymentOrchestration),
						TypeEnum:  celestialPkg.GetTypeEnumFromWorkflowType(upiNs.ActivateUpiLite),
					},
				},
			},

			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqID: clientReqId.GetId(),
				want: &upiOnboardingPb.UpiOnboardingDetail{
					AccountId: "account-id",
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
						ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
							OrderId: "order-id",
						},
					},
				},
				err: nil,
			},

			mockGetOrder: mockGetOrder{
				enable: true,
				req: &orderPb.GetOrderRequest{
					Identifier: &orderPb.GetOrderRequest_OrderId{
						OrderId: "order-id",
					},
				},
				want: &orderPb.GetOrderResponse{
					Status: rpcPb.StatusOk(),
					Order: &orderPb.Order{
						Id:           "order-id",
						Workflow:     orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						OrderPayload: marshalledPayload,
					},
				},
			},

			mockGetPIsByIds: mockGetPIsByIds{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{"pi-1", "pi-2"},
				},
				want: &piPb.GetPIsByIdsResponse{
					Status: rpcPb.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id: "pi-1",
						},
					},
				},
			},

			want: &upiOnboardingPb.CheckUpiLiteActionStatusResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetWorkflowStatus.enable {
				mocks.mockCelestialClient.EXPECT().GetWorkflowStatus(
					context.Background(),
					tt.mockGetWorkflowStatus.req,
				).Return(tt.mockGetWorkflowStatus.want, tt.mockGetWorkflowStatus.err)
			}

			if tt.mockGetByClientRequestId.enable {
				mocks.mockUpiOnbDao.EXPECT().GetByClientRequestId(context.Background(), tt.mockGetByClientRequestId.clientReqID).
					Return(tt.mockGetByClientRequestId.want, tt.mockGetByClientRequestId.err)
			}

			if tt.mockGetOrder.enable {
				mocks.mockOrderClient.EXPECT().GetOrder(context.Background(), tt.mockGetOrder.req).
					Return(tt.mockGetOrder.want, tt.mockGetOrder.err)
			}

			if tt.mockGetPIsByIds.enable {
				mocks.mockPiClient.EXPECT().GetPIsByIds(context.Background(), tt.mockGetPIsByIds.req).
					Return(tt.mockGetPIsByIds.want, tt.mockGetPIsByIds.err)
			}

			got, err := svc.CheckUpiLiteActionStatus(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckUpiLiteActionStatus() gotErr: %v wantErr: %v", got, tt.wantErr)
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("CheckUpiLiteActionStatus() got :%v want :%v", got, tt.want)
			}
		})
	}

}
