package onboarding_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/golang/protobuf/proto"
	"google.golang.org/protobuf/encoding/protojson"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"

	accountsPb "github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	upiPayloadPb "github.com/epifi/gamma/api/upi/payload"
)

func TestService_InitiateInternationalPaymentsDeactivation(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	svc, mocks, _, ctrFinishFun := getUpiServiceWithMocks(t)
	ctrFinishFun()

	var (
		clientReqIdWf = &workflowPb.ClientReqId{
			Id:     "client-req-id",
			Client: workflowPb.Client_USER_APP,
		}

		clientReqId = &celestialPb.ClientReqId{
			Id:     "client-req-id",
			Client: workflowPb.Client_USER_APP,
		}

		payload, _ = protojson.Marshal(&upiPayloadPb.DeactivateInternationalUpiPaymentsInfo{
			AccountId: "upi-account-id-1",
			ActorId:   "actor-id",
			Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
			Action:    upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS,
			Vpa:       "vpa@upi",
			Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{
				DeactivateInternationalPaymentsPayload: &upiOnboardingPb.DeactivateInternationalPaymentsPayload{},
			},
		})
	)

	type mockGetWorkflowStatus struct {
		enable bool
		req    *celestialPb.GetWorkflowStatusRequest
		res    *celestialPb.GetWorkflowStatusResponse
		err    error
	}

	type mockInitiateWorkflow struct {
		enable bool
		req    *celestialPb.InitiateWorkflowRequest
		res    *celestialPb.InitiateWorkflowResponse
		err    error
	}

	type mockGetUpiAccountById struct {
		enable     bool
		accountId  string
		upiAccount *upiOnboardingPb.UpiAccount
		err        error
	}

	type mockGetPisForAccount struct {
		enable      bool
		accountId   string
		piTypes     []piPb.PaymentInstrumentType
		accountType accountsPb.Type
		pis         []*piPb.PaymentInstrument
		err         error
	}

	tests := []struct {
		name                  string
		req                   *upiOnboardingPb.InitiateInternationalPaymentsDeactivationRequest
		mockGetWorkflowStatus mockGetWorkflowStatus
		mockInitiateWorkflow  mockInitiateWorkflow
		mockGetUpiAccountById mockGetUpiAccountById
		mockGetPisForAccount  mockGetPisForAccount
		want                  *upiOnboardingPb.InitiateInternationalPaymentsDeactivationResponse
		wantErr               bool
	}{
		{
			name: "successfully initiated workflow to deactivate internal payments",
			req: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationRequest{
				ClientId:  clientReqId,
				ActorId:   "actor-id",
				AccountId: "upi-account-id-1",
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				res: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				req: &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     "actor-id",
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(upiNs.DeactivateInternationalUpiPayments),
						Payload:     payload,
						ClientReqId: clientReqIdWf,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &celestialPb.InitiateWorkflowResponse{
					Status:            rpcPb.StatusOk(),
					WorkflowRequestId: "wf-req-id",
				},
			},
			mockGetUpiAccountById: mockGetUpiAccountById{
				enable:     true,
				accountId:  "upi-account-id-1",
				upiAccount: upiAccount,
			},
			mockGetPisForAccount: mockGetPisForAccount{
				enable:      true,
				piTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
				accountId:   "upi-account-id-1",
				accountType: accountsPb.Type_SAVINGS,
				pis: []*piPb.PaymentInstrument{
					{
						Id:    "pi-id",
						State: piPb.PaymentInstrumentState_VERIFIED,
						Type:  piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "vpa@upi",
							},
						},
					},
				},
			},
			want: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationResponse{
				Status: rpcPb.StatusOk(),
			},
		},
		{
			name: "failed to initiate workflow as workflow already exists",
			req: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationRequest{
				ClientId:  clientReqId,
				ActorId:   "actor-id",
				AccountId: "upi-account-id-1",
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				res: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusOk(),
				},
			},
			want: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationResponse{
				Status: rpcPb.StatusAlreadyExists(),
			},
		},
		{
			name: "failed to initiate workflow as get workflow returned non success status",
			req: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationRequest{
				ClientId:  clientReqId,
				ActorId:   "actor-id",
				AccountId: "upi-account-id-1",
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				res: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusInternal(),
				},
			},
			want: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "failed to initiate workflow as initiate workflow rpc returned error",
			req: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationRequest{
				ClientId:  clientReqId,
				ActorId:   "actor-id",
				AccountId: "upi-account-id-1",
			},
			mockGetUpiAccountById: mockGetUpiAccountById{
				enable:     true,
				accountId:  "upi-account-id-1",
				upiAccount: upiAccount,
			},
			mockGetPisForAccount: mockGetPisForAccount{
				enable:      true,
				piTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
				accountId:   "upi-account-id-1",
				accountType: accountsPb.Type_SAVINGS,
				pis: []*piPb.PaymentInstrument{
					{
						Id:    "pi-id",
						State: piPb.PaymentInstrumentState_VERIFIED,
						Type:  piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa: "vpa@upi",
							},
						},
					},
				},
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				res: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				req: &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						ActorId:     "actor-id",
						Version:     workflowPb.Version_V0,
						Type:        celestialPkg.GetTypeEnumFromWorkflowType(upiNs.DeactivateInternationalUpiPayments),
						Payload:     payload,
						ClientReqId: clientReqIdWf,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &celestialPb.InitiateWorkflowResponse{
					Status: rpcPb.StatusInternal(),
				},
			},
			want: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "failed to initiate workflow, faced error while fetching upi account",
			req: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationRequest{
				ClientId:  clientReqId,
				ActorId:   "actor-id",
				AccountId: "upi-account-id-1",
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				res: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
			},
			mockGetUpiAccountById: mockGetUpiAccountById{
				enable:     true,
				accountId:  "upi-account-id-1",
				upiAccount: nil,
				err:        epifierrors.ErrRecordNotFound,
			},
			want: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
		{
			name: "failed to initiate workflow, failed while fetching fetching Pis for the account",
			req: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationRequest{
				ClientId:  clientReqId,
				ActorId:   "actor-id",
				AccountId: "upi-account-id-1",
			},
			mockGetWorkflowStatus: mockGetWorkflowStatus{
				enable: true,
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: clientReqId,
					},
				},
				res: &celestialPb.GetWorkflowStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
			},
			mockGetUpiAccountById: mockGetUpiAccountById{
				enable:     true,
				accountId:  "upi-account-id-1",
				upiAccount: upiAccount,
			},
			mockGetPisForAccount: mockGetPisForAccount{
				enable:      true,
				piTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
				accountId:   "upi-account-id-1",
				accountType: accountsPb.Type_SAVINGS,
				pis:         nil,
				err:         epifierrors.ErrContextCanceled,
			},
			want: &upiOnboardingPb.InitiateInternationalPaymentsDeactivationResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetWorkflowStatus.enable {
				mocks.mockCelestialClient.EXPECT().GetWorkflowStatus(
					context.Background(),
					tt.mockGetWorkflowStatus.req,
				).Return(tt.mockGetWorkflowStatus.res, tt.mockGetWorkflowStatus.err)
			}
			if tt.mockInitiateWorkflow.enable {
				mocks.mockCelestialClient.EXPECT().InitiateWorkflow(
					context.Background(),
					tt.mockInitiateWorkflow.req,
				).Return(tt.mockInitiateWorkflow.res, tt.mockInitiateWorkflow.err)
			}

			if tt.mockGetUpiAccountById.enable {
				mocks.mockUpiAccountDao.EXPECT().GetById(context.Background(), tt.mockGetUpiAccountById.accountId).
					Return(tt.mockGetUpiAccountById.upiAccount, tt.mockGetUpiAccountById.err)
			}

			if tt.mockGetPisForAccount.enable {
				mocks.mockPiProcessor.EXPECT().GetPisForAccount(context.Background(), tt.mockGetPisForAccount.piTypes,
					[]piPb.PaymentInstrumentState{piPb.PaymentInstrumentState_CREATED, piPb.PaymentInstrumentState_VERIFIED},
					tt.mockGetPisForAccount.accountId, tt.mockGetPisForAccount.accountType).
					Return(tt.mockGetPisForAccount.pis, tt.mockGetPisForAccount.err)
			}

			got, err := svc.InitiateInternationalPaymentsDeactivation(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateInternationalPaymentsDeactivation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InitiateInternationalPaymentsDeactivation() got = %v, want %v", got, tt.want)
			}
		})
	}
}
