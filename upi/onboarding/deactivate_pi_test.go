package onboarding_test

import (
	"context"
	"reflect"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/upi/onboarding"
)

func TestService_DeActivatePi(t *testing.T) {
	svc, mocks, _, ctrFinishFun := getUpiServiceWithMocks(t)
	ctrFinishFun()

	type mockGetPisByVpas struct {
		enable bool
		req    *piPb.GetPisByVpasRequest
		res    *piPb.GetPisByVpasResponse
		err    error
	}

	type mockUpdatePi struct {
		enable bool
		req    *piPb.UpdatePiRequest
		res    *piPb.UpdatePiResponse
		err    error
	}

	tests := []struct {
		name             string
		req              *onboarding.DeActivatePiRequest
		mockGetPisByVpas mockGetPisByVpas
		mockUpdatePi     mockUpdatePi
		want             *onboarding.DeActivatePiResponse
		wantErr          bool
	}{
		{
			name: "pi deactivated successfully",
			req: &onboarding.DeActivatePiRequest{
				Vpa: "lucky@fbl",
			},
			mockGetPisByVpas: mockGetPisByVpas{
				enable: true,
				req: &piPb.GetPisByVpasRequest{
					Vpas: []string{"lucky@fbl"},
				},
				res: &piPb.GetPisByVpasResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:    "id-1",
							State: piPb.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:    "id-1",
						State: piPb.PaymentInstrumentState_SUSPENDED,
					},
					UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{
						piPb.PaymentInstrumentFieldMask_STATE,
					},
					Source:       piPb.Source_SYSTEM,
					UpdateReason: "deactivatePi",
				},
				res: &piPb.UpdatePiResponse{
					Status: rpc.StatusOk(),
				},
				err: nil,
			},
			want: &onboarding.DeActivatePiResponse{
				Status: rpc.StatusOk(),
			},
		},
		{
			name: "pi deactivation failed due to error while fetching pi using vpa",
			req: &onboarding.DeActivatePiRequest{
				Vpa: "lucky@fbl",
			},
			mockGetPisByVpas: mockGetPisByVpas{
				enable: true,
				req: &piPb.GetPisByVpasRequest{
					Vpas: []string{"lucky@fbl"},
				},
				res: &piPb.GetPisByVpasResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			want: &onboarding.DeActivatePiResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "pi deactivation failed due to error while updating pi",
			req: &onboarding.DeActivatePiRequest{
				Vpa: "lucky@fbl",
			},
			mockGetPisByVpas: mockGetPisByVpas{
				enable: true,
				req: &piPb.GetPisByVpasRequest{
					Vpas: []string{"lucky@fbl"},
				},
				res: &piPb.GetPisByVpasResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:    "id-1",
							State: piPb.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: &piPb.PaymentInstrument{
						Id:    "id-1",
						State: piPb.PaymentInstrumentState_SUSPENDED,
					},
					UpdateFieldMask: []piPb.PaymentInstrumentFieldMask{
						piPb.PaymentInstrumentFieldMask_STATE,
					},
					Source:       piPb.Source_SYSTEM,
					UpdateReason: "deactivatePi",
				},
				res: &piPb.UpdatePiResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &onboarding.DeActivatePiResponse{
				Status: rpc.StatusInternal(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetPisByVpas.enable {
				mocks.mockPiClient.EXPECT().GetPisByVpas(context.Background(), tt.mockGetPisByVpas.req).
					Return(tt.mockGetPisByVpas.res, tt.mockGetPisByVpas.err)
			}

			if tt.mockUpdatePi.enable {
				mocks.mockPiClient.EXPECT().UpdatePi(context.Background(), tt.mockUpdatePi.req).
					Return(tt.mockUpdatePi.res, tt.mockUpdatePi.err)
			}

			got, err := svc.DeActivatePi(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeActivatePi() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DeActivatePi() got = %v, want %v", got, tt.want)
			}
		})
	}
}
