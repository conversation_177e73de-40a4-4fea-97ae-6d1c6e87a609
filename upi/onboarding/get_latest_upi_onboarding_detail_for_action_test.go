package onboarding_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func TestService_GetLatestUpiOnboardingDetailForAccount(t *testing.T) {
	svc, mocks, _, ctrFinishFun := getUpiServiceWithMocks(t)
	ctrFinishFun()

	type mockGetLatestByAccountId struct {
		enable    bool
		accountId string
		want      *upiOnboardingPb.UpiOnboardingDetail
		err       error
	}

	tests := []struct {
		name                     string
		req                      *upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest
		mockGetLatestByAccountId mockGetLatestByAccountId
		want                     *upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse
		wantErr                  bool
	}{
		{
			name: "successfully fetch latest upi onboarding detail for upi lite linking action",
			req: &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest{
				AccountId:           "account-id",
				UpiOnboardingAction: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
				UpiOnboardingStatus: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id",
				want: &upiOnboardingPb.UpiOnboardingDetail{
					Id:     "upi-onboarding-detail-id",
					Action: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
				},
			},
			want: &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse{
				Status: rpcPb.StatusOk(),
				UpiOnboardingDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Id:     "upi-onboarding-detail-id",
					Action: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
				},
			},
		},
		{
			name: "no successful upi onboarding detail for upi lite delinking action",
			req: &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest{
				AccountId:           "account-id",
				UpiOnboardingAction: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
				UpiOnboardingStatus: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id",
				err:       epifierrors.ErrRecordNotFound,
			},
			want: &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "error while fetching successful upi onboarding detail for upi lite delinking action",
			req: &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountRequest{
				AccountId:           "account-id",
				UpiOnboardingAction: upiOnboardingEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
				UpiOnboardingStatus: upiOnboardingEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL,
			},
			mockGetLatestByAccountId: mockGetLatestByAccountId{
				enable:    true,
				accountId: "account-id",
				err:       epifierrors.ErrResourceExhausted,
			},
			want: &upiOnboardingPb.GetLatestUpiOnboardingDetailForAccountResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetLatestByAccountId.enable {
				mocks.mockUpiOnbDao.EXPECT().GetLatestByAccountId(context.Background(),
					tt.mockGetLatestByAccountId.accountId, gomock.Any()).
					Return(tt.mockGetLatestByAccountId.want, tt.mockGetLatestByAccountId.err)
			}

			got, err := svc.GetLatestUpiOnboardingDetailForAccount(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestUpiOnboardingDetailForAccount() got = %v, want = %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetLatestUpiOnboardingDetailForAccount() \n got = %v, \n want = %v", got, tt.want)
			}
		})
	}
}
