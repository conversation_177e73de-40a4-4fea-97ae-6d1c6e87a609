//go:generate mockgen -source=$PWD/processor.go -destination=$PWD/mocks/mock_processor.go -package=mocks
package upi

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	upiTypes "github.com/epifi/gamma/upi/types"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"strconv"

	accountsPb "github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	beRecurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/upi/helper/paymentinstrument"

	"github.com/google/wire"
	"github.com/samber/lo"
	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	actorpb "github.com/epifi/gamma/api/actor"
	savingsPb "github.com/epifi/gamma/api/savings"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/feature/release"
	upiPkg "github.com/epifi/gamma/pkg/upi"
	"github.com/epifi/gamma/upi/config"
	"github.com/epifi/gamma/upi/dao"
)

var WireSet = wire.NewSet(NewProcessor, wire.Bind(new(UpiOnboardingProcessor), new(*Processor)))

type UpiOnboardingProcessor interface {
	// GetLatestOnboardingDetail gets the latest onboarding details for accountId and statuses
	// If length of status is nil, it fetchese the latest onboarding details independent of status
	GetLatestOnboardingDetail(ctx context.Context, accountId string, statuses []upiOnboardingEnumsPb.UpiOnboardingStatus, actions []upiOnboardingEnumsPb.UpiOnboardingAction) (*onboarding.UpiOnboardingDetail, error)

	// UpdateUpiAccount updates the upi account
	UpdateUpiAccount(
		ctx context.Context,
		account *onboarding.UpiAccount,
		updateMask []onboarding.UpiAccountFieldMask,
		currentStatus, nextStatus upiOnboardingEnumsPb.UpiAccountStatus) error

	// UpsertVpaName updates the vpa name for the actor if already present.
	// Inserts the vpa name for actor if not present already
	UpsertVpaName(ctx context.Context, vpaName string, actorId string) error

	// InsertRequestLogs inserts the request logs in the db
	InsertRequestLogs(ctx context.Context, upiRequestLog *upiOnboardingPb.UpiRequestLog) error

	// UpdateRequestLogsForVendorReqId updates the status and detailed status for the request logs for given vendor req id
	// if the status passed is unspecified will skip status update
	// if the detailed status metadata passed is null will skip detailed status update
	// for detailed status metadata update, will compare if the latest inserted metadata is same as the current one
	// if same then wil update the updated at timestamp in the latest metadata
	// if not, will append the current metadata in the list and update the request logs
	UpdateRequestLogsForVendorReqId(
		ctx context.Context,
		vendorReqId string,
		status upiOnboardingEnumsPb.UpiRequestLogApiStatus,
		detailedStatusMetaData *upiOnboardingPb.DetailedStatusMetadata,
	) error

	// GetTPAPAccount will fetch TPAP account for the user
	GetTPAPAccount(ctx context.Context, accountId string) (*onboarding.UpiAccount, error)

	// GetLatestRequestLogByAccountId fetches the latest upi request log for given account id and filter option
	GetLatestRequestLogByAccountId(ctx context.Context, accountId string, options ...storagev2.FilterOption) (*upiOnboardingPb.UpiRequestLog, error)

	// IsTpapEnabledForActor checks if upi onboarding TPAP is allowed for the given actorId
	IsTpapEnabledForActor(ctx context.Context, actorId string) bool

	// GetTPAPAccountsByIds will fetch TPAP accounts for the user by given account ids
	GetTPAPAccountsByIds(ctx context.Context, accountIds []string) ([]*onboarding.UpiAccount, error)

	// GetPiIdForActiveUpiNumberVpa returns piId for the given upi number vpa if the mapping is active
	GetPiIdForActiveUpiNumberVpa(ctx context.Context, vpa string) (string, error)

	// PublishUpiPinSetEventForNudgeExit publishes events to pay-upi-event-topic which go to a nudge-upi-event-queue
	// and ultimately consumed by nudge-exit-evaluator-queue
	PublishUpiPinSetEventForNudgeExit(ctx context.Context, event *upiPb.UpiEvent) error

	// GetTPAPAccountsForActor - fetches all tpap accounts for given actor with passed filter option
	GetTPAPAccountsForActor(ctx context.Context, actorId string, options ...storagev2.FilterOption) ([]*onboarding.UpiAccount, error)

	// IsMapperEnabledForActor checks if upi Mapper is allowed for the given actorId
	IsMapperEnabledForActor(ctx context.Context, actorId string) bool

	// UpdateInternalAccountToPrimary - updates the account preference to non-primary and makes internal account primary by default
	// If Internal account doesn't exists (Fi Lite user) then makes any random account as primary
	UpdateInternalAccountToPrimary(ctx context.Context, actorId string, upiAccount *upiOnboardingPb.UpiAccount) error

	// IsUpiPinSetUsingAadhaarEnabledForActor checks if user is allowed to set/reset upi pin using aadhaar number
	IsUpiPinSetUsingAadhaarEnabledForActor(ctx context.Context, actorId string) bool

	// IsUpiInternationalPaymentEnabledForActor checks if user is allowed to activate/deactivate international payment
	IsUpiInternationalPaymentEnabledForActor(ctx context.Context, actorId string) bool

	// UpdateAllUpiAccountsPinSetState - updates the pin set status for all upi accounts
	UpdateAllUpiAccountsPinSetState(ctx context.Context, actorId string, fromPinSetStates []upiOnboardingEnumsPb.UpiPinSetStatus, toPinSetState upiOnboardingEnumsPb.UpiPinSetStatus) error

	// UpdateInternalAccountPinSetState - updates the pin set status for internal upi accounts
	UpdateInternalAccountPinSetState(ctx context.Context, actorId string, fromPinSetStates []upiPb.PinSetState, toPinSetState upiPb.PinSetState) error

	// IsCcLinkingEnabledForActor checks if user is allowed to link credit cards for upi payments
	IsCcLinkingEnabledForActor(ctx context.Context, actorId string) bool

	// IsNewUpiHandleAvailableForActor checks if actor is allowed to use new upi handle
	IsNewUpiHandleAvailableForActor(ctx context.Context, actorId string) bool

	// IsNewUpiHandleAssignedToActor checks if new upi handle is assigned to actor or not
	IsNewUpiHandleAssignedToActor(ctx context.Context, actorId string) bool

	// IsUpiLiteEnabledForActor - checks if user is allowed to use upi lite
	IsUpiLiteEnabledForActor(ctx context.Context, actorId string) bool

	// IsFiLiteUser - checks if user is a file lite user or not
	IsFiLiteUser(ctx context.Context, actorId string) bool

	// IsNewAddFundsVpaEnabledForActor - check if add funds through new vpa handle is allowed or not for user
	IsNewAddFundsVpaEnabledForActor(ctx context.Context, actorId string) bool

	// IsFeatureEnabledForActor - checks if the passed feature is enabled for given actor or not
	IsFeatureEnabledForActor(ctx context.Context, actorId string, feature typesPb.Feature) bool

	// IsActiveMandatePresentForAccount : checks if there is an active mandate present for the account
	IsActiveMandatePresentForAccount(ctx context.Context, upiAccount *upiOnboardingPb.UpiAccount) (bool, error)

	// UpdateUpiAccountPinSetState - updates the pin set status for account fetched from provided upi account id
	UpdateUpiAccountPinSetState(ctx context.Context, accountId string, fromPinSetStates []upiOnboardingEnumsPb.UpiPinSetStatus, toPinSetState upiOnboardingEnumsPb.UpiPinSetStatus) error
}

type Processor struct {
	userClient             userPb.UsersClient
	userGrpClient          userGroupPb.GroupClient
	actorClient            actorpb.ActorClient
	upiOnboardingDetailDao dao.UpiOnboardingDetailDao
	upiAccountDao          dao.UpiAccountDao
	actorVpaNameDao        dao.ActorVpaNameMapDao
	upiRequestLogDao       dao.UpiRequestLogDao
	upiNumberPiMappingDao  dao.UpiNumberPiMappingDao
	upiConf                *config.Config
	evaluator              release.IEvaluator
	upiEventSnsPublisher   queue.Publisher
	eventBroker            events.Broker
	upiDao                 dao.UpiDao
	savingsClient          savingsPb.SavingsClient
	userOnboardingClient   onboardingPb.OnboardingClient
	accountPiClient        accountPiPb.AccountPIRelationClient
	piProcessor            paymentinstrument.PIHelper
	mandateDao             dao.MandateDao
	recurringPaymentClient beRecurringPaymentPb.RecurringPaymentServiceClient
}

func NewProcessor(
	userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	actorClient actorpb.ActorClient,
	upiOnboardingDetailDao dao.UpiOnboardingDetailDao,
	upiAccountDao dao.UpiAccountDao,
	actorVpaNameDao dao.ActorVpaNameMapDao,
	upiRequestLogDao dao.UpiRequestLogDao,
	upiConf *config.Config,
	evaluator release.IEvaluator,
	upiNumberPiMappingDao dao.UpiNumberPiMappingDao,
	upiEventSnsPublisher upiTypes.UpiEventSnsPublisher,
	eventBroker events.Broker,
	upiDao dao.UpiDao,
	savingsClient savingsPb.SavingsClient,
	userOnboardingClient onboardingPb.OnboardingClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	piProcessor paymentinstrument.PIHelper,
	mandateDao dao.MandateDao,
	recurringPaymentClient beRecurringPaymentPb.RecurringPaymentServiceClient,
) *Processor {
	return &Processor{
		userClient:             userClient,
		userGrpClient:          userGrpClient,
		actorClient:            actorClient,
		upiOnboardingDetailDao: upiOnboardingDetailDao,
		upiAccountDao:          upiAccountDao,
		actorVpaNameDao:        actorVpaNameDao,
		upiRequestLogDao:       upiRequestLogDao,
		upiConf:                upiConf,
		evaluator:              evaluator,
		upiNumberPiMappingDao:  upiNumberPiMappingDao,
		upiEventSnsPublisher:   upiEventSnsPublisher,
		eventBroker:            eventBroker,
		upiDao:                 upiDao,
		savingsClient:          savingsClient,
		userOnboardingClient:   userOnboardingClient,
		accountPiClient:        accountPiClient,
		piProcessor:            piProcessor,
		mandateDao:             mandateDao,
		recurringPaymentClient: recurringPaymentClient,
	}
}

// UpdateUpiAccount updates the upi account
func (p *Processor) UpdateUpiAccount(
	ctx context.Context,
	account *onboarding.UpiAccount,
	updateMask []onboarding.UpiAccountFieldMask,
	currentStatus, nextStatus upiOnboardingEnumsPb.UpiAccountStatus) error {

	err := p.upiAccountDao.UpdateAndChangeStatus(ctx, account, updateMask, currentStatus, nextStatus)
	if err != nil {
		return err
	}
	return nil
}

// GetLatestOnboardingDetail gets the latest onboarding details for accountId and statuses
// If length of status is nil, it fetchese the latest onboarding details independent of status
func (p *Processor) GetLatestOnboardingDetail(ctx context.Context, accountId string, statuses []upiOnboardingEnumsPb.UpiOnboardingStatus, actions []upiOnboardingEnumsPb.UpiOnboardingAction) (*onboarding.UpiOnboardingDetail, error) {

	var filterOptions []storagev2.FilterOption
	if len(statuses) != 0 {
		filterOptions = append(filterOptions, dao.WithStatusFilter(statuses))
	}

	if len(actions) != 0 {
		filterOptions = append(filterOptions, dao.WithUpiAccountActionFilter(actions))
	}

	upiOnboardingDetail, err := p.upiOnboardingDetailDao.GetLatestByAccountId(ctx, accountId, filterOptions...)
	if err != nil {
		return nil, err
	}
	return upiOnboardingDetail, nil
}

// UpsertVpaName updates the vpa name for the actor if already present.
// Inserts the vpa name for actor if not present already
func (p *Processor) UpsertVpaName(ctx context.Context, vpaName string, actorId string) error {
	_, err := p.actorVpaNameDao.GetByActorId(ctx, actorId)
	switch {
	case err != nil && !storage.IsRecordNotFoundError(err):
		return fmt.Errorf("error fetching vpa name for actorId :%s %w", actorId, err)
	case storage.IsRecordNotFoundError(err):
		// creating the vpa name
		err = p.actorVpaNameDao.Create(ctx, &upiOnboardingPb.ActorVpaNameMap{
			ActorId: actorId,
			VpaName: vpaName,
		})
		if err != nil {
			return fmt.Errorf("error creating vpa name for actorId :%s :%w", actorId, err)
		}
	default:
		// updating the vpa name
		err = p.actorVpaNameDao.UpdateVpaNameForActor(ctx, vpaName, actorId)
		if err != nil {
			return fmt.Errorf("error updating vpa name for actor :%s :%w", actorId, err)
		}
	}
	return nil
}

// InsertRequestLogs inserts the request logs in the db
func (p *Processor) InsertRequestLogs(ctx context.Context, upiRequestLog *upiOnboardingPb.UpiRequestLog) error {
	return p.upiRequestLogDao.Create(ctx, upiRequestLog)
}

func (p *Processor) UpdateRequestLogsForVendorReqId(
	ctx context.Context,
	vendorReqId string,
	status upiOnboardingEnumsPb.UpiRequestLogApiStatus,
	detailedStatusMetaData *upiOnboardingPb.DetailedStatusMetadata) error {
	var (
		updateFiledMasks []upiOnboardingPb.UpiRequestLogFieldMask
	)
	if vendorReqId == "" {
		return fmt.Errorf("vendor ReqId cannot be empty :%w", epifierrors.ErrInvalidArgument)
	}
	if status == upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_UNSPECIFIED && detailedStatusMetaData == nil {
		return fmt.Errorf("status and detailed status bot canmot be empty :%w", epifierrors.ErrInvalidArgument)
	}

	upiRequestLog, err := p.upiRequestLogDao.GetByVendorReqId(ctx, vendorReqId)
	if err != nil {
		return fmt.Errorf("error fetching request logs for vendorReqId :%s :%w", vendorReqId, err)
	}

	if detailedStatusMetaData != nil {
		upiRequestLog.AddDetailedStatusMetadata(detailedStatusMetaData)
		updateFiledMasks = append(updateFiledMasks, upiOnboardingPb.UpiRequestLogFieldMask_UPI_REQUEST_LOG_FIELD_MASK_DETAILED_STATUS)
	}

	return p.upiRequestLogDao.UpdateAndChangeStatus(ctx, upiRequestLog, updateFiledMasks, upiRequestLog.GetStatus(), status)
}

// GetTPAPAccount gets the tpap account by account id
func (p *Processor) GetTPAPAccount(ctx context.Context, accountId string) (*onboarding.UpiAccount, error) {
	upiAccount, err := p.upiAccountDao.GetById(ctx, accountId)
	if err != nil {
		return nil, err
	}
	return upiAccount, nil
}

// GetLatestRequestLogByAccountId fetches the latest upi request log for given account id and filter option
func (p *Processor) GetLatestRequestLogByAccountId(ctx context.Context, accountId string, options ...storagev2.FilterOption) (*upiOnboardingPb.UpiRequestLog, error) {
	upiRequestLog, err := p.upiRequestLogDao.GetLatestByAccountId(ctx, accountId, options...)
	if err != nil {
		return nil, err
	}
	return upiRequestLog, nil
}

func (p *Processor) IsTpapEnabledForActor(ctx context.Context, actorId string) bool {
	constraints := release.NewCommonConstraintData(typesPb.Feature_UPI_TPAP).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if tpap is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return p.IsFiLiteUser(ctx, actorId) || (isAllowed && p.IsNewUpiHandleAssignedToActor(ctx, actorId))
}

// IsNewUpiHandleAssignedToActor checks if new upi handle is assigned to actor or not
func (p *Processor) IsNewUpiHandleAssignedToActor(ctx context.Context, actorId string) bool {
	filterOptions := []storagev2.FilterOption{
		dao.WithAccountTypeFilter([]accountsPb.Type{accountsPb.Type_SAVINGS}),
		dao.WithUpiAccountStatusFilter(
			[]upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE}),
	}
	_, err := p.upiAccountDao.GetByActorId(ctx, actorId, filterOptions...)
	if err != nil {
		if storagev2.IsRecordNotFoundError(err) {
			logger.Debug(ctx, "no upi account found for given actorID", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return false
		}
		logger.Error(ctx, "error while fetching internal active account for actorID", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return true
}

// GetTPAPAccountsByIds gets the tpap accounts by account ids
func (p *Processor) GetTPAPAccountsByIds(ctx context.Context, accountIds []string) ([]*onboarding.UpiAccount, error) {
	upiAccounts, err := p.upiAccountDao.GetByIds(ctx, accountIds)
	if err != nil {
		return nil, err
	}
	return upiAccounts, nil
}

// GetPiIdForActiveUpiNumberVpa returns piId for the given upi number vpa if the mapping is active
func (p *Processor) GetPiIdForActiveUpiNumberVpa(ctx context.Context, vpa string) (string, error) {
	isUpiNumberVpa, err := upiPkg.IsUpiNumberVpa(vpa)
	if err != nil {
		return "", fmt.Errorf("error checking if vpa is a upi number vpa or not :%w", err)
	}
	if !isUpiNumberVpa {
		return "", fmt.Errorf("invalid vpa :%w", epifierrors.ErrInvalidArgument)
	}
	upiNumber, err := upiPkg.GetVpaNameFromVpa(vpa)
	if err != nil {
		return "", fmt.Errorf("error fetching vpa name from vpa :%w", err)
	}
	mapping, err := p.upiNumberPiMappingDao.GetByUpiNumber(ctx, upiNumber)
	if err != nil {
		return "", fmt.Errorf("error fething upi number to pi mapping :%w", err)
	}
	if mapping.GetState() != upiOnboardingEnumsPb.UpiNumberState_UPI_NUMBER_STATE_ACTIVE {
		return "", fmt.Errorf("upi number mapping not in active state :%w", err)
	}
	return mapping.GetPiId(), nil
}

// PublishUpiPinSetEventForNudgeExit publishes events to pay-upi-event-topic which go to a 'nudge-upi-event-queue'
// and ultimately consumed by 'nudge-exit-evaluator-queue'
func (p *Processor) PublishUpiPinSetEventForNudgeExit(ctx context.Context, event *upiPb.UpiEvent) error {
	// accountId should not be empty
	if event.GetAccountId() == "" || event.GetActorId() == "" {
		return fmt.Errorf("insufficient arguments to publish event %w", epifierrors.ErrInvalidArgument)
	}
	// publish the event to 'pay-upi-event-topic'
	if _, err := p.upiEventSnsPublisher.Publish(ctx, event); err != nil {
		return fmt.Errorf("failed to publish to the pay-upi-event-topic %w", err)
	}
	return nil
}

// GetTPAPAccountsForActor - fetches all tpap accounts for given actor with passed filter option
func (p *Processor) GetTPAPAccountsForActor(ctx context.Context, actorId string, options ...storagev2.FilterOption) ([]*onboarding.UpiAccount, error) {
	tpapAccounts, err := p.upiAccountDao.GetByActorId(ctx, actorId, options...)
	if err != nil {
		return nil, err
	}
	return tpapAccounts, nil
}

// IsMapperEnabledForActor checks if upi Mapper is allowed for the given actorId
func (p *Processor) IsMapperEnabledForActor(ctx context.Context, actorId string) bool {
	constraints := release.NewCommonConstraintData(typesPb.Feature_UPI_MAPPER).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if mapper is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed && p.IsNewUpiHandleAssignedToActor(ctx, actorId)
}

func (p *Processor) UpdateInternalAccountToPrimary(ctx context.Context, actorId string, upiAccount *upiOnboardingPb.UpiAccount) error {
	filterOptions := []storagev2.FilterOption{
		dao.WithUpiAccountStatusFilter([]upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE}),
		dao.WithAccountTypeFilter([]accountsPb.Type{accountsPb.Type_SAVINGS}),
	}

	upiAccounts, err := p.upiAccountDao.GetByActorId(ctx, actorId, filterOptions...)
	switch {
	case storagev2.IsRecordNotFoundError(err) || len(upiAccounts) == 0:
		return fmt.Errorf("no record found for the actor %s %w", actorId, rpc.StatusAsError(rpc.StatusRecordNotFound()))
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return fmt.Errorf("error fetching accounts due to invalid Argument passed %s %w", actorId, rpc.StatusAsError(rpc.StatusInvalidArgument()))
	case err != nil:
		return fmt.Errorf("error updating account preference for the actor %s %w", actorId, rpc.StatusAsError(rpc.StatusInternal()))
	}

	internalAccount, randomAccount, err := p.getPrimaryAccountOptions(ctx, upiAccounts)
	if err != nil {
		return fmt.Errorf("error while fetching account options to make primary account, err = %w", err)
	}
	err = storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		updateMask := []upiOnboardingPb.UpiAccountFieldMask{upiOnboardingPb.UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PREFERENCE}
		upiAccount.AccountPreference = upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_UNSPECIFIED
		err = p.upiAccountDao.UpdateAndChangeStatus(txnCtx, upiAccount, updateMask, upiAccount.GetStatus(), upiAccount.GetStatus())
		if err != nil {
			return fmt.Errorf("error updating preference for the account %s %w", upiAccount.GetId(), rpc.StatusAsError(rpc.StatusInternal()))
		}

		accountToMakePrimary := internalAccount
		if accountToMakePrimary == nil {
			accountToMakePrimary = randomAccount
		}

		if accountToMakePrimary != nil {
			accountToMakePrimary.AccountPreference = upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY
			err = p.upiAccountDao.UpdateAndChangeStatus(txnCtx, accountToMakePrimary, updateMask, accountToMakePrimary.GetStatus(), accountToMakePrimary.GetStatus())
			if err != nil {
				return fmt.Errorf("error updating preference for the account %s %w", accountToMakePrimary.GetId(), rpc.StatusAsError(rpc.StatusInternal()))
			}
		}

		return nil
	})

	return err
}

// getPrimaryAccountOptions : finds the internal account and any random account which could be made primary account
//  1. First priority to make Primary account will be internal account
//  2. If Primary account doesn't exists then any random TPAP account will be
//     made primary
func (p *Processor) getPrimaryAccountOptions(ctx context.Context, upiAccounts []*upiOnboardingPb.UpiAccount) (*upiOnboardingPb.UpiAccount, *upiOnboardingPb.UpiAccount, error) {
	var (
		internalAccount, randomAccount *upiOnboardingPb.UpiAccount
	)

	for _, upiAccount := range upiAccounts {
		switch {
		case upiAccount.GetAccountPreference() == upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY:
			continue
		case upiAccount.IsInternal():
			internalAccount = upiAccount
		case randomAccount == nil:
			canMakePrimary, err := p.checkIfAccountHasAnActiveUpiPi(ctx, upiAccount.GetId(), upiAccount.GetAccountType())
			if err != nil {
				return nil, nil, fmt.Errorf("error while checking if account has an active upi pi, account-id = %v, err = %w", upiAccount.GetId(), err)
			}
			if canMakePrimary {
				randomAccount = upiAccount
			}
		}
	}

	return internalAccount, randomAccount, nil
}

// IsUpiPinSetUsingAadhaarEnabledForActor checks if user is allowed to set/reset upi pin using aadhaar number
func (p *Processor) IsUpiPinSetUsingAadhaarEnabledForActor(ctx context.Context, actorId string) bool {
	constraints := release.NewCommonConstraintData(typesPb.Feature_UPI_PIN_SET_USING_AADHAAR).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating upi pin set using aadhaar is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed && p.IsNewUpiHandleAssignedToActor(ctx, actorId)
}

// IsUpiInternationalPaymentEnabledForActor checks if user is allowed to set/reset upi pin using aadhaar number
func (p *Processor) IsUpiInternationalPaymentEnabledForActor(ctx context.Context, actorId string) bool {
	constraints := release.NewCommonConstraintData(typesPb.Feature_UPI_INTERNATIONAL_PAYMENT).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating upi international payments is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed && p.IsNewUpiHandleAssignedToActor(ctx, actorId)
}

// IsCcLinkingEnabledForActor checks if user is allowed to link credit cards for upi payments
func (p *Processor) IsCcLinkingEnabledForActor(ctx context.Context, actorId string) bool {
	constraints := release.NewCommonConstraintData(typesPb.Feature_CC_UPI_LINKING).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if cc linking is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed
}

// UpdateAllUpiAccountsPinSetState - updates the pin set status for all upi accounts
func (p *Processor) UpdateAllUpiAccountsPinSetState(ctx context.Context, actorId string, fromPinSetStates []upiOnboardingEnumsPb.UpiPinSetStatus, toPinSetState upiOnboardingEnumsPb.UpiPinSetStatus) error {
	txnErr := storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		upiAccounts, err := p.upiAccountDao.GetByActorId(txnCtx, actorId, []storagev2.FilterOption{dao.WithUpiAccountStatusFilter(
			[]upiOnboardingEnumsPb.UpiAccountStatus{
				upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
			})}...)
		switch {
		case storagev2.IsRecordNotFoundError(err) || len(upiAccounts) == 0:
			return fmt.Errorf("no record found for the actor %s %w", actorId, rpc.StatusAsError(rpc.StatusRecordNotFound()))
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			return fmt.Errorf("error fetching accounts due to invalid Argument passed %s %w", actorId, rpc.StatusAsError(rpc.StatusInvalidArgument()))
		case err != nil:
			return fmt.Errorf("error updating account preference for the actor %s %w", actorId, rpc.StatusAsError(rpc.StatusInternal()))
		}

		for _, upiAccount := range upiAccounts {
			if lo.Contains(fromPinSetStates, upiAccount.GetPinSetStatus()) {
				upiAccount.PinSetStatus = toPinSetState
				err = p.UpdateUpiAccount(txnCtx, upiAccount, []upiOnboardingPb.UpiAccountFieldMask{
					upiOnboardingPb.UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS,
				}, upiAccount.GetStatus(), upiAccount.GetStatus())
				if err != nil {
					return fmt.Errorf("error in updating status for upiAccount id: %s %w", upiAccount.GetId(), err)
				}
			}
		}
		return nil
	})
	return txnErr
}

// UpdateInternalAccountPinSetState - updates the pin set status for internal upi accounts
func (p *Processor) UpdateInternalAccountPinSetState(ctx context.Context, actorId string, fromPinSetStates []upiPb.PinSetState, toPinSetState upiPb.PinSetState) error {
	savingsAccountRes, err := p.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	switch {
	case err != nil:
		return fmt.Errorf("failed to get savings account detail for actorId: %s %w", actorId, err)
	case savingsAccountRes.GetAccount() == nil:
		return fmt.Errorf("received nil account details actorId: %s %w", actorId, err)
	}

	txnErr := storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		upiAccountInfo, err := p.upiDao.GetAccountInfoByAccountId(txnCtx, savingsAccountRes.GetAccount().GetId())
		switch {
		case errors.Is(err, gormv2.ErrRecordNotFound):
			return fmt.Errorf("no upi account info found for given account id %s", savingsAccountRes.GetAccount().GetId())
		case err != nil:
			return fmt.Errorf("error in getting upi account info by accountId %s %w", savingsAccountRes.GetAccount().GetId(), err)
		}

		if lo.Contains(fromPinSetStates, upiAccountInfo.GetPinSetState()) {
			fromPinSetState := upiAccountInfo.GetPinSetState()
			upiAccountInfo.PinSetState = toPinSetState
			err = p.upiDao.ChangePinSetState(txnCtx, upiAccountInfo, fromPinSetState, []upiPb.UpiAccountInfoFieldMask{upiPb.UpiAccountInfoFieldMask_PIN_SET_STATE})
			if err != nil {
				return fmt.Errorf("error in updating upi account pin set state: %s %w", upiAccountInfo.GetAccountId(), err)
			}
		}
		return nil
	})
	return txnErr
}

// IsNewUpiHandleAvailableForActor checks if user is allowed to use new upi handle
func (p *Processor) IsNewUpiHandleAvailableForActor(ctx context.Context, actorId string) bool {
	// Issue: AppVersionInfo is not populated in context in some cases.
	// Summary: When this method is called in flows initiated via
	// frontend then it is prefilled in it. (at interceptor
	// level)
	// But in other flows context doesn't have appVersionInfo in it.
	// In such cases we need to fetch it from device properties of
	// user and populate in the context manually.
	if epificontext.AppPlatformFromContext(ctx) == commontypes.Platform_PLATFORM_UNSPECIFIED {
		appVersionInfo, err := fetchAppVersionInfoForActor(ctx, actorId, p.userClient)
		if err != nil {
			logger.Error(ctx, "error while fetching appVersionInfo for actor",
				zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))

			return false
		}

		// populate appPlatform and appVersionCode in the context
		ctx = epificontext.CtxWithAppPlatform(ctx, appVersionInfo.GetPlatform())
		ctx = epificontext.CtxWithAppVersionCode(ctx, strconv.Itoa(int(appVersionInfo.GetAppVersionCode())))
	}

	constraints := release.NewCommonConstraintData(typesPb.Feature_NEW_VPA_HANDLE).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if new upi handle is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed
}

// fetchAppVersionInfoForActor: fetches the app platform and version info for actor
func fetchAppVersionInfoForActor(ctx context.Context, actorId string, userClient userPb.UsersClient) (*typesPb.AppVersionInfo, error) {
	getUserDevicePropertiesRes, err := userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId:       actorId,
		PropertyTypes: []typesPb.DeviceProperty{typesPb.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO},
	})
	if err = epifigrpc.RPCError(getUserDevicePropertiesRes, err); err != nil {
		return nil, err
	}

	for _, userDeviceProperty := range getUserDevicePropertiesRes.GetUserDevicePropertyList() {
		if userDeviceProperty.GetDeviceProperty() == typesPb.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO {
			return userDeviceProperty.GetPropertyValue().GetAppVersionInfo(), nil
		}
	}
	return nil, fmt.Errorf("app version info not found for actor %s", actorId)
}

// IsUpiLiteEnabledForActor - checks if user is allowed to use upi lite
func (p *Processor) IsUpiLiteEnabledForActor(ctx context.Context, actorId string) bool {
	constraints := release.NewCommonConstraintData(typesPb.Feature_UPI_LITE).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if upi lite is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed && p.IsNewUpiHandleAssignedToActor(ctx, actorId)
}

// IsFiLiteUser - checks if user is a file lite user or not
func (p *Processor) IsFiLiteUser(ctx context.Context, actorId string) bool {
	getSavingsAccountEssentialsRes, err := p.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	case getSavingsAccountEssentialsRes.GetStatus().IsRecordNotFound():
		return true
	case !getSavingsAccountEssentialsRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non-success code while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return false
}

// IsNewAddFundsVpaEnabledForActor - check if add funds through new vpa handle is allowed or not for user
func (p *Processor) IsNewAddFundsVpaEnabledForActor(ctx context.Context, actorId string) bool {
	constraints := release.NewCommonConstraintData(typesPb.Feature_NEW_ADD_FUNDS_VPA).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if add funds through new vpa handle is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}
	return isAllowed
}

// IsFeatureEnabledForActor - checks if the passed feature is enabled for given actor or not
func (p *Processor) IsFeatureEnabledForActor(ctx context.Context, actorId string, feature typesPb.Feature) bool {
	constraints := release.NewCommonConstraintData(feature).WithActorId(actorId)
	isAllowed, err := p.evaluator.Evaluate(ctx, constraints)
	if err != nil {
		logger.Error(ctx, "error evaluating if feature is allowed or not", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.FEATURE, feature.String()), zap.Error(err))
		return false
	}
	return isAllowed
}

func (p *Processor) checkIfAccountHasAnActiveUpiPi(ctx context.Context, accountId string, accountType accountsPb.Type) (bool, error) {
	res, err := p.accountPiClient.GetPiByAccountId(ctx, &accountPiPb.GetPiByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType, PiTypes: []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
		PiStates: []piPb.PaymentInstrumentState{piPb.PaymentInstrumentState_CREATED, piPb.PaymentInstrumentState_VERIFIED},
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return false, fmt.Errorf("error fetching pis for accountId :%s :%w", accountId, rpcErr)
	}

	for _, paymentInstrument := range res.GetPaymentInstruments() {
		if !paymentInstrument.IsMandateVPA() {
			return true, nil
		}
	}

	return false, nil
}

// IsActiveMandatePresentForAccount : checks if there is an active mandate present for the account
func (p *Processor) IsActiveMandatePresentForAccount(ctx context.Context, upiAccount *upiOnboardingPb.UpiAccount) (bool, error) {
	var (
		recurringPaymentTerminalStatus = []beRecurringPaymentPb.RecurringPaymentState{
			beRecurringPaymentPb.RecurringPaymentState_REVOKED,
			beRecurringPaymentPb.RecurringPaymentState_EXPIRED,
			beRecurringPaymentPb.RecurringPaymentState_COMPLETED,
			beRecurringPaymentPb.RecurringPaymentState_FAILED,
		}
	)

	accountIdToFetchAccountPis := upiAccount.GetId()
	if upiAccount.IsInternal() {
		// for internal TPAP accounts, accountPi relation is created with savings accounts id itself
		// so the same should be used while fetching it.
		accountIdToFetchAccountPis = upiAccount.GetAccountRefId()
	}
	paymentInstruments, err := p.piProcessor.GetPisForAccount(ctx,
		[]piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI}, nil, accountIdToFetchAccountPis, upiAccount.GetAccountType())
	if err != nil {
		return false, fmt.Errorf("failed to fetch pis for account %s %v %w", upiAccount.GetId(), err, rpc.StatusAsError(rpc.StatusInternal()))
	}

	for _, pi := range paymentInstruments {
		if pi.IsMandateVPA() {
			mandate, err := p.mandateDao.GetByUmn(ctx, pi.GetUpi().GetVpa())
			if err != nil {
				return false, fmt.Errorf("failed to fetch mandate by umn %s %v %w", pi.GetUpi().GetVpa(), err, rpc.StatusAsError(rpc.StatusInternal()))
			}

			recurringPaymentResp, err := p.recurringPaymentClient.GetRecurringPaymentById(ctx, &beRecurringPaymentPb.GetRecurringPaymentByIdRequest{
				Id: mandate.GetRecurringPaymentId(),
			})
			if grpcErr := epifigrpc.RPCError(recurringPaymentResp, err); grpcErr != nil {
				return false, fmt.Errorf("failed to fetch recurring payment by id %s %w", mandate.GetRecurringPaymentId(), grpcErr)
			}

			if !lo.Contains(recurringPaymentTerminalStatus, recurringPaymentResp.GetRecurringPayment().GetState()) {
				return true, nil
			}
		}
	}

	return false, nil
}

// UpdateUpiAccountPinSetState - updates the pin set status for account fetched from provided upi account id
func (p *Processor) UpdateUpiAccountPinSetState(ctx context.Context, accountId string, fromPinSetStates []upiOnboardingEnumsPb.UpiPinSetStatus, toPinSetState upiOnboardingEnumsPb.UpiPinSetStatus) error {
	upiAccount, err := p.upiAccountDao.GetById(ctx, accountId)
	switch {
	case errors.Is(err, gormv2.ErrRecordNotFound):
		return fmt.Errorf("no upi account found for given account id %s: %w", accountId, err)
	case err != nil:
		return fmt.Errorf("error in getting upi account by accountId %s: %w", accountId, err)
	}

	if len(fromPinSetStates) == 0 {
		return fmt.Errorf("fromPinSetStates cannot be empty")
	}

	if lo.Contains(fromPinSetStates, upiAccount.GetPinSetStatus()) {
		upiAccount.PinSetStatus = toPinSetState
		err = p.UpdateUpiAccount(ctx, upiAccount, []upiOnboardingPb.UpiAccountFieldMask{
			upiOnboardingPb.UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS,
		}, upiAccount.GetStatus(), upiAccount.GetStatus())
		if err != nil {
			return fmt.Errorf("error in updating status for upiAccount id: %s: %w", upiAccount.GetId(), err)
		}
	}

	return nil
}
