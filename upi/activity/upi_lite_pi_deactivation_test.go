package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"
)

func TestProcessor_UpiLitePiDeactivation(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockGetByClientRequestId struct {
		enable       bool
		clientReqId  string
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	type mockGetUpiLitePi struct {
		enable bool
		lrn    string
		want   *piPb.PaymentInstrument
		err    error
	}

	type mockUpdatePi struct {
		enable bool
		req    *piPb.UpdatePiRequest
		want   *piPb.UpdatePiResponse
		err    error
	}

	tests := []struct {
		name                     string
		req                      *upiActivityPb.UpiLitePiDeactivationRequest
		mockGetByClientRequestId mockGetByClientRequestId
		mockGetUpiLitePi         mockGetUpiLitePi
		mockUpdatePi             mockUpdatePi
		res                      *upiActivityPb.UpiLitePiDeactivationResponse
		wantErr                  bool
		assertErr                func(err error) bool
	}{
		{
			name: "successfully deactivate upi lite PI",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{DeactivateUpiLitePayload: &upiOnboardingPb.DeactivateUpiLitePayload{
						Lrn: "lrn",
					}},
				},
			},
			mockGetUpiLitePi: mockGetUpiLitePi{
				enable: true,
				lrn:    "lrn",
				want:   upiLitePi,
			},

			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: upiLitePi,
					UpdateFieldMask:   []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
					Source:            piPb.Source_SYSTEM,
					UpdateReason:      "upi lite account deletion",
				},
				want: &piPb.UpdatePiResponse{
					Status: rpcPb.StatusOk(),
				},
			},
		},
		{
			name: "transient error while updating upi lite PI",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{DeactivateUpiLitePayload: &upiOnboardingPb.DeactivateUpiLitePayload{
						Lrn: "lrn",
					}},
				},
			},
			mockGetUpiLitePi: mockGetUpiLitePi{
				enable: true,
				lrn:    "lrn",
				want:   upiLitePi,
			},

			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: upiLitePi,
					UpdateFieldMask:   []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
					Source:            piPb.Source_SYSTEM,
					UpdateReason:      "upi lite account deletion",
				},
				want: &piPb.UpdatePiResponse{
					Status: rpcPb.StatusInternal(),
				},
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "permanent error while updating upi lite PI",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{DeactivateUpiLitePayload: &upiOnboardingPb.DeactivateUpiLitePayload{
						Lrn: "lrn",
					}},
				},
			},
			mockGetUpiLitePi: mockGetUpiLitePi{
				enable: true,
				lrn:    "lrn",
				want:   upiLitePi,
			},

			mockUpdatePi: mockUpdatePi{
				enable: true,
				req: &piPb.UpdatePiRequest{
					PaymentInstrument: upiLitePi,
					UpdateFieldMask:   []piPb.PaymentInstrumentFieldMask{piPb.PaymentInstrumentFieldMask_STATE},
					Source:            piPb.Source_SYSTEM,
					UpdateReason:      "upi lite account deletion",
				},
				want: &piPb.UpdatePiResponse{
					Status: rpcPb.StatusInvalidArgument(),
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient failure while deactivating PI as GetUpiLitePi call failed with transient error",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{DeactivateUpiLitePayload: &upiOnboardingPb.DeactivateUpiLitePayload{
						Lrn: "lrn",
					}},
				},
			},
			mockGetUpiLitePi: mockGetUpiLitePi{
				enable: true,
				lrn:    "lrn",
				want:   nil,
				err:    epifierrors.ErrTransient,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "permanent failure while fetching PI as GetUpiLitePi call failed with permanent error",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{DeactivateUpiLitePayload: &upiOnboardingPb.DeactivateUpiLitePayload{
						Lrn: "lrn",
					}},
				},
			},
			mockGetUpiLitePi: mockGetUpiLitePi{
				enable: true,
				lrn:    "lrn",
				want:   upiLitePi,
				err:    epifierrors.ErrPermanent,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "gracefully handling record not found for activation workflow",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{Lrn: "lrn"}},
				},
			},
			mockGetUpiLitePi: mockGetUpiLitePi{
				enable: true,
				lrn:    "lrn",
				want:   upiLitePi,
				err:    epifierrors.ErrRecordNotFound,
			},
		},
		{
			name: "transient failure as GetByClientRequestId() call failed",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrContextCanceled,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "permanent failure as GetByClientRequestId() call failed with InvalidArgument error",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrInvalidArgument,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as GetByClientRequestId() call failed with RecordNotFound error",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrRecordNotFound,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as upi onboarding detail has unexpected action",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as upiOnboardingDetail is already in terminal state",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "lrn",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as lrn is empty",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				Lrn: "",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as client-req-id is empty",
			req: &upiActivityPb.UpiLitePiDeactivationRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "",
				},
				Lrn: "lrn",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByClientRequestId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetByClientRequestId(gomock.Any(), tt.mockGetByClientRequestId.clientReqId).
					Return(tt.mockGetByClientRequestId.upiOnbDetail, tt.mockGetByClientRequestId.err)
			}

			if tt.mockGetUpiLitePi.enable {
				md.piHelper.EXPECT().GetUpiLitePi(gomock.Any(), tt.mockGetUpiLitePi.lrn).
					Return(tt.mockGetUpiLitePi.want, tt.mockGetUpiLitePi.err)
			}

			if tt.mockUpdatePi.enable {
				md.piClient.EXPECT().UpdatePi(gomock.Any(), tt.mockUpdatePi.req).
					Return(tt.mockUpdatePi.want, tt.mockUpdatePi.err)
			}

			if _, err := env.ExecuteActivity(upiNs.UpiLitePiDeactivation, tt.req); (err != nil) != tt.wantErr {
				t.Errorf("UpiLitePiDeactivation() error = %v, wantErr %v", err, tt.wantErr)
			} else if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("UpiLitePiDeactivation() error = %v, assertion failed", err)
				return
			}
			assertTest()
		})
	}
}
