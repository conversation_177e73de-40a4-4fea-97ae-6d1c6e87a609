package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	accountsPb "github.com/epifi/gamma/api/accounts"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
)

// UpiLitePiCreation -
//  1. creates new pi for the upi lite account in the DB
//  2. stores the reference to pi of the upi account for which
//     upi lite account is being activated
//  3. will be uniquely identified by lrn (Lite Reference Number)
//  4. For all the actions on upi lite PI, PI corresponding
//     to pi-ref-id should be used.
func (p *Processor) UpiLitePiCreation(ctx context.Context, req *upiActivityPb.UpiLitePiCreationRequest) (*upiActivityPb.UpiLitePiCreationResponse, error) {
	var (
		res                 = &upiActivityPb.UpiLitePiCreationResponse{}
		lg                  = activity.GetLogger(ctx)
		upiOnboardingDetail *upiOnboardingPb.UpiOnboardingDetail
		upiLitePi           *piPb.PaymentInstrument
		err                 error
	)

	// validation check
	if !validRequestForUpiLitePiCreation(req) {
		lg.Error("mandatory fields couldn't be empty", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.ACCOUNT_ID, req.GetAccountRefId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "one or more mandatory field is empty")
	}

	upiOnboardingDetail, err = p.upiOnbDetailsDao.GetByClientRequestId(ctx, req.GetRequestHeader().GetClientReqId())
	switch {
	case err != nil && storageV2.IsRecordNotFoundError(err):
		lg.Error("no onb details found corresponding to given client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "no onb details found corresponding to given client req id")
	case err != nil && errors.Is(err, epifierrors.ErrInvalidArgument):
		lg.Error("invalid parameter passed while fetching onb details for clientReqId:", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "invalid parameter passed")
	case err != nil:
		lg.Error("failed to fetch upi onb details corresponding to given client req id", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch upi onb details corresponding to given client req id: %s", err))
	case upiOnboardingDetail.GetAction() != upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE:
		lg.Error("unexpected-action type received", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.UPI_ONBOARDING_DETAILS_ACTION_TYPE, upiOnboardingDetail.GetAction().String()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid action type received: expected = %s, got = %s",
			upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE.String(), upiOnboardingDetail.GetAction().String()))
	case upiOnboardingDetail.GetStatus() == upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL:
		lg.Info("upi onboarding details already in successful state", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return res, nil
	case isStatusTerminal(upiOnboardingDetail.GetStatus()):
		lg.Error("upi onboarding status already in terminal state", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.STATUS, upiOnboardingDetail.GetStatus().String()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "workflow in unexpected state")
	}

	// creation rpc will handle the case,
	// if pi already exists then state will be updated to CREATED state
	if upiLitePi, err = p.piHelper.CreateUpiLitePi(ctx, req.GetAccountRefId(), req.GetLrn()); err != nil {
		lg.Error("error while creating upi lite pi", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.ACCOUNT_ID, req.GetAccountRefId()), zap.Error(err))

		// CreateUpiLitePi decides if the error is transient or permanent
		// hence it is safe to directly return the error
		return nil, err
	}

	// create new account PI
	if _, err = p.piHelper.CreateAccountPi(ctx, &accountPiPb.CreateAccountPIRequest{
		ActorId:     req.GetActorId(),
		AccountId:   req.GetUpiLiteAccountId(),
		AccountType: accountsPb.Type_UPI_LITE,
		PiId:        upiLitePi.GetId(),
	}); err != nil {
		lg.Error("error while creating account pi for the upi lite account",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.ACCOUNT_ID, req.GetAccountRefId()),
			zap.Error(err))

		return nil, err
	}

	return res, nil
}

func validRequestForUpiLitePiCreation(req *upiActivityPb.UpiLitePiCreationRequest) bool {
	return req.GetActorId() != "" && req.GetAccountRefId() != "" && req.GetUpiLiteAccountId() != "" &&
		req.GetLrn() != "" && req.GetRequestHeader().GetClientReqId() != ""
}
