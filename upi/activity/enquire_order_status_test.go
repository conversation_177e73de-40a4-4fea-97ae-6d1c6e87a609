package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"

	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
)

func TestProcessor_EnquireOrderStatus(t *testing.T) {
	act, md, assertTest := newProcessorWithMocks(t)
	defer assertTest()
	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(act)

	type mockGetByClientRequestId struct {
		enable       bool
		clientReqId  string
		upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail
		err          error
	}

	type mockGetOrderWithTransactions struct {
		enable bool
		req    *orderPb.GetOrderWithTransactionsRequest
		want   *orderPb.GetOrderWithTransactionsResponse
		err    error
	}

	type mockUpdateAndChangeStatus struct {
		enable      bool
		clientReqId string
		status      upiOnbEnumsPb.UpiOnboardingStatus
		err         error
	}

	tests := []struct {
		name                         string
		req                          *upiActivityPb.EnquireOrderStatusRequest
		mockGetByClientRequestId     mockGetByClientRequestId
		mockGetOrderWithTransactions mockGetOrderWithTransactions
		mockUpdateAndChangeStatus    mockUpdateAndChangeStatus
		res                          *upiActivityPb.EnquireOrderStatusResponse
		wantErr                      bool
		assertErr                    func(err error) bool
	}{
		{
			name: "successful enquiry - order found in PAID state",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
						Lrn: "lrn",
					},
					},
				},
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req: &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-id",
				},
				want: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpcPb.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Status: orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:     "txn-id-1",
								Status: paymentPb.TransactionStatus_SUCCESS,
							},
						},
					},
				},
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			res: &upiActivityPb.EnquireOrderStatusResponse{},
		},
		{
			name: "transient failure as GetByClientRequestId() call failed",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrContextCanceled,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrInvalidArgument,
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as GetByClientRequestId() call failed with RecordNotFound error",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:       true,
				clientReqId:  "client-req-id",
				upiOnbDetail: nil,
				err:          epifierrors.ErrRecordNotFound,
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as upi onboarding detail has unspecified action",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_UNSPECIFIED,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as upiOnboardingDetail is already in terminal state",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action:  upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS,
					Status:  upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{}},
				},
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as order-id is empty",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as client-req-id is empty",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "",
				},
				OrderId: "order-id",
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient error while fetching order and txns for the order-id",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
						Lrn: "lrn",
					},
					},
				},
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req: &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-id",
				},
				err: epifierrors.ErrResourceExhausted,
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "permanent failure as no order was found for the given order-id",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
						Lrn: "lrn",
					},
					},
				},
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req: &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-id",
				},
				want: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpcPb.StatusRecordNotFound(),
				},
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "permanent failure as one or more invalid arguments were passed",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
						Lrn: "lrn",
					},
					},
				},
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req: &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-id",
				},
				want: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpcPb.StatusInvalidArgument(),
				},
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "transient failure as GetOrderWithTransactions call failed with internal error",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
						Lrn: "lrn",
					},
					},
				},
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req: &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-id",
				},
				want: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpcPb.StatusInternal(),
				},
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "permanent failure as txn found in non-success state whereas order was in PAID state",
			req: &upiActivityPb.EnquireOrderStatusRequest{
				RequestHeader: &activityPb.RequestHeader{
					ClientReqId: "client-req-id",
				},
				OrderId: "order-id",
			},
			mockGetByClientRequestId: mockGetByClientRequestId{
				enable:      true,
				clientReqId: "client-req-id",
				upiOnbDetail: &upiOnboardingPb.UpiOnboardingDetail{
					Action: upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_UPI_LITE,
					Status: upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
					Payload: &upiOnboardingPb.UpiOnboardingDetailsPayload{ActivateUpiLitePayload: &upiOnboardingPb.ActivateUpiLitePayload{
						Lrn: "lrn",
					},
					},
				},
			},
			mockGetOrderWithTransactions: mockGetOrderWithTransactions{
				enable: true,
				req: &orderPb.GetOrderWithTransactionsRequest{
					OrderId: "order-id",
				},
				want: &orderPb.GetOrderWithTransactionsResponse{
					Status: rpcPb.StatusOk(),
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Status: orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:     "txn-id-1",
								Status: paymentPb.TransactionStatus_IN_PROGRESS,
							},
						},
					},
				},
			},
			mockUpdateAndChangeStatus: mockUpdateAndChangeStatus{
				enable:      true,
				clientReqId: "client-req-id",
				status:      upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_CREATED,
				err:         nil,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.mockGetByClientRequestId.enable {
				md.upiOnboardingDetailDao.EXPECT().GetByClientRequestId(gomock.Any(), tt.mockGetByClientRequestId.clientReqId).
					Return(tt.mockGetByClientRequestId.upiOnbDetail, tt.mockGetByClientRequestId.err)
			}

			if tt.mockGetOrderWithTransactions.enable {
				md.orderClient.EXPECT().GetOrderWithTransactions(gomock.Any(), tt.mockGetOrderWithTransactions.req).
					Return(tt.mockGetOrderWithTransactions.want, tt.mockGetOrderWithTransactions.err)
			}

			if tt.mockUpdateAndChangeStatus.enable {
				md.upiOnboardingDetailDao.EXPECT().
					UpdateAndChangeStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(tt.mockUpdateAndChangeStatus.err).AnyTimes()
			}

			if _, err := env.ExecuteActivity(upiNs.EnquireOrderStatus,
				tt.req); (err != nil) != tt.wantErr {
				t.Errorf("EnquireOrderStatus() error = %v, wantErr %v", err, tt.wantErr)
			} else if tt.wantErr && !tt.assertErr(err) {
				t.Errorf("EnquireOrderStatus() error = %v assertion failed", err)
				return
			}
		})
	}
}
