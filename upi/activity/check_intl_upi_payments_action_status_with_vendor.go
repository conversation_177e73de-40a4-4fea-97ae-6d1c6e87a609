package activity

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
)

// CheckIntlUpiPaymentsActionStatusWithVendor - checks the status of activation/deactivation of International Payment with vendor
func (p *Processor) CheckIntlUpiPaymentsActionStatusWithVendor(ctx context.Context, req *upiActivityPb.CheckIntlUpiPaymentsActionStatusWithVendorRequest) (
	*upiActivityPb.CheckIntlUpiPaymentsActionStatusWithVendorResponse, error) {
	var (
		res = &upiActivityPb.CheckIntlUpiPaymentsActionStatusWithVendorResponse{}
		lg  = activity.GetLogger(ctx)
	)

	vendorResp, err := p.upiOnboardingClient.InternationalPaymentsActionEnquiryWithVendor(ctx, &upiOnboardingPb.InternationalPaymentsActionEnquiryWithVendorRequest{
		ClientReqId: req.GetClientReqId(),
		ActorId:     req.GetActorId(),
	})

	switch {
	case err != nil:
		lg.Error("error in calling InternationalPaymentsActionEnquiryWithVendor rpc", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in calling InternationalPaymentsActionEnquiryWithVendor rpc: %s", err.Error()))
	case vendorResp.GetStatus().IsRecordNotFound():
		lg.Error("InternationalPaymentsActionEnquiryWithVendor rpc returned record not found status", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("InternationalPaymentsActionEnquiryWithVendor rpc returned record not found status: %s", vendorResp.GetStatus()))
	case vendorResp.GetStatus().IsInvalidArgument():
		lg.Error("InternationalPaymentsActionEnquiryWithVendor rpc returned Invalid Argument", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("InternationalPaymentsActionEnquiryWithVendor rpc returned invalid argument: %s", vendorResp.GetStatus()))
	case vendorResp.GetStatus().IsInternal():
		lg.Error("InternationalPaymentsActionEnquiryWithVendor rpc returned internal server error", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("InternationalPaymentsActionEnquiryWithVendor rpc returned internal server error: %s", vendorResp.GetStatus()))
	case vendorResp.GetStatus().IsPermanentFailure():
		lg.Error("international payment action failed with vendor", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("International Payment action failed with vendor for given user: %s", vendorResp.GetStatus()))
	case vendorResp.GetUpiOnboardingStatus() == upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_FAILED:
		lg.Error("international payment action failed with vendor", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "International Payment action failed with vendor for given user")
	case vendorResp.GetUpiOnboardingStatus() == upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_IN_PROGRESS:
		lg.Error("InternationalPaymentsActionEnquiryWithVendor rpc returned non success status", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "International Payment action failed with vendor for given user has not reached terminal state")
	}
	return res, nil
}
