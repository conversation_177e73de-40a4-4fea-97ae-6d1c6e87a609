package upi_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"

	"context"
	"errors"
	"fmt"
	"reflect"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	pkgTest2 "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/card"
	commsPb "github.com/epifi/gamma/api/comms"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	upiOnboardingMocks "github.com/epifi/gamma/api/upi/onboarding/mocks"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/upi/mocks"
	"github.com/epifi/gamma/upi"
	accountUpiPinInfoMock "github.com/epifi/gamma/upi/dao/mocks"
	"github.com/epifi/gamma/upi/helper/mocks"
	upiHelperPiMocks "github.com/epifi/gamma/upi/helper/paymentinstrument/mocks"
)

func TestService_RegisterMobile(t *testing.T) {
	logger.Init("test")
	ctr := gomock.NewController(t)
	mockVgUpiClient := vgMocks.NewMockUPIClient(ctr)
	mockAccountPIClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockPIClient := piMocks.NewMockPiClient(ctr)
	mockAccountUpiPinPinInfoDao := accountUpiPinInfoMock.NewMockAccountUpiPinInfoDao(ctr)
	mockUpiOnboardingProcessor := mocks.NewMockUpiOnboardingProcessor(ctr)
	mockPiHelperSvc := upiHelperPiMocks.NewMockPIHelper(ctr)
	mockCommsClient := commsMocks.NewMockCommsClient(ctr)
	pinAttemptExceededSNSPublisher := queueMocks.NewMockPublisher(ctr)
	defer ctr.Finish()
	brokerMock := eventMock.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiOnboardingProcessor.EXPECT().PublishUpiPinSetEventForNudgeExit(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockPiHelperSvc.EXPECT().GetActorIdForPi(gomock.Any(), gomock.Any()).Return("actor-id", nil).AnyTimes()
	mockCommsClient.EXPECT().SendMessage(gomock.Any(), gomock.Any()).Return(&commsPb.SendMessageResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
	// Create upi test suite
	upiService := upi.NewService(
		mockVgUpiClient,
		upiConf.PSPUpiHandleMap,
		upiConf.PSPUpiHandleMapOld,
		mockAccountPIClient,
		mockPIClient,
		upiDao,
		nil,
		upiConf,
		nil,
		nil,
		nil,
		nil,
		mockAccountUpiPinPinInfoDao,
		mockPiHelperSvc,
		nil,
		nil,
		brokerMock,
		nil,
		nil,
		nil,
		nil,
		nil,
		mockCommsClient,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		mockUpiOnboardingProcessor,
		nil,
		pinAttemptExceededSNSPublisher,
		upiDynamicConf, nil,
		nil,
		nil,
	)
	mockAccountUpiPinPinInfoDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	type mockVgRegisterMobile struct {
		isEnable bool
		req      *vgUpiPb.RegisterMobileRequest
		res      *vgUpiPb.RegisterMobileResponse
		wantErr  bool
	}

	type mockGetPIbyAccountId struct {
		enable bool
		req    *accountPiPb.GetPiByAccountIdRequest
		want   *accountPiPb.GetPiByAccountIdResponse
		err    error
	}

	type mockPinInfoCreate struct {
		enable bool
		req    *upiPb.AccountUpiPinInfo
		err    error
	}

	type mockGetTPAPAccount struct {
		enable bool
		req    string
		want   *upiOnboardingPb.UpiAccount
		err    error
	}

	type mockUpdateUpiAccount struct {
		enable        bool
		upiAccount    *upiOnboardingPb.UpiAccount
		updateMask    []upiOnboardingPb.UpiAccountFieldMask
		currentStatus upiOnboardingEnumsPb.UpiAccountStatus
		nextStatus    upiOnboardingEnumsPb.UpiAccountStatus
		err           error
	}

	type mockInsertRequestLogs struct {
		enable bool
		req    *upiOnboardingPb.UpiRequestLog
		err    error
	}

	type mockUpdateRequestLogsForVendorReqId struct {
		enable                 bool
		vendorReqId            string
		newStatus              upiOnboardingEnumsPb.UpiRequestLogApiStatus
		detailedStatusMetadata *upiOnboardingPb.DetailedStatusMetadata
		err                    error
	}

	tests := []struct {
		name                                string
		mockGetTPAPAccount                  mockGetTPAPAccount
		mockVgRegisterMobile                mockVgRegisterMobile
		mockGetPIbyAccountId                mockGetPIbyAccountId
		mockPinInfoCreate                   mockPinInfoCreate
		mockUpdateUpiAccount                mockUpdateUpiAccount
		mockInsertRequestLogs               mockInsertRequestLogs
		mockUpdateRequestLogsForVendorReqId mockUpdateRequestLogsForVendorReqId
		req                                 *upiPb.RegisterMobileRequest
		want                                *upiPb.RegisterMobileResponse
		wantErr                             bool
	}{
		{
			name: "Request register mobile with valid accountId",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockVgRegisterMobile: mockVgRegisterMobile{
				req: &vgUpiPb.RegisterMobileRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnHeader: &vgUpiPb.TransactionHeader{},
					CredBlock: []*upiPb.CredBlock{
						{
							Type:    upiPb.CredBlock_PIN,
							SubType: upiPb.CredBlock_MPIN,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
						{
							Type:    upiPb.CredBlock_PIN,
							SubType: upiPb.CredBlock_ATMPIN,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
					},
					Payer: &upiPb.Customer{
						Type: upiPb.CustomerType_PERSON,
						AccountDetails: &upiPb.CustomerAccountDetails{
							AccountNumber: "ref",
							Type:          accounts.Type_SAVINGS,
							Ifsc:          "IFSC",
						},
						Name:           "",
						PaymentAddress: "test@okaxis",
						MCC:            "0000",
					},
					FormatType: upiPb.FormatType_FORMAT_TYPE_FORMAT2,
					CardInfo: &card.BasicCardInfo{
						Expiry:     "MMYY",
						CardNumber: "************",
					},
				},
				res: &vgUpiPb.RegisterMobileResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
				},
				isEnable: true,
				wantErr:  false,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "test@okaxis",
									IfscCode:               "IFSC",
									AccountReferenceNumber: "ref",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
						{
							Type: piPb.PaymentInstrumentType_DEBIT_CARD,
							Identifier: &piPb.PaymentInstrument_Card{
								Card: &piPb.Card{
									Expiry:           "MMYY",
									SecureCardNumber: "************",
								},
							},
							Id:    "piId-1",
							State: piPb.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockPinInfoCreate: mockPinInfoCreate{
				enable: true,
				req: &upiPb.AccountUpiPinInfo{
					AccountId:       "accountId-1",
					UserAction:      upiPb.AccountUpiPinInfo_PIN_SET,
					Status:          upiPb.AccountUpiPinInfo_INITIATED,
					VendorRequestId: "txn_id",
				},
			},
			req: &upiPb.RegisterMobileRequest{
				AccountId:     "accountId-1",
				Device:        &upiPb.Device{},
				TransactionId: "txn_id",
				CredBlock: []*upiPb.CredBlock{
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_MPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_ATMPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
				},
			},
			want: &upiPb.RegisterMobileResponse{
				Status:          rpc.StatusOk(),
				RawResponseCode: "000",
			},
			wantErr: false,
		},
		{
			name: "Request register mobile with in-valid accountId",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockVgRegisterMobile: mockVgRegisterMobile{
				req:      nil,
				res:      nil,
				isEnable: false,
				wantErr:  false,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{Status: rpc.StatusRecordNotFound()},
				err:  nil,
			},
			mockPinInfoCreate: mockPinInfoCreate{
				enable: true,
				req: &upiPb.AccountUpiPinInfo{
					AccountId:       "accountId-1",
					UserAction:      upiPb.AccountUpiPinInfo_PIN_SET,
					Status:          upiPb.AccountUpiPinInfo_INITIATED,
					VendorRequestId: "txn_id",
				},
			},
			req: &upiPb.RegisterMobileRequest{
				AccountId:     "accountId-1",
				Device:        &upiPb.Device{},
				TransactionId: "txn_id",
				CredBlock: []*upiPb.CredBlock{
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_MPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_ATMPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
				},
			},
			want: &upiPb.RegisterMobileResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error in fetching tpap account",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				err:    errors.New("error"),
			},
			mockVgRegisterMobile: mockVgRegisterMobile{
				req:      nil,
				res:      nil,
				isEnable: false,
				wantErr:  false,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				req:    nil,
				want:   nil,
				enable: false,
				err:    nil,
			},
			req: &upiPb.RegisterMobileRequest{
				AccountId:     "accountId-1",
				Device:        &upiPb.Device{},
				TransactionId: "txn_id",
				CredBlock: []*upiPb.CredBlock{
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_MPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_ATMPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
				},
			},
			want: &upiPb.RegisterMobileResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Request register for tpap account successfully which is an internal account",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "tpap-accountId-1",
				want: &upiOnboardingPb.UpiAccount{
					Id:           "tpap-accountId-1",
					AccountType:  accounts.Type_SAVINGS,
					AccountRefId: "accountId-1",
					Status:       upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				},
			},
			mockVgRegisterMobile: mockVgRegisterMobile{
				req: &vgUpiPb.RegisterMobileRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnHeader: &vgUpiPb.TransactionHeader{},
					CredBlock: []*upiPb.CredBlock{
						{
							Type:    upiPb.CredBlock_PIN,
							SubType: upiPb.CredBlock_MPIN,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
						{
							Type:    upiPb.CredBlock_PIN,
							SubType: upiPb.CredBlock_ATMPIN,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
					},
					Payer: &upiPb.Customer{
						Type: upiPb.CustomerType_PERSON,
						AccountDetails: &upiPb.CustomerAccountDetails{
							AccountNumber: "ref",
							Type:          accounts.Type_SAVINGS,
							Ifsc:          "IFSC",
						},
						Name:           "",
						PaymentAddress: "test@okaxis",
						MCC:            "0000",
					},
					CardInfo: &card.BasicCardInfo{
						Expiry:     "MMYY",
						CardNumber: "************",
					},
					FormatType: upiPb.FormatType_FORMAT_TYPE_FORMAT2,
				},
				res: &vgUpiPb.RegisterMobileResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
				},
				isEnable: true,
				wantErr:  false,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "test@okaxis",
									IfscCode:               "IFSC",
									AccountReferenceNumber: "ref",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
						{
							Type: piPb.PaymentInstrumentType_DEBIT_CARD,
							Identifier: &piPb.PaymentInstrument_Card{
								Card: &piPb.Card{
									Expiry:           "MMYY",
									SecureCardNumber: "************",
								},
							},
							Id:    "piId-1",
							State: piPb.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockUpdateUpiAccount: mockUpdateUpiAccount{
				enable: true,
				upiAccount: &upiOnboardingPb.UpiAccount{
					Id:           "tpap-accountId-1",
					AccountType:  accounts.Type_SAVINGS,
					AccountRefId: "accountId-1",
					Status:       upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					PinSetStatus: upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
				},
				updateMask:    []upiOnboardingPb.UpiAccountFieldMask{upiOnboardingPb.UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS},
				currentStatus: upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				nextStatus:    upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
			},
			mockInsertRequestLogs: mockInsertRequestLogs{
				enable: true,
				req: &upiOnboardingPb.UpiRequestLog{
					AccountId:   "accountId-1",
					Status:      upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED,
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					ApiType:     upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE,
					VendorReqId: "txn_id",
				},
			},
			req: &upiPb.RegisterMobileRequest{
				AccountId:     "tpap-accountId-1",
				Device:        &upiPb.Device{},
				TransactionId: "txn_id",
				CredBlock: []*upiPb.CredBlock{
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_MPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_ATMPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
				},
				CardInfo: &card.BasicCardInfo{
					Expiry:     "MMYY",
					CardNumber: "************",
				},
			},
			mockUpdateRequestLogsForVendorReqId: mockUpdateRequestLogsForVendorReqId{
				enable:      true,
				vendorReqId: "txn_id",
				newStatus:   upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS,
				detailedStatusMetadata: &upiOnboardingPb.DetailedStatusMetadata{
					RawStatusCode: "000",
				},
			},
			want: &upiPb.RegisterMobileResponse{
				Status:          rpc.StatusOk(),
				RawResponseCode: "000",
			},
			wantErr: false,
		},
		{
			name: "Request register for tpap account successfully which is not an internal account (should go via default flow)",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				want: &upiOnboardingPb.UpiAccount{
					Id:          "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					Status:      upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				},
			},
			mockInsertRequestLogs: mockInsertRequestLogs{
				enable: true,
				req: &upiOnboardingPb.UpiRequestLog{
					AccountId:   "accountId-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Status:      upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED,
					ApiType:     upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE,
					VendorReqId: "txn_id",
				},
			},
			mockVgRegisterMobile: mockVgRegisterMobile{
				req: &vgUpiPb.RegisterMobileRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnHeader: &vgUpiPb.TransactionHeader{},
					CredBlock: []*upiPb.CredBlock{
						{
							Type:    upiPb.CredBlock_PIN,
							SubType: upiPb.CredBlock_MPIN,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
						{
							Type:    upiPb.CredBlock_PIN,
							SubType: upiPb.CredBlock_ATMPIN,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
					},
					Payer: &upiPb.Customer{
						Type: upiPb.CustomerType_PERSON,
						AccountDetails: &upiPb.CustomerAccountDetails{
							AccountNumber: "ref",
							Type:          accounts.Type_SAVINGS,
							Ifsc:          "IFSC",
						},
						Name:           "",
						PaymentAddress: "test@okaxis",
						MCC:            "0000",
					},
					FormatType: upiPb.FormatType_FORMAT_TYPE_FORMAT2,

					CardInfo: &card.BasicCardInfo{
						Expiry:           "MMYY",
						MaskedCardNumber: "************",
					},
				},
				res: &vgUpiPb.RegisterMobileResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
				},
				isEnable: true,
				wantErr:  false,
			},
			mockUpdateRequestLogsForVendorReqId: mockUpdateRequestLogsForVendorReqId{
				enable:      true,
				vendorReqId: "txn_id",
				newStatus:   upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS,
				detailedStatusMetadata: &upiOnboardingPb.DetailedStatusMetadata{
					RawStatusCode: "000",
				},
			},

			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "test@okaxis",
									IfscCode:               "IFSC",
									AccountReferenceNumber: "ref",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
						{
							Type: piPb.PaymentInstrumentType_DEBIT_CARD,
							Identifier: &piPb.PaymentInstrument_Card{
								Card: &piPb.Card{
									Expiry:           "MMYY",
									SecureCardNumber: "************",
								},
							},
							Id:    "piId-1",
							State: piPb.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockUpdateUpiAccount: mockUpdateUpiAccount{
				enable: true,
				upiAccount: &upiOnboardingPb.UpiAccount{
					Id:           "accountId-1",
					AccountType:  accounts.Type_SAVINGS,
					Status:       upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					PinSetStatus: upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
				},
				updateMask:    []upiOnboardingPb.UpiAccountFieldMask{upiOnboardingPb.UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS},
				currentStatus: upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				nextStatus:    upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
			},
			req: &upiPb.RegisterMobileRequest{
				AccountId:     "accountId-1",
				Device:        &upiPb.Device{},
				TransactionId: "txn_id",
				CredBlock: []*upiPb.CredBlock{
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_MPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_ATMPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
				},
				CardInfo: &card.BasicCardInfo{
					Expiry:           "MMYY",
					MaskedCardNumber: "************",
				},
			},
			want: &upiPb.RegisterMobileResponse{
				Status:          rpc.StatusOk(),
				RawResponseCode: "000",
			},
			wantErr: false,
		},
		{
			name: "Request register mobile for internal tpap account failed due to missing card info(via debit card flow)",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				want: &upiOnboardingPb.UpiAccount{
					Id:          "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					Status:      upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				},
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "test@okaxis",
									IfscCode:               "IFSC",
									AccountReferenceNumber: "ref",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
						{
							Type: piPb.PaymentInstrumentType_DEBIT_CARD,
							Identifier: &piPb.PaymentInstrument_Card{
								Card: &piPb.Card{
									Expiry:           "MMYY",
									SecureCardNumber: "************",
								},
							},
							Id:    "piId-1",
							State: piPb.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			req: &upiPb.RegisterMobileRequest{
				AccountId:           "accountId-1",
				CardInfo:            &card.BasicCardInfo{},
				UpiPinSetOptionType: upiOnboardingEnumsPb.UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD,
			},
			want: &upiPb.RegisterMobileResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "Request register for aadhaar enabled tpap account successfully which is not an internal account(should go via aadhaar flow)",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				want: &upiOnboardingPb.UpiAccount{
					Id:          "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					Status:      upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				},
			},
			mockInsertRequestLogs: mockInsertRequestLogs{
				enable: true,
				req: &upiOnboardingPb.UpiRequestLog{
					AccountId:   "accountId-1",
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Status:      upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED,
					ApiType:     upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_REGISTER_MOBILE,
					VendorReqId: "txn_id",
				},
			},
			mockVgRegisterMobile: mockVgRegisterMobile{
				req: &vgUpiPb.RegisterMobileRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					TxnHeader: &vgUpiPb.TransactionHeader{},
					CredBlock: []*upiPb.CredBlock{
						{
							Type:    upiPb.CredBlock_PIN,
							SubType: upiPb.CredBlock_MPIN,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
						{
							Type:    upiPb.CredBlock_OTP,
							SubType: upiPb.CredBlock_AADHAAR,
							Data: &upiPb.CredBlock_Data{
								Code: "code",
								Ki:   "ki",
								Text: "text",
							},
						},
					},
					Payer: &upiPb.Customer{
						Type: upiPb.CustomerType_PERSON,
						AccountDetails: &upiPb.CustomerAccountDetails{
							AccountNumber: "ref",
							Type:          accounts.Type_SAVINGS,
							Ifsc:          "IFSC",
						},
						Name:           "",
						PaymentAddress: "test@okaxis",
						MCC:            "0000",
					},
					FormatType: upiPb.FormatType_FORMAT_TYPE_FORMAT3,
				},

				res: &vgUpiPb.RegisterMobileResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
				},
				isEnable: true,
				wantErr:  false,
			},
			mockUpdateRequestLogsForVendorReqId: mockUpdateRequestLogsForVendorReqId{
				enable:      true,
				vendorReqId: "txn_id",
				newStatus:   upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS,
				detailedStatusMetadata: &upiOnboardingPb.DetailedStatusMetadata{
					RawStatusCode: "000",
				},
			},

			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "test@okaxis",
									IfscCode:               "IFSC",
									AccountReferenceNumber: "ref",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
						{
							Type: piPb.PaymentInstrumentType_DEBIT_CARD,
							Identifier: &piPb.PaymentInstrument_Card{
								Card: &piPb.Card{
									Expiry:           "MMYY",
									SecureCardNumber: "************",
								},
							},
							Id:    "piId-1",
							State: piPb.PaymentInstrumentState_CREATED,
						},
					},
				},
				err: nil,
			},
			mockUpdateUpiAccount: mockUpdateUpiAccount{
				enable: true,
				upiAccount: &upiOnboardingPb.UpiAccount{
					Id:           "accountId-1",
					AccountType:  accounts.Type_SAVINGS,
					Status:       upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					PinSetStatus: upiOnboardingEnumsPb.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
				},
				updateMask:    []upiOnboardingPb.UpiAccountFieldMask{upiOnboardingPb.UpiAccountFieldMask_UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS},
				currentStatus: upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
				nextStatus:    upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
			},
			req: &upiPb.RegisterMobileRequest{
				AccountId:     "accountId-1",
				Device:        &upiPb.Device{},
				TransactionId: "txn_id",
				CredBlock: []*upiPb.CredBlock{
					{
						Type:    upiPb.CredBlock_PIN,
						SubType: upiPb.CredBlock_MPIN,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
					{
						Type:    upiPb.CredBlock_OTP,
						SubType: upiPb.CredBlock_AADHAAR,
						Data: &upiPb.CredBlock_Data{
							Code: "code",
							Ki:   "ki",
							Text: "text",
						},
					},
				},
				UpiPinSetOptionType: upiOnboardingEnumsPb.UpiPinSetOptionType_UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER,
			},
			want: &upiPb.RegisterMobileResponse{
				Status:          rpc.StatusOk(),
				RawResponseCode: "000",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wg := sync.WaitGroup{}

			if tt.mockGetTPAPAccount.enable {
				mockUpiOnboardingProcessor.EXPECT().GetTPAPAccount(context.Background(), tt.mockGetTPAPAccount.req).
					Return(tt.mockGetTPAPAccount.want, tt.mockGetTPAPAccount.err)
			}
			if tt.mockInsertRequestLogs.enable {
				mockUpiOnboardingProcessor.EXPECT().InsertRequestLogs(context.Background(), newInsertRequestLogsReqMatcher(tt.mockInsertRequestLogs.req)).
					Return(tt.mockInsertRequestLogs.err)
			}
			if tt.mockVgRegisterMobile.isEnable {
				mockVgUpiClient.EXPECT().RegisterMobile(context.Background(), newRegisterMobileVgMatch(tt.mockVgRegisterMobile.req)).
					Return(tt.mockVgRegisterMobile.res, nil)
			}
			if tt.mockUpdateRequestLogsForVendorReqId.enable {
				wg.Add(1)
				mockUpiOnboardingProcessor.EXPECT().UpdateRequestLogsForVendorReqId(gomock.Any(), tt.mockUpdateRequestLogsForVendorReqId.vendorReqId,
					tt.mockUpdateRequestLogsForVendorReqId.newStatus, tt.mockUpdateRequestLogsForVendorReqId.detailedStatusMetadata).
					DoAndReturn(
						func(arg0, arg1, arg2 interface{}, x ...interface{}) (r1 interface{}) {
							defer wg.Done()
							return tt.mockUpdateRequestLogsForVendorReqId.err
						})
			}
			if tt.mockGetPIbyAccountId.enable {
				mockAccountPIClient.EXPECT().GetPiByAccountId(context.Background(), tt.mockGetPIbyAccountId.req).
					Return(tt.mockGetPIbyAccountId.want, tt.mockGetPIbyAccountId.err)
			}
			if tt.mockPinInfoCreate.enable {
				mockAccountUpiPinPinInfoDao.EXPECT().Create(context.Background(), tt.mockPinInfoCreate.req).Return(tt.mockPinInfoCreate.req, tt.mockPinInfoCreate.err)
			}
			if tt.mockUpdateUpiAccount.enable {
				mockUpiOnboardingProcessor.EXPECT().UpdateUpiAccount(context.Background(), tt.mockUpdateUpiAccount.upiAccount,
					tt.mockUpdateUpiAccount.updateMask, tt.mockUpdateUpiAccount.currentStatus, tt.mockUpdateUpiAccount.nextStatus).
					Return(tt.mockUpdateUpiAccount.err)
			}

			got, err := upiService.RegisterMobile(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RegisterMobile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RegisterMobile() got = %v, want %v", got, tt.want)
			}
			wg.Wait()
		})
	}
}

func TestService_GenerateUpiOtp(t *testing.T) {
	upiService, mocks, deferFun := getUpiTestServiceWithMock(t)
	mocks.brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	defer deferFun()

	type mockVgGenerateOtp struct {
		isEnable bool
		req      *vgUpiPb.GenerateUpiOtpRequest
		res      *vgUpiPb.GenerateUpiOtpResponse
		wantErr  bool
	}

	type mockGetTPAPAccount struct {
		enable bool
		req    string
		want   *upiOnboardingPb.UpiAccount
		err    error
	}

	type mockGetPIbyAccountId struct {
		enable bool
		req    *accountPiPb.GetPiByAccountIdRequest
		want   *accountPiPb.GetPiByAccountIdResponse
		err    error
	}
	type mockInsertRequestLogs struct {
		enable bool
		req    *upiOnboardingPb.UpiRequestLog
		err    error
	}

	type mockUpdateRequestLogsForVendorReqId struct {
		enable                 bool
		newStatus              upiOnboardingEnumsPb.UpiRequestLogApiStatus
		detailedStatusMetadata *upiOnboardingPb.DetailedStatusMetadata
		err                    error
	}

	tests := []struct {
		name                                string
		mockVgGenerateOtp                   mockVgGenerateOtp
		mockGetTPAPAccount                  mockGetTPAPAccount
		mockGetPIbyAccountId                mockGetPIbyAccountId
		mockInsertRequestLogs               mockInsertRequestLogs
		mockUpdateRequestLogsForVendorReqId mockUpdateRequestLogsForVendorReqId
		req                                 *upiPb.GenerateUpiOtpRequest
		want                                *upiPb.GenerateUpiOtpResponse
		wantErr                             bool
	}{
		{
			name: "Request OTP with valid account id",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockVgGenerateOtp: mockVgGenerateOtp{
				req: &vgUpiPb.GenerateUpiOtpRequest{},
				res: &vgUpiPb.GenerateUpiOtpResponse{
					Status: rpc.StatusOk(),
				},
				isEnable: true,
				wantErr:  false,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "hulk@okaxis",
									IfscCode:               "MARVEL",
									AccountReferenceNumber: "ss==",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
					},
				},
				err: nil,
			},
			mockInsertRequestLogs: mockInsertRequestLogs{
				enable: true,
				req: &upiOnboardingPb.UpiRequestLog{
					ActorId:   "actor-id-1",
					AccountId: "accountId-1",
					Status:    upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED,
					Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
					ApiType:   upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP,
				},
			},
			mockUpdateRequestLogsForVendorReqId: mockUpdateRequestLogsForVendorReqId{
				enable:                 true,
				newStatus:              upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS,
				detailedStatusMetadata: &upiOnboardingPb.DetailedStatusMetadata{},
			},
			req: &upiPb.GenerateUpiOtpRequest{
				ActorId:   "actor-id-1",
				AccountId: "accountId-1",
				Device:    &upiPb.Device{},
			},
			want: &upiPb.GenerateUpiOtpResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "Request OTP with in-valid account id",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "invalid-accountId-1",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "invalid-accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{Status: rpc.StatusRecordNotFound()},
				err:  nil,
			},
			req: &upiPb.GenerateUpiOtpRequest{
				ActorId:   "actor-id-1",
				AccountId: "invalid-accountId-1",
				Device:    &upiPb.Device{},
			},
			want: &upiPb.GenerateUpiOtpResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error in fetching tpap account",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				err:    errors.New("error in fetching tpap account"),
			},
			req: &upiPb.GenerateUpiOtpRequest{
				ActorId:   "actor-id-1",
				AccountId: "accountId-1",
				Device:    &upiPb.Device{},
			},
			want: &upiPb.GenerateUpiOtpResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Request OTP successfully for tpap account which is an internal account",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "tpap-accountId-1",
				want: &upiOnboardingPb.UpiAccount{
					Id:           "tpap-accountId-1",
					AccountRefId: "accountId-1",
					AccountType:  accounts.Type_SAVINGS,
				},
			},
			mockVgGenerateOtp: mockVgGenerateOtp{
				req: &vgUpiPb.GenerateUpiOtpRequest{},
				res: &vgUpiPb.GenerateUpiOtpResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
				},
				isEnable: true,
				wantErr:  false,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "hulk@okaxis",
									IfscCode:               "MARVEL",
									AccountReferenceNumber: "ss==",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
					},
				},
				err: nil,
			},
			mockInsertRequestLogs: mockInsertRequestLogs{
				enable: true,
				req: &upiOnboardingPb.UpiRequestLog{
					ActorId:   "actor-id-1",
					AccountId: "accountId-1",
					Status:    upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED,
					Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
					ApiType:   upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP,
				},
			},
			req: &upiPb.GenerateUpiOtpRequest{
				ActorId:   "actor-id-1",
				AccountId: "tpap-accountId-1",
				Device:    &upiPb.Device{},
			},
			mockUpdateRequestLogsForVendorReqId: mockUpdateRequestLogsForVendorReqId{
				enable:    true,
				newStatus: upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS,
				detailedStatusMetadata: &upiOnboardingPb.DetailedStatusMetadata{
					RawStatusCode: "000",
				},
			},
			want: &upiPb.GenerateUpiOtpResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "Request OTP successfully for tpap account which is not an internal account",
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "accountId-1",
				want: &upiOnboardingPb.UpiAccount{
					Id:          "accountId-1",
					AccountType: accounts.Type_SAVINGS,
				},
			},
			mockVgGenerateOtp: mockVgGenerateOtp{
				req: &vgUpiPb.GenerateUpiOtpRequest{},
				res: &vgUpiPb.GenerateUpiOtpResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
				},
				isEnable: true,
				wantErr:  false,
			},
			mockGetPIbyAccountId: mockGetPIbyAccountId{
				enable: true,
				req: &accountPiPb.GetPiByAccountIdRequest{
					AccountId:   "accountId-1",
					AccountType: accounts.Type_SAVINGS,
					PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI, piPb.PaymentInstrumentType_DEBIT_CARD},
				},
				want: &accountPiPb.GetPiByAccountIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "hulk@okaxis",
									IfscCode:               "MARVEL",
									AccountReferenceNumber: "ss==",
									MaskedAccountNumber:    "xxxx2345",
									AccountType:            accounts.Type_SAVINGS,
								},
							},
							Id: "piId-1",
						},
					},
				},
				err: nil,
			},
			mockInsertRequestLogs: mockInsertRequestLogs{
				enable: true,
				req: &upiOnboardingPb.UpiRequestLog{
					ActorId:   "actor-id-1",
					AccountId: "accountId-1",
					Status:    upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_CREATED,
					Vendor:    commonvgpb.Vendor_FEDERAL_BANK,
					ApiType:   upiOnboardingEnumsPb.UpiRequestLogApiType_UPI_REQUEST_LOG_API_TYPE_GENERATE_UPI_OTP,
				},
			},
			req: &upiPb.GenerateUpiOtpRequest{
				ActorId:   "actor-id-1",
				AccountId: "accountId-1",
				Device:    &upiPb.Device{},
			},
			mockUpdateRequestLogsForVendorReqId: mockUpdateRequestLogsForVendorReqId{
				enable:    true,
				newStatus: upiOnboardingEnumsPb.UpiRequestLogApiStatus_UPI_REQUEST_LOG_API_STATUS_SUCCESS,
				detailedStatusMetadata: &upiOnboardingPb.DetailedStatusMetadata{
					RawStatusCode: "000",
				},
			},
			want: &upiPb.GenerateUpiOtpResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wg := sync.WaitGroup{}
			if tt.mockGetTPAPAccount.enable {
				mocks.mockUpiOnboardingProcessor.EXPECT().GetTPAPAccount(context.Background(), tt.mockGetTPAPAccount.req).
					Return(tt.mockGetTPAPAccount.want, tt.mockGetTPAPAccount.err)
			}
			if tt.mockInsertRequestLogs.enable {
				mocks.mockUpiOnboardingProcessor.EXPECT().InsertRequestLogs(context.Background(), newInsertRequestLogsReqMatcher(tt.mockInsertRequestLogs.req)).
					Return(tt.mockInsertRequestLogs.err)
			}
			if tt.mockVgGenerateOtp.isEnable {
				mocks.mockVgUpiClient.EXPECT().GenerateUpiOtp(context.Background(), gomock.Any()).
					Return(tt.mockVgGenerateOtp.res, nil)
			}
			if tt.mockUpdateRequestLogsForVendorReqId.enable {
				wg.Add(1)
				mocks.mockUpiOnboardingProcessor.EXPECT().UpdateRequestLogsForVendorReqId(gomock.Any(), gomock.Any(),
					tt.mockUpdateRequestLogsForVendorReqId.newStatus, tt.mockUpdateRequestLogsForVendorReqId.detailedStatusMetadata).
					DoAndReturn(
						func(arg0, arg1, arg2 interface{}, x ...interface{}) (r1 interface{}) {
							defer wg.Done()
							return tt.mockUpdateRequestLogsForVendorReqId.err
						})
			}
			if tt.mockGetPIbyAccountId.enable {
				mocks.mockAccountPIClient.EXPECT().GetPiByAccountId(context.Background(), tt.mockGetPIbyAccountId.req).
					Return(tt.mockGetPIbyAccountId.want, tt.mockGetPIbyAccountId.err)
			}

			got, err := upiService.GenerateUpiOtp(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateUpiOtp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Status.Code != tt.want.Status.Code {
				t.Errorf("GenerateUpiOtp() got = %v, want %v", got, tt.want)
			}
			wg.Wait()
		})
	}
}

// nolint: funlen
func TestService_GetPinFlowParameters(t *testing.T) {
	// actor context for test
	ctx := context.WithValue(context.Background(), epificontext.CtxActorKey, "actor-1")

	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockVgUpiClient := vgMocks.NewMockUPIClient(ctr)
	mockAccountPIClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockPIClient := piMocks.NewMockPiClient(ctr)
	brokerMock := eventMock.NewMockBroker(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPublisher := queueMocks.NewMockDelayPublisher(ctr)
	mockOnboardingClient := onbMocks.NewMockOnboardingClient(ctr)
	mockUpiOnboardingProcessor := mocks.NewMockUpiOnboardingProcessor(ctr)
	mockUpiOnboardingClient := upiOnboardingMocks.NewMockUpiOnboardingClient(ctr)
	pinAttemptExceededSNSPublisher := queueMocks.NewMockPublisher(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

	// Create UPI test suite
	upiService := upi.NewService(
		mockVgUpiClient,
		upiConf.PSPUpiHandleMap,
		upiConf.PSPUpiHandleMapOld,
		mockAccountPIClient,
		mockPIClient,
		upiDao,
		nil,
		upiConf,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		nil,
		brokerMock,
		mockSavingsClient,
		nil,
		nil,
		mockPublisher,
		nil,
		nil,
		nil,
		nil,
		mockOnboardingClient,
		nil,
		nil,
		nil,
		nil,
		mockUpiOnboardingProcessor,
		nil,
		pinAttemptExceededSNSPublisher,
		nil,
		nil,
		mockUpiOnboardingClient,
		nil,
	)

	mockVgUpiClient.EXPECT().ListKeys(ctx, gomock.Any()).
		Return(&vgUpiPb.ListKeysResponse{
			KeyXmlPayload: keyXmlResponse,
			Status:        rpc.StatusOk(),
		}, nil).MaxTimes(1)

	type args struct {
		ctx context.Context
		req *upiPb.GetPinFlowParametersRequest
	}
	type mockGetAccount struct {
		enable bool
		req    *savingsPb.GetAccountRequest
		res    *savingsPb.GetAccountResponse
		err    error
	}
	type mockPublish struct {
		enable  bool
		payload *savingsPb.CreateVPARequest
		err     error
	}

	type mockGetOnboardingDetails struct {
		enable bool
		req    *onboardingPb.GetDetailsRequest
		res    *onboardingPb.GetDetailsResponse
		err    error
	}

	type mockGetPiByAccountId struct {
		enable bool
		req    *accountPiPb.GetPiByAccountIdRequest
		res    *accountPiPb.GetPiByAccountIdResponse
		err    error
	}

	type mockGetTPAPAccount struct {
		enable bool
		req    string
		want   *upiOnboardingPb.UpiAccount
		err    error
	}

	type mockLinkInternalAccount struct {
		enable bool
		req    *upiOnboardingPb.LinkInternalAccountRequest
		res    *upiOnboardingPb.LinkInternalAccountResponse
		err    error
	}

	tests := []struct {
		name                     string
		args                     args
		want                     *upiPb.GetPinFlowParametersResponse
		mockGetTPAPAccount       mockGetTPAPAccount
		mockGetAccount           mockGetAccount
		mockGetPiByAccountId     []mockGetPiByAccountId
		mockGetOnboardingDetails mockGetOnboardingDetails
		mockPublish              mockPublish
		mockLinkInternalAccount  mockLinkInternalAccount
		wantErr                  bool
	}{
		{
			name: "Pin flow params with valid account request for set pin",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{
				ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      fixturesAccountInfoModel.AccountId,
					PinFlowType:    upiPb.GetPinFlowParametersRequest_SET_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    fixturesAccountInfoModel.AccountId,
				err:    epifierrors.ErrRecordNotFound,
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status:             rpc.StatusOk(),
				BankConfigJson:     (*upiConf.BankConfigJsonMap)[FEDERAL_BANK],
				ControlJson:        fixturesAccountInfoModel.AllowedCred,
				KeyXmlPayload:      keyXmlResponse,
				TransactionId:      uuid.New().String(),
				KeyCode:            types.KeyCode_NPCI,
				IsExistingBankUser: false,
				BankConfig: &upiPb.GetPinFlowParametersResponse_BankConfig{
					PayerBankName:    commonvgpb.Vendor_FEDERAL_BANK.String(),
					Color:            upiConf.NPCICLBankConfig.Color,
					BackgroundColor:  upiConf.NPCICLBankConfig.BackgroundColor,
					ResendOtpFeature: upiConf.NPCICLBankConfig.ResendOTPFeature,
				},
				Vpas: []string{"abc@okaxis"},
			},
			mockGetPiByAccountId: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_DEBIT_CARD},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_DEBIT_CARD,
								State: piPb.PaymentInstrumentState_VERIFIED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_UPI,
								State: piPb.PaymentInstrumentState_VERIFIED,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "abc@okaxis",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetOnboardingDetails: mockGetOnboardingDetails{
				enable: true,
				req: &onboardingPb.GetDetailsRequest{
					ActorId: "actor-1",
					Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
				},
				res: &onboardingPb.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboardingPb.OnboardingDetails{
						ActorId: "actor-1",
						StageMetadata: &onboardingPb.StageMetadata{
							DedupeStatus:    customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
							KycDedupeStatus: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
						},
					},
				},
				err: nil,
			},
			wantErr: false,
		},
		{
			name: "Pin flow params with invalid account id set pin",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{
				ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      "12963",
					PinFlowType:    upiPb.GetPinFlowParametersRequest_SET_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "12963",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockLinkInternalAccount: mockLinkInternalAccount{
				enable: true,
				req: &upiOnboardingPb.LinkInternalAccountRequest{
					ActorId: "actor-1",
				},
				res: &upiOnboardingPb.LinkInternalAccountResponse{
					Status: rpc.StatusOk(),
				},
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Pin flow params with valid account id for change pin",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      fixturesAccountInfoModel.AccountId,
					PinFlowType:    upiPb.GetPinFlowParametersRequest_CHANGE_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    fixturesAccountInfoModel.AccountId,
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetPiByAccountId: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_DEBIT_CARD},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_DEBIT_CARD,
								State: piPb.PaymentInstrumentState_VERIFIED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_UPI,
								State: piPb.PaymentInstrumentState_VERIFIED,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "abc@okaxis",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetOnboardingDetails: mockGetOnboardingDetails{
				enable: true,
				req: &onboardingPb.GetDetailsRequest{
					ActorId: "actor-1",
					Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
				},
				res: &onboardingPb.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboardingPb.OnboardingDetails{
						ActorId: "actor-1",
						StageMetadata: &onboardingPb.StageMetadata{
							DedupeStatus:    customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
							KycDedupeStatus: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
						},
					},
				},
				err: nil,
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status:         rpc.StatusOk(),
				BankConfigJson: (*upiConf.BankConfigJsonMap)[FEDERAL_BANK],
				ControlJson: &upiPb.ControlJson{
					CredAlloweds: []*upiPb.CredAllowed{
						fixturesAccountInfoModel.AllowedCred.CredAlloweds[0],
						{
							SubType: "NMPIN",
							DLength: fixturesAccountInfoModel.AllowedCred.CredAlloweds[0].DLength,
							DType:   fixturesAccountInfoModel.AllowedCred.CredAlloweds[0].DType,
							Type:    fixturesAccountInfoModel.AllowedCred.CredAlloweds[0].Type,
						},
					},
				},
				KeyXmlPayload:      keyXmlResponse,
				TransactionId:      uuid.New().String(),
				KeyCode:            types.KeyCode_NPCI,
				IsExistingBankUser: false,
				Vpas:               []string{"abc@okaxis"},
			},
			wantErr: false,
		},
		{
			name: "Pin flow params with VPA CREATED failed for a valid savings account",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{
				ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      "12963",
					PinFlowType:    upiPb.GetPinFlowParametersRequest_SET_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    "12963",
				err:    epifierrors.ErrRecordNotFound,
			},
			mockLinkInternalAccount: mockLinkInternalAccount{
				enable: true,
				req: &upiOnboardingPb.LinkInternalAccountRequest{
					ActorId: "actor-1",
				},
				res: &upiOnboardingPb.LinkInternalAccountResponse{
					Status: rpc.StatusInternal(),
				},
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "Pin flow params with valid account id and no activated card and one suspended card",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      fixturesAccountInfoModel.AccountId,
					PinFlowType:    upiPb.GetPinFlowParametersRequest_CHANGE_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    fixturesAccountInfoModel.AccountId,
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetPiByAccountId: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_DEBIT_CARD},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_DEBIT_CARD,
								State: piPb.PaymentInstrumentState_SUSPENDED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_UPI,
								State: piPb.PaymentInstrumentState_VERIFIED,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "abc@okaxis",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetOnboardingDetails: mockGetOnboardingDetails{
				enable: true,
				req: &onboardingPb.GetDetailsRequest{
					ActorId: "actor-1",
					Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
				},
				res: &onboardingPb.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboardingPb.OnboardingDetails{
						ActorId: "actor-1",
						StageMetadata: &onboardingPb.StageMetadata{
							DedupeStatus:    customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
							KycDedupeStatus: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
						},
					},
				},
				err: nil,
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status:         rpc.StatusOk(),
				BankConfigJson: (*upiConf.BankConfigJsonMap)[FEDERAL_BANK],
				ControlJson: &upiPb.ControlJson{
					CredAlloweds: []*upiPb.CredAllowed{
						fixturesAccountInfoModel.AllowedCred.CredAlloweds[0],
						{
							SubType: "NMPIN",
							DLength: fixturesAccountInfoModel.AllowedCred.CredAlloweds[0].DLength,
							DType:   fixturesAccountInfoModel.AllowedCred.CredAlloweds[0].DType,
							Type:    fixturesAccountInfoModel.AllowedCred.CredAlloweds[0].Type,
						},
					},
				},
				KeyXmlPayload:      keyXmlResponse,
				TransactionId:      uuid.New().String(),
				KeyCode:            types.KeyCode_NPCI,
				IsExistingBankUser: false,
				Vpas:               []string{"abc@okaxis"},
			},
			wantErr: false,
		},
		{
			name: "Pin flow params with valid account id with no activated card and one block card",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      fixturesAccountInfoModel.AccountId,
					PinFlowType:    upiPb.GetPinFlowParametersRequest_CHANGE_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    fixturesAccountInfoModel.AccountId,
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetPiByAccountId: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_DEBIT_CARD},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_DEBIT_CARD,
								State: piPb.PaymentInstrumentState_BLOCKED,
							},
						},
					},
					err: nil,
				},
			},
			mockGetOnboardingDetails: mockGetOnboardingDetails{
				enable: false,
				req: &onboardingPb.GetDetailsRequest{
					ActorId: "actor-1",
					Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
				},
				res: &onboardingPb.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboardingPb.OnboardingDetails{
						ActorId: "actor-1",
						StageMetadata: &onboardingPb.StageMetadata{
							DedupeStatus:    customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
							KycDedupeStatus: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
						},
					},
				},
				err: nil,
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status: rpc.NewStatus(uint32(upiPb.GetPinFlowParametersResponse_CARD_BLOCKED),
					"",
					"",
				),
			},
			wantErr: false,
		},
		{
			name: "Pin flow params with valid account id and no activated card and one block card and one inactive card",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      fixturesAccountInfoModel.AccountId,
					PinFlowType:    upiPb.GetPinFlowParametersRequest_CHANGE_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    fixturesAccountInfoModel.AccountId,
				err:    epifierrors.ErrRecordNotFound,
			},
			mockGetPiByAccountId: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   fixturesAccountInfoModel.AccountId,
						AccountType: accounts.Type_SAVINGS,
						PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_DEBIT_CARD},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*piPb.PaymentInstrument{
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_DEBIT_CARD,
								State: piPb.PaymentInstrumentState_BLOCKED,
							},
							{
								Id:    "id-1",
								Type:  piPb.PaymentInstrumentType_DEBIT_CARD,
								State: piPb.PaymentInstrumentState_PRE_CREATED,
							},
						},
					},
					err: nil,
				},
			},
			mockGetOnboardingDetails: mockGetOnboardingDetails{
				enable: false,
				req: &onboardingPb.GetDetailsRequest{
					ActorId: "actor-1",
					Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
				},
				res: &onboardingPb.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboardingPb.OnboardingDetails{
						ActorId: "actor-1",
						StageMetadata: &onboardingPb.StageMetadata{
							DedupeStatus:    customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
							KycDedupeStatus: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
						},
					},
				},
				err: nil,
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status: rpc.NewStatus(uint32(upiPb.GetPinFlowParametersResponse_CARD_INACTIVED),
					"",
					"",
				),
			},
			wantErr: false,
		},
		{
			name: "error while fetching tpap account",
			args: struct {
				ctx context.Context
				req *upiPb.GetPinFlowParametersRequest
			}{ctx: ctx,
				req: &upiPb.GetPinFlowParametersRequest{
					AccountId:      fixturesAccountInfoModel.AccountId,
					PinFlowType:    upiPb.GetPinFlowParametersRequest_CHANGE_PIN,
					CurrentActorId: "actor-1",
				},
			},
			mockGetTPAPAccount: mockGetTPAPAccount{
				enable: true,
				req:    fixturesAccountInfoModel.AccountId,
				err:    errors.New("error in fetching tpap account"),
			},
			want: &upiPb.GetPinFlowParametersResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			// Clean database, run migrations and load fixtures
			pkgTest2.PrepareRandomScopedCrdbDatabase(t, upiConf.EpifiDb, dbName, db, affectedTestTables, &initialiseSameDbOnce)

			if tt.mockGetTPAPAccount.enable {
				mockUpiOnboardingProcessor.EXPECT().GetTPAPAccount(tt.args.ctx, tt.mockGetTPAPAccount.req).
					Return(tt.mockGetTPAPAccount.want, tt.mockGetTPAPAccount.err)
			}

			if tt.mockGetAccount.enable {
				mockSavingsClient.EXPECT().GetAccount(tt.args.ctx, tt.mockGetAccount.req).
					Return(tt.mockGetAccount.res, tt.mockGetAccount.err)
			}

			if tt.mockPublish.enable {
				mockPublisher.EXPECT().PublishWithDelay(tt.args.ctx, tt.mockPublish.payload, time.Minute).
					Return("", tt.mockPublish.err)
			}

			if tt.mockGetOnboardingDetails.enable {
				mockOnboardingClient.EXPECT().GetDetails(tt.args.ctx, tt.mockGetOnboardingDetails.req).
					Return(tt.mockGetOnboardingDetails.res, tt.mockGetOnboardingDetails.err)
			}

			for _, t := range tt.mockGetPiByAccountId {
				if t.enable {
					mockAccountPIClient.EXPECT().GetPiByAccountId(tt.args.ctx, t.req).
						Return(t.res, t.err)
				}
			}

			if tt.mockLinkInternalAccount.enable {
				mockUpiOnboardingClient.EXPECT().LinkInternalAccount(tt.args.ctx, newLinkInternalAccountArgMatcher(tt.mockLinkInternalAccount.req)).
					Return(tt.mockLinkInternalAccount.res, tt.mockLinkInternalAccount.err)
			}

			got, err := upiService.GetPinFlowParameters(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPinFlowParameters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assertPinFlowParams(t, got, tt.want)
		})
	}
}

type RegisterMobileVgArgMatcher struct {
	want *vgUpiPb.RegisterMobileRequest
}

func newRegisterMobileVgMatch(want *vgUpiPb.RegisterMobileRequest) *RegisterMobileVgArgMatcher {
	return &RegisterMobileVgArgMatcher{want: want}
}

func (r *RegisterMobileVgArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*vgUpiPb.RegisterMobileRequest)
	if !ok {
		return false
	}

	r.want.TxnHeader = got.TxnHeader
	r.want.Payer.Device = got.Payer.Device
	return reflect.DeepEqual(r.want, got)
}

func (s *RegisterMobileVgArgMatcher) String() string {
	return fmt.Sprintf("%v", s.want)
}

type LinkInternalAccountArgMatcher struct {
	want *upiOnboardingPb.LinkInternalAccountRequest
}

func newLinkInternalAccountArgMatcher(want *upiOnboardingPb.LinkInternalAccountRequest) *LinkInternalAccountArgMatcher {
	return &LinkInternalAccountArgMatcher{want: want}
}

func (c *LinkInternalAccountArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*upiOnboardingPb.LinkInternalAccountRequest)
	if !ok {
		return false
	}

	c.want.ClientReqId = got.ClientReqId
	return reflect.DeepEqual(c.want, got)
}

func (c *LinkInternalAccountArgMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}
