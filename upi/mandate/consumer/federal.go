package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	upiPb "github.com/epifi/gamma/api/upi"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/api/vendors"
	"github.com/epifi/gamma/api/vendors/federal/upi/mandate"
)

var (
	reqAuthMandateTypeMap = map[string]upiMandatePb.MandateType{
		vendors.MandateTypeCreate:  upiMandatePb.MandateType_CREATE,
		vendors.MandateTypeRevoke:  upiMandatePb.MandateType_REVOKE,
		vendors.MandateTypeUpdate:  upiMandatePb.MandateType_UPDATE,
		vendors.MandateTypePause:   upiMandatePb.MandateType_PAUSE,
		vendors.MandateTypeUnpause: upiMandatePb.MandateType_UNPAUSE,
	}
)

// createRespAuthReqFromReqAuthMandateFederal helper method to create `RespAuthMandateReq` VG proto struct from
// `ReqAuthMandate`
func createRespAuthReqFromReqAuthMandateFederal(
	ctx context.Context,
	reqAuth *mandate.ReqAuthMandate,
	accDetail *upiPb.CustomerAccountDetails,
	info *upiPb.CustomerInformation,
	initiatedBy upiMandatePb.MandateInitiatedBy,
	signedToken string,
) (*vgUpiPb.RespAuthMandateRequest, error) {
	var err error
	req := &vgUpiPb.RespAuthMandateRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		Resp: &vgUpiPb.ResponseHeader{
			ReqMsgId: reqAuth.Head.Msg,
			Result:   vgUpiPb.ResponseHeader_SUCCESS,
		},
		ReqType:     reqAuthMandateTypeMap[reqAuth.Txn.Type],
		UpiVersion:  upiPb.ParseStringToVersion(reqAuth.Head.Ver),
		InitiatedBy: initiatedBy,
	}
	req.TxnHeader, err = reqAuth.Txn.ConvertToVGTransactionHeader(vendors.UPIEpifiURL)
	if err != nil {
		return nil, fmt.Errorf("unable to parse transaction header for respAuth: %w", err)
	}

	if reqAuth != nil && reqAuth.Txn.Rules != nil {
		vgRules := reqAuth.Txn.Rules.ConvertToVGRulesProto(ctx)
		if vgRules != nil {
			req.Rules = &upiPb.Rules{
				MinAmount:   vgRules.GetMinAmount(),
				ExpireAfter: vgRules.GetExpireAfter(),
			}
		}
	}

	req.Payer, err = reqAuth.Payer.ConvertToVGCustomerProto()
	if err != nil {
		return nil, fmt.Errorf("unable to parse payer for respAuth: %w", err)
	}
	if initiatedBy == upiMandatePb.MandateInitiatedBy_PAYEE {
		req.Payer.Name = info.GetIdentity().GetVerifiedName()
		req.Payer.AccountDetails = accDetail
		req.Payer.Info = info
	}

	if reqAuth.GetInitiatedBy() == upiMandatePb.MandateInitiatedBy_PAYEE &&
		reqAuth.GetRequestType() == upiMandatePb.MandateType_REVOKE {
		req.Payer.Creds = []*upiPb.CredBlock{
			{
				Type:    upiPb.CredBlock_UPI_MANDATE,
				SubType: upiPb.CredBlock_DS,
				Data: &upiPb.CredBlock_Data{
					Text: signedToken,
				},
			},
		}
	}

	if len(reqAuth.Payees.Payee) != 0 {
		vgCust, convertErr := reqAuth.Payees.Payee[0].ConvertToVGCustomerProto()
		if convertErr != nil {
			return nil, fmt.Errorf("unable to parse payee for respAuth: %w", convertErr)
		}

		if reqAuth.Payees.Payee[0].Code == "" || reqAuth.Payees.Payee[0].Code == vendors.PersonMccCode {
			vgCust.Merchant = nil
		}

		if initiatedBy == upiMandatePb.MandateInitiatedBy_PAYER {
			vgCust.Name = info.GetIdentity().GetVerifiedName()
			vgCust.AccountDetails = accDetail
			vgCust.Info = info
		}

		req.Payees = []*upiPb.Customer{vgCust}
	}

	req.Mandate, err = reqAuth.Mandate.CovertToMandateProto()
	if err != nil {
		return nil, fmt.Errorf("error converting mandate to proto: %w", err)
	}

	return req, nil
}
