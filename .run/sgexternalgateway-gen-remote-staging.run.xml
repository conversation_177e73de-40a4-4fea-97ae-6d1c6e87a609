<component name="ProjectRunConfigurationManager">
	<configuration default="false" name="sgexternalgateway-gen-remote-staging" type="GoApplicationRunConfiguration" factoryName="Go Application">
		<module name="gringott" />
		<working_directory value="$PROJECT_DIR$" />
		<envs>
			<env name="AWS_PROFILE" value="epifi-staging" />
			<env name="AWS_SDK_LOAD_CONFIG" value="true" />
			<env name="CGO_ENABLED" value="0" />
			<env name="CONFIG_DIR" value="$PROJECT_DIR$/output/sgexternalgateway/config" />
			<env name="ENVIRONMENT" value="staging" />
			<env name="REMOTE_DEBUG" value="enable" />
			<env name="REMOTE_DEBUG_CONFIG" value="{&quot;consumer&quot;:&quot;disable&quot;}" />
		</envs>
		<kind value="PACKAGE" />
		<package value="github.com/epifi/gringott/cmd/servers/staging/sgexternalgateway" />
		<directory value="$PROJECT_DIR$" />
		<filePath value="$PROJECT_DIR$/cmd/servers/staging/sgexternalgateway/server.gen.go" />
		<output_directory value="$PROJECT_DIR$/output/sgexternalgateway" />
		<method v="2">
			<option name="RunConfigurationTask" enabled="true" run_configuration_name="init_sgexternalgateway_remote_debug" run_configuration_type="ShConfigurationType" />
		</method>
	</configuration>
</component>
